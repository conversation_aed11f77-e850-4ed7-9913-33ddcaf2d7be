/* $ScmHeader: 9996u4zuy5238uCw7C68999999978&5|@ $*/
/* $Id: dsh8ah_dyn2.h,v 1.4 2002/12/05 14:10:15 mathieuh(MASTER_VERSION|CAE_MR) Exp $*/

void ah_dyn2(void)
  {
  /* File generated using Matrix Conversion version 1.3.1         */
  /* To be used with the Matrix Resolution method verion 1.3      */
  /*  (use with matrix_res.c and .h stamper version 1.8 and 1.8)  */
   static double ah_A_adm[10][10];
   static double *ah_A[10][10];
   static double **ah_ptr = &ah_A[0][0];
   static double *ah_x[10];
   static double **ah_x_ptr = &ah_x[0];
   static double ah_b[10];
   static int    ah_size = 10;
   static int    ah_i;
   static int    ah_j;
   const int     ah_bw = 4;
   static unsigned char ah_fpass = 0;

   if ( !ah_fpass )
     {
      for ( ah_i = 0 ; ah_i < ah_size ; ah_i++ )
        {
         for ( ah_j = 0 ; ah_j < ah_size ; ah_j++ )
           {
            ah_A[ah_i][ah_j] = &ah_A_adm[ah_i][ah_j];
           }
        }
     ah_x[0] = &h3psys2[0];
     ah_x[1] = &h3psys2[1];
     ah_x[2] = &h3psys2[2];
     ah_x[3] = &h3psys2[3];
     ah_x[4] = &h3psys2[4];
     ah_x[5] = &h3psys2[5];
     ah_x[6] = &h3psys2[6];
     ah_x[7] = &h3psys2[7];
     ah_x[8] = &h3psys2[8];
     ah_x[9] = &h3psys2[9];

     ah_fpass = 1;
    }

   ah_A_adm[0][0] = - ( h3asys2[0] +
                        h3asys2[1] +
                        h3asys2[2] +
                        h3asys2[3] );
   ah_A_adm[0][1] = ( h3asys2[3] );
   ah_A_adm[0][2] = 0.0;
   ah_A_adm[0][3] = 0.0;
   ah_A_adm[0][4] = 0.0;
   ah_A_adm[0][5] = 0.0;
   ah_A_adm[0][6] = 0.0;
   ah_A_adm[0][7] = 0.0;
   ah_A_adm[0][8] = 0.0;
   ah_A_adm[0][9] = 0.0;
   ah_A_adm[1][0] = ah_A_adm[0][1];
   ah_A_adm[1][1] = - ( h3asys2[3] +
                        h3asys2[4] +
                        h3asys2[5] +
                        h3asys2[6] +
                        h3asys2[7] +
                        h3asys2[8] +
                        h3asys2[9] );
   ah_A_adm[1][2] = 0.0;
   ah_A_adm[1][3] = 0.0;
   ah_A_adm[1][4] = ( h3asys2[9] );
   ah_A_adm[1][5] = 0.0;
   ah_A_adm[1][6] = 0.0;
   ah_A_adm[1][7] = 0.0;
   ah_A_adm[1][8] = 0.0;
   ah_A_adm[1][9] = 0.0;
   ah_A_adm[2][0] = ah_A_adm[0][2];
   ah_A_adm[2][1] = ah_A_adm[1][2];
   ah_A_adm[2][2] = - ( h3asys2[10] +
                        h3asys2[11] +
                        h3asys2[12] +
                        h3asys2[13] );
   ah_A_adm[2][3] = 0.0;
   ah_A_adm[2][4] = ( h3asys2[13] );
   ah_A_adm[2][5] = 0.0;
   ah_A_adm[2][6] = 0.0;
   ah_A_adm[2][7] = 0.0;
   ah_A_adm[2][8] = 0.0;
   ah_A_adm[2][9] = 0.0;
   ah_A_adm[3][0] = ah_A_adm[0][3];
   ah_A_adm[3][1] = ah_A_adm[1][3];
   ah_A_adm[3][2] = ah_A_adm[2][3];
   ah_A_adm[3][3] = - ( h3asys2[15] +
                        h3asys2[16] +
                        h3asys2[17] +
                        h3asys2[18] );
   ah_A_adm[3][4] = ( h3asys2[18] );
   ah_A_adm[3][5] = 0.0;
   ah_A_adm[3][6] = 0.0;
   ah_A_adm[3][7] = 0.0;
   ah_A_adm[3][8] = 0.0;
   ah_A_adm[3][9] = 0.0;
   ah_A_adm[4][0] = ah_A_adm[0][4];
   ah_A_adm[4][1] = ah_A_adm[1][4];
   ah_A_adm[4][2] = ah_A_adm[2][4];
   ah_A_adm[4][3] = ah_A_adm[3][4];
   ah_A_adm[4][4] = - ( h3asys2[9] +
                        h3asys2[13] +
                        h3asys2[14] +
                        h3asys2[18] +
                        h3asys2[19] +
                        h3asys2[20] +
                        h3asys2[22] +
                        h3asys2[23] +
                        h3asys2[39] +
                        h3asys2[41] );
   ah_A_adm[4][5] = ( h3asys2[23] );
   ah_A_adm[4][6] = 0.0;
   ah_A_adm[4][7] = 0.0;
   ah_A_adm[4][8] = 0.0;
   ah_A_adm[4][9] = 0.0;
   ah_A_adm[5][0] = ah_A_adm[0][5];
   ah_A_adm[5][1] = ah_A_adm[1][5];
   ah_A_adm[5][2] = ah_A_adm[2][5];
   ah_A_adm[5][3] = ah_A_adm[3][5];
   ah_A_adm[5][4] = ah_A_adm[4][5];
   ah_A_adm[5][5] = - ( h3asys2[21] +
                        h3asys2[23] +
                        h3asys2[24] +
                        h3asys2[25] +
                        h3asys2[26] +
                        h3asys2[27] +
                        h3asys2[28] );
   ah_A_adm[5][6] = ( h3asys2[27] );
   ah_A_adm[5][7] = ( h3asys2[26] );
   ah_A_adm[5][8] = 0.0;
   ah_A_adm[5][9] = 0.0;
   ah_A_adm[6][0] = ah_A_adm[0][6];
   ah_A_adm[6][1] = ah_A_adm[1][6];
   ah_A_adm[6][2] = ah_A_adm[2][6];
   ah_A_adm[6][3] = ah_A_adm[3][6];
   ah_A_adm[6][4] = ah_A_adm[4][6];
   ah_A_adm[6][5] = ah_A_adm[5][6];
   ah_A_adm[6][6] = - ( h3asys2[27] +
                        h3asys2[29] +
                        h3asys2[30] +
                        h3asys2[31] +
                        h3asys2[42] );
   ah_A_adm[6][7] = 0.0;
   ah_A_adm[6][8] = 0.0;
   ah_A_adm[6][9] = ( h3asys2[42] );
   ah_A_adm[7][0] = ah_A_adm[0][7];
   ah_A_adm[7][1] = ah_A_adm[1][7];
   ah_A_adm[7][2] = ah_A_adm[2][7];
   ah_A_adm[7][3] = ah_A_adm[3][7];
   ah_A_adm[7][4] = ah_A_adm[4][7];
   ah_A_adm[7][5] = ah_A_adm[5][7];
   ah_A_adm[7][6] = ah_A_adm[6][7];
   ah_A_adm[7][7] = - ( h3asys2[26] +
                        h3asys2[32] +
                        h3asys2[33] +
                        h3asys2[34] +
                        h3asys2[35] );
   ah_A_adm[7][8] = ( h3asys2[35] );
   ah_A_adm[7][9] = 0.0;
   ah_A_adm[8][0] = ah_A_adm[0][8];
   ah_A_adm[8][1] = ah_A_adm[1][8];
   ah_A_adm[8][2] = ah_A_adm[2][8];
   ah_A_adm[8][3] = ah_A_adm[3][8];
   ah_A_adm[8][4] = ah_A_adm[4][8];
   ah_A_adm[8][5] = ah_A_adm[5][8];
   ah_A_adm[8][6] = ah_A_adm[6][8];
   ah_A_adm[8][7] = ah_A_adm[7][8];
   ah_A_adm[8][8] = - ( h3asys2[35] +
                        h3asys2[36] +
                        h3asys2[37] +
                        h3asys2[38] );
   ah_A_adm[8][9] = 0.0;
   ah_A_adm[9][0] = ah_A_adm[0][9];
   ah_A_adm[9][1] = ah_A_adm[1][9];
   ah_A_adm[9][2] = ah_A_adm[2][9];
   ah_A_adm[9][3] = ah_A_adm[3][9];
   ah_A_adm[9][4] = ah_A_adm[4][9];
   ah_A_adm[9][5] = ah_A_adm[5][9];
   ah_A_adm[9][6] = ah_A_adm[6][9];
   ah_A_adm[9][7] = ah_A_adm[7][9];
   ah_A_adm[9][8] = ah_A_adm[8][9];
   ah_A_adm[9][9] = - ( h3asys2[40] +
                        h3asys2[42] +
                        h3asys2[43] +
                        h3asys2[44] );
   ah_b[0] = - ( ( h3asys2[0] * h3psys2q[0] ) +
                 ( h3asys2[1] * h3prsv[2] ) +
                 ( h3asys2[2] * h3pspu[2] ) );
   ah_b[1] = - ( ( h3asys2[4] * h3psys2q[1] ) +
                 ( h3asys2[5] * h3prsv[2] ) +
                 ( h3asys2[6] * h3prsv[2] ) +
                 ( h3asys2[7] * h3prsv[1] ) +
                 ( h3asys2[8] * h3prsv[2] ) );
   ah_b[2] = - ( ( h3asys2[10] * h3psys2q[2] ) +
                 ( h3asys2[11] * h3prsv[1] ) +
                 ( h3asys2[12] * h3pedp[1] ) );
   ah_b[3] = - ( ( h3asys2[15] * h3psys2q[3] ) +
                 ( h3asys2[16] * h3prsv[1] ) +
                 ( h3asys2[17] * h3pspu[1] ) );
   ah_b[4] = - ( ( h3asys2[14] * h3prsv[1] ) +
                 ( h3asys2[19] * h3psys2q[4] ) +
                 ( h3asys2[20] * h3prsv[1] ) +
                 ( h3asys2[22] * dtpa ) +
                 ( h3asys2[39] * h3pext[1] ) +
                 ( h3asys2[41] * h3prsv[1] ) );
   ah_b[5] = - ( ( h3asys2[21] * h3prsv[1] ) +
                 ( h3asys2[24] * h3psys2q[5] ) +
                 ( h3asys2[25] * h3prsv[1] ) +
                 ( h3asys2[28] * h3prsv[1] ) );
   ah_b[6] = - ( ( h3asys2[29] * h3psys2q[6] ) +
                 ( h3asys2[30] * h3prsv[1] ) +
                 ( h3asys2[31] * h3prsv[1] ) );
   ah_b[7] = - ( ( h3asys2[32] * h3psys2q[7] ) +
                 ( h3asys2[33] * h3prsv[1] ) +
                 ( h3asys2[34] * h3prsv[1] ) );
   ah_b[8] = - ( ( h3asys2[36] * h3psys2q[8] ) +
                 ( h3asys2[37] * h3prsv[1] ) +
                 ( h3asys2[38] * h3pptup ) );
   ah_b[9] = - ( ( h3asys2[40] * h3ppbhp ) +
                 ( h3asys2[43] * h3psys2q[9] ) +
                 ( h3asys2[44] * h3prsv[1] ) );

   matrix_resolution( ah_ptr , &ah_b[0] , ah_x_ptr ,             
                      ah_size , ah_bw);
  }
