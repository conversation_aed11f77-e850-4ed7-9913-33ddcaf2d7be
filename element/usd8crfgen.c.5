/** 100 Data*/
/***/
/**          SPOILER SURFACE GEARING*/
/***/
extern float CS3CMD;
extern float CS3CMDF;
extern float CS4CMD;
extern float CS4CMDF;
extern float CS5CMD;
extern float CS5CMDF;
extern float CS6CMD;
extern float CS6CMDF;
struct { 
  int dimensions;
  int multiplicity;
  int sizes[1];
  float *labels[8];
  float x_breakpoints[8];
  float data[8];
  } CSGEAR_DATA = {
  { 1 },
  { 4 },
  { 8 },
  { &CS3CMD ,&CS3CMDF  , 
   &CS4CMD ,&CS4CMDF  , 
   &CS5CMD ,&CS5CMDF  , 
   &CS6CMD ,&CS6CMDF  }, 
  {          -70.,            0.,            5.,           33.,           43.,
              54.,           65.,           70.
  },
  {            0.,            0.,            0.,           22.,           31.,
              45.,           63.,           74.
  } };
/**/
/**     */
/**          Aileron surface gearing*/
/***/
extern float CACSPOS;
extern float CAFQPOS;
extern float CAFSPOS;
extern float CANQPOS;
struct { 
  int dimensions;
  int multiplicity;
  int sizes[1];
  float *labels[4];
  float x_breakpoints[7];
  float data[7];
  } CAGEAR_DATA = {
  { 1 },
  { 2 },
  { 7 },
  { &CACSPOS ,&CAFQPOS  , 
   &CAFSPOS ,&CANQPOS  }, 
  {          -28.,        -22.75,         -17.5,            0.,          17.5,
            22.75,           28.
  },
  {          -20.,          -20.,         -16.3,            0.,          16.3,
              20.,           20.
  } };
/**/
/***/
/** 300 Data*/
/***/
/**          SPOILER SURFACE GEARING*/
/***/
extern float CS3CMD;
extern float CS3CMDF;
extern float CS4CMD;
extern float CS4CMDF;
extern float CS5CMD;
extern float CS5CMDF;
extern float CS6CMD;
extern float CS6CMDF;
struct { 
  int dimensions;
  int multiplicity;
  int sizes[1];
  float *labels[8];
  float x_breakpoints[8];
  float data[8];
  } CSGEAR3_DATA = {
  { 1 },
  { 4 },
  { 8 },
  { &CS3CMD ,&CS3CMDF  , 
   &CS4CMD ,&CS4CMDF  , 
   &CS5CMD ,&CS5CMDF  , 
   &CS6CMD ,&CS6CMDF  }, 
  {          -70.,            0.,            3.,           33.,           43.,
              54.,           65.,           70.
  },
  {            0.,            0.,            0.,           24.,           33.,
              47.,           65.,           77.
  } };
/**/
/***/
/**          Aileron surface gearing*/
/***/
extern float CACSPOS;
extern float CAFQPOS;
extern float CAFSPOS;
extern float CANQPOS;
struct { 
  int dimensions;
  int multiplicity;
  int sizes[1];
  float *labels[4];
  float x_breakpoints[7];
  float data[7];
  } CAGEAR3_DATA = {
  { 1 },
  { 2 },
  { 7 },
  { &CACSPOS ,&CAFQPOS  , 
   &CAFSPOS ,&CANQPOS  }, 
  {          -28.,         -18.2,         -17.5,            0.,          11.0,
             17.5,           28.
  },
  {         -17.5,         -17.5,         -17.5,            0.,          10.0,
             15.3,          18.0
  } };
/**/

/*
C   -----------------------
C   FUNCTION INITIALIZATION
C   -----------------------
*/

extern int CSGEAR       ;
extern int CAGEAR       ;
extern int CSGEAR3      ;
extern int CAGEAR3      ;

fgen_init()
{
  CSGEAR       = fgen1d_init(&CSGEAR_DATA );
  CAGEAR       = fgen1d_init(&CAGEAR_DATA );
  CSGEAR3      = fgen1d_init(&CSGEAR3_DATA);
  CAGEAR3      = fgen1d_init(&CAGEAR3_DATA);
}
