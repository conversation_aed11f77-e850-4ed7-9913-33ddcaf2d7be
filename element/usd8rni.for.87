C'Title                AHRS Modes Module
C'Module_ID            USD8RNI
C'PDD_#
C'Customer             US AIR DASH 8 100A/300A
C'Application          DASH 8  SPERRY AHZ600 AHRS
C'Author               <PERSON><PERSON>'Date                 August 1991
C
C'System               Attitude Heading Reference System (AHRS)
C'Iter_rate            66 mSec
C'Process              CPU0.S01
C
C
C
C'Revision_History
C
C  usd8rni.for.5 31Jul1997 02:22 usd8 jwm
C       < COA S81-1-131  Change made to quick align the stby ADI when
C         activating the AHRS quick align. >
C
C
C  usd8rni.for.4  1May1992 06:25 usd8 nd
C       < changed init counter to 30 iterations. >
C
C  usd8rni.for.3  1May1992 06:01 usd8 nd
C       < compilation errors >
C
C  usd8rni.for.2  1May1992 05:58 usd8 ND
C       < INSERTED COUNTER FOR VG/ERECT COMMAND AFTER A LOAD. >
C
C  usd8rni.for.1 30Apr1992 17:14 usd8 ND
C       < Modified ahrs controller processing logic on a load. >
C
C  usd8rni.for    30Apr1992 11:05 usd8 nd
C       < Added logic for RMI headinf flag, snag 1141 >
C
C  usd8rni.for.45 26Apr1992 17:51 usd8 nd
C       < replaced enoftst with endoftst1 >
C
C  usd8rni.for.44 26Apr1992 16:13 usd8 ND
C       < COMPILATION ERRORS >
C
C                 24Apr1992 15:30 usd8 ND
C       < Implemented AHRS countdown mode during alignment snag# 1138 >
C
C  usd8rni.for.43 17Apr1992 15:28 usd8 ND
C       < Modified advisory light test and put back old controller
C         processing logic. >
C
C  usd8rni.for.42 17Apr1992 13:44 usd8 ND
C       < IF ADVISORY LIGHT TEST IS ON THEN LIGHT UP ALL ANNUNCIATORS. >
C
C  usd8rni.for.41 17Apr1992 13:41 usd8 ND
C       < AHRS ENTERS BASIC MODE IF BOTH FGC'S ARE INVALID. >
C
C  usd8rni.for.40 17Apr1992 12:19 usd8 ND
C       < If excess motion is detected during alignment then alignment is
C         retriggered. >
C
C  usd8rni.for.39 17Mar1992 10:58 usd8 nd
C       < fast align available when in BASIC mode. >
C
C  usd8rni.for.38 12Mar1992 11:46 usd8 NINO
C       < FAST ALIGN AVAILABLE WHEN PITCH/ROLL DRIFT > 0 (IN BASIC MODE).
C         FOR >
C
C  usd8rni.for.37 10Mar1992 08:50 usd8 NINO
C       < DISPLAY HEADING FLAG IF SLEW COMMAND IS ISSUED. >
C
C  usd8rni.for.36  9Mar1992 10:47 usd8 nino
C       < rni running at 66msec. >
C
C  usd8rni.for.35  5Mar1992 11:03 usd8 NINO
C       < Modified FAST ALIGN logic and DG mode logic. >
C
C  usd8rni.for.34  4Mar1992 14:11 usd8 nino
C       < declred G as a constant. >
C
C  usd8rni.for.33  4Mar1992 14:03 usd8 nino
C       < compilation errors >
C
C  usd8rni.for.32  4Mar1992 14:00 usd8 NINO
C       < DECLARED VARIABLES IN CP STATEMENTS >
C
C  usd8rni.for.31  4Mar1992 13:50 usd8 NINO
C       < MODIFIED FAST ALIGN LOGIC AND ADDED ALIGNMENT IN AIR LOGIC. >
C
C  usd8rni.for.30  2Mar1992 13:29 usd8 NINO
C       < REMOVED DARK CONCEPT WHEN IN POWER OFF. >
C
C  usd8rni.for.29  2Mar1992 12:41 usd8 nino
C       < both cbs must be reset in order to reset ahrs fail resettable
C         malf. >
C
C  usd8rni.for.28  2Mar1992 12:06 usd8 NINO
C       < COMPILATION ERRORS >
C
C  usd8rni.for.27  2Mar1992 12:04 usd8 NINO
C       < SETTING DARK CONCEPT FEATURE. >
C
C  usd8rni.for.26  1Mar1992 10:25 usd8 NINO
C       < RESETTING AHRS RESETTABLE MALF WHEN BOTH CBS ARE RESET. >
C
C  usd8rni.for.25  1Mar1992 08:52 usd8 nino
C       < AUX PR IS ILLUMINATED IF PRIMARY POWER IS LOST AND SECONDARY
C         POWER IS AVAILABLE. >
C
C  usd8rni.for.24 28Feb1992 16:21 usd8 NINO
C       < USING GROUND SPEED TO CHECK FOR EXCESS MOTION DURING ALIGNMENT. >
C
C  usd8rni.for.23 28Feb1992 13:30 usd8 NINO
C       < RESETTING RNDGMODE WHEN NO POWER AVAILABLE TO AHRS. >
C
C  usd8rni.for.22 28Feb1992 12:38 usd8 NINO
C       < RESETTING TEST FLAGS AT POWER OFF. >
C
C  usd8rni.for.21 28Feb1992 11:49 usd8 nino
C       < compilation errors >
C
C  usd8rni.for.20 28Feb1992 11:44 usd8 NINO
C       < AHRS CBs MUST BE RECYCLED WHEN EXCESS MOTION IS DETECTED DURING
C         ALIGN >
C
C  usd8rni.for.19 26Feb1992 11:25 usd8 NINO
C       < TURNING ON AUX POWER ANNUNCIATORS WHEN PRIMARY POWER IS LOST. >
C
C  usd8rni.for.18 25Feb1992 16:20 usd8 NINO
C       < RESERVING 64 BYTES FOR BOTH INPUT BUFFERS FOR AHRS CONTROLLERS. >
C
C  usd8rni.for.17 20Feb1992 17:59 usd8 NINO
C       < CONTINUOUSLY READING DATA TRANSMITTED FROM CONTROLLER >
C
C  usd8rni.for.16 20Feb1992 15:01 usd8 NINO
C       < UPDATING RNIDA,RNIDB ON EVERY ITERATION. >
C
C  usd8rni.for.15 20Feb1992 14:41 usd8 nino
C       < data is outputted to the controller on every iteration whether
C         there >
C
C  usd8rni.for.14  7Feb1992 12:11 usd8 NINO
C       < MESSAGE SEQUENCE NUMBER IS INCREASED ONLY WHEN A NEW COMMAND IS
C         ENTERED ON CONTROLLER. >
C
C  usd8rni.for.13  6Feb1992 11:40 usd8 M.WARD
C       < WILL CHANGE JRCONA, RNBUFFA ETC TO INT1 IN CDB, USE LOCAL LABEL
C         FOR INTRINSIC FUNCTIONS >
C
C  usd8rni.for.12  3Feb1992 14:02 usd8 nino
C       < fast align feature available only when ahrs in align mode. >
C
C  usd8rni.for.11 31Jan1992 14:23 usd8 nino
C       < changed tcm0ahru,tcmahru to tcm0ahrs,tcmahrs. >
C
C  usd8rni.for.10 31Jan1992 10:56 usd8 NINO
C       < DECLARED NEW ENTRY POINT: RNAHRS. >
C
C  usd8rni.for.9 31Jan1992 10:31 usd8 nino
C       < ran forport on new changes. >
C
C  usd8rni.for.8 30Jan1992 17:58 usd8 nino
C       < uncommented endif for freeze flag >
C
C  usd8rni.for.7 30Jan1992 17:22 usd8 nino
C       < declared heading slew commands as integer*2 in local variable
C         section. >
C
C  usd8rni.for.6 29Jan1992 14:02 usd8 NINO
C       < UNCOMMENTED RNBUFFA, RNBUFFB AND RE-COMPILED MODULE. >
C
C  usd8rni.for.5 19Dec1991 11:53 usd8 M.WARD
C       < until cdb update done to fix rnbuffa, rnbuffb have commented out
C         with CMW >
C
C  usd8rni.for.4 19Dec1991 10:04 usd8 nd
C       < indexing malfunctions and rnbuffa,rnbuffb. >
C
C  usd8rni.for.3 19Dec1991 09:47 usd8 nd
C       < deleted duplicate ubtas from CP block >
C
C  usd8rni.for.2 19Dec1991 09:44 usd8 nd
C       < pre-compilation errors >
C
C  usd8rni.for.1 19Dec1991 09:39 usd8 nd
C       < entering module into configuration. >
C
C'
C
C
C'References
C
C      [ 1 ]  Product/Installation Specification for the AHZ-600 Attitude and
C             Heading Reference System, Spec. No.X7012773, Sperry Flight System,
C             March 27, 1987.
C
C      [ 2 ]  Dash 8 Series 100 Maintenance Manual, PSM 1-8-2(CCV), Boeing of
C             Canada LTD. De Havilland Division, Chapter 34-20, January 30,1985.
C
C      [ 3 ]  Dash 8 Aircraft Operating Manual, PSM 1-8-1, Boeing of Canada LTD.
C             De Havilland Division, Chapter 1, Section 13, October 8,1987.
C
C      [ 4 ]  Product Specification for the AHRU Controller ,
C             Part No. 7004545-901 to 908, Sperry Flight Systems Division,
C             August 29, 1983.
C
C      [ 5 ]  Dash 8 Attitude and Heading Reference System Wiring Diagrams,
C             Chapter 34-20-05, April 20,1990.
C'
C
C
      SUBROUTINE   USD8RNI
C     ~~~~~~~~~~
C
C
C'Purpose
C
C     This routine is used to monitor inputs to AHRS ...........
C     ..........................................................
C'
C
C'Include_files
C
C     Not applicable
C'
C
C'Subroutines_called
C
C     Not applicable
C'
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 01/31/92 - 10:05 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C     ~~~~~~~~~~~~~
C
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C           COMMON DATA BASE
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C     Inputs
C     ~~~~~~
C
CP    USD8 UBTAS,           !    TAS INPUT FROM ADC
CP   *  AGRK1,              !    AIR/GROUND RELAY (T=GROUND)
CP   *  AMRLK1,             !    ADVISORY LIGHT TEST ON
CP   *  BIAD05,             !    FGC-1
CP   *  BIAD08,             !    FGC-2
CP   *  BILK01,             !    AHRS1 PRIMARY POWER SOURCE (28 VDC)
CP   *  BIRF01,             !    AHRS1 AUXILIARY POWER SOURCE (28VDC)
CP   *  BIAC08,             !    AHRS2 PRIMARY POWER SOURCE (28VDC)
CP   *  BILF01,             !    AHRS2 AUXILIARY POWER SOURCE (28VDC)
CP   *  JRHEADA,            !    CURRENT MESSAGE INDEX FROM CONTROLLER 1
CP   *  JRCONA,             !    MESSAGE CONTENT FROM CONTROLLER 1
CP   *  JRHEADB,            !    CURRENT MESSAGE INDEX FROM CONTROLLER 2
CP   *  JRCONB,             !    MESSAGE CONTENT FROM CONTROLLER 2
CP   *  RNBUFFA,            !    EQUIVALENT TO JRCONA
CP   *  RNBUFFB,            !    EQUIVALENT TO JRCONB
CP   *  RNDGCMND,           !    DG COMMAND SELECTED ON AHRS CONTROLLER
CP   *  RNVGCMND,           !    VG ERECT COMMAND SELECTED ON AHRS CONTROLLER
CP   *  RNTYPA,             !    MESSAGE TYPE SENT TO AHRS 1 CONTROLLER
CP   *  RNTYPB,             !    MESSAGE TYPE SENT TO AHRS 2 CONTROLLER
CP   *  RNLENA,             !    MESSAGE LENGTH SENT TO AHRS 1 CONTROLLER
CP   *  RNLENB,             !    MESSAGE LENGTH SENT TO AHRS 2 CONTROLLER
CP   *  RNCONA,             !    MESSAGE CONTENT SENT TO AHRS 1 CONTROLLER
CP   *  RNCONB,             !    MESSAGE CONTENT SENT TO AHRS 2 CONTROLLER
CP   *  RNIDA,              !    MESSAGE IDENT. SENT TO AHRS 1 CONTROLLER
CP   *  RNIDB,              !    MESSAGE IDENT. SENT TO AHRS 2 CONTROLLER
CP   *  RNTSTFLG,           !    TEST FLAG SET WHEN TEST INITIATED ON CONTRLR
CP   *  TCMAHRS,            !    AHRU FAST ALIGN Pb ACTIVE
CP   *  TCM0AHRS,           !    AHRU FAST ALIGN AVAILABILITY FLAG
CP   *  UBX006A,            !    TAS FROM ADC-1
CP   *  UBX006B,            !    TAS FROM ADC-2
CP   *  UBZ006A0,           !    ADC-1 TAS VAILDITY
CP   *  UBZ006B0,           !    ADC-2 TAS VAILDITY
CP   *  UBTEST,             !    ADC TEST FLAG
CP   *  UCFALIGN,           !    STBY ADI FAST ALIGN
CP   *  VAZB,               !    NORMAL ACCELERATION
CP   *  VVNS,               !    NORTH/SOUTH GROUND SPEED COMPONENT
CP   *  VVEW,               !    EAST/WEST GROUND SPEED COMPONENT
CP   *  HR,HP,HQ,           !    GYRO RATE B. AXES  (DEG/SEC)
CP   *  TF31041(2),         !    HDG  FAIL MALFUNCTION
CP   *  TF34I001(2),        !    TAS REF FAIL MALFUNCTION
CP   *  TF34I021(2),        !    AHRS COMPLETE FAILURE - RESETTABLE MALF.
CP   *  TF34I011(2),        !    AHRS COMPLETE FAILURE - NON-RESET MALF.
CP   *  T031041(2),         !    GREY CONCEPT FOR HDG  FAIL MALFUNCTION
CP   *  T034I001(2),        !    GREY CONCEPT FOR TAS REF FAIL MALFUNCTION
CP   *  T034I021(2),        !    AHRS COMPLETE FAILURE - RESETTABLE MALF.
CP   *  T034I011(2),        !    AHRS COMPLETE FAILURE - NON-RESET MALF.
CP   *  VPILTXCG,           !    X-DIST C OF G OF A/C TO PILOTS EYE POINT
CP   *  VPILTZCG,           !    Z-DIST C OF G OF A/C TO PILOTS EYE POINT
C
C
C     Outputs
C     ~~~~~~~
C
CP   *  RNPCHV,             !    AHRS PITCH VALIDITY  (T = VALID)
CP   *  RNPCHDFT,           !    AHRS PITCH DRIFT VALUE
CP   *  RNROLDFT,           !    AHRS ROLL DRIFT VALUE
CP   *  RNROLV,             !    AHRS ROLL  VALIDITY  (T = VALID)
CP   *  RNMHDGV,            !    AHRS MAGNETIC HEADING VALIDITY  (T=VALID)
CP   *  RNRMIV,             !    RMI MAGNETIC HEADING VALIDITY   (T=VALID)
CP   *  RNHDGRV,            !    RATE OF TURN (HDG RATE) VALIDITY (T=VALID)
CP   *  RNPCHRV,            !    BODY PITCH RATE VALIDITY  (T = VALID)
CP   *  RNROLRV,            !    BODY ROLL  RATE VALIDITY  (T = VALID)
CP   *  RNYAWRV,            !    BODY YAW RATE VALIDITY  (T = VALID)
CP   *  RNALATV,            !    BODY LATERAL ACCEL'N  VALIDITY  (T=VALID)
CP   *  RNALNGV,            !    BODY LONG ACCEL'N VALIDITY  (T=VALID)
CP   *  RNANRMV,            !    BODY NORMAL  ACCEL'N  VALIDITY  (T=VALID)
CP   *  RNSLCOF,            !    SLAVING CUT-OFF FLAG  (T = CUT-OFF)
CP   *  RNPCHVO,            !    AHRS PITCH VALIDITY  (T = VALID)
CP   *  RNROLVO,            !    AHRS ROLL  VALIDITY  (T = VALID)
CP   *  RNMHDGVO,           !    AHRS MAGNETIC HEADING VALIDITY  (T=VALID)
CP   *  RNRMIVO,            !    RMI MAGNETIC HEADING VALIDITY   (T=VALID)
CP   *  RNHDGRVO,           !    RATE OF TURN (HDG RATE) VALIDITY (T=VALID)
CP   *  RNPCHRVO,           !    BODY PITCH RATE VALIDITY  (T = VALID)
CP   *  RNROLRVO,           !    BODY ROLL  RATE VALIDITY  (T = VALID)
CP   *  RNYAWRVO,           !    BODY YAW RATE VALIDITY  (T = VALID)
CP   *  RNALATVO,           !    BODY LATERAL ACCEL'N  VALIDITY  (T=VALID)
CP   *  RNALNGVO,           !    BODY LONG ACCEL'N VALIDITY  (T=VALID)
CP   *  RNANRMVO,           !    BODY NORMAL  ACCEL'N  VALIDITY  (T=VALID)
CP   *  RNSLCOFO,           !    SLAVING CUT-OFF FLAG  (T = CUT-OFF)
CP   *  RNEXCM,             !    EXCESSIVE MOTION FLAG
CP   *  RNBASIC,            !    AHRS BASIC MODE FLAG
CP   *  RNDGMODE,           !    AHRS DG MODE FLAG
CP   *  RNNORM,             !    AHRS NORMAL MODE FLAG
CP   *  RNTEST,             !    AHRS TEST MODE FLAG
CP   *  RNCPTEST,           !    TEST TIMER FOR AHRS TEST INITIATED ON CNTRLR
CP   *  RNTSTLCH,           !    LATCH FLAG SET WHEN TEST INITIATED ON CNTRLR
C                           !    GOES BEYOND 5 SEC.
CP   *  RNSYSON,            !    SYSTEM ON FLAG
CP   *  RNALIGN,            !    AHRU IN ALIGN MODE
CP   *  RNPWR,              !    AHRS POWER FLAG
CP   *  RNADCV,             !    TAS INPUT VALID FLAG
CP   *  RNFREEZE,           !    AHRS FREEZE FLAG
CP   *  RNSYSFLG,           !    AHRS SYSTEM FLAGS
CP   *  RNXAHRU,            !    X-DIST FROM  C OF G TO AHRU  (FEET)
CP   *  RNYAHRU,            !    Y-DIST FROM  C OF G TO AHRU  (FEET)
CP   *  RNZAHRU,            !    Z-DIST FROM  C OF G TO AHRU  (FEET)
CP   *  RNALNTMR,           !    AHRS ALIGN TIMER  (180 SEC)
CP   *  RNTESTMR,           !    AHRS TEST TIMER  (SEC)
CP   *  RNMODE,             !    AHRS MODE STATUS FLAG (INTIGER REP)
CP   *  RN$D28V(2)          !    POWER DOP FOR AHRS CONTROLLERS
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:51:29 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  HP             ! ROLL RATE                            [deg/s]
     &, HQ             ! PITCH RATE                           [deg/s]
     &, HR             ! YAW RATE                             [deg/s]
     &, RNALNTMR(2)    ! AHRS ALIGN TIMER                       [SEC]
     &, RNCPTEST(2)    ! TEST TIMER WHEN TEST INITIATED ON CONTR[SEC]
     &, RNPCHDFT(2)    ! PITCH DRIFT                        [DEG/SEC]
     &, RNROLDFT(2)    ! ROLL DRIFT                         [DEG/SEC]
     &, RNTESTMR(2)    ! AHRS TEST TIMER                        [SEC]
     &, RNXAHRU        ! X-DIST FROM  C OF G TO AHRU           [FEET]
     &, RNYAHRU        ! Y-DIST FROM  C OF G TO AHRU           [FEET]
     &, RNZAHRU        ! Z-DIST FROM  C OF G TO AHRU           [FEET]
     &, UBTAS(3)       !  ADC True Aispeeed                    [kts]
     &, UBX006A        ! TRUE AIRSPEED (KTS)           R1
     &, UBX006B        ! TRUE AIRSPEED (KTS)           R1
     &, VAZB           ! BODY AXES TOTAL Z ACC.             [ft/s**2]
     &, VPILTXCG       ! X CG DIST TO PILOT                      [ft]
     &, VPILTZCG       ! Z CG DIST TO PILOT                      [ft]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
C$
      INTEGER*4
     &  RNDGCMND(2)    ! DG COMMAND SENT BY AHRU CONTROLLER
     &, RNMODE(2)      ! AHRS MODE STATUS FLAG (INTEGER REP)
C$
      INTEGER*2
     &  JRHEADA        !
     &, JRHEADB        !
     &, RNIDA          !
     &, RNIDB          !
     &, RNLENA         !
     &, RNLENB         !
     &, RNTYPA         !
     &, RNTYPB         !
C$
      LOGICAL*1
     &  AGRK1          ! Gear aux lndg relay K1
     &, AMRLK1         ! Positive seeking lamp relay
     &, BIAC08         ! AHRS 2                      34 PDAR   DI197B
     &, BIAD05         ! FGC 1                       22 PDAL   DI195B
     &, BIAD08         ! FGC 2                       22 PDAR   DI197C
     &, BILF01         ! AHRS 2 AUX                  34 PDLES  DI2005
     &, BILK01         ! AHRS 1                      34 PDLES  DI2009
     &, BIRF01         ! AHRS 1 AUX                  34 PDRES  DI2165
     &, JRCONA(64)     !
     &, JRCONB(64)     !
     &, RN$D28V(2)     ! POWER TO AHRU 1 CONTROLLER            DO0000
     &, RNADCV(2)      ! ADC INPUT VALID FLAG
     &, RNALATV(2)     ! BODY LATERAL ACCEL'N  VALIDITY           [-]
     &, RNALATVO(2)    ! BODY LATERAL ACCEL'N  VALIDITY           [-]
     &, RNALIGN(2)     ! AHRU IN ALIGN MODE
     &, RNALNGV(2)     ! BODY LONG ACCEL'N  VALIDITY              [-]
     &, RNALNGVO(2)    ! BODY LONG ACCEL'N  VALIDITY              [-]
     &, RNANRMV(2)     ! BODY NORMAL  ACCEL'N  VALIDITY           [-]
     &, RNANRMVO(2)    ! BODY NORMAL  ACCEL'N  VALIDITY           [-]
     &, RNBASIC(2)     ! AHRS BASIC MODE FLAG
     &, RNCONA(64)     !
     &, RNCONB(64)     !
     &, RNDGMODE(2)    ! AHRS DG MODE FLAG
     &, RNEXCM(2)      ! EXCESSIVE MOTION FLAG
     &, RNFREEZE       ! AHRS RNI FREEZE FLAG
     &, RNHDGRV(2)     ! RATE OF TURN (HDG RATE) VALIDITY         [-]
     &, RNHDGRVO(2)    ! RATE OF TURN (HDG RATE) VALIDITY         [-]
     &, RNMHDGV(2)     ! AHRS MAGNETIC HEADING VALIDITY           [-]
     &, RNMHDGVO(2)    ! AHRS MAGNETIC HEADING VALIDITY           [-]
     &, RNNORM(2)      ! AHRS NORMAL MODE FLAG
     &, RNPCHRV(2)     ! BODY PITCH RATE VALIDITY                 [-]
      LOGICAL*1
     &  RNPCHRVO(2)    ! BODY PITCH RATE VALIDITY                 [-]
     &, RNPCHV(2)      ! AHRS PITCH VALIDITY                      [-]
     &, RNPCHVO(2)     ! AHRS PITCH VALIDITY                      [-]
     &, RNPWR(2)       ! AHRS POWER FLAG
     &, RNRMIV(2)      ! RMI MAGNETIC HEADING VALIDITY            [-]
     &, RNRMIVO(2)     ! RMI MAGNETIC HEADING VALIDITY            [-]
     &, RNROLRV(2)     ! BODY ROLL  RATE VALIDITY                 [-]
     &, RNROLRVO(2)    ! BODY ROLL  RATE VALIDITY                 [-]
     &, RNROLV(2)      ! AHRS ROLL  VALIDITY                      [-]
     &, RNROLVO(2)     ! AHRS ROLL  VALIDITY                      [-]
     &, RNSLCOF(2)     ! SLAVING CUT-OFF FLAG                     [-]
     &, RNSLCOFO(2)    ! SLAVING CUT-OFF FLAG                     [-]
     &, RNSYSFLG(20,2) ! AHRS SYSTEM FLAGS
     &, RNSYSON(2)     ! SYSTEM ON FLAG
     &, RNTEST(2)      ! AHRS TEST MODE FLAG
     &, RNTSTFLG(2)    ! AHRS TEST INITIATED BY PB ON CONTRLR
     &, RNTSTLCH(2)    ! LATCH FLAG DURING AHRS TEST
     &, RNVGCMND(2)    ! VG ERECT COMMAND
     &, RNYAWRV(2)     ! BODY YAW RATE VALIDITY                   [-]
     &, RNYAWRVO(2)    ! BODY YAW RATE VALIDITY                   [-]
     &, T031041(2)     ! HDG REF FAIL LEFT
     &, T034I001(2)    ! TAS REF FAIL LEFT
     &, T034I011(2)    ! AHRS COMPLETE FAILURE - NON-RESET LEFT
     &, T034I021(2)    ! AHRS COMPLETE FAILURE - RESETTABLE LEFT
     &, TCM0AHRS       ! AHRS ALIGN - AVAILABLE FLAG
     &, TCMAHRS        ! AHRS ALIGN
     &, TF31041(2)     ! HDG REF FAIL LEFT
     &, TF34I001(2)    ! TAS REF FAIL LEFT
     &, TF34I011(2)    ! AHRS COMPLETE FAILURE - NON-RESET LEFT
     &, TF34I021(2)    ! AHRS COMPLETE FAILURE - RESETTABLE LEFT
     &, UBTEST(3)      !  ADC test enable flag
      LOGICAL*1
     &  UBZ006A0       ! TAS FLAG
     &, UBZ006B0       ! TAS FLAG
     &, UCFALIGN       !  Stby Horizon fast align
C$
      INTEGER*1
     &  RNBUFFA(64)    ! CONTENT OF INPUT MESSAGE FROM CONTROLLER 1
     &, RNBUFFB(64)    ! CONTENT OF INPUT MESSAGE FROM CONTROLLER 2
C$
      LOGICAL*1
     &  DUM0000001(9072),DUM0000002(4460),DUM0000003(228)
     &, DUM0000004(3044),DUM0000005(1004),DUM0000006(2184)
     &, DUM0000007(4),DUM0000008(2272),DUM0000009(4)
     &, DUM0000010(4),DUM0000011(1334),DUM0000012(123)
     &, DUM0000013(405),DUM0000014(71718),DUM0000015(2)
     &, DUM0000016(2),DUM0000017(2),DUM0000018(4),DUM0000019(1)
     &, DUM0000020(120),DUM0000021(64),DUM0000022(7786)
     &, DUM0000023(606),DUM0000024(212068),DUM0000025(2995)
     &, DUM0000026(549),DUM0000027(1048),DUM0000028(271)
     &, DUM0000029(9923),DUM0000030(2),DUM0000031(2)
     &, DUM0000032(2)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RN$D28V,DUM0000002,BIAD05,BIAD08,DUM0000003
     &, BILK01,BIAC08,BIRF01,BILF01,DUM0000004,VAZB,DUM0000005
     &, VVNS,VVEW,DUM0000006,VPILTXCG,DUM0000007,VPILTZCG,DUM0000008
     &, HP,DUM0000009,HQ,DUM0000010,HR,DUM0000011,UBTEST,DUM0000012
     &, UBTAS,DUM0000013,UCFALIGN,DUM0000014,RNPCHVO,RNROLVO
     &, RNMHDGVO,RNRMIVO,RNHDGRVO,RNPCHRVO,RNROLRVO,RNYAWRVO
     &, RNALNGVO,RNALATVO,RNANRMVO,RNSLCOFO,RNPCHV,RNROLV,RNMHDGV
     &, RNRMIV,RNHDGRV,RNPCHRV,RNROLRV,RNYAWRV,DUM0000015,RNALNGV
     &, RNALATV,RNANRMV,RNSLCOF,DUM0000016,RNEXCM,RNBASIC,RNDGMODE
     &, RNVGCMND,RNNORM,RNTEST,RNTSTFLG,RNTSTLCH,DUM0000017,RNSYSON
     &, RNALIGN,DUM0000018,RNPWR,RNBUFFA,RNBUFFB,RNADCV,RNFREEZE
     &, DUM0000019,RNSYSFLG,DUM0000020,RNPCHDFT,RNROLDFT,DUM0000021
     &, RNALNTMR,RNTESTMR,RNCPTEST,RNXAHRU,RNYAHRU,RNZAHRU,RNMODE
     &, RNDGCMND,DUM0000022,AGRK1,DUM0000023,AMRLK1,DUM0000024
     &, TCMAHRS,TCM0AHRS,DUM0000025,TF31041,TF34I001,TF34I011
     &, TF34I021,DUM0000026,T031041,T034I001,T034I011,T034I021
     &, DUM0000027,UBX006A,UBZ006A0,DUM0000028,UBX006B,UBZ006B0
     &, DUM0000029,RNTYPA,RNLENA,RNCONA,RNIDA,DUM0000030,RNTYPB
     &, RNLENB,RNCONB,RNIDB,DUM0000031,JRHEADA,JRCONA,DUM0000032
     &, JRHEADB,JRCONB    
C------------------------------------------------------------------------------
C
C
C
C
C'Ident
C
      CHARACTER*55 REV
     &               /
     &  '$Source: usd8rni.for.5 31Jul1997 02:22 usd8 jwm    $'/
C
C
C'Include_files
C
      INCLUDE 'disp.com'         ! NO FPC
C
C'Local_variables
C
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C           LOCAL VARIABLES
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C
      REAL*4
C     ******
C
     &  TIME1(2),            !  TIMER FOR POWER INTERRUPTION
     &  TIME2(2)             !  10 sec TIMER FOR NORMAL MODE
C
C
      INTEGER*1
C     *********
C
     &  AUXPRANA,            !  AUXILIARY POWER ANN.FLAG ON AHRS 1 CONTROLLER
     &  AUXPRANB,            !  AUXILIARY POWER ANN.FLAG ON AHRS 2 CONTROLLER
     &  BASICANA,            !  BASIC ANN. FLAG ON AHRS 1 CONTROLLER
     &  BASICANB,            !  BASIC ANN. FLAG ON AHRS 2 CONTROLLER
     &  DGANNA,              !  DG MODE ANN. FLAG ON AHRS 1 CONTROLLER
     &  DGANNB,              !  DG MODE ANN. FLAG ON AHRS 2 CONTROLLER
     &  FASTANNA,            !  FAST ANN. FLAG ON AHRS 1 CONTROLLER
     &  FASTANNB,            !  FAST ANN. FLAG ON AHRS 2 CONTROLLER
     &  SLAVANNA,            !  SLAVE ANN.FLAG ON AHRS 1 CONTROLLER
     &  SLAVANNB             !  SLAVE ANN.FLAG ON AHRS 1 CONTROLLER
C
      INTEGER*2
C     *********
C
     &  BUFF(2),
     &  DGSLEW,              !  SLEW COMMAND SELECTED ON CONTROLLER
     &  HEX(3),
     &  PRETEMP(3,2),
     &  TEMP(3,2)
C
      INTEGER*4
C     *********
C
     &  INIT(2)              ! COUNTER USED ON A LOAD FOR AHRS CONTROLLER
C
      LOGICAL*1
C     *********
C
     &  T,F,                 !  .TRUE. , .FALSE. - FLAGS
     &  FGCVALID,            !  TRUE IF EITHER FGC'S IS VALID
     &  CBPWR(2,2),          !  LOCAL LABEL FOR CB INPUT
     &  CBOUT(2),            !  CB STATUS DURING AHRS FAILURE -
C                            !  RESETTABLE MALF
     &  ENDOFTST1(2),        !  AFTER AHRS SELF TEST, THE AHRS STAYS
                             !  IN TEST MODE FOR 30 SECONDS ONLY IF A/C
                             !  IS ON GROUND
     &  MALFON,              !  FLAG SET WHEN ANY MALF ACTIVE
     &  ONCE(2),             !  FLAG SET TO EXECUTE A SECTION OF CODE ONCE
     &  PR_CBOUT(2),         !  PREVIOUS CB STATUS DURING AHRS FAILURE -
C                            !  RESETTABLE MALF.
     &  PRETEST(2),          !  PREVIOUS STATE OF TEST
     &  ONEPASS1,            !  ONE EXECUTION PASS FLAG
     &  TESTCOMP(2),         !  TEST COMPLETED FLAG
     &  TSTCOMP2(2),         !  TEST COMPLETED FLAG WHEN INITIATED ON CONTRLR
     &  FIRST(2),            !  SETTING DARK CONCEPT IN FIRST PASS
     &  FIRSTPAS  /.TRUE./   !  FIRST PASS LOGIC FLAG
C
C
C'
C
C'Constants
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C           LOCAL CONSTANTS
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C
      REAL*4
C     ******
C
     &  XOFF,                !  X - distance from pilot eye pt to AHRU
     &  YOFF,                !  Y - distance from pilot eye pt to AHRU
     &  ZOFF                 !  Z - distance from pilot eye pt to AHRU
C
C
      INTEGER*4
C     *********
C
     &  J,                   !  DO LOOP INDEX
     &  K(2),                !  POINTERS TO BUFFERS USED FOR STORING
                             !  INPUT COMMAND RECEIVED FROM AHRS CONTR.
     &  N,                   !  DO LOOP INDEX
     &  NAHRS                !  DO LOOP INDEX (# OF AHRS SYSTEM)
C
      INTEGER*2
C     *********
C
     &  MSGTYPA,             !  MESSAGE TYPE SENT TO AHRS 1 CONTROLLER
     &  MSGTYPB,             !  MESSAGE TYPE SENT TO AHRS 2 CONTROLLER
     &  MSGLENA,             !  MESSAGE LENGTH SENT TO AHRS 1 CONTROLLER
     &  MSGLENB              !  MESSAGE LENGTH SENT TO AHRS 2 CONTROLLER
C
C
C
      PARAMETER  (T = .TRUE.,
     &            F = .FALSE.,
     &            NAHRS = 2)
C
C
C
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C           DATA  INITIALIZATIONS
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C     NOTE: The figures below are approximations
C     ----
C
      DATA
C
     &  XOFF/26.03/, YOFF/0.0/, ZOFF/7.61/,
     &  DGSLEW    /'0F'X/,
     &  INIT      /30,30/,
     &  HEX       /'10'X,'20'X,'40'X/
C
C
C
C'
C
C<---------------------------------------------------------------->
C               END OF DATA BASE & LOCAL VARIABLES
C<---------------------------------------------------------------->
C
C
C
C
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~
C
C         START OF PROGRAM
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C
      ENTRY   RNAHRS
C     *****
C
C
C
C     """""""""""""""""""""""""""""""""""""""""""""
C     "                                           "
C     "    SECTION 100    AHRS INITIALIZATIONS    "
C     "                   AND INPUT MONITORING    "
C     "                                           "
C     """""""""""""""""""""""""""""""""""""""""""""
C
C
C     Execute module if freeze flag not on
C
      IF(.NOT.RNFREEZE)THEN
C
C 2000
C
C +------------------+
C | FIRST PASS LOGIC |
C +------------------+
C
        IF(FIRSTPAS)THEN
          FIRSTPAS = .FALSE.
C
C Initialize TYPE and LENGTH of output message  from the AHRS S/W to the AHRS
C controller.
C TYPE is always set to 0 to enable transmission and LENGTH of the output
C message is one byte,
C Output TYPE and LENGTH of AHRS 1
C
          MSGTYPA = 0
          MSGLENA = 1
C
C Output TYPE and LENGTH of AHRS 2
C
          MSGTYPB = 0
          MSGLENB = 1
        ENDIF
C
C 2100
C
C +--------------------------+
C | AHRS POWER INPUTS (CB's) |
C +--------------------------+
C
C Power is available if CBs are set and no malfunction present.
C
      CBPWR(1,1) = BILK01.AND..NOT.TF34I021(1)
      CBPWR(2,1) = BIRF01.AND..NOT.TF34I021(1)
      CBPWR(1,2) = BIAC08.AND..NOT.TF34I021(2)
      CBPWR(2,2) = BILF01.AND..NOT.TF34I021(2)
C
C If AHRS is running on auxiliary power then turn on AUX PR annunciator on
C controller
C
      IF(.NOT.BILK01.AND.BIRF01)THEN
         AUXPRANA = '04'X
      ELSE
         AUXPRANA = '0'X
      ENDIF
C
      IF(.NOT.BIAC08.AND.BILF01)THEN
         AUXPRANB = '04'X
      ELSE
         AUXPRANB = '0'X
      ENDIF
C
C 2150
C
C +-------------------------+
C | AHRS MALFUNCTION INPUTS |
C +-------------------------+
C
      MALFON = TF31041(1).OR.TF31041(2).OR.TF34I001(1).OR.TF34I001(2)
     &         .OR.TF34I011(1).OR.TF34I011(2).OR.TF34I021(1).OR.
     &         TF34I021(2)
C
C
C 2200
C
C +---------------------+
C | ADC VALIDITY INPUTS |
C +---------------------+
C
C The ADC transmits data through the FGCs bus controller. If both FGCs are
C dead then both AHRSs will enter BASIC mode since no bus is available for
C the transmission of TAS from the ADC to the AHRS.
C
      FGCVALID = BIAD05.OR.BIAD08
C
C ADC is valid if power to ADC is available and no TAS fail malfunction present
C If ON-SIDE TAS is lost and TAS is less than 60 knots then AHRU enters BASIC
C mode.
C
      IF (FGCVALID) THEN
C
         RNADCV(1)  = UBZ006A0.AND..NOT.TF34I001(1)
         RNADCV(2)  = UBZ006B0.AND..NOT.TF34I001(2)
C
C If ON-SIDE TAS is lost and TAS is greater than 60 knots and no ADC TEST
C active then AHRU reverts to opposite side TAS.
C
       IF((UBX006A.GT.60.).AND..NOT.RNADCV(1).AND..NOT.UBTEST(1))THEN
         RNADCV(1)=RNADCV(2)
       ENDIF
C
C If ADC TEST active then reset BASIC annunciator as soon as TEST complete.
C
       IF (UBTEST(1)) THEN
         TIME2(1)=10.
       ENDIF
C
C Monitor ADC-2 inputs
C
       IF((UBX006B.GT.60.).AND..NOT.RNADCV(2).AND..NOT.UBTEST(2))THEN
         RNADCV(2)=RNADCV(1)
       ENDIF
C
       IF (UBTEST(2)) THEN
         TIME2(2)=10.
       ENDIF
C
      ELSE
         RNADCV(1) = F
         RNADCV(2) = F
      ENDIF
C
C
C 2300
C
C +--------------------------------+
C | AHRU CENTER OF GRAVITY OFFSETS |
C +--------------------------------+
C
C     Note: for program execution time savings, both AHRU's
C     ----  are considered to be in the same locations
C
      RNXAHRU = VPILTXCG - XOFF
      RNYAHRU = YOFF
      RNZAHRU = VPILTZCG - ZOFF
C
C 2400
C
C  +----------------------------+
C  | AHRS CONTROLLER PROCESSING |
C  +----------------------------+
C
C
C PICK UP DATA SENT BY CONTROLLER AND STORE IT IN LOCAL BUFFER
C DATA FOR LEFT CONTROLLER IS FOUND IN LOCATIONS 2 TO 65 (JRCONA).
C DATA FOR RIGHT CONTROLLER IS FOUND IN LOCATIONS 70 TO 133 (JRCONB).
C
      K(1)    = JRHEADA-1
C
C LIMITING HEADER VALUES BETWEEN 1 AND 64.
C
      IF (K(1).LT.1) K(1)=1
      IF (K(1).GT.64) K(1)=64
C
      K(2)    = JRHEADB-69
      IF (K(2).LT.1) K(2)=1
      IF (K(2).GT.64) K(2)=64
C
      BUFF(1) = JRCONA(K(1))
      BUFF(2) = JRCONB(K(2))
C
C DECODE THE WORD AND DETERMINE THE AHRUs MODE OF OPERATION.
C
      DO N = 1, NAHRS
C
C VG/ERECT COMMAND IS NOT PROCESSED FOR THE FIRST 50 ITERATIONS
C
      IF (INIT(N).GT.0) INIT(N) = INIT(N) - 1
C
C DETERMINE WHETHER DG, VG ERECT OR TEST HAS BEEN SELECTED ON CONTROLLER.
C
        DO J = 1, 3
           TEMP(J,N) = IAND(BUFF(N),HEX(J))
C
           IF (TEMP(J,N).NE.PRETEMP(J,N)) THEN
              PRETEMP(J,N) = TEMP(J,N)
C
              IF (TEMP(J,N).NE.0) THEN
                IF (J.EQ.1) RNDGMODE(N) = .NOT.RNDGMODE(N)
C
C If VG/ERECT is pressed during alignment then to exit the
C alignment countdown mode VG/ERECT must be pressed again.
C
                 IF ((J.EQ.2).AND.(INIT(N).LE.0)) THEN
                   IF (RNALIGN(N).AND..NOT.(RNTEST(N).OR.
     &                 RNTSTFLG(N).OR.(RNCPTEST(N).GT.0.))) THEN
C
                      RNVGCMND(N) = .NOT.RNVGCMND(N)
                   ELSE
                      RNVGCMND(N) = .TRUE.
                   ENDIF
                 ENDIF
C
                IF (J.EQ.3) RNTSTFLG(N) = .TRUE.
              ELSE
                IF ((J.EQ.2).AND..NOT.RNALIGN(N)) RNVGCMND(N) = .FALSE.
                IF (J.EQ.3) RNTSTFLG(N) = .FALSE.
              ENDIF
           ENDIF
        ENDDO
C
C IF DG IS SELECTED ON CONTROLLER THEN CHECK IF ANY SLEWING IS REQUESTED.
C IF RNDGCMND = 0 THEN NO SLEW IN PROGRESS
C IF RNDGCMND = 10 THEN HEADING SLEWS AT 6 DEG/SEC
C IF RNDGCMND = 5 THEN HEADING SLEWS AT 20 DEG/SEC
C IF RNDGCMND = 9 THEN HEADING SLEWS AT -6 DEG/SEC
C IF RNDGCMND = 6 THEN HEADING SLEWS AT -20 DEG/SEC
C
           IF (RNDGMODE(N)) THEN
             RNDGCMND(N) = IAND(BUFF(N),DGSLEW)
           ELSE
             RNDGCMND(N) = 0
           ENDIF
C
C
C     """""""""""""""""""""""""""""""""""""""
C     "                                     "
C     "   SECTION 200    AHRS MODES LOGIC   "
C     "                                     "
C     """""""""""""""""""""""""""""""""""""""
C
C
C
C 2500
C
C  +-------------------------+
C  | AHRS POWER availability |
C  +-------------------------+
C
C If PRIMARY or AUXILIARY power is present and a non-resettable AHRS FAIL
C malfunction is not present then power present flag is set.
C
         RNPWR(N) = (CBPWR(1,N).OR.CBPWR(2,N)) .AND..NOT.TF34I011(N)
C
C     Power interruptions > 0.2 sec will shut down AHRS
C
         IF(RNPWR(N))THEN
            TIME1(N) = 0.
         ELSE IF(TIME1(N) .LT. 0.2)THEN
            TIME1(N) = TIME1(N) + YITIM
            RNPWR(N) = T
         ENDIF
C
C 2600
C
C +---------------------------------+
C | AHRS ALIGN and FAST ALIGN LOGIC |
C +---------------------------------+
C
C        ---------------
C        0 - OFF    MODE
C        1 - ALIGN  MODE
C        2 - BASIC  MODE
C        3 - NORMAL MODE
C        ---------------
C
C
C
C     Execute AHRS modes logic only when there is power to
C     system. Otherwise, execute power off logic only once
C
         IF(RNPWR(N))THEN
C
C Setting DARK CONCEPT feature.
C
            IF (FIRST(N)) THEN
              FIRST(N) = F
              T031041(N) = T
              T034I001(N)= T
              T034I011(N)= T
              T034I021(N)= T
            ENDIF
C
            RNSYSON(N)  = T
C
C   AHRS FAST ALIGN LOGIC
C   =====================
C
C     Align AHRS at once if FAST ALIGN pb via I/F is pressed
C
            IF(TCMAHRS) THEN
             RNALNTMR(N) = 181.
             TF31041(N) = F
             TF34I001(N)= F
             TF34I011(N)= F
             TF34I021(N)= F
             UCFALIGN = .TRUE.                  !COA S81-1-131 --jwm--
C
C If pitch or roll drift exists then reset drifts to 0.
C
             IF((RNPCHDFT(N).NE.0.).OR.(RNROLDFT(N).NE.0.)) THEN
               RNPCHDFT(N) = 0.
               RNROLDFT(N) = 0.
             ENDIF
            ENDIF
C
C
C   AHRS ALIGN MODE
C   ===============
C
C     Determine ALIGN status flag and AHRS 3 min align phase execution
C
            RNALIGN(N) = (RNALNTMR(N) .LT. 180.)
C
C     Pre-flight test is performed during the first 5 sec of alignment.
C
            RNTEST(N) = (RNALNTMR(N).LT.5.0)
C
C
            IF(RNALIGN(N))THEN
C
               RNMODE(N) = 1
C
C     Increment align timer.
C
               IF (.NOT.RNEXCM(N)) THEN
                  RNALNTMR(N) = RNALNTMR(N) + YITIM
               ENDIF
C
C FAST annunciator is illuminated during alignment if VG button is pressed to
C determine time left in alignment.
C
               IF (RNVGCMND(N) .AND. .NOT.(RNTEST(N).OR.RNTSTFLG(N)
     &             .OR.(RNCPTEST(N).GT.0.))) THEN
                 IF (N.EQ.1) THEN
                   FASTANNA = '10'X
                 ELSE
                   FASTANNB = '10'X
                 ENDIF
               ELSE IF (N.EQ.1) THEN
                   FASTANNA = '0'X
               ELSE
                   FASTANNB = '0'X
               ENDIF
C
C     Retrigger alignment process if excessive A/C movement is
C     detected during alignment phase in flight or on ground.
C
C If A/C aligning in air: AGRK1=T when A/C on ground
C (normal accel > 4g, or gyro rates > 4.125 deg/sec)
C
               IF (.NOT.AGRK1) THEN
                 IF((ABS(VAZB).GT.128.696) .OR. (ABS(HP).GT.4.125)
     &            .OR.(ABS(HQ).GT.4.125).OR.(ABS(HR).GT.4.125))THEN
C
                   RNEXCM(N) = T
                 ELSE IF (RNEXCM(N)) THEN
                   RNEXCM(N) = F
                   RNALNTMR(N)=5.
                 ENDIF
C
C If A/C aligning on ground then check ground speed components.
C
               ELSE IF((ABS(VVNS).GT.2.) .OR. (ABS(VVEW).GT.2.))THEN
                  RNEXCM(N) = T
               ELSE IF (RNEXCM(N)) THEN
                  RNEXCM(N) = F
                  RNALNTMR(N)=5.
               ENDIF
C
C AHRS will enter NORMAL/BASIC and SLAVED/DG modes only if alignment is
C completed and no excess motion was detected during alignment on gnd.
C
            ELSE IF (RNALNTMR(N).GT.180.) THEN
C
C 2700
C
C +------------------------+
C | AHRS NORMAL/BASIC MODE |
C +------------------------+
C
C If AHRS was in countdown while in alignment then exit countdown mode
C
            IF (RNVGCMND(N)) THEN
              IF (ONCE(N)) THEN
                ONCE(N) = F
                RNVGCMND(N) = F
              ENDIF
            ENDIF
C
C  AHRS Validity OUTPUTS
C  =====================
C
               RNPCHV(N)  = T
               RNROLV(N)  = T
               RNPCHRV(N) = T
               RNROLRV(N) = T
               RNMHDGV(N) = T
               RNRMIV(N)  = T
               RNHDGRV(N) = T
               RNYAWRV(N) = T
               RNALATV(N) = T
               RNANRMV(N) = T
               RNALNGV(N) = T
               RNSLCOF(N) = T
C
C
               IF(RNADCV(N))THEN
                  RNMODE(N)   = 3
               ELSE
                  RNMODE(N)   = 2
               ENDIF
C
C     BASIC to NORMAL mode transition takes 10 sec
C
               IF((RNMODE(N).EQ.3) .AND. .NOT.RNNORM(N))THEN
                  TIME2(N) = TIME2(N) + YITIM
                  IF(TIME2(N) .GE. 10)THEN
                     RNBASIC(N) = F
                     RNNORM(N)  = T
                     TIME2(N)   = 0
C
C Turn off BASIC annunciator
C
                    IF(N.EQ.1)THEN
                       BASICANA = '0'X
                    ELSE
                       BASICANB = '0'X
                    ENDIF
                  ENDIF
               ELSE IF(RNMODE(N) .EQ. 2)THEN
                  RNNORM(N) = F
                  RNBASIC(N)= T
C
C Turn on BASIC annunciator
C
                 IF(N.EQ.1)THEN
                    BASICANA = '02'X
                 ELSE
                    BASICANB = '02'X
                 ENDIF
               ENDIF
C
C ATTITUDE outputs are failed if gyro fast erect is requested in BASIC mode
C
            IF(RNVGCMND(N))THEN
               RNPCHV(N)  = F
               RNROLV(N)  = F
               RNPCHRV(N) = F
               RNROLRV(N) = F
C
C Turn on FAST annunciator
C
               IF(N.EQ.1) THEN
                  FASTANNA = '10'X
               ELSE
                  FASTANNB = '10'X
               ENDIF
C
C Turn off FAST annunciator
C
            ELSE
               IF (N.EQ.1) THEN
                  FASTANNA = '0'X
               ELSE
                  FASTANNB = '0'X
               ENDIF
            ENDIF
C
C 2750
C
C +-----------------+
C | DG/SLAVED MODES |
C +-----------------+
C
C When DG  is selected on controller then set flag to turn on DG annunciator
C on controller
C
            IF (RNDGMODE(N)) THEN
              IF(N.EQ.1)THEN
                 DGANNA = '01'X
              ELSE
                 DGANNB = '01'X
              ENDIF
C
C When a slew command is initiated while in DG mode then HDG flag is in view
C
              IF (RNDGCMND(N).NE.0) THEN
                RNMHDGV(N) = F
                RNRMIV(N)  = F
              ENDIF
C
C Turn off DG annunciator
C
            ELSE IF (N.EQ.1) THEN
               DGANNA = '0'X
            ELSE
               DGANNB = '0'X
            ENDIF
C
C Fail HDG outputs if HDG malfunction is set and DG MODE is not requested via
C the controller else heading parameters are valid
C
              IF(TF31041(N))THEN
C
C Turn on SLAVE annunciator
C
                 IF(N.EQ.1)THEN
                    SLAVANNA = '08'X
                 ELSE
                    SLAVANNB = '08'X
                 ENDIF
C
C Fail heading if not in DG mode
C
                 IF(.NOT.RNDGMODE(N))THEN
                   RNMHDGV(N) = F
                   RNRMIV(N)  = F
                   RNHDGRV(N) = F
                   RNYAWRV(N) = F
                 ENDIF
C
C Turn off SLAVE annunciator
C
              ELSE IF(N.EQ.1) THEN
                 SLAVANNA = '0'X
              ELSE
                 SLAVANNB = '0'X
              ENDIF
C
            ENDIF            ! ENDIF FOR RNALIGN
C
C
C 2800
C
C +----------------+
C | AHRS TEST MODE |
C +----------------+
C
C Pre-flight test done during the first 5 seconds of alignment.
C
            IF(RNTEST(N))THEN
C
               TESTCOMP(N) = .FALSE.
C
C Turn on all annunciators for the duration of the TEST procedure.
C
               IF (N.EQ.1) THEN
                 DGANNA   = '01'X
                 BASICANA = '02'X
                 AUXPRANA = '04'X
                 SLAVANNA = '08'X
                 FASTANNA = '10'X
               ELSE
                 DGANNB   = '01'X
                 BASICANB = '02'X
                 AUXPRANB = '04'X
                 SLAVANNB = '08'X
                 FASTANNB = '10'X
               ENDIF
C
               RNTESTMR(N) = RNTESTMR(N) + YITIM
C
               IF(RNTESTMR(N) .LT. 2.5)THEN
                  RNPCHVO(N)  = T
                  RNROLVO(N)  = T
                  RNPCHRVO(N) = T
                  RNROLRVO(N) = T
                  RNMHDGVO(N) = T
                  RNRMIVO(N)  = T
                  RNHDGRVO(N) = T
                  RNYAWRVO(N) = T
                  RNALATVO(N) = T
                  RNANRMVO(N) = T
                  RNALNGVO(N) = T
                  RNSLCOFO(N) = F
C
               ELSE
                  RNPCHVO(N)  = F
                  RNROLVO(N)  = F
                  RNPCHRVO(N) = F
                  RNROLRVO(N) = F
                  RNMHDGVO(N) = F
                  RNHDGRVO(N) = F
                  RNYAWRVO(N) = F
                  RNALATVO(N) = F
                  RNANRMVO(N) = F
                  RNALNGVO(N) = F
                  RNSLCOFO(N) = F
               ENDIF
            ELSE IF (.NOT. TESTCOMP(N)) THEN
               TESTCOMP(N) = .TRUE.
               RNTESTMR(N) = 0.
               RNRMIVO(N)  = F
C
C Turn off annunciators at end of TEST
C
               IF (N.EQ.1) THEN
                 DGANNA   = '0'X
                 BASICANA = '0'X
                 AUXPRANA = '0'X
                 SLAVANNA = '0'X
                 FASTANNA = '0'X
               ELSE
                 DGANNB   = '0'X
                 BASICANB = '0'X
                 AUXPRANB = '0'X
                 SLAVANNB = '0'X
                 FASTANNB = '0'X
               ENDIF
C
            ENDIF
C
C AHRS TEST initiated by pressing TEST pb. on controller
C RNTSTFLG is set during the first 5 seconds of test. If test goes beyond
C 5 seconds then rntstlch is set to TRUE and stays TRUE as long as the
C TEST pushbutton is pressed.
C
            IF (RNTSTFLG(N).AND..NOT.PRETEST(N)) THEN
                RNCPTEST(N) = 5.0
            ENDIF
C
            PRETEST(N) = RNTSTFLG(N)
            IF (RNCPTEST(N).GE.0.) RNCPTEST(N)=RNCPTEST(N)-YITIM
C
            IF(RNTSTFLG(N) .OR. (RNCPTEST(N).GT.0.))THEN
C
               TSTCOMP2(N)=.FALSE.
C
C At the end of TEST if A/C is on ground and not in align mode then the AHRS
C will stay in TEST mode for another 30 sec.
C
               ENDOFTST1(N)=AGRK1.AND..NOT.RNALIGN(N)
C
C Turn on all annunciators for the entire 5 seconds.
C
               IF (N.EQ.1) THEN
                 DGANNA   = '01'X
                 BASICANA = '02'X
                 AUXPRANA = '04'X
                 SLAVANNA = '08'X
                 FASTANNA = '10'X
               ELSE
                 DGANNB   = '01'X
                 BASICANB = '02'X
                 AUXPRANB = '04'X
                 SLAVANNB = '08'X
                 FASTANNB = '10'X
               ENDIF
C
C During the first 2.5 seconds TEST values are outputted
C
               IF(RNCPTEST(N) .GT. 2.5)THEN
                  RNPCHVO(N)  = T
                  RNROLVO(N)  = T
                  RNPCHRVO(N) = T
                  RNROLRVO(N) = T
                  RNMHDGVO(N) = T
                  IF (.NOT.RNALIGN(N)) RNRMIVO(N) = T
                  RNHDGRVO(N) = T
                  RNYAWRVO(N) = T
                  RNALATVO(N) = T
                  RNANRMVO(N) = T
                  RNALNGVO(N) = T
                  RNSLCOFO(N) = F
C
               ELSE
                  RNPCHVO(N)  = F
                  RNROLVO(N)  = F
                  RNPCHRVO(N) = F
                  RNROLRVO(N) = F
                  RNMHDGVO(N) = F
                  RNHDGRVO(N) = F
                  RNYAWRVO(N) = F
                  RNALATVO(N) = F
                  RNANRMVO(N) = F
                  RNALNGVO(N) = F
                  RNSLCOFO(N) = F
               ENDIF
            ELSE IF (.NOT. TSTCOMP2(N)) THEN
               TSTCOMP2(N) = .TRUE.
C
C At the end of the TEST, all parameters become valid if A/C is on ground
C and not in align mode then the AHRS remains in test mode for another 30 sec.
C
               IF (ENDOFTST1(N)) THEN
                  RNPCHVO(N)  = T
                  RNROLVO(N)  = T
                  RNPCHRVO(N) = T
                  RNROLRVO(N) = T
                  RNMHDGVO(N) = T
                  RNHDGRVO(N) = T
                  RNYAWRVO(N) = T
                  RNALATVO(N) = T
                  RNANRMVO(N) = T
                  RNALNGVO(N) = T
                  RNSLCOFO(N) = T
               ENDIF
C
C Turn off annunciators at end of TEST
C
               IF (N.EQ.1) THEN
                 DGANNA   = '0'X
                 BASICANA = '0'X
                 AUXPRANA = '0'X
                 SLAVANNA = '0'X
                 FASTANNA = '0'X
               ELSE
                 DGANNB   = '0'X
                 BASICANB = '0'X
                 AUXPRANB = '0'X
                 SLAVANNB = '0'X
                 FASTANNB = '0'X
               ENDIF
C
            ENDIF
C
C
C
C 2900
C
C Set POWER DOP for AHRS controllers
C
      RN$D28V(N) = T
C
C +---------------------------------+
C | OUTPUT DATA TO AHRS CONTROLLERS |
C +---------------------------------+
C
C AHRS 1 CONTROLLER
C -----------------
C
      IF(N.EQ.1) THEN
C
         RNTYPA    = MSGTYPA
C
C If ADVISORY LIGHT TEST is on then turn on all annunciators
C
         IF (AMRLK1) THEN
            RNCONA(1) = '1F'X
         ELSE
            RNCONA(1) = DGANNA + BASICANA + AUXPRANA +
     &                  SLAVANNA + FASTANNA
         ENDIF
         RNLENA    = MSGLENA
         RNIDA     = RNIDA + 1
C
      ELSE
C
C AHRS 2 CONTROLLER
C -----------------
C
         RNTYPB    = MSGTYPB
C
C If ADVISORY LIGHT TEST is on then turn on all annunciators
C
         IF (AMRLK1) THEN
            RNCONB(1) = '1F'X
         ELSE
            RNCONB(1) = DGANNB + BASICANB + AUXPRANB +
     &                  SLAVANNB + FASTANNB
         ENDIF
         RNLENB    = MSGLENB
         RNIDB     = RNIDB + 1
C
      ENDIF
C
C 3000
C
C +----------------------+
C | AHRS POWER OFF LOGIC |
C +----------------------+
C
C
         ELSE
C
C If both power CBs are reset during the AHRS FAIL - resettable
C malfunction then the malfunction is automatically reset.
C
          IF(TF34I021(N))THEN
             PR_CBOUT(N) = CBOUT(N)
             IF (N.EQ.1) THEN
               CBOUT(1)   = .NOT.(BILK01.AND.BIRF01)
             ELSE
               CBOUT(2)   = .NOT.(BIAC08.AND.BILF01)
             ENDIF
C
             IF(PR_CBOUT(N).AND..NOT.CBOUT(N))THEN
               TF34I021(N) = F
             ENDIF
          ELSE
             CBOUT(N)=F
          ENDIF
C
            RN$D28V(N)   = F
            RNALNTMR(N) = 0.
            RNTESTMR(N) = 0.
            RNMODE(N)   = 0
            RNTEST(N)   = F
            RNSYSON(N)  = F
            RNNORM(N)   = F
            RNBASIC(N)  = F
            RNALIGN(N)  = F
            RNEXCM(N)   = F
            RNDGMODE(N) = F
            FIRST(N)    = T
            ONCE(N)     = T
C
C     Reset all AHRS validities
C
            RNPCHV(N)  = F
            RNROLV(N)  = F
            RNMHDGV(N) = F
            RNRMIV(N)  = F
            RNHDGRV(N) = F
            RNPCHRV(N) = F
            RNROLRV(N) = F
            RNYAWRV(N) = F
            RNALATV(N) = F
            RNANRMV(N) = F
            RNALNGV(N) = F
            RNSLCOF(N) = F
         ENDIF                ! ENDIF OF RNPOWER
C
      ENDDO
C
C
C 3100
C
C     """"""""""""""""""""""""""""""""""""""""""""""
C     "                                            "
C     "   SECTION 300     AHRS DISCRETE OUTPUTS    "
C     "                                            "
C     """"""""""""""""""""""""""""""""""""""""""""""
C
C
C     Determine  AHRS FAST ALIGN  availability  (I/F amber light)
C     (available when either AHRU is in ALIGN mode or in BASIC mode)
C
            TCM0AHRS = RNALIGN(1) .OR. RNALIGN(2).OR.MALFON.OR.
     &                 (RNMODE(1).EQ.2).OR.(RNMODE(2).EQ.2)
C
C
C
      ENDIF    !  END IF FOR FREEZE FLAG
C
C
C     ------------------ END OF AHRS MODES PROGRAM ------------------
C
C
      RETURN
      END
C Comment for forport to recognize end of file
