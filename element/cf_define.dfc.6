!
! ----------------------------------------------------------
! STANDARD FLIGHT CONTROLS LABEL DEFINITIONS
! ----------------------------------------------------------
!
! Valve tuning parameters
!
LABEL  KA        /REAL    /WR     /PL=2      /MIN=0.0       /MAX=100.0     /DESCR="Servo Acceleration Gain"   /SAVE
LABEL  KV        /REAL    /WR     /PL=2      /MIN=0.0       /MAX=500.0     /DESCR="Servo Velocity Gain"       /SAVE
LABEL  KP        /REAL    /WR     /PL=2      /MIN=0.0       /MAX=100.0     /DESCR="Servo Position error Gain" /SAVE
LABEL  KI        /REAL    /WR     /PL=2      /MIN=0.0       /MAX=2.0       /DESCR="Current Gradient"          /SAVE
LABEL  IPE       /ENUM    /WR     /CAT=ONOFF                     /DESCR="Position error current compensation" /NOSAVE
LABEL  IAOS      /REAL    /WR     /PL=4      /MIN=-20.0     /MAX=20.0      /DESCR="Current Offset"            /SAVE
!
! Valve tuning parameters
!
LABEL  KANOR     /REAL    /READ   /PL=5      /MIN=0.0       /MAX=100.0     /DESCR="Servo Acceleration Gain"   /NOSAVE
LABEL  KVNOR     /REAL    /READ   /PL=5      /MIN=0.0       /MAX=100.0     /DESCR="Servo Velocity Gain"       /NOSAVE
LABEL  KPNOR     /REAL    /READ   /PL=5      /MIN=0.0       /MAX=100.0     /DESCR="Servo Position error Gain" /NOSAVE
LABEL  IALC      /REAL    /WR     /PL=2      /MIN=0.0       /MAX=20.0      /DESCR="Current Limit Command"     /SAVE
!
! Backdrive Labels
!
LABEL  MTSTF     /REAL    /WR     /PL=3      /MIN=-150.0    /MAX=150.0     /DESCR="Test force"                /NOSAVE
LABEL  MBMOD     /ENUM    /WR      /CAT=BDRIVE                              /DESCR="Backdrive mode"            /NOSAVE
LABEL  MBPOS     /REAL    /WR      /PL=2      /MIN=-180.0    /MAX=180.0     /DESCR="Backdrive position"        /NOSAVE
LABEL  BDAMP     /REAL    /WR      /PL=2      /MIN=0.0       /MAX=180.0     /DESCR="SINE backdrive amplitude"  /NOSAVE
LABEL  BDFREQ    /REAL    /WR      /PL=2      /MIN=0.0       /MAX=180.0     /DESCR="SINE backdrive frequency"  /NOSAVE
LABEL  BDLAG     /REAL    /WR      /PL=2      /MIN=0.0       /MAX=10.0      /DESCR="Backdrive lag constant"    /SAVE  
LABEL  BDLIM     /REAL    /WR      /PL=2      /MIN=0.0       /MAX=100.0     /DESCR="Backdrive Rate Limit"      /SAVE  
LABEL  BDFOR     /REAL    /WR      /PL=2      /MIN=0.0       /MAX=50.0      /DESCR="Backdrive Override Force"  /SAVE  
LABEL  BDOVRG    /REAL    /WR      /PL=2      /MIN=0.0       /MAX=50.0      /DESCR="Backdrive Override Velocity Gain" /SAVE  
!
LABEL  CALMOD   /ENUM    /CAT=CALMODE  /WR                                 /DESCR="Calibration Mode"    /NOSAVE  
LABEL  CALFOR   /REAL    /WR      /PL=2      /MIN=0.0       /MAX=200.0     /DESCR="Calibration Notch Force Level"  /SAVE  
LABEL  CALKN    /REAL    /WR      /PL=2      /MIN=0.0       /MAX=200.0     /DESCR="Calibration Notch Stiffness"    /SAVE  
LABEL  CALDMP   /REAL    /WR      /PL=4      /MIN=0.0       /MAX=10.0      /DESCR="Calibration Mode Damping Incr"  /SAVE  
LABEL  CALIMF   /REAL    /WR      /PL=2      /MIN=0.0       /MAX=200.0     /DESCR="Calibration Mode IMF"           /SAVE  
!
LABEL  KIMF      /REAL    /WR   /PL=4      /MIN=0.0       /MAX=10.0      /DESCR="Inverse Forward Mass Gain"         /SAVE
LABEL  KFDMP     /REAL    /WR   /PL=4      /MIN=0.0       /MAX=10.0      /DESCR="Forward Damping Gain"              /SAVE
!
! Monitor Labels
!
LABEL  IA        /REAL    /READ    /PL=2      /MIN=-20.0     /MAX=20.0      /DESCR="Actual Current"            /NOSAVE
LABEL  IAL       /REAL    /READ    /PL=2      /MIN=0.0       /MAX=100.0     /DESCR="Actual Current Limit"      /NOSAVE
LABEL  FPU       /REAL    /READ    /PL=3      /MIN=-1024.    /MAX=1024.     /DESCR="Actuator force"            /NOSAVE
LABEL  AFOR      /REAL    /READ    /PL=3      /MIN=-300.0    /MAX=300.0     /DESCR="Pilot/Actuator Force"      /NOSAVE
LABEL  DFOR      /REAL    /READ    /PL=3      /MIN=-300.0    /MAX=300.0     /DESCR="Driving Force"             /NOSAVE
LABEL  XPU       /REAL    /READ    /PL=4      /MIN=-2.0      /MAX=2.0       /DESCR="Actuator position"         /NOSAVE
LABEL  XP        /REAL    /READ    /PL=3      /MIN=-20.0     /MAX=20.0      /DESCR="Control Position"          /NOSAVE
LABEL  PE        /REAL    /READ    /PL=4      /MIN=-5.0      /MAX=5.0       /DESCR="Position Error"            /NOSAVE
LABEL  DPOS      /REAL    /READ    /PL=3      /MIN=-150.0    /MAX=150.0     /DESCR="Demanded Position"         /NOSAVE
LABEL  FPOS      /REAL    /READ    /PL=3      /MIN=-150.0    /MAX=150.0     /DESCR="Fokker Position"           /NOSAVE
LABEL  DVEL      /REAL    /READ    /PL=2      /MIN=-150.0    /MAX=150.0     /DESCR="Demanded Velocity"         /NOSAVE
LABEL  HTSTF     /REAL    /READ    /PL=3      /MIN=-150.0    /MAX=150.0     /DESCR="Host Test force"            /NOSAVE
LABEL  KCUR      /REAL    /READ   /PL=3      /MIN=-10.0     /MAX=10.0      /DESCR="Current Scaling Factor"    /NOSAVE
LABEL  MF        /REAL    /READ   /PL=3      /MIN=-10.0     /MAX=10.0      /DESCR="Mechanical Friction"       /NOSAVE
LABEL  FOS       /REAL    /READ   /PL=3      /MIN=-50.0     /MAX=50.0      /DESCR="Force Offset"              /NOSAVE
!
LABEL  POS       /REAL    /WR     /PL=4      /MIN=-2.0      /MAX=2.0       /DESCR="Position Offset"           /SAVE
LABEL  ZMPOS     /REAL    /WR      /PL=4      /MIN=-1.0      /MAX=1.0       /DESCR="Mechanical Compliance, Positive Force" /SAVE
LABEL  ZMNEG     /REAL    /WR      /PL=4      /MIN=-1.0      /MAX=1.0       /DESCR="Mechanical Compliance, Negative Force" /SAVE
!
LABEL  VARI      /REAL    /READ  /WRITE   /PL=2  /MIN=0.0   /MAX=5000.0    /DESCR="Aft Notch Positive Level"   /SAVE
LABEL  QPOS      /REAL    /READ    /PL=3      /MIN=-150.0    /MAX=150.0     /DESCR="Equivalent Position"       /NOSAVE
LABEL  MFOR      /REAL    /READ    /PL=3      /MIN=-300.0    /MAX=300.0     /DESCR="Model Force"      /NOSAVE
LABEL  FEELSFO   /REAL    /READ    /PL=3      /MIN=-300.0    /MAX=300.0     /DESCR="Spring Force"      /NOSAVE
LABEL  FFMF      /REAL    /READ    /PL=3      /MIN=0.0       /MAX=100.0     /DESCR="Forward Friction minus Mechanical Friction"    /NOSAVE
LABEL  FEELSFR      /REAL    /READ    /PL=3      /MIN=0.0       /MAX=100.0     /DESCR="Spring/Total Friction"     /NOSAVE
LABEL  AFRI      /REAL    /READ    /PL=3      /MIN=0.0       /MAX=100.0     /DESCR="Aft Friction"              /NOSAVE
!
!
! Safety Page Labels
!
LABEL       FSAFVAL   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Force Value"               /NOSAVE
LABEL       MSAFVAL   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Force*Velocity Value"      /NOSAVE
LABEL       NSAFVAL   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Negative Force*Velocity Value"  /NOSAVE
LABEL       VSAFVAL   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Velocity Value"            /NOSAVE
LABEL       PSAFVAL   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Position Error Value"      /NOSAVE
LABEL       BSAFVAL   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Bungee Velocity Error Value"      /NOSAVE
!
LABEL       FSAFMAX   /REAL  /READ   /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Force Maximum"             /NOSAVE
LABEL       MSAFMAX   /REAL  /READ   /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Force*Velocity Maximum"    /NOSAVE
LABEL       NSAFMAX   /REAL  /READ   /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Negative Force*Velocity Maximum"    /NOSAVE
LABEL       VSAFMAX   /REAL  /READ   /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Velocity Maximum"          /NOSAVE
LABEL       PSAFMAX   /REAL  /READ   /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Position Error Maximum"    /NOSAVE
LABEL       BSAFMAX   /REAL  /READ   /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Bungee Velocity Error Maximum"    /NOSAVE
!
LABEL       FSAFLIM   /REAL  /WRITE /READ /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Force Limit"               /SAVE
LABEL       MSAFLIM   /REAL  /WRITE /READ /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Force*Velocity Limit"      /SAVE
LABEL       VSAFLIM   /REAL  /WRITE /READ /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Velocity Limit"            /SAVE
LABEL       PSAFLIM   /REAL  /WRITE /READ /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Position Error Limit"      /SAVE
LABEL       BSAFLIM   /REAL  /WRITE /READ /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Bungee Velocity Error Limit"      /SAVE
!
LABEL       NSAFLIM   /REAL  /WRITE /READ /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Negative Force*Velocity Limit"      /SAVE
LABEL       NSAFUPR   /REAL  /WR    /PL=1   /MIN=-500.0   /MAX=500.0  /DESCR="Neg For*Vel Effective Range Upper Limit"      /SAVE
LABEL       NSAFLWR   /REAL  /WR    /PL=1   /MIN=-500.0   /MAX=500.0  /DESCR="Neg For*Vel Effective Range Lower Limit"      /SAVE
!
LABEL       FSAFSAF   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Maximum Allowed Force Limit" /SAVE
LABEL       MSAFSAF   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Maximum Allowed Force*Velocity Limit"      /SAVE
LABEL       NSAFSAF   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Maximum Allowed Negative Force*Velocity Limit"      /SAVE
LABEL       VSAFSAF   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Maximum Allowed Velocity Limit"            /SAVE
LABEL       PSAFSAF   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Maximum Allowed Position Error Limit"      /SAVE
LABEL       BSAFSAF   /REAL  /READ    /PL=1   /MIN=0.0   /MAX=100000.0  /DESCR="Maximum Allowed Bungee Velocity Error Limit"      /SAVE
!
LABEL       FSAFFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Force Limit Failure"          /NOSAVE
LABEL       MSAFFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Force*Velocity Limit Failure" /NOSAVE
LABEL       NSAFFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Negative Force*Velocity Limit Failure" /NOSAVE
LABEL       VSAFFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Velocity Limit Failure"       /NOSAVE
LABEL       PSAFFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Position Error Limit Failure" /NOSAVE
LABEL       BSAFFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Bungee Velocity Error Limit Failure" /NOSAVE
LABEL       BPWRFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Buffer Unit power failure"    /NOSAVE
LABEL       DSCNFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Buffer Unit Disconnect Failure"    /NOSAVE
LABEL       FTRNFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Force Input Transient Failure"    /NOSAVE
LABEL       PTRNFL  /ENUM   /READ    /CAT=CL_STATUS  /DESCR="Position Input Transient Failure"    /NOSAVE
!
LABEL       FSAFTST   /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Force Safety Test"              /NOSAVE
LABEL       MSAFTST   /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Force*Velocity Safety Test"     /NOSAVE
LABEL       NSAFTST   /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Negative Force*Velocity Safety Test"     /NOSAVE
LABEL       VSAFTST   /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Velocity Safety Test"           /NOSAVE
LABEL       PSAFTST   /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Position Error Safety Test"     /NOSAVE
LABEL       BSAFTST   /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Bungee Velocity Error Safety Test"     /NOSAVE
LABEL       BPWRTST   /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Buffer Unit Power Safety Test"  /NOSAVE
LABEL       DSCNTST   /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Buffer Unit Disconnect Failure Test"    /NOSAVE
LABEL       FTRNTST    /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Force Input Transient Failure Test"    /NOSAVE
LABEL       PTRNTST    /ENUM   /READ /WRITE   /CAT=CL_TEST    /DESCR="Position Input Transient Failure Test"    /NOSAVE
!
LABEL       LUTYPE     /ENUM    /READ    /CAT=CL_TYPE      /MIN=0     /MAX=20        /DESCR="LOAD UNIT TYPE"               /NOSAVE
LABEL       SAFDSBL   /BOOLEAN /READ  /WRITE    /DESCR="SAFETY DISABLE FLAG " /NOSAVE
LABEL       BSENABL   /BOOLEAN /READ  /WRITE    /DESCR="BUNGEE SAFETY ENABLE FLAG " /NOSAVE
LABEL       FLDSABL   /BOOLEAN /READ  /WRITE    /DESCR="FORCE SAFTEY DISABLE FLAG " /NOSAVE
 
LABEL       _IN_STB  /BOOLEAN   /READ    /DESCR="B.U. in Standby mode"         /NOSAVE
LABEL       _IN_NRM  /BOOLEAN   /READ    /DESCR="B.U. in Normal mode "         /NOSAVE
LABEL       _CMP_IT  /BOOLEAN   /READ    /DESCR="Computer itter. toggle to B.U. "     /NOSAVE
LABEL       _HY_RDY  /BOOLEAN   /READ    /DESCR="Hydr. ready signal to B.U. " /NOSAVE
LABEL       _STB_RQ  /BOOLEAN   /READ    /DESCR="Request to B.U. for Standby mode "    /NOSAVE
!
! Testing variables
!
!LABEL  STB_RQ    /ENUM   /CAT=ONOFF     /WR                               /DESCR="Standby mode"            /NOSAVE
!
! Calibration 
!
LABEL  CALAPOS /DIM=1 /BOU=(11)   /REAL  /WR   /PL=3  /MIN=-2.0      /MAX=2.0       /DESCR="Actuator Position Breakpoint" /SAVE
LABEL  CALPPOS /DIM=1 /BOU=(11)   /REAL  /WR   /PL=2  /MIN=-1000.0   /MAX=1000.0    /DESCR="Pilot Position"               /SAVE
LABEL  CALGEAR /DIM=1 /BOU=(11)   /REAL  /WR   /PL=3  /MIN=0.0       /MAX=1.0       /DESCR="Force Gearing"                /SAVE
LABEL  CALFRIC /DIM=1 /BOU=(11)   /REAL  /WR   /PL=2  /MIN=0.0       /MAX=50.0      /DESCR="Mecanical Friction"           /SAVE
LABEL  CALFORC /DIM=1 /BOU=(11)   /REAL  /WR   /PL=3  /MIN=-1000.0   /MAX=1000.0    /DESCR="Force Offset"                 /SAVE
LABEL  CALCNT     /INTEGER     /WR                    /MIN=0         /MAX=1000      /DESCR="?"                            /SAVE
LABEL  CALCHG     /BOOLEAN     /WR /READ                                            /DESCR="Calibration change flag"      /SAVE
!
! Feelspring
!
LABEL  FFRI      /REAL    /WR     /PL=3      /MIN=0.0       /MAX=500.0     /DESCR="Forward friction"          /SAVE
LABEL  FEELAFT   /ENUM    /CAT=FAFT  /READ                                  /DESCR="Feelspring Mode"     /SAVE  
!LABEL  FEELTYP   /ENUM    /CAT=FTYPE /READ                                  /DESCR="Feelspring Type"     /NOSAVE  
LABEL  KN        /REAL    /WR     /PL=2      /MIN=0.0       /MAX=500.0     /DESCR="Aft Notch Stiffness"        /SAVE
LABEL  NPL       /REAL    /WR     /PL=2      /MIN=0.0       /MAX=500.0     /DESCR="Aft Notch Positive Level"   /SAVE
LABEL  NNL       /REAL    /WR     /PL=2      /MIN=-500.0    /MAX=0.0       /DESCR="Aft Notch Positive Level"   /SAVE
!
LABEL  FEELBCN    /INTEGER  /WRITE                        /SAVE
LABEL  FEELCCN    /INTEGER  /WRITE                        /SAVE
LABEL  FEELPOS    /REAL    /DIM=1 /BOU=(17) /WR /PL=2     /SAVE
LABEL  FEELFOR    /REAL    /DIM=2 /BOU=(17,7) /WR /PL=2   /SAVE
LABEL  FEELFRI    /REAL    /DIM=2 /BOU=(17,7) /WR /PL=2   /SAVE
LABEL  FEELCRV    /REAL    /DIM=1 /BOU=(7) /WR /PL=2      /SAVE
LABEL  FEELCHG    /BOOLEAN /DIM=1 /BOU=(7) /WR            /SAVE
LABEL  FEELERR    /ENUM /READ /CAT=FERROR /DESCR="Feelspring error type" /NOSAVE
!
