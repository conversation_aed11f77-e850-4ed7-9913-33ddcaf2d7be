C
C'Title          Dash 8 Flight Guidance Computer Roll Axis module
C'Module_id      USD8SR
C'Entry_point    SROLL
C'Documentation  [tbd]
C'Customer       US Air
C'Author         <PERSON><PERSON><PERSON>/<PERSON><PERSON>'Date           July 1991
C
C'System         Autoflight
C'Itrn           133 msec
C'Process        Synchronous process
C
C'Revision_history
C
C  usd8sr.for.25  8Dec1992 15:31 usd8 M.WARD
C       < FIX FOR SNAG 749 >
C
C  usd8sr.for.24 28Aug1992 21:44 usd8 M.WARD
C       < TUNING CONSTANT SRKFD HALVED >
C
C  usd8sr.for.23 27Aug1992 01:20 usd8 PUT BAR
C       <
C
C  usd8sr.for.22 15Jul1992 11:46 usd8 M.WARD
C       < REF SNAG 1357, F/D TOO SENSITIVE. PUT BACK OLDER EQUATION FOR
C         EVALUATION >
C
C  usd8sr.for.21 10Jul1992 20:31 usd8 sbw
C       <
C
C  usd8sr.for.20 10Jul1992 17:02 usd8 Sbw
C       < trying to reduce the engage bump >
C
C  usd8sr.for.19  4Jul1992 11:35 usd8 steve W
C       < changed all references to CAILL to (CIAFDPOS*.35), the servo
C         drives the wheel in the dash 8 and not the surface so small
C         differences between the wheel position and the surface position
C         were causing the wheel to jump when engaging the A/P >
C
C  usd8sr.for.18 24Jun1992 19:47 usd8 SBRIERE
C       < RECODED FD BAR COMMAND >
C
C  usd8sr.for.17 24Jun1992 15:03 usd8 SBRIERE
C       < SYNC OLD VALUE OF HDG ERROR WITH REPOSITION FLAG >
C
C  usd8sr.for.16 23Jun1992 12:10 usd8 sbriere
C       < put back fd in view when usind TCS >
C
C  usd8sr.for.15 21Jun1992 18:25 usd8 SBRIERE
C       < coded HDG/CRS ERROR cb logic >
C
C  usd8sr.for.14 17Jun1992 20:15 usd8 SBRIERE
C       < RECODED BC COURSE ERROR INPUT >
C
C  usd8sr.for.13 16Apr1992 21:25 usd8 SBRIERE
C       < PUT FD BAR OUT OF VIEW WHEN FGC IS INVALID >
C
C  usd8sr.for.12 13Apr1992 03:06 usd8 sbriere
C       < mod to SINOMEGA COSOMEGA since they are 120 deg out of phase >
C
C  usd8sr.for.10 25Mar1992 18:17 usd8 SBRIERE
C       < PUT MISTRIM MONITORING ONLY DURING A/P ENGAGED >
C
C  usd8sr.for.9 25Mar1992 17:28 usd8 SBRIERE
C       < PUT L_FADE1 AND 2 TO LARGE VALUES >
C
C  usd8sr.for.8 12Mar1992 16:10 usd8 sbriere
C       < boosted roll trim threshold to 8.0 >
C
C  usd8sr.for.7 12Mar1992 15:36 usd8 sbriere
C       < put TAU01 for loc dev lag to 0.2 instead of 2.0 >
C
C  usd8sr.for.6 10Mar1992 21:07 usd8 sbriere
C       < increased bank angle limit to 45 deg >
C
C  usd8sr.for.5  9Mar1992 14:06 usd8 SBRIERE
C       < RESYNC O_DMEDOT WHEN VORARMSS IS TRUE >
C
C  usd8sr.for.4  5Mar1992 19:53 usd8 sbriere
C       < added hdgoff of 90 deg >
C
C  usd8sr.for.3  1Mar1992 14:54 usd8 sbriere
C       < put back sign of fd roll command since i reverse calibration >
C
C  usd8sr.for.2 28Feb1992 05:02 usd8 sbriere
C       < reverse sign of fd roll command output to adi >
C
C  usd8sr.for.1 28Feb1992 00:37 usd8 sbriere
C       < added coment at end of module for forport >
C
C'
C
C'Compilation_directives
C
C    - Compile and add to SHIP library.
C      It must be FPC'd, compiled and put again in SHIP
C      library after each CDB update
C
C'
C
C'Include_files_directives
C
C    DISP.COM     Iteration time and frequency declaration.
C                 No FPC required.
C
C'
C
C'References
C
C      Ref. #1 :   HONEYWELL SPZ-8000 Digital Automatic Flight Control
C                  System,  DeHavilland DHC-8 (serie 100), Maintenance
C                  Manual, chapter  22-14-00, dated June 15/89
C
C      Ref. #2 :   HONEYWELL DASH8-100 control laws,  Drawing no. 5430-95221
C                  Revision B, 10/89
C
C'
C
C'Purpose
C
C    The purpose of this module is to simulate the roll axis computation
C   of the Sperry SPZ-6000 Flight Guidance Computer.  The logic provides
C   mode engage flags determining which outer loop guidance command will be
C   routed to the inner loop for roll guidance.  A specified reference is
C   to held (i.e. heading, bank angle and radio guidance) and
C   any deviation from this reference will generate an error term.  This
C   error term is generally rate limited and damped by the reference
C   deviation rate or an inertially derived rate.  The resultant command
C   is used by a servo to drive the aileron.  Since the ailerons are an
C   integrating surface (i.e. a constant deflection will cause the aircraft
C   to continuously roll), the inner loop will zero the servo command when
C   the desired roll angle is attained till the outer commands otherwise.
C
C'
C
C'Description
C
C              mode indices are as follows:
C
C               roll mode
C
C              0 - no mode
C              1 - vor cap
C              2 - vor trk
C              3 - vor os
C              4 - vor aos1
C              5 - vor aos2
C              6 - loc cap1
C              7 - loc cap2
C              8 - loc trk1
C              9 - loc trk2
C              10- bc cap1
C              11- bc cap2
C              12- bc trk1
C              13- bc trk2
C              14- ga
C              15- hdg sel lo
C              16- hdg sel hi
C              17- hdg hld
C              18- vor app cap
C              19- vor app trk
C              20- vor app os
C              21- vor app aos1
C              22- vor app aos2
C              23- lnav cap
C
C'
C
      SUBROUTINE USD8SR
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 02/28/92 - 00:39 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Include_files
C
      INCLUDE 'disp.com'  !NOFPC
C
C'
C
C'Common_Data_Base
C ----------------
C
CQ    USD8 XRFTEST*
C
C'Common_Data_Base_Variables
C --------------------------
C
CP    USD8
C
C
C  CDB inputs to the AFDS Roll Axis module
C  ---------------------------------------
C
CPI  &  BIAF01   , BIAF02   , YITAIL,
C
CPI  &  CAILL    , CIAFDPOS ,
CPI  &  CIACAFOR , CIAFAFOR ,
C
CPI  &  IDRHER1  , IDRHER12 , IDRHER2  , IDRHER22 ,
C
CPI  &  JEX008A  , JEX008B  , JEX013A  , JEX013B  ,
C
CPI  &  RBVRCC   ,
C
CPI  &  SLAAPENG , SLAPENG  , SLAPPTRK ,
CPI  &  SLBCARM  , SLBCCAP  , SLBCCAP1 , SLBCCAP2 ,
CPI  &  SLBCENG  , SLBCTRK  , SLBCTRK1 , SLBCTRK2 ,
CPI  &  SLBNKHLD ,
CPI  &  SLCPLSEL ,
CPI  &  SLD8300  , SLDFGCVL ,
CPI  &  SLFGCVL  ,
CPI  &  SLGSARM  , SLGSENG  ,
CPI  &  SLHDGHLD , SLHDGSLM ,
CPI  &  SLLNVCAP ,
CPI  &  SLOCARM  , SLOCCAP  , SLOCCAP1 , SLOCCAP2 ,
CPI  &  SLOCENG  , SLOCTRK  , SLOCTRK1 , SLOCTRK2 ,
CPI  &  SLONGRND ,
CPI  &  SLPFDENG , SLPWRUP  ,
CPI  &  SLREPSYN , SLRFDENG , SLRMODE  ,
CPI  &  SLSPDFLP , SLSPL    , SLSPR    , SLSRVENG ,
CPI  &  SLTCSENG ,
CPI  &  SLVAPARM , SLVAPCAP , SLVAPENG ,
CPI  &  SLVAPOSS , SLVAPTRK , SLVAAOS1 , SLVAAOS2 ,
CPI  &  SLVORARM , SLVORCAP , SLVORENG ,
CPI  &  SLVOROSS , SLVORTRK , SLVRAOS1 , SLVRAOS2 ,
C
CPI  &  SPESTFLP ,
C
CPI  &  SVALTGPM ,
CPI  &  SVDGS    , SVDGSV   , SVDME    , SVDMEVLD ,
CPI  &  SVDVL    , SVDVLV   , SVDYNGPM ,
CPI  &  SVHDG    , SVHDGV   ,
CPI  &  SVIAS    ,
CPI  &  SVLATACF ,
CPI  &  SVPHID   ,
CPI  &  SVRALT   , SVRNVST1 , SVROLL   ,
CPI  &  SVTASFPS , SVTASV   , SVTOFROM ,
CPI  &  SVVAHRSV , SVVRTACF , SVVRALTV ,
C
CPI  &  TCFFLPOS ,
C
CPI  &  VCSPHI   , VSNPHI   ,
C
C
C  CDB outputs from the AFCS Roll Axis module
C  ------------------------------------------
C
CPO  &  SI$ROLC1 , SI$ROLC2 ,
C
CPO  &  SRACCLC2 , SRACCLMC , SRAIL    , SRALTCMD , SRALTRTL ,
CPO  &  SRANGEC  , SRATAKER , SRATDEV  , SRATECMD , SRATEDEV ,
CPO  &  SRBKLMPC , SRBNKLIM ,
CPO  &  SRCEFADE , SRCELIM  , SRCESW   , SRCMDRTL , SRCRSER  ,
CPO  &  SRCRSERD , SRCRSERR , SRCRSINT , SRCRSSEL ,
CPO  &  SRDDDVL  , SRDDVL   , SRDEVDD  , SRDEVDIF , SRDEVERR ,
CPO  &  SRDEVFB  , SRDEVFDB , SRDEVINT , SRDEVRT  , SRDME    ,
CPO  &  SRDMEAVB , SRDMEDOT , SRDMEINT , SRDMELIM , SRDVL    ,
CPO  &  SREFIS   , SRESTDEV , SRESTDVL ,
CPO  &  SRFADE   , SRFADEIN , SRFDBINP , SRFDCMD  , SRFDERRC ,
CPO  &  SRFDINP  , SRFDINT  , SRFLAG   , SRFLAPM  , SRFREZ   ,
CPO  &  SRGSDEV  , SRGT3DSW ,
CPO  &  SRHDATUM , SRHDGCMD , SRHDGERR , SRHDGRTC , SRHDGSCD ,
CPO  &  SRHDGSER ,
CPO  &  SRINIT   ,
CPO  &  SRKCUTLL , SRKCUTUL , SRKDVSCH , SRKFADE  , SRKFADE1 ,
CPO  &  SRKFADE2 , SRKFADLM , SRKFD    , SRKFEEDB , SRKFLAPM ,
CPO  &  SRKGSDEV , SRKHDGD  , SRKHDGRT , SRKLATCD , SRKLOC   ,
CPO  &  SRKMOD1  , SRKMOD2  , SRKOMEGA , SRKPOS   , SRKSERVO ,
CPO  &  SRKTACHO , SRKVOROS ,
CPO  &  SRLAGDEV , SRLATCD  , SRLATCMD , SRLATDEV , SRLATDVD ,
CPO  &  SRLATGPM , SRLATTRK , SRLGDVRT , SRLMLOCD , SRLMVORC ,
CPO  &  SRLOCACC , SRLOCCD  , SRLOCCMD , SRLROLCD ,
CPO  &  SRMODRTL ,
CPO  &  SROLLCMD , SROLLPCD , SROLLREF , SROSDVRT , SROUTBUF ,
CPO  &  SROUTERC ,
CPO  &  SRPHIACC , SRPHIALG , SRPHICMD , SRPHID   , SRPHIDI  ,
CPO  &  SRPHIDWL , SRPHIDWO , SRPHIEST , SRPOSFBC , SRPOSIN  ,
CPO  &  SRPROPCD , SRPSID   , SRPSIERR , SRPSIFDB , SRPSILIM ,
CPO  &  SRPSINT  , SRPSITAS ,
CPO  &  SRSERVIN , SRSERVOC , SRSPL    ,
CPO  &  SRTACHO  , SRTASGP  , SRTLIMCD , SRTLMDVL , SRTOTPCD ,
CPO  &  SRTRMLWD , SRTRMRWD ,
CPO  &  SRVORCD  , SRVORCMD , SRVORDEV
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:56:43 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  CAILL          ! LEFT AILERON POSITION
     &, CIACAFOR       ! CAPT WHEEL ACTUAL FORCE                [LBS]
     &, CIAFAFOR       ! F/O WHEEL ACTUAL FORCE                 [LBS]
     &, CIAFDPOS       ! F/O WHEEL DEMANDED POSITION            [DEG]
     &, IDRHER1        ! CAPT HSI HDG ERROR(X)                 CI006
     &, IDRHER12       ! F/O  HSI HDG ERROR(X)                 CI020
     &, IDRHER2        ! CAPT HSI HDG ERROR(Y)                 CI007
     &, IDRHER22       ! F/O  HSI HDG ERROR(Y)                 CI021
     &, JEX008A        ! HEADING/TRK ERROR (DEG)       R1
     &, JEX008B        ! HEADING/TRK ERROR (DEG)       R1
     &, JEX013A        ! SELECTED COURSE SRN (DEG)     R1
     &, JEX013B        ! SELECTED COURSE SRN (DEG)     R1
     &, RBVRCC(3)      ! VOR COURSE SELECTED                   [DEG]
     &, SLSPR(10)      ! logic real spares
     &, SPESTFLP       ! estimated flap position                [deg]
     &, SVALTGPM       ! dadc pressure alt. (gain prog)          [ft]
     &, SVDGS          ! voted glide slope deviation           [dots]
     &, SVDME          ! voted dme                               [ft]
     &, SVDVL          ! voted localizer deviation             [dots]
     &, SVDYNGPM       ! voted dadc dyn press (gain prog)  [lb/ft*ft]
     &, SVHDG          ! voted ahrs magnetic heading            [deg]
     &, SVIAS          ! voted indicated airspeed              [knts]
     &, SVLATACF       ! filtered voted ahrs lat. accel.        [g's]
     &, SVPHID         ! voted ahrs roll rate               [deg/sec]
     &, SVRALT         ! radio altitude                          [ft]
     &, SVRNVST1       ! R-NAV lateral steering cmd from KNS660 [deg]
     &, SVROLL         ! voted ahrs roll angle                  [deg]
     &, SVTASFPS       ! voted true airspeed                 [ft/sec]
     &, SVVRTACF       ! filtered voted ahrs vert. accel.       [g's]
     &, VCSPHI         ! COSINE OF VPHI
     &, VSNPHI         ! SINE OF VPHI
C$
      INTEGER*4
     &  SLCPLSEL(2)    ! couple side select index
     &, SLRMODE(2)     ! roll mode index
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  BIAF01         ! HDG/CRS ERROR 1            *22 PIAL   DI1925
     &, BIAF02         ! HDG/CRS ERROR 2            *22 PIAR   DI1934
     &, SLAAPENG       ! Any autopilot engaged flag
     &, SLAPENG(2)     ! autopilot engaged
     &, SLAPPTRK(2)    ! approach mode track flag
     &, SLBCARM(2)     ! back course mode armed
     &, SLBCCAP(2)     ! back course capture mode flag (#1 or #2)
     &, SLBCCAP1(2)    ! phase 1 back course capture mode flag
     &, SLBCCAP2(2)    ! phase 2 back course capture mode flag
     &, SLBCENG(2)     ! back course engage flag
     &, SLBCTRK(2)     ! back course track mode flag (#1 or #2)
     &, SLBCTRK1(2)    ! phase 1 back course track mode flag
     &, SLBCTRK2(2)    ! phase 2 back course track mode flag
     &, SLBNKHLD(2)    ! bank hold mode
     &, SLD8300        ! DASH-8 -300 flag
     &, SLDFGCVL(2)    ! delayed fgc valid flag
     &, SLFGCVL(2)     ! flight guidance computer valid
     &, SLGSARM(2)     ! glide slope arm flag
     &, SLGSENG(2)     ! glide slope engaged (cap or trk)
     &, SLHDGHLD(2)    ! heading hold mode
     &, SLHDGSLM(2)    ! heading select mode
     &, SLLNVCAP(2)    ! LNAV capture flag
     &, SLOCARM(2)     ! localizer arm flag
     &, SLOCCAP(2)     ! localizer capture mode flag
     &, SLOCCAP1(2)    ! phase 1 localizer capture mode flag
     &, SLOCCAP2(2)    ! phase 2 localizer capture mode flag
     &, SLOCENG(2)     ! localizer engage flag
     &, SLOCTRK(2)     ! localizer track mode flag
     &, SLOCTRK1(2)    ! phase 1 localizer track mode flag
     &, SLOCTRK2(2)    ! phase 2 localizer track mode flag
     &, SLONGRND       ! A/C on ground flag
      LOGICAL*1
     &  SLPFDENG(2)    ! pitch f/d engage flag
     &, SLPWRUP        ! Power-up test in progress flag
     &, SLREPSYN(2)    ! reposition in progress flag
     &, SLRFDENG(2)    ! roll f/d engage flag
     &, SLSPDFLP       ! Low airspeed with flaps extended flag
     &, SLSPL(20)      ! logic program logical spares
     &, SLSRVENG(2)    ! servo engage flag (pitch & roll)
     &, SLTCSENG(2)    ! TCS engaged flag
     &, SLVAAOS1(2)    ! vor app over station sensor flag #1
     &, SLVAAOS2       ! VOR APP after over station #2 flag
     &, SLVAPARM(2)    ! vor app mode armed
     &, SLVAPCAP(2)    ! vor app capture flag
     &, SLVAPENG(2)    ! any vor app mode engaged flag
     &, SLVAPOSS(2)    ! vor app over station flag
     &, SLVAPTRK(2)    ! vor app track flag
     &, SLVORARM(2)    ! vor mode armed
     &, SLVORCAP(2)    ! vor capture flag
     &, SLVORENG(2)    ! any vor mode engaged flag
     &, SLVOROSS(2)    ! vor over station flag
     &, SLVORTRK(2)    ! vor track flag
     &, SLVRAOS1(2)    ! vor over station sensor flag #1
     &, SLVRAOS2(2)    ! vor over station sensor flag #2
     &, SVDGSV         ! voted vertical deviation valid
     &, SVDMEVLD       ! voted dme valid flag
     &, SVDVLV         ! voted lateral deviation valid
     &, SVHDGV         ! voted heading valid flag
     &, SVTASV         ! voted true airspeed valid flag
     &, SVTOFROM       ! vor to/from station flag
     &, SVVAHRSV       ! voted AHRS validity
     &, SVVRALTV       ! voted radio altitude valid flag
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  SI$ROLC1       ! F/D roll  command to ADI 1      [Deg] AO110
     &, SI$ROLC2       ! F/D roll  command to ADI 2      [Deg] AO174
     &, SRACCLC2       ! acceleration limited command #2    [deg/s/s]
     &, SRACCLMC       ! accel. limited roll command          [deg/s]
     &, SRAIL          ! commanded aileron position             [deg]
     &, SRALTCMD       ! alt compensated roll cmd (appt)        [deg]
     &, SRALTRTL       ! rate limited bank angle limit          [deg]
     &, SRATAKER       ! command rate taker                   [deg/s]
     &, SRATDEV        ! proportional term processing input
     &, SRATECMD       ! rate limited command                   [deg]
     &, SRATEDEV       ! processed rate dev damping term       [ft/s]
     &, SRBKLMPC       ! bank angle limited roll command        [deg]
     &, SRBNKLIM       ! outer loop command bank limit          [deg]
     &, SRCEFADE       ! course error fader final gain
     &, SRCELIM        ! limited course error fader
     &, SRCESW         ! gain programmed course error           [deg]
     &, SRCMDRTL       ! command rate limiter                 [deg/s]
     &, SRCRSER        ! nav course error term                  [deg]
     &, SRCRSERD       ! rate of change of course error         [deg]
     &, SRCRSERR       ! actual course error                    [deg]
     &, SRCRSINT       ! integrated course error rate           [deg]
     &, SRCRSSEL       ! selected course from the sg            [deg]
     &, SRDDDVL        ! deviation rate term input             [ft/s]
     &, SRDDVL         ! calculated deviation rate term        [ft/s]
     &, SRDEVDD        ! processed dev acceleration term     [ft/s/s]
     &, SRDEVDIF       ! dev term integrator input             [ft/s]
     &, SRDEVERR       ! dev error term for rate calc.           [ft]
     &, SRDEVFB        ! deviation feedback input                [ft]
     &, SRDEVFDB       ! deviation feedback for slg inv          [ft]
     &, SRDEVINT       ! integrated dev term (rate)            [ft/s]
     &, SRDEVRT        ! processed dev. rate term              [ft/s]
      REAL*4   
     &  SRDME          ! loc and b/c dist. from tx estim         [ft]
     &, SRDMEDOT       ! rate of change of dme                 [ft/s]
     &, SRDMEINT       ! dme approximation (integrated)          [ft]
     &, SRDMELIM       ! limited dme rate                      [ft/s]
     &, SRDVL          ! calculated deviation term               [ft]
     &, SRESTDEV       ! processed vor deviation command         [ft]
     &, SRESTDVL       ! estimated vor/loc deviation LBS       [dots]
     &, SRFADE         ! outer loop cmd fader(mode chng)        [deg]
     &, SRFADEIN       ! crs error fader for cap/trk xsn
     &, SRFDBINP       ! heading feedback term input
     &, SRFDCMD        ! roll f/d command to efis               [deg]
     &, SRFDERRC       ! flight director error command          [deg]
     &, SRFDINP        ! flight director integrator input       [deg]
     &, SRFDINT        ! flight director integrator             [deg]
     &, SRFLAPM        ! flap modifier term
     &, SRGSDEV        ! glide slope deviation                  [deg]
     &, SRHDATUM       ! heading hold datum                     [deg]
     &, SRHDGCMD       ! heading hold outer loop command        [deg]
     &, SRHDGERR       ! heading hold error term                [deg]
     &, SRHDGRTC       ! vor hdg rate command                 [deg/s]
     &, SRHDGSCD       ! heading select outer loop cmd          [deg]
     &, SRHDGSER       ! heading select error term              [deg]
     &, SRKCUTLL       ! course cut lower limit                 [deg]
     &, SRKCUTUL       ! course cut upper limit                 [deg]
     &, SRKDVSCH       ! v/l deviation schedule final value
     &, SRKFADE        ! fader time constant
     &, SRKFADE1       ! loc active #1 gain
     &, SRKFADE2       ! loc active #2 gain
     &, SRKFADLM       ! crs error fader limit
     &, SRKFD          ! flight director output gain
     &, SRKFEEDB       ! servo feedback gain
      REAL*4   
     &  SRKFLAPM       ! flap modifier gain term
     &, SRKGSDEV       ! dots to degree conversion         [dots/deg]
     &, SRKHDGD        ! heading rate dev gain
     &, SRKHDGRT       ! rate limited heading rate dev gain
     &, SRKLATCD       ! rate lim. mode dependant dev gain
     &, SRKLOC         ! mode dependant dev gain
     &, SRKMOD1        ! loc active #1 modifier gain
     &, SRKMOD2        ! loc active #2 modifier gain
     &, SRKOMEGA       ! natural frequency calculations
     &, SRKPOS         ! position gain for integral path        [d/d]
     &, SRKSERVO       ! servo loop gain
     &, SRKTACHO       ! tacho feedback gain
     &, SRKVOROS       ! vor over station fader gain
     &, SRLAGDEV       ! lagged lateral deviation                [ft]
     &, SRLATCD        ! gain prgm proportional term           [ft/s]
     &, SRLATCMD       ! tas prgm prop + rate command           [deg]
     &, SRLATDEV       ! lateral deviation                      [rad]
     &, SRLATDVD       ! vor dev rate - 0.5s w/o on dev      [dots/s]
     &, SRLATGPM       ! lateral gain program
     &, SRLGDVRT       ! lagged vor dev rate 0.2 s           [dots/s]
     &, SRLMLOCD       ! course cut limited loc. command        [deg]
     &, SRLMVORC       ! course cut limited vor command         [deg]
     &, SRLOCACC       ! loc beam acceleration term          [ft/s/s]
     &, SRLOCCD        ! proportional + rate command           [ft/s]
     &, SRLOCCMD       ! loc and b/c outer loop command         [deg]
     &, SRLROLCD       ! limited roll servo position cmd        [deg]
     &, SRMODRTL       ! mode dependant rate limit           [deg/it]
     &, SROLLCMD       ! roll command (integ + prop.)           [deg]
     &, SROLLPCD       ! roll proportionnal command             [deg]
     &, SROLLREF       ! roll ref. for roll hold mode           [deg]
     &, SROSDVRT       ! over station deviation rate           [ft/s]
      REAL*4   
     &  SROUTBUF       ! outer loop cmd buffer for fader        [deg]
     &, SROUTERC       ! outer loop command to rate limit       [deg]
     &, SRPHIACC       ! roll acc. compensation term            [deg]
     &, SRPHIALG       ! sum roll angle and roll acc lag        [deg]
     &, SRPHICMD       ! sum of rate lim o/l cmd and phi        [deg]
     &, SRPHID         ! estimated roll rate                  [deg/s]
     &, SRPHIDI        ! estimated roll rate input            [deg/s]
     &, SRPHIDWL       ! roll rate w/o + lag                  [deg/s]
     &, SRPHIDWO       ! roll rate w/o                        [deg/s]
     &, SRPHIEST       ! estimated roll angle from hdg          [deg]
     &, SRPOSFBC       ! position feedback cmd term             [deg]
     &, SRPOSIN        ! position integrator input            [deg/s]
     &, SRPROPCD       ! inner loop proportionnal cmd           [deg]
     &, SRPSID         ! pseudo heading rate term               [deg]
     &, SRPSIERR       ! heading error term                     [deg]
     &, SRPSIFDB       ! heading feedback term                  [deg]
     &, SRPSILIM       ! lim integ. roll rate term              [deg]
     &, SRPSINT        ! integrated roll rate term              [deg]
     &, SRPSITAS       ! tas programmed heading error         [deg-s]
     &, SRSERVIN       ! servo integrator input term          [deg/s]
     &, SRSERVOC       ! aileron servo command                  [deg]
     &, SRTACHO        ! aileron servo tacho feedback         [deg/s]
     &, SRTASGP        ! tas gain programming                   [sec]
     &, SRTLIMCD       ! rate limited roll cmd error          [deg/s]
     &, SRTLMDVL       ! rate limited deviation                  [ft]
     &, SRTOTPCD       ! total prop. inner loop cmd             [deg]
     &, SRVORCD        ! total vor dev rate command            [ft/s]
     &, SRVORCMD       ! vor outer loop command                 [deg]
     &, SRVORDEV       ! vor deviation rate command            [ft/s]
C$
      LOGICAL*1
     &  SRANGEC        ! calculated range flag
     &, SRDMEAVB       ! dme available flag
     &, SREFIS         ! EFIS instaled
     &, SRFLAG         ! SROLL         CONSTANT INIT FLAG
     &, SRFREZ         ! SROLL         FREEZE FLAG
     &, SRGT3DSW       ! greater than 3 deg bank or wow switch
     &, SRINIT         ! SROLL         TIME CONSTANT INIT FLAG
     &, SRLATTRK       ! any lateral track mode switch
     &, SRSPL(10)      ! roll logical spares
     &, SRTRMLWD       ! retrim roll left wing down flag
     &, SRTRMRWD       ! retrim roll right wing down flag
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(4960),DUM0000003(6660)
     &, DUM0000004(1626),DUM0000005(4368),DUM0000006(8060)
     &, DUM0000007(8),DUM0000008(8),DUM0000009(5648)
     &, DUM0000010(236),DUM0000011(8),DUM0000012(8)
     &, DUM0000013(121),DUM0000014(8),DUM0000015(166)
     &, DUM0000016(32),DUM0000017(12),DUM0000018(3)
     &, DUM0000019(6),DUM0000020(4),DUM0000021(9),DUM0000022(6)
     &, DUM0000023(36),DUM0000024(4),DUM0000025(2)
     &, DUM0000026(4),DUM0000027(10),DUM0000028(23)
     &, DUM0000029(2),DUM0000030(6),DUM0000031(2),DUM0000032(6)
     &, DUM0000033(4),DUM0000034(2),DUM0000035(8),DUM0000036(4)
     &, DUM0000037(6),DUM0000038(2),DUM0000039(4),DUM0000040(2)
     &, DUM0000041(825),DUM0000042(620),DUM0000043(1)
     &, DUM0000044(242),DUM0000045(16),DUM0000046(16)
     &, DUM0000047(28),DUM0000048(12),DUM0000049(24)
     &, DUM0000050(24),DUM0000051(72),DUM0000052(28)
     &, DUM0000053(28),DUM0000054(24),DUM0000055(71)
     &, DUM0000056(3),DUM0000057(4),DUM0000058(9),DUM0000059(29)
     &, DUM0000060(1),DUM0000061(6),DUM0000062(10181)
     &, DUM0000063(260897),DUM0000064(16426),DUM0000065(20)
     &, DUM0000066(208),DUM0000067(20)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,SI$ROLC1,SI$ROLC2,DUM0000003
     &, IDRHER1,IDRHER12,IDRHER2,IDRHER22,DUM0000004,BIAF01,BIAF02
     &, DUM0000005,VSNPHI,VCSPHI,DUM0000006,CIACAFOR,DUM0000007
     &, CIAFDPOS,DUM0000008,CIAFAFOR,DUM0000009,CAILL,DUM0000010
     &, SRFREZ,DUM0000011,SRFLAG,DUM0000012,SRINIT,DUM0000013
     &, SLCPLSEL,DUM0000014,SLRMODE,SLSPR,DUM0000015,SLSPL,SLAAPENG
     &, DUM0000016,SLAPENG,DUM0000017,SLAPPTRK,DUM0000018,SLBCARM
     &, SLBCCAP,SLBCCAP1,SLBCCAP2,SLBCENG,DUM0000019,SLBCTRK
     &, SLBCTRK1,SLBCTRK2,DUM0000020,SLBNKHLD,DUM0000021,SLD8300
     &, DUM0000022,SLDFGCVL,DUM0000023,SLFGCVL,DUM0000024,SLGSARM
     &, DUM0000025,SLGSENG,DUM0000026,SLHDGHLD,SLHDGSLM,DUM0000027
     &, SLLNVCAP,DUM0000028,SLOCARM,SLOCCAP,SLOCCAP1,SLOCCAP2
     &, SLOCENG,DUM0000029,SLOCTRK,SLOCTRK1,SLOCTRK2,DUM0000030
     &, SLONGRND,DUM0000031,SLPFDENG,DUM0000032,SLPWRUP,DUM0000033
     &, SLREPSYN,DUM0000034,SLRFDENG,DUM0000035,SLSPDFLP,SLSRVENG
     &, DUM0000036,SLTCSENG,SLVAAOS1,SLVAAOS2,SLVAPARM,SLVAPCAP
     &, SLVAPENG,DUM0000037,SLVAPOSS,SLVAPTRK,DUM0000038,SLVORARM
     &, SLVORCAP,SLVORENG,DUM0000039,SLVOROSS,SLVORTRK,DUM0000040
     &, SLVRAOS1,SLVRAOS2,DUM0000041,SPESTFLP,DUM0000042,SRACCLC2
     &, SRACCLMC,SRAIL,SRALTCMD,SRALTRTL,SRATAKER,SRATECMD,SRATEDEV
     &, SRATDEV,SRBKLMPC,SRBNKLIM,SRCEFADE,SRCELIM,SRCESW,SRCMDRTL
     &, SRCRSER,SRCRSERD,SRCRSERR,SRCRSINT,SRCRSSEL,SRDDDVL,SRDDVL
     &, SRDEVDD,SRDEVERR,SRDEVDIF,SRDEVFB,SRDEVFDB,SRDEVINT,SRDEVRT
     &, SRDME,SRDMEDOT,SRDMEINT,SRDMELIM,SRDVL,SRESTDEV,SRESTDVL
     &, SRFADE,SRFADEIN,SRFDBINP,SRFDCMD,SRFDERRC,SRFDINP,SRFDINT
     &, SRFLAPM,SRGSDEV,SRHDATUM,SRHDGCMD,SRHDGERR,SRHDGRTC,SRHDGSCD
     &, SRHDGSER,SRKCUTLL,SRKCUTUL,SRKDVSCH,SRKFADE,SRKFADE1
     &, SRKFADE2,SRKFADLM,SRKFD,SRKFEEDB,SRKFLAPM,SRKGSDEV,SRKHDGD
     &, SRKHDGRT,SRKLATCD,SRKLOC,SRKMOD1,SRKMOD2,SRKOMEGA,SRKPOS
     &, SRKSERVO,SRKTACHO,SRKVOROS,SRLAGDEV,SRLATCD,SRLATCMD
      COMMON   /XRFTEST   /
     &  SRLATDEV,SRLATDVD,SRLATGPM,SRLGDVRT,SRLMLOCD,SRLMVORC
     &, SRLOCACC,SRLOCCD,SRLOCCMD,SRLROLCD,SRMODRTL,SROLLPCD
     &, SROLLCMD,SROLLREF,SROSDVRT,SROUTBUF,SROUTERC,SRPHIACC
     &, SRPHIALG,SRPHICMD,SRPHID,SRPHIDI,SRPHIDWL,SRPHIDWO,SRPHIEST
     &, SRPOSFBC,SRPOSIN,SRPROPCD,SRPSID,SRPSIERR,SRPSIFDB,SRPSILIM
     &, SRPSINT,SRPSITAS,SRSERVIN,SRSERVOC,SRTACHO,SRTASGP,SRTLIMCD
     &, SRTLMDVL,SRTOTPCD,SRVORCD,SRVORCMD,SRVORDEV,SRSPL,SRANGEC
     &, SRDMEAVB,SREFIS,SRGT3DSW,DUM0000043,SRLATTRK,SRTRMRWD
     &, SRTRMLWD,DUM0000044,SVALTGPM,DUM0000045,SVDGS,DUM0000046
     &, SVDME,SVDVL,DUM0000047,SVDYNGPM,DUM0000048,SVHDG,DUM0000049
     &, SVIAS,DUM0000050,SVLATACF,DUM0000051,SVPHID,DUM0000052
     &, SVRALT,SVRNVST1,SVROLL,DUM0000053,SVTASFPS,DUM0000054
     &, SVVRTACF,DUM0000055,SVDGSV,DUM0000056,SVDVLV,DUM0000057
     &, SVDMEVLD,DUM0000058,SVHDGV,DUM0000059,SVTASV,DUM0000060
     &, SVTOFROM,SVVAHRSV,DUM0000061,SVVRALTV,DUM0000062,RBVRCC
     &, DUM0000063,TCFFLPOS,DUM0000064,JEX008A,DUM0000065,JEX013A
     &, DUM0000066,JEX008B,DUM0000067,JEX013B   
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
C      real variables
C
      REAL*4   AILFORCE     ! lagged value of aileron force
      REAL*4   AILFTIM      ! servo load timer
      REAL*4   BANKLIM(0:23)! table of bank limits for inner loop
      REAL*4   COSCRSER     ! local cosine of course error term
      REAL*4   COSHDG       ! local cosine of heading error input
      REAL*4   COSOMEGA     ! Cosine constant
      REAL*4   COSPHI       ! local cosine of roll angle
      REAL*4   CRSERR       ! local course error term in rads
      REAL*4   CSER         ! local course error term
      REAL*4   HDGOFF       ! Heading error offset
      REAL*4   HDGSER       ! local heading select error
      REAL*4   KAILCDD      ! limit on aileron commanded rate fdb.
      REAL*4   KAILLIM      ! aileron commanded position limit
      REAL*4   KB           ! rate taker gain
      REAL*4   KCRSFADE     ! crs error fader t/c
      REAL*4   KD           ! roll inner loop dyn pressure gain
      REAL*4   KDDOTLIM     ! proportional command accel limit
      REAL*4   KDOTLIM      ! proportional command rate limit
      REAL*4   KDMEVOR      ! vor natural freq. calc gain
      REAL*4   KDVLLIM      ! lat gain program lower limit
      REAL*4   KDVULIM      ! lat gain program upper limit
      REAL*4   KFADE1       ! loc active gain modifier #1
      REAL*4   KFADE2       ! loc active gain modifier #1
      REAL*4   KFADE3       ! loc active gain modifier #2
      REAL*4   KFADE4       ! loc active gain modifier #2
      REAL*4   KFTONM       ! ft to nautical miles conversion
      REAL*4   KHDGHLD      ! heading hold command gain
      REAL*4   KHDGSEL      ! heading select command gain
      REAL*4   KLNAV        ! lateral steering command gain        [deg/volt]
      REAL*4   KOMEGALL     ! natural frequency lower limit
      REAL*4   KOMEGAN      ! natural frequency for roll estimator
      REAL*4   KOMEGAUL     ! natural frequency upper limit
      REAL*4   KP           ! roll rate w/o gain
      REAL*4   KPUT1        ! FD bar position during power up test
      REAL*4   KPUT2        ! FD bar position during power up test
      REAL*4   KPUT3        ! FD bar position during power up test
      REAL*4   KSLOPE1(3)   ! slope of assoc. table lookup data
      REAL*4   KSLOPE2(3)   ! slope of assoc. table lookup data
      REAL*4   KSUB         ! rate taker dynamic gain
      REAL*4   KTHRESH      ! trim rwd/lwd threshold
      REAL*4   KVORDEV      ! dots to rad vor dev conversion
      REAL*4   KVOROSS      ! vor oss fader gain
      REAL*4   L_FADE1      ! loc active rate limit #1
      REAL*4   L_FADE2      ! loc active rate limit #2
      REAL*4   L_MODRTL     ! rate limit for mode rate limit
      REAL*4   L_RATE1      ! proportionnal command rate limit
      REAL*4   L_RATE2      ! loc command rate limit
      REAL*4   L_RATE3      ! loc command rate limit
      REAL*4   L_RATE4      ! loc gain schedule rate limit
      REAL*4   L_RATE5      ! heading rate gain rate limit
      REAL*4   L_RATE6      ! loc course cut limiter rate limit
      REAL*4   L_RATE7      ! vor os rate limited gain (+)
      REAL*4   L_RATE8      ! vor os rate limited gain (-)
      REAL*4   L_RATE9      ! vor course cut limiter rate limit
      REAL*4   L_RATE10     ! acceleration limit
      REAL*4   L_RATLIM     ! proportionnal command rate limit
      REAL*4   MODRTLIM     ! mode rate limit
      REAL*4   PHINP        ! roll angle input to lag
      REAL*4   PSIELAST     ! last value of srpsierr before wow etc..
      REAL*4   PSINPNT      ! integrator heading rate input (no track)
      REAL*4   PSINPT       ! integrator heading rate input (track)
      REAL*4   P5RTIME      ! half the local iteration time
      REAL*4   RATELIM(0:23)! table of rate limits for inner loop
      REAL*4   RTIME        ! local iteration time
      REAL*4   SINCRSER     ! local sine of course error term
      REAL*4   SINHDG       ! local sine of heading error input
      REAL*4   SINOMEGA     ! Sine constant
      REAL*4   SINPHI       ! local sine of roll angle
      REAL*4   TABLEX1(4)   ! flap modifier computation table
      REAL*4   TABLEX2(4)   ! lateral gain program computation table
      REAL*4   TABLEY1(4)   ! flap modifier computation table
      REAL*4   TABLEY2(4)   ! lateral gain programcomputation table
      REAL*4   TAUP         ! roll rate w/o TAU
      REAL*4   X            ! scratch pad variable
C
C        Time constants
C
      REAL*4   TAU(10)      ! array of time constants for sync.
      REAL*4   TFREEZE(10)  ! array of time constants for freeze
      REAL*4   TREPOS(10)   ! array of time constants for reposition
      REAL*4   TRUN(10)     ! array of time constants for prog. run
      REAL*4   TAU01        ! LOC or B/C lag time constant
      REAL*4   TAU02        ! roll rate input lag time constant
      REAL*4   TAU03        ! roll rate input lead time constant
      REAL*4   TAU04        ! roll acceleration lag t/c
      REAL*4   TAU05        ! lat dev w/o lag t/c
      REAL*4   TAU06        ! lat dev w/o lead t/c
      REAL*4   TAU07        ! lat dev rate lag t/c
      REAL*4   TAU08        ! roll rate lag time constant
      REAL*4   TAU09        ! roll rate lead time constant
C
C        old values
C
      REAL*4   O_ALGINP        ! old value of lag acc input
      REAL*4   O_CELIM         ! old value of course error limit gain
      REAL*4   O_CRSERD        ! old value of course error rate
      REAL*4   O_DEVDIF        ! old value of deviation error term
      REAL*4   O_DEVFB         ! old value of deviation feedback
      REAL*4   O_DEVRT         ! old value of lat rate dev term
      REAL*4   O_DMEDOT        ! old value of dme rate term
      REAL*4   O_DMELIM        ! old value of limited dme
      REAL*4   O_DRFOR         ! old value of aileron force
      REAL*4   O_DDDVL         ! old value of lat accel. dev term
      REAL*4   O_FDBINP        ! old value of heading rate feedback term
      REAL*4   O_FDINP         ! old value of fd integrator input
      REAL*4   O_HDG           ! old value of a/c heading
      REAL*4   O_HDGSER        ! old value of heading select error
      REAL*4   O_LATDEV        ! old value of lat dev term
      REAL*4   O_LATDVD        ! old value of calc. dev rate
      REAL*4   O_OUTBUF        ! old value of outer loop buffer
      REAL*4   O_PHIEST        ! old value of estimated roll command
      REAL*4   O_PHID          ! old value of SVPHID
      REAL*4   O_POSIN         ! old value of position integ. input
      REAL*4   O_PHIDI         ! old value of rol rate taker input
      REAL*4   O_PSITAS        ! old value of tas prog heading error
      REAL*4   O_RATDEV        ! old value of rate deviation term
      REAL*4   O_RTLDVL        ! old value of rate limited loc dev
      REAL*4   O_SERVIN        ! old value of servo integrator input
C
C      integer variables
C
      INTEGER*4   CPL          ! local cpl selection
C
      INTEGER*4   FGC          ! local FGC index
      INTEGER*4   I            ! scratch pad integer
      INTEGER*4   IX1          ! flap modifier computation table index
      INTEGER*4   IX2          ! lateral gain prog. comp. table index
      INTEGER*4   NTIMCNST     ! number of time constants
      INTEGER*4   O_RMODE      ! old value of roll mode engaged
C
C      logical variables
C
      LOGICAL*1  APENGSS      ! ap engage single shot
      LOGICAL*1  ARMCAP1      ! any loc/bc arm or capt1
      LOGICAL*1  CPL1         ! local cpl side 1 selected
      LOGICAL*1  DVLVSS       ! vor/loc valid single shot
      LOGICAL*1  LATCHNEG     ! latch counterclockwise turn
      LOGICAL*1  LATCHPOS     ! latch clockwise turn
      LOGICAL*1  LOCARMSS     ! localiser arm single shot flag
      LOGICAL*1  NOSYNC       ! no mode change sync flag
      LOGICAL*1  OLDFREZE     ! old state of freeze flag
      LOGICAL*1  OLDREPOS     ! old state of reposition flag
      LOGICAL*1  OLDRUN       ! old state of run flag
      LOGICAL*1  O_AAPENG     ! old value of ap eng
      LOGICAL*1  O_BCARM      ! old value of bc arm flag
      LOGICAL*1  O_DVLV       ! old value of svdvlv
      LOGICAL*1  O_LOCARM     ! old value of loc arm flag
      LOGICAL*1  O_GT3DSW     ! old value of srgt3dsw
      LOGICAL*1  O_RANGC      ! old value of srangec
      LOGICAL*1  O_VAPARM     ! old value of vor app arm flag
      LOGICAL*1  O_VORARM     ! old value of vor arm flag
      LOGICAL*1  O_VAPTRK     ! old value of vor app track
      LOGICAL*1  O_VORTRK     ! old value of vor track
      LOGICAL*1  RANGCSS      ! loc range calc change single shot
      LOGICAL*1  RMODESS      ! roll mode change single shot
      LOGICAL*1  VAPARMSS     ! vor app arm single shot flag
      LOGICAL*1  VORARMSS     ! vor arm single shot flag
C
C       equivalences
C
      EQUIVALENCE
     & (TAU01, TAU(1)),
     & (TAU02, TAU(2)),
     & (TAU03, TAU(3)),
     & (TAU04, TAU(4)),
     & (TAU05, TAU(5)),
     & (TAU06, TAU(6)),
     & (TAU07, TAU(7)),
     & (TAU08, TAU(8)),
     & (TAU09, TAU(9))
C
C
C        data tables
C        ===========
C
C        rate limit table
C
C                   -0-     -1-      -2-      -3-      -4-
      DATA RATELIM/ 5.5,    7.0,     4.0,     5.5,     7.0,
C
C                   -5-     -6-      -7-      -8-      -9-
     &              7.0,    7.0,     7.0,     5.5,     5.5,
C
C                   -10-    -11-     -12-     -13-     -14-
     &              7.0,    7.0,     5.5,     5.5,     5.5,
C
C                   -15-    -16-     -17-     -18-     -19-
     &              4.0,    4.0,     5.5,     7.0,     4.0,
C
C                   -20-    -21-     -22-     -23-
     &              5.5,    7.0,     7.0,     5.5/
C
C
C      flap modifier table
C
      DATA TABLEX1/   0.0,    7.5,    25.0,   35.0/
C
      DATA TABLEY1/ 0.070,   0.05,    0.05,  0.148/
C
C      lateral gain program
C
      DATA TABLEX2/ 0.0,  151900.0,  303800.0,  600000.0/
C
      DATA TABLEY2/ 0.182,  0.1188,    0.0828,     0.060/
C
C
C        bank limit table
C
C                   -0-     -1-      -2-      -3-      -4-
      DATA BANKLIM/ 24.0,   24.0,    24.0,    24.0,    24.0,
C
C                   -5-     -6-      -7-      -8-      -9-
     &              24.0,   30.0,    30.0,    24.0,    24.0,
C
C                   -10-    -11-     -12-     -13-     -14-
     &              30.0,   30.0,    24.0,    24.0,    24.0,
C
C                   -15-    -16-     -17-     -18-     -19-
     &              24.0,    24.0,   13.0,    24.0,    24.0,
C
C                   -20-    -21-     -22-     -23-
     &              24.0,   24.0,    24.0,    30.0/
C
C
C      Time constants reposition table
C
C         lag gain:  1
C         lead gain: 0
C
C                      -1- -2- -3- -4- -5- -6- -7- -8- -9- -10-
C
      DATA   TREPOS   / 1., 1., 0., 1., 1., 0., 0., 1., 0., 1./
C
C
C      Time constants freeze table
C
C         lag gain:  0
C         lead gain: X
C
C                      -1- -2- -3- -4- -5- -6- -7- -8- -9- -10-
C
      DATA   TFREEZE  / 0., 0., 0., 0., 0., 0., 0., 0., 0., 0./
C
C
      ENTRY SROLL
C
C
C ==============================================================================
C
C                       SECTION 0: INTIALIZATION
C
C ==============================================================================
C
C
C= SR0005
C
C -- module freeze flag                                  CAE          SRFREZ
C    ---------------------------------------------------!------------!----------
C
      IF (SRFREZ) RETURN
C
C.el
C
C
C= SR0010
C
C -- first pass initialization                           CAE          SRFLAG
C    ---------------------------------------------------!------------!----------
C
      IF (SRFLAG) THEN
        SRFLAG   = .false.
C
        KAILCDD  = 10.8
        KAILLIM  = 25.0
        KB       = 10.2
        KCRSFADE = 8.0
        KD       = 3.71
        KDDOTLIM = 4.0
        KDOTLIM  = 7.5
        KDMEVOR  = 6076.0 * 0.25
        KDVULIM  = 0.141
        KDVLLIM  = 0.050
        KFADE1   = 0.4
        KFADE2   = 0.666
        KFADE3   = 3.0
        KFADE4   = 2.67
        KFTONM   = 6076.0
        KHDGHLD  = 0.125
        KHDGSEL  = 0.136
        KLNAV    = 1.0       ! -1.0/0.397  REF 8 sh. 3
        KOMEGALL = 0.0025
        KOMEGAN  = 0.125
        KOMEGAUL = 0.02
        KPUT1    = 30.0
        KPUT2    = 10.0
        KPUT3    = 5.0
        KTHRESH  = 1.0               ! was 4.0
        KVORDEV  = 5.0 / 57.3        ! rad/dots
        NTIMCNST = 9
C
        SRKFADE  = 0.5
C !FM+
C !FM  28-Aug-92 21:43:44 M.WARD
C !FM    < TUNING CONSTANT >
C !FM
CMW        SRKFD    = 1.0
        SRKFD    = 0.5
C !FM-
        SRKFEEDB = 10.0
        SRKGSDEV = 2.77778
        SRKPOS   = 1.0
        SRKSERVO = 1.0            ! 4.3925
        SRKTACHO = 0.0
C
C       calculate slopes from data tables
C
        IX1 = 1
        IX2 = 1
        DO I = 1,3
          KSLOPE1(I) = (TABLEY1(I + 1) - TABLEY1(I))/(TABLEX1(I + 1) -
     &                TABLEX1(I))
        ENDDO
C
        DO I = 1,3
          KSLOPE2(I) = (TABLEY2(I + 1) - TABLEY2(I))/(TABLEX2(I + 1) -
     &                TABLEX2(I))
        ENDDO
C
        SVTASFPS = 150.0
        SVDYNGPM = 10.0
C
        SINOMEGA = 1.0 / (2.0 * SIN(2.094240838))    ! 120 deg phase
        COSOMEGA = -1.0 / (2.0 * COS(2.094240838))   ! 120 deg phase
        HDGOFF = 90.0
      ENDIF
C
C.el
C
C
C= SR0015
C
C -- time dependant variable initialization              CAE          SRINIT
C    ---------------------------------------------------!------------!----------
C
      IF (SRINIT) THEN
        SRINIT   = .false.
C
        RTIME    = YITIM
        P5RTIME  = 0.5 * RTIME
C
        L_FADE1  = 1000.0            ! RTIME / 9.0
        L_FADE2  = 1000.0            ! RTIME / 3.0
        L_MODRTL = 2.2 * RTIME
        L_RATE1  = 2.0 * RTIME
        L_RATE3  = 0.00188 * RTIME
        L_RATE4  = 0.06667 * RTIME
        L_RATE5  = 0.014 * RTIME
        L_RATE6  = 3.6 * RTIME
        L_RATE7  = 0.26667 * RTIME
        L_RATE8  = -RTIME
        L_RATE10 = 6.0 * RTIME
        L_RATLIM = 7.5 * RTIME
C
        TRUN(1) = RTIME / (0.2 + P5RTIME)      ! 2.0 in drawings
        TRUN(2) = RTIME / (1.0 + P5RTIME)
        TRUN(3) = 1.0 / RTIME
        TRUN(4) = RTIME / (0.5 + P5RTIME)
        TRUN(5) = RTIME / (0.2 + P5RTIME)
        TRUN(6) = P5RTIME
        TRUN(7) = RTIME
C !FM+
C !FM   8-Dec-92 15:30:42 M.WARD
C !FM    < FIX FOR SNAG 749 >
C !FM
CMW        IF (SLD8300) THEN
        IF ( YITAIL .EQ. 230 ) THEN
C !FM-
          TAUP = 0.1    ! -300
        ELSE
          TAUP = 0.025  ! -100
        ENDIF
        TRUN(8) = RTIME / (TAUP + P5RTIME)
        TRUN(9) = TAUP / RTIME
C
        RETURN
      ENDIF
C
C.el
C
C
C= SR0020
C
C -- Reposition Time Constants                           CAE          TAU**
C    ---------------------------------------------------!------------!----------
C
      IF (SLREPSYN(1) .and. .not. OLDREPOS) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TREPOS(I)
        ENDDO
        OLDREPOS = .true.
        OLDRUN   = .false.
        OLDFREZE = .false.
      ELSE IF (TCFFLPOS .and. .not.(OLDFREZE .or. SLREPSYN(1))) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TFREEZE(I)
        ENDDO
        OLDREPOS = .false.
        OLDRUN   = .false.
        OLDFREZE = .true.
      ELSE IF (.not.(OLDRUN .or. SLREPSYN(1) .or. TCFFLPOS)) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TRUN(I)
        ENDDO
        OLDREPOS = .false.
        OLDRUN   = .true.
        OLDFREZE = .false.
      ENDIF
C
      CPL  = SLCPLSEL(1)
      CPL1 = CPL .eq. 1
C
      IF (SLAPENG(1)) THEN
        FGC = 1
      ELSEIF (SLAPENG(2)) THEN
        FGC = 2
      ELSE
        FGC = 1
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 1: OUTER LOOP COMPUTATIONS
C
C ==============================================================================
C
C
C= SR1010
C
C -- TAS gain programming                                CAE          SRTASGP
C    ---------------------------------------------------!------------!----------
C
      IF (SVTASV) THEN
        SRTASGP = SVTASFPS / 32.2
      ELSE
        SRTASGP = 7.868       ! 150 knots
      ENDIF
C
      COSPHI = VCSPHI
      SINPHI = VSNPHI
C
      RMODESS = SLRMODE(1) .ne. O_RMODE
      APENGSS = SLAAPENG .and. .not. O_AAPENG
C
C.el
C
C
C= SR1020
C
C -- Course select term                                  CAE          SRCRSSEL
C    ---------------------------------------------------!------------!----------
C
      IF (SREFIS) THEN
        IF (CPL1) THEN
          SRCRSSEL = JEX013A
        ELSE
          SRCRSSEL = JEX013B
        ENDIF
      ELSE
        IF (CPL1) THEN
          IF (BIAF01) THEN
            SRCRSSEL = RBVRCC(1)
          ELSE
            SRCRSSEL = SVHDG
          ENDIF
        ELSE
          IF (BIAF02) THEN
            SRCRSSEL = RBVRCC(2)
          ELSE
            SRCRSSEL = SVHDG
          ENDIF
        ENDIF
      ENDIF
C
C.el
C
C
C= SR1030
C
C -- Course error term                                   ref 1 p 256  SRCRSERR
C    ---------------------------------------------------!------------!----------
C
      IF (SVHDGV) THEN
        CSER = SRCRSSEL - SVHDG
C
        IF (SLBCARM(1) .or. SLBCENG(1)) THEN
          IF(ABS(CSER).GT.90.0)THEN
            X=180.0-CSER
          ELSE
            X=CSER
          ENDIF
        ELSE
          X = CSER
        ENDIF
C
        IF (X .gt. 180.0) THEN
          X =  X - 360.0
        ELSEIF (X .lt. -180.0) THEN
          X =  X + 360.0
        ENDIF
        SRCRSERR = X
      ELSE
        SRCRSERR = 0.0
      ENDIF
C
      CRSERR = SRCRSERR / 57.3
C
C.el
C
C
C= SR1040
C
C -- Heading hold computations                           ref 1 p 254  SRHDGCMD
C    ---------------------------------------------------!------------!----------
C
      IF (SLHDGHLD(1) .and. .not. SLREPSYN(1)) THEN
        SRHDGERR = SRHDATUM - SVHDG
      ELSE
        SRHDATUM = SVHDG
        SRHDGERR = 0.0
      ENDIF
C
      SRHDGCMD = SRHDGERR * KHDGHLD * SRTASGP
C
C.el
C
C
C= SR1050
C
C -- Heading select computations                         ref 1 p 254  SRHDGSCD
C    ---------------------------------------------------!------------!----------
C
      IF (SLHDGSLM(1)) THEN
C
        O_HDGSER = HDGSER
        IF (SREFIS) THEN
          IF (CPL1) THEN
            HDGSER = JEX008A         !from efis
          ELSE
            HDGSER = JEX008B         !from efis
          ENDIF
        ELSE
          IF (CPL1) THEN
            SINHDG = (IDRHER1 - IDRHER2) * SINOMEGA
            COSHDG = (IDRHER1 + IDRHER2) * COSOMEGA
          ELSE
            SINHDG = (IDRHER12 - IDRHER22) * SINOMEGA
            COSHDG = (IDRHER12 + IDRHER22) * COSOMEGA
          ENDIF
C
          IF (COSHDG .ge. 0.0 .and. COSHDG .lt. 0.0001) THEN
            COSHDG = 0.0001
          ENDIF
          IF (COSHDG .le. 0.0 .and. COSHDG .ge. -0.0001) THEN
            COSHDG = -0.0001
          ENDIF
C
          IF ((.not. BIAF01 .and. CPL1) .or.
     &        (.not. (BIAF02 .or. CPL1))) THEN
            HDGSER = 0.0
          ELSE
            HDGSER = ATAN2(SINHDG,COSHDG) * 57.3 + HDGOFF
          ENDIF
C
          IF (HDGSER .gt. 180.0) THEN
            HDGSER = HDGSER - 360.0
          ENDIF
        ENDIF
C
        IF (SLREPSYN(1)) THEN
          O_HDGSER = HDGSER
        ENDIF
C
        IF (LATCHPOS) THEN
          LATCHPOS = HDGSER .lt. 0.0
        ELSE IF (LATCHNEG) THEN
          LATCHNEG = HDGSER .gt. 0.0
        ELSE IF (ABS(HDGSER - O_HDGSER) .gt. 180.0) THEN
          LATCHPOS = O_HDGSER .gt. 0.0
          LATCHNEG = O_HDGSER .lt. 0.0
        ENDIF
C
        IF (LATCHPOS) THEN
          SRHDGSER = 180.0
        ELSE IF (LATCHNEG) THEN
          SRHDGSER = -180.0
        ELSE
          SRHDGSER = HDGSER
        ENDIF
C
        SRHDGSCD = SRHDGSER * SRTASGP * KHDGSEL
C
      ELSE
C
        HDGSER   = 0.0
        LATCHPOS = .false.
        LATCHNEG = .false.
        SRHDGSCD = 0.0
C
      ENDIF
C
C.el
C
C
C= SR1060
C
C -- VOR mode computations                               ref 2 sh.5   SRVORCMD
C    ---------------------------------------------------!------------!----------
C
      IF (SLVORARM(1) .or. SLVORENG(1) .or. SLVAPARM(1) .or.
     &                                             SLVAPENG(1)) THEN
        COSCRSER = COS(CRSERR)
        SINCRSER = SIN(CRSERR)
C
C     dme distance calculation
C
        VORARMSS = SLVORARM(1) .and. .not. O_VORARM
        VAPARMSS = SLVAPARM(1) .and. .not. O_VAPARM
        SRDMEAVB = SVDMEVLD .or. SRDMEAVB
C
        O_DMEDOT = SRDMEDOT
        IF (SRDMEAVB) THEN
          IF (SVDMEVLD) THEN
            SRDME = SVDME - SVALTGPM
            SRDMEDOT = 0.125 * (SRDME - SRDMEINT)
          ELSE
            IF (SVTOFROM) THEN
              SRDMEDOT = -SVTASFPS * COSCRSER
            ELSE
              SRDMEDOT = SVTASFPS * COSCRSER
            ENDIF
          ENDIF
        ELSE
          IF (SVTOFROM) THEN
            IF (SLVAPARM(1) .or. SLVAPCAP(1)) THEN
              SRDME = 30000.0
            ELSEIF (SLVAPTRK(1)) THEN
              SRDME = 6000.0
            ELSEIF (SLVAPOSS(1)) THEN
              SRDME = 3000.0
            ELSEIF (SLVORARM(1) .or. SLVORCAP(1)) THEN
              SRDME = 120000.0
            ELSEIF (SLVORTRK(1)) THEN
              SRDME = 30000.0
            ELSE
              SRDME = 15000.0
            ENDIF
          ELSE
            IF (SLVAAOS1(1)) THEN
              SRDME = 6000.0
            ELSEIF (SLVAAOS2) THEN
              SRDME = 18000.0
            ELSEIF (SLVAPARM(1) .or. SLVAPCAP(1)) THEN
              SRDME = 24000.0
            ELSEIF (SLVAPTRK(1)) THEN
              SRDME = 50000.0
            ELSEIF (SLVRAOS1(1)) THEN
              SRDME = 30000.0
            ELSEIF (SLVRAOS2(1)) THEN
              SRDME = 90000.0
            ELSEIF (SLVORARM(1) .or. SLVORCAP(1)) THEN
              SRDME = 120000.0
            ELSE
              SRDME = 250000.0
            ENDIF
          ENDIF
          SRDMEDOT = AMIN1(AMAX1(0.5 * (SRDME - SRDMEINT),
     &               -SVTASFPS * 1.5),SVTASFPS * 0.67)
        ENDIF
C
        IF (VORARMSS .or. VAPARMSS) THEN
          SRDMEINT = SRDME
          O_DMEDOT = SRDMEDOT
        ELSE
          SRDMEINT = AMIN1(AMAX1(SRDMEINT + (SRDMEDOT + O_DMEDOT)
     &               * TAU06,1.0),600000.0)
        ENDIF
C
C       natural frequency calculation
C
        SRKOMEGA = AMIN1(AMAX1(KDMEVOR / SRDMEINT,KOMEGALL),KOMEGAUL)
C
C       lateral gain program
C
        X = SRDMEINT
        DO WHILE (X .gt. TABLEX2(IX2 + 1))
          IX2 = IX2 + 1
        ENDDO
C
        DO WHILE (X .lt. TABLEX2(IX2))
          IX2 = IX2 - 1
        ENDDO
C
        SRKDVSCH = TABLEY2(IX2) + KSLOPE2(IX2)*(X - TABLEX2(IX2))
C
        SRLATGPM = AMIN1(AMAX1(SRKDVSCH,KDVLLIM),KDVULIM)
C
C       process beam deviation
C
        O_LATDEV = SRLATDEV
        SRLATDEV = SVDVL * KVORDEV
C
        IF (SVDVLV) THEN
          SRDVL = SRLATDEV * SRDMEINT
        ELSE
          SRDVL = SRESTDEV
        ENDIF
C
C       calculate lateral deviation and rate for LBS logic
C
        O_LATDVD = SRLATDVD
        SRLATDVD = SRLATDVD + TAU04 * (TAU03 * (SRLATDEV - O_LATDEV)
     &             - SRLATDVD)
        SRLGDVRT = SRLGDVRT + TAU05 * (0.5 * (SRLATDVD + O_LATDVD)
     &             - SRLGDVRT)
C
        SRESTDVL = SRDVL / (SRDMEINT * KVORDEV)
C
C       course error pseudo rate and acceleration calculation
C
        O_CRSERD = SRCRSERD
        SRCRSERD = SRCRSERR - SRCRSINT
        IF (SLVOROSS(1) .or. SLVRAOS1(1) .or. SLVAPOSS(1) .or.
     &                                             SLVAAOS1(1)) THEN
          CONTINUE
        ELSEIF (SLVORTRK(1) .and. .not. O_VORTRK .or.
     &          SLVAPTRK(1) .and. .not. O_VAPTRK) THEN
          SRCRSINT = SRCRSERR
        ELSE
          SRCRSINT = SRCRSINT + (SRCRSERD + O_CRSERD) * TAU06 * 0.1
        ENDIF
        SROSDVRT = SRCRSERD * SVTASFPS / 57.3
C
        SRLOCACC = (-SVLATACF * COSPHI - SVVRTACF * SINPHI)
     &             * COSCRSER * 32.2
C
C       proportional term processing
C
        SRDEVERR = SRDVL - SRESTDEV
        SRATEDEV = SRKOMEGA * SRDEVERR
C
        O_RATDEV = SRATDEV
        SRATDEV = SRATEDEV * SRKOMEGA**2
        IF (SLVORARM(1) .or. SLVOROSS(1) .or. SLVRAOS1(1) .or.
     &      SLVAPARM(1) .or. SLVAPOSS(1) .or. SLVAAOS1(1)) THEN
          SRDEVDD = 0.0
        ELSE
          SRDEVDD = SRDEVDD + (SRATDEV + O_RATDEV) * TAU06
        ENDIF
C
C       rate term calculations
C
        O_DDDVL = SRDDDVL
        SRDDDVL = SRATEDEV * SRKOMEGA * 3.0 + SRLOCACC + SRDEVDD
        IF (SLVORARM(1) .or. SLVAPARM(1)) THEN
          SRDDVL = SVTASFPS * SINCRSER
        ELSEIF (SLVOROSS(1) .or. SLVRAOS1(1) .or. SLVAPOSS(1) .or.
     &                                               SLVAAOS1(1)) THEN
          SRDDVL = SROSDVRT
        ELSE
          SRDDVL = SRDDVL + (SRDDDVL + O_DDDVL) * TAU06
        ENDIF
C
        O_DEVRT = SRDEVRT
        SRDEVRT = SRDDVL + 3.0 * SRATEDEV
        IF (SVDVLV .and. (SLVORARM(1) .or. SLVOROSS(1) .or. SLVRAOS1(1)
     &      .or. SLVAPARM(1) .or. SLVAPOSS(1) .or. SLVAAOS1(1))) THEN
          SRESTDEV = SRDVL
        ELSE
          SRESTDEV = SRESTDEV + (SRDEVRT + O_DEVRT) * TAU06
        ENDIF
C
C       vor over station gain changer
C
        IF (SLVOROSS(1) .or. SLVAPOSS(1)) THEN
          KVOROSS = 0.0
        ELSE
          KVOROSS = 1.0
        ENDIF
C
        IF (VORARMSS .or. VAPARMSS) THEN
          SRKVOROS = 1.0
        ELSE
          SRKVOROS = SRKVOROS + AMIN1(AMAX1(KVOROSS - SRKVOROS,
     &               L_RATE8),L_RATE7)
        ENDIF
C
C       proportional and derivative terms
C
        SRVORDEV = SRESTDEV * SRLATGPM * SRKVOROS / 4.25
        SRVORCD  = SRVORDEV + SRDDVL
        SRLATCMD = SRVORCD * 57.3 / SVTASFPS
C
C       course cut limiter computations
C
        L_RATE9 = 10.0 / SRTASGP
        IF (SLVORARM(1) .or. SLVAPARM(1)) THEN
          IF (SRCRSERR .ge. 0.0) THEN
            SRKCUTLL = -SRCRSERR
            SRKCUTUL = 45.0
          ELSE
            SRKCUTLL = -45.0
            SRKCUTUL = -SRCRSERR
          ENDIF
        ELSE
          SRKCUTUL = SRKCUTUL + AMIN1(AMAX1(45.0 - SRKCUTUL,
     &               -L_RATE9),L_RATE9)
          SRKCUTLL = SRKCUTLL + AMIN1(AMAX1(-45.0 - SRKCUTLL,
     &               -L_RATE9),L_RATE9)
        ENDIF
C
C       final outer loop command
C
        SRLMVORC = AMIN1(AMAX1(SRLATCMD - SRCRSERR,SRKCUTLL),SRKCUTUL)
        SRHDGRTC = (SRLMVORC + SRCRSERR) * SRLATGPM
        SRVORCMD = SRHDGRTC * SRTASGP
C
      ELSE
        SRDMEAVB = .false.
      ENDIF
C
C.el
C
C
C= SR1070
C
C -- LOC and Back Course computations                    ref 2 sh.4   SRLOCCMD
C    ---------------------------------------------------!------------!----------
C
      LOCARMSS = SLOCARM(1) .and. .not. O_LOCARM .or. SLBCARM(1) .and.
     &           .not. O_BCARM
      IF (SLOCARM(1) .or. SLBCARM(1) .or. SLBCENG(1) .or. SLOCENG(1))
     &THEN
        COSCRSER = COS(CRSERR)
        SINCRSER = SIN(CRSERR)
C
C     calculate range
C
        O_RANGC = SRANGEC
        SRANGEC = (SLOCTRK2(1) .or. SLBCTRK2(1) .or. (SLGSENG(1) .or.
     &            SLGSARM(1)) .and. SVDGSV) .and. SVVRALTV
        RANGCSS = SRANGEC .and. .not. O_RANGC
        SRGSDEV = AMIN1(AMAX1(SVDGS / SRKGSDEV, -2.9), 2.9)
        O_DMELIM = SRDMELIM
        IF (SRANGEC) THEN
          SRDME = 9000.0 + 57.3 * SVRALT / (3.0 - SRGSDEV)
          SRDMELIM = AMIN1(AMAX1((SRDME - SRDMEINT) * 0.125,
     &               -1.3 * SVTASFPS),50.0)
        ELSE
          IF (SLOCARM(1)) THEN
            SRDME = 60000.0
          ELSEIF (SLOCENG(1)) THEN
            IF (SLOCCAP1(1)) THEN
              SRDME = 60000.0
            ELSEIF (SLOCCAP2(1)) THEN
              SRDME = 35000.0
            ELSEIF (SLOCTRK1(1)) THEN
              SRDME = 35000.0
            ELSE
              SRDME = 12000.0
            ENDIF
          ELSEIF (SLBCARM(1)) THEN
            SRDME = 40000.0
          ELSE
            IF (SLBCCAP1(1)) THEN
              SRDME = 40000.0
            ELSEIF (SLBCCAP2(1)) THEN
              SRDME = 20000.0
            ELSEIF (SLBCTRK1(1)) THEN
              SRDME = 20000.0
            ELSE
              SRDME = 8000.0
            ENDIF
          ENDIF
          SRDMELIM = AMIN1(AMAX1(SRDME - SRDMEINT,-SVTASFPS *
     &               COSCRSER),0.0)
        ENDIF
C
        IF (LOCARMSS .or. RANGCSS) THEN
          SRDMEINT = SRDME
        ELSE
          SRDMEINT = AMIN1(AMAX1((SRDMEINT + (SRDMELIM + O_DMELIM) *
     &               TAU06),0.0),100000.0)
        ENDIF
C
C       calculate lateral gain program
C
        IF (SLOCTRK(1) .or. SLBCTRK(1)) THEN
          IF (SRDMEINT .ge. 39960.0) THEN
            SRKDVSCH = 1.0
          ELSEIF (SRDMEINT .le. 27960.0) THEN
            SRKDVSCH = 0.0
          ELSE
            SRKDVSCH = -2.33 + SRDMEINT / 12000
          ENDIF
        ELSE
          SRKDVSCH = 1.0
        ENDIF
        SRLATGPM = SRLATGPM + AMIN1(AMAX1(SRKDVSCH - SRLATGPM,-L_RATE4)
     &             ,L_RATE4)
C
C       calculate beam deviation
C
        SRLATDEV = SVDVL * SRDMEINT / 57.3
C
        IF (SVDVLV) THEN
          SRDVL = SRLATDEV
        ELSE
          SRDVL = SRDEVFDB
        ENDIF
        DVLVSS = SVDVLV .and. .not. O_DVLV
C
        SRESTDVL = (SRDVL / SRDMEINT) * 57.3
C
C       process beam deviation
C
        IF (SLBCTRK(1) .or. SLOCTRK(1)) THEN
          L_RATE2 = 50.0
        ELSE
          L_RATE2 = 400.0
        ENDIF
C
        O_RTLDVL = SRTLMDVL
        IF (LOCARMSS .or. DVLVSS) THEN
          SRTLMDVL = SRDVL
          SRLAGDEV = SRDVL
        ELSE
          SRTLMDVL = SRTLMDVL + AMIN1(AMAX1(SRDVL - SRTLMDVL,
     &               - L_RATE2),L_RATE2)
          SRLAGDEV = SRLAGDEV + TAU01 * (0.5 * (SRTLMDVL + O_RTLDVL)
     &               - SRLAGDEV)
        ENDIF
C
        SRKLOC = 0.0518
C
        SRKLATCD = SRKLATCD + AMIN1(AMAX1(SRKLOC - SRKLATCD,-L_RATE3),
     &             L_RATE3)
C
        SRLATCD = SRKLATCD * SRLAGDEV
C
C       calculate beam rate
C
        SRDEVERR = SRTLMDVL - SRDEVFDB
        SRLOCACC = (-SVLATACF * COSPHI - SVVRTACF *
     &               SINPHI) * COSCRSER * 32.2
C
        O_DEVDIF = SRDEVDIF
        IF (SLBCARM(1) .or. SLBCENG(1)) THEN
          SRDEVDIF = SRDEVERR * 0.01 - SRLOCACC
        ELSE
          SRDEVDIF = SRDEVERR * 0.01 + SRLOCACC
        ENDIF
C
        ARMCAP1 = SLBCARM(1) .or. SLOCARM(1) .or. SLOCCAP1(1) .or.
     &            SLBCCAP1(1)
C
        IF (ARMCAP1) THEN
          SRDEVINT = SVTASFPS * SINCRSER
        ELSE
          SRDEVINT = SRDEVINT + (SRDEVDIF + O_DEVDIF) * TAU06
        ENDIF
C
        SRDEVRT = SRDEVERR * 0.2
C
        O_DEVFB = SRDEVFB
        SRDEVFB = SRDEVRT + SRDEVINT
C
        SRDDVL = SRLATGPM * SRDEVINT + (1.0 - SRLATGPM) * SRDEVFB
C
        IF (SVDVLV .and. ARMCAP1) THEN
          SRDEVFDB = SRTLMDVL
        ELSE
          SRDEVFDB = SRDEVFDB + (SRDEVFB + O_DEVFB) * TAU06
        ENDIF
C
C       proportional and derivative command
C
        SRLOCCD = SRDDVL + SRLATCD
        IF (SLBCENG(1)) THEN
          SRLATCMD = -SRLOCCD * 57.3 / SVTASFPS
        ELSE
          SRLATCMD = SRLOCCD * 57.3 / SVTASFPS
        ENDIF
C
C       course error
C
        IF (SLBCARM(1) .or. SLBCENG(1)) THEN
          SRCRSER = -SRCRSERR
        ELSE
          SRCRSER = SRCRSERR
        ENDIF
C
C       outer loop command mode gains
C
        IF (SLOCARM(1) .or. SLBCARM(1)) THEN
          SRKHDGD = 0.168
          SRKHDGRT = 0.168
        ELSEIF (SLOCTRK(1)) THEN
          SRKHDGD = 0.336
        ELSE
          SRKHDGD = 0.168
        ENDIF
C
        SRKHDGRT = SRKHDGRT + AMIN1(AMAX1(SRKHDGD - SRKHDGRT,
     &             - L_RATE5),L_RATE5)
C
C       course error fader gain
C
        IF (SLBCCAP(1) .or. SLOCCAP(1)) THEN
          SRFADEIN = 0.5
        ELSE
          SRFADEIN = 1.0
        ENDIF
C
        IF (SLBCTRK(1) .or. SLOCTRK(1)) THEN
          SRKFADLM = 0.0083
        ELSE
          SRKFADLM = 1.0
        ENDIF
C
        O_CELIM = SRCELIM
        SRCELIM = AMIN1(AMAX1((SRFADEIN - SRCEFADE) * KCRSFADE,
     &            -SRKFADLM),SRKFADLM)
        IF (LOCARMSS) THEN
          SRCEFADE = 1.0
        ELSE
          SRCEFADE = SRCEFADE + (SRCELIM + O_CELIM) * TAU06
        ENDIF
C
C       course cut limiter
C
        SRCESW = SRCRSER * SRCEFADE
C
        IF (SLOCARM(1) .or. SLBCARM(1)) THEN
          IF (SRCESW .ge. 0.0) THEN
            SRKCUTLL = -SRCESW
            SRKCUTUL = 45.0
          ELSE
            SRKCUTLL = -45.0
            SRKCUTUL = -SRCESW
          ENDIF
        ELSE
          SRKCUTLL = SRKCUTLL + AMIN1(AMAX1(-45.0 - SRKCUTLL,
     &               -L_RATE6),L_RATE6)
          SRKCUTUL = SRKCUTUL + AMIN1(AMAX1(45.0 - SRKCUTUL,
     &               -L_RATE6),L_RATE6)
        ENDIF
C
        SRLMLOCD = AMIN1(AMAX1(SRLATCMD - SRCESW, SRKCUTLL), SRKCUTUL)
C
C       outer loop command
C
        SRLOCCMD = (SRLMLOCD + SRCESW) * SRKHDGRT * SRTASGP
C
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 2: INNER LOOP COMPUTATIONS
C
C ==============================================================================
C
C
C= SR2010
C
C -- Buffered outer loop command                         ref 2 sh.3   SROUTBUF
C    ---------------------------------------------------!------------!----------
C
      O_OUTBUF = SROUTBUF
      IF (SLOCENG(1) .or. SLBCENG(1)) THEN
        SROUTBUF = SRLOCCMD
      ELSEIF (SLHDGSLM(1)) THEN
        SROUTBUF = SRHDGSCD
      ELSEIF (SLHDGHLD(1)) THEN
        SROUTBUF = SRHDGCMD
      ELSEIF (SLVORENG(1) .or. SLVAPENG(1)) THEN
        SROUTBUF = SRVORCMD
      ELSEIF (SLLNVCAP(1)) THEN
        SROUTBUF = AMIN1(AMAX1(SVRNVST1 * KLNAV,-32.0),32.0)
      ELSE
        SROUTBUF = 0.0
      ENDIF
C
C.el
C
C
C= SR2020
C
C -- Estimated roll angle from heading                   ref 2 sh.3   SRPHIEST
C    ---------------------------------------------------!------------!----------
C
      SRPHIACC = SVROLL
C
C     calculate error and rate term
C
      IF (ABS(SVHDG - O_HDG) .ge. 180.0) THEN
        SRPSIFDB = SVHDG - SRPSIERR
      ENDIF
      IF (.not. SLFGCVL(FGC)) THEN
        SRPSIFDB = SVHDG
      ENDIF
      SRPSIERR = SVHDG - SRPSIFDB
      SRPSITAS = SRPSIERR * SRTASGP
      SRPSID   = SRPSITAS * 2.0 * KOMEGAN
C
      SRLATTRK = SLVORTRK(1) .or. SLOCTRK2(1) .or. SLBCTRK2(1) .or.
     &           SLVAPTRK(1)
      O_GT3DSW = SRGT3DSW
      SRGT3DSW = SLONGRND .or. ABS(SVROLL).gt.3.0
C
      O_PHIEST = PSINPT
      PSINPT   = -SRPHIEST / 80.0
C
      O_PSITAS = PSINPNT
      IF (SRGT3DSW) THEN
        PSINPNT = 0.0
        IF (.not. O_GT3DSW) THEN
          PSIELAST = SRPSIERR
        ENDIF
      ELSE
        PSINPNT  = SRPSITAS * KOMEGAN * KOMEGAN
        PSIELAST = 0.0
      ENDIF
C
      SRSPL(1) = .not. (SVVAHRSV .or. SLAAPENG .or. SLPFDENG(1) .and.
     &                  SLRFDENG(1)) .or. SLLNVCAP(1)
C
      IF (.not. SLFGCVL(FGC) .or. SRSPL(1) .or. RMODESS) THEN
        SRPSINT = 0.0
      ELSEIF (SRLATTRK) THEN
        SRPSINT = AMIN1(AMAX1(SRPSINT + (PSINPT + O_PHIEST)
     &            * TAU06,-1.0),1.0)
      ELSE
        SRPSINT = SRPSINT + (PSINPNT + O_PSITAS) * TAU06
      ENDIF
C
      SRPSILIM = SRPSINT
      SRPHIEST = SRPHIACC + SRPSILIM
C
      O_FDBINP = SRFDBINP
      SRFDBINP = (SRPSID + SRPHIEST) / SRTASGP
      IF (APENGSS) THEN
        SRPSIFDB = SVHDG
      ELSEIF (SRGT3DSW .or. SRLATTRK) THEN
        SRPSIFDB = SVHDG - PSIELAST
      ELSE
        SRPSIFDB = SRPSIFDB + (SRFDBINP + O_FDBINP) * TAU06
      ENDIF
C
C.el
C
C
C= SR2030
C
C -- Bank limited proportional command                   ref 1 p 260  SRBKLMPC
C    ---------------------------------------------------!------------!----------
C
      IF (RMODESS .or. APENGSS) THEN
        MODRTLIM = RATELIM(SLRMODE(1))
        SRBNKLIM = BANKLIM(SLRMODE(1))
      ENDIF
C
      SRKFADE = 100.0 / SVTASFPS
C
      IF (SLRMODE(1) .ne. O_RMODE) THEN
        NOSYNC = SLRMODE(1) .ge.  2 .and. SLRMODE(1) .le.  5 .or.
     &           SLRMODE(1) .ge.  7 .and. SLRMODE(1) .le.  9 .or.
     &           SLRMODE(1) .ge. 11 .and. SLRMODE(1) .le. 13 .or.
     &           SLRMODE(1) .ge. 19 .and. SLRMODE(1) .le. 22
        IF (.not. NOSYNC) THEN
          SRFADE = SROUTBUF - O_OUTBUF
        ENDIF
      ELSE
        SRFADE = SRFADE - SRFADE * RTIME * SRKFADE
      ENDIF
      SROUTERC = SROUTBUF - SRFADE + SVROLL - SRPHIEST
C
      SRBKLMPC = AMIN1(AMAX1(SROUTERC,-SRBNKLIM),SRBNKLIM)
C
C.el
C
C
C= SR2040
C
C -- Roll proportionnal command                          ref 2 sh.3   SROLLPCD
C    ---------------------------------------------------!------------!----------
C
      IF (SLRMODE(1) .ne. 0) THEN
        SROLLPCD = SRBKLMPC
        SROLLREF = 0.0
      ELSEIF (SLBNKHLD(1) .and. SLTCSENG(1)) THEN
        SROLLREF = AMIN1(AMAX1(SVROLL,-45.0),45.0)
        SROLLPCD = SROLLREF
      ELSEIF (SLBNKHLD(1)) THEN
        SROLLPCD = SROLLREF
      ELSE
        SROLLPCD = 0.0
      ENDIF
C
C.el
C
C
C= SR2050
C
C -- Roll rate limited proportional command              ref 2 sh. 3  SRPROPCD
C    ---------------------------------------------------!------------!----------
C
      SRMODRTL = SRMODRTL + AMIN1(AMAX1(MODRTLIM - SRMODRTL,-L_MODRTL),
     &           L_MODRTL)
      SRPROPCD = SRPROPCD + AMIN1(AMAX1(SROLLPCD - SRPROPCD,
     &           -SRMODRTL*TAU07),SRMODRTL*TAU07)
C
C.el
C
C
C= SR2060
C
C -- Altitude compensated bank limited command (appt)    ref 2 sh.2   SRALTCMD
C    ---------------------------------------------------!------------!----------
C
      IF (SLAPPTRK(1)) THEN
        X = 0.1*(AMIN1(AMAX1(SVRALT,0.0),200.0)) + 15.0
      ELSE
        X = 45.0
      ENDIF
C
      SRALTRTL = SRALTRTL + AMIN1(AMAX1(X - SRALTRTL,-L_RATE1),L_RATE1)
      SRALTCMD = AMIN1(AMAX1(SRPROPCD,-SRALTRTL),SRALTRTL)
C
C.el
C
C
C= SR2070
C
C -- Rate limited command                                ref 2 sh  2  SRATECMD
C    ---------------------------------------------------!------------!----------
C
      SRATECMD = SRATECMD + AMIN1(AMAX1(SRALTCMD - SRATECMD,
     &           -L_RATLIM),L_RATLIM)
C
C.el
C
C
C= SR2080
C
C -- Rate limited proportional command                   ref 2 sh 2   SRPHICMD
C    ---------------------------------------------------!------------!----------
C
      SRPHICMD = SRATECMD - SVROLL
C
C.el
C
C
C= SR2090
C
C -- Localizer active modifier #1                        ref 2 sh. 2  SRKMOD1
C    ---------------------------------------------------!------------!----------
C
      IF (SLSPDFLP) THEN
        SRKFADE1 = KFADE2
      ELSE
        SRKFADE1 = KFADE1
      ENDIF
C
      SRKMOD1 = SRKFADE1
C
C.el
C
C
C= SR2100
C
C -- Rate limited total proportionnal command            ref 1 p 260  SRTLIMCD
C    ---------------------------------------------------!------------!----------
C
      X = SRPHICMD * SRKMOD1
      SRTLIMCD = AMIN1(AMAX1(X, -KDOTLIM), KDOTLIM)
C
C.el
C
C
C= SR2110
C
C -- Acceleration limited command                        ref 1 p 260  SRACCLMC
C    ---------------------------------------------------!------------!----------
C
      SRACCLMC = SRACCLMC + AMIN1(AMAX1(SRTLIMCD - SRACCLMC,-L_RATE10),
     &           L_RATE10)
C
C.el
C
C
C= SR2120
C
C -- Estimated roll rate                                 ref 2 sh.2   SRPHID
C    ---------------------------------------------------!------------!----------
C
      SRPHIDWO = TAU09 * (SVPHID - O_PHID)
      IF (SLD8300) THEN
        IF (SVIAS .lt. 150.0) THEN
          KP = 0.0
        ELSE
          KP = 1.0
        ENDIF
      ELSE
        KP = 2.0
      ENDIF
      SRPHIDWL = SVPHID + SRPHIDWO * KP
C
      O_PHIDI = SRPHIDI
      SRPHIDI = SVROLL + SRPHIDWL
      SRPHID  = SRPHID + TAU02 * (TAU03 * (SRPHIDI - O_PHIDI) - SRPHID)
C
C.el
C
C
C= SR2130
C
C -- Localizer active modifier                           ref 2 sh.2   SRKMOD2
C    ---------------------------------------------------!------------!----------
C
      IF (SLSPDFLP) THEN
        SRKFADE2 = KFADE4
      ELSE
        SRKFADE2 = KFADE3
      ENDIF
C
      SRKMOD2 = SRKFADE2
C
C.el
C
C
C= SR2140
C
C -- Acceleration limiter #2 command                     ref 2 sh. 2  SRACCLC2
C    ---------------------------------------------------!------------!----------
C
      X = (SRACCLMC - SRPHID) * SRKMOD2
      SRACCLC2 = AMIN1(AMAX1(X,-KDDOTLIM),KDDOTLIM)
C
C.el
C
C
C= SR2150
C
C -- Flap in motion modifier                             ref 2 sh.2   SRFLAPM
C    ---------------------------------------------------!------------!----------
C
      X = AMIN1(AMAX1(SPESTFLP,0.0),35.0)
C
      DO WHILE (X .gt. TABLEX1(IX1 + 1))
        IX1 = IX1 + 1
      ENDDO
C
      DO WHILE (X .lt. TABLEX1(IX1))
        IX1 = IX1 - 1
      ENDDO
C
      SRKFLAPM = TABLEY1(IX1) + KSLOPE1(IX1) * (X - TABLEX1(IX1))
      SRFLAPM  = 1.0 / (SRKFLAPM + 0.120)
C
C.el
C
C
C= SR2160
C
C -- Total proportionnal command                         ref 1 p 260  SRTOTPCD
C    ---------------------------------------------------!------------!----------
C
      SRTOTPCD = SRACCLC2 * SRFLAPM * KD / SVDYNGPM
C
C.el
C
C
C= SR2170
C
C -- Command rate taker                                  ref 1 p 260  SRATAKER
C    ---------------------------------------------------!------------!----------
C
      KSUB = KB * SVDYNGPM / SVTASFPS
C
      IF (ABS(SRLROLCD) .eq. 8.0 .or. SLONGRND) THEN
        SRATAKER = 0.0
      ELSE
        SRATAKER = SRTOTPCD * KSUB
      ENDIF
C
C.el
C
C
C= SR2180
C
C -- Command rate limiter                                ref 1 p 260  SRCMDRTL
C    ---------------------------------------------------!------------!----------
C
      SRCMDRTL = AMIN1(AMAX1(SRATAKER,-KAILCDD),KAILCDD)
C
C.el
C
C
C= SR2190
C
C -- Integral position feedback command path             ref 1 p 260  SRPOSFBC
C    ---------------------------------------------------!------------!----------
C
      O_POSIN = SRPOSIN
      SRPOSIN = (SRCMDRTL - SRTACHO) * SRKPOS
      IF (APENGSS) THEN
C !FM+
C !FM   4-Jul-92 11:32:17 steve w
C !FM    < changed tracking to dpos scaled to surface istead of surface >
C !FM
C        SRPOSFBC = - SRTOTPCD + CAILL / SRKFEEDB
C SBW        SRPOSFBC = - SRTOTPCD + (CIAFDPOS*0.35)/ SRKFEEDB
C ATTEMPTING TO REDUCE ENGAGE BUMP
        SRPOSFBC = (CIAFDPOS*0.35)
        SRACCLMC = 0
        SRPHID = 0
C !FM-
      ELSE
        SRPOSFBC = SRPOSFBC + (SRPOSIN + O_POSIN) * TAU06
      ENDIF
C
C.el
C
C
C= SR2200
C
C -- Aileron servo command                               ref 2 sh.2   SRAIL
C    ---------------------------------------------------!------------!----------
C
      SROLLCMD = SRTOTPCD + SRPOSFBC
      SRLROLCD = AMIN1(AMAX1(SROLLCMD, -KAILLIM), KAILLIM)
      SRSERVOC = SRLROLCD * SRKFEEDB
C
      O_SERVIN = SRSERVIN
      SRSERVIN = SRSERVOC - SRAIL * SRKFEEDB
C
      IF (SLSRVENG(FGC)) THEN
        SRAIL = AMIN1(AMAX1(SRAIL + (SRSERVIN + O_SERVIN)*TAU06,
     &          -25.0), 25.0)
      ELSE
C !FM+
C !FM   4-Jul-92 11:33:11 steve w
C !FM    < changed tracking to wheel position scaled to surface instead of
C !FM      surface >
C !FM
C        SRAIL = CAILL
        SRAIL = CIAFDPOS*.35
C !FM-
      ENDIF
C
C.el
C
C= SR2210
C
C -- Tacho feedback term                                 ref 1 p 260  SRTACHO
C    ---------------------------------------------------!------------!----------
C
      SRTACHO = SRSERVIN
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 3: FLIGHT DIRECTOR COMPUTATIONS
C
C ==============================================================================
C
C
C= SR3010
C
C -- Flight director command to ADIs                     ref 2 sh. 3  SRFDCMD
C    ---------------------------------------------------!------------!----------
C
      IF (SLRFDENG(FGC)) THEN
        SRFDERRC = SROLLPCD - SVROLL
        O_FDINP  = SRFDINP
        IF (SLOCENG(1)) THEN
          SRFDINP = (3.0 * SRFDERRC - SRFDINT) * 0.067
        ELSE
          SRFDINP = -SRFDINT * 0.067
        ENDIF
        IF (RMODESS) THEN
          SRFDINT = 0.0
        ELSE
          SRFDINT = AMAX1(AMIN1(SRFDINT + (SRFDINP + O_FDINP) * TAU06,
     &              2.0),-2.0)
        ENDIF
C !FM+
C !FM  15-Jul-92 11:45:17 M.WARD
C !FM    < REF SNAG 1357, F/D TOO SENSITIVE. WILL TRY THE OLDER EQUATION >
C !FM
CMW      SRFDCMD = SRACCLMC * SRKFADE2
         SRFDCMD = SRKFD * (SRFDERRC + SRFDINT)
C !FM-
      ELSE
        SRFDCMD = -80.0
        SRFDINT = 0.0
        SRFDINP = 0.0
      ENDIF
C
      IF (SLPWRUP) THEN
        IF (SLSPR(1) .le. 3.5) THEN
          SI$ROLC1 = -80.0
          SI$ROLC2 = -80.0
        ELSEIF (SLSPR(1) .le. 4.0) THEN
          SI$ROLC1 = KPUT1
          SI$ROLC2 = KPUT1
        ELSEIF (SLSPR(1) .le. 4.5) THEN
          SI$ROLC1 = KPUT2
          SI$ROLC2 = KPUT2
        ELSEIF (SLSPR(1) .le. 5.0) THEN
          SI$ROLC1 = KPUT3
          SI$ROLC2 = KPUT3
        ELSEIF (SLSPR(1) .le. 5.5) THEN
          SI$ROLC1 = 0.0
          SI$ROLC2 = 0.0
        ELSEIF (SLSPR(1) .le. 6.0) THEN
          SI$ROLC1 = -KPUT3
          SI$ROLC2 = -KPUT3
        ELSEIF (SLSPR(1) .le. 6.5) THEN
          SI$ROLC1 = -KPUT2
          SI$ROLC2 = -KPUT2
        ELSEIF (SLSPR(1) .le. 7.0) THEN
          SI$ROLC1 = -KPUT1
          SI$ROLC2 = -KPUT1
        ELSE
          SI$ROLC1 = -80.0
          SI$ROLC2 = -80.0
        ENDIF
      ELSE
C !FM+
C !FM  27-Aug-92 01:20:27 M.WARD
C !FM    < PUT BARS TO MIDDLE IF POWER OFF >
C !FM
        IF (SLSPL(4)) THEN
          SI$ROLC1 = -80.0
        ELSEIF (SLDFGCVL(1)) THEN
          SI$ROLC1 = SRFDCMD
        ELSE
          SI$ROLC1 = 0.0
        ENDIF
C
        IF (SLSPL(5)) THEN
          SI$ROLC2 = -80.0
        ELSEIF (SLDFGCVL(2)) THEN
          SI$ROLC2 = SRFDCMD
        ELSE
          SI$ROLC2 = 0.0
        ENDIF
C !FM-
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 4: MISCELLANEOUS COMPUTATIONS
C
C ==============================================================================
C
C
C= SR4010
C
C -- Old values                                          CAE          O_*
C    ---------------------------------------------------!------------!----------
C
      O_AAPENG = SLAAPENG
      O_BCARM  = SLBCARM(1)
      O_DVLV   = SVDVLV
      O_HDG    = SVHDG
      O_LOCARM = SLOCARM(1)
      O_PHID   = SVPHID
      O_RMODE  = SLRMODE(1)
      O_VAPARM = SLVAPARM(1)
      O_VAPTRK = SLVAPTRK(1)
      O_VORARM = SLVORARM(1)
      O_VORTRK = SLVORTRK(1)
C
C.el
C
C
C= SR4020
C
C -- Trim right wing down flag                           CAE          SRTRMRWD
C    ---------------------------------------------------!------------!----------
C
      IF (SLAAPENG .and. .not. TCFFLPOS) THEN
C !FM+
C !FM   4-Jul-92 11:34:49 steve w
C !FM    < changed tracking to wheel pos scaled to surface istead of
C !FM      surface >
C !FM
C        AILFORCE = CAILL - SRAIL
        AILFORCE = (CIAFDPOS*.35) - SRAIL
C !FM-
        IF (ABS(AILFORCE) .gt. KTHRESH) THEN
          AILFTIM = AMIN1(AILFTIM + RTIME, 25.0)
        ELSE
          AILFTIM = 0.0
        ENDIF
C
        IF (AILFTIM .gt. 20.0) THEN
          SRTRMRWD = AILFORCE .lt. -KTHRESH
          SRTRMLWD = AILFORCE .gt. KTHRESH
        ELSE
          SRTRMRWD = .false.
          SRTRMLWD = .false.
        ENDIF
      ELSE
        SRTRMRWD = .false.
        SRTRMLWD = .false.
      ENDIF
C
C.el
C
C
      RETURN
      END
C Comment for forport
