C'Title                 DASH-8 100,300 Flaps
C'Module_ID             USD8AW
C'Entry_Point           AWFLPS
C'Documentation         Flaps SDD
C'Application           Simulation of the DASH-8 Flaps System
C'Author                <PERSON>'Date                  January 1992
C
C'System                Ancillaries (Dept 21)
C'Itrn_rate             266 msec
C'Process               Synchronous process
C
C
C
C'Compilation_directives
C
C     Compile and add in SHIPDIR library.
C     It must be FPC'd, compiled and put again in SHIPDIR library after each
C     CDB update.
C
C
C'Include_files_directives
C
C     DISP.COM - iteration time and frequency declaration
C              - need not to be FPC'd
C
C     SHIPINFO.INC: - declarations for using YISHIP on IBM computers
C                   - need not be FPC'd
C
C'Revision_History
C
C  usd8aw.for.3 28Oct2011 02:24 usd8 Tom
C       < Working on flaps jam and SPU gripe >
C
C  usd8aw.for.2  9Aug1993 09:27 usd8 S.GOULD
C       < FIXED SUBROUTINE NAME >
C
C  usd8aw.for.1  8Aug1993 07:08 usd8 s.gould
C       < modified dash to usd8 in CP block >
C
C* DASHAW.FOR;001   10Feb1993 11:42 DASH ATH
C    added code to support AWFLG10 for T/O warning
C
C  usd8aw.for.3 27Jun1992 20:41 usd8 R.AUBRY
C       < Computed individual results of call routines for extension rates.
C           >
C
C  usd8aw.for.2 25Jun1992 09:31 usd8 R.AUBRY
C       < Corrected FLAPS PRIMARY SHAFT FAIL malfunction effects
C         (TF27F001) on the left inbd and outbd flaps. >
C
C  usd8aw.for.1 24Jun1992 10:20 usd8 R.AUBRY
C       < Implemented a new malfunction for flaps not moving as requested
C         by USAir. Malfunciton is TFSPR001 and should be changed for
C         TF27F041 at next CDB up-date. Also corrected logic for time
C         reference, ref.:AWTIME. >
C
C   #(053)  8-Apr-92 GP
C         Corrected Flaps movement on standby pump
C
C   #(052)  8-Apr-92 GP
C         Modifications of certains flaps timings (5 to 0 & 15 to 35)
C
C   #(125) 18-Mar-92 GP
C         For Backdriving to be possible, HATGON must be TRUE
C
C   #(119) 13-Mar-92 GP
C         Request to snag #397 (simulate Trigger after HATGON is removed)
C
C   #(072)  4-Mar-92 GP
C         Added Takeoff warning horn signal (AWFTOWH)
C
C   #(046) 24-Feb-92 RA
C         Added a limit on iaawvl to prevent abnormal situations
C
C   #(124) 13-Feb-92 GP
C         Add GAP for Flap Handle position (IAAWVL)
C
C   #(122) 13-Feb-92 GP
C         Add CNIA Logic for ATM Reposition (TCATMFL)
C
C   #(095) 11-Feb-92 GP
C         Add Logic for MICROSWITCH
C
C
C
C'Description
C
CT    The wing flaps system consists of a drive system, a control system
CT    and four seperate flaps sections, two on each wing. The inboard
CT    flap on each wing is secured between the fuselage structure and the
CT    engine nacelle inboard structure. The outboard flap on each wing is
CT    secured between the engine nacelle outboard structure and the aileron.
CT    Indication of flap position is provided on an indicator in the flight
CT    compartment.
CT
CT    Operation of the flap selection lever actuates a switch which directs
CT    electrical power to the flap drive unit.
CT
CT    The No.1 hydraulic system powers the primary flap drive shaft.
CT
CT    FLAPS CONTROL SYSTEM
CT
CT    The flaps control system consists of a flap selector lever, a hydraulic
CT    flap power unit (FPU), control rod, levers, a quadrant and cam assembly
CT    with associated microswitches and an interconnecting control cable
CT    installation.
CT
CT    FLAPS DRIVE SYSTEM
CT
CT    The flaps drive system consists of a mechanically-operated hydraulic
CT    flap power unit (FPU), a primary drive system, a secondary flexible
CT    drive system and four ball screw actuators in each wing. The ball
CT    screw actuators (two to each flap) drive the flaps up or down in
CT    tracks as selected. The actuators are numbered 1 through 4, inboard
CT    to outboard. 1 and 2 drive the inboard flaps, 3 and 4 drive the
CT    outboard flaps.
CT
CT    FLAPS POSITION INDICATING SYSTEM
CT
CT    The flaps position indicating system consists of an indicator and
CT    two ratio voltage differential transformers.
C
C
C
C'References
C
C     [ 1 ]    DASH-8 Operating Data Manual, Jul. 1990
C
C     [ 2 ]    DASH-8 Maintenance Manual Chapter 27-52-24, Aug 1988
C
C     [ 3 ]    DASH-8 Wiring Diagrams Manual Chapter 27, Oct. 1988
C
C     [ 4 ]    AEROC 8.6.CF.1 Sect 2.5
C
C     [ 5 ]    AEROC 8.6.J.1  Sect 2.6, Sept 1982
C
C     [ 6 ]    AEROC 8.6.HY.1, Sept 1990
C
C
C
C'
C
C
      SUBROUTINE USD8AW
C
      IMPLICIT NONE
C
C
C'Include_files
C
      INCLUDE 'disp.com'     !NOFPC
      INCLUDE 'ancmask.inc'  !NOFPC
C
CIBM+        ------- IBM Variable -------
          INCLUDE 'shipinfo.inc' !NOFPC
CIBM-         ------------------------
C
C
C'Ident
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8aw.for.3 28Oct2011 02:24 usd8 Tom    $'/
C
C
C
C'Subroutines_called
C
C     SCALE1( X, F, M, B, N )
C
C     Purpose: This subroutine generates slope-intercept tables
C              for one dimensionnal interpolation
C
C     Inputs:  X  : vector of x breakpoints
C              F  : table of function
C              Nx : number of X breakpoints
C
C     Outputs: Mx : table of slopes w/r to X
C              B  : table of intercepts
C
C
C     This subroutine should be called once to initialize the
C     tables value. Once this is done, the function may be
C     computed using the formula:
C
C        F(u) = B(I) + u*M(I)
C
C     where:  X(I) < u < X(I+1)
C
C
C
C'
C
C
C'Common_Data_Base_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *              C O M M O N    D A T A    B A S E                     *
C     *                                                                    *
C     **********************************************************************
C
C
CQ    USD8 XRFTEST*
C
C
C     **********************************************************************
C     *			           INPUTS                                  *
C     **********************************************************************
C
C
CPI   USD8
C
C     ----------------------------------------------------------------------
C     -                            OTHER SYSTEMS                           -
C     ----------------------------------------------------------------------
C
CPI   AHP1         , VVE          , HFLEV        , HQUICK       ,
CPI   HATGON       ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
CPI   YITAIL       ,
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
CPI   IAAWVL       ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C     SWITCHES
C     ========
C
CPI   IDAWARM      , IDAWHYD1     , IDAWHYD2     , IDAWGND      ,
CPI   IDAWLGH      ,
CPI   IDAWS1       , IDAWS2       , IDAWS3       , IDAWS4       ,
CPI   IDAWS5       , IDAWS6       ,
C
C     ----------------------------------------------------------------------
C     -                            CIRCUIT BREAKER                         -
C     ----------------------------------------------------------------------
C
CPI   BILL07       , BILM07       , BIRM07       , BILM06       ,
C
C     ----------------------------------------------------------------------
C     -                         INSTRUCTOR FACILITY                        -
C     ----------------------------------------------------------------------
C
CPI   TF27F001     , TF27F011     , TF27F021     , TF27F022     ,
CPI   TF27F031     , TF27F041     ,
C
CPI   TCFFSLAT     , TCRMAINT     ,
C
C
C     **********************************************************************
C     *			           OUTPUTS                                 *
C     **********************************************************************
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
C
CPO   AW$GAUG      , AW$GAUG2     ,
C
CPO   AWAFIL       , AWAFIR       , AWAFOL       , AWAFOR       ,
CPO   AWHFRQ       , AWAFL        , AWAFR        , AWWD         ,
CPO   AWTIME       , AWFLG10      ,
C
C
C     VALVES
C     ======
C
CPO   AWVISOL      , AWVSO        , AWVCT        ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
CPO   AW$FA        , AW$PWR       , AW$FLVR      ,
C
CPO   AWFSWH       , AWFSWH2      , AWFSWL       , AWFSWL2      ,
CPO   AWFDATA      , AWFTOWH      , AWFSYNC      , AWFTOR       ,
C
CPO   BPLL07       ,
C
C
C     RELAYS
C     ======
C
CPO   AWRK1        , AWRK2        , AWRK3        ,
C
C     ----------------------------------------------------------------------
C     -                       INSTRUCTOR FACILITY                          -
C     ----------------------------------------------------------------------
C
CPO   TCATMFL      ,
C
CPO   T027F001     , T027F011     , T027F021     , T027F022     ,
CPO   T027F031
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:34:44 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd8.xeq.79
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  AHP1           ! Hyd pressure node 1                    [psi]
     &, HFLEV          ! FLAP LEVER REQ -2=OFF                  [deg]
     &, IAAWVL         ! Flap handle position           27-002 AI068
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  BILL07         ! FLAPS CONT                  27 PDLES  DI2070
     &, BILM06         ! FLAP POSN IND L            *27 PDLES  DI2060
     &, BILM07         ! FLAPS PWR IND               27 PDLES  DI2071
     &, BIRM07         ! FLAP POSN IND R             27 PDRES  DI2231
     &, HATGON         ! ATG RUNNING FLAG
     &, HQUICK         ! MOVE GEAR,FLAPS INSTANTLY
     &, IDAWARM        ! Flap select arming flag        12-022 DI0038
     &, IDAWGND        ! Flap quad sw  gnd proximity    12-022 DIDUMY
     &, IDAWHYD1       ! Flap quad sw  stby hyd pump 1  12-022 DIDUMY
     &, IDAWHYD2       ! Flap quad sw  stby hyd pump 2  12-022 DIDUMY
     &, IDAWLGH        ! Flap quad sw  L/G horn         12-022 DIDUMY
     &, IDAWS1         ! Flap control sw 1              12-023 DIDUMY
     &, IDAWS2         ! Flap control sw 2              12-023 DIDUMY
     &, IDAWS3         ! Flap control sw 3              12-023 DIDUMY
     &, IDAWS4         ! Flap control sw 4              12-023 DIDUMY
     &, IDAWS5         ! Flap control sw 5              12-023 DIDUMY
     &, IDAWS6         ! Flap control sw 6              12-023 DIDUMY
     &, TCFFSLAT       ! FREEZE/FLAPS AND SLATS
     &, TCRMAINT       ! MAINTENANCE
     &, TF27F001       ! FLAPS PRIMARY SHAFT FAIL
     &, TF27F011       ! FLAPS HANDLE JAM
     &, TF27F021       ! FPU PRESS SENSOR FAILS IN LOW PRESSU
     &, TF27F022       ! FPU PRESS SENSOR FAILS IN HIGH PRESS
     &, TF27F031       ! FLAPS HYD SOLENOID VALVE CB TRIP
     &, TF27F041       ! FLAPS STUCK
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  AW$GAUG        ! Flaps RVDT 1                   14-331 CO0001
     &, AW$GAUG2       ! Flaps RVDT 2                   14-331 CO0000
     &, AWAFIL         ! Flap position - Inb Left               [deg]
     &, AWAFIR         ! Flap position - Inb Right              [deg]
     &, AWAFL          ! Flap position sensor L wing            [deg]
     &, AWAFOL         ! Flap position - Outb Left              [deg]
     &, AWAFOR         ! Flap position - Outb Right             [deg]
     &, AWAFR          ! Flap position sensor R wing            [deg]
     &, AWHFRQ         ! Flaps module frequency                 [Hz ]
     &, AWTIME         ! Flap equivalent time for I/F maint     [sec]
     &, AWVCT          ! Flap control valve position   (1 = Open) [-]
     &, AWVISOL        ! Flap isolation valve position (1 = Open) [-]
     &, AWVSO          ! Flap shutoff valve position   (1 = Open) [-]
     &, AWWD           ! Flap motor hyd. demand             [Gal/min]
C$
      INTEGER*4
     &  TCATMFL        ! CNIA ATM FLAP LEVER POSITION
C$
      LOGICAL*1
     &  AWFLG10        ! T/O WARN FLAP LEVER > 10
     &, AW$FA          ! FLAP DRIVE lt                  40-029 DO0712
     &, AW$FLVR        ! Flap lever load unit           27-007 DO0384
     &, AW$PWR         ! FLAP POWER lt                  40-027 DO068E
     &, AWFDATA        ! Flight data recorder signal from pos ind
     &, AWFSWH         ! Stall warning 1 LO flaps signal
     &, AWFSWH2        ! Stall warning 2 LO flaps signal
     &, AWFSWL         ! Stall warning 1 HI flaps signal
     &, AWFSWL2        ! Stall warning 2 HI flaps signal
     &, AWFSYNC        ! Flaps synchronisation flag for indicators
     &, AWFTOR         ! Secondary flap drive torque sensor prox sw
     &, AWFTOWH        ! Takeoff warning horn signal from pos ind
     &, AWRK1          ! Flap release relay k1
     &, AWRK2          ! Flap power control indication relay k2
     &, AWRK3          ! Flap power control indication relay k3
     &, BPLL07         ! FLAPS CONT                  27 PDLES  DO2070
     &, T027F001       ! FLAPS PRIMARY SHAFT FAIL
     &, T027F011       ! FLAPS HANDLE JAM
     &, T027F021       ! FPU PRESS SENSOR FAILS IN LOW PRESSU
     &, T027F022       ! FPU PRESS SENSOR FAILS IN HIGH PRESS
     &, T027F031       ! FLAPS HYD SOLENOID VALVE CB TRIP
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(5300),DUM0000003(4082)
     &, DUM0000004(948),DUM0000005(1418),DUM0000006(1106)
     &, DUM0000007(420),DUM0000008(279),DUM0000009(14)
     &, DUM0000010(3678),DUM0000011(4237),DUM0000012(8)
     &, DUM0000013(49),DUM0000014(81960),DUM0000015(820)
     &, DUM0000016(1),DUM0000017(201648),DUM0000018(24)
     &, DUM0000019(10646),DUM0000020(2912),DUM0000021(549)
     &, DUM0000022(15514)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,AW$GAUG,AW$GAUG2,DUM0000003
     &, AW$FA,AW$PWR,AW$FLVR,DUM0000004,BPLL07,DUM0000005,IAAWVL
     &, DUM0000006,IDAWARM,IDAWHYD1,IDAWHYD2,IDAWGND,IDAWLGH
     &, IDAWS1,IDAWS2,IDAWS3,IDAWS4,IDAWS5,IDAWS6,DUM0000007
     &, BILM06,DUM0000008,BILL07,BILM07,DUM0000009,BIRM07,DUM0000010
     &, VVE,DUM0000011,HATGON,DUM0000012,HQUICK,DUM0000013,HFLEV
     &, DUM0000014,AHP1,DUM0000015,AWFSWH,AWFSWH2,AWFSWL,AWFSWL2
     &, AWFDATA,AWFTOWH,AWFSYNC,AWFTOR,AWRK1,AWRK2,AWRK3,DUM0000016
     &, AWHFRQ,AWVISOL,AWVSO,AWVCT,AWAFIL,AWAFIR,AWAFOL,AWAFOR
     &, AWAFL,AWAFR,AWWD,AWTIME,DUM0000017,TCFFSLAT,DUM0000018
     &, TCRMAINT,DUM0000019,TCATMFL,DUM0000020,TF27F001,TF27F011
     &, TF27F021,TF27F022,TF27F031,TF27F041,DUM0000021,T027F001
     &, T027F011,T027F021,T027F022,T027F031,DUM0000022,AWFLG10   
C------------------------------------------------------------------------------
C
C
C
C
C
C'Local_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C
C     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
C     =============================================
C
C     Variables Second Number Function
C     --------------------------------
C
C     0 : Miscellaneous
C     1 : Controller
C     2 : Logic & Indications
C     3 : Performance
C
C
C     REAL Variables Names Third Letter Function
C     ------------------------------------------
C
C     A : Admittance, Angle, Acceleration
C     B : CB, X intercept, Admittance ( Y delta Transformation )
C     C : Constants
C     D : Time delay
C     E : Energy, Source pressure
C     F : Force, "Forcing function", Volumetric flow rate
C     G : Gain, Admittance ( Summ of 3 admittances in Y )
C     H : Frequency, Altitude
C     I : Current
C     J : ........
C     K : ........
C     L : Admittance ( Summ of 2 admittances in parallel )
C     M : Admittance ( Summ of 2 admittances in series )
C     N : RPM, Node capacitor admittance
C     O : Negative flow
C     P : Pressure, Phase
C     Q : Quantity, Battery charge
C     R : Resistance
C     S : Scratchpad
C     T : Temperature, Torque
C     U : Rate, Velocity
C     V : Valve position, Voltage
C     W : Positive flow, Electrical load
C     X : Coefficient, Time factor ( Tau )
C     Y : Total heat transfer flow
C     Z : "Malfunctions"
C
C     LOGICAL Variables Names Third Letter Function
C     ---------------------------------------------
C
C     F : Logical flag
C     G : ........
C     M : Option, mod #, Service bulletin #
C     R : Relay status
C     S : Switch status
C     Z : Malfunction
C
C
C     INTEGER Variables Names Third Letter Function
C     ---------------------------------------------
C
C     J : All integer variables
C
C
C     LOGICAL/REAL Variables Names last Letter Function
C     -------------------------------------------------
C
C     I : Integer*4 for bite //
C     J : Logical*4 for bite //
C     L : Left, Latched
C     Q : Previous buffer
C     R : Right
C     T : Time dependant
C
C
C     ----------------------------------------------------------------------
C     -                           PARAMETER                                -
C     ----------------------------------------------------------------------
C
C
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
      INTEGER*4
C
     &  I,J,K,L                ! indexes
     &, N1 / 6 /               ! # of Interpolation breakpoints
     &, N2 / 6 /               ! # of Interpolation breakpoints
     &, N3 / 3 /               ! # of Interpolation breakpoints
C
     &, W3JFLAP                ! Interpolation index
     &, W3JPOS                 ! Index for flap position
     &, W3JSPDE                ! Interpolation index
     &, W0HFLEV                ! Permit to Backdrive trigger if HFLEV changes
     &, W0HFLEVQ               ! Previous value of HFLEV
C
C
C
C
C     ----------------------------------------------------------------------
C     -                             REAL                                   -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &  W0AFLAP   ! Flap position request                            [ deg ]
     &, W0ASHAFT  ! Shaft position request                               [%]
     &, W0S1      ! Scratch variable
     &, W0S2      ! Scratch variable
     &, W0TIME    ! Time delay before resetting timer                [ sec ]
C
     &, W2DTK1    ! Timer used for flap release relay K1             [ sec ]
     &, W2DTK4    ! Timer used for relay K4                          [ sec ]
     &, W2USO     ! Shutoff valve rate of change                     [  %  ]
C
     &, W3AFA     ! Flap position - average                          [ deg ]
     &, W3AFAQ    ! Previous average flaps position                  [ deg ]
     &, W3AFM     ! Average flaps movement                        [deg/iter]
     &, W3AFS     ! Flap position as read by torque shaft            [ deg ]
     &, W3APDU    ! PDU shaft position                                   [%]
     &, W3APDUQ   ! Previous PDU shaft position                          [%]
     &, W3ASHLI   ! Flap shaft position L inbd                      [%/iter]
     &, W3ASHLM   ! Flap shaft position L mid                       [%/iter]
     &, W3ASHLO   ! Flap shaft position L outbd                     [%/iter]
     &, W3ASHRI   ! Flap shaft position R inbd                      [%/iter]
     &, W3ASHRM   ! Flap shaft position R mid                       [%/iter]
     &, W3ASHRO   ! Flap shaft position R outbd                     [%/iter]
C
     &, W3DLEVQ   ! Time delay for the previous flap lever           [ sec ]
     &, W3DSEC    ! Time delay till the secondary operates fully     [ sec ]
     &, W3PAWWD   ! Flap Hyd. Demand                               [gal/min]
C
     &, W3UAIRS   ! Airspeed rate factor                             [coeff]
     &, W3UEX     ! PDU Extend rate of movement                     [ %/sec]
     &, W3UFT     ! Flaps rate of movement                        [deg/iter]
     &, W3UHP     ! Hydraulic pressure factor                        [coeff]
     &, W3UPDU    ! PDU rate                                        [%/iter]
     &, W3URE     ! PDU Retract rate of movement                    [ %/sec]
     &, W3USEC    ! Secondary shaft rate                            [%/iter]
     &, W3USHLI   ! Flap shaft rate L inbd                          [%/iter]
     &, W3USHLM   ! Flap shaft rate L mid                           [%/iter]
     &, W3USHLO   ! Flap shaft rate L outbd                         [%/iter]
     &, W3USHRI   ! Flap shaft rate R inbd                          [%/iter]
     &, W3USHRM   ! Flap shaft rate R mid                           [%/iter]
     &, W3USHRO   ! Flap shaft rate R outbd                         [%/iter]
     &, W3USSHL   ! Left secondary shaft rate                       [%/iter]
     &, W3USSHR   ! Right secondary shaft rate                      [%/iter]
     &, W3UV      ! Valve factor                                     [coeff]
     &, W3XSEC    ! Speed factor when the secondary operates         [coeff]
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
      LOGICAL*1
C
     &  W0FIRST    /.TRUE. /   ! First pass flag
     &, W0FCHAN1               ! 28 VDC Flap Pos Ind CH 1
     &, W0FCHAN2               ! 28 VDC Flap Pos Ind CH 2
     &, W0FEXT    /.FALSE./    ! Flaps in extension
     &, W0FRET    /.FALSE./    ! Flaps in retraction
     &, W0MSR300               ! SERIES 300 FLAG
C
     &, W2RK4     /.FALSE./    ! Relay k4
     &, W2SPS1    /.FALSE./    ! Pressure switch - 750 to 950 psi
     &, W2SPS2    /.FALSE./    ! Pressure switch - 1000 to 1500 psi
C
     &, W3FSEC    /.FALSE./    ! Indicate a failure of the primary shaft
     &, W3FSECF   /.FALSE./    ! First pass of the primary shaft failure
     &, W3FTORL   /.FALSE./    ! Left torque
     &, W3FTORR   /.FALSE./    ! Right torque
C
     &, W3PSW     /.FALSE./    ! Hysterisis for Standby pump
     &, W3TRIG    /.FALSE./    ! Trigger simulation when HATGON is removed
C
C
C     ----------------------------------------------------------------------
C     -                            CONSTANTS                               -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &  W2CDTK1  /70.0      /   ! Time delay used with W2DTK1        [ sec ]
     &, W2CDTK4  /0.1       /   ! Time delay on operate relay k4     [ sec ]
     &, W2CUVSO  /0.7       /   ! Shutoff valve rate                 [1/sec]
C
     &, W3CAWWD  /1.4       /   ! Factor added for hyd demand
     &, W3CAWWDM /4.0       /   ! Maximim value for hyd demand
     &, W3CDTP   /2.857143  /   ! Degree to % conversion factor
     &, W3CFTO   /2.0       /   ! Last degrees where rate decreases    [deg]
     &, W3CGAP   /0.95      /   ! Gap for Flap Handle                [coeff]
     &, W3CGAP1  /0.05      /   ! Gap for Flap position              [coeff]
     &, W3CHPL   / 700.0    /   ! Lower hydraulic press const        [ psi ]
     &, W3CHPU   /3000.0    /   ! Upper hydraulic press const        [ psi ]
     &, W3CPTD   /0.35      /   ! % to degree conversion factor
C
     &, W3CSEC   /5.0       /   ! Time for the secondary to transmit   [sec]
     &, W3CTO    /3.0       /   ! Time to reach the final position     [sec]
     &, W3CUPDUL /0.1       /   ! Minimum value of PDU Rate          [%/sec]
C
C
C
C     ----------------------------------------------------------------------
C     -                           Data Tables                              -
C     ----------------------------------------------------------------------
C
      REAL*4
C
C     *****   Press sw W2SPS1 low/high set points [psi]   *****
C
     &  W2CPSW1(2) /  750,   950 /
C
C     *****   Press sw W2SPS2 low/high set points [psi]   *****
     &, W2CPSW2(2) /  750,  1200 /
C
C     *****   Pressure of Hysterisis for standby pump on   *****
     &, W3CPSW(2)  /  750,  1200 /
C
C
C     *****   EXTEND RATE in flight [sec] SERIE 100   *****
     &, W3XEX100(3)   /  0.69,   0.48,   3.077  /
C
C     *****   RETRACT RATE in flight [sec] SERIE 100   *****
     &, W3XRE100(3)   /  0.56,   0.75,   3.57  /
C
C
C     *****   EXTEND RATE in flight [sec] SERIE 300   *****
C !FM+
C !FM  9-Dec10-93 00:39:49 BCA
C !FM    < Coorected extension rate for 15-35 to match flt test data time >
C !FM
CC   &, W3XEX300(4)   /  0.56,   0.49,   0.64,   2.22  /
     &, W3XEX300(4)   /  0.56,   0.49,   0.64,   3.07 /
C !FM-
C
C     *****   RETRACT RATE in flight [sec] SERIE 300   *****
C !FM+
C !FM  9-Dec10-93 00:39:49 BCA
C !FM    < Corrected retraction rate to match 5-0 retraction time at 140 kts >
C !FM
CC   &, W3XRE300(4)   /  0.56,   0.56,   1.14,   3.57  /
     &, W3XRE300(4)   /  0.52,   0.56,   1.14,   3.57  /
C !FM
C
C
C     F1: Time factor for EXTENSION (coeff) according to Air speed (knots)
C     --------------------------------------------------------------------
C
      REAL*4
C !FM+
C !FM  27-Jun-92 20:32:48 R.AUBRY
C !FM    < Added W3M1A,B,C since they were all refered to W3M1 ??? >
C !FM
     &  W3M1(6)
     &, W3M3(6)
     &, W3M1A(6)
     &, W3M1B(6)
     &, W3M1C(6)
C !FM-
C !FM+
C !FM  27-Jun-92 20:34:30 R.AUBRY
C !FM    < Same for W3B1 ??? >
C !FM
     &, W3B1(6)
     &, W3B3(6)
     &, W3B1A(6)
     &, W3B1B(6)
     &, W3B1C(6)
C !FM-
     &, W3XEXTF(6)
C
C     *****   Air speed [knots]   *****
     &, W3UVVE(6)   / 0.0, 100.0, 125.0, 130.0, 140.0, 999.0 /
C
C     *****   Time factor for extension [coeff] -SERIE 300-   *****
C !FM+
C !FM  9-Dec10-93 00:39:49 BCA
C !FM    < Corrected extension rate for 15-35 to match time at 140 kts >
C !FM
CC   &, W3XEXTF3(6)  / 1.085, 1.0,   1.0,   1.0,   0.91,  5.0 /
     &, W3XEXTF3(6)  / 1.085, 1.0,   1.0,   1.0,    1.0,  5.0 /
C !FM
C
C     *****   Time factor for extension [coeff] -SERIE 100-  *****
     &, W3XEXT1A(6)  / 0.928, 1.0,   1.0,   1.0,   1.0,   5.0 /
     &, W3XEXT1B(6)  / 1.233, 1.0,   1.0,   1.0,   1.0,   5.0 /
     &, W3XEXT1C(6)  / 0.728, 1.0,   1.0,   1.0,   1.0,   5.0 /
C
C
C     F2: Time factor for RETRACTION (coeff) according to Air speed (knots)
C     --------------------------------------------------------------------
C
      REAL*4
     &  W3M2(6)
     &, W3B2(6)
C
C     *****   Time factor for retraction [coeff]   *****
     &, W3XRETF(6)  / 0.84,  1.0,   1.0,   1.0,   1.22,  5.0 /
C
C
C     F3: Pressure Factor VS Hydraulic Pressure AHP1
C     ----------------------------------------------
C
     &, W3PAHP1(3)  / 700.0,  1000.0,   3000.0  /
     &, W3XUHP(3)   /   0.0,     0.5,      1.0  /
     &, W3MHP(3)
     &, W3BHP(3)
C
C
C     ----------------------------------------------------------------------
C     -                        LOCAL EQUIVALENCES                          -
C     ----------------------------------------------------------------------
C
C
C
C
C     ----------------------------------------------------------------------
C     -                       FUTURE CDB LABELS                            -
C     ----------------------------------------------------------------------
C
C
C
C
C
      ENTRY AWFLPS
C
C
C
      IF ( TCFFSLAT )  RETURN
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 0 :  INITIAL & SPECIAL FUNCTIONS                  #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.1 :  First pass                                 |
CD    ----------------------------------------------------------------------
C
CT    That section is executed once at the beginning when the module is
CT    running.  Ship selection, Grey concept malfunctions, tables and
CT    constants are set in this section.
C
C
      IF ( W0FIRST ) THEN
C
C
CD    W01010  SHIP SELECTION AND OPTIONS                          (W0MSR300)
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CTT+
CT      =================================================================
CT      |                                                               |
CT      |                SHIPS                  TAIL #                  |
CT      |                ------                 ------                  |
CT      |                USAIR 100A              226                    |
CT      |                USAIR 300A              230                    |
CT      |                                                               |
CT      =================================================================
CTT-
CT
CT    The  following  is made to take in account the changes which have been
CT    done  on  the  aircraft along the years. These features are references
CT    as  modifications (MO), standard options (SO) or change requests (CH).
C
C
        IF ( YITAIL .EQ. 000 )  THEN
C
          W0MSR300 = .FALSE.
C
        ELSEIF ( YITAIL .EQ. 226 )  THEN
C
          W0MSR300 = .FALSE.
C
        ELSEIF ( YITAIL .EQ. 230 )  THEN
C
          W0MSR300 = .TRUE.
C
        ENDIF
C
C
CD    W01020  GREY CONCEPT MLF                                    (--------)
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    Grey concepts are used on the instructor facility to inhibit the malf
CT    selection if the malfunction is not yet ready.
C
C
        T027F001 = .TRUE.
        T027F011 = .TRUE.
        T027F021 = .TRUE.
        T027F022 = .TRUE.
        T027F031 = .TRUE.
C
C
CD    W01030  MODULE FREQUENCY FOR INSTRUMENT MODULE (AX)         (AWHFRQ  )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    This flag is used in the AX module to compare the module frequencies
CT    to properly drive the analogue instruments.
C
C
        AWHFRQ = YIFRE
C
C
CD    W01040  MISCELLENAOUS                                       (--------)
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    This section allows the assignment of iteration time-dependent
CT    constants.
C
C
        W2DTK1   = 0.0
        W2DTK4   = 0.0
C
        W2USO    = YITIM * W2CUVSO
C
        W3DSEC   = 0.0
C
        W3UAIRS  = 1.0
C
        AWAFL    = 0.0
C
        W0AFLAP  = 0.0
C
        W0HFLEV  = 0.0
C
C
CD    W01050  CALL SUBROUTINE FOR SLOPE-INTERCEPT TABLES          (--------)
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    The subroutine SCALE should be called once to initialize the
CT    tables value. Once this is done, the function may be
CT    computed using the formula:
CT
CT       F(u) = B(I) + u*M(I)
CT
CT    where:  X(I) < u < X(I+1)
CT
C !FM+
C !FM  27-Jun-92 20:37:39 R.AUBRY
C !FM    < Replaced W3M1 by individual value in each call routine. >
C !FM
C
CRA     CALL SCALE1(W3UVVE,W3XEXTF3,W3M1,W3B1,N1)
CRA     CALL SCALE1(W3UVVE,W3XEXT1A,W3M1,W3B1,N1)
CRA     CALL SCALE1(W3UVVE,W3XEXT1B,W3M1,W3B1,N1)
CRA     CALL SCALE1(W3UVVE,W3XEXT1C,W3M1,W3B1,N1)
        CALL SCALE1(W3UVVE,W3XEXTF3,W3M3,W3B3,N1)
        CALL SCALE1(W3UVVE,W3XEXT1A,W3M1A,W3B1A,N1)
        CALL SCALE1(W3UVVE,W3XEXT1B,W3M1B,W3B1B,N1)
        CALL SCALE1(W3UVVE,W3XEXT1C,W3M1C,W3B1C,N1)
C !FM-
        CALL SCALE1(W3UVVE,W3XRETF,W3M2,W3B2,N2)
C
        CALL SCALE1(W3PAHP1,W3XUHP,W3MHP,W3BHP,N3)
C
C
        W0FIRST = .FALSE.
C
      ENDIF                                  ! First pass
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.2 :  General                                    |
CD    ----------------------------------------------------------------------
C
CT    All the general module characteristics, which are simulation features,
CT    are computed here.
CT
C
C
CD    W02010  FLAPS SYNCHRONISATION FLAG FOR INDICATORS           (AWFSYNC )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    The synchronious flag is needed to compute the new difference between
CT    the target value and the one indicated on the instrument.
CT    The synchronisation ensures that the indicator needle movement is
CT    continious.
C
CC    *****   SYNCHRONISATION FOR AX MODULE   ****
C
      AWFSYNC = .TRUE.
C
C
CD    W02020  FLAP POSITION INDICATOR CHANNEL                     (W0FCHANX)
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  sec. 27-50-21 fig. 3
CR                  [ 5 ]  page 3, par 2.6.3
C
CT    Power for channel 1 is supplied by the FLAP POSN 1 (BILM06) breaker
CT    on the left 28 VDC Essential Bus.  Power for channel 2 is supplied
CT    by the FLAP POSN 2 (BIRM07) breaker on the right 28 VDC Essential Bus.
C
C
      W0FCHAN1 = BILM06
      W0FCHAN2 = BIRM07
C
C
CD    W02030  CNIA                                                (TCATMFL )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    This section does the CNIA Logic for ATM Reposition.
CT
CT    CNIA labels are used to transmit the actual switch positions (in the
CT    cockpit)  to  the  instructor  station  according  to  a  particular
CT    reposition.  When  switches are in disagree with the reposition, the
CT    information  appears  on the screen (I/F) and physical action in the
CT    cockpit is necessary.
CT
CT    0 = T/O             [ 10 and 15 deg ]
CT    1 = UP              [  0 deg        ]
CT    2 = LAND            [ 35 deg        ]
CT    3 = APPROACH        [  5 deg        ]
C
      IF ( IAAWVL .GT. 24.5 ) THEN
        TCATMFL = 2
C
      ELSE IF ( IAAWVL .GT. 9.5 ) THEN
        TCATMFL = 0
C
      ELSE IF ( IAAWVL .GT. 4.5 ) THEN
        TCATMFL = 3
C
      ELSE
        TCATMFL = 1
      ENDIF
C
C
CD    W02040  FLIGHT BACKDRIVE                                    (HFLEV   )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    For any Backdriving to be possible, HATGON must be TRUE.
CT
CT    When the Flaps Required Position Flight Backdrive (HFLEV) equals -2,
CT    flight backdrive is ignored.
CT
CT    If HQUICK is true and HFLEV is active ( > -2), then flaps move
CT    instantly to the position demanded by HFLEV.
CT
CT    If flight backdrive (HFLEV) is active and HQUICK is not true, then
CT    the switches for the flap control switch unit are set according to
CT    HFLEV.  These switches are otherwise set by the flaps control lever.
C
      W0HFLEVQ  =  W0HFLEV
      W0HFLEV   =  HFLEV
C
      IF ( ( HFLEV .NE. -2 ) .AND. HATGON ) THEN
C
        IF ( HQUICK ) THEN
C
          IF ( HFLEV .EQ. -1 ) THEN
C
            AWAFIL  = 0.0
            AWAFIR  = 0.0
            AWAFOL  = 0.0
            AWAFOR  = 0.0
            W3AFA   = 0.0
            W3AFAQ  = 0.0
            W3APDU  = 0.0
            W3APDUQ = 0.0
            W0AFLAP = 0.0
C
            W3ASHLI = W3APDU     ! Inbd shaft
            W3ASHRI = W3APDU     ! Inbd shaft
            W3ASHLM = W3ASHLI    ! Mid shaft
            W3ASHRM = W3ASHRI    ! Mid shaft
            W3ASHLO = W3ASHLM    ! Outbd shaft
            W3ASHRO = W3ASHRM    ! Outbd shaft
C
          ELSE
C
            AWAFIL  = HFLEV
            AWAFIR  = HFLEV
            AWAFOL  = HFLEV
            AWAFOR  = HFLEV
            W3AFA   = HFLEV
            W3AFAQ  = HFLEV
            W3APDU  = HFLEV * W3CDTP
            W3APDUQ = W3APDU
            W0AFLAP = HFLEV
C
            W3ASHLI = W3APDU     ! Inbd shaft
            W3ASHRI = W3APDU     ! Inbd shaft
            W3ASHLM = W3ASHLI    ! Mid shaft
            W3ASHRM = W3ASHRI    ! Mid shaft
            W3ASHLO = W3ASHLM    ! Outbd shaft
            W3ASHRO = W3ASHRM    ! Outbd shaft
C
          ENDIF
C
C
        ELSE
C
          IF ( HFLEV .EQ. -1 ) THEN
            W0AFLAP = 0.0
          ELSE
            W0AFLAP = HFLEV
          ENDIF
C
        ENDIF
C
      ELSE
C
C
CD    W02050  FLAPS POSITION REQUEST  (deg)                       (W0AFLAP )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    The flaps position request (W0AFLAP) is set by the flaps control
CT    lever (IAAWVL). The label W3JPOS is set according to the flaps position
CT    to determine the range and though the proper rate of the flaps
CT    movement according to its position.
C
C
C !FM+
C !FM  24-Jun-92 10:19:12 R.AUBRY
C !FM    < Implemented a new malfunction for flaps not moving as requested
C !FM      by USAir. >
C !FM
C
        IF ( .NOT.TF27F041 )  THEN
C
          IF ( IAAWVL .GE. ( 0.0 - W3CGAP ) .AND.
     &         IAAWVL .LE. ( 0.0 + W3CGAP ) )  THEN
            W0AFLAP  = 0.0
C
          ELSEIF ( IAAWVL .GE. ( 5.0 - W3CGAP ) .AND.
     &             IAAWVL .LE. ( 5.0 + W3CGAP ) )  THEN
            W0AFLAP  = 5.0
C
          ELSEIF ( IAAWVL .GE. ( 10.0 - W3CGAP ) .AND.
     &             IAAWVL .LE. ( 10.0 + W3CGAP ) )  THEN
            W0AFLAP  = 10.0
C
          ELSEIF ( IAAWVL .GE. ( 15.0 - W3CGAP ) .AND.
     &             IAAWVL .LE. ( 15.0 + W3CGAP ) )  THEN
            W0AFLAP  = 15.0
C
          ELSEIF ( IAAWVL .GE. ( 35.0 - W3CGAP ) .AND.
     &             IAAWVL .LE. ( 35.0 + W3CGAP ) )  THEN
            W0AFLAP  = 35.0
C
          ELSEIF ( IAAWVL .GE. ( 0.0  - W3CGAP ) .AND.
     &             IAAWVL .LE. ( 35.0 + W3CGAP ) )  THEN
            W0AFLAP  = IAAWVL
C
          ENDIF
C
        ENDIF
C
C !FM-
C
      ENDIF
C
C
      IF ( W0MSR300 ) THEN
C
        IF ( AWAFL .GT. 15 ) THEN
          W3JPOS = 4
C
        ELSE IF ( AWAFL .GT. 10 ) THEN
          W3JPOS = 3
C
        ELSE IF ( AWAFL .GT. 5 ) THEN
          W3JPOS = 2
C
        ELSE
          W3JPOS = 1
        ENDIF
C
      ELSE
C
        IF ( AWAFL .GT. 15 ) THEN
          W3JPOS = 3
C
        ELSE IF ( AWAFL .GT. 5 ) THEN
          W3JPOS = 2
C
        ELSE
          W3JPOS = 1
        ENDIF
C
      ENDIF
C
C
CD    W02060  SHAFT POSITION REQUEST  (%)                         (W0ASHAFT)
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    Positive gates are provided at lever positions corresponding to
CT    flap angles of 0, 5, 10 (Series 300 only), 15 and 35 degrees.
CT    The shaft position corresponding to these values are 0, 14.29,
CT    (28,57), 42.86 and 100 % respectively.
C
C
      W0ASHAFT = W0AFLAP * W3CDTP
C
C
CD    W02070  DETERMINATION OF EXTENSION MOVEMENT                 (W0FEXT  )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    When the request position is greater than the present flaps position
CT    then flaps are in extension.
C
      IF ( W0ASHAFT .GT. W3APDU ) THEN
        W0FEXT = .TRUE.
        W0FRET = .FALSE.
C
C
CD    W02080  DETERMINATION OF RETRACTION MOVEMENT                (W0FRET  )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    When the request position is lower than the present flaps position
CT    then flaps are in retraction.
C
      ELSE IF (W0ASHAFT .LT. W3APDU ) THEN
        W0FRET = .TRUE.
        W0FEXT = .FALSE.
C
      ELSE
        W0FEXT  = .FALSE.
        W0FRET  = .FALSE.
        W3UPDU  = 0.0
        W3USEC  = 0.0
      ENDIF
C
C
CD    W02090  MICROSWITCHES                                       (IDAWXXXX)
C     ----------------------------------------------------------------------
CR            Ref : [ 2 ]  sec. 27-51-00, page 1
CR                  [ 4 ]  page 8, par 2.5.4.1.5 and page 8A
C
CT    Flap selector lever movement is transmitted by push rod to the inboard
CT    end of the cable quadrant torque tube. This torque tube is a
CT    convenient location for installation of the cam which operates
CT    switches controlling functions related to the flap angle as follows:
CT
CTT+
CT    ----------------------------------------------------------------------
CT                      FLAP LEVER   -   RIGGING TABLE
CT
CT    ======================================================================
CT      MICROSWITCH                      |     RIG POSITION
CT    ======================================================================
CT       STDBY HYD PUMP #1  (IDAWHYD1)   |  SERIE 100:
CT       STDBY HYD PUMP #2  (IDAWHYD2)   |    OPEN AT    < 3 DEGREES
CT                                       |    CLOSED AT  > 3 DEGREES
CT                                       |  SERIE 300:
CT                                       |    OPEN AT 0 DEGREE
CT                                       |    CLOSED AT ALL OTHER ANGLES
CT    ----------------------------------------------------------------------
CT       GROUND PROXIMITY   (IDAWGND)    |  SERIE 100:
CT       L/G WARN HORN      (IDAWLGH)    |    OPEN AT   < 10 DEG
CT                                       |    CLOSED AT > 10 DEG
CT                                       |  SERIE 300:
CT                                       |    OPEN   AT 10 AND < 10 DEG
CT                                       |    CLOSED AT 15 AND > 15 DEG
CT    ----------------------------------------------------------------------
CT       AUTOPILOT SW 1     (IDAWS1)     |  SERIE 100 AND 300:
CT       AUTOPILOT SW 2     (IDAWS2)     |    CLOSED AT 0 DEG
CT                                       |    OPEN AT ALL OTHER ANGLES
CT    ----------------------------------------------------------------------
CT       AUTOPILOT SW 3     (IDAWS3)     |  SERIE 100:
CT       AUTOPILOT SW 4     (IDAWS4)     |    CLOSED AT 15 DEG
CT                                       |    OPEN AT ALL OTHER ANGLES
CT                                       |  SERIE 300:
CT                                       |    CLOSED AT 10 AND 15 DEG
CT                                       |    OPEN AT ALL OTHER ANGLES
CT    ----------------------------------------------------------------------
CT       AUTOPILOT SW 5     (IDAWS5)     |  SERIE 100 AND 300:
CT       AUTOPILOT SW 6     (IDAWS6)     |    CLOSED AT 35 DEG
CT                                       |    OPEN AT ALL OTHER ANGLES
CT    ----------------------------------------------------------------------
CT       T/O WARNING        (AWFLG10)    |  SERIE 300:
CT       AUTOPILOT SW 6     (IDAWS6)     |    CLOSED AT <= 10 DEG
CT                                       |    OPEN AT > 10
CT    ----------------------------------------------------------------------
CTT-
C
CC    *****   SERIE 300   *****
C
      IF ( W0MSR300 ) THEN
C
C        IDAWHYD1 = ( W0AFLAP .NE. 0.0 )
C        IDAWHYD2 = ( W0AFLAP .NE. 0.0 )
C
C I changed this on 10.28.2011 to fix a gripe  Tom M
C
        IDAWHYD1 = ( IAAWVL .GT. 3.0 )
        IDAWHYD2 = ( IAAWVL .GT. 3.0 )
C
        IF ( W0AFLAP .LE. 10.0 ) THEN
          IDAWGND = .FALSE.
          IDAWLGH = .FALSE.
        ELSE IF ( W0AFLAP .GE. 15.0 ) THEN
          IDAWGND = .TRUE.
          IDAWLGH = .TRUE.
        ENDIF
C
        AWFLG10 = W0AFLAP .GT. 10.0
C
        IDAWS1 = ( W0AFLAP .EQ. 0.0 )
        IDAWS2 = ( W0AFLAP .EQ. 0.0 )
        IDAWS3 = ( W0AFLAP .EQ. 10.0 ) .AND. ( W0AFLAP .EQ. 15.0 )
        IDAWS4 = ( W0AFLAP .EQ. 10.0 ) .AND. ( W0AFLAP .EQ. 15.0 )
        IDAWS5 = ( W0AFLAP .EQ. 35.0 )
        IDAWS6 = ( W0AFLAP .EQ. 35.0 )
C
CC    *****   SERIE 100   *****
C
      ELSE
C
        IF ( W0AFLAP .GT. 3.0 ) THEN
          IDAWHYD1 = .TRUE.
          IDAWHYD2 = .TRUE.
        ELSE IF ( W0AFLAP .LT. 3.0 ) THEN
          IDAWHYD1 = .FALSE.
          IDAWHYD2 = .FALSE.
        ENDIF
C
CCOA S81-2-021
C
C        IF ( W0AFLAP .LT. 10.0 ) THEN
C          IDAWGND = .FALSE.
C          IDAWLGH = .FALSE.
C        ELSE IF ( W0AFLAP .GT. 10.0 ) THEN
C          IDAWGND = .TRUE.
C          IDAWLGH = .TRUE.
C        ENDIF
C
        IF ( W0AFLAP .GT. 5.0 ) THEN
          IDAWGND = .TRUE.
        ELSE
          IDAWGND = .FALSE.
        ENDIF
C
        IF ( W0AFLAP .GT. 18.0 ) THEN
          IDAWLGH = .TRUE.
        ELSE
          IDAWLGH = .FALSE.
        ENDIF
C
        IDAWS1 = ( W0AFLAP .EQ. 0.0 )
        IDAWS2 = ( W0AFLAP .EQ. 0.0 )
        IDAWS3 = ( W0AFLAP .EQ. 15.0 )
        IDAWS4 = ( W0AFLAP .EQ. 15.0 )
        IDAWS5 = ( W0AFLAP .EQ. 35.0 )
        IDAWS6 = ( W0AFLAP .EQ. 35.0 )
C
      ENDIF
C
C
CD    W02100  FLAPS REFERENCE TIME                                (AWTIME  )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
C
C !FM+
C !FM  24-Jun-92 10:13:53 R.AUBRY
C !FM    < Corrected time reference logic in order to have it resetted when
C !FM      flaps are stopped. >
C !FM
      IF ( .NOT.TCRMAINT .AND. HFLEV .EQ. -2 )  THEN
CRA     IF ( W0FEXT )  AWTIME = AWTIME + YITIM
CRA     IF ( W0FRET )  AWTIME = AWTIME + YITIM
        IF ( ABS( W3AFM ) .GT. 0.01 )  THEN
          AWTIME = AWTIME + YITIM
          W0TIME = 1.5
        ELSEIF ( W0TIME .GT. 0.0 )  THEN
          W0TIME = W0TIME - YITIM
        ELSE
          AWTIME = 0.0
        ENDIF
C !FM-
      ELSE
        AWTIME = 0.0
      ENDIF
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 1 :  CONTROL                                      #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |                             N/A                                    |
CD    ----------------------------------------------------------------------
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 2 :  LOGIC & INDICATIONS                          #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.1 :  Relays logic                               |
CD    ----------------------------------------------------------------------
C
CT    That section computes the logic related to the flap relays. When
CT    relay labels are true, they are considered energized.
C
C
CD    W21010  FLAP RELEASE RELAY K1                               (AWRK1   )
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  sec. 27-50-01 fig.3
C
CT    A trigger (IDAWARM) incorporated into the flap selection lever must
CT    be raised to actuate a switch which directs electrical power to
CT    the flap drive power unit.  When the trigger is raised, the flap
CT    release relay k1 (AWRK1) is energized for a delay of 70 seconds.
CT
CT    The handle jams when the malfunction TF27F011 (FLAPS HANDLE JAM) is
CT    inserted and it is impossible to activate the flaps using the flaps
CT    handle.
CT
C
      IF ( BILL07 .AND. IDAWARM ) THEN
        AWRK1 = .TRUE.
        W2DTK1 = 0.0
      ELSE IF ( .NOT.BILL07 ) THEN
        AWRK1 = .FALSE.
      ENDIF
C
      IF (TF27F011) THEN
        AW$FLVR = .TRUE.
      ELSE
        AW$FLVR = .FALSE.
      ENDIF
C
      IF (AWRK1) THEN
        W2DTK1 = W2DTK1 + YITIM
      ENDIF
C
      IF ( AWRK1 .AND. (W2DTK1 .LT. W2CDTK1)) THEN
        AWRK1 = .TRUE.
      ELSE
        AWRK1 = .FALSE.
        W2DTK1 = 0.0
      ENDIF
C
C
CD    W21020  FLAP POWER CONTROL INDICATION RELAY K2              (AWRK2   )
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  sec. 27-50-01 fig.3
C
CT    The flap power control relay k2 (AWRK2) is energized when the flap
CT    release relay (AWRK1) is energized, so when the trigger is actuated.
C
      AWRK2 = AWRK1
C
C
CD    W21030  PRESSURE SWITCH STATUS                              (W2SPS   )
C     ----------------------------------------------------------------------
CR            Ref : [ 2 ]  sec. 27-50-00, page 203
CR                  [ 3 ]  sec. 27-50-01 fig.3
C
CT    The minimum pressure corresponds to the pressure at which the switch
CT    will close when system pressure is decreasing and the maximum
CT    pressure is the one at which the switch will open when system
CT    pressure is rising (see label W2CPSW).
CT
CT    The pressure switch (W2SPS1) is open when the system 1 pressure is
CT    over 950 psi and it closes when the pressure is below 750 psi.
CT
CT    When there is a malfunction of FPU PRESS SENSORS FAILS IN, the
CT    pressure sensor fails to detect the real pressure.
CT
CT    When flap lever is selected and the malfunction TF27F021 (FPU PRESS
CT    SENSOR FAILS IN LOW PRESSURE) is inserted, FLAP POWER light
CT    illuminates even if pressure is available.
CT
CT    When malfunction TF27F022 (FPU PRESS SENSOR FAILS IN HIGH PRESSURE)
CT    is inserted, no LOW HYD PRESS detection is possible. If pressure
CT    is below 1500 psi, flaps may stop without FLAP POWER light
CT    indication.
C
      IF  ( BILM07 ) THEN
C
        IF ( AWVISOL .EQ. 1.0 ) THEN
C
          IF ( AHP1 .LE. W2CPSW1(1) ) W2SPS1 = .FALSE.
          IF ( AHP1 .GE. W2CPSW1(2) ) W2SPS1 = .TRUE.
C
        ELSE
          W2SPS1 = .FALSE.
        ENDIF
C
        IF ( .NOT.TF27F021 .AND. .NOT.TF27F022
     &       .AND. ( AWVISOL .EQ. 1.0 ) ) THEN
C
          IF ( AHP1 .LE. W2CPSW2(1) ) W2SPS2 = .FALSE.
          IF ( AHP1 .GE. W2CPSW2(2) ) W2SPS2 = .TRUE.
C
        ELSE IF ( TF27F021 ) THEN
          W2SPS2 = .FALSE.
        ELSE IF ( TF27F022 ) THEN
          W2SPS2 = .TRUE.
        ELSE
          W2SPS2 = .FALSE.
        ENDIF
C
      ELSE
C
        W2SPS1 = .FALSE.
        W2SPS2 = .FALSE.
C
      ENDIF
C
C
CD    W21040  FLAP POWER CONTROL INDICATION RELAY K3              (AWRK3   )
C     ----------------------------------------------------------------------
CR            Ref : [ 2 ]  sec. 27-50-00, p.203
CR                  [ 3 ]  sec. 27-50-01 fig.3
C
CT    The flap power control relay k3 (AWRK3) is energized when the pressure
CT    switch (W2SPS2) is closed.
CT
C
      AWRK3 = W2SPS2
C
C
CD    W21050  RELAY K4                                            (W2RK4   )
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  sec. 27-50-01 fig.3
C
CT    The relay k4 has a delay of 100 milliseconds before operating:
CT    the Relay k4 is energized when one of relays k2 or k3 is
CT    not energized while the other is energized for more than
CT    100 milliseconds.
CT
C
      IF ( BILM07 .AND. AWRK2 .AND. .NOT.AWRK3 ) THEN
        W2RK4 = .TRUE.
      ELSEIF ( BILM07 .AND. .NOT.AWRK2 .AND. AWRK3 ) THEN
        W2RK4 = .TRUE.
      ELSE
        W2RK4 = .FALSE.
      ENDIF
C
      IF (W2RK4) THEN
        W2DTK4 = W2DTK4 + YITIM
      ELSE
        W2DTK4 = 0.0
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.2 :  Valves logic                               |
CD    ----------------------------------------------------------------------
C
CT    The flap system includes the following valves :
CT
CT    Flap isolation valve
CT    Flap shut-off valve
CT    Flap control valve
C
C
CD    W22010  FLAPS HYD SOLENOID VALVE CB TRIPS                   (TF27F031)
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    When the malfunction TF27F031 (FLAPS HYD SOLENOID VALVE CB TRIPS)
CT    is inserted, flaps cannot be selected: isolation valve closes
CT    immediately. Flaps will stop moving and their position will be
CT    displayed on the indicator.
C
CT    The FLAPS malfunction TF27F031 will cause the CB to trip when the
CT    corresponding bus is powered (i.e. BILL07 is set to true via AZ module).
C
C
      IF ( TF27F031 )  BPLL07 = BILL07
C
C
CD    W22020  FLAP ISOLATION VALVE POSITION                       (AWVISOL )
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  sec. 27-50-01 fig.3
C
CT    The isolation solenoid valve in the FPU is a two positions solenoid
CT    valve, offset to  the  normally  closed  position.  The valve is
CT    electrically energized when a flap selection is initiated.  If the
CT    flap release relay k1 is energized, the solenoid valve will go to
CT    the desired position instantaneously.
CT
C
      IF ( AWRK1 ) THEN
        AWVISOL = 1.0
      ELSE
        AWVISOL = 0.0
      ENDIF
C
C
CD    W22030  FLAP SHUTOFF VALVE POSITION                         (AWVSO   )
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  sec. 27-50-01 fig.3
C
CT    When the isolation solenoid valve is open, the valve supplies
CT    hydraulic pressure to operate the shut-off valve.
C
      IF ( (AWVSO .NE. 1.0) .AND. (AWVISOL .EQ. 1.0) ) THEN
        AWVSO = AWVSO + W2USO
        IF ( AWVSO .GT. 1.0 ) AWVSO = 1.0
      ELSEIF ( (AWVSO .NE. 0.0) .AND. (AWVISOL .EQ. 0.0) ) THEN
        AWVSO = AWVSO - W2USO
        IF ( AWVSO .LT. 0.0 ) AWVSO = 0.0
      ENDIF
C
C
CD    W22040  FLAP CONTROL VALVE POSITION                         (AWVCT   )
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  sec. 27-50-01 fig.3
C
CT    The control valve is closed when the selected flap angle is reached
CT    by the feedback system contained within the FPU.
C
CGP      IF ( W0FEXT .OR. W0FRET ) THEN
C
CGP        IF ( (AWVCT .NE. 1.0) .AND. (AWVISOL .EQ. 1.0) ) THEN
CGP          AWVCT = AWVCT + W2USO
CGP          IF ( AWVCT .GT. 1.0 ) AWVCT = 1.0
CGP        ELSEIF ( (AWVCT .NE. 0.0) .AND. (AWVISOL .EQ. 0.0) ) THEN
CGP          AWVCT = AWVCT - W2USO
CGP          IF ( AWVCT .LT. 0.0 ) AWVCT = 0.0
CGP        ENDIF
C
CGP      ELSE
C
CGP        IF ( AWVCT .NE. 0.0 ) THEN
CGP          AWVCT = AWVCT - W2USO
CGP          IF ( AWVCT .LT. 0.0 ) AWVCT = 0.0
CGP        ENDIF
C
CGP      ENDIF
C
      AWVCT = AWVSO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.3 :  Lights Logic                               |
CD    ----------------------------------------------------------------------
C
C
CD    W23010  FLAP POWER LT                                       (AW$PWR  )
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  sec. 27-50-01 fig.3
C
CT    FLAP POWER CAUTION lt illuminates when one the relay k4 is energized
CT    for more than 100 milliseconds.
C
      IF ( W2RK4 .AND. (W2DTK4 .GE. W2CDTK4)) THEN
        AW$PWR = .TRUE.
      ELSE
        AW$PWR = .FALSE.
      ENDIF
C
C
CD    W23020  FLAP DRIVE LT                                       (AW$FA   )
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  sec. 27-50-01 fig.3
C
CT    When there is a FLAPS PRIMARY SHAFT FAIL ( TF27F001 ) malfunction,
CT    the secondary drive system will carry load imposed by the failure
CT    and FLAP DRIVE caution light illuminates.
CT
CT    A torque sensor coupling connects the left and right wing secondary
CT    drives, and detects drive torque in either side of the secondary
CT    drive. The sensor will alert the pilot by bringing on a FLAP DRIVE
CT    caution light in flight compartment.
C
C
      IF ( TF27F001 ) THEN
        AW$FA = .TRUE.
      ELSE
        AW$FA = .FALSE.
      ENDIF
C
C
C
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 3 :  PERFORMANCES                                 #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.1 :  Time and Rate Factors                      |
CD    ----------------------------------------------------------------------
C
C
CD    W31010  HYDRAULIC PRESSURE FACTOR  (coeff)                  (W3UHP   )
C     ----------------------------------------------------------------------
CR            Ref : [ 1 ]  p.10
C
CT    The hydraulic pressure factor modifies the rate of the shaft movement
CT    and though the rate at which the flaps are extending and retracting,
CT    depending on available hydraulic pressure.
CT
CTT+
CT                    ^
CT                    |
CT               1.0  +                       /-------------
CT                    |                      /
CT       W3UHP        |                     / |
CT                    |                    /
CT                    |                   /   |
CT                    |                  /
CT               0.5  +                 /     |
CT                    |                /
CT                    |               / |     |
CT                    |              /
CT                    |             /   |     |
CT                    |            /
CT               0.0  +-----------+-----+-----+--------------------->
CT                                ^           ^           Hyd. press. (psi)
CT                            W3CHPL  1000   W3CHPU          AHP1
CT
CT
CT    where:  W3CHPL =  750 psi   :  Hydraulic pressure below which
CT                                   operation of flaps is not possible.
CT
CT            W3CHPU = 3000 psi   :  Hydraulic pressure above which
CT                                   increase in pressure has no effect.
CT
CTT-
CT
CT    With the Number 1 Engine shut down and the #1 Stby hydraulic pump on,
CT    the flap power light and the flap movement should cycle i.e. move and
CT    remain stationary. For 3 seconds, flaps move/light out, then for
CT    1 second, flaps still/light on. Flaps will stop moving when hydraulic
CT    pressure decreases below a certain value and it will begin to move
CT    when hydraulic pressure increases above a fixed value.
C
CC    This hysterisis determines the flaps timing movement (stop/start)
CC    on standby pump.
CC    If you increase the upper value, the Flap Power Light illuminates
CC    less often.
C
C
      IF ( AHP1 .LE. W3CPSW(1) ) W3PSW = .FALSE.
      IF ( AHP1 .GE. W3CPSW(2) ) W3PSW = .TRUE.
C
      IF ( AHP1 .GE. W3CHPU ) THEN
        W3UHP = W3XUHP(3)
      ELSE IF ( AHP1 .LE. W3CHPL ) THEN
        W3UHP = W3XUHP(1)
      ELSE
        J = 1
        DO I = 1,5
          IF ( AHP1 .GT. W3PAHP1(J+1) ) THEN
            J = J + 1
          ENDIF
          IF ( AHP1 .LT. W3PAHP1(J) ) THEN
            J = J - 1
          ENDIF
        ENDDO
        W3UHP = AHP1 * W3MHP(J) + W3BHP(J)
      ENDIF
C
      IF ( .NOT.W3PSW ) THEN
        W3UHP = 0.0
      ENDIF
C
C
CD    W31020  VALVE FACTOR  (coeff)                               (W3UV    )
C     ----------------------------------------------------------------------
CR            Ref : [ 1 ]  p.10
C
CT
CT    The rate of the shaft movement depends also on the three following
CT    valves:
CT
CT        Flap isolation valve  ( AWVISOL )
CT        Flap shut-off valve   ( AWVSO )
CT        Flap control valve    ( AWVCT )
C
      W3UV = AWVSO * AWVISOL * AWVCT
C
C
CD    W31030  AIRSPEED RATE FACTOR  (coeff)                       (W3UAIRS )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    The airspeed factor multiplies the normal rate to take into account
CT    the effect of the drag force on the flap.
C
C
      IF ( W0MSR300 ) THEN
C
        DO I = 1,6
          W3XEXTF(I) = W3XEXTF3(I)
C !FM+
C !FM  27-Jun-92 20:39:36 R.AUBRY
C !FM    < Set the current value of current call routine. >
C !FM
          W3M1(I)    = W3M3(I)
          W3B1(I)    = W3B3(I)
C !FM-
        ENDDO
C
      ELSE
C
        IF ( W3JPOS .EQ. 1 ) THEN
          DO I = 1,6
            W3XEXTF(I) = W3XEXT1A(I)
C !FM+
C !FM  27-Jun-92 20:39:36 R.AUBRY
C !FM    < Set the current value of current call routine. >
C !FM
            W3M1(I)    = W3M1A(I)
            W3B1(I)    = W3B1A(I)
C !FM-
          ENDDO
        ELSE IF  ( W3JPOS .EQ. 2 ) THEN
          DO I = 1,6
            W3XEXTF(I) = W3XEXT1B(I)
C !FM+
C !FM  27-Jun-92 20:39:36 R.AUBRY
C !FM    < Set the current value of current call routine. >
C !FM
            W3M1(I)    = W3M1B(I)
            W3B1(I)    = W3B1B(I)
C !FM-
          ENDDO
        ELSE
          DO I = 1,6
            W3XEXTF(I) = W3XEXT1C(I)
C !FM+
C !FM  27-Jun-92 20:39:36 R.AUBRY
C !FM    < Set the current value of current call routine. >
C !FM
            W3M1(I)    = W3M1C(I)
            W3B1(I)    = W3B1C(I)
C !FM-
          ENDDO
        ENDIF
C
      ENDIF
C
      IF ( W0FEXT ) THEN
C
        IF ( VVE .GE. W3UVVE(6) ) THEN
          W3UAIRS = W3XEXTF(6)
        ELSE IF ( VVE .LE. W3UVVE(1) ) THEN
          W3UAIRS = W3XEXTF(1)
        ELSE
          J=1
          DO I = 1,6
            IF ( VVE .GE. W3UVVE(J+1) ) THEN
              J = J + 1
            ENDIF
            IF ( VVE .LE. W3UVVE(J) ) THEN
              J = J - 1
            ENDIF
            W3UAIRS = VVE*W3M1(J) + W3B1(J)
          ENDDO
        ENDIF
C
      ENDIF
C
      IF ( W0FRET ) THEN
C
        IF ( VVE .GE. W3UVVE(6) ) THEN
          W3UAIRS = W3XRETF(6)
        ELSE IF ( VVE .LE. W3UVVE(1) ) THEN
          W3UAIRS = W3XRETF(1)
        ELSE
          J = 1
          DO I = 1,6
            IF ( VVE .GE. W3UVVE(J+1) ) THEN
              J = J + 1
            ENDIF
            IF ( VVE .LE. W3UVVE(J) ) THEN
              J = J - 1
            ENDIF
            W3UAIRS = VVE*W3M2(J) + W3B2(J)
          ENDDO
        ENDIF
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.2 :  PDU Rate                                   |
CD    ----------------------------------------------------------------------
C
C
CD    W32010  EXTEND RATE OF MOVEMENT  (%/sec)                    (W3UEX   )
C     ----------------------------------------------------------------------
CR            Ref : Flight Test
C
CT    Extension times are summarized below in terms of flap position and
CT    air speed.  These values may change.
CT
CTT+
CT    SERIE 300
CT    =========
CT                          Extension Times
CT                          ---------------
CT
CT       Extension                      Change in flap pos'n
CT       Times (sec)
CT                     |          0      5      10      15      35
CT                     |         0->5   5->10  10->15  15->35
CT              -------+-------------------------------------------------
CT              0      |         8.2     9.3    7.2     8.3     Dummy
CT              -------+-------------------------------------------------
CT   Airspeed   125    |         8.9    10.11   7.8     6.5     Dummy
CT    (knots)   -------+-------------------------------------------------
CT              140    |         9.7    11.1    8.6     11.4    Dummy
CT              ---------------------------------------------------------
CT
CT
CT    The following estimations were used to expand the above information
CT    into complete data:
CT
CT    1. It is assumed that the flaps will not further extend when
CT       airspeed rises 9 knots above Vfe.
CT
CT    2. Intermediate values are interpolated or extrapolated
CT       between points.
CT
CT
CT
CT    From the above data, the extension rates are simply calculated by
CT    dividing the distance (deg) by the time required to extend (sec).
CT
CT                Movement (deg)
CT                -------------- = Rate (deg/sec)
CT                Time (sec)
CT
CT
CT
CT                          Extension Rates
CT                          ---------------
CT
CT      Extension                      Change in flap pos'n
CT      Rates (deg/sec)
CT                     |          5      5       5       20
CT                     |         0->5   5->10  10->15  15->35   35->
CT              -------+-------------------------------------------------
CT              0      |         0.61    0.54   0.69    2.4      -
CT              -------+-------------------------------------------------
CT   Airspeed   125    |         0.56    0.49   0.64    3.08     -
CT    (knots)   -------+-------------------------------------------------
CT              140    |         0.52    0.45   0.58    1.75     -
CT              ---------------------------------------------------------
CT
CT
CT    SERIE 100
CT    =========
CT                          Extension Times
CT                          ---------------
CT
CT       Extension                      Change in flap pos'n
CT       Times (sec)
CT                     |          0           5         15      35
CT                     |         0->5       5->15     15->35
CT              -------+-------------------------------------------------
CT              0      |         7.8        16.9        8.5     Dummy
CT              -------+-------------------------------------------------
CT   Airspeed   125    |         7.2        20.8        6.5     Dummy
CT    (knots)   -------+-------------------------------------------------
CT              140    |         7.2        20.8        6.5     Dummy
CT              ---------------------------------------------------------
CT
CT
CT
CT                          Extension Rates
CT                          ---------------
CT
CT      Extension                      Change in flap pos'n
CT      Rates (deg/sec)
CT                     |          5           10         20
CT                     |         0->5        5->15     15->35   35->
CT              -------+-------------------------------------------------
CT              0      |         0.64        0.592      2.35     -
CT              -------+-------------------------------------------------
CT   Airspeed   125    |         0.69        0.48       3.077    -
CT    (knots)   -------+-------------------------------------------------
CT              140    |         0.69        0.48       3.077    -
CT              ---------------------------------------------------------
CT
CT
CTT-
C
C
      IF ( W0MSR300 ) THEN
C
CC      *****   Extension   *****
C
        IF ( W0FEXT ) THEN
          W3UEX  = W3XEX300(W3JPOS) * W3CDTP * W3UAIRS
        ENDIF
C
      ELSE
C
CC      *****   Extension   *****
C
        IF ( W0FEXT ) THEN
          W3UEX  = W3XEX100(W3JPOS) * W3CDTP * W3UAIRS
        ENDIF
C
      ENDIF
C
C
CD    W32020  RETRACT RATE OF MOVEMENT  (%/sec)                   (W3URE   )
C     ----------------------------------------------------------------------
CR            Ref : Flight Test
C
CT    Retraction times are summarized and shown in the following tables.
CT    These values may change.
CT
CTT+
CT    SERIE 300
CT    =========
CT                          Retraction Times
CT                          ----------------
CT
CT       Retraction                     Change in flap pos'n
CT       Times (sec)
CT                     |          0      5      10      15      35
CT                     |         0<-5   5<-10  10<-15  15<-35
CT              -------+-------------------------------------------------
CT              0      |        10.6    10.6    5.2     6.6     Dummy
CT              -------+-------------------------------------------------
CT   Airspeed   125    |         8.0     9.0    4.4     5.6     Dummy
CT    (knots)   -------+-------------------------------------------------
CT              140    |         7.4     7.4    3.6     4.6     Dummy
CT              ---------------------------------------------------------
CT
CT
CT
CT
CT                          Retraction Rates
CT                          ----------------
CT
CT      Retraction                     Change in flap pos'n
CT      Rates (deg/sec)
CT                     |          5      5       5       20
CT                     |         0<-5   5<-10  10<-15  15<-35
CT              -------+-------------------------------------------------
CT              0      |         0.47    0.47   0.96    3.03      -
CT              -------+-------------------------------------------------
CT   Airspeed   125    |         0.625   0.56   1.14    3.57      -
CT    (knots)   -------+-------------------------------------------------
CT              140    |         0.68    0.68   1.39    4.35      -
CT              ---------------------------------------------------------
CT
CT
CT    SERIE 100
CT    =========
CT                          Retraction Times
CT                          ----------------
CT
CT       Retraction                     Change in flap pos'n
CT       Times (sec)
CT                     |          0           5         15      35
CT                     |         0<-5       5<-15     15<-35
CT              -------+-------------------------------------------------
CT              0      |        10.6         15.8       6.6     Dummy
CT              -------+-------------------------------------------------
CT   Airspeed   125    |         8.0         13.4       5.6     Dummy
CT    (knots)   -------+-------------------------------------------------
CT              140    |         7.4         11.0       4.6     Dummy
CT              ---------------------------------------------------------
CT
CT
CT
CT
CT                          Retraction Rates
CT                          ----------------
CT
CT      Retraction                     Change in flap pos'n
CT      Rates (deg/sec)
CT                     |          5           10         20
CT                     |         0<-5        5<-15     15<-35
CT              -------+-------------------------------------------------
CT              0      |         0.47        0.63       3.03      -
CT              -------+-------------------------------------------------
CT   Airspeed   125    |         0.625       0.75       3.57      -
CT    (knots)   -------+-------------------------------------------------
CT              140    |         0.68        0.91       4.35      -
CT              ---------------------------------------------------------
CT
CT
CTT-
C
C
      IF ( W0MSR300 ) THEN
C
CC      *****   Retraction   *****
C
        IF ( W0FRET ) THEN
          W3URE  = -W3XRE300(W3JPOS) * W3CDTP * W3UAIRS
        ENDIF
C
      ELSE
C
CC      *****   Retraction   *****
C
        IF ( W0FRET ) THEN
          W3URE  = -W3XRE100(W3JPOS) * W3CDTP * W3UAIRS
        ENDIF
C
      ENDIF
C
C
CD    W32030  PDU RATE OF MOVEMENT  (%/iter)                      (W3UPDU  )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    The PDU movement follows a pseudo-exponential curve.
CT
CT    That means that the flaps will move faster at the beginning and then
CT    the speed of the flaps movement will decrease when approaching its
CT    final value.
CT
CT    It follows the equation:
CT
CTT+
CT    W3APDU = W3APDU + ( W0ASHAFT - W3APDU )  *  YITIM
CT                                               -------
CT                                                  T
CT
CT    Where :  W0ASHAFT = Shaft position request (target)
CT             W3APDU   = PDU shaft position
CT             T        = Time constant
CT
CT    After about 5*T seconds, the function (W3APDU) has reached its
CT    final value. Though, flaps are at the lever position request.
CT
CTT-
C
C
CC    *****   Extension   *****
C
      IF ( W0FEXT ) THEN
C
        IF ( ( W0ASHAFT - W3APDU ) .LT. W3CFTO*W3CDTP ) THEN
C
          W3UPDU  = ((W0ASHAFT - W3APDU) / (W3CTO/5.0))
          IF ( W3UPDU .GT. W3UEX ) THEN
            W3UPDU = W3UEX
          ENDIF
          W3UPDU  = W3UPDU * W3UHP * W3UV * YITIM
        ELSE
          W3UPDU  = W3UEX * W3UHP * W3UV * YITIM
C
        ENDIF
C
        IF ( W3UPDU .NE. 0.0 ) THEN
          IF ( W3UPDU .LT. W3CUPDUL ) THEN
           W3UPDU  = W3CUPDUL
          ENDIF
        ENDIF
        W3USEC  = W3UPDU
        W3USHLI = W3UPDU
        W3USHRI = W3UPDU
C
      ENDIF
C
CC    *****   Retraction   *****
C
      IF ( W0FRET ) THEN
C
        IF ( ( W0ASHAFT - W3APDU ) .GT. -W3CFTO*W3CDTP ) THEN
C
          W3UPDU  = ((W0ASHAFT - W3APDU) / (W3CTO/5.0))
          IF ( W3UPDU .LT. W3URE ) THEN
            W3UPDU = W3URE
          ENDIF
          W3UPDU  = W3UPDU * W3UHP * W3UV * YITIM
        ELSE
          W3UPDU  = W3URE * W3UV * W3UHP * YITIM
        ENDIF
C
        IF ( W3UPDU .NE. 0.0 ) THEN
          IF ( W3UPDU .GT. -W3CUPDUL ) THEN
            W3UPDU  = -W3CUPDUL
          ENDIF
        ENDIF
        W3USEC  = W3UPDU
        W3USHLI = W3UPDU
        W3USHRI = W3UPDU
C
      ENDIF
C
C !FM+
C !FM  25-Jun-92 09:30:17 R.AUBRY
C !FM    < This section causes the L inbd & outbd flaps to stop when mlf is
C !FM      inserted which is not correct. >
C !FM
C
CRA   IF ( TF27F001 ) THEN
CRA     W3FSEC  = .TRUE.
CRA     W3USHLI = 0.0
CRA     IF ( .NOT.W3FSECF ) THEN
CRA       W3FSECF = .TRUE.
CRA       W3DSEC  = 0.0
CRA     ENDIF
CRA   ELSE
        W3FSEC  = .FALSE.
        W3FSECF = .FALSE.
CRA   ENDIF
C !FM-
C
      IF ( (HFLEV .EQ. -2) .AND. .NOT.W2SPS1 ) THEN
        W3UPDU  = 0.0
        W3USEC  = 0.0
        W3USHLI = 0.0
        W3USHRI = 0.0
      ENDIF
C
C
CD    W32040  PRIMARY SHAFT RATE  (%/iter)                        (W3USHXX )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    Shaft position is computed as a percentage of the total actuation, where
CT    0 % is flaps retracted and 100 % is flaps fully extended. The 6 shaft
CT    sections are respectively: L inbd, R inbd, L outbd, R outbd, L mid and
CT    R mid.
C
C
        W3USHLM = W3USHLI    ! Mid shaft
C
        W3USHRM = W3USHRI    ! Mid shaft
C
        W3USHLO = W3USHLM    ! Outbd shaft
C
        W3USHRO = W3USHRM    ! Outbd shaft
C
C
C
CD    W32050  SECONDARY SHAFT RATE  (%/iter)                      (W3USSHX )
C     ----------------------------------------------------------------------
CR            Ref : [ 4 ]  page 5, par. 2.5.2.3
C
CT    The flap system is designed for continued safe flight in the event of
CT    a single failure. The secondary transmission installation will allow
CT    continued flap operation after a failed drive shaft, though its
CT    primary purpose is to prevent flap asymmetry.
CT
CT    A secondary drive system maintains symmetrical operation of all flaps
CT    in case of breakage of the primary shaft.  It consists of a secondary
CT    flexible transmission shaft wich is parallel to the primary shaft.
CT
CT    There is a certain time delay before complete transmission of the
CT    secondary drive system to the primary when a failure occurs.
CTT+
CT
CT                       ^
CT                       |
CT                     1 +     /-----
CT          Speed        |    /|
CT          factor       |   / |
CT         (W3XSEC)      |  /  |
CT                       | /   |
CT                       |/    |
CT                     0 +-----+-------------->
CT                       0     5         Time (sec)
CT                             ^
CT                           W3CSEC
CT
CTT-
C
      W3XSEC = ( 1.0 / W3CSEC ) * W3DSEC
C
      W3DSEC = W3DSEC + YITIM
C
      IF ( W3DSEC .GT. 20.0 ) W3DSEC = 10.0
C
      IF ( W3XSEC .GT. 1.0 ) THEN
        W3XSEC = 1.0
      ELSE IF ( W3XSEC .LT. 0.0 ) THEN
        W3XSEC = 0.0
      ENDIF
C
      W3USSHL = W3USEC     ! Left secondary shaft
C
      W3USSHR = W3USEC     ! Right secondary shaft
C
C
CD    W32060  TORQUE SENSOR                                       (W3FTORX )
C     ----------------------------------------------------------------------
CR            Ref : [ 4 ]  page 5, par 2.5.2.3
C
CT    A transfer gearbox at each end connects the primary and secondary
CT    shafts together, creating a closed loop with the secondary shaft
CT    normally operating under no load. In the event of failure of the
CT    primary drive shaft at any point on its lenght, torque is
CT    automatically transmitted via the secondary shaft and transfer
CT    gearboxes to the section of primary shaft outboard of the failure.
CT
C
      IF ( ( W3USHLO .GT. ( W3USSHL + 0.2 ) ) .AND.
     &     ( W3USHLO .LT. ( W3USSHL - 0.2 ) ) ) THEN
        W3FTORL = .TRUE.
      ELSE
        W3FTORL = .FALSE.
      ENDIF
C
      IF ( ( W3USHRO .GT. ( W3USSHR + 0.2 ) ) .AND.
     &     ( W3USHRO .LT. ( W3USSHR - 0.2 ) ) ) THEN
        W3FTORR = .TRUE.
      ELSE
        W3FTORR = .FALSE.
      ENDIF
C
      IF ( W3FTORL ) THEN
        AW$FA   = .TRUE.
        W3USHLO = W3USSHL*W3XSEC
        W3USHLM = W3USHLO
        W3USHLI = W3USHLM
      ENDIF
C
      IF ( W3FTORR ) THEN
        AW$FA   = .TRUE.
        W3USHRO = W3USSHR*W3XSEC
        W3USHRM = W3USHRO
        W3USHRI = W3USHRM
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.3 :  PDU position                               |
CD    ----------------------------------------------------------------------
C
C
CD    W33010  PDU POSITION  (%)                                   (W3APDU  )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    PDU position is computed as a percentage of the total actuation, where
CT    0 % is flaps retracted and 100 % is flaps fully extended.
C
C
      IF ( .NOT.W3FSEC ) THEN
C
CC    *****   Extension   *****
C
        IF ( W0FEXT ) THEN
C
          W3APDU = W3APDU + W3UPDU
C
          IF ( W3APDU .GT. ( W0ASHAFT - W3CGAP1*W3CDTP ) ) THEN
            W3APDU = W0ASHAFT
          ENDIF
C
        ENDIF
C
CC    *****   Retraction   *****
C
        IF ( W0FRET ) THEN
C
          W3APDU = W3APDU + W3UPDU
C
          IF ( W3APDU .LT. ( W0ASHAFT + W3CGAP1*W3CDTP ) ) THEN
            W3APDU = W0ASHAFT
          ENDIF
C
        ENDIF
C
C
      ELSE
C
CC    *****   Extension   *****
C
        IF ( W0FEXT ) THEN
C
          W3APDU = W3APDU + W3USEC * W3XSEC
C
          IF ( W3APDU .GT. ( W0ASHAFT - W3CGAP1*W3CDTP ) ) THEN
            W3APDU = W0ASHAFT
          ENDIF
C
        ENDIF
C
CC    *****   Retraction   *****
C
        IF ( W0FRET ) THEN
C
          W3APDU = W3APDU + W3USEC * W3XSEC
C
          IF ( W3APDU .LT. ( W0ASHAFT + W3CGAP1*W3CDTP ) ) THEN
            W3APDU = W0ASHAFT
          ENDIF
C
        ENDIF
C
      ENDIF
C
C
CC    *****     Left Inboard shaft   *****
C
CC    *****   Extension   *****
C
      IF ( W0FEXT ) THEN
C
        W3ASHLI = W3ASHLI + W3USHLI
C
        IF ( W3ASHLI .GT. ( W0ASHAFT - W3CGAP1*W3CDTP ) ) THEN
          W3ASHLI = W0ASHAFT
        ENDIF
C
      ENDIF
C
CC    *****   Retraction   *****
C
      IF ( W0FRET ) THEN
C
        W3ASHLI = W3ASHLI + W3USHLI
C
        IF ( W3ASHLI .LT. ( W0ASHAFT + W3CGAP1*W3CDTP ) ) THEN
          W3ASHLI = W0ASHAFT
        ENDIF
C
      ENDIF
C
C
CC    *****   Right Inboard Shaft   *****
C
CC    *****   Extension   *****
C
      IF ( W0FEXT ) THEN
C
        W3ASHRI = W3ASHRI + W3USHRI
C
        IF ( W3ASHRI .GT. ( W0ASHAFT - W3CGAP1*W3CDTP ) ) THEN
          W3ASHRI = W0ASHAFT
        ENDIF
C
      ENDIF
C
CC    *****   Retraction   *****
C
      IF ( W0FRET ) THEN
C
        W3ASHRI = W3ASHRI + W3USHRI
C
        IF ( W3ASHRI .LT. ( W0ASHAFT + W3CGAP1*W3CDTP ) ) THEN
          W3ASHRI = W0ASHAFT
        ENDIF
C
      ENDIF
C
C
CC    *****   Left Mid Shaft   *****
C
CC    *****   Extension   *****
C
      IF ( W0FEXT ) THEN
C
        W3ASHLM = W3ASHLM + W3USHLM
C
        IF ( W3ASHLM .GT. ( W0ASHAFT - W3CGAP1*W3CDTP ) ) THEN
          W3ASHLM = W0ASHAFT
        ENDIF
C
      ENDIF
C
CC    *****   Retraction   *****
C
      IF ( W0FRET ) THEN
C
        W3ASHLM = W3ASHLM + W3USHLM
C
        IF ( W3ASHLM .LT. ( W0ASHAFT + W3CGAP1*W3CDTP ) ) THEN
          W3ASHLM = W0ASHAFT
        ENDIF
C
      ENDIF
C
C
CC    *****   Right Mid Shaft   *****
C
CC    *****   Extension   *****
C
      IF ( W0FEXT ) THEN
C
        W3ASHRM = W3ASHRM + W3USHRM
C
        IF ( W3ASHRM .GT. ( W0ASHAFT - W3CGAP1*W3CDTP ) ) THEN
          W3ASHRM = W0ASHAFT
        ENDIF
C
      ENDIF
C
CC    *****   Retraction   *****
C
      IF ( W0FRET ) THEN
C
        W3ASHRM = W3ASHRM + W3USHRM
C
        IF ( W3ASHRM .LT. ( W0ASHAFT + W3CGAP1*W3CDTP ) ) THEN
          W3ASHRM = W0ASHAFT
        ENDIF
C
      ENDIF
C
C
CC    *****   Left Outboard Shaft   *****
C
CC    *****   Extension   *****
C
      IF ( W0FEXT ) THEN
C
        W3ASHLO = W3ASHLO + W3USHLO
C
        IF ( W3ASHLO .GT. ( W0ASHAFT - W3CGAP1*W3CDTP ) ) THEN
          W3ASHLO = W0ASHAFT
        ENDIF
C
      ENDIF
C
CC    *****   Retraction   *****
C
      IF ( W0FRET ) THEN
C
        W3ASHLO = W3ASHLO + W3USHLO
C
        IF ( W3ASHLO .LT. ( W0ASHAFT + W3CGAP1*W3CDTP ) ) THEN
          W3ASHLO = W0ASHAFT
        ENDIF
C
      ENDIF
C
C
CC    *****   Right Outboard Shaft   *****
C
CC    *****   Extension   *****
C
      IF ( W0FEXT ) THEN
C
        W3ASHRO = W3ASHRO + W3USHRO
C
        IF ( W3ASHRO .GT. ( W0ASHAFT - W3CGAP1*W3CDTP ) ) THEN
          W3ASHRO = W0ASHAFT
        ENDIF
C
      ENDIF
C
CC    *****   Retraction   *****
C
      IF ( W0FRET ) THEN
C
        W3ASHRO = W3ASHRO + W3USHRO
C
        IF ( W3ASHRO .LT. ( W0ASHAFT + W3CGAP1*W3CDTP ) ) THEN
          W3ASHRO = W0ASHAFT
        ENDIF
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.4 :  FLAPS position                             |
CD    ----------------------------------------------------------------------
C
C
CD    W34010  FLAPS POSITION  (deg)                               (AWAFXX  )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    If there is a maintenance reset, flaps position is set to the flap
CT    average position (W3AFA).
CT    Otherwise, flaps position is set according to the flaps rate
CT    of movement. Flaps rate of movement is equal to the
CT    corresponding shaft rate.
CT
C
C
CC    **********   Extension   **********
C
      IF ( W0FEXT ) THEN
C
CC    ***   Left Outboard   ***
C
        AWAFOL = AWAFOL + (W3USHLO * W3CPTD)
        IF ( AWAFOL .GT. ( W0AFLAP - W3CGAP1) ) THEN
          AWAFOL = W0AFLAP
        ENDIF
C
CC    ***   Left Inboard   ***
C
        AWAFIL = AWAFIL + (W3USHLI * W3CPTD)
        IF ( AWAFIL .GT. ( W0AFLAP - W3CGAP1) ) THEN
          AWAFIL = W0AFLAP
        ENDIF
C
CC    ***   Right Outboard   ***
C
        AWAFOR = AWAFOR + (W3USHRO * W3CPTD)
        IF ( AWAFOR .GT. ( W0AFLAP - W3CGAP1) ) THEN
          AWAFOR = W0AFLAP
        ENDIF
C
CC    ***   Right Inboard   ***
C
        AWAFIR = AWAFIR + (W3USHRI * W3CPTD)
        IF ( AWAFIR .GT. ( W0AFLAP - W3CGAP1) ) THEN
          AWAFIR = W0AFLAP
        ENDIF
C
      ENDIF
C
CC    **********   Retraction   **********
C
      IF ( W0FRET ) THEN
C
C
CC    ***   Left Outboard   ***
C
        AWAFOL = AWAFOL + (W3USHLO * W3CPTD)
        IF ( AWAFOL .LT. ( W0AFLAP + W3CGAP1 ) ) THEN
          AWAFOL = W0AFLAP
        ENDIF
C
CC    ***   Left Inboard   ***
C
        AWAFIL = AWAFIL + (W3USHLI * W3CPTD)
        IF ( AWAFIL .LT. ( W0AFLAP + W3CGAP1 ) ) THEN
          AWAFIL = W0AFLAP
        ENDIF
C
CC    ***   Right Outboard   ***
C
        AWAFOR = AWAFOR + (W3USHRO * W3CPTD)
        IF ( AWAFOR .LT. ( W0AFLAP + W3CGAP1 ) ) THEN
          AWAFOR = W0AFLAP
        ENDIF
C
CC    ***   Right Inboard   ***
C
        AWAFIR = AWAFIR + (W3USHRI * W3CPTD)
        IF ( AWAFIR .LT. ( W0AFLAP + W3CGAP1 ) ) THEN
          AWAFIR = W0AFLAP
        ENDIF
C
      ENDIF
C
C
      IF ( TCRMAINT ) THEN             ! .OR. TCRTEFLP Flaps assym reset
C
        AWAFIL = W3AFA
        AWAFIR = W3AFA
        AWAFOL = W3AFA
        AWAFOR = W3AFA
C
      ENDIF
C
C
CD    W34020  PREVIOUS AVERAGE FLAPS POSITION  (deg)              (W3AFAQ  )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    It is necessary to remember the previous average flaps position to
CT    compute the average flaps movement.
C
C
      W3AFAQ = W3AFA
C
C
CD    W34030  AVERAGE FLAP POSITION  (deg)                        (W3AFA   )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    This equation evaluates the average of the four flaps positions:
CT    Left outboard, left inboard, right inboard and right outboard.
C
C
      W3AFA = ( AWAFOL + AWAFIL + AWAFIR + AWAFOR ) * 0.25
C
C
CD    W34040  FLAP POSITION SENSOR LEFT WING  (deg)               (AWAFL   )
C     ----------------------------------------------------------------------
CR            Ref : [ 4 ]  page 4
C
CT    Flaps position sensors are contained within transfer gearboxes which
CT    interconnect the primary and the secondary transmission systems. The
CT    sensor on the left wing is connected electrically to the flap position
CT    indicator in the cockpit.
C
C
        IF ( ( W3ASHLO .GE. ( 0.0 - W3CGAP1*W3CDTP ) )  .AND.
     &       ( W3ASHLO .LE. ( 0.0 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFL   = 0.0
C
        ELSE IF ( ( W3ASHLO .GE. ( 14.2857 - W3CGAP1*W3CDTP ) )  .AND.
     &            ( W3ASHLO .LE. ( 14.2857 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFL   = 5.0
C
        ELSE IF ( ( W3ASHLO .GE. ( 28.5714 - W3CGAP1*W3CDTP ) )  .AND.
     &            ( W3ASHLO .LE. ( 28.5714 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFL   = 10.0
C
        ELSE IF ( ( W3ASHLO .GE. ( 42.8571 - W3CGAP1*W3CDTP ) )  .AND.
     &            ( W3ASHLO .LE. ( 42.8571 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFL   = 15.0
C
        ELSE IF ( ( W3ASHLO .GE. ( 100.0 - W3CGAP1*W3CDTP ) )  .AND.
     &            ( W3ASHLO .LE. ( 100.0 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFL   = 35.0
C
        ELSE
          AWAFL   = W3ASHLO*W3CPTD
C
        ENDIF
C
C
CD    W34050  FLAP POSITION SENSOR RIGHT WING  (deg)              (AWAFR   )
C     ----------------------------------------------------------------------
CR            Ref : [ 4 ]  page 4
C
CT    Flaps position sensors are contained within transfer gearboxes which
CT    interconnect the primary and the secondary transmission systems. The
CT    sensor on the right wing is set according to the Right Outboard shaft
CT    position.
C
        IF ( ( W3ASHRO .GE. ( 0.0 - W3CGAP1*W3CDTP ) )  .AND.
     &       ( W3ASHRO .LE. ( 0.0 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFR   = 0.0
C
        ELSE IF ( ( W3ASHRO .GE. ( 14.2857 - W3CGAP1*W3CDTP ) )  .AND.
     &            ( W3ASHRO .LE. ( 14.2857 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFR   = 5.0
C
        ELSE IF ( ( W3ASHRO .GE. ( 28.5714 - W3CGAP1*W3CDTP ) )  .AND.
     &            ( W3ASHRO .LE. ( 28.5714 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFR   = 10.0
C
        ELSE IF ( ( W3ASHRO .GE. ( 42.8571 - W3CGAP1*W3CDTP ) )  .AND.
     &            ( W3ASHRO .LE. ( 42.8571 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFR   = 15.0
C
        ELSE IF ( ( W3ASHRO .GE. ( 100.0 - W3CGAP1*W3CDTP ) )  .AND.
     &            ( W3ASHRO .LE. ( 100.0 + W3CGAP1*W3CDTP ) ) ) THEN
          AWAFR   = 35.0
C
        ELSE
          AWAFR   = W3ASHRO*W3CPTD
C
        ENDIF
C
C
CD    W34060  AVERAGE FLAPS MOVEMENT  (deg)                       (W3AFM   )
C     ----------------------------------------------------------------------
CR            Ref : [ N/A ]
C
CT    The average flaps movement (W3AFM) is the difference between the
CT    average flaps position (W3AFA) and the previous average flaps
CT    position (W3AFAQ).
C
      W3AFM = W3AFA - W3AFAQ
C
C
CD    W34070  FLAPS HYDRAULIC DEMAND  (gal/min)                   (AWWD    )
C     ----------------------------------------------------------------------
CR            Ref : [ 2 ]  page 201
CR                  [ 6 ]
C
CT    The hydraulic demand (AWWD) depends on the Rate of Flaps Movement
CT    (W3UPDU when there is no shaft failure and W3USEC when the secondary
CT    drive system will carry load imposed by the failure).
CT    The maximum demand is 3.3 gal/min when full extension or retraction.
C
      IF ( .NOT.W3FSEC ) THEN
        IF ( W3UPDU .GT. 0.0 ) THEN
          W3PAWWD = W3UPDU + W3CAWWD
          IF ( W3PAWWD .GT. W3CAWWDM ) THEN
            AWWD = W3CAWWDM
          ELSE
            AWWD = W3PAWWD
          ENDIF
        ELSE IF ( W3UPDU .LT. 0.0 ) THEN
          W3PAWWD = -W3UPDU + W3CAWWD
          IF ( W3PAWWD .GT. W3CAWWDM ) THEN
            AWWD = W3CAWWDM
          ELSE
            AWWD = W3PAWWD
          ENDIF
        ELSE
          AWWD = 0.0
        ENDIF
      ELSE
        IF (W3USEC .GT. 0.0 ) THEN
          AWWD = W3USEC * W3XSEC
        ELSE IF ( W3USEC .LT. 0.0 ) THEN
          AWWD = -W3USEC * W3XSEC
        ELSE
          AWWD = 0.0
        ENDIF
      ENDIF
C
C
CD    W34080  TAKEOFF WARNING HORN SIGNAL                         (AWFTOWH )
C     ----------------------------------------------------------------------
CR            Ref : [ 3 ]  27-50-21
CR                  [ 5 ]
C
CT    Channel 2 receives a signal from the right transfer gearbox RVDT and
CT    uses it to generate a discrete signal used by the take off warning
CT    system. Power for channel 2 is supplied by the "FLAP POSN 2" breaker
CT    on the right 28VDC Essential Bus (BIRM07).
CT
CT    When the TAKEOFF Reposition is selected and flaps are at 0 degree,
CT    the Takeoff warning horn will sound unless the FLAP POSN IND R CB
CT    (BIRM07) IS PULLED.
C
C
      AWFTOWH = ( BIRM07 .AND. ( AWAFL .LT. 4.5 .OR. AWAFL .GT. 15.5 ) )
C
C
CD    W34090  SIMULATION OF THE TRIGGER                           (W3TRIG  )
C     ----------------------------------------------------------------------
CR            [ N/A ]
C
CT    This section will allow flaps to move after the ATG Request (HATGON)
CT    is removed, without having to raise manually the trigger.
CC    Request to snag #397
C
C
      IF ( ( HATGON ) .AND. ( W0HFLEVQ .EQ. W0HFLEV ) ) THEN
        W3TRIG = .TRUE.
      ELSEIF ( W3TRIG ) THEN
        W3TRIG = .FALSE.
        AWRK1  = .TRUE.
        W2DTK1 = 0.0
      ENDIF
C
C
C
      RETURN
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00776 ###                                                                ###
C$ 00777 ######################################################################
C$ 00778 #                                                                    #
C$ 00779 #          SECTION 0 :  INITIAL & SPECIAL FUNCTIONS                  #
C$ 00780 #                                                                    #
C$ 00781 ######################################################################
C$ 00782 ###                                                                ###
C$ 00786 ----------------------------------------------------------------------
C$ 00787 |          Section 0.1 :  First pass                                 |
C$ 00788 ----------------------------------------------------------------------
C$ 00798 W01010  SHIP SELECTION AND OPTIONS                          (W0MSR300)
C$ 00833 W01020  GREY CONCEPT MLF                                    (--------)
C$ 00848 W01030  MODULE FREQUENCY FOR INSTRUMENT MODULE (AX)         (AWHFRQ  )
C$ 00859 W01040  MISCELLENAOUS                                       (--------)
C$ 00883 W01050  CALL SUBROUTINE FOR SLOPE-INTERCEPT TABLES          (--------)
C$ 00919 ----------------------------------------------------------------------
C$ 00920 |          Section 0.2 :  General                                    |
C$ 00921 ----------------------------------------------------------------------
C$ 00928 W02010  FLAPS SYNCHRONISATION FLAG FOR INDICATORS           (AWFSYNC )
C$ 00942 W02020  FLAP POSITION INDICATOR CHANNEL                     (W0FCHANX)
C$ 00956 W02030  CNIA                                                (TCATMFL )
C$ 00987 W02040  FLIGHT BACKDRIVE                                    (HFLEV   )
C$ 01064 W02050  FLAPS POSITION REQUEST  (deg)                       (W0AFLAP )
C$ 01145 W02060  SHAFT POSITION REQUEST  (%)                         (W0ASHAFT)
C$ 01158 W02070  DETERMINATION OF EXTENSION MOVEMENT                 (W0FEXT  )
C$ 01170 W02080  DETERMINATION OF RETRACTION MOVEMENT                (W0FRET  )
C$ 01189 W02090  MICROSWITCHES                                       (IDAWXXXX)
C$ 01314 W02100  FLAPS REFERENCE TIME                                (AWTIME  )
C$ 01342 ###                                                                ###
C$ 01343 ######################################################################
C$ 01344 #                                                                    #
C$ 01345 #          SECTION 1 :  CONTROL                                      #
C$ 01346 #                                                                    #
C$ 01347 ######################################################################
C$ 01348 ###                                                                ###
C$ 01352 ----------------------------------------------------------------------
C$ 01353 |                             N/A                                    |
C$ 01354 ----------------------------------------------------------------------
C$ 01358 ###                                                                ###
C$ 01359 ######################################################################
C$ 01360 #                                                                    #
C$ 01361 #          SECTION 2 :  LOGIC & INDICATIONS                          #
C$ 01362 #                                                                    #
C$ 01363 ######################################################################
C$ 01364 ###                                                                ###
C$ 01369 ----------------------------------------------------------------------
C$ 01370 |          Section 2.1 :  Relays logic                               |
C$ 01371 ----------------------------------------------------------------------
C$ 01377 W21010  FLAP RELEASE RELAY K1                               (AWRK1   )
C$ 01416 W21020  FLAP POWER CONTROL INDICATION RELAY K2              (AWRK2   )
C$ 01426 W21030  PRESSURE SWITCH STATUS                              (W2SPS   )
C$ 01484 W21040  FLAP POWER CONTROL INDICATION RELAY K3              (AWRK3   )
C$ 01496 W21050  RELAY K4                                            (W2RK4   )
C$ 01521 ----------------------------------------------------------------------
C$ 01522 |          Section 2.2 :  Valves logic                               |
C$ 01523 ----------------------------------------------------------------------
C$ 01532 W22010  FLAPS HYD SOLENOID VALVE CB TRIPS                   (TF27F031)
C$ 01548 W22020  FLAP ISOLATION VALVE POSITION                       (AWVISOL )
C$ 01566 W22030  FLAP SHUTOFF VALVE POSITION                         (AWVSO   )
C$ 01582 W22040  FLAP CONTROL VALVE POSITION                         (AWVCT   )
C$ 01611 ----------------------------------------------------------------------
C$ 01612 |          Section 2.3 :  Lights Logic                               |
C$ 01613 ----------------------------------------------------------------------
C$ 01616 W23010  FLAP POWER LT                                       (AW$PWR  )
C$ 01630 W23020  FLAP DRIVE LT                                       (AW$FA   )
C$ 01655 ###                                                                ###
C$ 01656 ######################################################################
C$ 01657 #                                                                    #
C$ 01658 #          SECTION 3 :  PERFORMANCES                                 #
C$ 01659 #                                                                    #
C$ 01660 ######################################################################
C$ 01661 ###                                                                ###
C$ 01665 ----------------------------------------------------------------------
C$ 01666 |          Section 3.1 :  Time and Rate Factors                      |
C$ 01667 ----------------------------------------------------------------------
C$ 01670 W31010  HYDRAULIC PRESSURE FACTOR  (coeff)                  (W3UHP   )
C$ 01744 W31020  VALVE FACTOR  (coeff)                               (W3UV    )
C$ 01759 W31030  AIRSPEED RATE FACTOR  (coeff)                       (W3UAIRS )
C$ 01862 ----------------------------------------------------------------------
C$ 01863 |          Section 3.2 :  PDU Rate                                   |
C$ 01864 ----------------------------------------------------------------------
C$ 01867 W32010  EXTEND RATE OF MOVEMENT  (%/sec)                    (W3UEX   )
C$ 01986 W32020  RETRACT RATE OF MOVEMENT  (%/sec)                   (W3URE   )
C$ 02088 W32030  PDU RATE OF MOVEMENT  (%/iter)                      (W3UPDU  )
C$ 02195 W32040  PRIMARY SHAFT RATE  (%/iter)                        (W3USHXX )
C$ 02215 W32050  SECONDARY SHAFT RATE  (%/iter)                      (W3USSHX )
C$ 02264 W32060  TORQUE SENSOR                                       (W3FTORX )
C$ 02305 ----------------------------------------------------------------------
C$ 02306 |          Section 3.3 :  PDU position                               |
C$ 02307 ----------------------------------------------------------------------
C$ 02310 W33010  PDU POSITION  (%)                                   (W3APDU  )
C$ 02536 ----------------------------------------------------------------------
C$ 02537 |          Section 3.4 :  FLAPS position                             |
C$ 02538 ----------------------------------------------------------------------
C$ 02541 W34010  FLAPS POSITION  (deg)                               (AWAFXX  )
C$ 02633 W34020  PREVIOUS AVERAGE FLAPS POSITION  (deg)              (W3AFAQ  )
C$ 02644 W34030  AVERAGE FLAP POSITION  (deg)                        (W3AFA   )
C$ 02655 W34040  FLAP POSITION SENSOR LEFT WING  (deg)               (AWAFL   )
C$ 02691 W34050  FLAP POSITION SENSOR RIGHT WING  (deg)              (AWAFR   )
C$ 02726 W34060  AVERAGE FLAPS MOVEMENT  (deg)                       (W3AFM   )
C$ 02737 W34070  FLAPS HYDRAULIC DEMAND  (gal/min)                   (AWWD    )
C$ 02776 W34080  TAKEOFF WARNING HORN SIGNAL                         (AWFTOWH )
C$ 02794 W34090  SIMULATION OF THE TRIGGER                           (W3TRIG  )
C
C
C
C
