#!  /bin/csh -f
#!  $Revision: EXE_LOD - Load an ordinary process V2.0 (MT) Jun-92$
#!
#! &
#! %
#! ^
#!  Version 1.1: <PERSON> (26-May-91)
#!     - added support for files transfering between master/slave IF stations
#!
#!  Version 1.2: <PERSON> (03-Mar-92)
#!     - updated for MOM 4.0
#!
#!  Version 2.0: <PERSON>
#!     - rdist is not executed anymore on a regular host.  This is done to
#!       optimize the load time.
#!
if ("$argv[1]" == "Y") then
  set echo
  set verbose
endif
if ! ("$argv[2]" == "LOAD" || "$argv[2]" == "UNLOAD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_CPU="`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
set SIMEX_DIR = "`logicals -t CAE_SIMEX_PLUS`"
set US = "`/cae/logicals -t machine_usage`"
if ( ("$US" == "HOST_IF") || ("$US" == "IF_MAST") ) then
# 
####  beginning of IF specific part ####
#
   unalias cd
   cd $SIMEX_DIR/work
   unalias rm
   if ( -e  Distfile ) rm  Distfile
   fse_remote $argv[2] NOTIFY $argv[3] $argv[5]
   rdist -q > &/dev/null
   if ( -e  Distfile ) rm  Distfile
#
########  end of IF specific part #######
#
endif
if ("$argv[2]" == "LOAD") then
   fse_operate $argv[2] START_NOTIFY $argv[3]
else
   fse_operate $argv[2] STOP_NOTIFY $argv[3]
endif
if (($status == 0) || ("$argv[2]" == "UNLOAD")) touch $argv[4]
exit
