C
C'Title          Dash 8 Flight Guidance Computer Pitch Axis module
C'Module_id      USD8SP
C'Entry_point    SPITCH
C'Documentation  [tbd]
C'Customer       US Air
C'Author         <PERSON><PERSON><PERSON>/<PERSON><PERSON>'Date           July 1991
C
C'System         Autoflight
C'Itrn           133 msec
C'Process        Synchronous process
C
C'Revision_history
C
C  usd8sp.for.18  8Sep1993 05:55 usd8 W. Pin
C       < Expanded tables for alt. error. Fix for SPR #9015. >
C
C  usd8sp.for.17 16Dec1992 10:14 usd8 m.ward
C       < fix for spr 9016 line sp3010 >
C
C  usd8sp.for.16  8Dec1992 15:34 usd8 M.WARD
C       < FIX FOR SNAG 749 >
C
C  usd8sp.for.15 27Aug1992 01:17 usd8 m.ward
C       < put pitch bars to middle if power off >
C
C  usd8sp.for.14 10Jul1992 22:19 usd8 steve w
C       < trying to reduce bump when engagining >
C
C  usd8sp.for.13  6Jul1992 16:54 usd8 M.WARD
C       < PUT BACK ORIGINAL LOGIC REF SNAG 1309.  SEE CMW AND FIELD
C         MARKERS >
C
C  usd8sp.for.12 24Jun1992 22:28 usd8 sbriere
C       < delete use of alt preselect flag from ADC >
C
C  usd8sp.for.11 24Jun1992 20:36 usd8 SBRIERE
C       < RECODED ELEVATOR SYNC UPON ENGAGEMENT >
C
C  usd8sp.for.10 24Jun1992 19:04 usd8 sbriere
C       < put elevator limit to 20.0 >
C
C  usd8sp.for.9 24Jun1992 14:34 usd8 SBRIERE
C       < put back fd in view when using TCS >
C
C  usd8sp.for.8 16Apr1992 21:25 usd8 SBRIERE
C       < PUT FD BAR OUT OF VIEW WHEN FGC IS INVALID >
C
C  usd8sp.for.7 13Apr1992 03:20 usd8 sbriere
C       < limits on spelev had to be reverse >
C
C  usd8sp.for.6  7Apr1992 23:44 usd8 sbriere
C       < no fd bars when a/p is engaged with no mode >
C
C  usd8sp.for.5  7Apr1992 17:03 usd8 SBRIERE
C       < REPLACE CELVL BY CIECQPOS >
C
C  usd8sp.for.4  1Apr1992 05:22 usd8 SBRIERE
C       < TRY TO REVERSE ELV POS FOR SYNC >
C
C  usd8sp.for.3  1Apr1992 03:10 usd8 sbriere
C       < modify sptchold computation (- sign) >
C
C  usd8sp.for.2 12Mar1992 15:58 usd8 sbriere
C       < boosted pitch wheel tach gain to 3.0 >
C
C  usd8sp.for.1 10Jan1992 12:54 usd8 s.brier
C       < added comment for forport at end of module >
C
C'
C
C'Compilation_directives
C
C    - Compile and add to SHIP library.
C      It must be FPC'd, compiled and put again in SHIP
C      library after each CDB update
C
C'
C
C'Include_files_directives
C
C    DISP.COM     Iteration time and frequency declaration.
C                 No FPC required.
C
C'
C
C'References
C
C      Ref. #1 :   HONEYWELL SPZ-8000 Digital Automatic Flight Control
C                  System,  DeHavilland DHC-8 (serie 100), Maintenance
C                  Manual, chapter  22-14-00, dated June 15/89
C
C      Ref. #2 :   HONEYWELL DASH8-100 control laws,  Drawing no. 5430-95221
C                  Revision B, 10/89
C
C'
C
C'Purpose
C
C    The purpose of this module is to simulate the pitch axis computation
C   of the Sperry SPZ-6000 Flight Guidance Computer.  The logic provides
C   mode engage flags determining which outer loop guidance command will be
C   routed to the inner loop for pitch guidance.  A specified reference is
C   to be held (i.e. altitude, altitude rate, airspeed and radio guidance) and
C   any deviation from this reference will generate an error term.  This
C   error term is generally rate limited and damped by the reference
C   deviation rate or an inertially derived rate.  The resultant command
C   is used by a servo to drive the elevator to its desired position.
C
C'
C
C'Description
C
C              mode indices are as follows:
C
C               pitch mode
C
C              0 - no mode
C              1 - ias
C              2 - v/s
C              3 - alt hld
C              4 - alt cap
C              5 - gs cap
C              6 - gs trk
C              7 - ga
C
C'
C
      SUBROUTINE USD8SP
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 01/10/92 - 13:02 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Include_files
C
      INCLUDE 'disp.com'  !NOFPC
C
C'
C
C'Common_Data_Base
C ----------------
C
CQ    USD8 XRFTEST*
C
C'Common_Data_Base_Variables
C --------------------------
C
CP    USD8
C
C
C  CDB inputs to the AFDS Pitch Axis module
C  ----------------------------------------
C
CPI  &  CIECQPOS , BIAD05,
C
CPI  &  IASPTACC ,
C
CPI  &  JEX010A  , JEX010B  ,
C
CPI  &  RAMLP    ,
C
CPI  &  SLAAPENG , SLALTHLD , SLALTHPB , SLALTARM , SLALTCAP ,
CPI  &  SLAPENG  , SLAPPTRK ,
CPI  &  SLCPLSEL ,
CPI  &  SLDFGCVL , SLFGCVL  , SLD8300  ,
CPI  &  SLGAM    , SLGSARM  , SLGSCAP  , SLGSENG  , SLGSTRK  ,
CPI  &  SLIASM   ,
CPI  &  SLONGRND ,
CPI  &  SLPFDENG , SLPMODE  , SLPTCHLD , SLPWRUP  ,
CPI  &  SLREPSYN ,
CPI  &  SLSPDFLP , SLSPL    , SLSPR    , SLSRVENG ,
CPI  &  SLTCSENG ,
CPI  &  SLVSM    ,
C
CPI  &  SREFIS   ,
C
CPI  &  STRMUP   , STRMDN   ,
CPI  &  STSRVCUR ,
C
CPI  &  SVALT    , SVALTP   ,
CPI  &  SVDGS    , SVDGSV   , SVDYNGPM ,
CPI  &  SVFLAP   ,
CPI  &  SVIAS    ,
CPI  &  SVIASHSI ,
CPI  &  SVLATACF , SVLNGACF ,
CPI  &  SVPITCH  ,
CPI  &  SVRALT   , SVROLL   ,
CPI  &  SVTASFPS , SVTASV   , SVTHTD   ,
CPI  &  SVVDADCV , SVVRALTV , SVVRTACF ,
CPI  &  SVVZD    ,
C
CPI  &  TCFFLPOS ,
CPI  &  TF22071  ,
C
CPI  &  UBZ010A0 , UBZ010B0 ,
C
CPI  &  VCSTHE   , VCSPHI   , VSNTHE   , VSNPHI   ,
C
C
C  CDB outputs from the AFCS Pitch Axis module
C  -------------------------------------------
C
CPO  &  SI$PITC1 , SI$PITC2 ,
C
CPO  &  SPACCLC2 , SPACCLMC , SPACCSEL , SPALTCAP , SPALTCHG ,
CPO  &  SPALTCMD , SPALTDCD , SPALTDOT , SPALTDSY , SPALTEIN ,
CPO  &  SPALTELG , SPALTER  , SPALTERR , SPALTFDB , SPALTINP ,
CPO  &  SPALTKNB , SPALTLIM , SPALTREF , SPALTSEL , SPALTSER ,
CPO  &  SPALTSYN , SPAPPLTC ,
CPO  &  SPCMDRTL ,
CPO  &  SPDDDOT  , SPDEVRAT , SPDGSEST , SPDOTEST , SPDVRTCD ,
CPO  &  SPELEV   , SPESTFLP ,
CPO  &  SPFADE   , SPFDCMD  , SPFDERC  , SPFDINT  , SPFDLGIN ,
CPO  &  SPFDOUT  , SPFLAG   , SPFLAPDN , SPFLAPUP , SPFREZ   ,
CPO  &  SPGABIAS , SPGSCACC , SPGSCMD  , SPGSD    , SPGSDDD  ,
CPO  &  SPGSDEST , SPGSDEV  , SPGSDVD  , SPGSDVER , SPGSDVRT ,
CPO  &  SPGSRTDV , SPGW     ,
CPO  &  SPHDEST  , SPHDOT   , SPHDOTCD , SPHDRWY  , SPHRWY   ,
CPO  &  SPIASC   , SPIASCMD , SPIASD   , SPIASDLG , SPIASDLM ,
CPO  &  SPIASDOT , SPIASEIN , SPIASER  , SPIASFDB , SPIASINT ,
CPO  &  SPIASRT  , SPIASSEL , SPINIT   , SPITCHCD , SPITCHW  ,
CPO  &  SPKALTIN , SPKD     , SPKFADE  , SPKFD    , SPKFDTHT ,
CPO  &  SPKFEEDB , SPKGAM   , SPKGAMMA , SPKGS    , SPKGSOL  ,
CPO  &  SPKOMEG1 , SPKOMEG2 , SPKPOS   , SPKRAD   , SPKSERVO ,
CPO  &  SPKSFLM  , SPKTACHO , SPKTHT   , SPKTRMLM , SPKV     ,
CPO  &  SPKVSRT  ,
CPO  &  SPLFTIN  , SPLFTINT , SPLPTCMD ,
CPO  &  SPMGS    , SPMGSPRG , SPMIDMRK , SPMODLLM , SPMODULM ,
CPO  &  SPNRMACC ,
CPO  &  SPOUTERC , SPOUTMRK ,
CPO  &  SPPOSFBC , SPPOSIN  ,
CPO  &  SPRALT   , SPRALTER , SPRALTLM , SPRATAKR , SPRDANG  ,
CPO  &  SPRDFLAP , SPROPCDL , SPROPCMD , SPRTLDEV , SPRTLIM2 ,
CPO  &  SPRTLIM3 , SPRTLPCD ,
CPO  &  SPSERVIN , SPSERVOC ,
CPO  &  SPTACHO  , SPTASGP  , SPTCHLDC , SPTCHOLD , SPTCHREF ,
CPO  &  SPTCHTWV , SPTCHWCD , SPTCWCHG , SPTHTD   , SPTHTEST ,
CPO  &  SPTHTPLM , SPTHTPRD , SPTHTURN , SPTHTWO  , SPTHTWOP ,
CPO  &  SPTOTPCD , SPTURN   , SPTURNLM ,
CPO  &  SPVERSIN , SPVERSSW , SPVS     , SPVSC    , SPVSCMD  ,
CPO  &  SPVSDOT  , SPVSINT  , SPVSIVV  , SPVSIVVE , SPVSREF  ,
CPO  &  SPVSSEL
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:56:13 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  CIECQPOS       ! CAPT COLUMN EQUIVALENT POSITION        [DEG]
     &, IASPTACC       ! pitch wheel tach input                AI034
     &, SLSPR(10)      ! logic real spares
     &, STRMDN         ! flap mod gained trim down cmd          [sec]
     &, STRMUP         ! flap mod gained trim up cmd            [sec]
     &, STSRVCUR       ! estimated servo current                [amp]
     &, SVALT          ! voted altitude                          [ft]
     &, SVALTP         ! preselected altitude from dadc          [ft]
     &, SVDGS          ! voted glide slope deviation           [dots]
     &, SVDYNGPM       ! voted dadc dyn press (gain prog)  [lb/ft*ft]
     &, SVFLAP         ! quantized flap position                [deg]
     &, SVIAS          ! voted indicated airspeed              [knts]
     &, SVIASHSI       ! unlimited indicated airspeed          [knts]
     &, SVLATACF       ! filtered voted ahrs lat. accel.        [g's]
     &, SVLNGACF       ! filtered voted ahrs long. accel.       [g's]
     &, SVPITCH        ! average ahrs pitch attitude            [deg]
     &, SVRALT         ! radio altitude                          [ft]
     &, SVROLL         ! voted ahrs roll angle                  [deg]
     &, SVTASFPS       ! voted true airspeed                 [ft/sec]
     &, SVTHTD         ! voted ahrs pitch rate              [deg/sec]
     &, SVVRTACF       ! filtered voted ahrs vert. accel.       [g's]
     &, SVVZD          ! vertical speed                      [ft/sec]
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VSNPHI         ! SINE OF VPHI
     &, VSNTHE         ! SINE OF VTHETA
C$
      INTEGER*4
     &  SLCPLSEL(2)    ! couple side select index
     &, SLPMODE(2)     ! pitch mode index
C$
      INTEGER*2
     &  JEX010A        ! DISPLAYED DISCRETES           P
     &, JEX010B        ! DISPLAYED DISCRETES           P
C$
      LOGICAL*1
     &  BIAD05         ! FGC 1                       22 PDAL   DI195B
     &, RAMLP(3,2)     ! MKR LAMPS ON/OFF: 1=IN, 2=MID, 3=OUT
     &, SLAAPENG       ! Any autopilot engaged flag
     &, SLALTARM(2)    ! altitude armed flag
     &, SLALTCAP(2)    ! altitude capture mode engaged
     &, SLALTHLD(2)    ! altitude hold mode engaged
     &, SLALTHPB(2)    ! altitude hold switch depressed (1 IT.+)
     &, SLAPENG(2)     ! autopilot engaged
     &, SLAPPTRK(2)    ! approach mode track flag
     &, SLD8300        ! DASH-8 -300 flag
     &, SLDFGCVL(2)    ! delayed fgc valid flag
     &, SLFGCVL(2)     ! flight guidance computer valid
     &, SLGAM(2)       ! go around mode
     &, SLGSARM(2)     ! glide slope arm flag
     &, SLGSCAP(2)     ! glide slope capture flag
     &, SLGSENG(2)     ! glide slope engaged (cap or trk)
     &, SLGSTRK(2)     ! glide slope track flag
     &, SLIASM(2)      ! IAS mode engaged
     &, SLONGRND       ! A/C on ground flag
     &, SLPFDENG(2)    ! pitch f/d engage flag
     &, SLPTCHLD(2)    ! pitch attitude hold mode
     &, SLPWRUP        ! Power-up test in progress flag
     &, SLREPSYN(2)    ! reposition in progress flag
     &, SLSPDFLP       ! Low airspeed with flaps extended flag
     &, SLSPL(20)      ! logic program logical spares
     &, SLSRVENG(2)    ! servo engage flag (pitch & roll)
     &, SLTCSENG(2)    ! TCS engaged flag
     &, SLVSM(2)       ! vertical speed mode engaged
     &, SREFIS         ! EFIS instaled
     &, SVDGSV         ! voted vertical deviation valid
     &, SVTASV         ! voted true airspeed valid flag
      LOGICAL*1
     &  SVVDADCV       ! voted DADC validity
     &, SVVRALTV       ! voted radio altitude valid flag
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TF22071        ! AUTO ALTITUDE CAPTURE FAIL
     &, UBZ010A0       ! PRESELECT ALT FLAG
     &, UBZ010B0       ! PRESELECT ALT FLAG
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  SI$PITC1       ! F/D pitch command to ADI 1      [Deg] AO109
     &, SI$PITC2       ! F/D pitch command to ADI 2      [Deg] AO173
     &, SPACCLC2       ! accel. limited command #2          [deg/s/s]
     &, SPACCLMC       ! accel. limited pitch command         [deg/s]
     &, SPACCSEL       ! tach acceleration command           [knts/s]
     &, SPALTCMD       ! total outer loop alt. hold cmd         [deg]
     &, SPALTDCD       ! altitude rate command                 [ft/s]
     &, SPALTDOT       ! altitude rate feedback                [ft/s]
     &, SPALTEIN       ! altitude error input                  [ft/s]
     &, SPALTELG       ! altitude error lagged                   [ft]
     &, SPALTER        ! altitude error for rate calc.           [ft]
     &, SPALTERR       ! altitude error term (sel - act)         [ft]
     &, SPALTFDB       ! altitude feedback for rate calc.        [ft]
     &, SPALTINP       ! alt input from tachometer             [ft/s]
     &, SPALTKNB       ! selected alt. ref from tacho            [ft]
     &, SPALTLIM       ! rate limited alt command error          [ft]
     &, SPALTREF       ! selected altitude reference             [ft]
     &, SPALTSEL       ! selected altitude for display           [ft]
     &, SPALTSER       ! altitude select error                   [ft]
     &, SPAPPLTC       ! radio alt. approach limited cmd        [deg]
     &, SPCMDRTL       ! command rate limiter                 [deg/s]
     &, SPDDDOT        ! rate of change of speed              [knt/s]
     &, SPDEVRAT       ! vert. deviation rate estimator        [ft/s]
     &, SPDGSEST       ! estimated vert deviation term           [ft]
     &, SPDOTEST       ! estimated rate of change of spd      [knt/s]
     &, SPDVRTCD       ! deviation rate commanded              [ft/s]
     &, SPELEV         ! elevator servo command                 [deg]
     &, SPESTFLP       ! estimated flap position                [deg]
     &, SPFADE         ! outer loop cmd fader(mode chng)        [deg]
     &, SPFDCMD        ! pitch f/d command to efis              [deg]
     &, SPFDERC        ! flight director error command          [deg]
      REAL*4   
     &  SPFDINT        ! flight director integ. command         [deg]
     &, SPFDLGIN       ! f/d rate command (input to int.)     [deg/s]
     &, SPFDOUT        ! f/d output command                     [deg]
     &, SPGABIAS       ! go around pitch attitude bias          [deg]
     &, SPGSCACC       ! accel. term for g/s damping         [ft/s/s]
     &, SPGSCMD        ! total outer loop glide slope cmd       [deg]
     &, SPGSD          ! runway elevation gained g/s dev         [ft]
     &, SPGSDDD        ! total accel term for g/s damping    [ft/s/s]
     &, SPGSDEST       ! vert. dev. term to mode logic         [dots]
     &, SPGSDEV        ! gain programmed g/s deviation           [ft]
     &, SPGSDVD        ! g/s dev rate to integral feedbck      [ft/s]
     &, SPGSDVER       ! dev error term for g/s rate calc        [ft]
     &, SPGSDVRT       ! g/s dev rate term (2 nd order)        [ft/s]
     &, SPGSRTDV       ! pre gained g/s dev. rate term         [ft/s]
     &, SPGW           ! gross wt approx for tht dot cal [lb-sE2/fE3]
     &, SPHDEST        ! estimated rwy elev rate for g/s       [ft/s]
     &, SPHDOT         ! altitude rate                         [ft/s]
     &, SPHDOTCD       ! commanded alt. rate from table         [fps]
     &, SPHDRWY        ! rate of change of runway height       [ft/s]
     &, SPHRWY         ! runway height estimate                  [ft]
     &, SPIASC         ! indicated airspeed command          [knts/s]
     &, SPIASCMD       ! tas gained IAS outer loop cmd          [deg]
     &, SPIASD         ! ias outer loop rate command         [knts/s]
     &, SPIASDLG       ! ias outer loop rate cmd limit       [knts/s]
     &, SPIASDLM       ! ias outer loop rate cad lag         [knts/s]
     &, SPIASDOT       ! estimated ias rate                  [knts/s]
     &, SPIASEIN       ! ias error input                       [knts]
     &, SPIASER        ! ias error term                        [knts]
     &, SPIASFDB       ! estimated ias feedback term           [knts]
     &, SPIASINT       ! ias integrator                      [knts/s]
     &, SPIASRT        ! second order ias rate calc.         [knts/s]
      REAL*4   
     &  SPIASSEL       ! selected indicated airspeed           [knts]
     &, SPITCHCD       ! pitch proportionnal command            [deg]
     &, SPITCHW        ! pitch wheel inpt from controller     [deg/s]
     &, SPKALTIN       ! gain for alt. input from tacho
     &, SPKD           ! pitch command dynamic gain
     &, SPKFADE        ! fader gain time constant
     &, SPKFD          ! f/d gain on final command
     &, SPKFDTHT       ! f/d gain for ap eng path
     &, SPKFEEDB       ! servo feedback gain
     &, SPKGAM         ! theta w/o rate lim. gain fader
     &, SPKGAMMA       ! gross wt derived tc for thtd cal       [sec]
     &, SPKGS          ! g/s dev. gain for dev. rate est.
     &, SPKGSOL        ! outer loop g/s command gain
     &, SPKOMEG1       ! natural freq. gain for alt rate
     &, SPKOMEG2       ! natural freq. gain for ias dot
     &, SPKPOS         ! position gain for integral path
     &, SPKRAD         ! radio altitude gain for rate lim
     &, SPKSERVO       ! servo loop gain
     &, SPKSFLM        ! Command rate limit gain
     &, SPKTACHO       ! tacho feedback gain
     &, SPKTHT         ! proportional path gain
     &, SPKTRMLM       ! Autotrim compensation term
     &, SPKV           ! ias proportional path rate gain
     &, SPKVSRT        ! vert. speed rate time constant          [hz]
     &, SPLFTIN        ! lift compensation integrator inp     [deg/s]
     &, SPLFTINT       ! lift compensation integrated cmd       [deg]
     &, SPLPTCMD       ! limited pitch servo position cmd       [deg]
     &, SPMGS          ! rate limited g/s gain program
     &, SPMGSPRG       ! glide slope gain program
     &, SPMODLLM       ! mode dependant ptch att low lim        [deg]
     &, SPMODULM       ! mode dependant ptch att up lim         [deg]
      REAL*4   
     &  SPNRMACC       ! normal accel computation            [ft/s/s]
     &, SPOUTERC       ! outer loop pitch command (mode)        [deg]
     &, SPPOSFBC       ! position feedback cmd term             [deg]
     &, SPPOSIN        ! position integrator input            [deg/s]
     &, SPRALT         ! r/a term for g/s gain program           [ft]
     &, SPRALTER       ! radio altitude error rate term        [ft/s]
     &, SPRALTLM       ! rate limited r/a term for gn prg      [ft/s]
     &, SPRATAKR       ! command rate taker                   [deg/s]
     &, SPRDANG        ! predicted theta due to gw/prs          [deg]
     &, SPRDFLAP       ! predicted theta due to flaps           [deg]
     &, SPROPCDL       ! limited inner loop prop. cmd           [deg]
     &, SPROPCMD       ! inner loop prop. cmd                   [deg]
     &, SPRTLDEV       ! rate limited g/s dev cmd              [ft/s]
     &, SPRTLIM2       ! rate limited command #2                [deg]
     &, SPRTLIM3       ! rate limited command #3              [deg/s]
     &, SPRTLPCD       ! rate limited proportionnal cmd         [deg]
     &, SPSERVIN       ! servo integrator input term          [deg/s]
     &, SPSERVOC       ! elevator servo command                 [deg]
     &, SPTACHO        ! elevator servo tacho feedback        [deg/s]
     &, SPTASGP        ! tas gain program                     [1/sec]
     &, SPTCHLDC       ! theta hold predict term                [deg]
     &, SPTCHOLD       ! predicted theta hold term              [deg]
     &, SPTCHREF       ! pitch hold reference                   [deg]
     &, SPTCHTWV       ! voted pitch thumbwheel command       [deg/s]
     &, SPTCHWCD       ! pitch wheel command (tas prog)         [deg]
     &, SPTHTD         ! estimated pitch rate term            [deg/s]
     &, SPTHTEST       ! estimated inertial pitch angle       [deg/s]
     &, SPTHTPLM       ! Processed pitch rate input
     &, SPTHTPRD       ! total predicted theta flap+turn        [deg]
     &, SPTHTURN       ! predicted theta due to bank            [deg]
     &, SPTHTWO        ! final lift compensation tht w/o        [deg]
      REAL*4   
     &  SPTHTWOP       ! lift compensation theta washout        [deg]
     &, SPTOTPCD       ! total prop. inner loop cmd             [deg]
     &, SPTURN         ! roll rate turn term                  [deg/s]
     &, SPTURNLM       ! limited roll rate turn term          [deg/s]
     &, SPVERSIN       ! lift compensation in roll term
     &, SPVS           ! second order vert. speed term         [ft/s]
     &, SPVSC          ! vertical speed command                [ft/s]
     &, SPVSCMD        ! tas gained V/S outer loop cmd          [deg]
     &, SPVSDOT        ! vertical speed rate estimation      [ft/s/s]
     &, SPVSINT        ! vertical speed rate integrator        [ft/s]
     &, SPVSIVV        ! instantaneous vertical velocity       [ft/s]
     &, SPVSIVVE       ! instantaneous vrt velocity err        [ft/s]
     &, SPVSREF        ! vertical speed ref selected           [ft/s]
     &, SPVSSEL        ! displayed vertical speed ref           [fpm]
C$
      LOGICAL*1
     &  SPALTCAP       ! altitude capture flag
     &, SPALTCHG       ! selected altitude change flag
     &, SPALTDSY       ! altitude command synchronization flag
     &, SPALTSYN       ! altitude error synchronization flag
     &, SPFLAG         ! SPITCH        CONSTANT INIT FLAG
     &, SPFLAPDN       ! Flaps in down position
     &, SPFLAPUP       ! Flaps in up position
     &, SPFREZ         ! SPITCH        FREEZE FLAG
     &, SPINIT         ! SPITCH        TIME CONSTANT INIT FLAG
     &, SPMIDMRK       ! middle marker passed flag
     &, SPOUTMRK       ! outer marker passed flag
     &, SPTCWCHG       ! pitch wheel in motion
     &, SPVERSSW       ! versine disable latched switch
C$
      LOGICAL*1
     &  DUM0000001(4992),DUM0000002(6596),DUM0000003(1934)
     &, DUM0000004(4145),DUM0000005(12),DUM0000006(7908)
     &, DUM0000007(6043),DUM0000008(8),DUM0000009(8)
     &, DUM0000010(122),DUM0000011(8),DUM0000012(166)
     &, DUM0000013(4),DUM0000014(20),DUM0000015(12)
     &, DUM0000016(40),DUM0000017(6),DUM0000018(36)
     &, DUM0000019(2),DUM0000020(7),DUM0000021(56)
     &, DUM0000022(2),DUM0000023(4),DUM0000024(4),DUM0000025(12)
     &, DUM0000026(4),DUM0000027(41),DUM0000028(723)
     &, DUM0000029(10),DUM0000030(652),DUM0000031(83)
     &, DUM0000032(4),DUM0000033(4),DUM0000034(132)
     &, DUM0000035(16),DUM0000036(8),DUM0000037(52)
     &, DUM0000038(4),DUM0000039(32),DUM0000040(12)
     &, DUM0000041(8),DUM0000042(20),DUM0000043(64)
     &, DUM0000044(12),DUM0000045(4),DUM0000046(28)
     &, DUM0000047(16),DUM0000048(4),DUM0000049(20)
     &, DUM0000050(47),DUM0000051(48),DUM0000052(6)
     &, DUM0000053(2),DUM0000054(10109),DUM0000055(260975)
     &, DUM0000056(13481),DUM0000057(1956),DUM0000058(275)
     &, DUM0000059(727),DUM0000060(234)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,SI$PITC1,SI$PITC2,DUM0000002,IASPTACC,DUM0000003
     &, BIAD05,DUM0000004,VSNPHI,VCSPHI,DUM0000005,VSNTHE,VCSTHE
     &, DUM0000006,CIECQPOS,DUM0000007,SPFREZ,DUM0000008,SPFLAG
     &, DUM0000009,SPINIT,DUM0000010,SLCPLSEL,SLPMODE,DUM0000011
     &, SLSPR,DUM0000012,SLSPL,SLAAPENG,DUM0000013,SLALTARM,SLALTCAP
     &, SLALTHLD,SLALTHPB,DUM0000014,SLAPENG,DUM0000015,SLAPPTRK
     &, DUM0000016,SLD8300,DUM0000017,SLDFGCVL,DUM0000018,SLFGCVL
     &, SLGAM,DUM0000019,SLGSARM,SLGSCAP,SLGSENG,SLGSTRK,DUM0000020
     &, SLIASM,DUM0000021,SLONGRND,DUM0000022,SLPFDENG,DUM0000023
     &, SLPTCHLD,SLPWRUP,DUM0000024,SLREPSYN,DUM0000025,SLSPDFLP
     &, SLSRVENG,DUM0000026,SLTCSENG,DUM0000027,SLVSM,DUM0000028
     &, SPACCLC2,SPACCLMC,SPACCSEL,SPALTCMD,SPALTDCD,SPALTDOT
     &, SPALTELG,SPALTER,SPALTERR,SPALTFDB,SPALTEIN,SPALTINP
     &, SPALTKNB,SPALTLIM,SPALTREF,SPALTSEL,SPALTSER,SPAPPLTC
     &, SPCMDRTL,SPDDDOT,SPDEVRAT,SPDGSEST,SPDOTEST,SPDVRTCD
     &, SPELEV,SPESTFLP,SPFADE,SPFDCMD,SPFDERC,SPFDINT,SPFDLGIN
     &, SPFDOUT,SPGABIAS,SPGSCACC,SPGSCMD,SPGSD,SPGSDDD,SPGSDEST
     &, SPGSDEV,SPGSDVD,SPGSDVER,SPGSDVRT,SPGSRTDV,SPGW,SPHDEST
     &, SPHDOT,SPHDOTCD,SPHDRWY,SPHRWY,SPIASC,SPIASCMD,SPIASD
     &, SPIASDLG,SPIASDLM,SPIASDOT,SPIASEIN,SPIASER,SPIASFDB
     &, SPIASINT,SPIASRT,SPIASSEL,SPITCHW,SPITCHCD,SPKALTIN,SPKD
     &, SPKFADE,SPKFD,SPKFDTHT,SPKFEEDB,SPKGAM,SPKGAMMA,SPKGS
     &, SPKSFLM,SPKGSOL,SPKOMEG1,SPKOMEG2,SPKPOS,SPKRAD,SPKSERVO
     &, SPKTACHO,SPTHTPLM,SPKTHT,SPKTRMLM,SPKV,SPKVSRT,SPLFTIN
     &, SPLFTINT,SPLPTCMD,SPMGS,SPMGSPRG,SPMODLLM,SPMODULM,SPNRMACC
     &, SPOUTERC,SPPOSFBC,SPPOSIN,SPRALT,SPRALTER,SPRALTLM,SPRATAKR
     &, SPRDANG,SPRDFLAP,SPROPCDL,SPROPCMD,SPRTLDEV,SPRTLIM2
     &, SPRTLIM3,SPRTLPCD,SPSERVIN,SPSERVOC,SPTACHO,SPTASGP,SPTCHLDC
     &, SPTCHOLD,SPTCHREF,SPTCHTWV,SPTCHWCD,SPTOTPCD,SPTHTD,SPTHTEST
     &, SPTHTPRD,SPTHTURN,SPTHTWO,SPTHTWOP,SPTURN,SPTURNLM,SPVERSIN
      COMMON   /XRFTEST   /
     &  SPVS,SPVSC,SPVSCMD,SPVSDOT,SPVSINT,SPVSIVV,SPVSIVVE,SPVSREF
     &, SPVSSEL,DUM0000029,SPALTCAP,SPALTCHG,SPALTDSY,SPALTSYN
     &, SPFLAPDN,SPFLAPUP,SPMIDMRK,SPOUTMRK,SPTCWCHG,SPVERSSW
     &, DUM0000030,SREFIS,DUM0000031,STRMDN,DUM0000032,STRMUP
     &, DUM0000033,STSRVCUR,DUM0000034,SVALT,DUM0000035,SVALTP
     &, DUM0000036,SVDGS,DUM0000037,SVDYNGPM,DUM0000038,SVFLAP
     &, DUM0000039,SVIAS,DUM0000040,SVIASHSI,DUM0000041,SVLATACF
     &, DUM0000042,SVLNGACF,DUM0000043,SVPITCH,DUM0000044,SVRALT
     &, DUM0000045,SVROLL,DUM0000046,SVTASFPS,DUM0000047,SVTHTD
     &, DUM0000048,SVVRTACF,DUM0000049,SVVZD,DUM0000050,SVDGSV
     &, DUM0000051,SVTASV,DUM0000052,SVVDADCV,DUM0000053,SVVRALTV
     &, DUM0000054,RAMLP,DUM0000055,TCFFLPOS,DUM0000056,TF22071
     &, DUM0000057,UBZ010A0,DUM0000058,UBZ010B0,DUM0000059,JEX010A
     &, DUM0000060,JEX010B   
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
C      real variables
C
      REAL*4  ABSALTEL        ! absolute value of SPALTELG
      REAL*4  ALTDCD          ! local alitude rate command
      REAL*4  COSTHE          ! local cosine of pitch angle
      REAL*4  COSPHI          ! local cosine of roll angle
      REAL*4  FLAPER          ! input to flap rate limit
      REAL*4  KALTTACH        ! Altitude TACH gain
      REAL*4  KACC            ! acceleration command gain
      REAL*4  KAVT            ! tas program gain on integral term
      REAL*4  KCASLIM         ! ias command limit
      REAL*4  KDDOTLIM        ! acceleration command limit
      REAL*4  KDELGAM         ! delta gamma term for ias mode rate lim
      REAL*4  KDOTLIM         ! rate command limit
      REAL*4  KELVCDD         ! limit for elevator commanded rate
      REAL*4  KELVLIM         ! elevator position limit
      REAL*4  KFADE1          ! predicted theta w/o fader gain
      REAL*4  KHRWY           ! radio altitude error gain
      REAL*4  KIASWINP        ! ias wheel input gain
      REAL*4  KPITCHW         ! pitch wheel input gain
      REAL*4  KPUT1           ! FD bar position during power up test
      REAL*4  KPUT2           ! FD bar position during power up test
      REAL*4  KPUT3           ! FD bar position during power up test
      REAL*4  KPWTACH         ! PTW tach gain (compensated for calib.)
      REAL*4  KSF             ! low speed and flaps extended gain
C !FM+
C !FM   8-Sep-93 05:37:55 W. Pin
C !FM    < Expanded table to allow alt errors of up to 30 000 ft.  Fix for
C !FM      SPR #9015. >
C !FM
CWP      REAL*4  KSLOPE1(19)     ! slope of table lookup data
      REAL*4  KSLOPE1(48)     ! slope of table lookup data
C !FM-
      REAL*4  KSLOPE3(3)      ! slope of table lookup data
      REAL*4  KTHTCLIM        ! mode dependant command rate limit
      REAL*4  KTRIM           ! autotrim with airspeed compensation gain
      REAL*4  KTRM            ! autotrim pitch inner loop gain
      REAL*4  KVERSINE        ! versine compensation gain
      REAL*4  KVSWINP         ! vertical speed wheel inp gain
      REAL*4  KWSP            ! integ command dynamic pressure gain
      REAL*4  L_ALTRTL        ! rate limit for altitude error
      REAL*4  L_FLAPLL        ! estimated flap ext/ret lower rate limit
      REAL*4  L_FLAPUL        ! estimated flap ext/ret upper rate limit
      REAL*4  L_GSRTLM        ! rate limit for G/S deviation
      REAL*4  L_MGSLIM        ! rate limit for g/s gain prog
      REAL*4  L_PRTLM1        ! rate limit for proportionnal command
      REAL*4  L_PRTLM2        ! rate limit for proportionnal command
      REAL*4  L_RALTLL        ! g/s radio altitude term lower rate limit
      REAL*4  L_RALTUL        ! g/s radio altitude term upper rate limit
      REAL*4  L_RATE1         ! rate limited prop. cmd. rate limit
      REAL*4  L_RATE2         ! rate limit for sprtlim2 term
      REAL*4  L_RATE3         ! rate limit for spacclmc term
      REAL*4  L_RATE4         ! rate limit for theta pred. fader
      REAL*4  L_RATE5         ! rate limit for theta pred. to inner loop
      REAL*4  L_RATE6         ! rate limit for inner loop elev integrater
      REAL*4  PITCHCMD        ! pitch outer loop cmd before rate limit
      REAL*4  PITFDCMD        ! pitch outer loop cmd for flight director
      REAL*4  PTCHULIM(0:7)   ! upper pitch limit
      REAL*4  PTCHLLIM(0:7)   ! lower pitch limit
      REAL*4  P5RTIME         ! half the local iteration time
      REAL*4  RTIME           ! local iteration time
      REAL*4  SINTHE          ! local sine of pitch angle
      REAL*4  SINPHI          ! local sine of roll angle
C !FM+
C !FM   8-Sep-93 05:37:55 W. Pin
C !FM    < Expanded table to allow alt errors of up to 30 000 ft.  Fix for
C !FM      SPR #9015. >
C !FM
CWP      REAL*4  TABLEX1(31)     ! altitude rate computation table
      REAL*4  TABLEX1(48)     ! altitude rate computation table
      REAL*4  TABLEX3(4)      ! go around bias table
      REAL*4  TABLEY1(48)     ! altitude rate computation table
CWP      REAL*4  TABLEY1(31)     ! altitude rate computation table
C !FM-
      REAL*4  TABLEY3(4)      ! go around bias table
      REAL*4  X               ! local scratch pad variable
      REAL*4  Y               ! local scratch pad variable
C
C      Time constants
C
      REAL*4  TAU(7)          ! array of time constants for sync.
      REAL*4  TFREEZE(7)      ! array of time constants for freeze
      REAL*4  TREPOS(7)       ! array of time constants for reposition
      REAL*4  TRUN(7)         ! array of time constants for prog. run
      REAL*4  TAU01           ! altitude command error lag t/c
      REAL*4  TAU02           ! altitude command error lag t/c
      REAL*4  TAU03           ! pseudo pitch rate lag t/c
      REAL*4  TAU04           ! pseudo pitch rate lead t/c
      REAL*4  TAU05           ! ias command lag t/c
      REAL*4  TAU06           ! half iteration time for integ.
      REAL*4  TAU07           ! iteration time for integ.
C
C      Old values
C
      REAL*4  O_ACCSEL        ! old value of pitch wheel accel inp
      REAL*4  O_ALTDCD        ! old value of local alt. rate cmd.
      REAL*4  O_ALTDOT        ! old value of estimated altitude rate
      REAL*4  O_ALTEIN        ! old value of estimated altitude error
      REAL*4  O_ALTERR        ! old value of alt modes alt error
      REAL*4  O_ALTKNB        ! old value of selected altitude (knob)
      REAL*4  O_FDLGIN        ! old value of f/d lag input
      REAL*4  O_GSDDD         ! old value of g/s dev accel term
      REAL*4  O_GSDVD         ! old value of g/s rate dev term
      REAL*4  O_HDRWY         ! old value of rate of runway height
      REAL*4  O_IASDLM        ! old value of limited ias command
      REAL*4  O_IASDOT        ! old value of estimated ias rate
      REAL*4  O_IASEIN        ! old value of estimated ias error
      REAL*4  O_LFTIN         ! old value of lift comp. integ. inp.
      REAL*4  O_POSIN         ! old value of pseudo-position integrator
      REAL*4  O_PTCHWC        ! old value of pitch wheel command
      REAL*4  O_SERVIN        ! old value of servo input
      REAL*4  O_THTEST        ! old value of estimated theta
      REAL*4  O_VSDOT         ! old value of estimated vs rate
C
C      integer variables
C
      INTEGER*2   TEMPI2      ! scratch pad integer
C
      INTEGER*4   ADDRESS /'0C0000'X/
      INTEGER*4   SYSTEM  /'0C'X/
      INTEGER*4   COUNT       ! altitude change counter
      INTEGER*4   CPL         ! local cpl selection
      INTEGER*4   FGC         ! local fgc selection
      INTEGER*4   I           ! scratch pad integer
      INTEGER*4   IX1         ! altitude rate computation table index
      INTEGER*4   IX3         ! go around bias table index
      INTEGER*4   NTIMCNST    ! number of time constants
C
      INTEGER*4   O_PMODE     ! old value of pitch mode index
C
C      logical variables
C
      LOGICAL*1  APENGSS      ! ap engage single shot
      LOGICAL*1  CPL1         ! local cpl side 1 selected
      LOGICAL*1  GSARMSS      ! glide slope arm single shot
      LOGICAL*1  IASMODSS     ! ias mode single shot
      LOGICAL*1  OLDFREZE     ! old state of freeze flag
      LOGICAL*1  OLDREPOS     ! old state of reposition flag
      LOGICAL*1  OLDRUN       ! old state of run flag
      LOGICAL*1  O_AAPENG     ! old value of ap engage flag
      LOGICAL*1  O_GSARM      ! old value of g/s arm flag
      LOGICAL*1  O_IASM       ! old value of ias mode
      LOGICAL*1  PMODESS      ! pitch mode change single shot
      LOGICAL*1  ROLLT3D      ! bank angle less than 3 degrees
      LOGICAL*1  SYNC         ! mode change sync flag
      LOGICAL*1  VADCRALV     ! voted DADC and RALT valid
C
C       equivalences
C
      EQUIVALENCE
     & (TAU01, TAU(1)),
     & (TAU02, TAU(2)),
     & (TAU03, TAU(3)),
     & (TAU04, TAU(4)),
     & (TAU05, TAU(5)),
     & (TAU06, TAU(6)),
     & (TAU07, TAU(7))
C
C
C      data tables
C
C      altitude error lookup table
C
C               altitude error
C
      DATA TABLEX1/100.0,   125.0,   150.0,   175.0,   200.0,
     &             225.0,   250.0,   275.0,   300.0,   350.0,
     &             400.0,   450.0,   500.0,   600.0,   800.0,
     &            1000.0,  1250.0,  1500.0,  2000.0,  2500.0,
     &            3000.0,  4000.0,  5000.0,  6000.0,  7000.0,
     &            8000.0,  9000.0, 10000.0, 11000.0, 12000.0,
C !FM+
C !FM   8-Sep-93 05:37:55 W. Pin
C !FM    < Expanded table to allow alt errors of up to 30 000 ft.  Fix for
C !FM      SPR #9015. >
C !FM
CWP     &           13000.0/
CWP
     &           13000.0,  14000.0, 15000.0, 16000.0, 17000.0,
     &           18000.0,  19000.0, 20000.0, 21000.0, 22000.0,
     &           23000.0,  24000.0, 25000.0, 26000.0, 27000.0,
     &           28000.0,  29000.0, 30000.0/
C !FM-
C
C               altitude rate command
C
      DATA TABLEY1/12.49,   15.38,   17.81,   19.94,   21.87,
     &             23.63,   25.28,   26.82,   28.29,   31.00,
     &             33.50,   35.82,   38.00,   42.03,   49.09,
     &             55.26,   62.12,   68.29,   79.21,   88.79,
     &             97.44,  112.76,  126.23,  138.40,  149.58,
     &            159.98,  169.75,  178.98,  187.76,  196.15,
C !FM+
C !FM   8-Sep-93 05:37:55 W. Pin
C !FM    < Expanded table to allow alt errors of up to 30 000 ft.  Fix for
C !FM      SPR #9015. >
C !FM
CWP     &            204.19/
CWP
     &            204.19,  210.95,  217.71,  224.47,  231.23,
     &            237.99,  246.44,  253.20,  259.96,  263.35,
     &            266.73,  271.80,  276.87,  280.25,  283.63,
     &            285.32,  285.32,  288.70/
C
C      go around bias table
C
      DATA TABLEX3/0.0, 13.0, 15.0, 45.0/
C
      DATA TABLEY3/12.0, 12.0, 10.0, 10.0/
C
C      upper and lower pitch limits
C
C                   -0-   -1-   -2-   -3-   -4-   -5-   -6-   -7-
C
      DATA PTCHULIM/20.0, 20.0, 20.0, 20.0, 20.0, 15.0, 10.0, 20.0/
      DATA PTCHLLIM/20.0, 20.0, 20.0, 20.0, 20.0, 15.0, 10.0, 20.0/
C
C      Time constants reposition table
C
C           lag gain:  1
C           lead gain: 0
C
C                      -1- -2- -3- -4- -5- -6- -7-
C
      DATA   TREPOS   / 1., 1., 1., 0., 1., 0., 0./
C
C
C      Time constants freeze table
C
C           lag gain:  0
C           lead gain: X
C
C                      -1- -2- -3- -4- -5- -6- -7-
C
      DATA   TFREEZE  / 0., 0., 0., 0., 0., 0., 0./
C
C
      ENTRY SPITCH
C
C
C ==============================================================================
C
C                       SECTION 0: INTIALIZATION
C
C ==============================================================================
C
C
C= SP0005
C
C -- module freeze flag                                  CAE          SPFREZ
C    ---------------------------------------------------!------------!----------
C
      CALL SCALIN(ADDRESS,SYSTEM)   ! ????
C
      IF (SPFREZ) RETURN
C
C.el
C
C
C= SP0010
C
C -- first pass initialization                           CAE          SPFLAG
C    ---------------------------------------------------!------------!----------
C
      IF (SPFLAG) THEN
        SPFLAG   = .false.
C
        KACC     = 3.0
        KALTTACH = 22.0
        KAVT     = 0.015
        KDDOTLIM = 3.0
        KDELGAM  = 4.0
        KELVCDD  = 13.0
        KELVLIM  = 30.0
        KIASWINP = -0.25
        KPITCHW  = 0.072
        KPUT1    = 30.0
        KPUT2    = 10.0
        KPUT3    = 5.0
        KPWTACH  = 3.0           ! was 1.6
        KVSWINP  = 0.25
        NTIMCNST = 7
C
        SPKD     = 73.0
        SPKFADE  = 0.1
        SPKFD    = 1.0
        SPKFDTHT = 0.667
        SPKFEEDB = 10.0
        SPKGS    = 0.125
        SPKGSOL  = 1.406
        SPKOMEG1 = 0.2
        SPKOMEG2 = 0.125
        SPKPOS   = 1.0
        SPKRAD   = 0.25
        SPKSERVO = 1.0        ! 2.6625
        SPKTACHO = 0.0
        SPKTHT   = 0.667
        SPKV     = 0.10
        SPKVSRT  = 0.2
C
        SPMODULM = 20.0
        SPMODLLM = 20.0
C
C       calculate slopes from data tables
C
        IX1 = 1
        IX3 = 1
C !FM+
C !FM   8-Sep-93 05:37:55 W. Pin
C !FM    < Expanded table to allow alt errors of up to 30 000 ft.  Fix for
C !FM      SPR #9015. >
C !FM
CWP        DO I = 1,19
        DO I = 1,48
C !FM-
          KSLOPE1(I) = (TABLEY1(I + 1) - TABLEY1(I))/(TABLEX1(I + 1) -
     &                TABLEX1(I))
        ENDDO
C
        DO I = 1,3
          KSLOPE3(I) = (TABLEY3(I + 1) - TABLEY3(I))/(TABLEX3(I + 1) -
     &                  TABLEX3(I))
        ENDDO
C
        SVTASFPS = 150.0
        SVDYNGPM = 10.0
        SVIAS    = 100.0
        SPALTKNB = 100.0
        O_ALTKNB = SPALTKNB
      ENDIF
C
C.el
C
C
C= SP0015
C
C -- time dependant variable initialization              CAE          SPINIT
C    ---------------------------------------------------!------------!----------
C
      IF (SPINIT) THEN
        SPINIT   = .false.
C
        RTIME    = YITIM
        P5RTIME  = 0.5 * RTIME
C
        L_ALTRTL = 3.22 * RTIME
        L_GSRTLM = 3.22 * RTIME
        L_MGSLIM = 0.1 * RTIME
        L_RATE1  = 2.2 * RTIME
        L_RATE3  = 4.5 * RTIME
        L_RATE4  = RTIME / 2.5
        L_RATE5  = 1.5 * RTIME
        L_RATE6  = RTIME / 3.0
C
        TRUN(1)  = RTIME / (1.25 + P5RTIME)
        TRUN(2)  = RTIME / (0.8 + P5RTIME)
        TRUN(3)  = RTIME / (1.0 + P5RTIME)
        TRUN(4)  = 1.0 / RTIME
        TRUN(5)  = RTIME / (2.0 + P5RTIME)
        TRUN(6)  = P5RTIME
        TRUN(7)  = RTIME
C
        RETURN
      ENDIF
C
C.el
C
C
C= SP0020
C
C -- Reposition Time Constants                           CAE          TAU**
C    ---------------------------------------------------!------------!----------
C
      IF (SLREPSYN(1) .and. .not. OLDREPOS) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TREPOS(I)
        ENDDO
        OLDREPOS = .true.
        OLDRUN   = .false.
        OLDFREZE = .false.
      ELSE IF (TCFFLPOS .and. .not.(OLDFREZE .or. SLREPSYN(1))) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TFREEZE(I)
        ENDDO
        OLDREPOS = .false.
        OLDRUN   = .false.
        OLDFREZE = .true.
      ELSE IF (.not.(OLDRUN .or. SLREPSYN(1) .or. TCFFLPOS)) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TRUN(I)
        ENDDO
        OLDREPOS = .false.
        OLDRUN   = .true.
        OLDFREZE = .false.
      ENDIF
C
      CPL  = SLCPLSEL(1)
      CPL1 = CPL .eq. 1
C
      IF (SLAPENG(1)) THEN
        FGC = 1
      ELSEIF (SLAPENG(2)) THEN
        FGC = 2
      ELSE
        FGC =1
      ENDIF
C
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 1: OUTER LOOP COMPUTATIONS
C
C ==============================================================================
C
C
C= SP1010
C
C -- Tas gain programmer                                 CAE          SPTASGP
C    ---------------------------------------------------!------------!----------
C
      IF (SVTASV) THEN
        SPTASGP = 32.2 / SVTASFPS
      ELSE
        SPTASGP = 0.214667
      ENDIF
C
      IF (VCSTHE .eq. 0.0) THEN
        COSTHE = 0.00001
      ELSE
        COSTHE = VCSTHE
      ENDIF
      IF (VCSPHI .eq. 0.0) THEN
        COSPHI = 0.00001
      ELSE
        COSPHI = VCSPHI
      ENDIF
      SINTHE = VSNTHE
      SINPHI = VSNPHI
C
C.el
C
C
C= SP1020
C
C -- Pitch wheel tach input term                         ref 1 p 275  SPITCHW
C    ---------------------------------------------------!------------!----------
C
      SPTCHTWV   = IASPTACC * KPWTACH
      IF (ABS(SPTCHTWV) .lt. 10.0) THEN
        SPITCHW  = 0.0
        SPTCWCHG = .false.
      ELSEIF (ABS(SPTCHTWV) .lt. 100.0) THEN
        SPITCHW  = 0.2 * SPTCHTWV
        SPTCWCHG = .true.
      ELSE
        SPITCHW  = SPTCHTWV
        SPTCWCHG = .true.
      ENDIF
C
C.el
C
C
C     old value for pitch wheel input to all modes
C
      O_ACCSEL = SPACCSEL
C
C
C= SP1030
C
C -- Vertical speed command                              ref 1 p 278  SPVSCMD
C    ---------------------------------------------------!------------!----------
C
C     vertical speed reference computation
C
      IF (SLVSM(1) .and. .not.(SLTCSENG(1) .or. SLREPSYN(1))) THEN
        SPACCSEL = SPITCHW * KVSWINP
        SPVSREF = AMIN1(AMAX1(SPVSREF + (SPACCSEL + O_ACCSEL) *
     &            P5RTIME,-133.333),100.0)
      ELSE
        SPVSREF = AMIN1(AMAX1(SVVZD,-133.333),100.0)
      ENDIF
C
      X = SPVSREF * 60.0
      IF (X .lt. 0.0) THEN
        Y = INT((X - 50.0) * 0.01) * 100.0
      ELSE
        Y = INT((X + 50.0) * 0.01) * 100.0
      ENDIF
      SPVSSEL = Y
C
      O_ALTDCD = ALTDCD
        ALTDCD = SPVSSEL / 60.0
C
      SPALTDCD = SPALTDCD + TAU01 * (0.5 * (ALTDCD + O_ALTDCD)
     &           - SPALTDCD)
C
C     instantaneous vertical velocity calculations
C
      SPNRMACC = ((SVVRTACF * COSPHI - SVLATACF * SINPHI) * COSTHE
     &           + SVLNGACF * SINTHE - 1.0) * 32.2
C
      SPVSIVVE = SVVZD - SPVSIVV
      O_VSDOT  = SPVSDOT
      SPVSDOT  = SPNRMACC + SPVSIVVE * SPKVSRT
      IF (SLREPSYN(1)) THEN
        SPVSIVV = SVVZD
      ELSE
        SPVSIVV = SPVSIVV + (SPVSDOT + O_VSDOT) * TAU06
      ENDIF
C
C     estimated altitude rate calculation
C
      SPALTER  = SVALT - SPALTFDB
      O_ALTEIN = SPALTEIN
      SPALTEIN = SPALTER * SPKOMEG1**2
      IF (SLREPSYN(1)) THEN
        SPVSINT = 0.0
      ELSE
        SPVSINT = SPVSINT + (SPALTEIN + O_ALTEIN) * TAU06
      ENDIF
C
      SPVS = 2.0 * SPKOMEG1 * SPALTER
      SPHDOT = SPVSIVV + SPVSINT
C
      O_ALTDOT = SPALTDOT
      SPALTDOT = SPVS + SPHDOT
      IF (SLREPSYN(1)) THEN
        SPALTFDB = SVALT
      ELSE
        SPALTFDB = SPALTFDB + (SPALTDOT + O_ALTDOT) * TAU06
      ENDIF
C
C     final vertical speed outer loop command
C
      SPVSC = SPALTDCD - SPHDOT
      SPVSCMD = SPVSC * 57.3 / SVTASFPS
C
C.el
C
C
C= SP1040
C
C -- Indicated airspeed command                          ref 1 p 278  SPIASCMD
C    ---------------------------------------------------!------------!----------
C
C     ias reference computation
C
      IF (SLIASM(1) .and. .not.(SLTCSENG(1) .or. SLREPSYN(1))) THEN
        SPACCSEL = SPITCHW * KIASWINP
        SPIASSEL = AMIN1(AMAX1(SPIASSEL + (SPACCSEL + O_ACCSEL)
     &             * P5RTIME,80.0),300.0)
      ELSE
        SPIASSEL = AMIN1(AMAX1(SVIAS,80.0),300.0)
      ENDIF
C
C     estimated ias rate
C
      SPIASER = SVIASHSI - SPIASFDB
      SPIASRT = SPIASER * 2.0 * SPKOMEG2
      SPDDDOT = 32.2 * (SVLNGACF - SINTHE) * SVIAS / SVTASFPS
C
      O_IASEIN = SPIASEIN
      SPIASEIN = SPIASER * SPKOMEG2**2
      IASMODSS = SLIASM(1) .and. .not. O_IASM
      IF (IASMODSS .or. SLREPSYN(1)) THEN
        SPIASINT = - SPDDDOT
      ELSE
        SPIASINT = SPIASINT + (SPIASEIN + O_IASEIN) * TAU06
      ENDIF
C
      SPDOTEST = SPIASINT + SPDDDOT
      O_IASDOT = SPIASDOT
      SPIASDOT = SPDOTEST + SPIASRT
      IF (IASMODSS .or. SLREPSYN(1)) THEN
        SPIASFDB = SVIASHSI
      ELSE
        SPIASFDB = SPIASFDB + (SPIASDOT + O_IASDOT) * TAU06
      ENDIF
C
C     ias outer loop command
C
      SPIASD   = SPKV * (SPIASSEL - SPIASFDB)
      KCASLIM  = (KDELGAM * 32.2 / 57.3) * SVIAS / SVTASFPS
      O_IASDLM = SPIASDLM
      SPIASDLM = AMIN1(AMAX1(SPIASD,-KCASLIM),KCASLIM)
C
      IF (IASMODSS .or. SLTCSENG(1) .or. SLREPSYN(1)) THEN
        SPIASDLG = SPDOTEST
      ELSE
        SPIASDLG = SPIASDLG + TAU05 * (0.5 * (SPIASDLM + O_IASDLM)
     &             - SPIASDLG)
      ENDIF
C
      SPIASC   = SPDOTEST - SPIASDLG
      SPIASCMD = SPIASC * SVTASFPS * 57.3 / (SVIAS * 32.2)
C
C.el
C
C
C= SP1050
C
C -- Altitude hold outer loop command                    ref 1 p 280  SPALTCMD
C    ---------------------------------------------------!------------!----------
C
C     selected altitude computation
C
      O_ALTKNB = SPALTKNB
      IF (CPL1) THEN
        SPALTKNB = AMIN1(AMAX1(0., SVALTP), 30000.)
      ELSE
        SPALTKNB = AMIN1(AMAX1(0., SVALTP), 30000.)
      ENDIF
      X = ABS(SPALTKNB - O_ALTKNB)
      IF (X .gt. 0.0) THEN
        SPALTCHG = .true.
        COUNT = 2
      ELSEIF (COUNT .gt. 0) THEN
        COUNT = COUNT - 1
      ELSE
        SPALTCHG = .false.
      ENDIF
C
      SPALTSEL = SPALTKNB
C
C     calculate altitude reference and error
C
      APENGSS = SLAAPENG .and. .not. O_AAPENG
      IF (SLALTARM(1)) THEN
        SPALTSYN = SPALTCHG .or. .not. SLALTHLD(1)
      ELSE
        SPALTSYN = .false.
      ENDIF
      SPALTDSY = .false.
      IF (SLALTHLD(1) .and. .not.(SLTCSENG(1) .or. SLREPSYN(1))) THEN
        IF (APENGSS .or. SLALTHPB(1)) THEN
          SPALTREF = SVALT
          SPALTDSY = .true.
        ENDIF
      ELSEIF (SLALTARM(1) .or. SLALTCAP(1)) THEN
        SPALTREF = SPALTSEL
      ELSE
        SPALTREF = SVALT
        SPALTDSY = .true.
      ENDIF
C
C     altitude select error
C
      SPALTSER = SVALT - SPALTSEL
C
      IF (SLALTARM(1) .or. SLALTCAP(1) .or. SLALTHLD(1)) THEN
C
C     altitude hold error lag
C
        O_ALTERR = SPALTERR
        SPALTERR = SPALTREF - SVALT
C
        IF (SPALTSYN .or. SPALTDSY) THEN
          SPALTELG = SPALTERR
        ELSEIF (SLALTHLD(1)) THEN
          SPALTELG = SPALTELG + TAU01 * (0.5 * (SPALTERR + O_ALTERR)
     &               - SPALTELG)
        ELSE
          SPALTELG = SPALTELG + TAU02 * (0.5 * (SPALTERR + O_ALTERR)
     &               - SPALTELG)
        ENDIF
C
C     calculate altitude command from table
C
        ABSALTEL = ABS(SPALTELG)
C
        IF (ABSALTEL .lt. 103.8) THEN
          Y = ABSALTEL / 8.0
        ELSE
          DO WHILE (ABSALTEL .gt. TABLEX1(IX1 + 1))
            IX1 = IX1 + 1
          ENDDO
          DO WHILE (ABSALTEL .lt. TABLEX1(IX1))
            IX1 = IX1 - 1
          ENDDO
          Y = AMIN1(200.0, TABLEY1(IX1) + KSLOPE1(IX1) *
     &        (ABSALTEL - TABLEX1(IX1)))
        ENDIF
C
        IF (SPALTELG .lt. 0) THEN
          SPHDOTCD = -Y
        ELSE
          SPHDOTCD = Y
        ENDIF
C
C     altitude capture flag
C
        SPALTCAP = SLALTARM(1) .and. (ABS(SVVZD) .gt. ABS(SPHDOTCD))
     &             .and. .not.(SLREPSYN(1) .or. SPALTCHG .or. TF22071)
C
C     final altitude outer loop command
C
        IF (SPALTSYN) THEN
          SPALTLIM = SPHDOTCD
        ELSEIF (SPALTDSY) THEN
          SPALTLIM = SPHDOT
        ELSE
          SPALTLIM = SPALTLIM + AMIN1(AMAX1(SPHDOTCD - SPALTLIM,
     &               -L_ALTRTL),L_ALTRTL)
        ENDIF
C
        SPALTCMD = (SPALTLIM - SPHDOT) * 57.3 / SVTASFPS
C
      ENDIF
C
C.el
C
C
C= SP1060
C
C -- Glide slope outer loop command                      ref 1 p 284  SPGSCMD
C    ---------------------------------------------------!------------!----------
C
      IF (SREFIS) THEN
        IF (CPL1) THEN
          TEMPI2 = JEX010A
        ELSE
          TEMPI2 = JEX010B
        ENDIF
        SPMIDMRK = (TEMPI2 .and. '0002'X) .ne. 0  ! middle marker passed
        SPOUTMRK = (TEMPI2 .and. '0004'X) .ne. 0  ! outer marker passed
      ELSE
        SPMIDMRK = RAMLP(2,1)
        SPOUTMRK = RAMLP(1,1)
      ENDIF
C
      GSARMSS = SLGSARM(1) .and. .not. O_GSARM
C
      IF (SLGSARM(1) .or. SLGSENG(1)) THEN
C
C     radio altitude term (measured or estimated)
C
        IF (SVVRALTV) THEN
          SPRALT = SVRALT
        ELSEIF (SLGSARM(1) .or. SLGSCAP(1)) THEN
          SPRALT = 1500.0
        ELSEIF (SLGSTRK(1)) THEN
          IF (SPMIDMRK) THEN
            SPRALT = 100.0
          ELSE
            SPRALT = 250.0
          ENDIF
        ENDIF
C
        VADCRALV = SVVDADCV .and. SVVRALTV
C
        IF (VADCRALV) THEN
          KHRWY = 0.125
          IF (SPHRWY .gt. 500.0) THEN
            L_RALTLL = -0.75 * ABS(SPHDOT)
            L_RALTUL = 0.5 * ABS(SPHDOT)
          ELSE
            L_RALTLL = -0.5 * ABS(SPHDOT)
            L_RALTUL = 0.25 * ABS(SPHDOT)
          ENDIF
        ELSE
          KHRWY = 1.0
          IF (SVVDADCV) THEN
            SPHDEST = SVTASFPS * (-3.0 / 57.3)
          ELSE
            SPHDEST = 200.0 * (-3.0 / 57.3)
          ENDIF
          L_RALTLL = SPHDEST
          L_RALTUL = 0.0
        ENDIF
C
        SPRALTER = KHRWY * (SPRALT - SPHRWY)
C
C       limited radio altitude error
C
        SPRALTLM = AMIN1(AMAX1(SPRALTER,L_RALTLL),L_RALTUL)
C
C       altitude rate to runway estimate
C
        O_HDRWY = SPHDRWY
        IF (VADCRALV) THEN
          IF (SVRALT .gt. 1500.0) THEN
            SPVS = 0.0
          ELSE
            SPVS = SPHDOT
          ENDIF
          SPHDRWY = SPVS + SPRALTLM
        ELSE
          SPHDRWY = SPRALTLM
        ENDIF
C
        IF (GSARMSS) THEN
          SPHRWY = SPRALT
        ELSEIF (SPOUTMRK .and. .not. SVVRALTV) THEN
          SPHRWY = 1200.0
        ELSE
          SPHRWY = AMIN1(AMAX1(SPHRWY + (SPHDRWY + O_HDRWY) * TAU06
     &             ,100.0),1600.0)
        ENDIF
C
C       glide slope rate term programmer
C
        IF (SPHRWY .gt. 1500.0) THEN
          SPMGSPRG = 1.0
        ELSEIF (SPHRWY .lt. 1200.0) THEN
          SPMGSPRG = 0.0
        ELSE
          SPMGSPRG = SPHRWY / 300.0 - 4.0
        ENDIF
C
        IF (SLGSTRK(1)) THEN
          SPMGS = SPMGS + AMIN1(AMAX1(SPMGSPRG - SPMGS,-L_MGSLIM),
     &            L_MGSLIM)
        ELSE
          SPMGS = 1.0
        ENDIF
C
C       glide slope dev signal processing
C
        SPGSD = SVDGS * SPHRWY / (8.0 - SVDGS)
        IF (SVDGSV) THEN
          SPGSDEV  = SPGSD
        ELSE
          SPGSDEV = SPDGSEST
        ENDIF
        IF (GSARMSS) THEN
          SPGSDEST = SVDGS
        ENDIF
        SPGSDEST = AMIN1(AMAX1(SPGSDEV * (8.0 - SPGSDEST) / SPHRWY
     &             ,-5.7),5.7)
C
C       glide slope deviation rate calculation
C
        SPGSCACC = (SVLNGACF * (SINTHE + 0.052336) - SVLATACF * SINPHI
     &             + SVVRTACF * (COSPHI - SINTHE * 0.052336) -  1.0)
     &             * 32.2
        SPGSDVER = SPGSDEV - SPDGSEST
        SPGSDVRT = 0.2 * SPGSDVER
        O_GSDDD = SPGSDDD
        SPGSDDD = 0.01 * SPGSDVER - SPGSCACC
C
        IF (SLGSARM(1) .or. SLGSCAP(1)) THEN
          SPGSRTDV = -(SPHDOT + SVTASFPS * (3.0 / 57.3))
        ELSE
          SPGSRTDV = SPGSRTDV + (SPGSDDD + O_GSDDD) * TAU06
        ENDIF
C
        O_GSDVD = SPGSDVD
        SPGSDVD = SPGSRTDV + SPGSDVRT
        IF (GSARMSS .or. (SVDGSV .and. (SLGSARM(1) .or. SLGSCAP(1))))
     &  THEN
          SPDGSEST = SPGSDEV
        ELSE
          SPDGSEST = SPDGSEST + (SPGSDVD + O_GSDVD) * TAU06
        ENDIF
C
        SPDEVRAT = SPGSRTDV * SPMGS + SPGSDVD * (1.0 - SPMGS)
C
C       proportional path computation
C
        IF (GSARMSS) THEN
          SPRTLDEV = SPGSDEV * SPKGS
        ELSE
          SPRTLDEV = SPRTLDEV + AMIN1(AMAX1(SPGSDEV * SPKGS - SPRTLDEV
     &               ,-L_GSRTLM),L_GSRTLM)
        ENDIF
C
C       glide slope final outer loop command
C
        SPDVRTCD = SPDEVRAT + SPRTLDEV
        SPGSCMD = SPDVRTCD * SPKGSOL * 57.3 / SVTASFPS
C
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 2: INNER LOOP COMPUTATIONS
C
C ==============================================================================
C
C
C= SP2010
C
C -- Outer loop commands                                 ref 1 p 286  SPOUTERC
C    ---------------------------------------------------!------------!----------
C
      IF (SLALTHLD(1) .or. SLALTCAP(1)) THEN
        SPOUTERC = SPALTCMD
      ELSEIF (SLIASM(1)) THEN
        SPOUTERC = SPIASCMD
      ELSEIF (SLVSM(1)) THEN
        SPOUTERC = SPVSCMD
      ELSEIF (SLGSENG(1)) THEN
        SPOUTERC = SPGSCMD
      ELSE
        SPOUTERC = 0.0
      ENDIF
C
C.el
C
C
C= SP2020
C
C -- Predicted pitch rate term due to turn               ref 2 sh.7   SPTURN
C    ---------------------------------------------------!------------!----------
C
      ROLLT3D = ABS(SVROLL) .le. 3.0
      SPVERSSW = (ROLLT3D .or. SPVERSSW) .and. SLIASM(1)
C
      IF (SPVERSSW) THEN
        KVERSINE = 1.0
      ELSE
        KVERSINE = AMIN1(AMAX1(1.0 / COSPHI,-48.0),48.0)
      ENDIF
C
      SPTURN = 57.3 * SPTASGP * SINPHI**2 * KVERSINE
C
C.el
C
C
C= SP2030
C
C -- Pitch attitude prediction term                      ref 2 sh.8   SPTHTPRD
C    ---------------------------------------------------!------------!----------
C
C     flap position estimator
C
      IF (SPESTFLP .lt. 15) THEN
        L_FLAPUL = 0.57 * TAU07
        L_FLAPLL = -0.71 * TAU07
      ELSE
        L_FLAPUL = 4.6 * TAU07
        L_FLAPLL = -2.9 * TAU07
      ENDIF
      FLAPER = SVFLAP - SPESTFLP
C
      SPFLAPUP = FLAPER .lt. L_FLAPLL
      SPFLAPDN = FLAPER .gt. L_FLAPUL
C
      SPESTFLP = SPESTFLP + AMIN1(AMAX1(FLAPER,L_FLAPLL),L_FLAPUL)
C
      SPRDFLAP = -0.33 * SPESTFLP
C
C     predicted pitch angle term
C
      SPRDANG = 7.21 / SVDYNGPM   ! 27000 / (585 * 6.4)
C
C     versine term
C
      SPVERSIN = 1.0 / COSPHI
C
      SPTHTURN = SPRDANG * 57.3 * SPVERSIN**2
      SPTCHOLD = SPTHTURN - 57.3 * SPRDANG
C
C     predicted pitch angle term
C
      SPTHTPRD = SPRDFLAP * SPVERSIN + SPTHTURN
C
C.el
C
C
C= SP2040
C
C -- Pitch attitude washout                              ref 2 sh.7   SPTHTWO
C    ---------------------------------------------------!------------!----------
C
      IF (SLGSCAP(1) .or. SLGSTRK(1)) THEN
        SPGW = 0.140
      ELSE
        SPGW = 0.224
      ENDIF
C
      SPKGAMMA = SPGW * SVTASFPS / SVDYNGPM
C
      O_LFTIN = SPLFTIN
      SPLFTIN = SPTHTWOP / SPKGAMMA
C
      IF (APENGSS .or. SLREPSYN(1)) THEN
        SPLFTINT = SVPITCH - SPTHTPRD
      ELSEIF (SLIASM(1) .xor. O_IASM) THEN
        SPLFTINT = SVPITCH - SPTHTPRD - SPTHTWOP
      ELSE
        SPLFTINT = SPLFTINT + (SPLFTIN + O_LFTIN) * TAU06
      ENDIF
C
      SPTHTWOP = SVPITCH - SPTHTPRD - SPLFTINT
C
      IF (SLIASM(1) .and. .not. SLAAPENG) THEN
        KFADE1 = 0.0
      ELSE
        KFADE1 = 1.0
      ENDIF
C
      SPKGAM = SPKGAM + AMIN1(AMAX1(KFADE1 - SPKGAM,-L_RATE4),L_RATE4)
C
      SPTHTWO = SPTHTWOP * SPKGAM
C
C.el
C
C
C= SP2050
C
C -- Pitch limited proportionnal command                 ref 1 p 286  SPROPCMD
C    ---------------------------------------------------!------------!----------
C
      PMODESS = SLPMODE(1) .ne. O_PMODE
      SPKFADE = 300.0 / SVTASFPS
      IF (PMODESS .or. APENGSS) THEN
        SYNC = SLPMODE(1) .ne. 6
        IF (O_PMODE .eq. 0) THEN
          SPFADE = (SPOUTERC + SVPITCH - SPTHTWO) - SPTCHLDC
        ELSEIF (SYNC) THEN
          SPFADE = (SPOUTERC + SVPITCH - SPTHTWO) - SPROPCMD
        ENDIF
        SPMODULM = PTCHULIM(SLPMODE(1))
        SPMODLLM = PTCHLLIM(SLPMODE(1))
      ELSE
        SPFADE = SPFADE - SPFADE * RTIME * SPKFADE
      ENDIF
C
      SPROPCMD = SPOUTERC + SVPITCH - SPTHTWO - SPFADE
C
C.el
C
C
C= SP2060
C
C -- Proportionnal command limiter                       ref 1 p 286  SPROPCDL
C    ---------------------------------------------------!------------!----------
C
      SPROPCDL = AMIN1(AMAX1(SPROPCMD,-SPMODLLM),SPMODULM)
C
C.el
C
C
C= SP2070
C
C -- Pitch hold reference command                        ref 1 p 276  SPTCHLDC
C    ---------------------------------------------------!------------!----------
C
      O_PTCHWC = SPTCHWCD
      IF (SLAAPENG .and. .not. SLREPSYN(1)) THEN
        SPTCHWCD = SPITCHW * KPITCHW * AMIN1(400.0 / SVTASFPS,1.3)
      ELSE
        SPTCHWCD = 0.0
      ENDIF
C
      IF (SLPTCHLD(1) .and. .not. SLTCSENG(1)) THEN
        SPTCHREF = AMIN1(AMAX1(SPTCHREF + (SPTCHWCD + O_PTCHWC) *
     &             TAU06,-20.0),20.0)
      ELSE
        SPTCHREF = SVPITCH - SPTCHOLD
      ENDIF
C
      SPTCHLDC = SPTCHREF + SPTCHOLD
C
C.el
C
C
C= SP2080
C
C -- Rate limited proportional command                   ref 2 sh. 7  SPRTLPCD
C    ---------------------------------------------------!------------!----------
C
      IF (SLPTCHLD(1)) THEN
        KTHTCLIM = (30.0 * 57.3) / SVTASFPS
      ELSEIF (SLGSENG(1)) THEN
        KTHTCLIM = (6.44 * 57.3) / SVTASFPS
      ELSE
        KTHTCLIM = (3.22 * 57.3) / SVTASFPS
      ENDIF
C
      IF (APENGSS) THEN
        L_PRTLM1 = SPTHTD
      ELSE
        L_PRTLM1 = L_PRTLM1 + AMIN1(AMAX1(KTHTCLIM - L_PRTLM1,
     &            -L_RATE1),L_RATE1)
      ENDIF
C
      IF (SLPTCHLD(1)) THEN
        PITCHCMD = SPTCHLDC
      ELSE
        PITCHCMD = SPROPCDL
      ENDIF
C
C !FM+
C !FM   6-Jul-92 16:53:38 M.WARD
C !FM   9-Dec -92 16:53:38 M.WARD
C !FM    < PUT BACK ORIGINAL LOGIC REF SNAG 1309 >
C !FM
      IF (.not. SLTCSENG(1)) THEN
        PITFDCMD = PITCHCMD
      ENDIF
C !FM-
C
      IF (APENGSS) THEN
        SPRTLPCD = SVPITCH
      ELSE
        SPRTLPCD = SPRTLPCD + AMIN1(AMAX1(PITCHCMD - SPRTLPCD,
     &             -L_PRTLM1),L_PRTLM1)
      ENDIF
C
C.el
C
C
C= SP2090
C
C -- Approach pitch limited command                      ref 2 sh.6   SPAPPLTC
C    ---------------------------------------------------!------------!----------
C
      IF (SLAPPTRK(1)) THEN
        L_PRTLM2 = AMIN1(AMAX1(-SVRALT*SPKRAD + 5.0,-15.0),3.0)
      ELSE
        L_PRTLM2 = -15.0
      ENDIF
C
      SPAPPLTC = AMIN1(AMAX1(SPRTLPCD,L_PRTLM2),15.0)
C
C.el
C
C
C= SP2100
C
C -- Rate limited command #2                             ref 2 sh. 6  SPRTLIM2
C    ---------------------------------------------------!------------!----------
C
      L_RATE2 = 32.2 * 57.3 / SVTASFPS
      IF (APENGSS) THEN
        SPRTLIM2 = SVPITCH
      ELSE
        SPRTLIM2 = SPRTLIM2 + AMIN1(AMAX1(SPAPPLTC - SPRTLIM2,
     &             -L_RATE2),L_RATE2)
      ENDIF
C
C.el
C
C
C= SP2110
C
C -- Rate limited command #3                             ref 2 sh. 6  SPRTLIM3
C    ---------------------------------------------------!------------!----------
C
      KDOTLIM = 369.012 / SVTASFPS        ! 6.44 * 57.3
      X = (SPRTLIM2 - SVPITCH) * SPKTHT
      SPRTLIM3 = AMIN1(AMAX1(X,-KDOTLIM),KDOTLIM)
C
C.el
C
C
C= SP2120
C
C -- Acceleration limited command                        CAE          SPACCLMC
C    ---------------------------------------------------!------------!----------
C
      IF (APENGSS) THEN
        SPACCLMC = SPTHTD
      ELSE
        SPACCLMC = SPACCLMC + AMIN1(AMAX1(SPRTLIM3 - SPACCLMC,
     &             -L_RATE3),L_RATE3)
      ENDIF
C
C.el
C
C
C= SP2130
C
C -- Processed pitch rate term                           ref 2 sh. 6  SPTHTD
C    ---------------------------------------------------!------------!----------
C
      SPTURNLM = AMIN1(AMAX1(SPTURN,0.0),25.0)
      SPTHTPLM = SPTHTPLM + AMIN1(AMAX1(SPTHTPRD - SPTHTPLM,
     &           -L_RATE5),L_RATE5)
      O_THTEST = SPTHTEST
      SPTHTEST = SVPITCH - SPTHTPLM + SVTHTD - SPTURNLM
C
      SPTHTD = SPTHTD + TAU03 * (TAU04 * (SPTHTEST - O_THTEST)
     &         - SPTHTD)
C
C.el
C
C
C= SP2140
C
C -- Acceleration limit number 2                         ref 2 sh. 6  SPACCLC2
C    ---------------------------------------------------!------------!----------
C
      X = (SPACCLMC - SPTHTD) * KACC
      SPACCLC2 = AMIN1(AMAX1(X,-KDDOTLIM),KDDOTLIM)
C
C.el
C
C
C= SP2150
C
C -- Total proportionnal command                         ref 1 p 286  SPTOTPCD
C    ---------------------------------------------------!------------!----------
C
      SPTOTPCD = -SPKD * SPACCLC2 / (SVIAS * 1.688)
C
C.el
C
C
C= SP2160
C
C -- Command rate taker                                  ref 1 p 286  SPRATAKR
C    ---------------------------------------------------!------------!----------
C
      KWSP = KAVT * SVTASFPS * (1.0 - 1.2625E-5 * SVALT)
C
      IF (ABS(SPLPTCMD) .eq. 8.0 .or. SLONGRND) THEN
        SPRATAKR = 0.0
      ELSE
        SPRATAKR = SPTOTPCD * KWSP
      ENDIF
C
C.el
C
C
C= SP2170
C
C -- Command rate limiter                                ref 2 sh.6   SPCMDRTL
C    ---------------------------------------------------!------------!----------
C
      IF (SLSPDFLP) THEN
        KSF = 1.5
      ELSE
        KSF = 1.0
      ENDIF
      SPKSFLM = SPKSFLM + AMIN1(AMAX1(KSF - SPKSFLM,-L_RATE6),L_RATE6)
C
      SPCMDRTL = AMIN1(AMAX1(SPRATAKR * SPKSFLM,-KELVCDD),KELVCDD)
C
C.el
C
C
C= SP2175
C
C -- Autotrim compensation term                          ref 2 sh.6   SPKTRMLM
C    ---------------------------------------------------!------------!----------
C
      IF (STSRVCUR .gt. 0.025) THEN
        IF (STRMUP .gt. 0.01) THEN
          KTRM = -0.001957            ! 16 * (30/932) * -0.0038
        ELSEIF (STRMDN .gt. 0.01) THEN
          KTRM = 0.001957
        ELSE
          KTRM = 0.0
        ENDIF
        KTRIM = KTRM * (58.0 - SVIAS * 1.166)   ! 1.688 * -0.6907
      ELSE
        KTRIM = 0.0
      ENDIF
C
      SPKTRMLM = AMIN1(AMAX1(KTRIM,-32.0),32.0)
C
C.el
C
C
C= SP2180
C
C -- Integral position feedback command path             ref 1 p 286  SPPOSFBC
C    ---------------------------------------------------!------------!----------
C
      O_POSIN = SPPOSIN
      SPPOSIN = (SPCMDRTL + SPKTRMLM - SPTACHO) * SPKPOS
C !FM+
C !FM  10-Jul-92 22:18:25 Steve Walkington
C !FM    < Improving tracking by using better scaling (ie none) and zeroing
C !FM      input when engaging >
C !FM
C        SPPOSFBC = - SPTOTPCD - CIECQPOS / SPKFEEDB
C      IF (APENGSS) THEN
      IF (.NOT.(SLSRVENG(FGC))) THEN
        SPPOSFBC = CIECQPOS
        SPACCLMC = 0
        SPTHTD = 0
C !FM-
      ELSE
        SPPOSFBC = SPPOSFBC + (SPPOSIN + O_POSIN) * TAU06
      ENDIF
C
C.el
C
C
C= SP2190
C
C -- Elevator servo command                              ref 1 p 286  SPELEV
C    ---------------------------------------------------!------------!----------
C
      SPITCHCD = SPTOTPCD + SPPOSFBC
      SPLPTCMD = AMIN1(AMAX1(SPITCHCD, -KELVLIM), KELVLIM)
      SPSERVOC = SPLPTCMD * SPKFEEDB
C
      O_SERVIN = SPSERVIN
      SPSERVIN = SPSERVOC - SPELEV * SPKFEEDB
C
      IF (SLSRVENG(FGC)) THEN
        SPELEV = AMIN1(AMAX1(SPELEV + (SPSERVIN + O_SERVIN) * TAU06,
     &           -30.0), 20.0)
      ELSE
        SPELEV = CIECQPOS
      ENDIF
C
C.el
C
C
C= SP2200
C
C -- Tacho feedback term                                 ref 1 p 286  SPTACHO
C    ---------------------------------------------------!------------!----------
C
      IF (SLAAPENG) THEN
        SPTACHO = SPSERVIN
      ELSE
        SPTACHO = 0.0
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 3: FLIGHT DIRECTOR COMPUTATIONS
C
C ==============================================================================
C
C
C= SP3010
C
C -- Flight director command                             ref 2 sh. 7  SPFDCMD
C    ---------------------------------------------------!------------!----------
C
      IF (SLPFDENG(FGC)) THEN
C !FM+
C !FM  16-Dec-92 10:14:03 m.ward
C !FM    < fix for spr 9016 >
C !FM
        IF (SLTCSENG(1)) THEN
          SPFDCMD = 0.0
        ELSE
C !FM-
C !FM+
C !FM   8-Dec-92 15:34:29 M.WARD
C !FM    < FIX FOR SNAG 749 >
C !FM
          IF (SLGAM(1)) THEN
            IF (SLD8300) THEN
              SPGABIAS = 9.0
            ELSE
              X = SPESTFLP
              DO WHILE (X.gt.TABLEX3(IX3 + 1))
                IX3 = IX3 + 1
              ENDDO
              DO WHILE (X.lt.TABLEX3(IX3))
                IX3 = IX3 - 1
              ENDDO
              SPGABIAS = TABLEY3(IX3) + KSLOPE3(IX3)*(X - TABLEX3(IX3))
            ENDIF
C !FM-
              SPFDERC = SPGABIAS - SVPITCH
          ELSE
CMW            SPFDERC = PITFDCMD - SVPITCH
            SPFDERC = PITCHCMD - SVPITCH
          ENDIF
          O_FDLGIN = SPFDLGIN
          IF (SLGSTRK(1)) THEN
            SPFDLGIN = (2.0 * SPFDERC - SPFDINT) * 0.067
          ELSE
            SPFDLGIN = -SPFDINT * 0.067
          ENDIF
          IF (PMODESS) THEN
            SPFDINT = 0.0
          ELSE
            SPFDINT = AMIN1(AMAX1(SPFDINT + (SPFDLGIN + O_FDLGIN) *
     &                TAU06,-2.0),2.0)
          ENDIF
          IF (SLGSENG(1)) THEN
            SPFDOUT = 0.5 * SPFDERC
          ELSE
            SPFDOUT = SPFDERC
          ENDIF
          SPFDCMD = SPKFD * (SPFDOUT + SPFDINT)
        ENDIF      ! endif for dec 16 change
      ELSE
        SPFDCMD = 80.0
        SPFDINT = 0.0
        SPFDLGIN = 0.0
      ENDIF
C
      IF (SLPWRUP) THEN
        IF (SLSPR(1) .le. 3.5) THEN
          SI$PITC1 = 80.0
          SI$PITC2 = 80.0
        ELSEIF (SLSPR(1) .le. 4.0) THEN
          SI$PITC1 = -KPUT1
          SI$PITC2 = -KPUT1
        ELSEIF (SLSPR(1) .le. 4.5) THEN
          SI$PITC1 = -KPUT2
          SI$PITC2 = -KPUT2
        ELSEIF (SLSPR(1) .le. 5.0) THEN
          SI$PITC1 = -KPUT3
          SI$PITC2 = -KPUT3
        ELSEIF (SLSPR(1) .le. 5.5) THEN
          SI$PITC1 = 0.0
          SI$PITC2 = 0.0
        ELSEIF (SLSPR(1) .le. 6.0) THEN
          SI$PITC1 = KPUT3
          SI$PITC2 = KPUT3
        ELSEIF (SLSPR(1) .le. 6.5) THEN
          SI$PITC1 = KPUT2
          SI$PITC2 = KPUT2
        ELSEIF (SLSPR(1) .le. 7.0) THEN
          SI$PITC1 = KPUT1
          SI$PITC2 = KPUT1
        ELSE
          SI$PITC1 = 80.0
          SI$PITC2 = 80.0
        ENDIF
      ELSE
C !FM+
C !FM  27-Aug-92 01:17:21 m.ward
C !FM    < put bars at middle if powered off >
C !FM
        IF (SLSPL(4)) THEN
          SI$PITC1 = 80.0
        ELSEIF (SLDFGCVL(1)) THEN
          SI$PITC1 = SPFDCMD
        ELSE
          SI$PITC1 = 0.0
        ENDIF
C
        IF (SLSPL(5)) THEN
          SI$PITC2 = 80.0
        ELSEIF (SLDFGCVL(2)) THEN
          SI$PITC2 = SPFDCMD
        ELSE
          SI$PITC2 = 0.0
        ENDIF
      ENDIF
C !FM-
C.el
C
C
C ==============================================================================
C
C                       SECTION 4: MISCELLANEOUS COMPUTATIONS
C
C ==============================================================================
C
C
C= SP4010
C
C -- Old values                                          CAE          O_*
C    ---------------------------------------------------!------------!----------
C
      O_AAPENG = SLAAPENG
      O_GSARM  = SLGSARM(1)
      O_IASM   = SLIASM(1)
      O_PMODE  = SLPMODE(1)
C
C.el
C
C
      RETURN
      END
C Comment for forport
