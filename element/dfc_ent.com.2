#!  /bin/csh -f 
#!
#!  $Revision: DFC_ENT - Enter/Extract a DFC source file  Version 1.0 (GOF) 9/1991$
#!
#! &
#! @
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
#
set CAE_DFC_PLUS="`logicals -t cae_dfc_plus`"
#
if ! ("$argv[2]" == "EXTRACT" || "$argv[2]" == "ENTER") exit
set argv[3]="`revl -'$argv[3]'`"
set argv[4]="`revl -'$argv[4]' +`"
#
if ("$argv[2]" == "ENTER") then
#
  set SIMEX_DIR="`logicals -t cae_simex_plus`"
  set SIMEX_WORK="$SIMEX_DIR/work"
  set SIMEX_ENTER="$SIMEX_DIR/enter"
  set FSE_UNIK="`pid`"
#
  set FSE_LINE="`sed -n '1p' $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set FSE_FILE="`revl $FSE_FILE`"
#
  set FSE_FILE="`cvfs '$FSE_FILE'`"
#
  set FSE_SAVE="$SIMEX_ENTER/$FSE_FILE:t"
#  set FSE_SAVE="`revl $FSE_SAVE`"
  set FSE_MAKE="$SIMEX_WORK/dfcp_$FSE_UNIK.tmp"
  set FSE_TYPE="`norev $FSE_FILE`"
  set FSE_TYPE="$FSE_TYPE:e"
#
  if ("$FSE_TYPE" == "dfh") then
    set FSE_ACT="h"
  else if ("$FSE_TYPE" == "dfc") then
    set FSE_ACT="c"
  else if ("$FSE_TYPE" == "dfx") then
    set FSE_ACT="x"
  endif
#
  set EOFL=`sed -n '$=' "$argv[3]"`
#
  touch $FSE_MAKE
#
  set lcount = 2  
  while ($lcount <= $EOFL)
    set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
    set stat=$status
    if ($stat != 0) then
      echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
      exit
    endif
    @ lcount = $lcount + 1
#
    set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
    set FSE_INFO="`fmtime $FSE_FILE | cut -c1-17`"

    echo "$FSE_LINE $FSE_INFO" >>&$FSE_MAKE
#
  end
  
  setenv fse_select "$FSE_MAKE"
  setenv fse_source "$FSE_SAVE"
  setenv fse_target "$argv[4]"
  setenv fse_action "$FSE_ACT"
  $CAE_DFC_PLUS/support/dfc_enter
  rm $FSE_MAKE
endif
#
if ("$argv[2]" == "EXTRACT") then
  setenv fse_source "$argv[3]"
  setenv fse_target "$argv[4]"
  $CAE_DFC_PLUS/support/dfc_extract
endif
#
exit
