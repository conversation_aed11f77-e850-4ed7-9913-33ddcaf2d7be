C'Customer          All IBM based simulator
C'Application       Instructor status pages program
C'Author            <PERSON><PERSON> Bilo<PERSON>
C'Date              Mar 91
C
C'System            Morning Readiness
C'Process           Asynchronous Process
C
C
C'Revision_History
C'
C
C
C'Purpose
C
C    This asynchronous program handles the instructor status
C    pages for the Morning Readiness Utility.
C
C
      SUBROUTINE USD8ISP
C
      IMPLICIT NONE
C
C'Subroutines_called
C
C    CAE_IO_OPEN
C    CAE_IO_READ
C    CAE_IO_CLOSE
C
C'
C'Functions
C
C    CAE_TRNL
C
      INTEGER*4 CAE_TRNL
C
C'Common_data_base_variables
C
C
CP    USD8
CP   &      HMRDATE,HMRTIME,HMCH1BUF,
CP   &      HMBUF,HMELTEXT,HMOPNM,TAPAGE,HMRTEST
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:07:10 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*2
     &  TAPAGE(4)      ! PAGE REQUEST TO SGI
C$
      LOGICAL*1
     &  HMELTEXT       ! DISPLAY MORE TEXT
     &, HMRTEST        ! MORNING READINESS TEST IN PROGRESS
C$
      INTEGER*1
     &  HMBUF(32,16)   ! OPERATOR'S MESSAGE
     &, HMCH1BUF(32,16)! CHARACTER 1 BUFFER
     &, HMOPNM(20)     ! OPERATOR'S NAME
     &, HMRDATE(9)     ! MORNING READINESS DATE
     &, HMRTIME(5)     ! MORNING READINESS TIME
C$
      LOGICAL*1
     &  DUM0000001(24528),DUM0000002(287312)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,HMRDATE,HMRTEST,HMELTEXT,HMRTIME,HMOPNM,HMCH1BUF
     &, HMBUF,DUM0000002,TAPAGE    
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
C
C  Declaration for I/O disc file.
C
      CHARACTER*52  FINAM            ! file name
      CHARACTER*52  FILENAME         ! file name (for i/o)
      CHARACTER*52  LOGN             ! logical name
      CHARACTER*3   TYPE             ! type
 
C
      CHARACTER*9   DAY              ! date
      CHARACTER*20  OPNM             ! operator's name
C
      EQUIVALENCE (HMRDATE(1),DAY)
      EQUIVALENCE (HMOPNM(1),OPNM)
C
      INTEGER*4 BYTNUM              ! number of byte used in record
     &        , I , J               ! loop indices
     &        , ISPMODE             ! page display mode
     &        , ISPCODE             ! error status
     &        , ISP_DCB             ! file DCB
     &        , MODEC               ! mode change
     &        , ISPAGE(2)           ! I/F sim status page number
     &        , ISP_SEL             ! step to execute in ISP
     &        , MAXREC              ! maximum record number
     &        , TOTREC              ! record number containing msg
     &        , LOGL                ! logical name length
     &        , LFINAM              ! file name length
     &        , REVCODE             ! return status of REV_CURR
C
      INTEGER*1    IOBUF(512)       ! i/o buffer
      INTEGER*4    IOBUFI4(128)     ! I*4 access for i/o buffer
      CHARACTER*32 IOBUFC32(16)     ! string 32 access for i/o buffer
C
      EQUIVALENCE  (IOBUF(1)    , IOBUFC32(1))
      EQUIVALENCE  (IOBUF(1)    , IOBUFI4(1) )
      EQUIVALENCE  (IOBUFC32(1) , HMCH1BUF(1,1))
C
      INTEGER*4    BYTE_CNT         ! bytes to be read
     &           , STATUS           ! status flag
     &           , READONLY/5/      ! o_flag value
     &           , REC_NUM          ! 512 byte record number
     &           , REC_SIZE         ! Number of bytes in 1 record
     &           , STRT_POS         ! Offset in bytes from start of record
     &           , FR_STATUS/1/     ! Status of function request
     &           , IO_STATUS        ! Status of actual i/o
C
      LOGICAL*1 ISPINIT /.TRUE./     ! first pass flag
     &        , ISPFIRST             ! first page in view
     &        , ISPFLG               ! status flag
     &        , ISPOPEN              ! status open flag
     &        , SWAP_PAGE            ! swap between the two pages
     &        , WAIT                 ! i/o completion flag
C
C'
C
      ENTRY ISP
C
C  First pass initialization.
C
      IF (ISPINIT) THEN
        ISPINIT   = .FALSE.
        ISP_SEL   = 1
        ISPAGE(1) = 975
        ISPAGE(2) = 976
        ISPCODE   = 1
        ISPFLG    = .TRUE.
        ISPFIRST  = .TRUE.
C
C Get morning readiness directory location
C
        STATUS = CAE_TRNL('cae_test',LFINAM,FINAM,0)
        FINAM(LFINAM+1:LFINAM+11) = '/simerr.log'
        LFINAM = LFINAM + 11
      ENDIF
C
      IF (ISPCODE.EQ.1 .AND. FR_STATUS .EQ. 1) THEN
 
        IF ( (TAPAGE(1). EQ. ISPAGE(1))
     &     .OR.(TAPAGE(1). EQ. ISPAGE(2))
     &     .OR.(TAPAGE(3). EQ. ISPAGE(1))
     &     .OR.(TAPAGE(3). EQ. ISPAGE(2)) ) THEN
C
          GOTO (100,200,300,400) ISP_SEL
        ELSE
          ISPFIRST = .TRUE.
          ISP_SEL  = 1
          MODEC    = 1
          HMELTEXT = .FALSE.
        ENDIF
      ENDIF
C
      GOTO 10000
C
C  Open Morning readiness error log file if I/F page is in view
C
 100  CONTINUE
C
      CALL REV_CURR(FINAM,FILENAME,' ',.FALSE.,1,REVCODE)
      IF (REVCODE .GT. 1) THEN
        ISPCODE = 999
        RETURN
      ELSE
        IF (.NOT.ISPOPEN) THEN
          ISPCODE = 0
          FR_STATUS = 0
          DO WHILE (FILENAME(LFINAM:LFINAM).NE.' ')
            LFINAM = LFINAM + 1
          ENDDO
          FILENAME = FILENAME(1:LFINAM-1)//'\0'
          CALL CAE_IO_OPEN(FR_STATUS,ISPCODE,ISP_DCB
     &      ,512,FILENAME,%VAL(READONLY))
          ISP_SEL = 2
          RETURN
        ENDIF
      ENDIF
C
C  Read SIMERR.LOG header record
C
 200  CONTINUE
C
      ISPCODE = 0
      FR_STATUS = 0
      REC_NUM  = 0
      REC_SIZE = 512
      BYTE_CNT = 512
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS,ISPCODE,%VAL(ISP_DCB)
     &     ,%VAL(REC_SIZE),IOBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
C
      ISP_SEL = 3
      RETURN
C
C Memorize header parameters
C
 300  CONTINUE
C
      DAY  = IOBUFC32(6)(1:9)
      OPNM = IOBUFC32(7)(1:20)
      MAXREC = IOBUFI4(57)
      BYTNUM = IOBUFI4(65)
      TOTREC  = MAXREC - 1       !total number of record with messages
      ISP_SEL = 4
      RETURN
C
 400  CONTINUE
C
      IF (ISPFIRST) THEN
        ISPFIRST = .FALSE.
        ISPMODE  = 2
      ELSE IF (HMELTEXT) THEN
        HMELTEXT = .FALSE.
        IF (TOTREC.GT.1) THEN
          ISPMODE  = 3
        ENDIF
      ENDIF
C
      IF (ISPMODE.NE.0) THEN
        MODEC   = ISPMODE
        ISPMODE = 0
      ENDIF
C
      GOTO (10000,             !all buffer filled
     &      1200,              !display first page
     &      1300) MODEC        !display more text
C
      MODEC = 1
      GOTO 10000
C
C Dipslay first set of morning readiness messages
C
1200  CONTINUE
C
      ISPCODE = 0
      FR_STATUS = 0
      REC_NUM  = REC_NUM + 1
      REC_SIZE = 512
      BYTE_CNT = 512
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS,ISPCODE,%VAL(ISP_DCB)
     &     ,%VAL(REC_SIZE),HMBUF,REC_NUM,BYTE_CNT,STRT_POS)
C
      MODEC = 1
      GOTO 9999
C
C Display next set of morning readiness messages
C
1300  CONTINUE
C
      ISPCODE = 0
      FR_STATUS = 0
      REC_NUM  = REC_NUM + 1
      REC_SIZE = 512
      BYTE_CNT = 512
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(ISP_DCB)
     &     ,%VAL(REC_SIZE),HMBUF,REC_NUM,BYTE_CNT,STRT_POS)
C
      MODEC = 1
      SWAP_PAGE = .TRUE.
      RETURN
9999  CONTINUE
C
C Swap the I/F page if necessary to speed up refresh
C
      IF (SWAP_PAGE) THEN
        SWAP_PAGE = .FALSE.
        IF (TAPAGE(1).EQ.ISPAGE(1)) THEN
          TAPAGE(1) = ISPAGE(2)
        ELSE IF (TAPAGE(1).EQ.ISPAGE(2)) THEN
          TAPAGE(1) = ISPAGE(1)
        ELSE IF (TAPAGE(3).EQ.ISPAGE(1)) THEN
          TAPAGE(3) = ISPAGE(2)
        ELSE IF (TAPAGE(3).EQ.ISPAGE(2)) THEN
          TAPAGE(3) = ISPAGE(1)
        ENDIF
      ENDIF
C
10000 CONTINUE
C
      RETURN
      END
C
