C UNTOUCHABLE STAMP BEGINNING: */
C:999999999998Cv59896Cyuvy4Cu933274u0w8&8&%XOIQ$&4|HGWxI&ULI|HRNVx|8&8&%XOI
C Q$&8??%WOIQ$&8?%XOIQ$&5? :*/
C VERSION IDENTIFICATION 1.1.(clrj).5 */
C UNTOUCHABLE STAMP END. */
C'Title                  Miscellaneous
C'Module_ID              XZ
C'Entry_point            XRGRDTRF
C'System                 IOS
C'SubSystem              Miscellaneous
C'Documentation          Miscellaneous SDD
C'Author                 G.Harroch
C'Date                   Nov. 29, 1993
c'Version                5.0
C
C'Iteration_rate         266 msec
C'Process                Asynchronous (Host)
C'Compilation_directives None
C
C'Description  Driving the IOS Pages defining the route definition
C              for moving models. This module saves the route numbers
C              along with the route description and scenarios which consist
C              of scenario number, route number and model number.
C              The scenarios are saved the scenario creation page while a
C              different page is used for recordings and playbacks.
C
C'Inputs       stdXR.DAT
C'Outputs      stdXR.DAT
C
C'Revision_History
C
C  stdxr.for.37  16Oct2012 10:42 usd8 plemay
C       < brought over from Air India for 2ULD>
C
C  stdxr.for.36  8Nov1997 07:21 dlrj J. Chri
C       < changed data declaration of label GR_MODEL_DESC (BAE146 44->47;
C         RJ 45->49; Fire Truck 46->39) >
C
C  stdxr.for.35 25Apr1996 22:46 clrj SQian
C       < change the ground model as visual changed again >
C
C  stdxr.for.34  3Nov1995 19:32 rjf2 ralph
C       < added ground model descriptions and increased corresp array to
C         135 >
C
C  stdxr.for.33 14Jul1995 10:21 b737 LE
C       < Fixed logic to Extract/Copy/Chmod for data file xxxxxr.dat >
C
C  stdxr.for.32  5Jul1995 21:28 ca74 Le
C       < debug data file path name >
C
C  stdxr.for.31  1May1995 22:24 b737 Le
C       < Change CDB name to SHIP in "CP" statement >
C
C  stdxr.for.30 17Apr1995 03:37 b737 GHarroc
C       < now using the CAE_SHIP logical to find the SHIP directory >
C
C  stdxr.for.29  9Apr1995 10:24 b737 GHarroch
C       < Modified to change mode to be able to write to data file in ship
C         dir >
C
C  b767xr.for.28 29Mar1995 15:35 b737 GHarroch
C       < Modified for Air China 737 Set Up >
C
C  b747xr.for.27 20Jun1994 14:22 as74 Le
C       < Changed cae_shipnam to cae_cdbname >
C
C  b747xr.for.26 14Jun1994 14:10 as74 Le
C       < Temporary declaration YISIM as local label >
C
C  b747xr.for.25  8Jun1994 18:14 as74 Le
C       < install s/w for as74 >
C
C  b777xr.for.24 11May1994 16:49 b777 GHarroc
C       < Added logic to copy dat file to SHIP dir for I/O's >
C
C  b777xr.for.23 28Mar1994 18:22 b777 R.HEIDT
C       < Added visual bypass code when not running on the FFS >
C
C  b777xr.for.22 28Mar1994 14:03 b777 GHarroc
C       < Added fix for communication with visual >
C
C  b777xr.for.21 15Feb1994 13:38 b777 Mary
C       < updated module for boeing 777 >
C
C  c737xr.for.20  4Feb1994 11:48 c737 Samia
C       < update module for china southern >
C
C  hybrxr.for.19  3Dec1993 09:18 cafc GHarroch
C       < Modified to Hybrid Version >
C
C File: /cae1/ship/cvdsxr.for.18
C       Modified by: HLJ
C       Mon Jun  7 17:10:56 1993
C       < stopped reselecting a recordig to fast >
C
C  kl11xr.for.1 17May1993 08:47 KL11 HLJ
C       < removed use of dynamic indexing on klm's page 925 >
C
      SUBROUTINE STDXR
C
      Implicit None
C
      INCLUDE 'stdxr.inc'       !NOFPC
      INCLUDE 'cae_io.inc'       !NOFPC
C
C' Ident
C
      CHARACTER*55 RVL
     &              /
     -  '$Source: stdxr.for.36  8Nov1997 07:21 dlrj J. Chri$'/
C
C CDB labels
C
CP     USD8  XR_ERROR,  XRSELRTE, XRVRTEFL,  XRVRTEDS,  XRENERASE,
CP           XRENVIRT,  XRENSTR,  XRRECORD,  XRRTEXC,   XRTIMREM,
CP           XRENSCEN,  XRENDESC, XRERASE,   XRDESCH,   XREDTVRTD,
CP           XRENSCEXC, XRSCEXC,  XRSCMODE,  XRSCNUM,   XRSCVIRT,
CP           XRACTCHG,  XRENACTS, XRMODACTS, XRMODRTD,  XRMODGO,
CP           XRMODDES,  XRSCENUM, XRSCEDEL,
CP           TAGTVEHI,  TAGTROUT, TCMGTVSS,  TCMGTRREC, TAGTRECR,
CP           TCMGTDELR, TAGTTIMR, TCMGTRSAV, XRPLBAVL,
CP           TAICAO1,   YTSIMTM,  XRPLBRTE,  XRSCEXE,   XRPLBRTD,
CP           XRENSCEXE, XRPLBSS,  XRPLBACTS, XRPLBSCM,  XRPLBMOD,
CP           YSITRCNT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 17:14:49 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.226              
C$
      REAL*4   
     &  TAGTTIMR       ! GND TRAFF REC TIME REMAINING FROM VISUAL
     &, XRTIMREM       ! TIME REMAINING FOR VISUAL ROUTE RECORDING
     &, YTSIMTM        ! SIMULATOR TIME (SEC)
C$
      INTEGER*4
     &  XRPLBSCM       ! SELECTED MODEL NUMBER FOR SCENARIO
     &, XRSCMODE       ! SELECTED MODEL NUMBER FOR SCENARIO
     &, XRSCNUM        ! SCENARIO NUMBER BEEING ENTERED
     &, XRSCVIRT       ! SELECTED VISUAL ROUTE FOR SCENARIO
     &, XRSELRTE       ! SELECTED VISUAL ROUTE BEEING EDITED
     &, XR_ERROR       ! STATUS OF XR PROGRAM
     &, YSITRCNT(20)   ! CPUi Iteration Counts
C$
      INTEGER*2
     &  TAGTRECR       ! GROUND TRAFFIC ROUTE SELECTED
     &, TAGTROUT(10)   ! ROUTE # OF ACTIVATED VEHICULE
     &, TAGTVEHI(10)   ! VEHICULE ACTIVATED NUMBER
     &, XRSCENUM       ! XR SCENARIO NUMBER TO DEL
C$
      LOGICAL*1
     &  TCMGTDELR      ! DELETE SELECTED GT. ROUTE FLAG TO VISUAL
     &, TCMGTRREC      ! GROUND TRAFFIC ROUTE RECORD
     &, TCMGTRSAV      ! SAVE VS ROUTE SELECTED
     &, TCMGTVSS(10)   ! GROUND TRAFFIC START/STOP
     &, XRACTCHG       ! ACTIVATION OF MODEL OCCURED
     &, XRDESCH        ! VISUAL ROUTE DESCRIPTION WAS CHANGED
     &, XRENACTS(10)   ! ENABLE ACTIVATION OF MODELS
     &, XRENDESC       ! ENABLE MODIFICATION OF ROUTE DESCRIPTION
     &, XRENERASE      ! ENABLE ERASE BUTTON FLAG
     &, XRENSCEN       ! ENABLE CREATION OF SCENARIOS BUTTON FLAG
     &, XRENSCEXC      ! ENABLE SCENARIOS EXECUTE BUTTON FLAG
     &, XRENSCEXE(10)  ! ENABLE ERASE BUTTON FLAG
     &, XRENSTR        ! ENABLE START RECORDING BUTTON FLAG
     &, XRENVIRT(5)    ! ENABLE MODIFICATION OF ROUTE DESCRIPTION
     &, XRERASE        ! ERASE BUTTON DEPRESSED FLAG
     &, XRMODACTS(10)  ! MODEL ACTIVATED
     &, XRMODGO(10)    ! START/STOP EXECUTION OF VIS RTE FOR MODEL
     &, XRPLBACTS(10)  ! MODEL ACTIVATED
     &, XRPLBAVL(10)   ! PLAYBACK AVAILABLE
     &, XRPLBRTE(10)   ! VISUAL ROUTE USED FLAG
     &, XRPLBSS(10)    ! START/STOP EXECUTION OF VIS RTE FOR MODEL
     &, XRRECORD       ! VISUAL ROUTE RECORDING IN PROGRESS FLAG
     &, XRRTEXC        ! VISUAL ROUTE EXECUTE BUTTON DEPRESSED FLAG
     &, XRSCEDEL       ! XR SCENARIO DELETION
     &, XRSCEXC        ! SCENARIO EXECUTE BUTTON DEPRESSED FLAG
     &, XRSCEXE(10)    ! SCENARIO EXECUTE BUTTON DEPRESSED FLAG
     &, XRVRTEFL(5)    ! VISUAL ROUTE USED FLAG
C$
      INTEGER*1
     &  TAICAO1(4)     ! DEPARTURE ICAO CODE
     &, XREDTVRTD(30,2)! VISUAL ROUTE DESCRIPTION EDIT
     &, XRMODDES(15,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
     &, XRMODRTD(30,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
     &, XRPLBMOD(15,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
     &, XRPLBRTD(30,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
     &, XRVRTEDS(30,2,5)
C$                     ! VISUAL ROUTE DESCRIPTION
C$
      LOGICAL*1
     &  DUM0000001(24),DUM0000002(104),DUM0000003(310236)
     &, DUM0000004(1),DUM0000005(2),DUM0000006(3),DUM0000007(1)
     &, DUM0000008(1),DUM0000009(2167)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YTSIMTM,DUM0000002,YSITRCNT,DUM0000003,XRMODACTS
     &, XRMODGO,XRMODDES,XRMODRTD,XRPLBSCM,XRVRTEDS,XRPLBRTE
     &, XRVRTEFL,DUM0000004,XRSELRTE,XRRECORD,XRERASE,DUM0000005
     &, XRTIMREM,XRRTEXC,XRACTCHG,XRDESCH,XREDTVRTD,XRENACTS
     &, XRENDESC,XRENERASE,XRENSCEN,XRENSCEXC,XRENSCEXE,XRENSTR
     &, XRENVIRT,XRPLBACTS,XRPLBAVL,XRPLBMOD,XRPLBRTD,XRPLBSS
     &, XRSCEDEL,XRSCENUM,XRSCEXC,XRSCEXE,DUM0000006,XRSCMODE
     &, XRSCNUM,XRSCVIRT,XR_ERROR,TAGTVEHI,TAGTROUT,TCMGTVSS
     &, TCMGTRREC,DUM0000007,TAGTRECR,TCMGTDELR,DUM0000008,TAGTTIMR
     &, TCMGTRSAV,DUM0000009,TAICAO1   
C------------------------------------------------------------------------------
CGH+
C -- Declarations following is for copying file from SIMEX directory
C    to the SHIP directory, where are the I/O's on the file will take place.
C
        INTEGER*4
     .              COPYFNAME_L
     .,             COPYFNAME_S
     .,             SHIPNAME_L
     .,             SHIPNAME_S
     .,             SH_COM_L
     .,             DATAFILE_L
     .,             XR_I
     .,             COUNT
C
       CHARACTER BSLASH*1  /'5C'X/
     .,          PERIOD*1  /'2E'X/
     .,          CMD*3     /'cp '/
     .,          PLACE*20
     .,          SHIPDIR*20
C
      CHARACTER*128 COPYFNAME
     .,             SHIPNAME
     .,             DATAFILE
     .,             SH_COM
C
C -- The following declarations are for the error log file
C
      INTEGER*4 ERR_DESC
     .,         ERR_IOSTAT                ! Open File Return Status
     .,         CHECK_AIRPINDX(MAXAIRPOR) ! I/O Position values for scenarios
     .,         STATUS
     .,         SHIPDIR_L                 ! str len of SHIPDIR
     .,         PLACE_L                   ! str len of PLACE
C
      LOGICAL*1
     .    AIRP_READ /.FALSE./
C
      CHARACTER*50
     .    ERR_FILEN
      CHARACTER*80
     .    EBUFFER
C
        LOGICAL*1
     >           OLD_PLBACTS(10)
C
C     local variables
C
      LOGICAL*1     FIRSTPASS /.TRUE./
     .,             NEW_AIRPORT
      INTEGER*4     PROGSTATE   /1/         ! program state
     .,             OLD_SELRTE  /-1/
     .,             OLD_XRSCNUM /-1/
     .,             ACTIV_SCENA(MAXSCENA)
     .,             PLBK_SCENA(MAXSCENA)
     .,             VH_PLBK_SCENA(MAXSCENA)
     .,             OLD_START_STOP(MAXSCENA)
     .,             OLD_SS(MAXSCENA)
     .,             AIRPORT_IND
     .,             EMPTY_SLOT
     .,             PLBK_SLOT
     .,             VH_PLBK_SLOT
     .,             I
     .,             J
     .,             K, SCEN_NUM
C
      CHARACTER*4   OLD_REFAIRP
     .,             STRING
C
      INTEGER*2     ROUTE_NUM(10)
     .,             VEHIC_NUM(10)
C
C -- FOR FILE MANAGEMENT
C
      INTEGER*4     CAE_TRNL
     .,             FTN_STATUS
     .,             IO_STATUS
     .,             FILE_DESC
     .,             IO_POSITION
     .,             FILENAME_L
     .,             FILENAME_S
     .,             NB_ACTIVATED_MOD
C
      CHARACTER*256 FILENAME
C
      REAL*4    RECLENGTH          ! Recording Length
      PARAMETER (RECLENGTH  = 5.0 * 60.0)              ! was 10 min
C
      REAL*4    RECSTIME           ! Recording Start Time
C
C -- Define some constants
C
      INTEGER*4  MAX_ACTIVE_MOD    ! max active scenarios at a given time
      PARAMETER (MAX_ACTIVE_MOD = 5)
C
C -- Define models description
C
      INTEGER*4  MODDESCLG
      PARAMETER (MODDESCLG = 15)
C
      INTEGER*4 NBLIDESDES
      PARAMETER (NBLIDESDES = 2)
C
      INTEGER*4  MAX_NB_GR_MODELS  ! MAX number of available models
      PARAMETER (MAX_NB_GR_MODELS = 100)
C
      INTEGER*4  NB_GR_MODELS      ! number of available models
      PARAMETER (NB_GR_MODELS = 50)
C
      CHARACTER*(MODDESCLG*NBLIDESDES) GR_MODEL_DESC(NB_GR_MODELS)
C
C                                   1     /   2         3
      DATA GR_MODEL_DESC /' Fire truck                   ',  !#1
     .                    ' Ambulance                    ',  !#2
     .                    ' Trawler                      ',  !#3
     .                    ' Boat                         ',  !#4
     .                    ' B747(-200)                   ',  !#5
     .                    ' Follow Me Truck              ',  !#6
     .                    ' Snowplow                     ',  !#7
     .                    ' B737                         ',  !#8
     .                    ' B767                         ',  !#9
     .                    ' MD-11                        ',  !#10
     .                    ' Moveable Mountain            ',  !#11
     .                    ' SteamTrain                   ',  !#12
     .                    ' Pushback Truck               ',  !#13
     .                    ' Bus                          ',  !#14
     .                    ' Pick Up Truck                ',  !#15
     .                    ' Ferry Boat                   ',  !#16
     .                    ' Helicopter                   ',  !#17
     .                    ' B777                         ',  !#18
     .                    ' A300                         ',  !#19
     .                    ' Fuel Truck                   ',  !#20
     .                    ' Luggage Carrier              ',  !#21
     .                    ' Sea King Helicopter          ',  !#22
     .                    ' Crew Bus                     ',  !#23
     .                    ' B747-400                     ',  !#24
     .                    ' A330                         ',  !#25
     .                    ' Cargo Truck                  ',  !#26
     .                    ' Van                          ',  !#27
     .                    ' Food Truck                   ',  !#28
     .                    ' Ramp Truck                   ',  !#29
     .                    ' Tug                          ',  !#30
     .                    ' Ground Engineer              ',  !#31
     .                    ' Stair Truck                  ',  !#32
     .                    ' B757                         ',  !#33
     .                    ' A340                         ',  !#34
     .                    ' Rjet                         ',  !#35
     .                    ' F100                         ',  !#36
     .                    ' Drill Ship                   ',  !#37
     .                    ' B747-400 CLX                 ',  !#38
     .                    ' Fuel Transport               ',  !#39
     .                    ' Pc7                          ',  !#40
     .                    '                              ',  !#41
     .                    '                              ',  !#42
     .                    '                              ',  !#43
     .                    '                              ',  !#44
     .                    '                              ',  !#45
     .                    '                              ',  !#46
     .                    '                              ',  !#47
     .                    '                              ',  !#48
     .                    '                              ',  !#49
     .                    '                              '/  !#50
C
      INTEGER*4  NB_AIR_MODELS     ! number of available models
      PARAMETER (NB_AIR_MODELS = 10)
C
      CHARACTER*(MODDESCLG*NBLIDESDES) AIR_MODEL_DESC(NB_AIR_MODELS)
C
C                                    1     /   2         3
      DATA AIR_MODEL_DESC /'   Aircraft                   ',
     .                     ' Boeing 747                   ',
     .                     ' Helicopter                   ',
     .                     ' Regional jet                 ',
     .                     '                              ',
     .                     ' Boeing 737                   ',
     .                     ' Boeing 767                   ',
     .                     '                              ',
     .                     '                              ',
     .                     ' Boeing 777                   '/
C
C -- Define some Equivalences to CDB labels to be able to work more easily
C
      CHARACTER*(VISDESCLG) C_XRVRTEDS(NBLIVISDES,MAXVISU)
      CHARACTER*(VISDESCLG) C_XREDTVRTD(NBLIVISDES)
      CHARACTER*(VISDESCLG) C_XRMODRTD(NBLIVISDES,MAXSCENA)
      CHARACTER*(MODDESCLG*NBLIDESDES) C_XRMODDES(MAXSCENA)
      CHARACTER*(VISDESCLG) C_XRPLBRTD(NBLIVISDES,MAXSCENA)
      CHARACTER*(MODDESCLG*NBLIDESDES) C_XRPLBMOD(MAXSCENA)
      CHARACTER*4   C_TAICAO1
C
      EQUIVALENCE( C_XRVRTEDS(1,1), XRVRTEDS(1,1,1))
      EQUIVALENCE( C_XREDTVRTD(1), XREDTVRTD(1,1))
      EQUIVALENCE( C_XRMODRTD(1,1), XRMODRTD(1,1,1))
      EQUIVALENCE( C_XRMODDES(1), XRMODDES(1,1,1))
      EQUIVALENCE( C_XRPLBRTD(1,1), XRPLBRTD(1,1,1))
      EQUIVALENCE( C_XRPLBMOD(1), XRPLBMOD(1,1,1))
      EQUIVALENCE( C_TAICAO1, TAICAO1(1))
C
C -- DATA strings
C
      CHARACTER*(VISDESCLG) ERNOSPSTR
C
      DATA ERNOSPSTR /'ERROR : NO MORE AIRPORT SPACE '/
C
C=========================================================================
C Start of program
C=========================================================================
C
       ENTRY XRGRDTRF
C
       if ( YSITRCNT(1) .LE. 800) then
C
C        Bypass visual related code when not on full flight (will crash ap0c0)
C        -----------------------------------------------------------------
         return
       endif
C
       IF ( XR_ERROR .NE. 0) RETURN          ! if an unrecoverable error
C                                            ! occured freeze program
       IF ( FIRSTPASS ) THEN
C
         GOTO ( 1, 2, 3, 4, 5, 6) PROGSTATE     ! do first pass
C
       ELSE
          IF ( IO_STATUS .EQ. IN_PROGRESS ) GOTO 999
          IF ((OLD_REFAIRP .NE. C_TAICAO1).AND.! If reference airport has
     .        (PROGSTATE .NE. 12) .AND.        ! changed and no IO is pending
     .        (PROGSTATE .LE. 14)) THEN        ! and we are not recording,
            PROGSTATE = 11                     ! read the airport scenario
            OLD_REFAIRP =  C_TAICAO1
          ENDIF
C
C -- do required processing
C
          GOTO ( 11, 12, 13, 14, 15, 16, 17) PROGSTATE - 10
C
       ENDIF
C
       XR_ERROR = 1                            ! we should never get here
       GOTO 999                                ! since we'd be in an invalid
C                                              ! program state
C=========================================================================
C   F I R S T  P A S S   ( S T A T E  1 )
C   Init flags, Get path & file name for veh.& scenario database and
C   launch open of database
C=========================================================================
1       CONTINUE
        CALL DISABLE_INPUT        ! Disable input flags while we read
C
C
C --- Determine the ship directory where the xr.dat file will be copied
C --- to for recording purpose.
C
          STATUS = CAE_TRNL("CAE_SHIP",SHIPDIR_L,SHIPDIR,0)
          IF ( STATUS .NE. SUCCESS ) THEN
               WRITE(*,*) ' ERR/XR: could not translate CAE_SHIP:'
     .,                    STATUS
               XR_ERROR  = STATUS ! freeze program
          ELSE
               PLACE_L = SHIPDIR_L+1
               PLACE(1:PLACE_L) = SHIPDIR(1:SHIPDIR_L)//'/'
               PLACE(PLACE_L+1:PLACE_L+1) = '\0'
C
               ERR_FILEN(1:SHIPDIR_L+12) = SHIPDIR(1:SHIPDIR_L)
     .         //'/md11xr.log\0'
          ENDIF
C
          SHIPNAME_S = CAE_TRNL("CAE_CDBNAME",SHIPNAME_L,SHIPNAME,0)
           IF ( SHIPNAME_S .NE. SUCCESS ) THEN            ! 3
             WRITE(*,*) ' ERR/XR: could not translate SHIP NAME :'
     .,                 SHIPNAME_S
             XR_ERROR  = SHIPNAME_S ! freeze program
           ENDIF                                          ! 3
C
           COPYFNAME_S = CAE_TRNL("XR_DAT",COPYFNAME_L,COPYFNAME,0)
           IF ( COPYFNAME_S .NE. SUCCESS ) THEN           ! 4
             WRITE(*,*) ' ERR/XR: could not translate data file path :'
     .,                  COPYFNAME_S
             XR_ERROR  = COPYFNAME_S ! freeze program
           ELSE
             DO XR_I = 1, COPYFNAME_L
               IF ( COPYFNAME(XR_I:XR_I+SHIPNAME_L-1) .EQ.
     >                         SHIPNAME(1:SHIPNAME_L)) THEN
                 DATAFILE   = COPYFNAME(XR_I:COPYFNAME_L)
                 DATAFILE_L = COPYFNAME_L - XR_I + 1
                 GOTO 1010
               ENDIF
             ENDDO
1010         SH_COM_L = COPYFNAME_L + DATAFILE_L + PLACE_L + 5
             SH_COM(1:SH_COM_L) =
     >             BSLASH(1:1)//CMD(1:3)//
     >             COPYFNAME(1:COPYFNAME_L)//' '//
     >             PLACE(1:PLACE_L)//DATAFILE(1:DATAFILE_L)
             CALL SYSTEM( SH_COM )
           ENDIF
C
         PROGSTATE = 5
         GOTO 999
C
C---------------------------------------------------------------------
C SUB BANDING TO MAKE SURE THAT THE SHELL COMMAND HAS COMPLETED
C---------------------------------------------------------------------
5      <USER>
       <GROUP> = COUNT + 1
       IF ( COUNT .GT. 10 ) THEN
         PROGSTATE = 6
       ENDIF
       GOTO 999
C---------------------------------------------------------------------
C
C -- Get path & ...
C
6         CONTINUE
C
             DO XR_I = 1, COPYFNAME_L
               IF ( COPYFNAME(XR_I:XR_I+SHIPNAME_L-1) .EQ.
     >                         SHIPNAME(1:SHIPNAME_L)) THEN
                 FILENAME(1:PLACE_L+(COPYFNAME_L-XR_I+1)) =
     >              place(1:PLACE_L)//copyfname(xr_i:copyfname_l)
               ENDIF
             ENDDO
             IO_STATUS = IN_PROGRESS
             CALL CAE_IO_OPEN (FTN_STATUS
     .,                        IO_STATUS
     .,                        FILE_DESC
     .,                        %VAL(0)
     .,                        FILENAME
     .,                        %VAL(7))
C
             IF (FTN_STATUS .NE. SUCCESS)  THEN
               WRITE(*,*) ' ERR/XR: open file request failed :'
     .,                    FTN_STATUS
               XR_ERROR  = FTN_STATUS ! freeze program
C
             ELSE
               PROGSTATE = 2
             ENDIF
C
       GOTO 999
C
C=========================================================================
C   F I R S T  P A S S  ( S T A T E  2 )
C   Wait for open route definition file completion then launch read of
C   header of route definition file
C=========================================================================
2       CONTINUE
C
       IF (IO_STATUS .NE. IN_PROGRESS) THEN
C
         IF (IO_STATUS .NE. SUCCESS) THEN
           WRITE(*,*) ' ERR/XR: open file failed :'
     .,                IO_STATUS
           XR_ERROR  = IO_STATUS ! freeze program
C
         ELSE
           IO_STATUS = IN_PROGRESS
           IO_POSITION = 0
           CALL CAE_IO_READ ( FTN_STATUS,
     .                        IO_STATUS,
     .                        %VAL(FILE_DESC),
     .                        %VAL(0),
     .                        HEADER,
     .                        -1,
     .                        HEADERLEN,
     .                        IO_POSITION)
C
          IF (FTN_STATUS .NE. SUCCESS)  THEN
             WRITE(*,*) ' ERR/XR: read header from file request '
     .,                 'failed :', FTN_STATUS
             XR_ERROR  = FTN_STATUS ! freeze program
C
          ELSE
             PROGSTATE = 3
           ENDIF
         ENDIF
       ENDIF
C
       GOTO 999
C
C=========================================================================
C   F I R S T  P A S S ( S T A T E  3 )
C   Wait for read of header of route definition file then launch read of
C   airport index in route definition file
C=========================================================================
3      CONTINUE
C
       IF (IO_STATUS .NE. IN_PROGRESS) THEN
C
         IF (IO_STATUS .NE. SUCCESS) THEN
           WRITE(*,*) ' ERR/XR: Read of file header failed :'
     .,               IO_STATUS
           XR_ERROR  = IO_STATUS ! freeze program
C
C -- Validate File Header
C
         ELSE
           IF ((HEADER(HEADLENIND) .NE. HEADERLEN) .OR.
     .         (HEADER(FILETYPIND) .NE. FILETYPE)  .OR.
     .         (HEADER(APINDXLIND) .NE. AIRINDLEN) .OR.
     .         (HEADER(MAXAIRIND)  .NE. MAXAIRPOR) .OR.
     .         (HEADER(MAXVISIND)  .NE. MAXVISU)   .OR.
     .         (HEADER(MAXSCEIND)  .NE. MAXSCENA)  .OR.
     .         (HEADER(AIRPDLGIND) .NE. AIRPBFLG)) THEN
C
               WRITE(*,*) ' ERR/XR: Invalid route/scenario database'
               XR_ERROR  = 987     ! freeze program
C
           ELSE
             IO_STATUS = IN_PROGRESS
             IO_POSITION = HEADERLEN
             CALL CAE_IO_READ ( FTN_STATUS,
     .                          IO_STATUS,
     .                          %VAL(FILE_DESC),
     .                          %VAL(0),
     .                          AIRPINDEX,
     .                          -1,
     .                          AIRINDLEN,
     .                          IO_POSITION)
C
             IF (FTN_STATUS .NE. SUCCESS)  THEN
               WRITE(*,*) ' ERR/XR: index read request failed :'
     .,                    FTN_STATUS
               XR_ERROR  = FTN_STATUS ! freeze program
C
             ELSE
               PROGSTATE = 4
             ENDIF
           ENDIF
         ENDIF
       ENDIF
C
       GOTO 999
C
C=========================================================================
C   F I R S T  P A S S  ( S T A T E  4 )
C   Wait for read of airport index in route definition file
C=========================================================================
4      CONTINUE
C
       IF (IO_STATUS .NE. IN_PROGRESS) THEN
C
         IF (IO_STATUS .NE. SUCCESS) THEN
           WRITE(*,*) ' ERR/XR: Index read failed :'
     .,                IO_STATUS
           XR_ERROR  = IO_STATUS ! freeze program
C
         ELSE
           FIRSTPASS    = .FALSE.      ! first pass done
           OLD_REFAIRP(1:4)  = ' '     ! cause a refresh for airport
           DO XR_I = 1, MAXAIRPOR
              CHECK_AIRPINDX(XR_I) = HEADERLEN +
     >                               AIRINDLEN +
     >                              ((XR_I-1) * AIRPBFLG)
           ENDDO
           PROGSTATE = 11
         ENDIF
       ENDIF
C
       GOTO 999
C
C=========================================================================
C   R E G.  P R. ( S T A T E  1 1 )
C   New reference airport --> get scenario info for this airport
C=========================================================================
11     CONTINUE
C
C -- Init For New Airport
C
      XRSELRTE = 0
      NB_ACTIVATED_MOD = 0
      TCMGTRREC = .FALSE.             ! Send Command to Visual
      TAGTRECR  = 0
      DO I = 1, MAXSCENA
        TAGTVEHI(ACTIV_SCENA(I)) = 0
        TAGTROUT(ACTIV_SCENA(I)) = 0
        TCMGTVSS(ACTIV_SCENA(I)) = .FALSE.
        ACTIV_SCENA(I) = 0
        OLD_START_STOP(I) = 0
      ENDDO
C
C -- Find the airport in the index
C
       AIRPORT_IND = 1
       DO WHILE ((AIRPORT_IND .LE. HEADER(NBAIRPIND)) .AND.
     .           (C_TAICAO1 .NE. C_AIRPINDEX(AIRPORTID,AIRPORT_IND)))
         AIRPORT_IND = AIRPORT_IND + 1
       ENDDO
C
       IF (AIRPORT_IND .GT. HEADER(NBAIRPIND)) THEN
C
         IF (AIRPORT_IND .GT. HEADER(MAXAIRIND)) THEN
C
C  -- New airport but no free space for it : stop processing until
C     another one is selected and write error on console.
C
           WRITE(*,*) ' ERR/XR: Maximum number of airports reached'
           DO I = 1, MAXVISU
             C_XRVRTEDS(1,I) = ERNOSPSTR
             DO J = 2, NBLIVISDES
               C_XRVRTEDS(J,I)(1:VISDESCLG) = ' '
             ENDDO
           ENDDO
C
           DO J = 1, NBLIVISDES
              C_XREDTVRTD(J)(1:VISDESCLG) = ' '
           ENDDO
C
           DO I = 1, MAXSCENA
             XRMODGO(I)   = .FALSE.
             XRMODACTS(I) = .FALSE.
             XRENACTS(I)  = .FALSE.
             DO J = 1, NBLIVISDES
               C_XRMODRTD(J,I)(1:VISDESCLG) = ' '
             ENDDO
             C_XRMODDES(I) = ' '
           ENDDO
C
           CALL DISABLE_INPUT     ! Disable input flags while in error state
C
           PROGSTATE = 13
C
         ELSE                     ! New airport : prepare buffers and set flags
           NEW_AIRPORT = .TRUE.
           DO I = 1, MAXVISU
             XRVRTEFL(I)     = .TRUE.
             VISRTFREE(I)    = .TRUE.
             DO J = 1, NBLIVISDES
               C_XRVRTEDS(J,I)(1:VISDESCLG) = ' '
               VISRTDES(J,I)(1:VISDESCLG)   = ' '
             ENDDO
           ENDDO
C
           DO J = 1, NBLIVISDES
              C_XREDTVRTD(J)(1:VISDESCLG) = ' '
           ENDDO
C
           DO I = 1, MAXSCENA
             XRMODGO(I)   = .FALSE.
             XRMODACTS(I) = .FALSE.
             XRENACTS(I)  = .FALSE.
             XRPLBSS(I)   = .FALSE.
             XRPLBACTS(I) = .FALSE.
             DO J = 1, NBLIVISDES
               C_XRMODRTD(J,I)(1:VISDESCLG) = ' '
               C_XRPLBRTD(J,I)(1:VISDESCLG) = ' '
             ENDDO
             C_XRMODDES(I) = ' '
             C_XRPLBMOD(I) = ' '
           ENDDO
C
           PROGSTATE = 14
C
           OLD_XRSCNUM = -1              ! reset transition detectors
           OLD_SELRTE = -1
           CALL RESET_VR_INPUT           ! Reset visual route input
           CALL RESET_SC_INPUT           ! Reset scenario creation input
         ENDIF
C
C -- Found the airport : request the read of its info
C
       ELSE
         IO_STATUS = IN_PROGRESS
         IO_POSITION = AIRPINDEX(AIRPORTRTE, AIRPORT_IND)
         CALL CAE_IO_READ ( FTN_STATUS,
     .                      IO_STATUS,
     .                      %VAL(FILE_DESC),
     .                      %VAL(0),
     .                      AIRPOBUF,
     .                      -1,
     .                      AIRPBFLG,
     .                      IO_POSITION)
C
         IF (FTN_STATUS .NE. SUCCESS)  THEN
           WRITE(*,*) ' ERR/XR: Airport info read '
     .              , 'request failed :', FTN_STATUS
           XR_ERROR  = FTN_STATUS ! freeze program
C
         ELSE
           PROGSTATE   = 12
           NEW_AIRPORT = .FALSE.
C
           CALL DISABLE_INPUT          ! Disable input while we read
         ENDIF
       ENDIF
C
       GOTO 999
C
C=========================================================================
C   R E G. P R. ( S T A T E  1 2 )
C   Wait for end of read/write of the airport info then prepare the data
C   for the page.
C=========================================================================
12     CONTINUE
C
       IF (IO_STATUS .NE. IN_PROGRESS) THEN
C
         IF (IO_STATUS .NE. SUCCESS) THEN
           WRITE(*,*) ' ERR/XR: Read/write of airport info failed :'
     .,               IO_STATUS
           XR_ERROR  = IO_STATUS ! freeze program
C
         ELSE
C
C -- Check Data that was just read in to make sure it is valid.
C
           DO I = 1, MAXSCENA
            IF ( SCENARIOS(SCEVISRTOF,I) .GT. MAXSCENA ) THEN
C
C -- Error in data found, Write to Error Logger.
C
                  WRITE(*,100) I,SCENARIOS(SCEVISRTOF,I)
     .,                              SCENARIOS(SCMODELID,I)
C
C -- Reset information to avoid any residual problems.
C
                  SCENARIOS(SCEVISRTOF,I) = 0
                  SCENARIOS(SCMODELID,I)  = 0
C
            ENDIF
           ENDDO
C
           DO I = 1, MAXVISU
             XRVRTEFL(I) = VISRTFREE(I)
             DO J = 1, NBLIVISDES
               C_XRVRTEDS(J,I)(1:VISDESCLG) = VISRTDES(J,I)
             ENDDO
           ENDDO
C
           DO J = 1, NBLIVISDES
              C_XREDTVRTD(J)(1:VISDESCLG) = ' '
           ENDDO
C
           DO I = 1, MAXSCENA
             XRMODGO(I)   = .FALSE.
             XRMODACTS(I) = .FALSE.
             IF ( SCENARIOS(SCEVISRTOF,I) .GT. 0) THEN
               XRENACTS(I)  = .TRUE.
               DO J = 1, NBLIVISDES
                 C_XRMODRTD(J,I)(1:VISDESCLG) =
     .                              VISRTDES(J,SCENARIOS(SCEVISRTOF, I))
               ENDDO
               IF (SCENARIOS(SCMODELID, I).LT. MAX_NB_GR_MODELS) THEN
                 C_XRMODDES(I)= GR_MODEL_DESC(SCENARIOS(SCMODELID,I))
               ELSE
                 C_XRMODDES(I)= AIR_MODEL_DESC(SCENARIOS(SCMODELID,I)
     .                                              -MAX_NB_GR_MODELS)
               ENDIF
             ELSE
               XRENACTS(I)  = .FALSE.
               DO J = 1, NBLIVISDES
                 C_XRMODRTD(J,I)(1:VISDESCLG) = ' '
               ENDDO
                C_XRMODDES(I) = ' '
             ENDIF
           ENDDO
C
           OLD_XRSCNUM = -1         ! reset transition detectors
           OLD_SELRTE = -1
           CALL RESET_VR_INPUT      ! Reset visual route input
           CALL RESET_SC_INPUT      ! Reset scenario creation input
C
           PROGSTATE = 14
         ENDIF
       ENDIF
C
       GOTO 999
C
C=========================================================================
C   R E G. P R. ( S T A T E  1 3 )
C   Error state, wait till another airport is selected
C=========================================================================
13    GOTO 999
C
C=========================================================================
C   R E G. P R. ( S T A T E  1 4 )
C   We are all set up --> do the input processing
C=========================================================================
14     CONTINUE
C
C -- test for when a different route is selected
C
       IF (XRSELRTE .NE. OLD_SELRTE) THEN
         TAGTRECR   = XRSELRTE       ! send to visual group route selected
         OLD_SELRTE = XRSELRTE
         IF (XRSELRTE .GT. 0) THEN
           XRENSTR   = VISRTFREE(XRSELRTE)
           XRENERASE = .NOT. XRENSTR
           XRENDESC  = .TRUE.
           XRERASE   = .FALSE.       ! annul erase button touch
           XRRTEXC   = .FALSE.       ! annul execute erase button touch
           XRRECORD  = .FALSE.       ! annul record vis route touch
           DO J = 1, NBLIVISDES
             C_XREDTVRTD(J) = VISRTDES(J,XRSELRTE)(1:VISDESCLG)
           ENDDO
         ELSE
           XRENSTR   = .FALSE.
           XRENERASE = .FALSE.
           XRENDESC  = .FALSE.
           DO J = 1, NBLIVISDES
             C_XREDTVRTD(J) = ' '
           ENDDO
         ENDIF
       ENDIF
C
C -- Check if the erase & route execute buttons have been pressed if yes,
C    erase selected visual route and corresponding scenarios.
C
       IF (XRERASE .AND. XRRTEXC) THEN
C
         TCMGTDELR = .TRUE.          ! send delete command to vis. group
         VISRTFREE(XRSELRTE) = .TRUE.
         DO J = 1, NBLIVISDES
           VISRTDES(J,XRSELRTE)(1:VISDESCLG)   = ' '
         ENDDO
C
         DO I = 1, MAXSCENA          ! delete all scenarios with this vis route
           IF (SCENARIOS(SCEVISRTOF, I) .EQ. XRSELRTE) THEN
              SCENARIOS(SCEVISRTOF, I) = 0
              SCENARIOS(SCMODELID,  I) = 0
C
C -- deactivate scenario on playback page
C
              IF (ACTIV_SCENA(I) .NE. 0) THEN    ! deactivate scenario
                 XRMODGO(I) = .FALSE.            ! if it was activated
                 XRMODACTS(I) = .FALSE.
                 TAGTVEHI(ACTIV_SCENA(I)) = 0
                 TAGTROUT(ACTIV_SCENA(I)) = 0
                 TCMGTVSS(ACTIV_SCENA(I)) = .FALSE.
                 ACTIV_SCENA(I) = 0
                 NB_ACTIVATED_MOD = NB_ACTIVATED_MOD - 1
              ENDIF
            ENDIF
C
C -- deactivate scenario on scenario creation page
C
            IF ( TAGTROUT(I) .EQ. XRSELRTE ) THEN
              IF ( PLBK_SCENA(I) .NE. 0 ) THEN
                XRPLBSS  (I) = .FALSE.
                XRPLBACTS(I) = .FALSE.
                TAGTVEHI(ACTIV_SCENA(I)) = 0
                TAGTROUT(ACTIV_SCENA(I)) = 0
                TCMGTVSS(ACTIV_SCENA(I)) = .FALSE.
                PLBK_SCENA(I) = 0
              ENDIF
            ENDIF
C
         ENDDO
C
         GOTO 200        ! request write of airport data
       ENDIF
C
C -- If the user has change a Vis. route description save it.
C
       IF (XRDESCH) THEN
         XRDESCH = .FALSE.        ! the change has been noted
         DO J = 1, NBLIVISDES
           VISRTDES(J,XRSELRTE)(1:VISDESCLG)   = C_XREDTVRTD(J)
         ENDDO
         IF (NEW_AIRPORT) THEN
           NEW_AIRPORT = .FALSE.
           GOTO 300               ! add airport in file then write airport data
         ELSE
           GOTO 200               ! request write of airport data
         ENDIF
       ENDIF
C
C -- Check if scenario data on page is valid to be saved
C    In other words : - if a model is selected
C                     - if a valid scenario number is present
C                     - if a occupied visual route is selected
C                     - If its not the one already saved
      DO I = 1,MAXSCENA
       IF ((VEHIC_NUM(I) .GT. 0) .AND.
     .     (XRSCNUM .GT. 0)  .AND. (XRSCNUM .LE. MAXSCENA) .AND.
     .     (ROUTE_NUM(I) .GT. 0).AND.(ROUTE_NUM(I) .LE. MAXVISU)) THEN
         IF ((VEHIC_NUM(I) .NE. SCENARIOS(SCMODELID, XRSCNUM)) .OR.
     .       (ROUTE_NUM(I) .NE. SCENARIOS(SCEVISRTOF, XRSCNUM))) THEN
C                            ! allow entry of scenario if vis. rte. is occupied
           XRENSCEXE(I) = .NOT. (VISRTFREE(ROUTE_NUM(I)))
         ELSE
           XRENSCEXE(I) = .FALSE. ! disallow entry of scenario
           XRSCEXE(I)   = .FALSE. ! data has not changed, so nothing to save
         ENDIF
       ELSE
         XRENSCEXE(I) = .FALSE. ! disallow entry of scenario
         XRSCEXE(I)   = .FALSE. ! data is not valid, so nothing to save
       ENDIF
      ENDDO
C
C -- Check if the given scenario must be saved in the DataBase.
C
      DO I = 1,MAXSCENA
       IF (XRSCEXE(I)) THEN
         SCENARIOS(SCEVISRTOF,XRSCNUM)   = ROUTE_NUM(I)
         SCENARIOS(SCMODELID, XRSCNUM)   = VEHIC_NUM(I)
         XRSCEXE(I)   = .FALSE.
         XRENSCEXE(I) = .FALSE.
         GOTO 200                 ! request write of airport data
       ENDIF
      ENDDO
C
C --- Check if Scenario Number is available
C
       IF ( XRENACTS(XRSCNUM) ) XRSCNUM = 0
C
C --- Check if Scenario Number has Changed (to show previously saved Scenario)
C
       IF (XRSCNUM .NE. OLD_XRSCNUM) THEN
         OLD_XRSCNUM = XRSCNUM
         IF (SCENARIOS(SCEVISRTOF,XRSCNUM) .GT. 0) THEN
           XRSCVIRT = SCENARIOS(SCEVISRTOF,XRSCNUM)
           XRSCMODE = SCENARIOS(SCMODELID, XRSCNUM)
         ENDIF
       ENDIF
C
C --- Check if the Start recording button has been pressed
C
       IF (XRRECORD) THEN
         IF (.NOT.XRENSTR) THEN           ! is it a valid touch?
           XRRECORD = .FALSE.             ! no, cancel it
         ELSE
           TCMGTRREC = .TRUE.             ! yes, send command to visual
C
           XRENERASE = .FALSE.            ! disable input to other buttons
           XRENSCEN  = .FALSE.
           XRENDESC  = .FALSE.
           DO I = 1, MAXVISU
             XRENVIRT(I) = (XRSELRTE .EQ. I)
             XRPLBAVL(I) = XRENVIRT(I)
           ENDDO
           XRVRTEFL(XRSELRTE) = .FALSE.   ! The route is occupied for now
C
           TAGTTIMR = RECLENGTH           ! Remaining Time
           XRTIMREM = TAGTTIMR            ! Copy in Page label
           RECSTIME = YTSIMTM             ! Time at recording start
C
           PROGSTATE = 15
         ENDIF
       ENDIF
C
C --- check for scenario acivation changes through playback page
C
       IF (XRACTCHG) THEN
         XRACTCHG = .FALSE.
         DO I = 1, MAXSCENA
           IF (XRMODACTS(I)) THEN
C
C -- If a route was selected (new activation) find a empty slot in the buffer
C
             IF (ACTIV_SCENA(I) .EQ. 0) THEN  ! new activation
               EMPTY_SLOT = 0                 ! find an empty active model slot
               J = 1
               DO WHILE ((EMPTY_SLOT .EQ. 0) .AND.
     .                   (J .LE. MAX_ACTIVE_MOD))
                 IF (TAGTROUT(J) .EQ. 0) EMPTY_SLOT = J
                 J = J + 1
               ENDDO
C
C -- Empty slot was found, send appropriate data to visual
C
               IF (EMPTY_SLOT .NE. 0) THEN
                 TAGTVEHI(EMPTY_SLOT) = SCENARIOS(SCMODELID,I)
                 TAGTROUT(EMPTY_SLOT) = SCENARIOS(SCEVISRTOF,I)
                 TCMGTVSS(EMPTY_SLOT) = .FALSE.
                 NB_ACTIVATED_MOD = NB_ACTIVATED_MOD + 1
                 ACTIV_SCENA(I) = EMPTY_SLOT
               ELSE
                 XRMODACTS(I) = .FALSE.
               ENDIF
             ENDIF
           ELSE IF (ACTIV_SCENA(I) .NE. 0) THEN ! we have a desactivaion
             TAGTVEHI(ACTIV_SCENA(I)) = 0
             TAGTROUT(ACTIV_SCENA(I)) = 0
             TCMGTVSS(ACTIV_SCENA(I)) = .FALSE.
             NB_ACTIVATED_MOD = NB_ACTIVATED_MOD - 1
             ACTIV_SCENA(I) = 0
           ENDIF
         ENDDO
C
C           ! Limit the number of active models
         IF (NB_ACTIVATED_MOD .GE. MAX_ACTIVE_MOD) THEN
           DO I = 1, MAXSCENA
             XRENACTS (I) = XRMODACTS(I) ! only actived ones can be selected
           ENDDO
         ELSE
           DO I = 1, MAXSCENA            ! all of them can be selected
             XRENACTS (I) = (SCENARIOS(SCMODELID, I) .NE. 0)
           ENDDO
         ENDIF
C
       ENDIF    ! if (XRACTCHG)
C
C --- check for scenario acivation changes through scenario creation page
C
       DO I = 1, MAXSCENA
         IF ( XRPLBRTE(I) ) THEN
C
C -- If a route was selected (new activation) find a empty slot in the buffer
C
           K = 1
           DO WHILE ( K .LE. MAXSCENA )
             IF ( PLBK_SCENA(K) .EQ. 0 ) THEN
               PLBK_SLOT = 0
               J = 1
               DO WHILE (( PLBK_SLOT .EQ. 0 ) .AND.
     >                   ( J .LE. MAXSCENA ))
                 IF ( ROUTE_NUM(J) .EQ. 0 ) PLBK_SLOT = J
                 J = J + 1
               ENDDO
C
C -- Empty slot was found, send appropriate data to visual
C
               IF ( PLBK_SLOT .NE. 0 ) THEN
                 ROUTE_NUM(PLBK_SLOT) = I
                 TCMGTVSS (PLBK_SLOT) = .FALSE.
                 PLBK_SCENA(K) = PLBK_SLOT
C
C -- display route description
C
                 DO J = 1, NBLIVISDES
                   C_XRPLBRTD(J,PLBK_SLOT)(1:VISDESCLG) =
     >                            VISRTDES(J,I)(1:VISDESCLG)
                 ENDDO
                 K = MAXSCENA + 1
               ENDIF
             ENDIF
             K = K + 1
           ENDDO
           XRPLBRTE(I) = .FALSE.
         ENDIF
       ENDDO
C
C -- Check to see if a model number has been selected
C
       IF ( XRPLBSCM .GT. 0) THEN
         I = 1
         DO WHILE ( I .LE. MAXSCENA )
C
C -- If a model was selected (new activation) find a empty slot in the buffer
C
           IF (VH_PLBK_SCENA(I) .EQ. 0) THEN
             VH_PLBK_SLOT = 0
             J = 1
             DO WHILE ((VH_PLBK_SLOT .EQ. 0) .AND.
     .                 (J .LE. MAXSCENA ))
               IF (VEHIC_NUM(J) .EQ. 0) VH_PLBK_SLOT = J
               J = J + 1
             ENDDO
C
C -- Empty slot was found, send appropriate data to visual
C
             IF (VH_PLBK_SLOT .NE. 0) THEN
               VEHIC_NUM(VH_PLBK_SLOT) = XRPLBSCM
               VH_PLBK_SCENA(I) = VH_PLBK_SLOT
C
C -- display model description
C
               IF ( VEHIC_NUM(I) .LE. NB_GR_MODELS) THEN
                  C_XRPLBMOD(VH_PLBK_SLOT) = GR_MODEL_DESC(XRPLBSCM)
               ELSE IF (( VEHIC_NUM(I) .GE. MAX_NB_GR_MODELS+1 ) .AND.
     >                  ( VEHIC_NUM(I) .LE. NB_AIR_MODELS )) THEN
                 C_XRPLBMOD(VH_PLBK_SLOT) =
     >                  AIR_MODEL_DESC(XRPLBSCM-MAX_NB_GR_MODELS)
               ELSE
                 CALL CONVERT(XRPLBSCM,STRING)
                 C_XRPLBMOD(VH_PLBK_SLOT) = 'MODEL # '//STRING
               ENDIF
               I = MAXSCENA + 1
             ENDIF
           ENDIF
           I = I + 1
         ENDDO
         XRPLBSCM = 0
       ENDIF
C
C --- Set available flag for playback page when both a
C     model number and a route number have been selected.
C
      DO I = 1, MAXSCENA
C
C -- If route/model have been deselected from playback page
C    then remove description from page and reset visual and
C    slot avail labels
C
        IF ( .NOT. XRPLBACTS(I) .AND. OLD_PLBACTS(I) ) THEN
          ROUTE_NUM (I) = 0
          VEHIC_NUM (I) = 0
          TCMGTVSS  (I) = .FALSE.
          TAGTROUT  (I) = 0
          TAGTVEHI  (I) = 0
          C_XRPLBMOD(I) = ' '
          DO J = 1, NBLIVISDES
            C_XRPLBRTD(J,I)(1:VISDESCLG) = ' '
          ENDDO
          PLBK_SCENA   (I) = 0
          VH_PLBK_SCENA(I) = 0
        ENDIF
C
        IF ((( VEHIC_NUM(I) .GT. 0 ) .AND.
     >      ( ROUTE_NUM(I) .GT. 0 )) .AND. .NOT. XRPLBACTS(I) ) THEN
          XRPLBACTS(I) = .TRUE.
        ENDIF
C
        OLD_PLBACTS(I)  = XRPLBACTS(I)
C
       ENDDO
C
C --- check for changes in start/stop of scenario
C
       DO I = 1, MAXSCENA
         IF (ACTIV_SCENA(I) .NE. 0) THEN
           IF (.NOT. TCMGTVSS(ACTIV_SCENA(I)) .AND.
     .          OLD_START_STOP(I)) THEN
             OLD_START_STOP(I) = .FALSE.
             XRMODGO(I) = .FALSE.
           ELSE IF ( XRMODGO(I) .AND. .NOT. OLD_START_STOP(I)) THEN
             TCMGTVSS(ACTIV_SCENA(I)) = .TRUE.
             OLD_START_STOP(I) = .TRUE.
           ELSE IF ( .NOT. XRMODGO(I) .AND. OLD_START_STOP(I)) THEN
             OLD_START_STOP(I) = .FALSE.
             TCMGTVSS(ACTIV_SCENA(I)) = .FALSE.
           ENDIF
         ENDIF
C
         IF (PLBK_SCENA(I) .NE. 0) THEN
           IF (.NOT. TCMGTVSS(PLBK_SCENA(I)) .AND. OLD_SS(I)) THEN
             OLD_SS (I) = .FALSE.
             XRPLBSS(I) = .FALSE.
           ELSE IF ( XRPLBSS(I) .AND. .NOT. OLD_SS(I)) THEN
             TCMGTVSS(PLBK_SCENA(I)) = .TRUE.
             TAGTROUT(PLBK_SCENA(I)) = ROUTE_NUM(I)
             TAGTVEHI(PLBK_SCENA(I)) = VEHIC_NUM(I)
             OLD_SS(I) = .TRUE.
           ELSE IF ( .NOT. XRPLBSS(I) .AND. OLD_SS(I)) THEN
             OLD_SS(I) = .FALSE.
             TCMGTVSS(PLBK_SCENA(I)) = .FALSE.
           ENDIF
         ENDIF
       ENDDO
C
C ---  Perform scenario deletion request
C
       IF (XRSCEDEL) THEN
         IF (XRSCENUM.GT.0.AND.XRSCENUM.LE.MAXSCENA) THEN
           SCENARIOS(SCEVISRTOF,XRSCENUM) = 0      ! clear route
           SCENARIOS(SCMODELID,XRSCENUM)  = 0      ! clear model
           XRENACTS(XRSCENUM) = .FALSE.            ! disallow playback
           C_XRMODDES(XRSCENUM) = ' '              ! clear model description
           DO I = 1, NBLIVISDES
             C_XRMODRTD(I,XRSCENUM)(1:VISDESCLG) = ' ' !Clear route desc
           ENDDO
           XRSCEDEL = .FALSE.
           XRSCENUM = 0
C
           GOTO 200        ! request write of airport data
C
         ELSE
           XRSCEDEL = .FALSE.
           XRSCENUM = 0
         ENDIF
       ENDIF
C
       GOTO 999
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C   Reg. Pr. (state 15): We are recording, watch for end of recording
C
15     CONTINUE
C
       TAGTTIMR = RECLENGTH - YTSIMTM + RECSTIME ! Calculate remaining Time
       XRTIMREM = TAGTTIMR                       ! Get time remaining
C
       IF ((.NOT. XRRECORD) .OR. (XRTIMREM .LE. 0.0)) THEN
         TCMGTRREC = .FALSE.
         XRRECORD  = .FALSE.
         XRENDESC  = .TRUE.
C
         VISRTFREE(XRSELRTE) = .FALSE.
C
         TCMGTRSAV = .TRUE. ! Request to save the current route
         TAGTTIMR = 0.0                 ! Reset Remaining Time
         XRTIMREM = TAGTTIMR            ! Copy in Page label
C
         IF (NEW_AIRPORT) THEN
           NEW_AIRPORT = .FALSE.
           GOTO 300       ! add airport in file then write airport data
         ELSE
           GOTO 200       ! request write of airport data
         ENDIF
       ENDIF
C
       GOTO 999
 
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C   Reg. Pr. (state 16): wait for the end of the write of the header
C                        then launch write of airport index in route
C                        definition file
C
16     CONTINUE
C
       IF (IO_STATUS .NE. IN_PROGRESS) THEN
C
         IF (IO_STATUS .NE. SUCCESS) THEN
           WRITE(*,*) ' ERR/XR: Write of file header failed :'
     .,               IO_STATUS
           XR_ERROR  = IO_STATUS ! freeze program
C
C                ! WRITE AIRPORT INDEX
         ELSE
           IO_STATUS = IN_PROGRESS
           IO_POSITION = HEADERLEN
           CALL CAE_IO_WRITE( FTN_STATUS,
     .                        IO_STATUS,
     .                        %VAL(FILE_DESC),
     .                        %VAL(0),
     .                        AIRPINDEX,
     .                        -1,
     .                        AIRINDLEN,
     .                        IO_POSITION)
C
           IF (FTN_STATUS .NE. SUCCESS)  THEN
             WRITE(*,*) ' ERR/XR: index write request failed :'
     .,                  FTN_STATUS
             XR_ERROR  = FTN_STATUS ! freeze program
C
           ELSE
             PROGSTATE = 17
           ENDIF
         ENDIF
       ENDIF
C
       GOTO 999
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C   Reg. Pr. (state 17): wait for the end of the write of the index
C                        then launch write of airport data in route
C                        definition file
C
17     CONTINUE
C
       IF (IO_STATUS .NE. IN_PROGRESS) THEN
C
         IF (IO_STATUS .NE. SUCCESS) THEN
           WRITE(*,*) ' ERR/XR: Write of airport index failed :'
     .,               IO_STATUS
           XR_ERROR  = IO_STATUS ! freeze program
C
         ELSE
           GOTO 200        ! request write of airport data
         ENDIF
       ENDIF
C
       GOTO 999
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C  request write of the airport data (here so it wont be repeted
C                                     X times in the code)
C
200   CONTINUE
C
C -- Before writing to Data file let's first check our data
C
      DO I = 1, MAXSCENA
        IF ( SCENARIOS(SCEVISRTOF,I) .GT. MAXSCENA ) THEN
C
C -- Error in data found, Write to Error Logger.
C
          WRITE(*,102) I,SCENARIOS(SCEVISRTOF,I)
     .,                      SCENARIOS(SCMODELID,I)
C
C -- Reset information to avoid any residual problems.
C
          SCENARIOS(SCEVISRTOF,I) = 0
          SCENARIOS(SCMODELID,I)  = 0
C
        ENDIF
      ENDDO
      IO_STATUS = IN_PROGRESS
      IO_POSITION = AIRPINDEX(AIRPORTRTE, AIRPORT_IND)
C -- Check IO_POSITION to make sure we're writing to the appropriate place
C
      IF ( IO_POSITION .NE. CHECK_AIRPINDX(AIRPORT_IND) ) THEN
C
C -- Not writing to the appropriate place in data file
C
        WRITE(*,103) IO_POSITION,
     .               CHECK_AIRPINDX(AIRPORT_IND),
     .               AIRPORT_IND
C
C -- Set correct value for IO_POSITION
C
        IO_POSITION = CHECK_AIRPINDX(AIRPORT_IND)
      ENDIF
C
      CALL CAE_IO_WRITE( FTN_STATUS,
     .                   IO_STATUS,
     .                   %VAL(FILE_DESC),
     .                   %VAL(0),
     .                   AIRPOBUF,
     .                   -1,
     .                   AIRPBFLG,
     .                   IO_POSITION
     .                 )
C
      IF (FTN_STATUS .NE. SUCCESS)  THEN
        WRITE(*,*) ' ERR/XR: Airport visual routes descrtip'
     .,            'tion write request failed :', FTN_STATUS
        XR_ERROR  = FTN_STATUS ! freeze program
C
      ELSE
        PROGSTATE = 12
      ENDIF
C
      GOTO 999
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C  request write to add new airport in the file
C                    (put here so it wont be repeted 2 times in the code)
C
300   I = HEADER(NBAIRPIND) + 1
      C_AIRPINDEX(AIRPORTID,I) = C_TAICAO1
      AIRPINDEX(AIRPORTRTE, I) = HEADERLEN +
     .                           AIRINDLEN +
     .                           ((I-1) * AIRPBFLG)
      HEADER(NBAIRPIND)  = I
      AIRPORT_IND = I
C
C -- Check if Header Airport Data Section Length is correct
C
      IF (HEADER(AIRPDLGIND) .NE. AIRPBFLG) THEN
         WRITE(*,101) HEADER(AIRPDLGIND)
         HEADER(AIRPDLGIND) = AIRPBFLG
      ENDIF
C
      IO_STATUS = IN_PROGRESS
      IO_POSITION = 0
      CALL CAE_IO_WRITE ( FTN_STATUS,
     .                    IO_STATUS,
     .                    %VAL(FILE_DESC),
     .                    %VAL(0),
     .                    HEADER,
     .                    -1,
     .                    HEADERLEN,
     .                    IO_POSITION)
C
      IF (FTN_STATUS .NE. SUCCESS)  THEN
        WRITE(*,*) ' ERR/XR: Write header into file request ',
     .             'failed :', FTN_STATUS
        XR_ERROR  = FTN_STATUS ! freeze program
C
      ELSE
        PROGSTATE = 16
      ENDIF
C
      GOTO 999
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
 100  FORMAT('ERROR FOUND IN INFORMATION READ : ',I4,' ',I4,' ',I4)
 101  FORMAT('ERROR IN HEADER SECTION LENGTH  : ',I4)
 102  FORMAT('ERROR IN WRITE OF SCENARIOS INFO : ',I4,' ',I4,' ',I4)
 103  FORMAT('ERROR IN IO_POSITION VALUE : ',I4,' ',I4,' ',I4)
C
C   END OF PROCESSING: finished processing for now
C
999   CONTINUE
      RETURN
      END
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
CC                                                          CC
CC  subroutine reset the input for the visual route         CC
CC                                                          CC
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
      SUBROUTINE RESET_VR_INPUT
C
      Implicit None
C
      INCLUDE 'stdxr.inc'       !NOFPC
C
CP    USD8  XRENERASE, XRERASE,  XRRTEXC,
CP          XRENSTR,   XRRECORD, XRENDESC,
CP          XRENVIRT,  XRSELRTE, XRPLBRTE,
CP          XRSCEXE,   XRPLBRTD, XRPLBAVL,
CP          XRENSCEXE, XRPLBSS,  XRPLBSCM,  XRPLBMOD
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 17:14:52 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.226              
C$
      INTEGER*4
     &  XRPLBSCM       ! SELECTED MODEL NUMBER FOR SCENARIO
     &, XRSELRTE       ! SELECTED VISUAL ROUTE BEEING EDITED
C$
      LOGICAL*1
     &  XRENDESC       ! ENABLE MODIFICATION OF ROUTE DESCRIPTION
     &, XRENERASE      ! ENABLE ERASE BUTTON FLAG
     &, XRENSCEXE(10)  ! ENABLE ERASE BUTTON FLAG
     &, XRENSTR        ! ENABLE START RECORDING BUTTON FLAG
     &, XRENVIRT(5)    ! ENABLE MODIFICATION OF ROUTE DESCRIPTION
     &, XRERASE        ! ERASE BUTTON DEPRESSED FLAG
     &, XRPLBAVL(10)   ! PLAYBACK AVAILABLE
     &, XRPLBRTE(10)   ! VISUAL ROUTE USED FLAG
     &, XRPLBSS(10)    ! START/STOP EXECUTION OF VIS RTE FOR MODEL
     &, XRRECORD       ! VISUAL ROUTE RECORDING IN PROGRESS FLAG
     &, XRRTEXC        ! VISUAL ROUTE EXECUTE BUTTON DEPRESSED FLAG
     &, XRSCEXE(10)    ! SCENARIO EXECUTE BUTTON DEPRESSED FLAG
C$
      INTEGER*1
     &  XRPLBMOD(15,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
     &, XRPLBRTD(30,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
C$
      LOGICAL*1
     &  DUM0000001(311368),DUM0000002(300),DUM0000003(6)
     &, DUM0000004(6),DUM0000005(72),DUM0000006(2)
     &, DUM0000007(10),DUM0000008(4)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,XRPLBSCM,DUM0000002,XRPLBRTE,DUM0000003,XRSELRTE
     &, XRRECORD,XRERASE,DUM0000004,XRRTEXC,DUM0000005,XRENDESC
     &, XRENERASE,DUM0000006,XRENSCEXE,XRENSTR,XRENVIRT,DUM0000007
     &, XRPLBAVL,XRPLBMOD,XRPLBRTD,XRPLBSS,DUM0000008,XRSCEXE   
C------------------------------------------------------------------------------
C
C     working labels
C
      INTEGER*4     I
C
C     start of subroutine
C
      XRERASE   = .FALSE.
      XRRTEXC   = .FALSE.
      XRRECORD  = .FALSE.
      XRENDESC  = (XRSELRTE .NE. 0)
      XRENSTR   = VISRTFREE(XRSELRTE)
      XRENERASE = .NOT. XRENSTR
      DO I = 1, MAXVISU
        XRENVIRT(I) = .TRUE.
        XRPLBAVL(I) = .TRUE.
      ENDDO
C
      RETURN
      END
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
CC                                                          CC
CC  subroutine reset the input for the scenario creation    CC
CC                                                          CC
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
      SUBROUTINE RESET_SC_INPUT
C
      Implicit None
C
      INCLUDE 'stdxr.inc'       !NOFPC
C
CP    USD8  XRENSCEN, XRSCEXC, XRENSCEXC,  XRENACTS,
CP          XRPLBRTE, XRSCEXE, XRPLBRTD,
CP          XRENSCEXE,XRPLBSS, XRPLBSCM,  XRPLBMOD
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 17:14:52 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.226              
C$
      INTEGER*4
     &  XRPLBSCM       ! SELECTED MODEL NUMBER FOR SCENARIO
C$
      LOGICAL*1
     &  XRENACTS(10)   ! ENABLE ACTIVATION OF MODELS
     &, XRENSCEN       ! ENABLE CREATION OF SCENARIOS BUTTON FLAG
     &, XRENSCEXC      ! ENABLE SCENARIOS EXECUTE BUTTON FLAG
     &, XRENSCEXE(10)  ! ENABLE ERASE BUTTON FLAG
     &, XRPLBRTE(10)   ! VISUAL ROUTE USED FLAG
     &, XRPLBSS(10)    ! START/STOP EXECUTION OF VIS RTE FOR MODEL
     &, XRSCEXC        ! SCENARIO EXECUTE BUTTON DEPRESSED FLAG
     &, XRSCEXE(10)    ! SCENARIO EXECUTE BUTTON DEPRESSED FLAG
C$
      INTEGER*1
     &  XRPLBMOD(15,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
     &, XRPLBRTD(30,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
C$
      LOGICAL*1
     &  DUM0000001(311368),DUM0000002(300),DUM0000003(81)
     &, DUM0000004(2),DUM0000005(26),DUM0000006(3)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,XRPLBSCM,DUM0000002,XRPLBRTE,DUM0000003,XRENACTS
     &, DUM0000004,XRENSCEN,XRENSCEXC,XRENSCEXE,DUM0000005,XRPLBMOD
     &, XRPLBRTD,XRPLBSS,DUM0000006,XRSCEXC,XRSCEXE   
C------------------------------------------------------------------------------
C
C     working labels
C
      INTEGER*4     I
C
C     start of subroutine
C
      XRENSCEN = .FALSE.
      DO I = 1, MAXVISU
        XRENSCEN = (XRENSCEN .OR. .NOT. VISRTFREE(I))
      ENDDO
C
      DO I = 1,MAXSCENA
        XRSCEXE(I)   = .FALSE.
        XRENSCEXE(I) = .FALSE.
      ENDDO
C
      RETURN
      END
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
CC                                                          CC
CC  subroutine disable the input for the visual route &     CC
CC  scenario creation                                       CC
CC                                                          CC
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
      SUBROUTINE DISABLE_INPUT
C
      Implicit None
C
      INCLUDE 'stdxr.inc'       !NOFPC
C
CP    USD8  XRENERASE, XRENSTR,  XRENDESC,  XRENVIRT,
CP          XRRECORD,  XRENSCEN, XRENACTS,
CP          XRPLBRTE,  XRSCEXE,  XRPLBRTD,  XRPLBAVL,
CP          XRENSCEXE, XRPLBSS,  XRPLBSCM,  XRPLBMOD
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 17:14:52 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.226              
C$
      INTEGER*4
     &  XRPLBSCM       ! SELECTED MODEL NUMBER FOR SCENARIO
C$
      LOGICAL*1
     &  XRENACTS(10)   ! ENABLE ACTIVATION OF MODELS
     &, XRENDESC       ! ENABLE MODIFICATION OF ROUTE DESCRIPTION
     &, XRENERASE      ! ENABLE ERASE BUTTON FLAG
     &, XRENSCEN       ! ENABLE CREATION OF SCENARIOS BUTTON FLAG
     &, XRENSCEXE(10)  ! ENABLE ERASE BUTTON FLAG
     &, XRENSTR        ! ENABLE START RECORDING BUTTON FLAG
     &, XRENVIRT(5)    ! ENABLE MODIFICATION OF ROUTE DESCRIPTION
     &, XRPLBAVL(10)   ! PLAYBACK AVAILABLE
     &, XRPLBRTE(10)   ! VISUAL ROUTE USED FLAG
     &, XRPLBSS(10)    ! START/STOP EXECUTION OF VIS RTE FOR MODEL
     &, XRRECORD       ! VISUAL ROUTE RECORDING IN PROGRESS FLAG
     &, XRSCEXE(10)    ! SCENARIO EXECUTE BUTTON DEPRESSED FLAG
C$
      INTEGER*1
     &  XRPLBMOD(15,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
     &, XRPLBRTD(30,2,10)
C$                     ! VISUAL ROUTE OF SCENARIO DESCRIPTION
C$
      LOGICAL*1
     &  DUM0000001(311368),DUM0000002(300),DUM0000003(10)
     &, DUM0000004(70),DUM0000005(1),DUM0000006(10)
     &, DUM0000007(4)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,XRPLBSCM,DUM0000002,XRPLBRTE,DUM0000003,XRRECORD
     &, DUM0000004,XRENACTS,XRENDESC,XRENERASE,XRENSCEN,DUM0000005
     &, XRENSCEXE,XRENSTR,XRENVIRT,DUM0000006,XRPLBAVL,XRPLBMOD
     &, XRPLBRTD,XRPLBSS,DUM0000007,XRSCEXE   
C------------------------------------------------------------------------------
C
C     working labels
C
      INTEGER*4     I
C
C     start of subroutine
C
      XRENERASE = .FALSE.
      XRENSTR   = .FALSE.
      XRRECORD  = .FALSE.
      XRENSCEN  = .FALSE.
      XRENDESC  = .FALSE.
      DO I = 1, MAXVISU
        XRENVIRT(I) = .FALSE.
        XRPLBAVL(I) = .FALSE.
      ENDDO
      DO I = 1, MAXSCENA
        XRENACTS(I) = .FALSE.
      ENDDO
C
      RETURN
      END
C
C===========================================================================
C SUBROUTINE CONVERT
C===========================================================================
C
      SUBROUTINE CONVERT(NUMBER,STRING)
C
      INTEGER*4 NUMBER, I, J, K, OLD,
     >          VAL(4)/1000,100,10,1/,
     >          LIM(4)/1,2,3,4/
C
      CHARACTER*4 STRING
      CHARACTER*1 NUM1(0:9) /'0','1','2','3','4','5','6','7','8','9'/
C
      DO I = 4,1,-1
        IF ( NUMBER .GE. VAL(I) )  K = LIM(I)
      ENDDO
C
      OLD = NUMBER
      DO I = K,4
         J = OLD/VAL(I)
         STRING(I:I) = NUM1(J)
         OLD = OLD - VAL(I)*J
      ENDDO
C
      RETURN
      END
