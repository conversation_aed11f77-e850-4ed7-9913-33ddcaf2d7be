#!  /bin/csh -f
#!  $Revision: PR2_LOD - Load the foreground processes V1.4 (MR) Nov-91$
#!
#!  IF YOU MODIFY THIS FILE, PLEASE MODIFY THE FILE PRC_LOD AS WELL.
#!
#!  (SP0Cn and AP0Cn for scenario 2)
#!
#! &AP0C0.EXE
#! &SP0C0.EXE
#! &$eng.bin
#! &$flt1_300.bin
#! &$flt2_300.bin
#! &$flt3_300.bin
#! &$flt4_300.bin
#! &$sna.bin
#! &$dmc.dat
#! &$4290.mdf
#! &$sub.dat
#! &page.dat
#! &$xm.dat 
#! &$xr.dat
#!
#!  Use the next line only if there is a second ethernet line in use.
#!
#! &$1dmc.dat
#!
#!  Use one or the other of the following depending if there is scenario or not
#!
#! &$scaling2.dat
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
#!  Version 1.2: <PERSON> (06-Jun-91)
#!     - changed &$sc1.cal for &$scaling1.dat in header
#!
#!  Version 1.3: <PERSON>uy (20-Aug-91)
#!     - Add support for second ethernet line.
#!
#!  Version 1.4: Mario Royer (21-Nov-91)
#!     - New version for MOM 4.0 (parallel load)  
#!
if ("$argv[1]" == "Y") then
  set echo
  set verbose
endif
if ! ("$argv[2]" == "LOAD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
# --- Following lines commented on ibm for main/development concept.
#
set SIMEX_CPU="`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
set SIMEX_SHIP="`logicals -t CAE_CDBNAME`"
setenv "$SIMEX_SHIP"eng.bin       eng_bin
setenv "$SIMEX_SHIP"flt1_300.bin      flt1_bin
setenv "$SIMEX_SHIP"flt2_300.bin      flt2_bin
setenv "$SIMEX_SHIP"flt3_300.bin      flt3_bin
setenv "$SIMEX_SHIP"flt4_300.bin      flt4_bin
setenv "$SIMEX_SHIP"sna.bin           snd_bin
setenv "$SIMEX_SHIP"dmc.dat           cae_dmc_dat
setenv "$SIMEX_SHIP"4290.mdf          cae_scs_mdf
setenv "$SIMEX_SHIP"sub.dat           cae_asc_mdf
setenv page.dat                       page_dat
setenv "$SIMEX_SHIP"xm.dat            mmut_dat 
setenv "$SIMEX_SHIP"xr.dat            xr_dat
#
# Use the following line for the second ethernet line.
#
setenv "$SIMEX_SHIP"1dmc.dat      cae_dmc1_dat
#
# Use one or the other of the following depending if there is scenario or not.
#
setenv "$SIMEX_SHIP"scaling2.dat  cae_scal
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
fse_operate RESET EMPTY_ARG EMPTY_ARG 2
fse_operate LOAD  START_NOTIFY $argv[3] 2
if ($status == 0) touch $argv[4]
exit
