C'Name          Criteria Analysis
C'Module_ID     XSCA
C'<PERSON><PERSON><PERSON>'Date          May 27, 1991
C'Application   Monitors Criteria entries
C
C
      SUBROUTINE USD8XSCA
C
      IMPLICIT NONE
C
C'Purpose
C
C     The function of this program is to monitor and determine
C'Revision_History
C
C File: /cae/if/reference/usd8xsca.for.2
C       Modified by: BAON
C       Thu Dec 12 18:57:30 1991
C       < Brought XSCA from S742 to USD8 >
C
C File: /cae1/ship/s742xsca.for.15
C       Modified by: <PERSON><PERSON>       Tue Dec  3 16:20:36 1991
C       < Added logic for logical criteria >
C
C File: /cae1/ship/s742xsca.for.13
C       Modified by: MB
C       Mon Nov 18 18:00:39 1991
C       < LB and GA should be triggered at the value as well >
C
C File: /cae1/ship/s742xsca.for.8
C       Modified by: MB
C       Mon Nov 18 13:29:38 1991
C       < Once 1 criteria is satisfied, it should no longer be checked >
C
C File: /cae1/ship/s742xsca.for.6
C       Modified by: MB
C       Fri Nov 15 21:26:31 1991
C       < Parameter PRESCNT to CRIT_EVAL was declared incorrectly >
C
C File: /cae1/ship/s742xsca.for.5
C       Modified by: MB
C       Wed Nov 13 19:26:01 1991
C       < Fixed typo in I*4 Constant saving >
C
C File: /cae1/ship/aw37xsca.for.3
C       Modified by: Nam Tran
C       Fri Aug  2 12:56:32 1991
C       < modified AW20 for AW37: subroutine name & 2*CP statement     >
C       < commented out yxstrtxrf3/4, xr3/4 see also ADD_VALUE section >
C
C File: /cae1/ship/aw20xsca.for.2
C       Modified by: L.Lee
C       Mon Jul 15 17:08:27 1991
C       < Installing preselect system on AW20 >
C     whether preselect criteria entered in the preselect table
C     (XSTABLE) are satisfied. Once the preselect criteria are
C     satisfied, it will activate the preselect item or malfunction
C     that was selected and the preselect entry in the XSTABLE will
C     be deleted.
C
C'Input
C
C     XSTABLE - Preselect criteria table
C
C     +-------------------+
C     |      Entry 1      |
C     |  Preselect Size   |
C     +-------------------+          +-------------+
C     |  Type of Trigger  |          | Type        |
C     +-------------------+          +-------------+
C     |  Exercise Number  |          | Type     or |
C     +-------------------+          | Offset   or |
C     |  Spare or Time    |          | Constant    |
C     +-------------------+     +--> +-------------+
C     |  Preselect Item   |     |    | Type     or |
C     |  Set Value DCB    |     |    | Offset   or |
C     +-------------------+     |    | Constant    |
C     |  Criteria Size    |     |    +-------------+
C     +-------------------+     |    |   ......... |
C     |  Criteria         | ----+    +-------------+
C     |  Condition        |
C     +-------------------+
C     | Offset Block Size |          +-------------+
C     +-------------------+          |  Base       |
C     |  Offset Block     | ----+    +-------------+
C     +-------------------+     |    |  Base       |
C     |    ...........    |     |    +-------------+
C     +-------------------+     |    |  Offset     |
C                               |    +-------------+
C                               |    |  Offset     |
C                               +--> +-------------+
C                                    |  Byte Count |
C                                    +-------------+
C                                    |  Byte Count |
C                                    +-------------+
C                                    |  Data Type  |
C                                    +-------------+
C                                    |  Data Type  |
C                                    +-------------+
C                                    |   ........  |
C                                    +-------------+
C
C'Output
C
C      Update the preselect item or malfunction value in the CDB
C
C
C'Subroutine_calls
C
C      CRIT_EVAL   -  Criteria evaluation routine
C      XDSETIN     -  Directives for Set Value DCB
C
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'PAGECODE.INC'    !NOFPC
CVAX  INCLUDE 'STDPRES.INC'     !NOFPC
CVAXEND
CSGI
CSGI  INCLUDE 'pagecode.inc'    !NOFPC
CSGI  INCLUDE 'stdpres.inc'     !NOFPC
CSGIEND
CIBM
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'stdpres.inc'     !NOFPC
CIBMEND
C
C'Cdb_variables
C
CP     usd8
CP     XSARM   ,     XSOFFST ,   XSPRUPD,  TXSPRUPD,
CP   - XSCRITVAL,    XSINDEX ,   XSNXTOFF
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:20:34 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      REAL*4   
     &  XSCRITVAL(50,20)
C$                     ! VALUE TABLE FOR PRESELECT CRITERIA
C$
      INTEGER*4
     &  XSINDEX(50)    ! OFFSET LOCATION OF EACH PRESELECT TABLE
     &, XSNXTOFF       ! NEXT AVAILABLE LOCATON IN THE PRE' TABLE
     &, XSOFFST(50)    ! CONTAINS THE OFFSET OF THE ARMES MALF'
C$
      LOGICAL*1
     &  TXSPRUPD(6)    ! UPDATE PRESELECT FLAG
     &, XSARM(50)      ! ARMED MALFUNCTION FLAGS
     &, XSPRUPD(2)     ! UPDATE PRESELECT FLAG
C$
      LOGICAL*1
     &  DUM0200001(15285),DUM0200002(9),DUM0200003(86)
     &, DUM0200004(4252),DUM0200005(12200)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,TXSPRUPD,DUM0200002,XSPRUPD,DUM0200003,XSOFFST
     &, DUM0200004,XSINDEX,XSNXTOFF,XSCRITVAL,DUM0200005,XSARM     
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
      REAL*4
     >             R4DCB(0:100)          ! Equivalence to DCB
C
      INTEGER*4
     >             CRIT_START            ! Criteria start
     >,            CRIT_END              ! Criteria end
     >,            STV_START             ! Set Value start
     >,            STV_END               ! Set Value end
     >,            OFF_START             ! Offset block start
     >,            OFF_END               ! Offset block end
     >,            OFF_SIZE              ! Offset block size
     >,            DCB_PTR               ! DCB Pointer
     >,            OFF_PTR               ! Offset block Pointer
     >,            I4DCB(0:100)          ! Equivalence to DCB
     >,            PRES_INDEX                ! Preselect index
     >,            NEXT_PRES                 ! Next preselect index
     >,            I, J, K               ! Counters
     >,            OFF_BLK(4,MAX_CRIT+1,2)
     >,            OFF_BLOCK(4,-249:250,2)
     >,            ERROR_CODE            ! Error code
C
      INTEGER*2
     >             DCB(0:200)            ! DCB Variable
     >,            OFF_TBL((MAX_CRIT+1)*16)
C
      LOGICAL*1
     >             CRIT_STATUS           ! Criteria status flag
     >,            HOST_UPD              ! Host update flag
C
      BYTE         VAL_TABLE(100)
C
C --- PARAMETERS
C     --------------
C
      INTEGER*4
     >             BASE_EAS              ! Base parameter to offset block
     >,            OFFSET_EAS            ! Offset parameter to offset block
     >,            BYTE_EAS              ! Bytes parameter to offset block
     >,            TYPE_EAS              ! Type parameter to offset block
     >,            HOST_DATA             ! Host data flag
     >,            LOCAL_DATA            ! Local data flag
C
      PARAMETER   (
     >             BASE_EAS    =  1
     >,            OFFSET_EAS  =  2
     >,            BYTE_EAS    =  3
     >,            TYPE_EAS    =  4
     >,            HOST_DATA   =  1
     >,            LOCAL_DATA  =  2
     >            )
C
C
C --- EQUIVALENCE STATEMENT
C     ---------------------
C
      EQUIVALENCE  ( I4DCB  ,  DCB    )
     >,            ( R4DCB  ,  DCB    )
     >,            ( OFF_TBL, OFF_BLK )
C
C
C --- Entry Point
C
      ENTRY XSCA
C
C
C --- Monitor any arm preselect
C
      DO I = 1, MAX_PRES
C
         IF (XSARM(I).NE.0) THEN
C
            IF (XSOFFST(I).NE.0) THEN
C
C ---          Determine setvalue start and criteria start
C
               STV_START  = XSINDEX(I) + 4
               STV_END    = XSTABLE(STV_START) + STV_START - 1
               CRIT_START = STV_END + 1
               CRIT_END   = XSTABLE(CRIT_START)  + CRIT_START - 1
               OFF_SIZE   = XSTABLE(CRIT_END + 1)
C
C ---          Set up offset block
C
               OFF_START = CRIT_END + 1
               OFF_END   = OFF_START + OFF_SIZE - 1
               OFF_PTR   = 0
               DO J = OFF_START+1, OFF_END
                  OFF_PTR = OFF_PTR + 1
                  OFF_TBL(OFF_PTR) = XSTABLE(J)
               ENDDO
C
C ---          Call criteria evaluation routine
C
               CALL CRIT_EVAL(CRIT_START, CRIT_END, CRIT_STATUS,
     >                        OFF_BLK, I)
C
C ---          If criteria statisfied
C
               IF (CRIT_STATUS) THEN
C
C ---             Set up DCB for directives
C
                  DCB_PTR = 0
                  DO J = STV_START, STV_END
                     DCB(DCB_PTR) = XSTABLE(J)
                     DCB_PTR = DCB_PTR + 1
                  ENDDO
C
C ---             Set up offset block for directives
C
                  J = 1
                  DO WHILE (OFF_BLK(OFFSET_EAS,J,HOST_DATA).NE.0)
                     OFF_BLOCK(BASE_EAS,J,HOST_DATA) =
     >                         OFF_BLK(BASE_EAS,J,HOST_DATA)
                     OFF_BLOCK(OFFSET_EAS,J,HOST_DATA) =
     >                         OFF_BLK(OFFSET_EAS,J,HOST_DATA)
                     OFF_BLOCK(BYTE_EAS,J,HOST_DATA) =
     >                         OFF_BLK(BYTE_EAS,J,HOST_DATA)
                     OFF_BLOCK(TYPE_EAS,J,HOST_DATA) =
     >                         OFF_BLK(TYPE_EAS,J,HOST_DATA)
                     J = J + 1
                  ENDDO
C
C
C ---             Call directive to set value into the CDB
C
                  CALL XDSETIN ( DCB, I4DCB, R4DCB, ERROR_CODE,
     >                           OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C
C ---             Reset preselect variables
C
                  XSARM(I)    = .FALSE.
                  TXSPRUPD(1) = .TRUE.
                  XSOFFST(I)  = 0
C
C ---             Reset criteria value table
C
                  DO J = 1, MAX_CRIT
                     XSCRITVAL(I,J) = NULL_CRIT
                  ENDDO
C
                  PRES_INDEX  = XSINDEX(I)
                  XSINDEX(I)  = 0
                  NEXT_PRES   = PRES_INDEX + XSTABLE(PRES_INDEX)
C
C ---             Perform compress on XSTABLE for the deleted
C                 preselect item
C
                  DO K = NEXT_PRES, XSNXTOFF - 1
                     DO J = 1, MAX_PRES
                        IF (XSINDEX(J).EQ.K) THEN
                           XSINDEX(J) = PRES_INDEX
                        ENDIF
                     ENDDO
                     XSTABLE(PRES_INDEX) = XSTABLE(K)
                     PRES_INDEX = PRES_INDEX + 1
                  ENDDO
C
                  XSNXTOFF = PRES_INDEX
                  XSTABLE(XSNXTOFF) = -1
               ENDIF
            ENDIF
         ENDIF
      ENDDO
C
      RETURN
      END
C
C
C ---------------------------------------------------------------------
C --- Criteria evaluation Routine
C ---------------------------------------------------------------------
C
      SUBROUTINE CRIT_EVAL(CRIT_START, CRIT_END, CRIT_STATUS,
     >                     OFF_BLOCK , PRESCNT)
C
      IMPLICIT NONE
C
C'Purpose
C
C     The function of this routine is to evaluate criteria
C     expression.
C
C'Include file
C
CVAX
CVAX  INCLUDE 'PAGECODE.INC'    !NOFPC
CVAX  INCLUDE 'STDPRES.INC'     !NOFPC
CVAXEND
CSGI
CSGI  INCLUDE 'pagecode.inc'    !NOFPC
CSGI  INCLUDE 'stdpres.inc'     !NOFPC
CSGIEND
CIBM
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'stdpres.inc'     !NOFPC
CIBMEND
C
C
C'Cdb_variables
C
CP    usd8
CP    YXSTRTXRF(*)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:20:34 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      LOGICAL*1
     &  YXSTRTXRF      ! Start of CDB
     &, YXSTRTXRF0     ! Start of Base 0
C$
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,YXSTRTXRF0
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      LOGICAL*1
     &  YXSTRTXRF1     ! Start of Base 1
C$
C$
      COMMON   /XRFTEST1  /
     &  YXSTRTXRF1
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXSTRTXRF2     ! Start of Base 2
C$
C$
      COMMON   /XRFTEST2  /
     &  YXSTRTXRF2
C------------------------------------------------------------------------------
C
C'Local_variables
C
      REAL*8     R8VALUE               ! Real*8 value
C
      REAL*4
     >           STACKR4(MAX_STACK)    ! Real*4 stack buffer
     >,          TEMPR4(2)             ! Temporary real*4 variable
     >,          TEMP_R4               ! Temporary real*4 variable
     >,          OLD_TEMPR4            ! Old Temporary real*4 variable
     >,          R4VALUE               ! Real*4 value
     >,          R4CONSTANT            ! Constant value
C
      INTEGER*4
     >           CURPTR(11)            ! Element in respect with element type
     >,          CUROP                 ! Pointer to operation stack
     >,          CURDAT                ! Pointer to operand stack
     >,          TEMPI4(2)             ! Temporary integer*4 variable
     >,          OLD_TEMPI4            ! Old Temporary integer*4 variable
     >,          EXPR_CNT              ! Expression counter
     >,          I, J, K               ! Counters
     >,          STACKI4(MAX_STACK)    ! Integer*4 stack buffer
     >,          I4VALUE               ! Value from CDB
     >,          BASE                  ! Base number
     >,          OFFSET                ! Label offset
     >,          BYTECNT               ! Number of bytes
     >,          OFF_IND               ! Offset index
     >,          I4CONSTANT            ! Constant value
     >,          CRIT_START            ! Criteria start
     >,          CRIT_END              ! Criteria end
     >,          RTN_BACK              ! Return back label
     >,          VALPTR                ! Old criteria value pointer
     >,          PRESCNT               ! Preselect counter
     >,          OFF_BLOCK(4,MAX_CRIT+1,2)       ! Offset block
     >,          OLD_CRIT_VAL(MAX_CRIT,MAX_PRES) ! Old criteria value
     >,          BASE_EAS              ! Base parameter to offset block
     >,          OFFSET_EAS            ! Offset parameter to offset block
     >,          BYTE_EAS              ! Byte parameter to offset block
     >,          TYPE_EAS              ! Type parameter to offset block
     >,          HOST_DATA             ! Host data flag
     >,          LOCAL_DATA            ! Local data flag
C
      PARAMETER (
     >           BASE_EAS   = 1
     >,          OFFSET_EAS = 2
     >,          BYTE_EAS   = 3
     >,          TYPE_EAS   = 4
     >,          HOST_DATA  = 1
     >,          LOCAL_DATA = 2
     >          )
C
      INTEGER*2
     >           EXPR_SIZE             ! Expression size
     >,          CUR_OPER              ! current operation type
     >,          I2VALUE               ! CDB Value
     >,          I2OFF_IND(2)          ! Label offset
     >,          I2CONSTANT(2)         ! Constant value
C
      INTEGER*1
     >             TRUE_VALUE
     >,            FALSE_VALUE
C
      PARAMETER   (
     >             TRUE_VALUE = 1             ! $FF for VAX host
     >,            FALSE_VALUE= 0
     >            )
C
      LOGICAL*1
     >           STACKL1(MAX_STACK)    ! Logical*1 stack buffer
     >,          CRIT_MET(MAX_OPERATION,MAX_PRES) ! criteria satisfied flag
     >,          TEMPL1(2)             ! Temporary logical*1 variable
     >,          L1VALUE               ! Logical*1 value
     >,          CRIT_STATUS           ! Criteria status
C
      BYTE
     >           STACKI1(MAX_STACK)    ! Integer*1 stack buffer
     >,          TEMPI1(2)             ! Temporary logical*1 variable
     >,          I1CONSTANT(4)         ! Constant value
     >,          OPERATION(0:MAX_OPERATION) ! Expression stack
     >,          OPERAND(MAX_OPERATION)! Operand stack pointer
     >,          RUN_TYPE              ! Operand type of operation executed
     >,          RUN_OPER              ! Operation being executed
     >,          VALUEBUF(8)           ! Value buffer
     >,          XR(0:0)               ! Equivalence to YXSTRTXRF
     >,          XR1(0:0)              ! Equivalence to YXSTRTXRF1
     >,          XR2(0:0)              ! Equivalence to YXSTRTXRF2
CNT  >,          XR3(0:0)              ! Equivalence to YXSTRTXRF3
CNT  >,          XR4(0:0)              ! Equivalence to YXSTRTXRF4
C
     >,          OPER_STRENGTH(0:OPER_NUMBER)    ! Lower number means
     >           /21*0, 4, 4, 4, 4, 4, 4, 7, 6,  ! higher priority
     >               3, 3, 3, 3, 5, 9, 8, 1, 1,
     >               4, 4  /
C
     >,          OPER_CTN(0:OPER_NUMBER)         ! Number of operand
     >           /21*0, 2, 2, 2, 2, 2, 2, 2, 2,  ! for op (0,1,2)
     >               2, 2, 2, 2, 1, 0, 0, 1, 1,
     >               2, 2  /
C
C
C --- EQUIVALENCE STATEMENT
C     ---------------------
C
      EQUIVALENCE  ( R8VALUE    , VALUEBUF   )
     >,            ( R4VALUE    , VALUEBUF   )
     >,            ( I4VALUE    , VALUEBUF   )
     >,            ( I2VALUE    , VALUEBUF   )
     >,            ( L1VALUE    , VALUEBUF   )
     >,            ( I2OFF_IND  , OFF_IND    )
     >,            ( I2CONSTANT , I1CONSTANT )
     >,            ( I2CONSTANT , I4CONSTANT )
     >,            ( I2CONSTANT , R4CONSTANT )
     >,            ( OLD_TEMPI4 , OLD_TEMPR4 )
     >,            ( XR(0)      , YXSTRTXRF  )
     >,            ( XR1(0)     , YXSTRTXRF1 )
     >,            ( XR2(0)     , YXSTRTXRF2 )
CNT  >,            ( XR3(0)     , YXSTRTXRF3 )
CNT  >,            ( XR4(0)     , YXSTRTXRF4 )
C
C
C --- Initialize variables
C
      CRIT_STATUS       = .FALSE.
      EXPR_CNT          = CRIT_START
      EXPR_SIZE         = CRIT_END
      CURPTR(DTYP_I4)   = 1
      CURPTR(DTYP_R4)   = 1
      CURPTR(DTYP_BYTE) = 1
      CURPTR(DTYP_I1)   = 1
      CUROP             = 1
      CURDAT            = 1
      VALPTR            = 0
C
      DO WHILE (EXPR_CNT .LT. EXPR_SIZE)
C
         EXPR_CNT  =  EXPR_CNT + 1
         CUR_OPER  =  XSTABLE(EXPR_CNT)
C
         IF (CUR_OPER .GT. 0) THEN
C
C ---       Add value to stack
C
            IF (CUR_OPER .LT. 20) THEN
               ASSIGN 1000 TO RTN_BACK
               GOTO 10000
1000           CONTINUE
            ELSE
C
C ---          If left parenthesis, add to stack
C
               IF (CUR_OPER .EQ. OP_LPAR) THEN
                  OPERATION(CUROP) = CUR_OPER
                  CUROP = CUROP + 1
               ELSE
C
C
C ---             While the operation on top of the stack has priority
C                 over the current operation, do the operation on the
C                 stack
C
                  DO WHILE ((CUROP .GT. 1) .AND.
     >                    (OPER_STRENGTH(OPERATION(CUROP-1))
     >                    .LE. OPER_STRENGTH(CUR_OPER)))
                     ASSIGN 2000 TO RTN_BACK
                     GOTO 20000
2000                 CONTINUE
                  ENDDO
C
C ---             If not right parenthesis, add operation to stack
C
                  IF (CUR_OPER .NE. OP_RPAR) THEN
                     OPERATION(CUROP) = CUR_OPER
                     CUROP = CUROP + 1
                  ELSE
                     CUROP = CUROP - 1
                  ENDIF
C
               ENDIF
            ENDIF
         ENDIF
      ENDDO
C
C --- Complete evaluation while stack is not empty
C
      DO WHILE (CUROP .NE. 1)
         ASSIGN 3000 TO RTN_BACK
         GOTO 20000
3000     CONTINUE
      ENDDO
C
C --- The result of last element on L1 stack
C
      CRIT_STATUS = STACKL1(1)
      IF ( CRIT_STATUS ) THEN
        DO I = 1,MAX_OPERATION
          CRIT_MET(I,PRESCNT) = .FALSE.
        ENDDO
      ENDIF
C
      GOTO 9999
C
C
C
C-------------------------------------------------------------------
C     ADD VALUE
C     This subroutine adds the current data value to the stack
C-------------------------------------------------------------------
C
10000  CONTINUE
C
C --- If operation is not a constant, get current value from CDB
C
      IF (CUR_OPER .LT. DTYP_CI4)  THEN
C
         I2OFF_IND(1) = XSTABLE(EXPR_CNT+1)
         I2OFF_IND(2) = XSTABLE(EXPR_CNT+2)
         EXPR_CNT     = EXPR_CNT + 2
C
         BASE    = OFF_BLOCK(BASE_EAS  , OFF_IND, HOST_DATA)
         OFFSET  = OFF_BLOCK(OFFSET_EAS, OFF_IND, HOST_DATA)
         BYTECNT = OFF_BLOCK(BYTE_EAS  , OFF_IND, HOST_DATA)
C
C ---    Get criteria value from CDB depending on Base
C
         GOTO (
     >         10010     ! Base 0
     >,        10020     ! Base 1
     >,        10030     ! Base 2
     >,        10040     ! Base 3
     >,        10050     ! Base 4
     >        ) (BASE+1)
C
10010    DO I = 1, BYTECNT
            VALUEBUF(I) = XR(OFFSET + I - 1)
         ENDDO
         GOTO 11000
C
10020    DO I = 1, BYTECNT
            VALUEBUF(I) = XR1(OFFSET + I - 1)
         ENDDO
         GOTO 11000
C
10030    DO I = 1, BYTECNT
            VALUEBUF(I) = XR2(OFFSET + I - 1)
         ENDDO
         GOTO 11000
C
10040    DO I = 1, BYTECNT
CNT         VALUEBUF(I) = XR3(OFFSET + I - 1)
         ENDDO
         GOTO 11000
C
10050    DO I = 1, BYTECNT
CNT         VALUEBUF(I) = XR4(OFFSET + I - 1)
         ENDDO
         GOTO 11000
C
11000    CONTINUE
C
C ---    Add value to value stack
C        I*2 AND R*8 are type cast to I*4 and R*4 respectively
C
         GOTO (
     >         11010,     ! Integer*2
     >         11020,     ! Integer*4
     >         11030,     ! Real*4
     >         11040,     ! Real*8
     >         11050      ! Logical*1
     >        ) CUR_OPER
         CONTINUE
C
11010    CUR_OPER = DTYP_I4
         STACKI4(CURPTR(DTYP_I4)) = I2VALUE
         GOTO 12000
C
11020    STACKI4(CURPTR(DTYP_I4)) = I4VALUE
         GOTO 12000
C
11030    STACKR4(CURPTR(DTYP_R4)) = R4VALUE
         GOTO 12000
C
11040    CUR_OPER = DTYP_R4
         STACKR4(CURPTR(DTYP_R4)) = R8VALUE
         GOTO 12000
C
11050    CUR_OPER = DTYP_I1
         IF ( L1VALUE ) THEN
           STACKI1(CURPTR(DTYP_I1)) = TRUE_VALUE
         ELSE
           STACKI1(CURPTR(DTYP_I1)) = FALSE_VALUE
         ENDIF
C
12000    CONTINUE
C
C --- Constant Integer*1
C
      ELSE IF (CUR_OPER .EQ. DTYP_I1) THEN
         CUR_OPER = DTYP_I1
         I2CONSTANT(1)  = XSTABLE(EXPR_CNT+1)
         I2CONSTANT(2)  = XSTABLE(EXPR_CNT+2)
         EXPR_CNT       = EXPR_CNT + 2
         STACKI1(CURPTR(DTYP_I1)) = I1CONSTANT(1)
C
C --- Constant Integer*4
C
      ELSE IF (CUR_OPER .EQ. DTYP_CI4) THEN
         CUR_OPER = DTYP_I4
         I2CONSTANT(1)  = XSTABLE(EXPR_CNT+1)
         I2CONSTANT(2)  = XSTABLE(EXPR_CNT+2)
         EXPR_CNT       = EXPR_CNT + 2
         STACKI4(CURPTR(DTYP_I4)) = I4CONSTANT
C
C --- Constant Real*4
C
      ELSE IF (CUR_OPER .EQ. DTYP_CR4) THEN
         CUR_OPER = DTYP_R4
         I2CONSTANT(1)  = XSTABLE(EXPR_CNT+1)
         I2CONSTANT(2)  = XSTABLE(EXPR_CNT+2)
         EXPR_CNT       = EXPR_CNT + 2
         STACKR4(CURPTR(DTYP_R4)) = R4CONSTANT
      ENDIF
C
C --- Add pointer in operation stack
C
      OPERAND(CURDAT)  = CUR_OPER
      CURDAT           = CURDAT + 1
      CURPTR(CUR_OPER) = CURPTR(CUR_OPER) + 1
C
      GOTO RTN_BACK
C
C
C----------------------------------------------------------------------
C     DO_OPERATION
C     This subroutine is called when we want to execute the last
C     operation on the stack, it's updating the stack ptr works only
C     with operation using zero, one or two operand(s)
C----------------------------------------------------------------------
C
20000 CONTINUE
C
      CUROP    = CUROP  - 1
      CURDAT   = CURDAT - 1
      RUN_OPER = OPERATION(CUROP)
      RUN_TYPE = OPERAND(CURDAT)
C
      IF (OPER_CTN(RUN_OPER) .GT. 0) THEN
         CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
C
         GOTO (
     >          20100,      ! Integer*2
     >          20010,      ! Integer*4
     >          20020,      ! Real*4
     >          20100,      ! Real*8
     >          20030,      ! Logical*1
     >          20100,      ! Spare
     >          20100,      ! Spare
     >          20100,      ! Spare
     >          20100,      ! Spare
     >          20100,      ! Spare
     >          20040       ! Integer*1
     >         ) RUN_TYPE
C
20010    TEMPI4(2) = STACKI4(CURPTR(DTYP_I4))
         GOTO 20100
C
20020    TEMPR4(2) = STACKR4(CURPTR(DTYP_R4))
         GOTO 20100
C
20030    TEMPL1(2) = STACKL1(CURPTR(DTYP_BYTE))
         GOTO 20100
C
20040    TEMPI1(2) = STACKI1(CURPTR(DTYP_I1))
C
20100    CONTINUE
C
C ---    If there is a second operand
C
         IF (OPER_CTN(RUN_OPER) .EQ. 2) THEN
            CURDAT = CURDAT - 1
C
C ---       If type is the same
C
            IF (RUN_TYPE .EQ. OPERAND(CURDAT)) THEN
               CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
C
               GOTO (
     >                20200,    ! Integer*2
     >                20110,    ! Integer*4
     >                20120,    ! Real*4
     >                20200,    ! Real*8
     >                20130,    ! Logical*1
     >                20200,    ! Spare
     >                20200,    ! Spare
     >                20200,    ! Spare
     >                20200,    ! Spare
     >                20200,    ! Spare
     >                20140     ! Integer*1
     >              ) RUN_TYPE
C
20110          TEMPI4(1) = STACKI4(CURPTR(RUN_TYPE))
               GOTO 20200
C
20120          TEMPR4(1) = STACKR4(CURPTR(RUN_TYPE))
               GOTO 20200
C
20130          TEMPL1(1) = STACKL1(CURPTR(RUN_TYPE))
               GOTO 20200
C
20140          TEMPI1(1) = STACKI1(CURPTR(RUN_TYPE))
C
20200          CONTINUE
C
            ELSE IF (RUN_TYPE .EQ. DTYP_I4)  THEN
               RUN_TYPE         = DTYP_R4
               TEMPR4(2)        = TEMPI4(2)
               CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
               TEMPR4(1)        = STACKR4(CURPTR(RUN_TYPE))
            ELSE
               RUN_TYPE         = DTYP_R4
               CURPTR(OPERAND(CURDAT)) = CURPTR(OPERAND(CURDAT)) - 1
               TEMPR4(1)        = STACKI4(CURPTR(OPERAND(CURDAT)))
               CONTINUE
            ENDIF
         ENDIF
      ENDIF
C
C
C --- DO Operation
C     ------------
C
      GOTO (
     >       21010,           !  .EQ.
     >       21020,           !  .GE.
     >       21030,           !  .LE.
     >       21040,           !  .NE.
     >       21050,           !  .LT.
     >       21060,           !  .GT.
     >       21070,           !  .OR.
     >       21080,           !  .AND.
     >       21090,           !   +
     >       21100,           !   -
     >       21110,           !   *
     >       21120,           !   /
     >       21130,           !   .NOT.
     >       21140,           !   (
     >       21150,           !   )
     >       21160,           !   Unary -
     >       21170,           !   Unary +
     >       21180,           !   .GA. (above)
     >       21190            !   .LB. (Below)
     >      ) (RUN_OPER - 20)
C
      CONTINUE        ! ERROR UNKNOWN OPERATION
C
C
C --- .EQ. operation (21)
C     -------------------
C
21010 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .EQ. TEMPI4(2)
         CURPTR(DTYP_BYTE)          = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         TEMP_R4 = ABS(TEMPR4(1) - TEMPR4(2))
         IF (TEMP_R4.LE.0.5) THEN
            STACKL1(CURPTR(DTYP_BYTE)) = .TRUE.
         ELSE
            STACKL1(CURPTR(DTYP_BYTE)) = .FALSE.
         ENDIF
         CURPTR(DTYP_BYTE)          = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_I1) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI1(1) .EQ. TEMPI1(2)
         CURPTR(DTYP_BYTE)          = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- .GE. operation (22)
C     -------------------
C
21020 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .GE. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .GE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- .LE. operation (23)
C     -------------------
C
21030 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .LE. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .LE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- .NE. operation (24)
C     -------------------
C
21040 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .NE. TEMPI4(2)
         CURPTR(DTYP_BYTE) = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .NE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- .LT. operation (25)
C     -------------------
C
21050 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .LT. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .LT. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- .GT. operation (26)
C     --------------------
C
21060 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .GT. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .GT. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- .OR. operation (27)
C     --------------------
C
21070 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPL1(1) .OR. TEMPL1(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- .AND. operation (28)
C     --------------------
C
21080 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPL1(1) .AND. TEMPL1(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C ---  '+' Plus operation (29)
C      -----------------------
C
21090 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) + TEMPI4(2)
         CURPTR(DTYP_I4)     = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) + TEMPR4(2)
         CURPTR(DTYP_R4) = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C ---  '-' Minus operation (30)
C      ------------------------
C
21100 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) - TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) - TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C ---  '*' Multiply operation (31)
C      ---------------------------
C
21110 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) * TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) * TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C ---  '/' Divide operation (32)
C      -------------------------
C
21120 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) / TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) / TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- .NOT. operation (33)
C     --------------------
C
21130 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = .NOT. TEMPL1(2)
         CURPTR(DTYP_BYTE) = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- '(' operation (34)
C     ------------------
C
21140 GOTO 21999
C
C
C --- ')' operation (35)
C     ------------------
C
21150 GOTO 21999
C
C
C --- Unary '-' operation (36)
C     ------------------------
C
21160 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKI4(CURPTR(DTYP_I4)) = - TEMPI4(2)
         CURPTR(DTYP_I4) = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = - TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- Unary '+' operation (37)
C     ------------------------
C
21170 IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      GOTO 21999
C
C
C --- .GA. operation (38)
C     -------------------
C
21180 CONTINUE
      VALPTR = VALPTR + 1
      OLD_TEMPI4 = OLD_CRIT_VAL(VALPTR,PRESCNT)
      IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = (OLD_TEMPI4.LT.TEMPI4(2))
     >                                .AND. (TEMPI4(1) .GE. TEMPI4(2))
         OLD_TEMPI4 = TEMPI4(1)
         IF ( CRIT_MET(VALPTR,PRESCNT) )
     >      STACKL1(CURPTR(DTYP_BYTE))=.TRUE.
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = (OLD_TEMPR4.LT.TEMPR4(2))
     >                                .AND. (TEMPR4(1) .GE. TEMPR4(2))
         OLD_TEMPR4 = TEMPR4(1)
         IF ( CRIT_MET(VALPTR,PRESCNT) )
     >      STACKL1(CURPTR(DTYP_BYTE))=.TRUE.
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      OLD_CRIT_VAL(VALPTR,PRESCNT) = OLD_TEMPI4
      CRIT_MET(VALPTR,PRESCNT) = STACKL1(CURPTR(DTYP_BYTE)-1)
      GOTO 21999
C
C
C --- .LB. operation (39)
C     -------------------
C
21190 CONTINUE
      VALPTR = VALPTR + 1
      OLD_TEMPI4 = OLD_CRIT_VAL(VALPTR,PRESCNT)
      IF (RUN_TYPE .EQ. DTYP_I4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = (OLD_TEMPI4.GT.TEMPI4(2))
     >                                .AND. (TEMPI4(1) .LE. TEMPI4(2))
         OLD_TEMPI4 = TEMPI4(1)
         IF ( CRIT_MET(VALPTR,PRESCNT) )
     >      STACKL1(CURPTR(DTYP_BYTE))=.TRUE.
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = (OLD_TEMPR4.GT.TEMPR4(2))
     >                                .AND. (TEMPR4(1) .LE. TEMPR4(2))
         OLD_TEMPR4 = TEMPR4(1)
         IF ( CRIT_MET(VALPTR,PRESCNT) )
     >      STACKL1(CURPTR(DTYP_BYTE))=.TRUE.
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE
      ENDIF
      OLD_CRIT_VAL(VALPTR,PRESCNT) = OLD_TEMPI4
      CRIT_MET(VALPTR,PRESCNT) = STACKL1(CURPTR(DTYP_BYTE)-1)
C
21999 CONTINUE
      GOTO RTN_BACK
C
9999  CONTINUE
      RETURN
      END
