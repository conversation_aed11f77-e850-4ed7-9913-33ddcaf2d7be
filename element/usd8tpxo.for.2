C'Name         Programmable Touch Panel output module
C'Version      3.0
C'Module_ID    TPXO
C'PDD
C'Customer     Standard Software
C'Author       <PERSON><PERSON>'Date         April 1990
C'Application  Driver for the Electroluminescent Display by Emerald
C'
C'Revision_History
C
C File: /cae1/ship/usd8tpxo.for.2
C       Modified by: <PERSON><PERSON>
C       Fri Nov  8 14:06:09 1991
C       < added the index parameter to the directives call >
C
C File: /cae/if/el/usd8tpxo.for.2
C       Modified by: HA
C       Sun Nov  3 11:38:21 1991
C       < Modified include file names >
C'
C'
C'Purpose      - Display static information on the unit
C'             - Display dynamic information on the unit
C'             - Send the touch area information
C'Inputs       - CDB variables
C'Outputs      - RS-232 serial line hooked-up to the DeeCo unit
C'             - CDB variables
C'Interface
C'
C ---------------------------
      SUBROUTINE USD8TPXO
C ---------------------------
C
      IMPLICIT NONE
CVAX
CVAX  INCLUDE 'IO.PAR/NOLIST'                    !NOFPC   Sys$qio param.
CVAXEND
CIBM
      INCLUDE 'cae_io.inc'                       !NOFPC   cae_io param.
CIBMEND
      INCLUDE 'pageparm.inc'                     !NOFPC
      INCLUDE 'pagecode.inc'                     !NOFPC
      INCLUDE 'usd8xd.inc'                        !NOFPC
      INCLUDE 'usd8tp.inc'                        !NOFPC   Setup desc.
      INCLUDE 'stdtptb.inc'                      !NOFPC   Number table
      INCLUDE 'stdtpem.inc'                      !NOFPC   Emerald param.
CVAX
CVAX  INCLUDE 'stdtpas.inc'                      !NOFPC   AST Decl.
CVAXEND
      INCLUDE 'usd8tpio.inc'                      !NOFPC   I/O Decl.
      INCLUDE 'usd8tpxo.inc'                      !FPC     Program Decl.
C
C
C ---------------------------
C --- Program Entry Point ---
C ---------------------------
C
      ENTRY TPXO
C
C -------------------------------------------------------
C --- Dont execute if disable or if there is an error ---
C -------------------------------------------------------
C
      IF (TPDISAB .OR. (TPERROR .NE. ERR_NONE)) THEN
       RETURN
      ENDIF
C
C ---------------------------------------------------
C --- File update request        monitoring Logic ---
C ---------------------------------------------------
C
      IF (TPFILUPD) THEN
C
       TPFILUPD = .FALSE.              ! Clear all request
       TPRESET  = .FALSE.
       TPDEVRES = 0
       TPXIREST = .TRUE.
       TPMPREST = .TRUE.
C
       FILUPDIP = .TRUE.               ! Prepare to update the pages
C
       FIRSTPAS = .TRUE.               ! Force a First Pass
       FPSMODE  = FPMD_FUD
       FPSSTEP  = 1
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
C
C --------------------------------------------------------
C --- Reset all devices request       monitoring Logic ---
C --------------------------------------------------------
C
      ELSE IF (TPRESET) THEN
       TPRESET  = .FALSE.              ! Clear all request
       TPFILUPD = .FALSE.
       TPDEVRES = 0
       TPXIREST = .TRUE.
       TPMPREST = .TRUE.
C
       DO I=1, INDEV                   ! Prepare to reset all devices
        DEVRESET(I) = .TRUE.
       ENDDO
       FILUPDIP = .TRUE.               ! Prepare to update the pages
C
       FIRSTPAS = .TRUE.               ! Force a First Pass
       FPSMODE  = FPMD_TRS
       FPSSTEP  = 1
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
C
C -----------------------------------------------------
C --- Reset request for one device monitoring Logic ---
C -----------------------------------------------------
C
      ELSE IF (TPDEVRES .NE. 0) THEN
       IF ((TPDEVRES .GT. 0) .AND. (TPDEVRES .LE. INDEV)) THEN
        TPFILUPD = .FALSE.             ! Clear all request
        TPRESET  = .FALSE.
        TPXIREST = .TRUE.
        TPMPREST = .TRUE.
C
        DO I=1, INDEV                  ! Cancel the other request
         DEVRESET(I) = .FALSE.
        ENDDO
        DEVRESET(TPDEVRES) = .TRUE.    ! Reset the wanted unit
        FILUPDIP = .FALSE.             ! No page update
C
        FIRSTPAS = .TRUE.              ! Force a First Pass
        FPSMODE  = FPMD_DRS
        FPSSTEP  = 1
        FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
C
       ENDIF
       TPDEVRES = 0                    ! Cancel the request
      ENDIF
C
C
C ------------------------
C --- First Pass Logic ---
C ------------------------
C
C --- Process all devices
C
      IF (FIRSTPAS) THEN
       GOTO (
     .       1000                      ! Initial Variable Setting  - FPST_IVS
     .,      1100                      ! Close File                - FPST_CFL
     .,      1200                      ! Assign Serial Port        - FPST_ASP
     .,      1300                      ! Init Devices              - FPST_IDV
     .,      1400                      ! Open Files                - FPST_OFL
     .,      1500                      ! Read File Header          - FPST_RFH
     .,      1600                      ! Read File Directory       - FPST_RFD
     .,      1700                      ! Send Initial Display      - FPST_SID
     .,      1800                      ! Complete First Pass       - FPST_CFP
     .,      1900                      ! Complete Device reset     - FPST_CDR
     .           ) FPSSTATE
       GOTO  1999
C
C
C -------------------------------------------------------
C ---   First Pass   ---   Initial Variable Setting   ---
C -------------------------------------------------------
C
1000   CONTINUE
C
       IF (LOADCNTR .LE. LOAD_DEL) THEN          ! load time start delay
        LOADCNTR = LOADCNTR + 1
        RETURN
       ENDIF
C
       TPERROR = ERR_NONE
C
C --- Initial setting flag for other module using TPIOCOMM common block
C
       INITIRDY = .FALSE.
C
C --- page.dat AST routine addresse assignation
C
CVAX
CVAX   PGDTASRA(1)  = %LOC(PGDTAST1)
CVAX   PGDTASRA(2)  = %LOC(PGDTAST2)
CVAX   PGDTASRA(3)  = %LOC(PGDTAST3)
CVAX   PGDTASRA(4)  = %LOC(PGDTAST4)
CVAX   PGDTASRA(5)  = %LOC(PGDTAST5)
CVAX   PGDTASRA(6)  = %LOC(PGDTAST6)
CVAX   PGDTASRA(7)  = %LOC(PGDTAST7)
CVAX   PGDTASRA(8)  = %LOC(PGDTAST8)
CVAX   PGDTASRA(9)  = %LOC(PGDTAST9)
CVAX   PGDTASRA(10) = %LOC(PGDTAS10)
CVAXEND
C
C --- Serial I/O Read AST Routine Addresses assignation
C
CVAX
CVAX   SIOASTRA(1) = %LOC(SIOASTR1)
CVAX   SIOASTRA(2) = %LOC(SIOASTR2)
CVAX   SIOASTRA(3) = %LOC(SIOASTR3)
CVAXEND
C
C --- Serial I/O Write AST Routine Addresses assignation
C
CVAX
CVAX   SIOASTWA(1) = %LOC(SIOASTW1)
CVAX   SIOASTWA(2) = %LOC(SIOASTW2)
CVAX   SIOASTWA(3) = %LOC(SIOASTW3)
CVAXEND
C
C --- If a page update is in progress all the device are reset
C
       FILUPDIP = .TRUE.
       DO I=1 , INDEV
        SIOASSIF(I) = .FALSE.
        DEVRESET(I) = .TRUE.
        SIOWBUFP(I) = 1
        SIOWBLIS(I) = SUCCESS
       ENDDO
C
C --- Simulate buff_close answer
C
CVAX
CVAX   PGDTRTCD(1) = 1
CVAX   PGDTFLAG(1) = .TRUE.
CVAXEND
CSGI
CSGI   BIOICLOS.STATUS = 1
CSGIEND
CIBM
       BIOCLOFS = SUCCESS
       BIOCLOIS = SUCCESS
CIBMEND
C
C --- Prepare to jump to the next state
C
       FPSSTEP  = 2
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
       GOTO 1999
C
C ---------------------------------------
C ---   First Pass   ---   Close File ---
C ---------------------------------------
C
1100   CONTINUE
C
C --- Initial setting flag for other module using TPIOCOMM common block
C
       INITIRDY = .FALSE.
C
C --- Close the File
C
CVAX
CVAX   PGDTRTCD(1) = 0
CVAX   PGDTFLAG(1) = .FALSE.
CVAX   CALL BUFF_CLOSE(PGDTUNIT
CVAX .,                PGDTRTCD(1)
CVAX .,                PGDTFLAG(1)
CVAX .,                PGDTAST1)
CVAXEND
CSGI
CSGI   BIOICLOS.FD = BIOICLOS.FD
CSGI   CALL CAE_IO_CLOSE(BIOICLOS, PRIORITY)
CSGIEND
CIBM
       CALL CAE_IO_CLOSE(BIOCLOFS
     .,                  BIOCLOIS
     .,                  %VAL(PGDTUNIT))
CIBMEND
C
C --- Prepare to jump to the next state
C
       FPSSTEP  = FPSSTEP + 1
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
       GOTO 1999
C
C -------------------------------------------------
C ---   First Pass   ---   Assign Serial Port   ---
C -------------------------------------------------
C
1200   CONTINUE
       DO I=1 , INDEV                  ! Loop for all device
        IF (DEVRESET(I)) THEN          ! Assigned the reseted one
C
C --- Deassign the ports
C
         IF (SIOASSIF(I)) THEN
CVAX
CVAX      SIOASIEV(I) = SYS$DASSGN(SIOCHAN(I))
CVAXEND
CSGI
CSGI      CALL close_el(%VAL(I))
CSGIEND
CIBM
          CALL CAE_IO_CLOSE(SIOCLOFS(I)
     .,                     SIOCLOIS(I)
     .,                     %VAL(SIOCHAN(I)))
IBMEND
         ENDIF
C
C --- Assign the ports
C
CVAX
CVAX     SIOASIEV(I) = SYS$ASSIGN(SIOLOGNM(I), SIOCHAN(I), , )
CVAX     SIOASSIF(I) = ((SIOASIEV(I).EQ.1) .AND. (SIOCHAN(I).GT.0))
CVAXEND
CSGI
CSGI     SIOASSIF(I) = input_init(%VAL(I))
CSGIEND
CIBM
         J = CAE_TRNL(SIOLOGNM(I)      ! Get port name via logical name
     .,               JD
     .,               SIOFILNM(I)
     .,               0)
         IF (J.EQ.1) THEN
          SIOFILNC(JD+1,I) = '\0'
         ENDIF
C
         CALL CAE_IO_OPEN(SIOOPEFS(I)
     .,                   SIOOPEIS(I)
     .,                   SIOCHAN(I)
     .,                   %VAL(1)
     .,                   SIOFILNM(I)
     .,                   %VAL(SIOOLDRW))
IBMEND
        ENDIF
       ENDDO
C
C --- Prepare to jump to the next state
C
       FPSSTEP  = FPSSTEP + 1
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
       GOTO 1999
C
C -------------------------------------------------
C ---   First Pass   ---   Assign Serial Port   ---
C -------------------------------------------------
C
1300   CONTINUE
       ID = 0                          ! Assigned ports counter
       DO I=1 , INDEV                  ! Loop for all device
        IF (DEVRESET(I)) THEN          ! Assigned the reseted one
CIBM
         IF (SIOOPEFS(I) .NE. SUCCESS) THEN
          TPERROR = ERR_S_AS
          RETURN
         ELSE IF (SIOOPEIS(I) .NE. SUCCESS) THEN
          GOTO 1999
         ELSE
          SIOASSIF(I) = .TRUE.
         ENDIF
IBMEND
C
C --- Initial process of the assigned device
C
         IF (SIOASSIF(I)) THEN
          SIOWBUIN(I) = 0
          ID = ID + 1
C
C --- Send a reset command to the device
C
          SIOWBUFF(SIOWBUIN(I)+1, I) = ESC
          SIOWBUFF(SIOWBUIN(I)+2, I) = EL_RESTART
          SIOWBUIN(I)                = SIOWBUIN(I) + 2
C
C --- Force a page update and use the initial page
C
          DEVPANUM(I) = 0
          TPXOREQP    = -1
          TPXOPAGE(I) = INITIALP(I)
C
         ENDIF
        ENDIF
       ENDDO
C
C --- If no ports where assigned at the initial first pass
C
       IF ((ID .EQ. 0) .AND. (FPSMODE .EQ. FPMD_IRS))THEN
        TPERROR = ERR_S_NP
        RETURN
       ENDIF
C
       FPSRESCT = 1
C
C --- Prepare to jump to the next state
C
       FPSSTEP  = FPSSTEP + 1
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
       GOTO 1999
C
C --------------------------------------
C ---   First Pass   ---   Open File ---
C --------------------------------------
C
1400   CONTINUE
C
       FPSRESCT = FPSRESCT + 1         ! Increment the reset timer
C
C --- Buffered I/O in Progress monitoring logic (and error checking)
C
CVAX
CVAX   IF (.NOT. PGDTFLAG(1)) THEN
CVAX    RETURN
CVAX   ELSE IF (PGDTRTCD(1) .NE. 1) THEN
CVAX    TPERROR = ERR_P_CL
CVAX    RETURN
CVAX   ENDIF
CVAXEND
CSGI
CSGI   IF      (BIOICLOS.STATUS .EQ. 999) THEN
CSGI    RETURN
CSGI   ELSE IF (BIOICLOS.STATUS .NE.   1) THEN
CSGI    TPERROR = ERR_P_CL
CSGI    RETURN
CSGI   ENDIF
CSGIEND
CIBM
       IF (BIOCLOFS .NE. SUCCESS) THEN
        TPERROR = ERR_P_CL
        RETURN
       ELSE IF (BIOCLOIS .NE. SUCCESS) THEN
        RETURN
       ENDIF
CIBMEND
C
C --- Open the file
C
CVAX
CVAX   PGDTRTCD(1) = 0
CVAX   PGDTFLAG(1) = .FALSE.
CVAX   CALL BUFF_OPEN(PGDTUNIT
CVAX .,               PGDTFLNM
CVAX .,               MAX_ORECLEN
CVAX .,               BIOOLDRD
CVAX .,               BIOMAXRC
CVAX .,               PGDTNAMV
CVAX .,               PGDTRTCD(1)
CVAX .,               PGDTFLAG(1)
CVAX .,               PGDTAST1
CVAX .,               BIOEXTSZ)
CVAXEND
CSGI
CSGI   I = CAE_TRNL('PAGE.DAT', PGDTLEN, PGDTFLNM, 0)
CSGI   IF (I. NE. 1) THEN
CSGI    CALL REV_CURR(PGDTFLNM
CSGI .,               PGDTFLNM
CSGI .,               'dat'
CSGI .,               .FALSE.
CSGI .,               1
CSGI .,               PGDTRIEV)
CSGI    ID = INDEX(PGDTFLNM,' ')
CSGI    PGDTFLNM(ID:ID) = '\0'
CSGI   ELSE
CSGI    PGDTRIEV = 0
CSGI    PGDTLEN = PGDTLEN + 1
CSGI    PGDTFLNM(PGDTLEN:PGDTLEN) = '\0'
CSGI   ENDIF
CSGI   IF (PGDTRIEV .NE. 0) THEN
CSGI    TPERROR = ERR_P_RC
CSGI    RETURN
CSGI   ENDIF
CSGI   BIOIOPEN.FILE_NAMEP = %LOC(PGDTFLNM)
CSGI   BIOIOPEN.FD.RECORD_SIZE = MAX_ORECLEN
CSGI   BIOIOPEN.O_FLAG = BIOOLDRD
CSGI   CALL CAE_IO_OPEN(BIOIOPEN, PRIORITY)
CSGIEND
CIBM
       I = CAE_TRNL('PAGE_DAT'                   ! Get page data file name via
     .,             PGDTLEN                      !     logical name
     .,             PGDTNAMV
     .,             0)
C
       IF (I .NE. 1) THEN                        ! Use page data file default
        CALL REV_CURR(PGDTFLNM                   !      name if logical absent
     .,               PGDTNAMV
     .,               'dat'
     .,               .FALSE.
     .,               1
     .,               PGDTRIEV)
        IF (PGDTRIEV .NE. 0) THEN
         TPERROR = ERR_P_RC
         RETURN
        ENDIF
       ENDIF
C
       ID = INDEX(PGDTNAMV,' ')                  ! Add a null terminator
       PGDTNAMV(ID:ID) = '\0'
C
       CALL CAE_IO_OPEN(BIOOPEFS                 ! Open the page data file
     .,                 BIOOPEIS
     .,                 PGDTUNIT
     .,                 %VAL(MAX_ORECLEN)
     .,                 PGDTNAMV
     .,                 %VAL(BIOOLDRD))

       IF (PGDTRIEV .NE. 0) THEN
        TPERROR = ERR_P_RC
        RETURN
       ENDIF
CIBMEND
C
C --- Prepare to jump to the next state
C
       FPSSTEP  = FPSSTEP + 1
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
       GOTO 1999
C
C -----------------------------------------------
C ---   First Pass   ---   Read File Header   ---
C -----------------------------------------------
C
1500   CONTINUE
       FPSRESCT = FPSRESCT + 1         ! Increment the device reset timer
C
C --- Buffered I/O in Progress monitoring logic (and error checking)
C
CVAX
CVAX   IF (.NOT. PGDTFLAG(1)) THEN
CVAX    RETURN
CVAX   ELSE IF (PGDTRTCD(1) .NE. 1) THEN
CVAX    TPERROR = ERR_P_OP
CVAX    RETURN
CVAX   ENDIF
CVAXEND
CSGI
CSGI   IF      (BIOIOPEN.STATUS .EQ. 999) THEN
CSGI    RETURN
CSGI   ELSE IF (BIOIOPEN.STATUS .NE.   1) THEN
CSGI    TPERROR = ERR_P_OP
CSGI    RETURN
CSGI   ENDIF
CSGIEND
CIBM
       IF (BIOOPEFS .NE. SUCCESS) THEN
        TPERROR = ERR_P_OP
        RETURN
       ELSE IF (BIOOPEIS .NE. SUCCESS) THEN
        RETURN
       ENDIF
CIBMEND
C
C --- Read file Header
C
CVAX
CVAX   PGDTRTCD(1) = 0
CVAX   PGDTFLAG(1) = .FALSE.
CVAX   BIORECNB    = 1
CVAX   BIOBYTNB    = 1
CVAX   BIOBYTCT    = MAX_HDR * 4
CVAX   CALL BUFF_READ(PGDTUNIT
CVAX .,               %DESCR(PGDTHEAD)
CVAX .,               BIORECNB
CVAX .,               BIOBYTNB
CVAX .,               BIOBYTCT
CVAX .,               PGDTRTCD(1)
CVAX .,               PGDTFLAG(1)
CVAX .,               PGDTAST1)
CVAXEND
CSGI
CSGI   BIOIHEAD.FD.FD           = BIOIOPEN.FD.FD
CSGI   BIOIHEAD.FD.RECORD_SIZE  = MAX_ORECLEN
CSGI   BIOIHEAD.RECORD_NUM      = 0
CSGI   BIOIHEAD.AMOUNT          = MAX_HDR * 4
CSGI   BIOIHEAD.POSITION        = 0
CSGI   BIOIHEAD.BUFFERP         = %LOC(PGDTHEAD)
CSGI   CALL CAE_IO_READ(BIOIHEAD, PRIORITY)
CSGIEND
CIBM
       BIORECNB = 0
       BIOBYTCT = MAX_HDR * 4
       BIOBYTNB = 0
       CALL CAE_IO_READ(BIOHDRFS
     .,                 BIOHDRIS
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 PGDTHEAD
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Prepare to jump to the next state
C
       FPSSTEP  = FPSSTEP + 1
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
       GOTO 1999
C
C --------------------------------------------------
C ---   First Pass   ---   Read File Directory   ---
C --------------------------------------------------
C
1600   CONTINUE
       FPSRESCT = FPSRESCT + 1         ! Increment the device reset timer
C
C --- Buffered I/O in Progress monitoring logic (and error checking)
C
CVAX
CVAX   IF (.NOT. PGDTFLAG(1)) THEN
CVAX    RETURN
CVAX   ELSE IF (PGDTRTCD(1) .NE. 1) THEN
CVAX    TPERROR = ERR_P_HD
CVAX    RETURN
CVAX   ENDIF
CVAXEND
CSGI
CSGI   IF      (BIOIHEAD.STATUS .EQ. 999) THEN
CSGI    RETURN
CSGI   ELSE IF (BIOIHEAD.STATUS .NE.   1) THEN
CSGI    TPERROR = ERR_P_HD
CSGI    RETURN
CSGI   ENDIF
CSGIEND
CIBM
       IF (BIOHDRFS .NE. SUCCESS) THEN
        TPERROR = ERR_P_HD
        RETURN
       ELSE IF (BIOHDRIS .NE. SUCCESS) THEN
        RETURN
       ENDIF
CIBMEND
C
C --- Read File Directory
C
CVAX
CVAX   BIORECNB    = 1
CVAX   BIOBYTNB    = PGDTHEAD(FIL_CRTOFF)
CVAX   BIOBYTCT    = PGDTHEAD(FIL_CRTLEN)
CVAX   PGDTRTCD(1) = 0
CVAX   PGDTFLAG(1) = .FALSE.
CVAX   CALL BUFF_READ(PGDTUNIT
CVAX .,               %DESCR(PGDTDIR)
CVAX .,               BIORECNB
CVAX .,               BIOBYTNB
CVAX .,               BIOBYTCT
CVAX .,               PGDTRTCD(1)
CVAX .,               PGDTFLAG(1)
CVAX .,               PGDTAST1 )
CVAXEND
CSGI
CSGI   BIOIDIR.FD.FD           = BIOIOPEN.FD.FD
CSGI   BIOIDIR.FD.RECORD_SIZE  = MAX_ORECLEN
CSGI   BIOIDIR.RECORD_NUM      = 0
CSGI   BIOIDIR.AMOUNT          = PGDTHEAD(FIL_CRTLEN)
CSGI   BIOIDIR.POSITION        = PGDTHEAD(FIL_CRTOFF) - 1
CSGI   BIOIDIR.BUFFERP         = %LOC(PGDTDIR)
CSGI   CALL CAE_IO_READ(BIOIDIR, PRIORITY)
CSGIEND
CIBM
       BIORECNB = 0
       BIOBYTCT = PGDTHEAD(FIL_CRTLEN)
       BIOBYTNB = PGDTHEAD(FIL_CRTOFF) - 1
       CALL CAE_IO_READ(BIODIRFS
     .,                 BIODIRIS
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 PGDTDIR
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Prepare to jump to the next state
C
       FPSSTEP  = FPSSTEP + 1
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
       GOTO 1999
C
C ---------------------------------------------------
C ---   First Pass   ---   Send Initial Display   ---
C ---------------------------------------------------
C
1700   CONTINUE
C
       IF (FPSRESCT .LE. RESET_TM) THEN
        FPSRESCT = FPSRESCT + 1        ! Device reset time is not expired
        RETURN
       ENDIF
C
C --- Serial I/O in Progress monitoring logic
C
       DO I=1 , INDEV
        IF (SIOASSIF(I)) THEN
CVAX
CVAX     IF (.NOT.SIOWRFLG(I)) THEN
CVAX      RETURN                       ! Buff I/O not completed
CVAX     ENDIF
CVAX     IF ((SIOWRIEV(I)   .NE. 1) .OR.
CVAX .       (SIOWRSTB(1,I) .NE. 1) .OR.
CVAX .       (SIOWRSTB(3,I) .NE. 0)) THEN
CVAX      TPERROR = ERR_S_CM           ! Buff I/O completed with error
CVAX      RETURN
CVAX     ENDIF
CVAXEND
CIBM
         IF (SIOWBUIS(I) .NE. SUCCESS) THEN
          RETURN                       ! Buff I/O not completed
         ENDIF
CIBMEND
        ENDIF
       ENDDO
C
       FPSRESCT = 0                    ! Clear the reset timer
       DO I=1 , INDEV
        IF (DEVRESET(I) .AND. SIOASSIF(I)) THEN
         SBI = SIOWBUIN(I)             ! Local Buffer index
C
         SIOWBUFF(SBI+1,I) = I1PORT_2_CONTROL     ! Input port initialization
         SIOWBUFF(SBI+2,I) = I2PORT_2_CONTROL
         SIOWBUFF(SBI+3,I) = I3PORT_2_CONTROL
         SIOWBUFF(SBI+4,I) = I4PORT_2_CONTROL
         SIOWBUFF(SBI+5,I) = I5PORT_2_CONTROL
         SIOWBUFF(SBI+6,I) = I6PORT_2_CONTROL
         SIOWBUFF(SBI+7,I) = I7PORT_2_CONTROL
         SIOWBUFF(SBI+8,I) = I8PORT_2_CONTROL
         SBI = SBI + 8
C
         SIOWBUFF(SBI+1,I) = ESC       ! Clear Text cursor
         SIOWBUFF(SBI+2,I) = DISA_MONITOR_MODE
         SBI = SBI + 2
C
         SIOWBUFF(SBI+1,I) = ESC       ! Change the page number
         SIOWBUFF(SBI+2,I) = SET_ACTIVE_PAGE
         SIOWBUFF(SBI+3,I) = INUMBER(3,1)
         SBI = SBI + 3
C
         SIOWBUFF(SBI+1,I) = ESC       ! Clear Text cursor
         SIOWBUFF(SBI+2,I) = OFF_CURSOR
         SBI = SBI + 2
C
         SIOWBUFF(SBI+1, I) = ESC      ! Clear Screen
         SIOWBUFF(SBI+2, I) = CLR_PANEL
         SBI = SBI + 2
C
         SIOWBUFF(SBI+1, I) = ESC      ! ready message
         SIOWBUFF(SBI+2, I) = GRAPHICS_TEXT
         SIOWBUFF(SBI+3, I) = INUMBER(1,16)
         SIOWBUFF(SBI+4, I) = INUMBER(2,16)
         SIOWBUFF(SBI+5, I) = INUMBER(3,16)
         SIOWBUFF(SBI+6, I) = INUMBER(1,16)
         SIOWBUFF(SBI+7, I) = INUMBER(2,16)
         SIOWBUFF(SBI+8, I) = INUMBER(3,16)
         SIOWBUFF(SBI+9, I) = ICHAR('R')
         SIOWBUFF(SBI+10,I) = ICHAR('e')
         SIOWBUFF(SBI+11,I) = ICHAR('a')
         SIOWBUFF(SBI+12,I) = ICHAR('d')
         SIOWBUFF(SBI+13,I) = ICHAR('y')
         SBI = SBI + 13
C
         SIOWBUIN(I) = SBI             ! Serial I/O Buffer index
        ENDIF
       ENDDO
C
C --- Prepare to jump to the next state
C
       FPSSTEP  = FPSSTEP + 1
       FPSSTATE = FPSEQUEN(FPSSTEP,FPSMODE)
       GOTO 1999
C
C --------------------------------------------------
C ---   First Pass   ---   Complete First Pass   ---
C --------------------------------------------------
C
1800   CONTINUE
C
C --- Buffered I/O in Progress monitoring logic write (and error checking)
C
CVAX
CVAX   IF (.NOT. PGDTFLAG(1)) THEN
CVAX    RETURN
CVAX   ELSE IF (PGDTRTCD(1) .NE. 1) THEN
CVAX    TPERROR = ERR_P_DR
CVAX    RETURN
CVAX   ENDIF
CVAXEND
CSGI
CSGI   IF      (BIOIDIR.STATUS .EQ. 999) THEN
CSGI    RETURN
CSGI   ELSE IF (BIOIDIR.STATUS .NE.   1) THEN
CSGI    TPERROR = ERR_P_DR
CSGI    RETURN
CSGI   ENDIF
CSGIEND
CIBM
       IF (BIODIRFS .NE. SUCCESS) THEN
        TPERROR = ERR_P_DR
        RETURN
       ELSE IF (BIODIRIS .NE. SUCCESS) THEN
        RETURN
       ENDIF
CIBMEND
C
C --- Serial I/O in Progress monitoring logic
C
       DO I=1 , INDEV
        IF (SIOASSIF(I)) THEN
CVAX
CVAX     IF (.NOT.SIOWRFLG(I)) THEN
CVAX      RETURN                       ! Buff I/O not completed
CVAX     ENDIF
CVAX     IF ((SIOWRIEV(I)   .NE. 1) .OR.
CVAX .       (SIOWRSTB(1,I) .NE. 1) .OR.
CVAX .       (SIOWRSTB(3,I) .NE. 0)) THEN
CVAX      TPERROR = ERR_S_CM           ! Buff I/O completed with error
CVAX      RETURN
CVAX     ENDIF
CVAXEND
CIBM
         IF (SIOWBUIS(I) .NE. SUCCESS) THEN
          RETURN                       ! Buff I/O not completed
         ENDIF
CIBMEND
        ENDIF
       ENDDO
C
C --- Make sure that the pages will be read again
C
       TPXOODEV = 0
       DO I=1 , INDEV
        DEVPARDY(I) = .FALSE.
        DEVPARPD(I) = .FALSE.
        DEVPAGDL(I) = .FALSE.
        PAGFSTDP(I) = .TRUE.
        DEVPANUM(I) = 0
        PGNUMDFP(I) = 0
        PREVPAGE(I) = 0
        TPXOODCB(I) = 0
        TPXICURF(I) = .FALSE.
        PREVCURF(I) = .FALSE.
        DEVRESPC(I) = 1
        DEVPARNB(I) = 0
        SAVSCRCT(I) = 0
       ENDDO
C
       PARORDER = 0
       DO I=1 , NPAR
        PARUNREM(I) = .FALSE.
        DO J=1, INDEV
         PARRESID(I,J) = .FALSE.
        ENDDO
        PARREADY(I) = .TRUE.
        PARPENDI(I) = .FALSE.
        PARDVNUM(I) = 0
        PARPGNUM(I) = 0
        PARNBUSE(I) = 0
C
        PGPRMODE(I) = PPMD_ELP         ! Set page states variables to iddle
        PGPRSTEP(I) = 1
        PGPSTATE(I) = PPST_IDL
       ENDDO
C
C --- Set page pointer
C
       PPAR = 1
C
       INITIRDY = .TRUE.
       FIRSTPAS = .FALSE.
       GOTO 1999
C
C --------------------------------------------------------------
C ---   First Pass   ---   Complete First Pass Device reset  ---
C --------------------------------------------------------------
C
1900   CONTINUE
C
C --- Serial I/O in Progress monitoring logic
C
       DO I=1 , INDEV
        IF (SIOASSIF(I)) THEN
CVAX
CVAX     IF (.NOT. SIOWRFLG(I)) THEN
CVAX      RETURN                       ! Buff I/O not completed
CVAX     ENDIF
CVAXEND
CIBM
         IF (SIOOPEFS(I) .NE. SUCCESS) THEN
          TPERROR = ERR_S_CM           ! Buff I/O completed with error
          RETURN
         ELSE IF (SIOOPEIS(I) .NE. SUCCESS) THEN
          RETURN                       ! Buff I/O not completed
         ENDIF
CIBMEND
         IF (DEVRESET(I)) THEN
CVAX
CVAX      IF ((SIOWRIEV(I)   .NE. 1) .OR.
CVAX .        (SIOWRSTB(1,I) .NE. 1) .OR.
CVAX .        (SIOWRSTB(3,I) .NE. 0)) THEN
CVAX       TPERROR = ERR_S_CM          ! Buff I/O completed with error
CVAX       RETURN
CVAX      ENDIF
CVAXEND
CIBM
          IF (SIOWBUIS(I) .NE. SUCCESS) THEN
           RETURN                      ! Buff I/O not completed
          ENDIF
CIBMEND
          DEVRESET(I) = .FALSE.
          DEVPANUM(I) = 0              ! Request for a page update
          PGNUMDFP(I) = 0
          TPXOODCB(I) = 0
          DEVPARNB(I) = 0
          SAVSCRCT(I) = 0
         ENDIF
        ENDIF
       ENDDO
C
       INITIRDY = .TRUE.
       FIRSTPAS = .FALSE.
       GOTO 1999
C
C -----------------------------------------
C ---   First Pass   ---   Exit point   ---
C -----------------------------------------
C
1999   CONTINUE
      ELSE
C
C -------------------------
C --- Save Screen logic ---
C -------------------------
C
       DO I=1 , INDEV
        IF (SAVSCRAV(I)) THEN
         IF (SAVSCRCT(I).EQ.SAVSCRTM) THEN       ! Reached the screen off time
          SAVSCRCT(I) = SAVSCRCT(I) + 1
          TPXOPAGE(I) = SAVSCRPG
         ELSE IF (SAVSCRCT(I).LT.SAVSCRTM) THEN  ! Increment the time
          SAVSCRCT(I) = SAVSCRCT(I) + 1
         ENDIF
        ENDIF
       ENDDO
C
C -----------------------------------
C --- New Page request monitoring ---
C -----------------------------------
C
C --- Monitor the Device page change request
C        - I is the device treated
C        - J is the page buffer verified
C
       DO I=1 , INDEV
        IF ((DEVPANUM(I) .NE. TPXOPAGE(I)) .AND.
     .      (.NOT. DEVPAGDL(I))) THEN
         IF (TPXOPAGE(I) .EQ. 0) THEN            ! Jump to previous
          TPXOPAGE(I) = PREVPAGE(I)              ! if page number = 0
         ENDIF
         IF ((TPXOPAGE(I) .GT. 0) .AND.          ! Validate the page number
     .       (TPXOPAGE(I) .LE. AMAX_PGNO) .AND.
     .       (PGDTDIR(TPXOPAGE(I)) .NE. -1) .AND.
     .       (SIOASSIF(I))) THEN
C
C --- To find the page replacement area :
C        - the first unused page is determine,
C        - if the page is allready loaded is also determine ,
C        - the oldest page loaded is found.
C
          PREVPAGE(I) = DEVPANUM(I)
          DEVPANUM(I) = TPXOPAGE(I)
          PARUNUSE = 0                           ! First unused page
          PARLOADE = 0                           ! Allready loaded pag
          PAROLDES = 0                           ! Oldest page
          PAROLDAG = 0                           ! Oldest page age
          DO J=1 , NPAR
           IF (PARPGNUM(J).EQ.0) THEN
            IF (PARUNUSE.EQ.0) THEN
             PARUNUSE = J                        ! First unused page
            ENDIF
           ELSE IF (PARPGNUM(J) .EQ. DEVPANUM(I)) THEN
            PARLOADE = J                         ! Allready loaded page
           ELSE IF (.NOT. PARUNREM(J)) THEN
            IF (PARNBUSE(J) .EQ. 0) THEN         ! The buffer not used
             IF (PAROLDAG .EQ. 0) THEN
              PAROLDES = J                       ! First oldest page
              PAROLDAG = PARORDLD(J)
             ELSE IF (PARORDLD(J) .LT. PAROLDAG) THEN
              PAROLDES = J                       ! Oldest page
              PAROLDAG = PARORDLD(J)
             ENDIF
            ENDIF
           ENDIF
          ENDDO
C
C --- Initiate the page processing according to the free page area recherch
C        - Use Allready loaded page area
C        - May also treat the display list page
C
          IF (PARLOADE .NE. 0) THEN
           IF (PARREADY(PARLOADE)) THEN
            IF (DEVPARNB(I).NE.0) THEN           ! Old Page area
             PARDVNUM(DEVPARNB(I)) = 0
             PARNBUSE(DEVPARNB(I)) = PARNBUSE(DEVPARNB(I)) - 1
            ENDIF
            PARORDER = PARORDER + 1              ! New Page area
            PARORDLD(PARLOADE) = PARORDER
            PARDVNUM(PARLOADE) = I
            PARPGNUM(PARLOADE) = DEVPANUM(I)
            PARNBUSE(PARLOADE) = PARNBUSE(PARLOADE) + 1
            PARREADY(PARLOADE) = .FALSE.
            DEVPARDY(I) = .FALSE.                ! Device acces
            PAGFSTDP(I) = .TRUE.
            DEVPARNB(I) = PARLOADE
            PGPRSTEP(PARLOADE) = 1               ! Sequence init
            IF (PARRESID(PARLOADE,I)) THEN
             PGPRMODE(PARLOADE) = PPMD_RES
            ELSE IF (PGNUMDFP(I) .EQ. DEVPANUM(I)) THEN
             PGPRMODE(PARLOADE) = PPMD_RES
            ELSE
             PGPRMODE(PARLOADE) = PPMD_ALO
             IF (IAND(PAGEHEAD(PAG_TYPE,PARLOADE),         ! Page resident ?
     .                   PT_RESID) .NE. 0) THEN
              PAGEHEAD(PAG_TYPE,PARLOADE) =                ! Force a resident
     .           IOR(PAGEHEAD(PAG_TYPE,PARLOADE),PT_UNREM) !    page to stay
              PARUNREM(PARLOADE) = .TRUE.                  !    in the cache
              IF (DEVRESPC(I) .LT. NDAR) THEN
               DEVRESPC(I) = DEVRESPC(I) + 1               ! Allocate a new
               PARRESID(PARLOADE,I) = .TRUE.               !    resident
               PARESPGN(PARLOADE,I) = DEVRESPC(I)          !    device page
              ELSE
               PAGEHEAD(PAG_TYPE,PARLOADE) = IAND(         ! No more resident
     .          PAGEHEAD(PAG_TYPE,PARLOADE),NOT(PT_RESID)) !    page area
               PARRESID(PARLOADE,I) = .FALSE.              !    available
               PARESPGN(PARLOADE,I) = 1
              ENDIF
             ELSE
              PARRESID(PARLOADE,I) = .FALSE.
              PARESPGN(PARLOADE,I) = 1
             ENDIF
            ENDIF
            PGPSTATE(PARLOADE) = PPSEQUEN(PGPRSTEP(PARLOADE),
     .                                    PGPRMODE(PARLOADE))
C
C --- If the required page area is being process
C
           ELSE
            DEVPARPD(I) = .TRUE.
            PARPENDI(PARLOADE) = .TRUE.
           ENDIF
C
C --- Un-used page area
C
          ELSE IF (PARUNUSE .NE. 0) THEN
           IF (DEVPARNB(I).NE.0) THEN            ! Old Page area
            PARDVNUM(DEVPARNB(I)) = 0
            PARNBUSE(DEVPARNB(I)) = PARNBUSE(DEVPARNB(I)) - 1
           ENDIF
           PARORDER = PARORDER + 1               ! New Page area
           PARORDLD(PARUNUSE) = PARORDER
           PARDVNUM(PARUNUSE) = I
           PARPGNUM(PARUNUSE) = DEVPANUM(I)
           PARNBUSE(PARUNUSE) = PARNBUSE(PARUNUSE) + 1
           PARREADY(PARUNUSE) = .FALSE.
           DEVPARDY(I) = .FALSE.                 ! Device acces
           PAGFSTDP(I) = .TRUE.
           DEVPARNB(I) = PARUNUSE
           PGPRSTEP(PARUNUSE) = 1                ! Sequence init
           PGPRMODE(PARUNUSE) = PPMD_ELP
           PGPSTATE(PARUNUSE) = PPSEQUEN(PGPRSTEP(PARUNUSE),
     .                                   PGPRMODE(PARUNUSE))
C
C --- Replace allready used area
C
          ELSE IF (PAROLDES .NE. 0) THEN
           IF (DEVPARNB(I) .NE. 0) THEN          ! Old Page area
            PARDVNUM(DEVPARNB(I)) = 0
            PARNBUSE(DEVPARNB(I)) = PARNBUSE(DEVPARNB(I)) - 1
           ENDIF
           PARMAPPG(PAROLDES) = .FALSE.
           PARORDER = PARORDER + 1               ! New Page area
           PARORDLD(PAROLDES) = PARORDER
           PARDVNUM(PAROLDES) = I
           PARPGNUM(PAROLDES) = DEVPANUM(I)
           PARNBUSE(PAROLDES) = PARNBUSE(PAROLDES) + 1
           PARREADY(PAROLDES) = .FALSE.
           DEVPARDY(I) = .FALSE.                 ! Device acess
           PAGFSTDP(I) = .TRUE.
           DEVPARNB(I) = PAROLDES
           PGPRSTEP(PAROLDES) = 1                ! Sequence init
           PGPRMODE(PAROLDES) = PPMD_ELP
           PGPSTATE(PAROLDES) = PPSEQUEN(PGPRSTEP(PAROLDES),
     .                                   PGPRMODE(PAROLDES))
          ELSE
           TPERROR = ERR_AROF
           RETURN
          ENDIF
C
C --- Cancel a non-available page request
C
         ELSE
          TPXOPAGE(I) = DEVPANUM(I)
         ENDIF
        ENDIF
       ENDDO
C
C ----------------------------
C ---   Pages Processing   ---
C ----------------------------
C
C --- Process all pages
C
2000   DO I=1 , NUMPAGPP
        GOTO (2100                     ! Read Page Header          - PPST_RPH
     .,       2200                     ! Read Page data            - PPST_RPD
     .,       2300                     ! Process Page data         - PPST_PPD
     .,       2400                     ! Send Page data            - PPST_SPD
     .,       2500                     ! Read DCB                  - PPST_RDC
     .,       2600                     ! Activate Display List     - PPST_ADL
     .,       2700                     ! Complete Normal Page      - PPST_CPP
     .,       2800                     ! Complete Cache Page       - PPST_CHP
     .,       2900                     ! Iddle state               - PPST_IDL
     .            ) PGPSTATE(PPAR)
C
C ----------------------------------------------------
C --- Page Processing --- Read Page Header         ---
C ----------------------------------------------------
C
2100    CONTINUE
C
CVAX
CVAX    BIORECNB = PGDTDIR(PARPGNUM(PPAR))
CVAX    BIOBYTNB = 1
CVAX    BIOBYTCT = MAX_PGHDR * 4           ! Header size
CVAX    PGDTRTCD(PPAR) = 0
CVAX    PGDTFLAG(PPAR) = .FALSE.
CVAX    CALL BUFF_READ(PGDTUNIT
CVAX .,                %DESCR(PAGEHEAD(1,PPAR))
CVAX .,                BIORECNB
CVAX .,                BIOBYTNB
CVAX .,                BIOBYTCT
CVAX .,                PGDTRTCD(PPAR)
CVAX .,                PGDTFLAG(PPAR)
CVAX .,                %VAL(PGDTASRA(PPAR)))
CVAXEND
CSGI
CSGI   BIOIPGHD(PPAR).FD.FD          = BIOIOPEN.FD.FD
CSGI   BIOIPGHD(PPAR).FD.RECORD_SIZE = MAX_ORECLEN
CSGI   BIOIPGHD(PPAR).RECORD_NUM     = PGDTDIR(PARPGNUM(PPAR)) - 1
CSGI   BIOIPGHD(PPAR).AMOUNT         = MAX_PGHDR * 4
CSGI   BIOIPGHD(PPAR).POSITION       = 0
CSGI   BIOIPGHD(PPAR).BUFFERP        = %LOC(PAGEHEAD(1,PPAR))
CSGI   CALL CAE_IO_READ(BIOIPGHD(PPAR), PRIORITY)
CSGIEND
CIBM
       BIORECNB = PGDTDIR(PARPGNUM(PPAR)) - 1
       BIOBYTCT = MAX_PGHDR * 4
       BIOBYTNB = 0
       CALL CAE_IO_READ(BIOPHDFS(PPAR)
     .,                 BIOPHDIS(PPAR)
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 PAGEHEAD(1,PPAR)
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Prepare to jump to the next state
C
        PGPRSTEP(PPAR) = PGPRSTEP(PPAR) + 1
        PGPSTATE(PPAR) = PPSEQUEN(PGPRSTEP(PPAR),PGPRMODE(PPAR))
        GOTO 2999
C
C ----------------------------------------------------
C --- Page Processing --- Read Page data           ---
C ----------------------------------------------------
C
2200    CONTINUE
C
C --- Buffered I/O in Progress monitoring logic
C
CVAX
CVAX   IF (.NOT. PGDTFLAG(PPAR)) THEN
CVAX    GOTO 2999
CVAX   ELSE IF (PGDTRTCD(PPAR) .NE. 1) THEN
CVAX    TPERROR = ERR_A_HD
CVAX    RETURN
CVAX   ENDIF
CVAXEND
CSGI
CSGI   IF      (BIOIPGHD(PPAR).STATUS .EQ. 999) THEN
CSGI    GOTO 2999
CSGI   ELSE IF (BIOIPGHD(PPAR).STATUS .NE.   1) THEN
CSGI    TPERROR = ERR_A_HD
CSGI    RETURN
CSGI   ENDIF
CSGIEND
CIBM
       IF (BIOPHDFS(PPAR) .NE. SUCCESS) THEN
        TPERROR = ERR_A_HD
        RETURN
       ELSE IF (BIOPHDIS(PPAR) .NE. SUCCESS) THEN
        GOTO 2999
       ENDIF
CIBMEND
        LDV  = PARDVNUM(PPAR)                    ! Device using this area
C
C --- Check if the page is unremovable
C
        PARUNREM(PPAR) = IAND(PAGEHEAD(PAG_TYPE,PPAR),PT_UNREM) .NE. 0
C
C --- Check if the page is a resident one
C
        IF (IAND(PAGEHEAD(PAG_TYPE,PPAR),PT_RESID) .NE. 0) THEN
         PAGEHEAD(PAG_TYPE,PPAR) =                         ! Force a resident
     .      IOR(PAGEHEAD(PAG_TYPE,PPAR),PT_UNREM)          !    page to stay
         PARUNREM(PPAR) = .TRUE.                           !    in the cache
         IF (DEVRESPC(LDV) .LT. NDAR) THEN
          DEVRESPC(LDV) = DEVRESPC(LDV) + 1                ! Allocate a new
          DO J=1, INDEV                                    !    resident
           PARRESID(PPAR,J) = .FALSE.                      !    device page
           PARESPGN(PPAR,J) = 0
          ENDDO
          PARRESID(PPAR,LDV) = .TRUE.
          PARESPGN(PPAR,LDV) = DEVRESPC(LDV)
         ELSE
          PAGEHEAD(PAG_TYPE,PPAR) =                        ! No more resident
     .       IAND(PAGEHEAD(PAG_TYPE,PPAR),NOT(PT_RESID))   !    page area
          DO J=1, INDEV                                    !    available
           PARRESID(PPAR,J) = .FALSE.
           PARESPGN(PPAR,J) = 1
          ENDDO
         ENDIF
        ELSE
         DO J=1, INDEV
          PARRESID(PPAR,J) = .FALSE.
          PARESPGN(PPAR,J) = 1
         ENDDO
        ENDIF
C
C --- Read Page input Dictionary
C
CVAX
CVAX    BIORECNB = PGDTDIR(PARPGNUM(PPAR))
CVAX    BIOBYTNB = PAGEHEAD(PAG_IPOFF,PPAR)
CVAX    BIOBYTCT = PAGEHEAD(PAG_IPLEN,PPAR)
CVAX    PGDTDMRC = 0
CVAX    PGDTDMFL = .FALSE.
CVAX    CALL BUFF_READ(PGDTUNIT
CVAX .,                %DESCR(PAGEIPDR(1,1,PPAR))
CVAX .,                BIORECNB
CVAX .,                BIOBYTNB
CVAX .,                BIOBYTCT
CVAX .,                PGDTDMRC
CVAX .,                PGDTDMFL
CVAX .,                PGDTAST)
CVAXEND
CSGI
CSGI   BIOIIPDR(PPAR).FD.FD          = BIOIOPEN.FD.FD
CSGI   BIOIIPDR(PPAR).FD.RECORD_SIZE = MAX_ORECLEN
CSGI   BIOIIPDR(PPAR).RECORD_NUM     = PGDTDIR(PARPGNUM(PPAR)) - 1
CSGI   BIOIIPDR(PPAR).AMOUNT         = PAGEHEAD(PAG_IPLEN,PPAR)
CSGI   BIOIIPDR(PPAR).POSITION       = PAGEHEAD(PAG_IPOFF,PPAR) - 1
CSGI   BIOIIPDR(PPAR).BUFFERP        = %LOC(PAGEIPDR(1,1,PPAR))
CSGI   CALL CAE_IO_READ(BIOIIPDR(PPAR), PRIORITY)
CSGIEND
CIBM
       BIORECNB = PGDTDIR(PARPGNUM(PPAR)) - 1
       BIOBYTCT = PAGEHEAD(PAG_IPLEN,PPAR)
       BIOBYTNB = PAGEHEAD(PAG_IPOFF,PPAR) - 1
       CALL CAE_IO_READ(BIOIDRFS(PPAR)
     .,                 BIOIDRIS(PPAR)
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 PAGEIPDR(1,1,PPAR)
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Non Volatile
C
CVAX
CVAX    BIOBYTNB = PAGEHEAD(PAG_COMOFF,PPAR)
CVAX    BIOBYTCT = PAGEHEAD(PAG_COMLEN,PPAR)
CVAX    PGDTDMRC = 0
CVAX    PGDTDMFL = .FALSE.
CVAX    CALL BUFF_READ(PGDTUNIT
CVAX .,                %DESCR(PAGENOVL(1,PPAR))
CVAX .,                BIORECNB
CVAX .,                BIOBYTNB
CVAX .,                BIOBYTCT
CVAX .,                PGDTDMRC
CVAX .,                PGDTDMFL
CVAX .,                PGDTAST)
CVAXEND
CSGI
CSGI   BIOINOVL(PPAR).FD.FD          = BIOIOPEN.FD.FD
CSGI   BIOINOVL(PPAR).FD.RECORD_SIZE = MAX_ORECLEN
CSGI   BIOINOVL(PPAR).RECORD_NUM     = PGDTDIR(PARPGNUM(PPAR)) - 1
CSGI   BIOINOVL(PPAR).AMOUNT         = PAGEHEAD(PAG_COMLEN,PPAR)
CSGI   BIOINOVL(PPAR).POSITION       = PAGEHEAD(PAG_COMOFF,PPAR) - 1
CSGI   BIOINOVL(PPAR).BUFFERP        = %LOC(PAGENOVL(1,PPAR))
CSGI   CALL CAE_IO_READ(BIOINOVL(PPAR), PRIORITY)
CSGIEND
CIBM
       BIORECNB = PGDTDIR(PARPGNUM(PPAR)) - 1
       BIOBYTCT = PAGEHEAD(PAG_COMLEN,PPAR)
       BIOBYTNB = PAGEHEAD(PAG_COMOFF,PPAR) - 1
       CALL CAE_IO_READ(BIONOVFS(PPAR)
     .,                 BIONOVIS(PPAR)
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 PAGENOVL(1,PPAR)
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Starting ending row colum
C
CVAX
CVAX    BIOBYTNB = PAGEHEAD(PAG_RCOFF,PPAR)
CVAX    BIOBYTCT = PAGEHEAD(PAG_RCLEN,PPAR)
CVAX    PGDTDMRC = 0
CVAX    PGDTDMFL = .FALSE.
CVAX    CALL BUFF_READ(PGDTUNIT
CVAX .,                %DESCR(PAGESERC(1,PPAR))
CVAX .,                BIORECNB
CVAX .,                BIOBYTNB
CVAX .,                BIOBYTCT
CVAX .,                PGDTDMRC
CVAX .,                PGDTDMFL
CVAX .,                PGDTAST)
CVAXEND
CSGI
CSGI   BIOISERC(PPAR).FD.FD          = BIOIOPEN.FD.FD
CSGI   BIOISERC(PPAR).FD.RECORD_SIZE = MAX_ORECLEN
CSGI   BIOISERC(PPAR).RECORD_NUM     = PGDTDIR(PARPGNUM(PPAR)) - 1
CSGI   BIOISERC(PPAR).AMOUNT         = PAGEHEAD(PAG_RCLEN,PPAR)
CSGI   BIOISERC(PPAR).POSITION       = PAGEHEAD(PAG_RCOFF,PPAR) - 1
CSGI   BIOISERC(PPAR).BUFFERP        = %LOC(PAGESERC(1,PPAR))
CSGI   CALL CAE_IO_READ(BIOISERC(PPAR), PRIORITY)
CSGIEND
CIBM
       BIORECNB = PGDTDIR(PARPGNUM(PPAR)) - 1
       BIOBYTCT = PAGEHEAD(PAG_RCLEN,PPAR)
       BIOBYTNB = PAGEHEAD(PAG_RCOFF,PPAR) - 1
       CALL CAE_IO_READ(BIOSERFS(PPAR)
     .,                 BIOSERIS(PPAR)
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 PAGESERC(1,PPAR)
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Read Color Block
C
CVAX
CVAX    BIOBYTNB = PAGEHEAD(PAG_CLROFF,PPAR)
CVAX    BIOBYTCT = PAGEHEAD(PAG_CLRLEN,PPAR)
CVAX    PGDTRTCD(PPAR) = 0
CVAX    PGDTFLAG(PPAR) = .FALSE.
CVAX    CALL BUFF_READ(PGDTUNIT
CVAX .,                %DESCR(PAGECOLO(1,1,PPAR))
CVAX .,                BIORECNB
CVAX .,                BIOBYTNB
CVAX .,                BIOBYTCT
CVAX .,                PGDTRTCD(PPAR)
CVAX .,                PGDTFLAG(PPAR)
CVAX .,                %VAL(PGDTASRA(PPAR)))
CVAXEND
CSGI
CSGI   BIOICOLO(PPAR).FD.FD          = BIOIOPEN.FD.FD
CSGI   BIOICOLO(PPAR).FD.RECORD_SIZE = MAX_ORECLEN
CSGI   BIOICOLO(PPAR).RECORD_NUM     = PGDTDIR(PARPGNUM(PPAR)) - 1
CSGI   BIOICOLO(PPAR).AMOUNT         = PAGEHEAD(PAG_CLRLEN,PPAR)
CSGI   BIOICOLO(PPAR).POSITION       = PAGEHEAD(PAG_CLROFF,PPAR) - 1
CSGI   BIOICOLO(PPAR).BUFFERP        = %LOC(PAGECOLO(1,1,PPAR))
CSGI   CALL CAE_IO_READ(BIOICOLO(PPAR), PRIORITY)
CSGIEND
CIBM
       BIORECNB = PGDTDIR(PARPGNUM(PPAR)) - 1
       BIOBYTCT = PAGEHEAD(PAG_CLRLEN,PPAR)
       BIOBYTNB = PAGEHEAD(PAG_CLROFF,PPAR) - 1
       CALL CAE_IO_READ(BIOCOLFS(PPAR)
     .,                 BIOCOLIS(PPAR)
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 PAGECOLO(1,1,PPAR)
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Read Touch Block
C
CVAX
CVAX    BIOBYTNB = PAGEHEAD(PAG_TCHOFF,PPAR)
CVAX    BIOBYTCT = PAGEHEAD(PAG_TCHLEN,PPAR)
CVAX    PGDTRTCD(PPAR) = 0
CVAX    PGDTFLAG(PPAR) = .FALSE.
CVAX    CALL BUFF_READ(PGDTUNIT
CVAX .,                %DESCR(PAGETOBL(1,1,PPAR))
CVAX .,                BIORECNB
CVAX .,                BIOBYTNB
CVAX .,                BIOBYTCT
CVAX .,                PGDTRTCD(PPAR)
CVAX .,                PGDTFLAG(PPAR)
CVAX .,                %VAL(PGDTASRA(PPAR)))
CVAXEND
CSGI
CSGI   BIOITOBL(PPAR).FD.FD          = BIOIOPEN.FD.FD
CSGI   BIOITOBL(PPAR).FD.RECORD_SIZE = MAX_ORECLEN
CSGI   BIOITOBL(PPAR).RECORD_NUM     = PGDTDIR(PARPGNUM(PPAR)) - 1
CSGI   BIOITOBL(PPAR).AMOUNT         = PAGEHEAD(PAG_TCHLEN,PPAR)
CSGI   BIOITOBL(PPAR).POSITION       = PAGEHEAD(PAG_TCHOFF,PPAR) - 1
CSGI   BIOITOBL(PPAR).BUFFERP        = %LOC(PAGETOBL(1,1,PPAR))
CSGI   CALL CAE_IO_READ(BIOITOBL(PPAR), PRIORITY)
CSGIEND
CIBM
       BIORECNB = PGDTDIR(PARPGNUM(PPAR)) - 1
       BIOBYTCT = PAGEHEAD(PAG_TCHLEN,PPAR)
       BIOBYTNB = PAGEHEAD(PAG_TCHOFF,PPAR) - 1
       CALL CAE_IO_READ(BIOTOBFS(PPAR)
     .,                 BIOTOBIS(PPAR)
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 PAGETOBL(1,1,PPAR)
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Prepare to jump to the next state
C
        PGPRSTEP(PPAR) = PGPRSTEP(PPAR) + 1
        PGPSTATE(PPAR) = PPSEQUEN(PGPRSTEP(PPAR),PGPRMODE(PPAR))
        GOTO 2999
C
C ----------------------------------------------------
C --- Page Processing --- Process Page data        ---
C ----------------------------------------------------
C
2300    CONTINUE
C
C --- Buffered I/O in Progress monitoring logic
C
CVAX
CVAX   IF (.NOT. PGDTFLAG(PPAR)) THEN
CVAX    GOTO 2999
CVAX   ELSE IF (PGDTRTCD(PPAR) .NE. 1) THEN
CVAX    TPERROR = ERR_A_DT
CVAX    RETURN
CVAX   ENDIF
CVAXEND
CSGI
CSGI   IF      (BIOITOBL(PPAR).STATUS .EQ. 999) THEN
CSGI    GOTO 2999
CSGI   ELSE IF (BIOITOBL(PPAR).STATUS .NE.   1) THEN
CSGI    TPERROR = ERR_A_DT
CSGI    RETURN
CSGI   ENDIF
CSGIEND
CIBM
       IF (BIOTOBFS(PPAR) .NE. SUCCESS) THEN
        TPERROR = ERR_A_DT
        RETURN
       ELSE IF (BIOTOBIS(PPAR) .NE. SUCCESS) THEN
        GOTO 2999
       ENDIF
CIBMEND
C
        LDV  = PARDVNUM(PPAR)                    ! Dev. using this area
C
C --- Character attribute
C
        CHRWIDTH = PAGEHEAD(PAG_CHRWTH,PPAR)     ! Character width
        CHRHEIGH = PAGEHEAD(PAG_CHRHGT,PPAR)     ! Character height
C
C --- Calculate non-volatile position
C
        PAGENVCS(1,PPAR) = CHRHEIGH              ! Character size
        PAGENVCS(2,PPAR) = CHRWIDTH
C
        PAGEYORG = 1 + SCR_1_OFF + BOX_HEIGHT - CHRHEIGH +
     .             (PAGEHEAD(PAG_Y1,PPAR)-1) * CHRHEIGH
        IF (PAGEYORG .LT. 1) THEN
         PAGEYORG = 1
        ELSE IF (PAGEYORG .GT. SCR_HEIGHT) THEN
         PAGEYORG = SCR_HEIGHT
        ENDIF
        PAGEXORG = 1 + SCR_2_OFF +
     .             (PAGEHEAD(PAG_X1,PPAR)-1) * CHRWIDTH
        IF (PAGEXORG .GT. SCR_WIDTH) THEN
         PAGEXORG = SCR_WIDTH
        ENDIF
C
        PAGENVPO(1,PPAR) = PAGEYORG
        PAGENVPO(2,PPAR) = PAGEXORG
        PAGENVIN(1,PPAR) = CHRHEIGH
        PAGENVIN(2,PPAR) = 0
C
C --- Prepare the color zone using the row and colum buffer
C
        PAGEYORG = 1 + SCR_1_OFF + BOX_HEIGHT - CHRHEIGH +
     .             (PAGEHEAD(PAG_Y1,PPAR)-1) * CHRHEIGH
        IF (PAGEYORG .LT. 1) THEN
         PAGEYORG = 1
        ELSE IF (PAGEYORG .GT. SCR_HEIGHT) THEN
         PAGEYORG = SCR_HEIGHT
        ENDIF
        PAGEXORG = 1 + SCR_2_OFF +
     .             (PAGEHEAD(PAG_X1,PPAR)-1) * CHRWIDTH
        IF (PAGEXORG .GT. SCR_WIDTH) THEN
         PAGEXORG = SCR_WIDTH
        ENDIF
C
        JD = PAGEHEAD(PAG_NUMDCBS,PPAR)
        DO J=1, PAGEHEAD(PAG_NUMDCBS,PPAR)
         PAGEYPOS = PAGEYORG + (PAGESERC(J,PPAR)-1) * CHRHEIGH
         IF (PAGEYPOS  .GT. SCR_HEIGHT) THEN
          PAGEYPOS = SCR_HEIGHT
         ENDIF
         PAGEXPOS = PAGEXORG + (PAGESERC(JD+J,PPAR)-1) * CHRWIDTH
         IF (PAGEXPOS .GT. SCR_WIDTH) THEN
          PAGEXPOS =  SCR_WIDTH
         ENDIF
         PAGEYEND = PAGEYORG + PAGESERC(2*JD+J,PPAR)*CHRHEIGH - 1
         IF (PAGEYEND .GT. SCR_HEIGHT) THEN
          PAGEYEND = SCR_HEIGHT
          ENDIF
         PAGEXEND = PAGEXORG + PAGESERC(3*JD+J,PPAR)*CHRWIDTH - 1
         IF (PAGEXEND .GT. SCR_WIDTH) THEN
          PAGEXEND =  SCR_WIDTH
         ENDIF
C
         PAGETXLM(1,J,PPAR) = PAGESERC(     J, PPAR)       ! In Char.
         PAGETXLM(2,J,PPAR) = PAGESERC(  JD+J, PPAR)
         PAGETXLM(3,J,PPAR) = PAGESERC(2*JD+J, PPAR)
         PAGETXLM(4,J,PPAR) = PAGESERC(3*JD+J, PPAR)
C
         PAGETXPO(1,J,PPAR) = PAGEYPOS                     ! In Pixel
         PAGETXPO(2,J,PPAR) = PAGEXPOS
C
         IF ((PAGEYEND-PAGEYPOS) .LT. CHRHEIGH) THEN       ! At least one
          PAGEYEND = PAGEYPOS + CHRHEIGH - 1               !   Char high
          PAGECOBL(1,J,PPAR) = PAGEYPOS                    ! Color block
          PAGECOBL(2,J,PPAR) = PAGEXPOS + COL_BLOF
          PAGECOBL(3,J,PPAR) = PAGEYEND
          PAGECOBL(4,J,PPAR) = PAGEXEND - COL_BLOF
C
          PAGESTZN(1,J,PPAR) = PAGEYPOS
          PAGESTZN(2,J,PPAR) = PAGEXPOS + STA_BLOF
          PAGESTZN(3,J,PPAR) = PAGEYEND
          PAGESTZN(4,J,PPAR) = PAGEXEND - STA_BLOF
C
         ELSE
          PAGECOBL(1,J,PPAR) = PAGEYPOS + COL_BLOF         ! Color block
          PAGECOBL(2,J,PPAR) = PAGEXPOS + COL_BLOF
          PAGECOBL(3,J,PPAR) = PAGEYEND - COL_BLOF
          PAGECOBL(4,J,PPAR) = PAGEXEND - COL_BLOF
C
          PAGESTZN(1,J,PPAR) = PAGEYPOS + STA_BLOF         ! State Zone
          PAGESTZN(2,J,PPAR) = PAGEXPOS + STA_BLOF
          PAGESTZN(3,J,PPAR) = PAGEYEND - STA_BLOF
          PAGESTZN(4,J,PPAR) = PAGEXEND - STA_BLOF
         ENDIF
C
         PAGCLUST(J, PPAR) = NULL                ! Cancel the cluster
        ENDDO
C
C --- Calculate the touch block position in pixel
C
        PAGEYORG = 1 + SCR_1_OFF + BOX_HEIGHT - CHRHEIGH +
     .             (PAGEHEAD(PAG_Y1,PPAR)-1)*CHRHEIGH
        IF (PAGEYORG .LT. 1) THEN
         PAGEYORG = 1
        ELSE IF (PAGEYORG .GT. SCR_HEIGHT) THEN
         PAGEYORG = SCR_HEIGHT
        ENDIF
        PAGEXORG = 1 + SCR_2_OFF +
     .            (PAGEHEAD(PAG_X1,PPAR)-1)*CHRWIDTH
        IF (PAGEXORG .GT. SCR_WIDTH) THEN
         PAGEXORG = SCR_WIDTH
        ENDIF
C
        DO J=PAGEHEAD(PAG_FIRLIN,PPAR), PAGEHEAD(PAG_LASLIN,PPAR)
C
         IF (IAND(PAGETOBL(5,J,PPAR),TTY_TCH4DP) .NE. 0) THEN
          JD = PAGEIPDR(2,J,PPAR)
          PAGETXLM(1,JD,PPAR) = PAGETOBL(1,J,PPAR)
          PAGETXLM(2,JD,PPAR) = PAGETOBL(2,J,PPAR)
          PAGETXLM(3,JD,PPAR) = PAGETOBL(3,J,PPAR)
          PAGETXLM(4,JD,PPAR) = PAGETOBL(4,J,PPAR)
         ENDIF
C
         PAGEYPOS = PAGEYORG + (PAGETOBL(1,J,PPAR)-1) * CHRHEIGH
         IF (PAGEYPOS  .GT. SCR_HEIGHT) THEN
          PAGEYPOS = SCR_HEIGHT
         ENDIF
         PAGEXPOS = PAGEXORG + (PAGETOBL(2,J,PPAR)-1) * CHRWIDTH
         IF (PAGEXPOS .GT. SCR_WIDTH) THEN
          PAGEXPOS =  SCR_WIDTH
         ENDIF
         PAGEYEND = PAGEYORG + PAGETOBL(3,J,PPAR)*CHRHEIGH - 1
         IF (PAGEYEND .GT. SCR_HEIGHT) THEN
          PAGEYEND = SCR_HEIGHT
         ENDIF
         PAGEXEND = PAGEXORG + PAGETOBL(4,J,PPAR)*CHRWIDTH - 1
         IF (PAGEXEND .GT. SCR_WIDTH) THEN
          PAGEXEND =  SCR_WIDTH
         ENDIF
C
         PAGETOZN(1,J,PPAR) = PAGEYPOS
         PAGETOZN(2,J,PPAR) = PAGEXPOS
         PAGETOZN(3,J,PPAR) = PAGEYEND
         PAGETOZN(4,J,PPAR) = PAGEXEND
C
         PAGETOBL(1,J,PPAR) = (PAGEYPOS - (CHRHEIGH/2) - TCH_YP_O)
         PAGETOBL(2,J,PPAR) = (PAGEXPOS - (CHRWIDTH/2) - TCH_XP_O)
         PAGETOBL(3,J,PPAR) = (PAGEYEND + (CHRHEIGH/2) + TCH_YE_O)
         PAGETOBL(4,J,PPAR) = (PAGEXEND + (CHRWIDTH/2) + TCH_XE_O)
C
         IF (IAND(PAGETOBL(5,J,PPAR),TTY_TCH4DP) .NE. 0) THEN
          JD = PAGEIPDR(2,J,PPAR)                          ! DCB index
          PAGECOBL(1,JD,PPAR) = PAGEYPOS + COL_BLOF        ! Color block
          PAGECOBL(2,JD,PPAR) = PAGEXPOS + COL_BLOF
          PAGECOBL(3,JD,PPAR) = PAGEYEND - COL_BLOF
          PAGECOBL(4,JD,PPAR) = PAGEXEND - COL_BLOF
C
          PAGESTZN(1,JD,PPAR) = PAGEYPOS + STA_BLOF        ! State Zone
          PAGESTZN(2,JD,PPAR) = PAGEXPOS + STA_BLOF
          PAGESTZN(3,JD,PPAR) = PAGEYEND - STA_BLOF
          PAGESTZN(4,JD,PPAR) = PAGEXEND - STA_BLOF
C
         ENDIF
        ENDDO
C
C --- Prepare to jump to the next state
C --- But in this case dont wait for the next call
C
        PGPRSTEP(PPAR) = PGPRSTEP(PPAR) + 1
        PGPSTATE(PPAR) = PPSEQUEN(PGPRSTEP(PPAR),PGPRMODE(PPAR))
C
C ----------------------------------------------------
C --- Page Processing --- Send Page data           ---
C ----------------------------------------------------
C
2400    CONTINUE
C
        LDV  = PARDVNUM(PPAR)                    ! Device using this area
        SBI  = SIOWBUIN(LDV)                     ! Serial I/O Buffer index
C
        IF (IAND(PAGEHEAD(PAG_TYPE,PPAR),PT_RESID) .NE. 0) THEN
         IF (.NOT.PARRESID(PPAR,LDV)) THEN
          IF (DEVRESPC(LDV) .LT. NDAR) THEN
           DEVRESPC(LDV) = DEVRESPC(LDV) + 1               ! Allocate a new
           PARRESID(PPAR,LDV) = .TRUE.                     !    resident
           PARESPGN(PPAR,LDV) = DEVRESPC(LDV)              !    device page
          ELSE
           PAGEHEAD(PAG_TYPE,PPAR) =                       ! No more resident
     .        IAND(PAGEHEAD(PAG_TYPE,PPAR),NOT(PT_RESID))  !    page area
           PARRESID(PPAR,LDV) = .FALSE.                    !    available
           PARESPGN(PPAR,LDV) = 1
          ENDIF
         ENDIF
        ENDIF
C
        SIOWBUFF(SBI+1, LDV) = ESC               ! Change the page number
        SIOWBUFF(SBI+2, LDV) = SET_ACTIVE_PAGE
        SIOWBUFF(SBI+3, LDV) = INUMBER(3,PARESPGN(PPAR,LDV))
        SBI = SBI + 3
C
C --- Clear screen
C
        SIOWBUFF(SBI+1, LDV) = ESC               ! Clear Screen
        SIOWBUFF(SBI+2, LDV) = CLR_PANEL
        SBI = SBI + 2
        IF (.NOT.TPXICURF(LDV)) THEN             ! Turn off text cursor
         SIOWBUFF(SBI+1, LDV) = ESC
         SIOWBUFF(SBI+2, LDV) = OFF_CURSOR
         SBI = SBI + 2
        ENDIF
C
C --- Set the Character attribute
C
        SIOWBUFF(SBI+1, LDV) = ESC
        SIOWBUFF(SBI+2, LDV) = AVIDEO_CHAR_ATTR
        SIOWBUFF(SBI+3, LDV) =
     .                  INUMBER(3,IAND(1,PAGEHEAD(PAG_CHRSIZ,PPAR))*2)
        SBI = SBI + 3
C
C --- Send Non-volatile
C
        FBI  = 0                                 ! File data Buffer index
        PAGE1POS = PAGENVPO(1,PPAR)              ! Origin position
        PAGE2POS = PAGENVPO(2,PPAR)
        PAGENVNC = PAGEHEAD(PAG_C_INFO,PPAR)     ! Number of colum
C
        DO J=1 , PAGEHEAD(PAG_COMLEN,PPAR) / PAGENVNC
         LINESTRT = SBI
         CHARPRES = .FALSE.
         SIOWBUFF(SBI+1, LDV) = ESC              ! Send the position
         SIOWBUFF(SBI+2, LDV) = GRAPHICS_TEXT
         SIOWBUFF(SBI+3, LDV) = INUMBER(1,PAGE1POS)
         SIOWBUFF(SBI+4, LDV) = INUMBER(2,PAGE1POS)
         SIOWBUFF(SBI+5, LDV) = INUMBER(3,PAGE1POS)
         SIOWBUFF(SBI+6, LDV) = INUMBER(1,PAGE2POS)
         SIOWBUFF(SBI+7, LDV) = INUMBER(2,PAGE2POS)
         SIOWBUFF(SBI+8, LDV) = INUMBER(3,PAGE2POS)
         SBI = SBI + 8
         PAGE1POS = PAGE1POS + PAGENVIN(1,PPAR)  ! Incr. screen pos.
         PAGE2POS = PAGE2POS + PAGENVIN(2,PPAR)
C
         DO K=1 , PAGENVNC                       ! Send the char
          SIOWBUFF(SBI+K,LDV) = PAGENOVB(FBI+K,PPAR)
          IF (PAGENOVB(FBI+K,PPAR) .NE. CHR_SPAC) THEN
           CHARPRES = .TRUE.
          ENDIF
         ENDDO
         SBI = SBI + PAGENVNC
         FBI = FBI + PAGENVNC
         IF (.NOT. CHARPRES) THEN
          SBI = LINESTRT
         ENDIF
        ENDDO
C
        SIOWBUIN(LDV) = SBI                      ! Serial I/O Buffer index
C
C --- Prepare to jump to the next state
C
        PGPRSTEP(PPAR) = PGPRSTEP(PPAR) + 1
        PGPSTATE(PPAR) = PPSEQUEN(PGPRSTEP(PPAR),PGPRMODE(PPAR))
        GOTO 2999
C
C ----------------------------------------------------
C --- Page Processing --- Read DCB Block           ---
C ----------------------------------------------------
C
2500    CONTINUE
C
C --- Buffered I/O in Progress monitoring logic
C
CVAX
CVAX   IF (.NOT. PGDTFLAG(PPAR)) THEN
CVAX    GOTO 2999
CVAX   ELSE IF (PGDTRTCD(PPAR) .NE. 1) THEN
CVAX    TPERROR = ERR_A_DT
CVAX    RETURN
CVAX   ENDIF
CVAXEND
CSGI
CSGI   IF      (BIOIPGHD(PPAR).STATUS .EQ. 999) THEN
CSGI    GOTO 2999
CSGI   ELSE IF (BIOIPGHD(PPAR).STATUS .NE.   1) THEN
CSGI    TPERROR = ERR_A_DT
CSGI    RETURN
CSGI   ENDIF
CSGIEND
CIBM
       IF (BIOPHDFS(PPAR) .NE. SUCCESS) THEN
        TPERROR = ERR_A_DT
        RETURN
       ELSE IF (BIOPHDIS(PPAR) .NE. SUCCESS) THEN
        GOTO 2999
       ENDIF
CIBMEND
C
        IF (PAGEHEAD(PAG_DCBLEN,PPAR) .GT. EL_MAXDCBBLK*4) THEN
         TPERROR = ERR_DCBS
         GOTO 2999
        ENDIF
C
C --- Read Offset Block
C
CVAX
CVAX    BIORECNB = PGDTDIR(PARPGNUM(PPAR))
CVAX    BIOBYTNB = PAGEHEAD(PAG_OFFOFF,PPAR)
CVAX    BIOBYTCT = PAGEHEAD(PAG_OFFLEN,PPAR)
CVAX    ID       = 1-PAGEHEAD(PAG_RENTRY,PPAR)
CVAX    PGDTRTCD(PPAR) = 0
CVAX    PGDTFLAG(PPAR) = .FALSE.
CVAX    CALL BUFF_READ(PGDTUNIT
CVAX .,                %DESCR(PAGOFFBL(1,ID,PPAR))
CVAX .,                BIORECNB
CVAX .,                BIOBYTNB
CVAX .,                BIOBYTCT
CVAX .,                PGDTRTCD(PPAR)
CVAX .,                PGDTFLAG(PPAR)
CVAX .,                PGDTAST)
CVAXEND
CSGI
CSGI   BIOIOFF(PPAR).FD.FD          = BIOIOPEN.FD.FD
CSGI   BIOIOFF(PPAR).FD.RECORD_SIZE = MAX_ORECLEN
CSGI   BIOIOFF(PPAR).RECORD_NUM     = PGDTDIR(PARPGNUM(PPAR)) - 1
CSGI   BIOIOFF(PPAR).AMOUNT         = PAGEHEAD(PAG_OFFLEN,PPAR)
CSGI   BIOIOFF(PPAR).POSITION       = PAGEHEAD(PAG_OFFOFF,PPAR) - 1
CSGI   ID                           = 1-PAGEHEAD(PAG_RENTRY,PPAR)
CSGI   BIOIOFF(PPAR).BUFFERP        = %LOC(PAGOFFBL(1,ID,PPAR))
CSGI   CALL CAE_IO_READ(BIOIOFF(PPAR), PRIORITY)
CSGIEND
CIBM
       BIORECNB = PGDTDIR(PARPGNUM(PPAR)) - 1
       BIOBYTCT = PAGEHEAD(PAG_OFFLEN,PPAR)
       BIOBYTNB = PAGEHEAD(PAG_OFFOFF,PPAR) - 1
       ID       = 1 - PAGEHEAD(PAG_RENTRY,PPAR)
       CALL CAE_IO_READ(BIOOFFFS(PPAR)
     .,                 BIOOFFIS(PPAR)
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 PAGOFFBL(1,ID,PPAR)
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Read DCB Block
C
CVAX
CVAX    BIORECNB = PGDTDIR(PARPGNUM(PPAR))
CVAX    BIOBYTNB = PAGEHEAD(PAG_DCBOFF,PPAR)
CVAX    BIOBYTCT = PAGEHEAD(PAG_DCBLEN,PPAR)
CVAX    PGDTRTCD(PPAR) = 0
CVAX    PGDTFLAG(PPAR) = .FALSE.
CVAX    CALL BUFF_READ(PGDTUNIT
CVAX .,                %DESCR(I2PAGDCB(0,PPAR))
CVAX .,                BIORECNB
CVAX .,                BIOBYTNB
CVAX .,                BIOBYTCT
CVAX .,                PGDTRTCD(PPAR)
CVAX .,                PGDTFLAG(PPAR)
CVAX .,                %VAL(PGDTASRA(PPAR)))
CVAXEND
CSGI
CSGI   BIOIDCB(PPAR).FD.FD          = BIOIOPEN.FD.FD
CSGI   BIOIDCB(PPAR).FD.RECORD_SIZE = MAX_ORECLEN
CSGI   BIOIDCB(PPAR).RECORD_NUM     = PGDTDIR(PARPGNUM(PPAR)) - 1
CSGI   BIOIDCB(PPAR).AMOUNT         = PAGEHEAD(PAG_DCBLEN,PPAR)
CSGI   BIOIDCB(PPAR).POSITION       = PAGEHEAD(PAG_DCBOFF,PPAR) - 1
CSGI   BIOIDCB(PPAR).BUFFERP        = %LOC(I2PAGDCB(0,PPAR))
CSGI   CALL CAE_IO_READ(BIOIDCB(PPAR), PRIORITY)
CSGIEND
CIBM
       BIORECNB = PGDTDIR(PARPGNUM(PPAR)) - 1
       BIOBYTCT = PAGEHEAD(PAG_DCBLEN,PPAR)
       BIOBYTNB = PAGEHEAD(PAG_DCBOFF,PPAR) - 1
       ID       = 1 - PAGEHEAD(PAG_RENTRY,PPAR)
       CALL CAE_IO_READ(BIODCBFS(PPAR)
     .,                 BIODCBIS(PPAR)
     .,                 %VAL(PGDTUNIT)
     .,                 %VAL(MAX_ORECLEN)
     .,                 I2PAGDCB(0,PPAR)
     .,                 BIORECNB
     .,                 BIOBYTCT
     .,                 BIOBYTNB)
CIBMEND
C
C --- Prepare to jump to the next state
C
        PGPRSTEP(PPAR) = PGPRSTEP(PPAR) + 1
        PGPSTATE(PPAR) = PPSEQUEN(PGPRSTEP(PPAR),PGPRMODE(PPAR))
        GOTO 2999
C
C ----------------------------------------------------
C --- Page Processing --- Activate Display List    ---
C ----------------------------------------------------
C
2600    CONTINUE
C
        LDV  = PARDVNUM(PPAR)                    ! Device using this area
        SBI  = SIOWBUIN(LDV)                     ! Serial I/O Buffer index
C
        SIOWBUFF(SBI+1, LDV) = ESC               ! Change the page number
        SIOWBUFF(SBI+2, LDV) = SET_ACTIVE_PAGE
        SIOWBUFF(SBI+3, LDV) = INUMBER(3,PARESPGN(PPAR,LDV))
        SBI = SBI + 3
C
        IF (.NOT.TPXICURF(LDV)) THEN             ! Turn off text cursor
         SIOWBUFF(SBI+1, LDV) = ESC
         SIOWBUFF(SBI+2, LDV) = OFF_CURSOR
         SBI = SBI + 2
        ENDIF
C
        SIOWBUIN(LDV) = SBI                      ! Serial I/O Buffer index
C
        PAGFSTDP(LDV) = .FALSE.
        PAGFSTUP(LDV) = .FALSE.
C
C --- Prepare to jump to the next state
C
        PGPRSTEP(PPAR) = PGPRSTEP(PPAR) + 1
        PGPSTATE(PPAR) = PPSEQUEN(PGPRSTEP(PPAR),PGPRMODE(PPAR))
        GOTO 2999
C
C ----------------------------------------------------
C --- Page Processing --- Complete Page processing ---
C ----------------------------------------------------
C
2700    CONTINUE
C
C --- Buffered I/O in Progress monitoring logic
C
CVAX
CVAX   IF (.NOT. PGDTFLAG(PPAR)) THEN
CVAX    GOTO 2999
CVAX   ELSE IF (PGDTRTCD(PPAR) .NE. 1) THEN
CVAX    TPERROR = ERR_A_DC
CVAX    RETURN
CVAX   ENDIF
CVAXEND
CSGI
CSGI   IF      (BIOIDCB(PPAR).STATUS .EQ. 999) THEN
CSGI    GOTO 2999
CSGI   ELSE IF (BIOIDCB(PPAR).STATUS .NE.   1) THEN
CSGI    TPERROR = ERR_A_DC
CSGI    RETURN
CSGI   ENDIF
CSGIEND
CIBM
       IF (BIODCBFS(PPAR) .NE. SUCCESS) THEN
        TPERROR = ERR_A_DC
        RETURN
       ELSE IF (BIODCBIS(PPAR) .NE. SUCCESS) THEN
        GOTO 2999
       ENDIF
CIBMEND
C
C --- Serial I/O in Progress monitoring logic
C
        LDV = PARDVNUM(PPAR)                     ! Device using this area
CVAX
CVAX    IF (.NOT.SIOWRFLG(LDV)) THEN
CVAX     GOTO 2999                               ! Buff I/O not completed
CVAX    ENDIF
CVAX    IF ((SIOWRIEV(LDV)   .NE. 1) .OR.
CVAX .      (SIOWRSTB(1,LDV) .NE. 1) .OR.
CVAX .      (SIOWRSTB(3,LDV) .NE. 0)) THEN
CVAX     TPERROR = ERR_S_CM                      ! Buff I/O with error
CVAX     RETURN
CVAX    ENDIF
CVAXEND
CIBM
        IF (SIOWBUIS(LDV) .NE. SUCCESS) THEN
         GOTO 2999                               ! Buff I/O not completed
        ENDIF
CIBMEND
C
C --- Link all the DCB of the same cluster together
C     The DCB number of the special Display type DCB is use as the link
C     number for the other DCB part of the same cluster
C
        DO J=PAGEHEAD(PAG_FIRLIN,PPAR), PAGEHEAD(PAG_LASLIN,PPAR)
         JD   = PAGEIPDR(2,J,PPAR)               ! DCB number
         LDBP = PAGEIPDR(1,J,PPAR) - 1           ! DCB pointer
         IF (
     .  (IAND(PAGETOBL(5,J,PPAR),TTY_TCH4DP) .NE. 0)             .AND.
     .  (IAND(I2PAGDCB(LDBP+DCB_OPTIONS,PPAR), DCB_EXT_EX).NE.0) .AND.
     .  (IAND(I2PAGDCB(LDBP+DCB_EXTRA,  PPAR), DCB_CLU_EX).NE.0)) THEN
          KD = I2PAGDCB(LDBP+DCB_CLUSTER, PPAR)  ! Cluster number
          SDBP = 0                               ! Searched DCB ptr
          DO K=1, PAGEHEAD(PAG_NUMDCBS,PPAR)     ! Seacrh for other member
           IF (
     .  (IAND(I2PAGDCB(SDBP+DCB_OPTIONS,PPAR), DCB_EXT_EX).NE.0) .AND.
     .  (IAND(I2PAGDCB(SDBP+DCB_EXTRA,  PPAR), DCB_CLU_EX).NE.0)) THEN
            IF ((JD .NE. K) .AND.
     .          (I2PAGDCB(SDBP+DCB_CLUSTER, PPAR) .EQ.  KD)) THEN
             PAGCLUST(K, PPAR) = JD    ! same cluster
            ENDIF
           ENDIF
           SDBP = I2PAGDCB(SDBP+DCB_SIZE,PPAR) + 2 + SDBP
          ENDDO
         ENDIF
        ENDDO
C
C --- Indicate to the Volatile logic that the page area is ready
C
        TPXIFAST = .FALSE.
        DEVPARDY(LDV) = .TRUE.
        TPXOODCB(LDV) = 0
        IF (IAND(PAGEHEAD(PAG_TYPE,PPAR),PT_RESID) .NE. 0) THEN
         DEVPAGDL(LDV) = .TRUE.
        ENDIF
C
C --- Keep track of the page number stored on the device first page
C
        IF (PARESPGN(PPAR,LDV) .EQ. 1) THEN
         PGNUMDFP(LDV) = PARPGNUM(PPAR)
        ENDIF
C
C --- Prepare the echo area position variables
C
        LDBP = PAGEIPDR(1,1,PPAR) - 1
        IF (I2PAGDCB(LDBP+DCB_TYPE, PPAR) .EQ. ALPHANUMERIC) THEN
         DCBSIZE = I2PAGDCB(LDBP+DCB_SIZE, PPAR)
         ECHOAROW(PPAR) = I2PAGDCB(LDBP+DCBSIZE, PPAR)
         ECHOACOL(PPAR) = I2PAGDCB(LDBP+DCBSIZE+1, PPAR) -
     .                    I2PAGDCB(LDBP+ALP_NUM_CHARS, PPAR) + 1
        ELSE
         ECHOAROW(PPAR) = 1            ! Dummy value
         ECHOACOL(PPAR) = 1
        ENDIF
C
C --- Prepare to jump to the next state
C
        PGPRSTEP(PPAR) = PGPRSTEP(PPAR) + 1
        PGPSTATE(PPAR) = PPSEQUEN(PGPRSTEP(PPAR),PGPRMODE(PPAR))
        GOTO 2999
C
C ----------------------------------------------------------
C --- Page Processing --- Complete Cache Page processing ---
C ----------------------------------------------------------
C
2800    CONTINUE
C
C --- Buffered I/O in Progress monitoring logic
C
CVAX
CVAX   IF (.NOT. PGDTFLAG(PPAR)) THEN
CVAX    GOTO 2999
CVAX   ELSE IF (PGDTRTCD(PPAR) .NE. 1) THEN
CVAX    TPERROR = ERR_A_DC
CVAX    RETURN
CVAX   ENDIF
CVAXEND
CSGI
CSGI   IF      (BIOIDCB(PPAR).STATUS .EQ. 999) THEN
CSGI    GOTO 2999
CSGI   ELSE IF (BIOIDCB(PPAR).STATUS .NE.   1) THEN
CSGI    TPERROR = ERR_A_DC
CSGI    RETURN
CSGI   ENDIF
CSGIEND
CIBM
       IF (BIODCBFS(PPAR) .NE. SUCCESS) THEN
        TPERROR = ERR_A_DC
        RETURN
       ELSE IF (BIODCBIS(PPAR) .NE. SUCCESS) THEN
        GOTO 2999
       ENDIF
CIBMEND
C
C --- Serial I/O in Progress monitoring logic
C
        LDV = PARDVNUM(PPAR)           ! Device using this area
CVAX
CVAX    IF (.NOT.SIOWRFLG(LDV)) THEN
CVAX     GOTO 2999                     ! Buff I/O not completed
CVAX    ENDIF
CVAX    IF ((SIOWRIEV(LDV)   .NE. 1) .OR.
CVAX .      (SIOWRSTB(1,LDV) .NE. 1) .OR.
CVAX .      (SIOWRSTB(3,LDV) .NE. 0)) THEN
CVAX     TPERROR = ERR_S_CM            ! Buff I/O with error
CVAX     RETURN
CVAX    ENDIF
CVAXEND
CIBM
        IF (SIOWBUIS(LDV) .NE. SUCCESS) THEN
         GOTO 2999                               ! Buff I/O not completed
        ENDIF
CIBMEND
C
C --- Indicate to the Volatile logic that the page area is ready
C
        TPXIFAST = .FALSE.
        DEVPARDY(LDV) = .TRUE.
        TPXOODCB(LDV) = 0
C
C --- Keep track of the page number stored on the device first page
C
        IF (PARESPGN(PPAR,LDV) .EQ. 1) THEN
         PGNUMDFP(LDV) = PARPGNUM(PPAR)
        ENDIF
C
C --- Prepare to jump to the next state
C
        PGPRSTEP(PPAR) = PGPRSTEP(PPAR) + 1
        PGPSTATE(PPAR) = PPSEQUEN(PGPRSTEP(PPAR),PGPRMODE(PPAR))
        GOTO 2999
C
C ----------------------------------------------------
C --- Page Processing --- Iddle state              ---
C ----------------------------------------------------
C
2900    CONTINUE
        PARREADY(PPAR) = .TRUE.
C
C --- Re-issue a page processing request if such a request was pending
C
        IF (PARPENDI(PPAR)) THEN
         DO J=1, INDEV
          IF (DEVPARPD(J)) THEN
           DEVPARPD(J) = .FALSE.
           IF (DEVPARNB(J).NE.0) THEN           ! Old Page area
            PARDVNUM(DEVPARNB(J)) = 0
            PARNBUSE(DEVPARNB(J)) = PARNBUSE(DEVPARNB(J)) - 1
           ENDIF
           PARORDER = PARORDER + 1              ! New Page area
           PARORDLD(PPAR) = PARORDER
           PARDVNUM(PPAR) = J
           PARPGNUM(PPAR) = DEVPANUM(J)
           PARNBUSE(PPAR) = PARNBUSE(PPAR) + 1
           DEVPARDY(J) = .FALSE.                ! Device acces
           PAGFSTDP(J) = .TRUE.
           DEVPARNB(J) = PARLOADE
           PGPRSTEP(PPAR) = 1               ! Sequence init
           IF (PARRESID(PPAR,J)) THEN
            PGPRMODE(PPAR) = PPMD_RES
           ELSE IF (PGNUMDFP(J) .EQ. DEVPANUM(J)) THEN
            PGPRMODE(PPAR) = PPMD_RES
           ELSE
            PGPRMODE(PPAR) = PPMD_ALO
            IF (IAND(PAGEHEAD(PAG_TYPE,PPAR),              ! Page resident ?
     .                   PT_RESID) .NE. 0) THEN
             PAGEHEAD(PAG_TYPE,PPAR) =                     ! Force a resident
     .           IOR(PAGEHEAD(PAG_TYPE,PPAR),PT_UNREM)     !    page to stay
             PARUNREM(PPAR) = .TRUE.                       !    in the cache
             IF (DEVRESPC(J) .LT. NDAR) THEN
              DEVRESPC(J) = DEVRESPC(J) + 1                ! Allocate a new
              PARRESID(PPAR,J) = .TRUE.                    !    resident
              PARESPGN(PPAR,j) = DEVRESPC(J)               !    device page
             ELSE
              PAGEHEAD(PAG_TYPE,PPAR) = IAND(              ! No more resident
     .          PAGEHEAD(PAG_TYPE,PPAR),NOT(PT_RESID))     !    page area
              PARRESID(PPAR,J) = .FALSE.                   !    available
              PARESPGN(PPAR,J) = 1
             ENDIF
            ELSE
             PARRESID(PPAR,J) = .FALSE.
             PARESPGN(PPAR,J) = 1
            ENDIF
           ENDIF
           PGPSTATE(PPAR) = PPSEQUEN(PGPRSTEP(PPAR), PGPRMODE(PPAR))
           GOTO 2999
          ENDIF
         ENDDO
         PARPENDI(PPAR) = .FALSE.
        ENDIF
        GOTO 2999
C
C ----------------------------------------------------
C --- Page processing --- Exit point               ---
C ----------------------------------------------------
C
2999    CONTINUE
        IF (PPAR .EQ. NPAR) THEN       ! Point to the next page area
         PPAR = 1
        ELSE
         PPAR = PPAR + 1
        ENDIF
       ENDDO
C
C --------------------------------------
C ---   Volatile Update Processing   ---
C --------------------------------------
C
C --- Loop for all devices
C
       DO I=1, NUMDEVPP
3000    CONTINUE
C
C --- Treat next device
C
        IF (TPXIFAST .AND.                       ! Fast update
     .      (.NOT. PAGFSTDP(TPXOODEV)) .AND.
     .      (.NOT. PAGFSTUP(TPXOODEV))) THEN
         ODEV = TPXIIDEV
        ELSE IF ((TPXOODEV.EQ.INDEV) .OR.        ! Loop Back
     .           (TPXOODEV.EQ.0)) THEN
         TPXOODEV = 1
         ODEV = TPXOODEV
        ELSE                                     ! Next device
         TPXOODEV = TPXOODEV + 1
         ODEV = TPXOODEV
        ENDIF
C
C --- Serial I/O in Progress monitoring logic
C
        IF (SIOASSIF(ODEV)) THEN
        IF (DEVPARDY(ODEV)) THEN
C
CVAX
CVAX    IF (SIOWRFLG(ODEV)) THEN
CVAX     IF ((SIOWRIEV(ODEV)   .NE. 1) .OR.      ! Buff I/O compl. with error
CVAX .       (SIOWRSTB(1,ODEV) .NE. 1) .OR.
CVAX .       (SIOWRSTB(3,ODEV) .NE. 0)) THEN
CVAX      TPERROR = ERR_S_CM
CVAX      RETURN
CVAX     ENDIF
CVAXEND
CIBM
        IF (SIOWBUIS(ODEV) .EQ. SUCCESS) THEN
CIBMEND
C
         TPACTDEV = ODEV                         ! Device index
         TPXOREQP = TPXOPAGE(ODEV)               ! To display request page
         OPAR = DEVPARNB(ODEV)                   ! Page area number
         SBI = SIOWBUIN(ODEV)                    ! Serial I/O buff index
C
C ---------------------------------------------------------------------
C ---   Volatile Update   ---   Loop for a certain number of DCBs   ---
C ---------------------------------------------------------------------
C
         DO J=1 , NUMDCBPP
3010      CONTINUE
C
C --- Setting of the DCB index and the input dictionary DCB index
C
C ---  If there is a request for a fast update
C
          IF (TPXIFAST .AND.
     .        (TPXIIDEV .EQ. ODEV) .AND.
     .        (.NOT. PAGFSTDP(ODEV)) .AND.
     .        (.NOT. PAGFSTUP(ODEV))) THEN
           ILIN = TPXIILIN
           ODCB = PAGEIPDR(2,ILIN,OPAR)
           ODBP = PAGEIPDR(1,ILIN,OPAR) - 1
           TPXIFAST = .FALSE.
C
C ---  If their is no DCBs in that page
C
          ELSE IF (PAGEHEAD(PAG_NUMDCBS,OPAR) .EQ. 0) THEN
           GOTO 5990
C
C ---  If it is the first update of the first DCB
C
          ELSE IF (TPXOODCB(ODEV).EQ.0) THEN
           TPXOILIN(ODEV) = 1
           ILIN = 1
           TPXOODCB(ODEV) = 1
           ODCB = 1
           TPXOODBP(ODEV) = 0
           ODBP = 0
C
C ---  If it was the update of the last DCB
C
          ELSE IF (TPXOODCB(ODEV).EQ. PAGEHEAD(PAG_NUMDCBS,OPAR)) THEN
           TPXOILIN(ODEV) = 1
           ILIN = 1
           TPXOODCB(ODEV) = 1
           ODCB = 1
           TPXOODBP(ODEV) = 0
           ODBP = 0
           IF (PAGFSTDP(ODEV)) THEN
            PAGFSTDP(ODEV) = .FALSE.
            PAGFSTUP(ODEV) = .TRUE.
            IF (PARRESID(OPAR,ODEV)) THEN
             DEVPAGDL(ODEV) = .FALSE.
            ENDIF
           ELSE IF (PAGFSTUP(ODEV)) THEN
            PAGFSTUP(ODEV) = .FALSE.
           ENDIF
C
C ---  If it is a normal DCB index increment
C
          ELSE
           IF (PAGEIPDR(2,TPXOILIN(ODEV),OPAR).EQ.TPXOODCB(ODEV)) THEN
            TPXOILIN(ODEV)  = TPXOILIN(ODEV) + 1
            ILIN = TPXOILIN(ODEV)
            IF (ILIN .GT. PAGEHEAD(PAG_LASLIN,OPAR)) THEN
             TPXOILIN(ODEV) = 1
             ILIN = TPXOILIN(ODEV)
            ENDIF
           ELSE
            ILIN = TPXOILIN(ODEV)
           ENDIF
           TPXOODCB(ODEV) = TPXOODCB(ODEV) + 1
           ODCB = TPXOODCB(ODEV)
           TPXOODBP(ODEV) = TPXOODBP(ODEV) + I2PAGDCB(TPXOODBP(ODEV) +
     .                      DCB_SIZE,OPAR) + 2
           ODBP = TPXOODBP(ODEV)
          ENDIF
C
C  Volatile update Logic
C
C +--------+-----+----+-----------------------------------+---+
C |        |  S  |    | Non-   Butt   Vola   State  Color | O |
C |  I     |  t  | T  | Volat  Conto  tile   Color  Zone  | r |
C |  t     |  a  | y  | Draw   Draw   Draw   Toggl  Erasi | d |
C |  e     |  t  | p  +-----------------------------------+ e |
C |  m     |  e  | e  | 5200   5300   5400   5500   5600  | r |
C +--------+-----+----+-----------------------------------+---+
C | NewPag |  T  |Flip|  F     S=DII  S=DII S=DIIA  S=DIA |   |
C | NewPag |  T  |Mult|  F     S=DII  S=DII   F     S=DIA |   |
C | NewPag |  T  |Dec |  F     S=DII  S=DII   F     S=DIA |   |
C +--------+-----+----+-----------------------------------+---+
C | Dark   | F2T |    |  F      F      F      F      T    | | |
C | Dark   | T2F |Flip|  T      T      T    S=ACT    F    | | |
C | Dark   | T2F |Mult|  T      T      T      F      F    | | |
C | Dark   | T2F |Dec |  T      T      T      F      F    | | |
C | Dark   |  F  |Flip|  F      F     S=CHG S=CHG    F    | | |
C | Dark   |  F  |Mult|  F      F      T      F      F    | | |
C | Dark   |  F  |Dec |  F      F     S=CHG   F      F    | V |
C | Dark   |  T  |    |  F      F      F      F      F    |   |
C +--------+-----+----+-----------------------------------+---+
C
C  Notes:
C   1- The use of the verb is represent the evaluation of the present value
C   2- The use of the verb was represent the evaluation of the previous value
C   3- Button contour draw can only be true when an input line is available
C
C  Abreviations:
C   3-  S=ACT  :State is true if the label value is active and false if not
C   4-  S=CHG  :State is true if the label value has changed and false if not
C   5-  S=DII  :State is true if dark concept is inactive and false if active
C   6-  S=DII  :State is true if dark concept is active and false if not
C   7-  S=DIIA :State is true if dark concept is inactive and
C               if the label value is active and false if not
C
C --------------------------------------------------------
C ---   Volatile Update   ---   Variables evaluation   ---
C --------------------------------------------------------
C
C ---------------------------------------------------------------------------
C ---   Volatile Update   ---   Variables evaluation   --- Previous value ---
C ---------------------------------------------------------------------------
C
3100      CONTINUE
C
C --- Evaluate the previous value
C
          PRVDRKST = PAGDRKST(ODCB,PARESPGN(OPAR,ODEV),ODEV)
          PRVHIGST = PAGHIGST(ODCB,PARESPGN(OPAR,ODEV),ODEV)
          R8PRVVAL = R8PAGVAL(ODCB,PARESPGN(OPAR,ODEV),ODEV)
C
C -----------------------------------------------------------------------------
C ---   Volatile Update   ---   Variables evaluation   ---   Actual value   ---
C -----------------------------------------------------------------------------
C
3200      CONTINUE
C
          DCBTYPE = I2PAGDCB(ODBP+DCB_TYPE,OPAR) ! Local DCB type
C
C --- Jump according to the DCB type
C
          GOTO (3210                             ! Decimal
     .,         3220                             ! Boolean
     .,         3230                             ! Alphanumeric
     .,         3210                             ! Angle
     .,         3210                             ! Lat/Long
     .,         3210                             ! Time
     .,         3240                             ! Set Value
     .,         3300                             ! Spare
     .,         3250                             ! Variable Malfunction
     .,         3260) DCBTYPE                    ! Multiple
          GOTO  3300
C
C --- Evaluate Decimal Actual value
C
3210      CONTINUE
          FLIPADCB = .FALSE.
          MULVLDCB = .FALSE.
C
          DATATYPE = I2PAGDCB(ODBP+DCB_DATA_TYPE,OPAR)
C
          CALL XDCDBRD(I4PAGDCB((ODBP+DCB_VAR_OFFSET)/2, OPAR)
     .,                0                     ! Check with used directives
     .,                I1TMPVAL(1)
     .,                DATATYPE
     .,                PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                VAL_TABLE)
C
          GOTO (3211                         ! Integer*2
     .,         3212                         ! Integer*4
     .,         3213                         ! Real*4
     .,         3214) DATATYPE               ! Real*8
          GOTO  3300
C
3211      CONTINUE                           ! Integer*2
          R8ACTVAL = I2TMPVAL(1)
          GOTO 3300
3212      CONTINUE                           ! Integer*4
          R8ACTVAL = I4TMPVAL
          GOTO 3300
3213      CONTINUE                           ! Real*4
          R8ACTVAL = R4TMPVAL
          GOTO 3300
3214      CONTINUE                           ! Real*8
          R8ACTVAL = R8TMPVAL
          GOTO 3300
C
C --- Evaluate Boolean Actual value
C
3220      CONTINUE
C
          FLIPADCB = .TRUE.
          MULVLDCB = .FALSE.
C
          CALL XDCDBRD(I4PAGDCB((ODBP+DCB_VAR_OFFSET)/2, OPAR)
     .,                0                     ! Check with used directives
     .,                L1ACTVAL
     .,                DTYP_BYTE
     .,                PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                VAL_TABLE)
C
          GOTO 3300
C
C --- Evaluate String Actual value
C
3230      CONTINUE
          FLIPADCB = .FALSE.
          MULVLDCB = .TRUE.
          GOTO 3300
C
C --- Evaluate the actual set-value state
C
3240      CONTINUE
          IF (I2PAGDCB(ODBP+STV_DP_CODE,OPAR).EQ.0) THEN
           SDBP = ODBP + STV_SUB_STRT
          ELSE
           SDBP = ODBP + STV_DSP_STRT
          ENDIF
C
          FLIPADCB = .TRUE.
          MULVLDCB = .FALSE.
C
          CALL XDSETOUT(I2PAGDCB(ODBP,   OPAR)
     .,                 I4PAGDCB(ODBP/2, OPAR)
     .,                 R4PAGDCB(ODBP/2, OPAR)
     .,                 OSTRINGC
     .,                 OSTRSIZE
     .,                 L1ACTVAL
     .,                 PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                 VAL_TABLE)
C
          GOTO 3300
C
C --- Evaluate Variable malfunction Actual value
C
3250      CONTINUE
          FLIPADCB = .FALSE.
          MULVLDCB = .TRUE.
C
C --- Evaluate Muliple Actual value
C
3260      CONTINUE
          FLIPADCB = .FALSE.
          MULVLDCB = .TRUE.
C
C ----------------------------------------------------------------------
C ---   Volatile Update   ---   Variables evaluation   ---   Color   ---
C ----------------------------------------------------------------------
C
3300      CONTINUE
C
C --- Color condition compute
C
          IF (PAGCLUST(ODCB,OPAR).EQ.NULL) THEN
           IF (DCBTYPE .EQ. MULTIPLE) THEN
            SDBP = ODBP + MUL_SUB_STRT
           ELSE
            SDBP = ODBP
           ENDIF
           IF (IAND(I2PAGDCB(SDBP+DCB_OPTIONS,OPAR)
     .,             DCB_COLS_EX) .NE. 0) THEN
C
C --- Jump according to the DCB type
C
            GOTO (3310                           ! Decimal
     .,           3320                           ! Boolean
     .,           3330                           ! Alphanumeric
     .,           3340                           ! Angle
     .,           3350                           ! Lat/Long
     .,           3360                           ! Time
     .,           3370                           ! Set Value
     .,           3390                           ! Spare
     .,           3380                           ! Variable Malfunction
     .,           3390) DCBTYPE                  ! Multiple
            GOTO  3390

C
C --- Evaluate Color Condition on Decimal
C
3310        CONTINUE
            CALL XDSUBDCB(I2PAGDCB( SDBP+DEC_CLR_STRT,    OPAR)
     .,                   I4PAGDCB((SDBP+DEC_CLR_STRT)/2, OPAR)
     .,                   R4PAGDCB((SDBP+DEC_CLR_STRT)/2, OPAR)
     .,                   I2PAGDCB( SDBP+DCB_COLOR_CTN,   OPAR)
     .,                   COLORSTA
     .,                   COLORNUM
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
            GOTO 3399
C
C --- Evaluate Color Condition on Boolean
C
3320        CONTINUE
            CALL XDSUBDCB(I2PAGDCB( SDBP+BOL_CLR_STRT,    OPAR)
     .,                   I4PAGDCB((SDBP+BOL_CLR_STRT)/2, OPAR)
     .,                   R4PAGDCB((SDBP+BOL_CLR_STRT)/2, OPAR)
     .,                   I2PAGDCB( SDBP+DCB_COLOR_CTN,   OPAR)
     .,                   COLORSTA
     .,                   COLORNUM
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
            GOTO 3399
C
C --- Evaluate Color Condition on Alphanumeric
C
3330        CONTINUE
            CALL XDSUBDCB(I2PAGDCB( SDBP+ALP_CLR_STRT,    OPAR)
     .,                   I4PAGDCB((SDBP+ALP_CLR_STRT)/2, OPAR)
     .,                   R4PAGDCB((SDBP+ALP_CLR_STRT)/2, OPAR)
     .,                   I2PAGDCB( SDBP+DCB_COLOR_CTN,   OPAR)
     .,                   COLORSTA
     .,                   COLORNUM
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
            GOTO 3399
C
C --- Evaluate Color Condition on Angle
C
3340        CONTINUE
            CALL XDSUBDCB(I2PAGDCB( SDBP+ANG_CLR_STRT,    OPAR)
     .,                   I4PAGDCB((SDBP+ANG_CLR_STRT)/2, OPAR)
     .,                   R4PAGDCB((SDBP+ANG_CLR_STRT)/2, OPAR)
     .,                   I2PAGDCB( SDBP+DCB_COLOR_CTN,   OPAR)
     .,                   COLORSTA
     .,                   COLORNUM
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
            GOTO 3399
C
C --- Evaluate Color Condition on Lat/Long
C
3350        CONTINUE
            CALL XDSUBDCB(I2PAGDCB( SDBP+LAT_CLR_STRT,    OPAR)
     .,                   I4PAGDCB((SDBP+LAT_CLR_STRT)/2, OPAR)
     .,                   R4PAGDCB((SDBP+LAT_CLR_STRT)/2, OPAR)
     .,                   I2PAGDCB( SDBP+DCB_COLOR_CTN,   OPAR)
     .,                   COLORSTA
     .,                   COLORNUM
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
            GOTO 3399
C
C --- Evaluate Color Condition on Time
C
3360        CONTINUE
            CALL XDSUBDCB(I2PAGDCB( SDBP+TIM_CLR_STRT,    OPAR)
     .,                   I4PAGDCB((SDBP+TIM_CLR_STRT)/2, OPAR)
     .,                   R4PAGDCB((SDBP+TIM_CLR_STRT)/2, OPAR)
     .,                   I2PAGDCB( SDBP+DCB_COLOR_CTN,   OPAR)
     .,                   COLORSTA
     .,                   COLORNUM
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
            GOTO 3399
C
C --- Evaluate Color Condition on Set Value
C
3370        CONTINUE
            IF (I2PAGDCB(SDBP+STV_DP_CODE,OPAR) .NE. 0) THEN
             ID = SDBP + STV_DSP_STRT +
     .            (STV_SBL_SIZ * I2PAGDCB( SDBP+STV_VALS_NUM, OPAR))
            ELSE
             ID = SDBP + STV_SUB_STRT +
     .            (STV_SBL_SIZ * I2PAGDCB( SDBP+STV_VALS_NUM, OPAR))
            ENDIF
            CALL XDSUBDCB(I2PAGDCB(ID,   OPAR)
     .,                   I4PAGDCB(ID/2, OPAR)
     .,                   R4PAGDCB(ID/2, OPAR)
     .,                   I2PAGDCB(SDBP+DCB_COLOR_CTN,   OPAR)
     .,                   COLORSTA
     .,                   COLORNUM
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
            GOTO 3399
C
C --- Evaluate Color Condition on Variable Malfunction
C
3380        CONTINUE
            CALL XDSUBDCB(I2PAGDCB( SDBP+VAR_CLR_STRT,    OPAR)
     .,                   I4PAGDCB((SDBP+VAR_CLR_STRT)/2, OPAR)
     .,                   R4PAGDCB((SDBP+VAR_CLR_STRT)/2, OPAR)
     .,                   I2PAGDCB( SDBP+DCB_COLOR_CTN,   OPAR)
     .,                   COLORSTA
     .,                   COLORNUM
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
            GOTO 3399
C
C --- Force False Color Condition on invalid DCB type
C
3390        CONTINUE
            COLORSTA = .FALSE.
C
C --- Translate the result from the directive call into :
c                 - a Dark Concept Logical
C                 - an Highlight Logical
C
3399        CONTINUE
            IF (COLORSTA) THEN
             ACTDRKST = COLORNUM .EQ. TRANSPARENT ! Evaluate Color condition
             ACTCOLCD = HIGHCOLO(COLORNUM)
            ELSE                                  ! If their is no active
             ACTDRKST = .FALSE.                   !       color condition
             ACTCOLCD = .FALSE.
            ENDIF
           ELSE                                   ! If their is no color
            ACTDRKST = .FALSE.                    !       condition
            ACTCOLCD = .FALSE.
           ENDIF
          ELSE                                    ! Clustered color evaluation
           ACTDRKST = PAGDRKST(PAGCLUST(ODCB,OPAR)
     .,                        PARESPGN(OPAR,ODEV)
     .,                        ODEV)
           ACTCOLCD = .FALSE.
          ENDIF
C
C ------------------------------------------------------------------------
C ---   Volatile Update   ---   Active Color to highlight Conversion   ---
C ------------------------------------------------------------------------
C
          IF (FLIPADCB) THEN                          ! Flipable DCB
           INACTCOL = HIGHCOLO(I1PAGCOL(2,ODCB,OPAR)) ! Inactive color highli
           ACTIVCOL = HIGHCOLO(I1PAGCOL(3,ODCB,OPAR)) ! Active color highligh
           IF (INACTCOL .NEQV. ACTIVCOL) THEN         ! Normal color state
            ACTHIGST = ACTCOLCD .OR. (L1ACTVAL.EQV.ACTIVCOL)
           ELSE                                       ! If both same color
            ACTHIGST = ACTCOLCD .OR. INACTCOL
           ENDIF
          ELSE                                        ! Non Flipable DCB
           ACTHIGST = ACTCOLCD
          ENDIF
C
C -------------------------------------------------------------------
C ---   Volatile Update   ---   Monitoring   ---   New Page       ---
C -------------------------------------------------------------------
C
4000      CONTINUE
C
C --- If the page is not displayed yet
C
          IF (PAGFSTDP(ODEV)) THEN
           NONVOLDW = .FALSE.
           BUTCTNDW = (PAGEIPDR(2,ILIN,OPAR) .EQ. ODCB)
           VOLATIDW = .FALSE.
           STACOLTO = .FALSE.
           COLZONER = .FALSE.
C
C --- If the page is not updated at lease once yet
C
          ELSE IF (PAGFSTUP(ODEV)) THEN
           NONVOLDW = .FALSE.
           BUTCTNDW = .FALSE.
           VOLATIDW = .TRUE.
           IF (PAGCLUST(ODCB,OPAR).EQ.NULL) THEN
            VOLAHIGH = .FALSE.
            STACOLTO = ACTHIGST
           ELSE
            VOLAHIGH = PAGHIGST(PAGCLUST(ODCB,OPAR)
     .,                         PARESPGN(OPAR,ODEV)
     .,                         ODEV)
            STACOLTO = .FALSE.
           ENDIF
           COLZONER = ACTDRKST
C
C -------------------------------------------------------------------
C ---   Volatile Update   ---   Monitoring   ---   Dark Concept   ---
C -------------------------------------------------------------------
C
C --- If the Touch type require the center of the button to be updated
C
          ELSE IF ((PAGEIPDR(2,ILIN,OPAR) .EQ. ODCB) .AND.
     .             (IAND(PAGETOBL(5,ILIN,OPAR),TTY_DRKC) .NE. 0)) THEN
           NONVOLDW = .FALSE.
           IF ((.NOT.PRVDRKST) .AND. ACTDRKST) THEN
            BUTCTNDW = .FALSE.
            COLZONER = .TRUE.
           ELSE IF (PRVDRKST.AND.(.NOT.ACTDRKST)) THEN
            BUTCTNDW = .TRUE.
            COLZONER = .FALSE.
           ELSE
            BUTCTNDW = .FALSE.
            COLZONER = .FALSE.
           ENDIF
           IF (FLIPADCB) THEN
            VOLATIDW = (L1PRVVAL .NEQV. L1ACTVAL)
            IF (PAGCLUST(ODCB,OPAR).EQ.NULL) THEN
             VOLAHIGH = PRVHIGST
             STACOLTO = ACTHIGST .NEQV. PRVHIGST
            ELSE
             VOLAHIGH = PAGHIGST(PAGCLUST(ODCB,OPAR)
     .,                          PARESPGN(OPAR,ODEV)
     .,                          ODEV)
             STACOLTO = .FALSE.
            ENDIF
           ELSE IF (MULVLDCB) THEN
            VOLATIDW = .TRUE.
            IF (PAGCLUST(ODCB,OPAR).EQ.NULL) THEN
             VOLAHIGH = PRVHIGST
             STACOLTO = ACTHIGST .NEQV. PRVHIGST
            ELSE
             VOLAHIGH = PAGHIGST(PAGCLUST(ODCB,OPAR)
     .,                          PARESPGN(OPAR,ODEV)
     .,                          ODEV)
             STACOLTO = .FALSE.
            ENDIF
           ELSE IF (PAGCLUST(ODCB,OPAR).EQ.NULL) THEN
            VOLATIDW = (R8PRVVAL .NE. R8ACTVAL)
            VOLAHIGH = PRVHIGST
            STACOLTO = ACTHIGST .NEQV. PRVHIGST
           ELSE
            VOLATIDW = (R8PRVVAL .NE. R8ACTVAL)
            VOLAHIGH = PAGHIGST(PAGCLUST(ODCB,OPAR)
     .,                         PARESPGN(OPAR,ODEV)
     .,                         ODEV)
            STACOLTO = .FALSE.
           ENDIF
C
C --- If their is a transition of the Dark concept variable: False to True
C
          ELSE IF ((.NOT.PRVDRKST) .AND. ACTDRKST) THEN
           NONVOLDW = .FALSE.
           BUTCTNDW = .FALSE.
           VOLATIDW = .FALSE.
           STACOLTO = .FALSE.
           COLZONER = .TRUE.
C
C --- If their is a transition of the Dark concept variable: True to False
C
          ELSE IF (PRVDRKST.AND.(.NOT.ACTDRKST)) THEN
           NONVOLDW = .TRUE.
           BUTCTNDW = (PAGEIPDR(2,ILIN,OPAR) .EQ. ODCB)
           VOLATIDW = .TRUE.
           IF (PAGCLUST(ODCB,OPAR).EQ.NULL) THEN ! Color Cluster process
            VOLAHIGH = .FALSE.
            STACOLTO = ACTHIGST
           ELSE                                  ! Cluster exist
            VOLAHIGH = PAGHIGST(PAGCLUST(ODCB,OPAR)
     .,                         PARESPGN(OPAR,ODEV)
     .,                         ODEV)
            STACOLTO = .FALSE.
           ENDIF
           COLZONER = .FALSE.
C
C --- If the Dark concept variable is inactive
C
          ELSE IF (.NOT.ACTDRKST) THEN
           NONVOLDW = .FALSE.
           BUTCTNDW = .FALSE.
           IF (FLIPADCB) THEN
            VOLATIDW = (L1PRVVAL .NEQV. L1ACTVAL)
            IF (PAGCLUST(ODCB,OPAR).EQ.NULL) THEN
             VOLAHIGH = PRVHIGST
             STACOLTO = ACTHIGST .NEQV. PRVHIGST
            ELSE
             VOLAHIGH = PAGHIGST(PAGCLUST(ODCB,OPAR)
     .,                          PARESPGN(OPAR,ODEV)
     .,                          ODEV)
             STACOLTO = .FALSE.
            ENDIF
           ELSE IF (MULVLDCB) THEN
            VOLATIDW = .TRUE.
            IF (PAGCLUST(ODCB,OPAR).EQ.NULL) THEN
             VOLAHIGH = PRVHIGST
             STACOLTO = ACTHIGST .NEQV. PRVHIGST
            ELSE
             VOLAHIGH = PAGHIGST(PAGCLUST(ODCB,OPAR)
     .,                          PARESPGN(OPAR,ODEV)
     .,                          ODEV)
             STACOLTO = .FALSE.
            ENDIF
           ELSE IF (PAGCLUST(ODCB,OPAR).EQ.NULL) THEN
            VOLATIDW = (R8PRVVAL .NE. R8ACTVAL)
            VOLAHIGH = PRVHIGST
            STACOLTO = ACTHIGST .NEQV. PRVHIGST
           ELSE
            VOLATIDW = (R8PRVVAL .NE. R8ACTVAL)
            VOLAHIGH = PAGHIGST(PAGCLUST(ODCB,OPAR)
     .,                         PARESPGN(OPAR,ODEV)
     .,                         ODEV)
            STACOLTO = .FALSE.
           ENDIF
           COLZONER = .FALSE.
C
C --- If the Dark concept variable is active
C
          ELSE
           NONVOLDW = .FALSE.
           BUTCTNDW = .FALSE.
           VOLATIDW = .FALSE.
           STACOLTO = .FALSE.
           COLZONER = .FALSE.
          ENDIF
C
C --- Set the transistion monitoring variables to the actual value
C
          PAGDRKST(ODCB,PARESPGN(OPAR,ODEV),ODEV) = ACTDRKST
          PAGHIGST(ODCB,PARESPGN(OPAR,ODEV),ODEV) = ACTHIGST
          R8PAGVAL(ODCB,PARESPGN(OPAR,ODEV),ODEV) = R8ACTVAL
C
C ------------------------------------------
C ---   Volatile Udate   ---   Display   ---
C ------------------------------------------
C
5000      CONTINUE
C
C ----------------------------------------------------------------
C ---   Volatile Udate   ---    Display   ---   Non-volatile   ---
C ----------------------------------------------------------------
C
          IF (NONVOLDW) THEN
5200       CONTINUE
           PAGE1POS = PAGETXPO(1,ODCB,OPAR)
           PAGE2POS = PAGETXPO(2,ODCB,OPAR)
           PAGENVNC = PAGEHEAD(PAG_C_INFO,OPAR)
           FBI = ((PAGETXLM(1,ODCB,OPAR)-1) * PAGENVNC) +
     .           PAGETXLM(2,ODCB,OPAR) - 1
C
           DO K=PAGETXLM(1,ODCB,OPAR), PAGETXLM(3,ODCB,OPAR)
            SIOWBUFF(SBI+1, ODEV) = ESC          ! Send the text position
            SIOWBUFF(SBI+2, ODEV) = GRAPHICS_TEXT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGE1POS)
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGE1POS)
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGE1POS)
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGE2POS)
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGE2POS)
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGE2POS)
            SBI = SBI + 8
            DO L=1, PAGETXLM(4,ODCB,OPAR) - PAGETXLM(2,ODCB,OPAR) + 1
             SIOWBUFF(SBI+L,ODEV) = PAGENOVB(FBI+L,OPAR)
            ENDDO
            SBI = SBI+ PAGETXLM(4,ODCB,OPAR)- PAGETXLM(2,ODCB,OPAR)+ 1
C
            PAGE1POS = PAGE1POS + PAGENVIN(1,OPAR) ! Increment screen posit
            PAGE2POS = PAGE2POS + PAGENVIN(2,OPAR)
            FBI = FBI + PAGENVNC
           ENDDO
          ENDIF
C
C -----------------------------------------------------------------------
C ---   Volatile Udate   ---    Display   ---   Touch Block Contour   ---
C -----------------------------------------------------------------------
C
          IF (BUTCTNDW) THEN
5300       CONTINUE
           IF (IAND(PAGETOBL(5,ILIN,OPAR),TTY_EDGE_S) .EQ. 0) THEN
            SIOWBUFF(SBI+1, ODEV) = ESC
            SIOWBUFF(SBI+2, ODEV) = DRAW_RECT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGETOZN(1,ILIN,OPAR))
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGETOZN(1,ILIN,OPAR))
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGETOZN(1,ILIN,OPAR))
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGETOZN(2,ILIN,OPAR))
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGETOZN(2,ILIN,OPAR))
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGETOZN(2,ILIN,OPAR))
            SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGETOZN(3,ILIN,OPAR))
            SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGETOZN(3,ILIN,OPAR))
            SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGETOZN(3,ILIN,OPAR))
            SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGETOZN(4,ILIN,OPAR))
            SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGETOZN(4,ILIN,OPAR))
            SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGETOZN(4,ILIN,OPAR))
            SBI = SBI + 14
           ENDIF
C
           IF (IAND(PAGETOBL(5,ILIN,OPAR),TTY_EDGE) .NE. 0) THEN
C
            I2KD = TTY_EDGE_1                    ! prepare the mask
            DO K=1 , 3
             IF (IAND(PAGETOBL(5,ILIN,OPAR),I2KD) .NE. 0) THEN
              SIOWBUFF(SBI+1, ODEV) = ESC
              SIOWBUFF(SBI+2, ODEV) = DRAW_RECT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGETOZN(1,ILIN,OPAR)+K)
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGETOZN(1,ILIN,OPAR)+K)
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGETOZN(1,ILIN,OPAR)+K)
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGETOZN(2,ILIN,OPAR)+K)
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGETOZN(2,ILIN,OPAR)+K)
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGETOZN(2,ILIN,OPAR)+K)
            SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGETOZN(3,ILIN,OPAR)-K)
            SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGETOZN(3,ILIN,OPAR)-K)
            SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGETOZN(3,ILIN,OPAR)-K)
            SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGETOZN(4,ILIN,OPAR)-K)
            SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGETOZN(4,ILIN,OPAR)-K)
            SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGETOZN(4,ILIN,OPAR)-K)
              SBI = SBI + 14
             ENDIF
             I2KD = I2KD * 2                     ! Shift the mask of one
            ENDDO
           ENDIF
          ENDIF
C
C -------------------------------------------------------------------
C ---   Volatile Udate   ---    Display   ---   String volatile   ---
C -------------------------------------------------------------------
C
          IF (VOLATIDW) THEN
5400       CONTINUE
C
           GOTO (5401                            ! Decimal
     .,          5402                            ! Boolean
     .,          5403                            ! Alphanumeric
     .,          5404                            ! Angle
     .,          5405                            ! Lat/Long
     .,          5406                            ! Time
     .,          5407                            ! Set Value
     .,          5499                            ! Spare
     .,          5409                            ! Variable Malfunction
     .,          5410) DCBTYPE                   ! Multiple
           GOTO  5499
C
5401       CALL XDDECOUT(I2PAGDCB(ODBP,   OPAR)  ! Decimal
     .,                  I4PAGDCB(ODBP/2, OPAR)
     .,                  R4PAGDCB(ODBP/2, OPAR)
     .,                  OSTRINGC
     .,                  OSTRSIZE
     .,                  PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                  VAL_TABLE)
           GOTO 5490
C
5402       CALL XDBOOLOUT(I2PAGDCB(ODBP,   OPAR) ! Boolean
     .,                   I4PAGDCB(ODBP/2, OPAR)
     .,                   R4PAGDCB(ODBP/2, OPAR)
     .,                   OSTRINGC
     .,                   OSTRSIZE
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
           GOTO 5490
C
5403       CALL XDALPOUT(I2PAGDCB(ODBP,   OPAR)  ! Alphanumeric
     .,                  I4PAGDCB(ODBP/2, OPAR)
     .,                  R4PAGDCB(ODBP/2, OPAR)
     .,                  OSTRINGC
     .,                  OSTRSIZE
     .,                  PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                  VAL_TABLE)
           GOTO 5490
C
5404       CALL XDANGOUT(I2PAGDCB(ODBP,   OPAR)  ! Angle
     .,                  I4PAGDCB(ODBP/2, OPAR)
     .,                  R4PAGDCB(ODBP/2, OPAR)
     .,                  OSTRINGC
     .,                  OSTRSIZE
     .,                  PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                  VAL_TABLE)
           GOTO 5490
C
5405       CALL XDLATOUT(I2PAGDCB(ODBP,   OPAR)  ! Lat/Long
     .,                  I4PAGDCB(ODBP/2, OPAR)
     .,                  R4PAGDCB(ODBP/2, OPAR)
     .,                  OSTRINGC
     .,                  OSTRSIZE
     .,                  PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                  VAL_TABLE)
           GOTO 5490
C
5406       CALL XDTIMEOUT(I2PAGDCB(ODBP,   OPAR) ! Time
     .,                   I4PAGDCB(ODBP/2, OPAR)
     .,                   R4PAGDCB(ODBP/2, OPAR)
     .,                   OSTRINGC
     .,                   OSTRSIZE
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
           GOTO 5490
C
5407       CALL XDSETOUT(I2PAGDCB(ODBP,   OPAR)  ! Set Value
     .,                  I4PAGDCB(ODBP/2, OPAR)
     .,                  R4PAGDCB(ODBP/2, OPAR)
     .,                  OSTRINGC
     .,                  OSTRSIZE
     .,                  DIRERESU
     .,                  PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                  VAL_TABLE)
           GOTO 5490
C
5409       CALL XDVMLFOUT(I2PAGDCB(ODBP,   OPAR) ! Variable Malfunction
     .,                   I4PAGDCB(ODBP/2, OPAR)
     .,                   OSTRINGC
     .,                   OSTRSIZE
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
           GOTO 5490
C
5410       CALL XDMULTOUT(I2PAGDCB(ODBP,   OPAR) ! Multiple
     .,                   I4PAGDCB(ODBP/2, OPAR)
     .,                   R4PAGDCB(ODBP/2, OPAR)
     .,                   OSTRINGC
     .,                   OSTRSIZE
     .,                   PAGOFFBL(1,SIZE_LOC,OPAR)
     .,                   VAL_TABLE)
C
5490       CONTINUE
           IF (.NOT.((OSTRINGB(1).EQ. CHR_SPAC) .AND.
     .               (OSTRSIZE   .EQ. 1  ))) THEN
            DCBSIZE = I2PAGDCB(ODBP+DCB_SIZE,OPAR)
C
C --- Position the graphic text cursor
C
            PAGE1POS = PAGENVPO(1,OPAR) + (PAGENVCS(1,OPAR) *
     .                 (I2PAGDCB(ODBP+DCBSIZE,OPAR)-1))
            PAGE2POS = PAGENVPO(2,OPAR) + (PAGENVCS(2,OPAR) *
     .                 (I2PAGDCB(ODBP+DCBSIZE+1,OPAR)-OSTRSIZE))
C
C --- If the DCB is part of a cluster then the master DCB state must be used
C     to determine if the string has to be displayed in reverse video
C
            IF (VOLAHIGH) THEN
             SIOWBUFF(SBI+1, ODEV) = ESC
             SIOWBUFF(SBI+2, ODEV) = AVIDEO_CHAR_ATTR
             SIOWBUFF(SBI+3, ODEV) =
     .                INUMBER(3,4+IAND(1,PAGEHEAD(PAG_CHRSIZ,OPAR))*2)
             SBI = SBI + 3
            ENDIF
C
            SIOWBUFF(SBI+1, ODEV) = ESC          ! Send the text position
            SIOWBUFF(SBI+2, ODEV) = GRAPHICS_TEXT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGE1POS)
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGE1POS)
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGE1POS)
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGE2POS)
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGE2POS)
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGE2POS)
            SBI = SBI + 8
C
            DO L=1, OSTRSIZE
             SIOWBUFF(SBI+L,ODEV) = OSTRINGB(L)
            ENDDO
            SBI = SBI + OSTRSIZE
C
C --- If the string was drawend reverse then the mode is brought back normal
C
            IF (VOLAHIGH) THEN
             SIOWBUFF(SBI+1, ODEV) = ESC
             SIOWBUFF(SBI+2, ODEV) = AVIDEO_CHAR_ATTR
             SIOWBUFF(SBI+3, ODEV) =
     .                  INUMBER(3,IAND(1,PAGEHEAD(PAG_CHRSIZ,OPAR))*2)
             SBI = SBI + 3
            ENDIF
C
C --- Reposition the cursor if it is up
C
            IF (TPXICURF(ODEV)) THEN
             PAGE1POS = PAGENVPO(1,OPAR) +
     .           PAGENVCS(1,OPAR) * (ECHOAROW(OPAR)-1)
             PAGE2POS = PAGENVPO(2,OPAR) +
     .           PAGENVCS(2,OPAR) * (ECHOACOL(OPAR)-2+TPXICURS(ODEV))
             SIOWBUFF(SBI+1, ODEV) = ESC        ! Set the cursor position
             SIOWBUFF(SBI+2, ODEV) = GRAPHICS_TEXT
             SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGE1POS)
             SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGE1POS)
             SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGE1POS)
             SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGE2POS)
             SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGE2POS)
             SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGE2POS)
             SBI = SBI + 8
            ENDIF
           ENDIF
5499       CONTINUE
          ENDIF
C
C -----------------------------------------------------------------
C ---   Volatile Udate   ---    Display   ---   Button toggle   ---
C -----------------------------------------------------------------
C
          IF (STACOLTO) THEN
5500       CONTINUE
           SIOWBUFF(SBI+1, ODEV) = ESC           ! XOR a rectangle
           SIOWBUFF(SBI+2, ODEV) = RAREV_VIDEO_RECT_AREA
           SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGESTZN(1,ODCB,OPAR))
           SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGESTZN(1,ODCB,OPAR))
           SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGESTZN(1,ODCB,OPAR))
           SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGESTZN(2,ODCB,OPAR))
           SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGESTZN(2,ODCB,OPAR))
           SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGESTZN(2,ODCB,OPAR))
           SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGESTZN(3,ODCB,OPAR))
           SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGESTZN(3,ODCB,OPAR))
           SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGESTZN(3,ODCB,OPAR))
           SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGESTZN(4,ODCB,OPAR))
           SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGESTZN(4,ODCB,OPAR))
           SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGESTZN(4,ODCB,OPAR))
           SBI = SBI + 14
          ENDIF
C
C -------------------------------------------------------------------
C ---   Volatile Udate   ---   Display   ---   Color zone Erasing ---
C -------------------------------------------------------------------
C
          IF (COLZONER) THEN
5600       CONTINUE
C
C --- Normal no input DCB Color zone erasing
C
           IF (PAGEIPDR(2,ILIN,OPAR) .NE. ODCB) THEN
            SIOWBUFF(SBI+1, ODEV) = ESC
            SIOWBUFF(SBI+2, ODEV) = CLEAR_RECT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGECOBL(4,ODCB,OPAR))
            SBI = SBI + 14
C
C --- Normal input DCB Color zone erasing
C
           ELSE IF (IAND(PAGETOBL(5,ILIN,OPAR),
     .                   TTY_DRKC+TTY_DRKB) .EQ. 0) THEN
            SIOWBUFF(SBI+1, ODEV) = ESC
            SIOWBUFF(SBI+2, ODEV) = CLEAR_RECT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGECOBL(4,ODCB,OPAR))
            SBI = SBI + 14
C
C --- Input DCB Center zone erasing
C
           ELSE IF (IAND(PAGETOBL(5,ILIN,OPAR),TTY_DRKB) .NE. 0) THEN
            SIOWBUFF(SBI+1, ODEV) = ESC
            SIOWBUFF(SBI+2, ODEV) = CLEAR_RECT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGECOBL(1,ODCB,OPAR)+4)
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGECOBL(1,ODCB,OPAR)+4)
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGECOBL(1,ODCB,OPAR)+4)
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGECOBL(2,ODCB,OPAR)+4)
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGECOBL(2,ODCB,OPAR)+4)
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGECOBL(2,ODCB,OPAR)+4)
            SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGECOBL(3,ODCB,OPAR)-4)
            SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGECOBL(3,ODCB,OPAR)-4)
            SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGECOBL(3,ODCB,OPAR)-4)
            SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGECOBL(4,ODCB,OPAR)-4)
            SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGECOBL(4,ODCB,OPAR)-4)
            SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGECOBL(4,ODCB,OPAR)-4)
            SBI = SBI + 14
C
C --- Input DCB Border erasing
C
           ELSE
            SIOWBUFF(SBI+1, ODEV) = ESC
            SIOWBUFF(SBI+2, ODEV) = CLEAR_RECT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGECOBL(1,ODCB,OPAR)+3)
            SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGECOBL(1,ODCB,OPAR)+3)
            SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGECOBL(1,ODCB,OPAR)+3)
            SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGECOBL(4,ODCB,OPAR))
            SBI = SBI + 14
            SIOWBUFF(SBI+1, ODEV) = ESC
            SIOWBUFF(SBI+2, ODEV) = CLEAR_RECT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGECOBL(2,ODCB,OPAR)+3)
            SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGECOBL(2,ODCB,OPAR)+3)
            SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGECOBL(2,ODCB,OPAR)+3)
            SBI = SBI + 14
            SIOWBUFF(SBI+1, ODEV) = ESC
            SIOWBUFF(SBI+2, ODEV) = CLEAR_RECT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGECOBL(3,ODCB,OPAR)-3)
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGECOBL(3,ODCB,OPAR)-3)
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGECOBL(3,ODCB,OPAR)-3)
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGECOBL(2,ODCB,OPAR))
            SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGECOBL(4,ODCB,OPAR))
            SBI = SBI + 14
            SIOWBUFF(SBI+1, ODEV) = ESC
            SIOWBUFF(SBI+2, ODEV) = CLEAR_RECT
            SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGECOBL(1,ODCB,OPAR))
            SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGECOBL(4,ODCB,OPAR)-3)
            SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGECOBL(4,ODCB,OPAR)-3)
            SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGECOBL(4,ODCB,OPAR)-3)
            SIOWBUFF(SBI+9, ODEV) = INUMBER(1,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+10,ODEV) = INUMBER(2,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+11,ODEV) = INUMBER(3,PAGECOBL(3,ODCB,OPAR))
            SIOWBUFF(SBI+12,ODEV) = INUMBER(1,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+13,ODEV) = INUMBER(2,PAGECOBL(4,ODCB,OPAR))
            SIOWBUFF(SBI+14,ODEV) = INUMBER(3,PAGECOBL(4,ODCB,OPAR))
            SBI = SBI + 14
           ENDIF
          ENDIF
         ENDDO
5990     CONTINUE
C
C --------------------------------------------------
C ---   Volatile Udate   ---    Cursor Display   ---
C --------------------------------------------------
C
6000     CONTINUE
         IF (.NOT. PAGFSTDP(ODEV)) THEN
          IF (PAGFSTUP(ODEV)) THEN
           PREVCURF(ODEV) = .FALSE.
          ELSE
           IF (TPXICURF(ODEV) .NEQV. PREVCURF(ODEV)) THEN
6100        CONTINUE
C
C --- Cursor has been turn on
C
            IF (TPXICURF(ODEV)) THEN
             PAGE1POS = PAGENVPO(1,OPAR) +
     .           PAGENVCS(1,OPAR) * (ECHOAROW(OPAR)-1)
             PAGE2POS = PAGENVPO(2,OPAR) +
     .           PAGENVCS(2,OPAR) * (ECHOACOL(OPAR)-2+TPXICURS(ODEV))
             SIOWBUFF(SBI+1, ODEV) = ESC         ! Turn on text cursor
             SIOWBUFF(SBI+2, ODEV) = ON_CURSOR
             SBI = SBI + 2
             SIOWBUFF(SBI+1, ODEV) = ESC         ! Set the cursor position
             SIOWBUFF(SBI+2, ODEV) = GRAPHICS_TEXT
             SIOWBUFF(SBI+3, ODEV) = INUMBER(1,PAGE1POS)
             SIOWBUFF(SBI+4, ODEV) = INUMBER(2,PAGE1POS)
             SIOWBUFF(SBI+5, ODEV) = INUMBER(3,PAGE1POS)
             SIOWBUFF(SBI+6, ODEV) = INUMBER(1,PAGE2POS)
             SIOWBUFF(SBI+7, ODEV) = INUMBER(2,PAGE2POS)
             SIOWBUFF(SBI+8, ODEV) = INUMBER(3,PAGE2POS)
             SBI = SBI + 8
C
C --- Cursor has been turn of
C
            ELSE
             SIOWBUFF(SBI+1, ODEV) = ESC           ! Turn off text cursor
             SIOWBUFF(SBI+2, ODEV) = OFF_CURSOR
             SBI = SBI + 2
            ENDIF
            PREVCURF(ODEV) = TPXICURF(ODEV)
           ENDIF
          ENDIF
         ENDIF
C
C -----------------------------------------------------------------
C ---   Volatile Udate   ---    Display   ---   End of loop     ---
C -----------------------------------------------------------------
C
         SIOWBUIN(ODEV) = SBI                    ! Serial I/O Buffer index
         TPXOREQP = -1                           ! Request Page display
CVAX
CVAX    ENDIF
CVAXEND
CIBM
        ENDIF
CIBMEND
        ENDIF
        ENDIF
       ENDDO
      ENDIF
C
C ---------------------------------------------
C --- Send the buffer to the serial devices ---
C ---------------------------------------------
C
C --- Process all devices
C
C --- Send buffer to all devices
C
      DO I = 1, INDEV
C
C --- Send Serial I/O
C
       IF (SIOWBUIN(I) .GT. 0) THEN
        IF (SIOWBUIN(I) .LE. SIOW_BFL) THEN
         IF (SIOASSIF(I)) THEN
CVAX
CVAX      SIOWBUCT = SIOWBUIN(I)
CVAX      SIOWRSTB(1, I) = 0
CVAX      SIOWRSTB(3, I) = 0
CVAX      SIOWRFLG(I) = .FALSE.
CVAX      SIOWRIEV(I) = SYS$QIO(,%VAL(SIOCHAN(i))
CVAX .,                         %VAL(IO$_WRITEVBLK)
CVAX .,                         SIOWRSTB(1,I)
CVAX .,                         %VAL(SIOASTWA(I))
CVAX .,,                        SIOWBUFF(1,I)
CVAX .,                         %VAL(SIOWBUCT),,,,)
CVAXEND
CSGI
CSGI      SIOWBUCT = SIOWBUIN(I)
CSGI      SIOWRFLG(I) = welpnl(%VAL(I), SIOWBUFF(1,I), %VAL(SIOWBUCT))
CSGIEND
CIBM
          IF (SIOWBLIS(I) .EQ. SUCCESS) THEN
           IF (SIOWBUIN(I) .GT. SIOW_BUC) THEN   ! Break buffer in chucks
            SIOWBYTC(I) = SIOW_BUC
            SIORECNB    = -1
            SIOWBYTN(I) = -1
            SIOWBUFS(I) = 0
            SIOWBLIS(I) = 0
            SIOWBUIS(I) = AIO_NCMP
            CALL CAE_IO_WRITE_DELAY (SIOWBUFS(I)
     .,                       SIOWBLIS(I)
     .,                       %VAL(SIOCHAN(I))
     .,                       %VAL(1)
     .,                       SIOWBUFF(SIOWBUFP(I),I)
     .,                       SIORECNB
     .,                       SIOWBYTC(I)
     .,                       SIOWBYTN(I))
            SIOWBUFP(I) = SIOWBUFP(I) + SIOW_BUC
            SIOWBUIN(I) = SIOWBUIN(I) - SIOW_BUC
            IF (SIOWBUFS(I) .NE. SUCCESS) THEN
             TPERROR = ERR_S_CM
             RETURN
            ENDIF
           ELSE
            SIOWBYTC(I) = SIOWBUIN(I)
            SIOWBUIN(I) = 0
            SIORECNB    = -1
            SIOWBYTN(I) = -1
            SIOWBUFS(I) = 0
            SIOWBUIS(I) = AIO_NCMP
            CALL CAE_IO_WRITE_DELAY (SIOWBUFS(I)
     .,                       SIOWBUIS(I)
     .,                       %VAL(SIOCHAN(I))
     .,                       %VAL(1)
     .,                       SIOWBUFF(SIOWBUFP(I),I)
     .,                       SIORECNB
     .,                       SIOWBYTC(I)
     .,                       SIOWBYTN(I))
            SIOWBUFP(I) = 1
            SIOWBUIN(I) = 0
            IF (SIOWBUFS(I) .NE. SUCCESS) THEN
             TPERROR = ERR_S_CM
             RETURN
            ENDIF
           ENDIF
          ENDIF
CIBMEND
         ENDIF
        ELSE
         TPERROR = ERR_S_CM
        ENDIF
       ENDIF
      ENDDO
      RETURN
      END
C
CVAX
CVAX------------------------------------------------------------------    \C ---
CVAX  SUBROUTINE TP_AST
CVAX------------------------------------------------------------------    \C ---
CVAX                                                                      \C
CVAX  IMPLICIT NONE
CVAX                                                                      \C
CVAX  INCLUDE 'pageparm.inc'                     !NOFPC
CVAX  INCLUDE 'pagecode.inc'                     !NOFPC
CVAX  INCLUDE 'stdxd.inc'                        !NOFPC
CVAX  INCLUDE 'std8tp.inc'                        !NOFPC
CVAX  INCLUDE 'stdtpio.inc'                      !NOFPC
CVAX                                                                      \C
CVAX-----------------------------------------                             \C ---
CVAX- Buff I/O Page.dat read AST Routines ---                             \C ---
CVAX-----------------------------------------                             \C ---
CVAX                                                                      \C
CVAX  ENTRY PGDTAST
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAST1
CVAX  PGDTFLAG(1) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAST2
CVAX  PGDTFLAG(2) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAST3
CVAX  PGDTFLAG(3) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAST4
CVAX  PGDTFLAG(4) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAST5
CVAX  PGDTFLAG(5) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAST6
CVAX  PGDTFLAG(6) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAST7
CVAX  PGDTFLAG(7) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAST8
CVAX  PGDTFLAG(8) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAST9
CVAX  PGDTFLAG(9) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY PGDTAS10
CVAX  PGDTFLAG(10) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX----------------------------------                                    \C ---
CVAX- Serial I/O read AST Routines ---                                    \C ---
CVAX----------------------------------                                    \C ---
CVAX                                                                      \C
CVAX  ENTRY SIOASTR1
CVAX  SIORDFLG(1) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY SIOASTR2
CVAX  SIORDFLG(2) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY SIOASTR3
CVAX  SIORDFLG(3) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX-----------------------------------                                   \C ---
CVAX- Serial I/O Write AST Routines ---                                   \C ---
CVAX-----------------------------------                                   \C ---
CVAX                                                                      \C
CVAX  ENTRY SIOASTW1
CVAX  SIOWRFLG(1) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY SIOASTW2
CVAX  SIOWRFLG(2) = .TRUE.
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY SIOASTW3
CVAX  SIOWRFLG(3) = .TRUE.
CVAX  RETURN
CVAX  END
CVAXEND
C
