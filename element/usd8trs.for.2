C --- =======================================================================
C
C --- Name                    RAP VARIABLE Slew subroutine
C --- Module_ID               TRSLW
C --- Documentation_no        -----
C --- Customer                USAIR DASH 8
C --- Author                  <PERSON>, ing./Eng.
C --- Date                    27-MAY-1991
C --- Application             To slew variables from one value to another.
C
C --- =======================================================================
C
C'Revision_History
C
C   #108 31-Oct-90 NT
C         ADDED TRSFRZ LOGIC
C
C   #107 31-Oct-90 Y.H. AHN
C         REPLACE REAL VALUE DIVISION BY MULTIPLICATION
C
C   #014 25-Apr-90 FALJ
C         ENTRY POINT ADDED IN
C
C   #007 09-Apr-90 FALJ
C         COUNT --> NUMVAR
C
C   #005 05-Apr-90 FALJ
C         BIAS LOGIC ADDED
C
C   #004 05-Apr-90 FALJ
C         BUFFER(1) --> BUFFER(500)
C
C   #002 05-Apr-90 FALJ
C         ORIGINAL SLEW MODULE
C
C'
C --- =======================================================================
C
C
      SUBROUTINE USD8TRS
C                *******
C
C
      IMPLICIT NONE
C
C
C --- =======================================================================
C --- EXTERNAL LABELS
C --- =======================================================================
C
C
      INCLUDE 'usd8tr.inc'                   ! RAP include file
C
C
C --- --------------------------------------------
C
      INCLUDE 'usd8trs.inc'                  ! RAP SPARE COMMON decl
C
C
C --- =======================================================================
C --- LOCAL DECLARATIONS
C --- =======================================================================
C
CVAX
CVAX  BYTE
CVAX  ****                                                                \C
CVAXEND
C
CIBM
      INTEGER*1
C     ****
CIBMEND
C
     -     BUFFER   (1)           ! RECORDED VALUES FROM DISC
     -,    SLWDA    (8)           ! CURRENT VALUE, BYTE BREAKUP
     -,    SLWDR    (8)           ! RECORDED VALUE, BYTE BREAKUP
CJF  -,    XR       (1)           ! START OF CROSS REFERENCE
C
C ---
C
      INTEGER*2
C     *********
C
     -          ITN2              ! ITIRATION COUNTER FOR I*2'S
     -,         SLI2A             ! CURRENT VALUE
     -,         SLI2R             ! RECORDED VALUE
C
C ---
C
      INTEGER*4
C     *********
C
     -          ITN4              ! ITIRATION COUNTER FOR I*4'S
     -,         SLI4A             ! CURRENT VALUE
     -,         SLI4R             ! RECORDED VALUE
     -,         CDB_PTR           ! POINTS TO CURRENT VAR IN CDB
     -,         BAND_PTR          ! POINTS TO RECORDED VAR IN BUFFER
     -,         NUMVAR            ! NUMBER OF VARIABLES TO BE SLEWED
     -,         CODE              ! SLEW CODE
     -,         I                 ! DO LOOP INDEX
     -,         J                 ! DO LOOP INDEX
     -,         WSIZE             ! SIZE OF VARIABLE IN BYTES
     -,         WORDSIZE(12)      ! ARRAY OF BYTE SIZES FOR DECODING
C
C ---
C
      REAL*4
C     ******
C
     -       ITNR                 ! ITIRATION COUNTER FOR R*4
     -,      SLR4A                ! CURRENT VALUE
     -,      SLR4R                ! RECORDED VALUE
C
C ---
C
      REAL*8
C     ******
C
     -       SLR8A                ! CURRENT VALUE (ALL 8 BYTES)
     -,      SLR8R                ! CURRENT VALUE   "  "   "
C
      LOGICAL*1  TRSFRZ/.FALSE./           ! FREZE SLEW
C
C
C
C --- =======================================================================
C --- EQUIVALENCE DECLARATIONS
C --- =======================================================================
C
      EQUIVALENCE
C     ***********
C
     -  ( SLWDA(1)    , SLI2A       )
     -, ( SLWDA(1)    , SLI4A       )
     -, ( SLWDA(1)    , SLR4A       )
     -, ( SLWDA(1)    , SLR8A       )
C
     -, ( SLWDR(1)    , SLI2R       )
     -, ( SLWDR(1)    , SLI4R       )
     -, ( SLWDR(1)    , SLR4R       )
     -, ( SLWDR(1)    , SLR8R       )
C
     -, ( BUFFER      , TRBUFR(1,1) )
C
C
C
C --- =======================================================================
C --- DATA STATEMENTS
C --- =======================================================================
C
      DATA
C
     -  WORDSIZE    / 1 , 2 , 2 , 4 , 4 , 4
     -              , 4 , 8 , 4 , 4 , 4 , 8 /
C
C
C
C --- =======================================================================
C --- START OF SLEW
C --- =======================================================================
C
C
      ENTRY TRSLW
C           *****
      IF (TRSFRZ) RETURN
C
C
C --- =======================================================================
C
      IF     ( SLEW_BND .EQ. 100 ) THEN
C
         NUMVAR   = B100N
         ITN2     = TR100IT2
         ITN4     = TR100IT4
         ITNR     = 1/TR100ITR
         BAND_PTR = B100_PTR
C
      ELSEIF ( SLEW_BND .EQ. 200 ) THEN
C
         NUMVAR   = B200N
         ITN2     = TR200IT2
         ITN4     = TR200IT4
         ITNR     = 1/TR200ITR
         BAND_PTR = B200_PTR
C
      ELSEIF ( SLEW_BND .EQ. 400 ) THEN
C
         NUMVAR   = B400N
         ITN2     = TR400IT2
         ITN4     = TR400IT4
         ITNR     = 1/TR400ITR
         BAND_PTR = B400_PTR
C
      ELSEIF ( SLEW_BND .EQ. 800 ) THEN
C
         NUMVAR   = B800N
         ITN2     = TR800IT2
         ITN4     = TR800IT4
         ITNR     = 1/TR800ITR
         BAND_PTR = B800_PTR
C
      ENDIF
C
C
C
C --- =======================================================================
C --- TRANSFER LOOP
C --- =============
C
      DO 9000 I = 1 , NUMVAR
C
C ---
C
         IF     ( SLEW_BND .EQ. 100 ) THEN
C
            CODE    = B100S(I)
            CDB_PTR = B100B(I)
C
         ELSEIF ( SLEW_BND .EQ. 200 ) THEN
C
            CODE    = B200S(I)
            CDB_PTR = B200B(I)
C
         ELSEIF ( SLEW_BND .EQ. 400 ) THEN
C
            CODE    = B400S(I)
            CDB_PTR = B400B(I)
C
         ELSEIF ( SLEW_BND .EQ. 800 ) THEN
C
            CODE    = B800S(I)
            CDB_PTR = B800B(I)
C
         ENDIF
C
         WSIZE = WORDSIZE( CODE )
C
C
C
C ---    -----------------------------------------
C ---    EXTRACT THE VARIALBLES
C ---    ----------------------
C
         DO J = 1 , WSIZE
C
            SLWDR(J) = BUFFER(BUF_BIAS + BAND_PTR + J - 1)
            SLWDA(J) = CDB1(CDB_PTR - CDBSTART + J )
C
         ENDDO
C
C
C
C ---    -----------------------------------------
C ---    SLEW THE VARIABLE
C ---    -----------------
C
         GOTO
C        ****
C
     -(  900           !  1-  L*1 NOT SLEWED
     -,  100           !  2-  I*2 NORMAL SLEW
     -,  900           !  3-  I*2 NOT SLEWED
     -,  200           !  4-  R*4 NORMAL SLEW
     -,  300           !  5-  I*4 NORMAL SLEW
     -,  900           !  6-  I*4 NOT SLEWED
     -,  900           !  7-  R*4 NOT SLEWED
     -,  900           !  8-  R*8 NOT SLEWED
     -,  400           !  9-  R*4 ANGLE SLEW (+/- 1)
     -,  500           ! 10-  R*4 ANGLE SLEW (+/- PI)
     -,  600           ! 11-  R*4 ANGLE SLEW (+/- 180)
     -,  700 )         ! 12-  R*8 ANGLE SLEW (+/- 180)
C
     -         CODE
C
C
C
C --- -----------------------------------------------------------------------
C --- I*2 NORMAL SLEW
C --- ----------------------------------
C --- CODE(I) = 2
C --- -----------------------------------------------------------------------
C
100     CONTINUE
C
C ---
C
        SLI2A = SLI2A + ( SLI2R - SLI2A ) / ITN2
C
C ---
C
        GOTO 1000
C
C
C
C --- -----------------------------------------------------------------------
C --- R*4 NORMAL SLEW
C --- ----------------------------------
C --- CODE(I) = 4
C --- -----------------------------------------------------------------------
C
200     CONTINUE
C
C ---
C
        SLR4A = SLR4A + ( SLR4R - SLR4A ) * ITNR
C
C ---
C
        GOTO 1000
C
C
C
C --- -----------------------------------------------------------------------
C --- I*4 NORMAL SLEW
C --- ----------------------------------
C --- CODE(I) = 5
C --- -----------------------------------------------------------------------
C
300     CONTINUE
C
C ---
C
        SLI4A = SLI4A + ( SLI4R - SLI4A ) / ITN4
C
C ---
C
        GOTO 1000
C
C
C
C --- -----------------------------------------------------------------------
C --- R*4 ANGLE SLEW ( +/- 1 )
C --- ----------------------------------
C --- CODE(I) = 9
C --- -----------------------------------------------------------------------
C
400     CONTINUE
C
C ---
C
        IF ( ITNR .GT. 0 ) THEN
C
           SLR4A = SLR4A + ( SLR4R - SLR4A ) * ITNR  ! TEMP
C
CJF          CALL TRANGSLEW
C              *********
C
CJF     -       ( SLR4A
CJF     -,        SLR4R
CJF     -,       -1.0
CJF     -,        1.0
CJF     -,        ITNR    )
C
        ENDIF
C
C ---
C
        GOTO 1000
C
C
C
C --- -----------------------------------------------------------------------
C --- R*4 ANGLE SLEW ( +/- PI )
C --- ----------------------------------
C --- CODE(I) = 10
C --- -----------------------------------------------------------------------
C
500     CONTINUE
C
C ---
C
        IF ( ITNR .GT. 0 ) THEN
C
           SLR4A = SLR4A + ( SLR4R - SLR4A ) * ITNR  ! TEMP
C
CJF          CALL TRANGSLEW
C              *********
C
CJF     -       ( SLR4A
CJF     -,        SLR4R
CJF     -,       -PI
CJF     -,        PI
CJF     -,        ITNR  )
C
        ENDIF
C
C ---
C
        GOTO 1000
C
C
C
C --- -----------------------------------------------------------------------
C --- R*4 ANGLE SLEW ( +/- 180 )
C --- ----------------------------------
C --- CODE(I) = 11
C --- -----------------------------------------------------------------------
C
600     CONTINUE
C
C ---
C
        IF ( ITNR .GT. 0 ) THEN
C
           SLR4A = SLR4A + ( SLR4R - SLR4A ) * ITNR  ! TEMP
C
CJF          CALL TRANGSLEW
C              *********
C
CJF     -       ( SLR4A
CJF     -,        SLR4R
CJF     -,       -180.00
CJF     -,        180.00
CJF     -,        ITNR    )
C
        ENDIF
C
C ---
C
        GOTO 1000
C
C
C
C --- -----------------------------------------------------------------------
C --- R*8 ANGLE SLEW ( +/- 180 )
C --- ----------------------------------
C --- CODE(I) = 12
C --- -----------------------------------------------------------------------
C
700     CONTINUE
C
C ---
C
        IF ( ITNR .GT. 0 ) THEN
C
           SLR8A = SLR8A + ( SLR8R - SLR8A ) * ITNR  ! TEMP
C
CJF          CALL TRDANGSLEW
C              **********
C
CJF     -       ( SLR8A
CJF     -,        SLR8R
CJF     -,       -180.00
CJF     -,        180.00
CJF     -,        ITNR    )
C
        ENDIF
C
C ---
C
        GOTO 1000
C
C
C
C --- -----------------------------------------------------------------------
C --- L*1, I*2, I*4, R*4 & R*8 NOT SLEWED
C --- -----------------------------------
C --- CODE(I) = 1, 3, 6, 7, 8
C --- -----------------------------------------------------------------------
C
900     CONTINUE
C
C ---
C
        SLR8A = SLR8R
C
C ---
C
        GOTO 1000
C
C
C
C ---    --------------------------------------------------------------------
C ---    REPLACE SLEWED VALUE INTO CDB
C ---    --------------------------------------------------------------------
C
1000    CONTINUE
C
C ---
C
         DO  J = 1 , WSIZE
C
            CDB1( CDB_PTR - CDBSTART + J ) = SLWDA(J)
C
         ENDDO
C
C ---
C
         BAND_PTR = BAND_PTR + WSIZE
C
C ---
C
9000  CONTINUE
C
C
C
C --- =======================================================================
C --- UPDATE BAND POINTER
C --- ===================
C
      IF     ( SLEW_BND .EQ. 100 ) THEN
C
         B100_PTR = BAND_PTR
C
      ELSEIF ( SLEW_BND .EQ. 200 ) THEN
C
         B200_PTR = BAND_PTR
C
      ELSEIF ( SLEW_BND .EQ. 400 ) THEN
C
         B400_PTR = BAND_PTR
C
      ELSEIF ( SLEW_BND .EQ. 800 ) THEN
C
         B800_PTR = BAND_PTR
C
      ENDIF
C
C
C
C --- =======================================================================
C --- RETURN
C --- =======================================================================
C
      RETURN
C
C ---
C
      END
