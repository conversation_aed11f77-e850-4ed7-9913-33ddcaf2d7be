/******************************************************************************
C
C'Title                FPMC-C30 Task Routines
C'Module_ID            usd8cstask.c
C'Entry_points         cf_init(), cf_60(), cf_500(), cf_3000()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Secondarie control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz, 500 Hz, 3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8csxrf.ext", "usd8csdata.ext"
C
C'Subroutines called
C
C fgen_init(), cal_init(), cal_mod(), feel_init(), feel_mod(), feel_check(),
C adio_init(), adio_build_input(), adio_build_output(), adio_io(), cal_check(),
C create_mbx(), strpack(), write_mbx(), mail_check(), task_check(),
C cf_safemode(), cf_bdrive(), cf_calinp(), cf_servo(),
C cbslow(), cbmid(), cbfast()
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V2.1
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include "zspring.h"
#include <servocal.h>
#include "usd8csxrf.ext"
#include "usd8csdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CSTASK010 INITIALIZATION TASK ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine initializes the fgen, feelspring, calibration, adio, and
CC mailbox parameters for the different routines to be used afterwards.
CC
CC Called by: task_init() in file usd8cftask.c
CC
CC Iteration rate: called only once at first pass
CC
CC Subroutines called:
CC fgen_init(),               : in file fgen.c (standard library)
CC cal_init(), cal_mod(),     : in file servocal.c (standard library)
CC feel_init(), feel_mod(),   : in file fspring.c (standard library)
CC adio_init(),               : in file adio.c (standard library)
CC adio_build_input(),        : in file adio.c (standard library)
CC adio_build_output(),       : in file adio.c (standard library)
CC create_mbx(), strpack(),   : in file mailbox.c (standard library)
CC write_mbx()                : in file mailbox.c (standard library)
*/
 
cf_init()
{
static int c_iostatus;   /* ADIO utility return status            */
static int c_calstat;    /* calibration utility return status     */
static int c_feelstat;   /* feelspring utility return status      */
static int c_mbxstat;    /* mailbox utility return status         */
 
/*
C  -------------------------------
CD CSTASK020 - fgen initialization
C  -------------------------------
*/
 
/*   fgen_init();           Initialize fgen slopes and intercepts */
 
/*
C  --------------------------------------
CD CSTASK030 - calibration initialization
C  --------------------------------------
*/
 
#if SITE
 
      CBL_CAL_FUNC = cal_init(&CBLCALCNT,CBLCALAPOS,CBLCALPPOS,
                              CBLCALGEAR,CBLCALFORC,CBLCALFRIC);
      c_calstat     = cal_mod(CBL_CAL_FUNC);
 
      CBR_CAL_FUNC = cal_init(&CBRCALCNT,CBRCALAPOS,CBRCALPPOS,
                              CBRCALGEAR,CBRCALFORC,CBRCALFRIC);
      c_calstat     = cal_mod(CBR_CAL_FUNC);
 
#endif
 
/*
C  -------------------------------------
CD CSTASK040 - feelspring initialization
C  -------------------------------------
*/
 
      CBLFEEL_FUNC = feel_init(&CBLFEELERR,&CBLFEELAFT,&CBLFEELBCN,
                               &CBLFEELCCN, CBLFEELCRV, CBLFEELPOS,
                               CBLFEELFOR,  CBLFEELFRI, &CBLKN,
                               &CBLNNL, &CBLNPL, &CBLKC,
                               &CBLFEELXMN, &CBLFEELXMX);
      c_feelstat    = feel_mod(CBLFEEL_FUNC,CBLVARI);
 
      CBRFEEL_FUNC = feel_init(&CBRFEELERR,&CBRFEELAFT,&CBRFEELBCN,
                               &CBRFEELCCN, CBRFEELCRV, CBRFEELPOS,
                               CBRFEELFOR,  CBRFEELFRI, &CBRKN,
                               &CBRNNL, &CBRNPL, &CBRKC,
                               &CBRFEELXMN, &CBRFEELXMX);
      c_feelstat    = feel_mod(CBRFEEL_FUNC,CBRVARI);
 
#if SITE
 
/*
C  -------------------------------
CD CSTASK050 - adio initialization
C  -------------------------------
*/
/* Removed until new ADIO stuff  */ 
/* 
  c_iostatus = adio_init(ADIO_SLOT);
  if (c_iostatus != 1) ADIO_ERROR = TRUE;
 
  c_iostatus = adio_build_input(ADIO_SLOT,&ADIO_IP);
  if (c_iostatus != 1) ADIO_ERROR = TRUE;
 
  c_iostatus = adio_build_output(ADIO_SLOT,&ADIO_OP);
  if (c_iostatus != 1) ADIO_ERROR = TRUE;
*/
    init_adio(); 
 
/*
C  ----------------------------------
CD CSTASK060 - mailbox initialization
C  ----------------------------------
*/
 
  c_mbxstat = create_mbx(LREQ_MBX,0,&LOGIC_REQUEST,sizeof LOGIC_REQUEST);
  c_mbxstat = create_mbx(CSTAT_MBX,0,&CHANNEL_STATUS,sizeof CHANNEL_STATUS);
  c_mbxstat = create_mbx(CDEF_MBX,0,&CHANDEF,sizeof CHANDEF);
  c_mbxstat = create_mbx(ERROR_MBX,0,&CHANERR,sizeof CHANERR);
 
  CHANDEF.number = NUM_CHANNEL;
  CHANDEF.type   = 1;
  strpack(&CHANDEF.name[CBL_CHAN][0],"LToebrakes");
  strpack(&CHANDEF.name[CBR_CHAN][0],"RToebrakes");
 
  c_mbxstat = write_mbx(CDEF_MBX);
 
#endif
 
}  /* end of cf_init task */

 
 
/*
C -----------------------------------------------------------------------------
CD CSTASK070 60 Hz TASK ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This task calls the appropriate simulation model slow band routines
CC as well as it handles the mailbox buffer transfers between C/L and
CC Logic and the timing parameters for the DFC-PLUS time page utility.
CC
CC Called by: FPMC executive dispatcher
CC
CC Iteration rate: 60 Hz
CC
CC Subroutines called:
CC feel_check(),              : in file fspring.c (standard library)
CC cal_check(),               : in file servocal.c (standard library)
CC mail_check(),              : in file mailbox.c (standard library)
CC task_check(),              : in file usd8cftask.c
CC cbslow()                   : in file usd8cbs.c
*/
 
cf_60()
{
 
  cbslow();                 /* Toebrakes slow band simulation model */
 
  feel_check(CBLFEELCHG,CBLFEEL_FUNC,CBLVARI);   /* fspring change check */
  feel_check(CBRFEELCHG,CBRFEEL_FUNC,CBRVARI);   /* fspring change check */
 
#if SITE
  cal_check(&CBLCALCHG,CBL_CAL_FUNC);         /* calibration change check */
  cal_check(&CBRCALCHG,CBR_CAL_FUNC);         /* calibration change check */
 
  task_check();                  /* Timing for DFC-PLUS time page utility */
  mail_check();                  /* mail handling between C/L and Logic   */
#endif
 
}

 
 
/*
C -----------------------------------------------------------------------------
CD CSTASK080 500 Hz TASK ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This task calls the appropriate simulation model medium band routines
CC as well as it handles the operation mode, backdrive, safeties and
CC throughput for each control.
CC
CC Called by: FPMC executive dispatcher
CC
CC Iteration rate: 500 Hz
CC
CC Subroutines called:
CC cf_safemode(), cf_bdrive(),  : in file usd8cssys.c
CC cbmid()                      : in file usd8cbs.c
*/
 
cf_500()
{
 
  cf_safemode();         /* Safety and Control mode checks       */
  cf_bdrive();           /* Backdrive mode check                 */
  cbmid();               /* Toebrakes medium band simulation model */
 
}

 
 
/*
C -----------------------------------------------------------------------------
CD CSTASK090 3000 Hz TASK ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This task calls the adio routine for I/O transfers between buffer units
CC and the FPMC card. It then calibrates the inputs to be used by the fast band
CC control model which outputs a current calculated by the servo model and
CC sends it to the appropriate load unit.
CC
CC Called by: FPMC executive dispatcher
CC
CC Iteration rate: 3000 Hz
CC
CC Subroutines called:
CC adio_io(),                 : in file usd8cssys.c
CC cf_calinp(), cf_servo(),   : in file usd8cssys.c
CC cbfast()                   : in file usd8cbf.c
*/
 
cf_3000()
{
 
#if SITE
/*   adio_io();              Reads inputs, writes outputs to ADIO card */
  adio_in();              /* (old) Reads inputs, writes outputs to ADIO card */
  cf_calinp();            /* Calibrates inputs for use by the model    */
#endif
 
  cbfast();               /* Toebrakes fast band simulation model    */
 
#if SITE
  cf_servo();             /* Calculates a current to output to servo   */
  adio_out();
  adio_sync();

  asm(" LDI 0,ST ");
#endif
 
}

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00053 CSTASK010 INITIALIZATION TASK ROUTINE                                 
C$ 00085 CSTASK020 - fgen initialization                                       
C$ 00093 CSTASK030 - calibration initialization                                
C$ 00111 CSTASK040 - feelspring initialization                                 
C$ 00133 CSTASK050 - adio initialization                                       
C$ 00148 CSTASK060 - mailbox initialization                                    
C$ 00172 CSTASK070 60 Hz TASK ROUTINE                                          
C$ 00215 CSTASK080 500 Hz TASK ROUTINE                                         
C$ 00246 CSTASK090 3000 Hz TASK ROUTINE                                        
*/
