  CCU is  the interactive  facility for  a user  to  diagnose  I/O cards
residing on a CAE DMC-based  interface system. CCU stands for C-bus
Check  Utility. 
  Diagnostics tests  can be performed on the following I/O cards: 
AIRO-16, AOP-32, DI-64, DIO-48, DIO-CB, DOR-32, and SOP/ACI. Digital
input cards will be referred to as DIP cards, digital output as DOP,
Analog input as AIP, Analog output as AOP, and Syncro cards as SOP.
Interface assignments are used to identify each I/O card in the 
interface (ie. DI002, AO0016, etc.). CCU can also be used to test
the power level to each chassis and examine special intelligent cards
for errors. 
  Special cards are intelligent cards that have the capability 
to detect errors.  These errors can be retrieved by the host. Cards with 
error detecting capability are listed under the LIS ASS SPE command. Note 
that the interface assignments are formed by the DMC and slot number 
(SPXXYY) where XX is the DMC number and YY is the slot number. All cards 
are tested automatically by the host during simulation as well as on 
request using CCU. The power test, on the other hand, is performed on 
request only using the REP POW command. 
  All errors detected during simulation can be directed to disk and/or 
console depending on the cdb label YERLOFMT (see LIST LOG_FORMAT command).
The errors logged to disk can be viewed using the HISTORY command. 
HISTORY provides the user with the capability to view error messages, 
restart the error file (a circular queue of 1000 error messages), 
change the value of the YERLOFMT label, plus a number of other functions
that affect the logging of error messages. Note that during simulation,
the disk file may be constantly updated thus it is necessary to snap-shot
the log file for viewing using the LOAD command. The LOAD command simply
copies the disk file locally for viewing. Every time LOAD is executed, 
a new snap-shot is taken.
  
1 EXIT
  The EXIT command is used to exit the utility.

  COMMAND FORMAT: EXIT (EXI), QUIT (QUI), X

1 HISTORY
  The HISTORY command is used to view DMC-based error messages that 
have been detected and saved on the disk. Unlike the other CCU 
commands, HISTORY has a number of subcommands including HELP.

  COMMAND FORMAT: HISTORY (HIS)


1 LIST
  This command is used to display information to the user.

  COMMAND FORMAT: LIST (LIS) qualifier(s) 

  Where: qualifier can be ASSIGNMENT (ASS), CHASSIS (CHA), TESTS (TES),
                          LOG_FORMAT (LOG)

2 ASSIGNMENT
  This qualifier is used to display a list of interface assignments 
that the user can use in the REPORT command. 

  COMMAND FORMAT: ASSIGNMENT (ASS) qualifier(s) 

  Where: qualifier can be DIP, DOP, AIP, AOP, SOP, ALL, SPE, TOTAL or 
                          /chas=XX/slot=XX

The /chas /slot will display all assignments that match the DMC and
slot number.

TOTAL (TOT) is used to display the number of interface assignments that
exist in the current system.

2 CHASSIS
  This qualifier is used to display the chassis that are on-line or 
off-line.

  COMMAND FORMAT: CHASSIS (CHA) qualifier

  Where: qualifier is a 2 digit hexadecimal value representing the
DMC to check. When this qualifier is blank, all DMCs from 01-10 hex 
are examined.

2 TESTS
  This qualifier is used to display the card types (DIP, DOP, etc.)
that the host is automatically testing during simulation. This
can be changed using the SET and RESET commands.

  COMMAND FORMAT: TESTS (TES) 

2 LOG_FORMAT
  This qualifier is used to display where the DMC error_logger is
directing error messages. This value can be changed using the HISTORY
subcommand SET or when restarting the error log file.

  COMMAND FORMAT: LOG_FORMAT (LOG) 

1 SET
  This command is used to enable the host to automatically test
a card type during simulation.
     
  COMMAND FORMAT: SET qualifier

  Where: qualifier can be DIP, DOP, AIP, AOP, SOP, SPE, ALL, or SYSTEM

Note that SET ALL activates all tests DIP, DOP etc. except SYSTEM. SYSTEM
is not a test but the SYSTEM identifier (EX. A).

2 SYSTEM
  This qualifier is used to set the system to test. The default system 
is A. When the Standard and ARINC interface systems are found on more than
one ethernet line, the user must set the desired system to test. Each
system has a unique set of interface assignments it can test (see LIS ASS)
command.

  COMMAND FORMAT: SYSTEM (SYS) qualifier

  Where: qualifier can be A or B

  Note that each system has some unique commands such as LIS [ASS,CHA],
STATUS, and REPORT since each system has its own interface assignments
and definitions. However, commands such as SET, RESET, HISTORY, 
LIS [LOG,TES], and HELP are common for all systems.

1 RESET
  This command is used to disable the host from automatically testing
a card type during simulation.
     
  COMMAND FORMAT: RESET (RES) qualifier

  Where: qualifier can be DIP, DOP, AIP, AOP, SOP, SPE or ALL


1 STATUS
  This command displays the faulty non-intelligent I/O cards detected 
by the host during simulation.

  COMMAND FORMAT: STATUS (STA) qualifier

  Where: qualifier can be DIP, DOP, AIP, AOP, SOP, ALL

1 REPORT
  The REPORT command can be  used to request a test to be made on a
specific card independently of  the current test being performed by
the host. The simulation is not effected in any way. The results of a 
test are displayed on the users terminal. The REPORT command can also
be used to check the power level to each chassis.
 
  COMMAND FORMAT: REPORT (REP) qualifier

  Where: qualifier can be POWER, or a valid interface assignment 
(see LIST ASSIGNMENT command).
 
2 EXAMPLES
            CCU> REPORT POWER
            CCU> REPORT DI000
            CCU> REPORT AO0000
            CCU> REP SP0102
           
2 POWER
  If the POWER option is selected, a POWER test will be performed.
The test performed will refer to the +24V source, the +15V source,
and the -15V source.
 
  COMMAND FORMAT: POWER (POW) qualifier

  Where: qualifier is a 2 digit hexadecimal value representing the
DMC to check. When this qualifier is blank, all DMCs from 01-10 hex 
will be examined.

2 Tolerance

  0.030 Volts DC  for  AOP-32, AIRO-16
  0.500 Volts RMS for  SOP/ACI (one leg)
  3.6   Degrees   for  SOP/ACI (both legs)
  0000  MASK      for  DIO-48, DI-64, DOR-32, DIO-CB

