#!  /bin/csh -f
#!  $Revision: SGI_ENT - Enter on the remote computer V1.4 (RBE) Mar-92$
#!
#! &
#! %
#! ^
#!  Version 1.0: <PERSON> (08/04/1991)
#!       - initial version
#!
#!  Version 1.1: <PERSON> (26-May-91)
#!       - changed 0CCUUU code for 0CTBBI
#!
#!  Version 1.2: <PERSON> (20-Aug-91)
#!       - added double quotes to most of the character strings
#!       - optimized code
#!       - compute simtab value
#!
#!  Version 1.3: <PERSON> (25-Sep-91)
#!       - translate the logical name CAE_CAELIB_PATH in the file that
#!         is transfered to the remote machine.
#!
#!  Version 1.4: <PERSON> (09-Dec-91)
#!       - New version for MOM 4.0 (parallel load)  
#!
if ( "$argv[1]" == "Y" ) then
    set echo
    set verbose
endif
if ( "$argv[2]" != "ENTER") exit
set argv[3] = "`revl '-$argv[3]'`"
set argv[4] = "`revl '-$argv[4]' +`"
#
#     Find file name that has to be entered
#
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
#
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
set FSE_FILE="`revl '-$FSE_FILE'`"
set stat=$status
if ! ($stat == 0 || $stat == 1 || $stat == 6) then
  if ($stat == 5) then
    echo "%FSE-E-FILENOTFOUND, file does not exist."
  else 
    echo "%FSE-E-FILERROR, error on file $FSE_FILE."
    reverr $stat
  endif
  exit
endif
#
#  Find REMOTE COMPUTER(s) identification name(s) & compute SIMTAB value
#
set FSE_REMO = " "
set FSE_REMO_COUNT = 0
set FSE_COUNT = 1
set T = (0 0 0 0 0 0 0 0)
foreach FSE_ELEM ( `cat "$argv[3]"` )
  if ( $FSE_COUNT != 1 ) then
    set FSE_NUMB = "`echo $FSE_ELEM | cut -f2 -dC | cut -f1 -d'.'`"
    set FSE_REMO = ($FSE_REMO `logicals -t CAE_MOM$FSE_NUMB`)
    @ FSE_REMO_COUNT++
    if ( $status != 0 ) then
        echo "*** ERROR : Logical name CAE_MOM$FSE_NUMB is not defined"
        exit
    endif
    @ FSE_POSI   = $FSE_NUMB + 1
    set T[$FSE_POSI] = 1
    @ FSE_COUNT++
  endif
  @ FSE_COUNT++
end
set SIMTAB = $T[8]$T[7]$T[6]$T[5]$T[4]$T[3]$T[2]$T[1]
#
#     Create the remote computer SIMex-PLUS input file
#
set SIMEX_DIR = "`logicals -t CAE_SIMEX_PLUS`"
set FSE_TMP1 = "$SIMEX_DIR/work/sgie_`pid`.tmp.1"
echo "set session REMOTE"            > $FSE_TMP1
echo "set configuration $argv[5]"   >> $FSE_TMP1
echo enter \"/tmp/$FSE_FILE:t\"     >> $FSE_TMP1
echo "exit"                         >> $FSE_TMP1
#
#     Create the remote computer SIMex-PLUS invocation file
#
set FSE_EXEC = "sgie_`pid`"
set FSE_TMP2 = "$SIMEX_DIR/work/$FSE_EXEC.tmp.1"
echo '#!  /bin/csh -f'                                  > $FSE_TMP2
echo 'set BIN = "`/cae/logicals -t cae_caelib_path`"'  >> $FSE_TMP2
echo  setenv PATH '`echo $BIN`:`echo $PATH`'           >> $FSE_TMP2
echo  simex execute \\\"/tmp/$FSE_TMP1:t\\\"           >> $FSE_TMP2
echo  rm /tmp/$FSE_FILE:t                              >> $FSE_TMP2
echo  rm /tmp/$FSE_TMP1:t                              >> $FSE_TMP2 
echo  rm /tmp/$FSE_TMP2:t                              >> $FSE_TMP2
chmod +x $FSE_TMP2
#
#     Initiate files transfering
#
set I = 1
while ( "$I" <= "$FSE_REMO_COUNT" )
    @ DEC_I = $I
    rcp -p "$FSE_FILE" "$FSE_REMO[$DEC_I]":/tmp/"$FSE_FILE:t"
    rcp -p "$FSE_TMP1" "$FSE_REMO[$DEC_I]":/tmp/"$FSE_TMP1:t"
    rcp -p "$FSE_TMP2" "$FSE_REMO[$DEC_I]":/tmp/"$FSE_TMP2:t"
    if ( $status != 0 ) then
       echo "*** ERROR: Unable to copy files to $FSE_REMO[$DEC_I]"
       exit
    endif
    @ I++
end
#
#     Tell remote computer to ENTER the specified file
#
set FSE_TMP3 = "$SIMEX_DIR/work/sgie_`pid`.tmp.1"
echo "EL /tmp/$FSE_TMP2:t"  > $FSE_TMP3
set FSE_COUNT = 1
foreach FSE_ELEM ( `cat "$argv[3]"` )
  if ( $FSE_COUNT != 1 ) then
    echo "   $FSE_ELEM" >> $FSE_TMP3
  endif
  @ FSE_COUNT++
end
#
#     Invoke FSE_OPERATE
#
set FSE_PREVIOUS = "`logicals -t CAE_LD_CDB`"
logicals -c "CAE_LD_CDB" "$SIMTAB"
fse_operate LOAD START_REPORT $FSE_TMP3
set stat = $status
logicals -c "CAE_LD_CDB" "$FSE_PREVIOUS"
logicals -d "cae_ld_$FSE_EXEC"
#
#     End this execution
#
set FSE_INFO="`fmtime $FSE_FILE | cut -c1-17`"
if ("$FSE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information is not available ($FSE_FILE)"
else
  if ( $stat == 0 ) then
     echo "0CTBBI $FSE_FILE,$FSE_FILE,SGI_ENT.COM,,,Last modified on $FSE_INFO" >$argv[4]
  endif
endif
rm $FSE_TMP1
rm $FSE_TMP2
rm $FSE_TMP3
exit
