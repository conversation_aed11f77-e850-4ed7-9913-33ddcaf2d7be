/********************************************************************/
/* rvlstr: $Revision: RABI.CMD, Version 2.0 (KU) 1992-NOV-10$       */
/********************************************************************/
-q                                      /* QUIET MODE               */
-c                                      /* ROM AUTO-INITIALIZATION  */

/* SPECIFY THE SYSTEM MEMORY MAP */

MEMORY
{
   VECS:   org = 0        len = 0x0040    /* INTERRUPT VECTORS      */
   BOOT:   org = 0x40     len = 0x7fc0    /* BOOT MEMORY            */
   SHARE:  org = 0x008000 len = 0x7fff    /* SHARED MEMORY          */
   RAM:    org = 0x809800 len = 0x07ff    /* RAM BLOCK 0,1          */
   MEM:    org = 0x810000 len = 0x9fff    /* LOCAL MEMORY           */
}

/* SPECIFY THE SECTIONS ALLOCATION INTO MEMORY */

SECTIONS
{
   .vectors:{} > VECS               /* INTERRUPT VECTORS            */
   .stack:  {} > RAM                /* SYSTEM STACK                 */
   .bss:    {} > RAM                /* GLOBAL & STATIC VARIABLES    */
   .share:  {} > SHARE	            /* SHARED MEMORY                */
   .text:   {} > BOOT	            /* CODE                         */
   .data:   {} > BOOT	            /* 32 BIT CONSTANTS             */
   .cinit:  {} > BOOT               /* INITIALIZATION TABLES        */
   .sysmem: {} > BOOT               /* SPACE FOR MEMORY MANAGER     */
}
