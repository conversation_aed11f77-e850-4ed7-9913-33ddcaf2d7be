C'Title              DASH8-300 AERODYNAMIC MODEL
C'Module_ID          TBD
C'Entry_point        AEROCOEF !LIFT
C'Documentation      TBD
C'Customer           TBD
C'Application        Computes longitudinal and lateral aerodynamic coefficients
C'Author             CAE Department 24, NRC
C'Engineer           TBD
C'Date               Nov 24, 1992
C'System             FLIGHT
C'Iteration rate     TBD
C'Process            Synchronous process
C
C'References
C
C                    [1] DASH8-100 Aerodynamic model, CAE - NRC
C
C                    [2] 3211 flight test maneuvres for DASH8-300
C
C                    [3] Flight test trim data for DASH8-300
C
C'Revision_History
C
C  usd8vz3.for.10  6Sep1993 21:57 usd8 BCa
C       < Correction to engine-out model for flaps 15 >
C
C  usd8vz3.for.9  6Sep1993 21:32 usd8 BCa
C       < Correction to engine-out model for flaps 15 >
C
C  usd8vz3.for.8 20Feb1993 11:56 usd8 paul va
C       < latest engine out and reverse thrust models as well as revised
C         ground effect functions >
C
C  USD8vz3.for.7 19Dec1992 22:25 USD8 BCA
C       < Added logic for ground effects term on elevator effectivenss due
C         to Ct difference >
C
C  USD8vz3.for.6 19Dec1992 16:34 USD8 BCA
C       < Added logic for tuning ground effects term VCMge >
C
C  USD8vz3.for.5 16Dec1992 20:01 USD8 BCA
C       < Set value for dynamic pressure at tail factor (LTAILQF) >
C
C  USD8vz3.for.4 16Dec1992 19:01 USD8 BCA
C       < Added terms for dynamic pressure at tail >
C
C  USD8vz3.for.3 16Dec1992 00:07 USD8 BCA
C       < Removed tuning factor fort CDct >
C
C  USD8vz3.for.2 14Dec1992 00:55 USD8 BCA
C       < Added temp. tuning factor for CDct at CT<-0.1 >
C
C  USD8vz3.for.1 13Dec1992 22:10 USD8 BCA
C       < Added shaping factors for CLCT, CMCT at high alpha >
c
c	Version 37 - 24 Nov 92 release to CAE
c			- incorporates CAE long model of 23 Nov 92
c                       - prelim. ground effect model (slight tuning
c				may be still done).
c			- uncoordinated flight code reasonable at this
c				time.
c					S.B. IAR
c
c	Version 36 - 23 Nov 92
c			- lateral functions
c		 	- stall tuning of CAE included
c			- interim g.e. model
c			- interim single engine and beta sweep model
c
c	Version 35 - Nov.  3, 1992.
c		     Lateral terms in function format
c
c       Version 34 - Oct. 30, 1992.
c                    Includes CAE's fading between flap detent positions, and
c                    revised trim offsets for rolling and yawing moment coeffs.
c
c       Version 33 - Oct. 28, 1992.
c                    Spoiler-aileron gearing included,
c                    Aileron offset of 3.3 degrees eliminated.
c
c       Version 32 - Sept.18, 1992.
c                    Lat + Long combined Model.
c
c       Version 31 - Sept. 4, 1992.
c                    post-PEC   Lateral  Model.
c
c	Version 30 - May   6, 1992.
c                    Dash 8 300 Lateral  Model.
c
C  Revised 10 Sept 92 - revised Longitudinal Coefficients based on CAE-NRC
C                       meeting, 3 Sept 92
C
C  Revision NEW   - Preliminary release of longitudinal model - DASH8-300
C
C
      SUBROUTINE USD8VAERO  !AEROVI
C     ====================
C
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.5 in VAX-to-IBM mode 12/08/92 - 01:49 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
CQ    USD8 XRFTEST(*)
C
C  INPUTS
C
C23456789112345678921234567893123456789412345678951234567896123456789712
CP    USD8
CPI  H     HQUICK,
CPI  V     VALPHA,         VBETA,          VCDGE,          VCDSP,
CPI  V     VCLSP,          VCMGE,          VCNWB,          VCRWB,
CPI  V     VCSPI,          VCSPO,          VCT1,           VCT2,
CPI  V     VCTSUM,         VCYBAS,         VDCNP,          VDCNR,
CPI  V     VDCRP,          VDCRR,          VDCYP,          VDCYR,
CPI  V     VDUC,           VDYNPR,         VFCDBAS,        VFCDCT,
CPI  V     VFCDGRGR,       VFCLACTA,       VFCLBAS,        VFCLCT,
CPI  V     VFCLDEDE,       VFCLDEGE,       VFCMACGA,       VFCMACTA,
CPI  V     VFCMB2,         VFCMBAS,        VFCMCT,         VFCMGE,
CPI  V     VFCMGRGR,       VFCMO,          VFCNDA,         VFCNDSPI,
CPI  V     VFCNDSPO,       VFCNRUD,        VFCRDA,         VFCRDSPI,
CPI  V     VFCRDSPO,       VFCRRUD,        VFDCLNZ,        VFDCLQH,
CPI  V     VFDCMQH,        VH,             VICEF1,         VKINT,
CPI  V     VNZL,           VPHAT,          VQHAT,          VRHAT,
CPI  V     VSTALLQ1,       VSTALLQ2,       VSTALLZ,        VTAI,
CPI  V     VTAIL,          VTAIR,          VTRIM,          VVDA,
CPI  V     VXCG,
C
C  OUTPUTS
C
CPO  V     VCDBAS,         VCDCT,          VCDGR,          VCDS,
CPO  V     VCLBAS,         VCLCT,          VCLDE,          VCLGE,
CPO  V     VCLLS,          VCLNZ,          VCLQHAT,        VCLS,
CPO  V     VCM,            VCMBAS,         VCMCG,          VCMCT,
CPO  V     VCMDE,          VCMGR,          VCMQHAT,        VCNDA,
CPO  V     VCNDSPI,        VCNDSPO,        VCNPHAT,        VCNRHAT,
CPO  V     VCNRUD,         VCNS,           VCRDA,          VCRDSPI,
CPO  V     VCRDSPO,        VCRPHAT,        VCRRHAT,        VCRRUD,
CPO  V     VCY,            VCYDA,          VCYDSPI,        VCYDSPO,
CPO  V     VCYPHAT,        VCYRHAT,        VCYRUD,         VDUMMYR,
CPO  V     VFLAPS,         VRUD
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:10:17 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VCDGE          ! Drag due to ground effects
     &, VCDSP          ! Drag due to spoilers
     &, VCLSP          ! Lift due to spoilers
     &, VCMGE          ! PITCHING MOMENT DUE TO GROUND EFFECTS
     &, VCNWB          ! RIGID WING-BODY CN DUE TO BETA
     &, VCRWB          ! RIGID WING BODY CLL DUE TO BETA
     &, VCSPI          ! AVERAGE OF INBOARD SPOILERS            [deg]
     &, VCSPO          ! AVERAGE OF OUTBOARD SPOILERS           [deg]
     &, VCT1           ! ENGINE 1 THRUST COEFFICIENT
     &, VCT2           ! ENGINE 2 THRUST COEFFICIENT
     &, VCTSUM         ! SUM OF THRUST COEFFICIENTS
     &, VCYBAS         ! BASIC ELASTIC A/P CY WITH ZERO CONTROL INPUT
     &, VDCNP          ! NON NORMALIZE CN DUE TO ROLL RATE
     &, VDCNR          ! NON NORMALIZE CN DUE TO YAW RATE
     &, VDCRP          ! NON NORMALIZE CLL DUE TO ROLL RATE
     &, VDCRR          ! NON NORMALIZE CLL DUE TO YAW RATE
     &, VDCYP          ! CY DEPENDENCE ON PHAT
     &, VDCYR          ! Sideforce coefficient CYr
     &, VDUC           ! AVG. GEAR EXT.   1=DN,0=UP
     &, VDYNPR         ! DYNAMIC PRESSURE                 [lbs/ft**2]
     &, VFCDBAS        ! Cd BASIC f(Cl,flap)
     &, VFCDCT         ! Drag due to thrust effects
     &, VFCDGRGR       ! Drag due to gear
     &, VFCLACTA       ! Lift due to thrust effects
     &, VFCLBAS        ! Basic lift as a function of alpha and flaps
     &, VFCLCT         ! Lift due to thrust effects
     &, VFCLDEDE       ! Lift due to elevator
     &, VFCLDEGE       ! CM GROUND EFFECTS TERM ON ELEVATOR
     &, VFCMACGA       ! Pitching moment due to C.G.
      REAL*4   
     &  VFCMACTA       ! Pitching moment due to Ct
     &, VFCMB2         ! Additional CM at high angle of attack
     &, VFCMBAS        ! Cm due to alpha, Ct as a function of cg
     &, VFCMCT         ! Pitching moment due to Ct
     &, VFCMGE         ! PITCHING MOMENT DUE TO GROUND EFFECTS
     &, VFCMGRGR       ! Pitching moment due to gear
     &, VFCMO          ! First order pitching moment (alpha=0)
     &, VFCNDA         ! TOTAL YAW COEFFICIENT DUE TO AILERONS
     &, VFCNDSPI       ! CN DUE TO INBOARD SPOILERS
     &, VFCNDSPO       ! CN DUE TO OUTBOARD SPOILERS
     &, VFCNRUD        ! RIGID RUDDER YAW MOMENT COEFF.
     &, VFCRDA         ! TOTAL ROLL MOMENT DUE TO AILERONS
     &, VFCRDSPI       ! CLL DUE TO INBOARD SPOILERS
     &, VFCRDSPO       ! CLL DUE TO OUTBOARD SPOILERS
     &, VFCRRUD        ! RIGID RUDDER ROLL MOMENT COEFF.
     &, VFDCLNZ        ! Derivative of lift due to Normal Load
     &, VFDCLQH        ! Derivative of lift due to pitch rate
     &, VFDCMQH        ! Derivative of pitch due to pitch rate
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VICEF1         ! FLIGHT ICEING FACTOR
     &, VKINT          ! INTEGRATION CONSTANT
     &, VNZL           ! BODY AXES NORMAL LOAD FACTOR             [G]
     &, VPHAT          ! NON DIMENSIONAL ROLL RATE
     &, VQHAT          ! NON DIMENSIONAL PITCH RATE
     &, VRHAT          ! NON DIMENSIONAL YAW RATE
     &, VSTALLQ1       ! STALL HYSTERISIS PITCH TERM
     &, VSTALLQ2       ! STALL HYSTERISIS PITCH TERM
     &, VSTALLZ        ! STALL HYSTERISIS LIFT TERM
     &, VTAI           ! AVERAGE STATUS OF THERMAL ANTI-ICE SYSTEM
     &, VTAIL          ! STATUS OF THERMAL ANTI-ICE SYSTEM LEFT W.
     &, VTAIR          ! STATUS OF THERMAL ANTI-ICE SYSTEM RIGHT W.
      REAL*4   
     &  VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VVDA           ! NAE AILERON ARGUMENT (= - VAIL)        [deg]
     &, VXCG           ! CENTRE OF GRAVITY                       [in]
C$
      LOGICAL*1
     &  HQUICK         ! MOVE GEAR,FLAPS INSTANTLY
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  VCDBAS         ! BASIC ELASTIC A/P CD WITH ZERO CONTROL INPUT
     &, VCDCT          ! TOTAL CD DUE TO THRUST EFFECTS
     &, VCDGR          ! Drag due to gear
     &, VCDS           ! STABILITY AXIS TOTAL DRAG COEFFICIENT
     &, VCLBAS         ! BASIC WING BODY LIFT COEFFICIENT
     &, VCLCT          ! CL DUE TO SECONDARY THRUST EFFECTS
     &, VCLDE          ! CL DUE TO ELEVATOR DEFLECTION
     &, VCLGE          ! LIFT DUE TO GROUND EFFECTS
     &, VCLLS          ! STABILITY AXES TOTAL ROLL MOMENT COEFFICIENT
     &, VCLNZ          ! TOTAL CL DUE TO NORMAL LOAD
     &, VCLQHAT        ! TOTAL CL DUE TO NORMALIZED PITCH RATE
     &, VCLS           ! TOTAL CL IN THE STABILITY AXIS
     &, VCM            ! TOTAL PITCHING MOMENT COEFF (BODY AXES)
     &, VCMBAS         ! BASIC WING-BODY CM AS A FUNCTION OF ALPHA
     &, VCMCG          ! Pitching moment due to C.G.
     &, VCMCT          ! Pitching moment due to Ct
     &, VCMDE          ! TOTAL CHANGE IN CM DUE TO ELEVATOR
     &, VCMGR          ! Pitching moment due to gear
     &, VCMQHAT        ! TOTAL CM DUE TO NORMALIZED VQ
     &, VCNDA          ! TOTAL YAW COEFFICIENT DUE TO AILERONS
     &, VCNDSPI        ! CN DUE TO INBOARD SPOILERS
     &, VCNDSPO        ! CN DUE TO OUTBOARD SPOILERS
     &, VCNPHAT        ! EFFECT OF ROLL RATE ON CN
     &, VCNRHAT        ! EFFECT OF YAW RATE ON CN
     &, VCNRUD         ! RIGID RUDDER YAW MOMENT COEFF.
     &, VCNS           ! TOTAL YAW MOMENT COEFFICIENT (STAB. AXES)
     &, VCRDA          ! TOTAL ROLL MOMENT DUE TO AILERONS
     &, VCRDSPI        ! CLL DUE TO INBOARD SPOILERS
     &, VCRDSPO        ! CLL DUE TO OUTBOARD SPOILERS
     &, VCRPHAT        ! EFFECT OF ROLL RATE ON CLL
     &, VCRRHAT        ! EFFECT OF YAW RATE
      REAL*4   
     &  VCRRUD         ! RIGID RUDDER ROLL MOMENT COEFF.
     &, VCY            ! BODY AXES TOTAL SIDEFORCE COEFFICIENT
     &, VCYDA          ! CY DUE TO AILERONS
     &, VCYDSPI        ! SIDEFORCE DUE TO INBOARD SPOILERS
     &, VCYDSPO        ! SIDEFORCE DUE TO INBOARD SPOILERS
     &, VCYPHAT        ! TOTAL CY DUE ROLL RATE
     &, VCYRHAT        ! TOTAL CY DUE YAW RATE
     &, VCYRUD         ! RIGID RUDDER SIDEFORCE COEFFICIENT
     &, VDUMMYR(30)    ! REAL SPARES
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VRUD           ! RUDDER ANGLE +TEL                      [deg]
C$
      LOGICAL*1
     &  DUM0000001(16464),DUM0000002(32),DUM0000003(12)
     &, DUM0000004(108),DUM0000005(24),DUM0000006(36)
     &, DUM0000007(4),DUM0000008(28),DUM0000009(4)
     &, DUM0000010(8),DUM0000011(4),DUM0000012(12)
     &, DUM0000013(20),DUM0000014(48),DUM0000015(8)
     &, DUM0000016(4),DUM0000017(4),DUM0000018(24)
     &, DUM0000019(40),DUM0000020(32),DUM0000021(252)
     &, DUM0000022(12),DUM0000023(8),DUM0000024(4)
     &, DUM0000025(4),DUM0000026(8),DUM0000027(28)
     &, DUM0000028(16),DUM0000029(4),DUM0000030(4)
     &, DUM0000031(8),DUM0000032(52),DUM0000033(12)
     &, DUM0000034(4),DUM0000035(28),DUM0000036(360)
     &, DUM0000037(1888),DUM0000038(52),DUM0000039(188)
     &, DUM0000040(288),DUM0000041(936),DUM0000042(34)
     &, DUM0000043(1089),DUM0000044(4),DUM0000045(20)
     &, DUM0000046(28),DUM0000047(12),DUM0000048(16)
     &, DUM0000049(8)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VRUD,DUM0000002,VVDA,DUM0000003,VCSPI,VCSPO
     &, DUM0000004,VCT1,VCT2,DUM0000005,VCTSUM,DUM0000006,VFLAPS
     &, DUM0000007,VDUC,DUM0000008,VSTALLZ,VSTALLQ1,VSTALLQ2
     &, DUM0000009,VCDCT,DUM0000010,VCLCT,VCLDE,VCLNZ,VCLBAS
     &, VCLQHAT,VCLS,DUM0000011,VCLGE,DUM0000012,VNZL,DUM0000013
     &, VALPHA,DUM0000014,VCYDA,VCYDSPI,VCYDSPO,DUM0000015,VDCYR
     &, VCYPHAT,VCYRHAT,DUM0000016,VCYBAS,VCYRUD,DUM0000017,VDCYP
     &, VCY,DUM0000018,VBETA,DUM0000019,VCDBAS,VCDS,DUM0000020
     &, VCDGE,VFCDCT,VCDGR,VFCDGRGR,VCDSP,DUM0000021,VDYNPR,DUM0000022
     &, VCRDA,VCRDSPI,VCRDSPO,VCRPHAT,VCRRHAT,DUM0000023,VCRRUD
     &, DUM0000024,VCRWB,DUM0000025,VDCRP,VDCRR,DUM0000026,VCLLS
     &, DUM0000027,VPHAT,VCMDE,DUM0000028,VCMBAS,VCMQHAT,VCMGE
     &, DUM0000029,VCMCT,VFCMACTA,VCMGR,VFCMGRGR,VCMCG,VFCMACGA
     &, DUM0000030,VFCMCT,VFCMO,VCM,DUM0000031,VQHAT,DUM0000032
     &, VCNDA,VCNDSPI,VCNDSPO,VCNPHAT,VCNRHAT,DUM0000033,VCNRUD
     &, VDCNP,VDCNR,DUM0000034,VCNWB,VCNS,DUM0000035,VRHAT,DUM0000036
     &, VH,DUM0000037,VICEF1,VTAI,VTAIL,VTAIR,DUM0000038,VXCG
     &, DUM0000039,VKINT,DUM0000040,VTRIM,DUM0000041,VDUMMYR
     &, DUM0000042,HQUICK,DUM0000043,VFCLBAS,DUM0000044,VFCMBAS
     &, VFCMB2,DUM0000045,VFDCLNZ,VFDCLQH,VFDCMQH,DUM0000046
     &, VFCRDA,VFCRDSPI,VFCRDSPO,VFCNDSPI,VFCNDSPO,VFCRRUD,DUM0000047
     &, VFCNDA,VFCNRUD,VFCLDEDE,VFCLDEGE,VFCMGE,DUM0000048,VFCLCT
     &, VFCLACTA,VCLSP,DUM0000049,VFCDBAS   
C------------------------------------------------------------------------------
C
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
C Character
C
      CHARACTER*55  REV/
     &  '$Source: usd8vz3.for.10  6Sep1993 21:57 usd8 BCa    $'/
C
C Logicals
C
      LOGICAL   LFPASS/.TRUE./ ! First pass flag
      LOGICAL   LFLSTOP        ! Flaps not moving indication
      LOGICAL   LGRSTOP        ! Gear not moving indication
C
C Reals
C
      REAL*4    LSP0        ! Scratch pad
      REAL*4    LSP1        ! Scratch pad
      REAL*4    LSP2        ! Scratch pad
      REAL*4    LSP3        ! Scratch pad
      REAL*4    LSP4        ! Scratch pad
      REAL*4    LSP5        ! Scratch pad
      REAL*4    LSP6        ! Scratch pad
      REAL*4    LSP7        ! Scratch pad
      REAL*4    LSP8        ! Scratch pad
      REAL*4    VFCLICE /-.05/  !  Lift due to icing
      REAL*4    VFCMICE /-.075/ !  Pitch due to icing
      REAL*4    VFCDICE /.01/   !  Drag due to icing
      REAL*4    VFCRICE /-.005/ !  Roll due to assymetrical icing
      REAL*4    LCLICE      !  Lift due to icing
      REAL lbeta2,labeta,lfbeta,LG/0.85/
      real ltemp,LB1/.2/,LB2/.45/,LB3/.3/,LB4/-10.46/,LB5/-6.61/
      real LB6/1.0/,LB7/0.4/,L5
      REAL*4    LCMICE      !  Pitch due to icing
      REAL*4    LCDICE      !  Drag due to icing
      REAL*4    LCRICE      !  Roll due to assymetrical icing
      REAL*4    LVCTSUM     ! VCTSUM limited to 0.5
      REAL*4    LNZ         ! Local load factor
      REAL*4    LASTALL     ! Angle of attack for stall
      REAL*4    LALPHMAX    ! Maximum angle of attack
      REAL*4    LCLDEGE/1./ ! Elevator G.E. factor
      REAL*4    LCTLMCM/0.3/ ! Limit on CTSUM for pitch
      REAL*4    LFLAPS      ! Local flaps position
      REAL*4    LFLPMV      ! Flap motion indication
      REAL*4    LFLTIMER    ! Flaps not moving timer
      REAL*4    LCMFLP      ! Increment in pitching moment due to flp transition
      REAL*4    LFCMFLP     ! Maximum increment             "  "   "      "
      REAL*4    LFCMFLPR    ! Maximum increment retraction  "  "   "      "
      REAL*4    LFCMFLPE    ! Maximum increment extension   "  "   "      "
      REAL*4    LCMFLPED(11) ! Flap extend disturbance data
      REAL*4    LCMFLPRD(11) ! Flap retract disturbance data
      REAL*4    LFLAPBP(11) ! Flap breakpoint schedule
      REAL*4    LFLTC       ! Time constant on flap transition effect
      REAL*4    LFLTCE      ! Time constant on flap transition effect
      REAL*4    LFLTCR      ! Time constant on flap transition effect
      REAL*4    LFLTCS      ! Time constant on flap transition effect
      REAL*4    LFLTCV      ! Time constant on flap transition effect
      REAL*4    LDUC        ! Local gear position
      REAL*4    LGRMV       ! Gear motion indication
      REAL*4    LGRTIMER    ! Gear not moving timer
      REAL*4    LCMGR       ! Increment in pitching moment due to flp transition
      REAL*4    LFCMGR      ! Maximum increment             "  "   "      "
      REAL*4    LFCMGRR     ! Maximum increment retraction  "  "   "      "
      REAL*4    LFCMGRE     ! Maximum increment extension   "  "   "      "
      REAL*4    LCMGRED(7)  ! Gear extend disturbance data
      REAL*4    LCMGRRD(7)  ! Gear retract disturbance data
      REAL*4    LGEARBP(7)  ! Gear breakpoint schedule
      REAL*4    LGRTC       ! Time constant on gear transition effect
      REAL*4    LGRTCE      ! Time constant on gear transition effect
      REAL*4    LGRTCR      ! Time constant on gear transition effect
      REAL*4    LGRTCS      ! Time constant on gear transition effect
      REAL*4    LGRTCV      ! Time constant on gear transition effect
      REAL*4    LISTALLB    ! Beta factor for stall
      REAL*4    LRUD        ! scaled rudder input (reduced for vrud>10)
      REAL*4    LCYGER      ! additional CY when gear is down (lduc.ne.0)
      REAL*4    LDCT2       ! differential thrust ratio
      REAL*4    LCYDCT      ! sideforce induced by LDCT2
      REAL*4    LCNDCT      ! yawing moment induced by LDCT2
      REAL*4    LCNDCT2     ! yawing moment induced by LDCT2
      REAL*4    ROLL1/0.07/ ! scale factor to obtain LCRDCT2
      REAL*4    ldct2s       ! sign for scale factor to obtain LCRDCT2
      REAL*4    YAW1        ! scale factor to obtain LCNDCT2
      REAL*4    ascoef      ! air speed ramping of diff thrust moments
      REAL*4    ascm/0.8/   ! minimum value of ascoef
      REAL*4    hcoef       ! height ramping of diff thrust moments
      REAL*4    LCRDCT      ! rolling moment induced by LDCT2
      REAL*4    LCRDCT2     ! rolling moment induced by LDCT2
      REAL*4    LALFST      ! shaping factor for CLCT,CMCT at high alpha
      REAL*4    LALFSTV(5)  ! shaping factor for CLCT,CMCT at high alpha
      REAL*4    lbct        ! uncoord. flt. terms
      REAL*4    X1,X2       ! ground effect terms
      REAL*4    ldum        ! blend term in lcndct for flap=15
      REAL*4    QTAILR      ! Dynamic pressure at tail term
      REAL*4    LTAILQF/0.03/ ! Factor for dynamic pressure at tail
      REAL*4    LVAL0_35/0.03/   ! CMge ground effects term factor
      REAL*4    LVAL1_35/0.025/  ! CMge ground effects term factor
      REAL*4    LALT1_35/20.0/   ! CMge ground effects term factor
      REAL*4    LALT2_35/80.0/   ! CMge ground effects term factor
      REAL*4    LVAL0_15/0.02/   ! CMge ground effects term factor
      REAL*4    LVAL1_15/0.015/  ! CMge ground effects term factor
      REAL*4    LALT1_15/20.0/   ! CMge ground effects term factor
      REAL*4    LALT2_15/80.0/   ! CMge ground effects term factor
      REAL*4    LCTLIM/0.15/     ! Effect on elevator power due to CT , GE
      real*4    LGEULIM/2.0/     ! Downwash angle dependence VCLS (upper limit)
      real*4    LGELLIM/.5/      ! Downwash angle dependence VCLS (lower limit)
      real*4    LGEFACT/1.4/     ! Downwash dependence on normal load
      real*4    LDCNCRT          ! Secondary yaw effect of reverse thrust.
      REAL*4    CTMIN/1.5/,CTG1/1.0/,CTG2/-.04/
C
C Integers
C
      INTEGER   I, LISTALL, LIFLAP
C
C Data
C
      DATA LFLAPBP  /     1.5,    5.0,    7.5,   10.0,   12.5,   15.0,
     &                   20.0,   25.0,   30.0,   35.0,  100.0/
      DATA LCMFLPED /     0.020,  0.020,  0.015,  0.015,  0.010,  0.010,
     &                    0.050,  0.060,  0.080,  0.010,  0.010/
      DATA LCMFLPRD /    -0.040,  0.000,  0.000,  0.000, -0.010, -0.050,
     &                   -0.050, -0.040, -0.020, -0.000, -0.000/
      DATA LGEARBP  /     0.0,    0.2,    0.4,    0.6,    0.8,    1.0,
     &                   10.0/
      DATA LCMGRED  /     0.005,  0.04,  -0.025, -0.020, -0.020, -0.020,
     &                   -0.02/
      DATA LCMGRRD  /     0.00,   0.00,   0.005,  0.025,  0.030,  0.020,
     &                    0.00/
C
      DATA LALFSTV  /    18.,  18.,  19.,   19.,  20. /
C
C     +------------------------------+
C     |                              |
C     |     E N T R Y  P O I N T     |
C     |                              |
C     +------------------------------+
C
      ENTRY AEROCOEF   !LIFT
C     ==============
C
C
CD VAD010  First pass assignments
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         First past assignments
C
C
      IF (LFPASS) THEN
        LFLTCE   =   3.
        LFLTCR   =   6.
        LFLTCS   =   4.
        LGRTCE   =   3.
        LGRTCR   =   3.
        LGRTCS   =   6.
C
        LFPASS   = .FALSE.
      ENDIF
C
C
CD VAD020  Limited Ct values
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Limit Ct values for input to aerodynamic terms
C
C
C 5 Feb.'93:IAR Mod.
C LVCTSUM
C limit of 0.5 changed to 0.35 for Flaps=15 degrees, to get a
C better rudder match on Take-off's (high CT valued cases).
C
      LSP0    = VCTSUM
      IF (LSP0 .GT. 0.35) LSP0 = 0.35
      if(lsp0.eq.0.) lsp0=.01
      LVCTSUM = LSP0
      IF (VFLAPS.GT.15.0)THEN
         LVCTSUM=LVCTSUM+((VFLAPS-15.0)/20.0*AMAX1(0.,(VCTSUM-0.35)))
         IF (LVCTSUM.GT.0.5) LVCTSUM=0.5
      ENDIF
C
C     LIMITED AVERAGE THRUST COEFFIECINT FOR GROUND EFFECT FUNCTION
C
      LSP1 = AMIN1(LCTLIM,VCT1)
      LSP2 = AMIN1(LCTLIM,VCT2)
      VDUMMYR(7) = .5 * (LSP1+LSP2)
c
c	calculate thrust differential IAR sb
c
        ldct2= vct1 - vct2
        if(ldct2.gt. LB2)ldct2= LB2
        if(ldct2.lt.-LB2)ldct2=-LB2
C
C	TERM FOR DIFF THRUST MOMENTS AND FLAP 15
C
        if((vflaps.gt.10).and.(vflaps.lt.35.))then
           if(vflaps.le.15.)ldum=(vflaps-10.)/5.
           if(vflaps.gt.15.)ldum=(35-vflaps)/20.
        ELSE
          LDUM = 0.0
        endif
C
CD VAD040  Load factor aerodynamic terms
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Determine load factor terms used in aerodynamic build-up
C
C
      LNZ = VNZL-1.
      IF (LNZ .LT. -1.) LNZ = -1.
      IF (LNZ .GT. 1.)  LNZ =  1.
C
C
CD VAD050  Stall factor term
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Determine stall factor term
C
C
      IF (VFLAPS .LT. 3.) THEN        !flaps  0
         LASTALL = 19.
         LALFST  = LALFSTV(1)
      ELSEIF (VFLAPS .LT. 7.)  THEN   !flaps  5
         LASTALL = 18.
         LALFST  = LALFSTV(2)
      ELSEIF (VFLAPS .LT. 12.) THEN   !flaps 10
         LASTALL = 18.
         LALFST  = LALFSTV(3)
      ELSEIF (VFLAPS .LT. 17.) THEN   !flaps 15
         LASTALL = 20.
         LALFST  = LALFSTV(4)
      ELSE                            !flaps 35
         LASTALL = 19.
         LALFST  = LALFSTV(5)
      ENDIF
C
      IF (VTRIM .EQ. 0) THEN
        LISTALL  = 0.
        LALPHMAX = 0.
      ELSE
        IF ((VALPHA .GT. LASTALL) .AND. (VALPHA .GT. LALPHMAX)) THEN
          LALPHMAX = VALPHA
          LISTALL  = 1
        ELSE
          LISTALL  = 0
          LALPHMAX = 0.
        ENDIF
      ENDIF
C
C
C
CD VAD055  Dynamic pressure at tail
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Determine dynamic pressure at tail section
C
C
      IF (VCTSUM .GT. 0.) THEN
        QTAILR = 0.5 + VCTSUM*LTAILQF + 0.5*SQRT(1.+VCTSUM*LTAILQF)
      ELSE
        QTAILR = VDUMMYR(15)
      ENDIF
c
c	height above ground blending term for differential thrust induced
c       moments
c
      hcoef = amin1(1.0, amax1( 0., (vh-10.) / 50.))
C
C
C-------------------------------- LIFT SYSTEM -------------------------------
C
C
CD VAD060  Basic Lift
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate basic lift term
C
C
      VCLBAS = VFCLBAS - VSTALLZ
C
CD VAD070  Increment in Lift due to wing tosion
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate lift term due to large 'g' loads
C
C
      VCLNZ = VFDCLNZ * LNZ
C
C
CD VAD080  Increment in lift due to ground effects
CR         IAR calculation - RS
CC ---------------------------------------------------------------------------
CC         Calculate lift term due to ground effects
C
C
      VCLGE = 0.0
      if(VFLAPS.ge.13.0)then    ! i.e. 15 or 35
         x1 = 0.0
c
c In USD8VS.for, VHT(tire ht. agl) is set = VH(CG ht. agl)
c But RADALT shows 53 feet, instead of 0.0, when a/c is on the ground,
c ie. RadAlt'=RadAlt-53.0
c Also, 0 RadAlt = 10.5 VH, ie. RadAlt=VH-10.5
c
        if((VH-10.5).le.80.)then
          x1 = VH - 10.5
          if (x1.le.0.) x1 = 0.
                if(VFLAPS.le.17.0)then  ! flaps=15 deg.
                  VCLGE = 0.04 - (0.04/80.0)*x1
          else                    ! flaps=35 deg.
            VCLGE = 0.07 - (0.07/80.0)*x1
          endif
        endif
      endif
C
C
CD VAD090  Increment in lift due to Ct effects
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate lift term due to Ct effects
C
C
      VCLCT = (VFCLCT + VFCLACTA) * LVCTSUM
C
      VCLCT = VCLCT * ( 1. - 0.5 * ABS(LDCT2/LVCTSUM) )
C
      LSP6  = (AMIN1((LALFST+4.),VALPHA) - LALFST)/4.
      LSP6  = 1. - LSP6
      IF (LSP6 .LT. 0.2) LSP6 = 0.2
      IF (VALPHA .GT. LALFST) VCLCT = VCLCT * LSP6
C
      IF (VCLCT .LT. 0.) VCLCT = 0.
C
C
CD VAD100  Increment in lift due to elevator
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate lift term due to elevator
C
      VCLDE = VFCLDEDE * VFCLDEGE
C
C
CD VAD110  Increment in lift due to spoilers
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate lift term due spoilers
C
C
C       VCLSP = VFCRDSPI * (VCSPLI + VCSPRI) * 90. / 24.62
C     &       + VFCRDSPO * (VCSPLO + VCSPRO) * 90. / 30.37
C
C
CD VAD120  Increment in lift due to icing
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate lift term due to icing
C
C
      LCLICE = (VTAIL + VTAIR) * VFCLICE
C
C
CD VAD130  Increment in lift due to Effect of pitch rate
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate lift term due to pitch rate
C
C
      VCLQHAT = VFDCLQH * VQHAT
C
C
CD VAD140  Total lift coefficient
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate total lift coefficient
C
C
      VCLS  = VCLBAS + VCLNZ + VCLGE + VCLCT + VCLDE + VCLSP +
     &        LCLICE + VCLQHAT
C
C
C
C------------------------------- PITCH SYSTEM --------------------------------
C
C
CD VAD160  Basic Pitching moment
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate basic pitching moment term
C
C
      VCMBAS = VFCMBAS + VFCMB2 + VFCMO - VSTALLQ1
C
C
CD VAD165  Increment in pitching moment due to flap transition effects
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate pitching moment effects due to flap transitions
C
C
C     Determine whether flaps are stationary, extending, or retracting
C
      IF (HQUICK .OR. (VTRIM.EQ.0.)) THEN
        LFCMFLP  = 0.
        LCMFLP   = 0.
        LFLTCV   = LFLTCS
        LFLTC    = LFLTCS
        LFLSTOP  = .TRUE.
        LFLTIMER = 0.1
      ELSEIF (VFLAPS .EQ. LFLAPS) THEN
        LFLTIMER = AMAX1((LFLTIMER + VKINT),0.1)
        IF (LFLTIMER .GT. 0.099) LFLSTOP = .TRUE.
      ELSEIF (VFLAPS .GT. LFLAPS) THEN
        LFLPMV   =  1.
        LFLTIMER =  0.
        LFLSTOP  = .FALSE.
      ELSEIF (VFLAPS .LT. LFLAPS) THEN
        LFLPMV   = -1.
        LFLTIMER =  0.
        LFLSTOP  = .FALSE.
      ENDIF
      LFLAPS = VFLAPS
C
C     Determine disturbance effect: When flaps are moved, a disturbance is
C     modelled which becomes effective just after flap movement is initiated
C     and faded to 0. within a certain time after flap movement has stopped.
C
      I = 1
      DO WHILE (VFLAPS .GT. LFLAPBP(I))
       I = I + 1
      ENDDO
      IF (I.GT.10) I=10
      LFCMFLPE  = LCMFLPED(I)
      LFCMFLPR  = LCMFLPRD(I)
C
      IF (LFLSTOP) THEN
        LFCMFLP = LFCMFLP - LFCMFLP * 0.2 * VKINT
        LFLTCV  = LFLTCS
      ELSEIF (LFLPMV .LT. 0.) THEN
        LFCMFLP = LFCMFLPR
        LFLTCV  = LFLTCR
      ELSE
        LFCMFLP = LFCMFLPE
        LFLTCV  = LFLTCE
      ENDIF
C
      LFLTC  =  LFLTC  + (LFLTCV  - LFLTC)  * (0.9 * VKINT)
      LCMFLP =  LCMFLP + (LFCMFLP - LCMFLP) *
     &                   (0.9 * VKINT / AMAX1(0.5,LFLTC))
C
C
CD VAD168  Increment in pitching moment due to gear transition effects
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate pitching moment effects due to gear transitions
C
C
C     Determine whether gear is stationary, extending, or retracting
C
      IF (HQUICK .OR. (VTRIM.EQ.0.)) THEN
        LFCMGR   = 0.
        LCMGR    = 0.
        LGRTCV   = LGRTCS
        LGRTC    = LGRTCS
        LGRSTOP  = .TRUE.
        LGRTIMER = 0.1
      ELSEIF (VDUC .EQ. LDUC) THEN
        LGRTIMER = AMAX1((LGRTIMER + VKINT),0.1)
        IF (LGRTIMER .GT. 0.099) LGRSTOP = .TRUE.
      ELSEIF (VDUC .GT. LDUC) THEN
        LGRMV    =  1.
        LGRTIMER =  0.
        LGRSTOP  = .FALSE.
      ELSEIF (VDUC .LT. LDUC) THEN
        LGRMV    = -1.
        LGRTIMER =  0.
        LGRSTOP  = .FALSE.
      ENDIF
      LDUC = VDUC
C
C     Determine disturbance effect: When gear is moved, a disturbance is
C     modelled which becomes effective just after gear movement is initiated
C     and faded to 0. within a certain time after gear movement has stopped.
C
      I = 1
      DO WHILE (VDUC .GT. LGEARBP(I))
       I = I + 1
      ENDDO
      IF (I.GT.6) I=6
      LFCMGRE  = LCMGRED(I)
      LFCMGRR  = LCMGRRD(I)
C
      IF (LGRSTOP) THEN
        LFCMGR  = LFCMGR - LFCMGR * 0.2 * VKINT
        LGRTCV  = LGRTCS
      ELSEIF (LGRMV .LT. 0.) THEN
        LFCMGR  = LFCMGRR
        LGRTCV  = LGRTCR
      ELSE
        LFCMGR  = LFCMGRE
        LGRTCV  = LGRTCE
      ENDIF
C
      LGRTC  =  LGRTC + (LGRTCV  - LGRTC) * (2.0 * VKINT)
      LCMGR  =  LCMGR + (LFCMGR - LCMGR)  *
     &                   (0.9 * VKINT / AMAX1(0.5,LGRTC))
C
C
CD VAD170  Increment in pitching moment due to ground effects
CR         IAR calculation - RS
CC ---------------------------------------------------------------------------
CC         Calculate pitching moment term due to ground effects
C          PVE. multiply ground effects function by a scaled Cl
C          to simulate increased effects at higher downwash angles.
C
       LSP0 = AMIN1(LGEULIM,AMAX1(LGELLIM,(VCLS-VCLDE)))
       VCMGE = VFCMGE * LSP0*lgefact
C
CD VAD180  Increment in pitching moment due to Ct effects
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate pitching moment term due to Ct effects
C
C
      VCMCT = (VFCMACTA + VFCMCT) * AMIN1(VCTSUM,LCTLMCM)
      LSP6  = (AMIN1((LALFST+4.),VALPHA) - LALFST)/4.
      LSP6  = 1. - LSP6
      IF (LSP6 .LT. 0.2) LSP6 = 0.2
      IF (VALPHA .GT. LALFST) VCMCT = VCMCT * LSP6
C
C
CD VAD190  Increment in pitching moment due to elevator
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate pitching moment term due to elevator
C
      VCMDE = - (VCLDE) * (909.56-VXCG)/80.64 * VSTALLQ2
C
C
CD VAD200  Increment in pitching moment due to gear
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate pitching moment term due to gear
C
C
      VCMGR = VFCMGRGR + LCMGR
C
C
CD VAD210  Increment in pitching moment due to icing
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate pitching moment term due to icing
C
C
      LCMICE = VTAI * VFCMICE
C
C
CD VAD220  Increment in pitching moment  due to c.g. effects
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate pitching moment term due to c.g.
C
C
      VCMCG = VFCMACGA * (VXCG - 400)
C
C
CD VAD230  Increment in pitching moment due to pitch rate
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate pitching moment term due to pitch rate
C
C
      VCMQHAT = VFDCMQH * VQHAT
C
C
CD VAD240  Total pitching moment coefficient.
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate total pitching moment
C
C
      VCM = VCMBAS + LCMFLP + VCMGE + VCMCT + VCMDE + VCMGR  + LCMICE +
     &      VCMCG + VCMQHAT
C
C
C-------------------------------- DRAG SYSTEM --------------------------------
C
C
CD VAD250  Basic drag
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate basic drag term
C
      VCDBAS = VFCDBAS
C
CD VAD260  Reduction in drag due to ground effects
CR         IAR calculation - RS
CC ---------------------------------------------------------------------------
CC         Calculate reduction in CDbas due to ground effects
C
C
      IF (VH.GE.15.0) THEN
        X2 = (17.0* VH /90.0)**2.
      ELSE
        X2 = (17.0*15.0/90.0)**2.
      ENDIF
      VCDBAS = VCDBAS*X2/(1+X2)
C
C
CD VAD270  Increment in drag due to Ct effects
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate drag term due to Ct effects
C
C
      VCDCT = VFCDCT * VCTSUM
C
C     Increment in drag due to one engine failed
C     checked for flaps 35 only (SB. nrc)
C
      IF (ABS(LDCT2).GT.0.02) THEN
        VCDCT = VCDCT + 0.095 * (ABS(LDCT2)-0.02) *
     &          ( 0.3*AMIN1(1.,VFLAPS/15.) +
     &            0.7*AMIN1(1.,AMAX1(0.,(VFLAPS-15)/20.)) )
      ENDIF
C
CD VAD280  Increment in drag due to gear
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate drag term due to gear
C
C
      VCDGR = VFCDGRGR
C
C
CD VAD290  Increment in drag due to spoilers
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate drag term due to spoilers
C
C
C     Drag term due to spoilers is calculated here: VCDDSPI,VCDDSPO,VCDSPGND,
C                                                   VCDSP
C
C
CD VAD300  INcrement in drag due to icing effects
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate drag term due to icing
C
C
      LCDICE = VICEF1 * VFCDICE
C
C
CD VAD310  Total drag coefficient
CR         CAE calculation
CC ---------------------------------------------------------------------------
CC         Calculate total drag coefficient.
C
C
      VCDS = VCDBAS + VCDGE + VCDGR + VCDSP + VCDCT + LCDICE
C
CC-----------------------------------------------------------------------------
C
C-------------------------------- LATERAL MODEL -------------------------------
c
CD VAD320  Calculation of reduced rudder deflection
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
         lrud = vrud
        if(abs(vrud).gt.10.)then
         lrud = vrud * 0.74
         if(lrud.gt.0.)lrud=lrud+2.6
         if(lrud.lt.0.)lrud=lrud-2.6
         if (vflaps.lt.20.) then
           if (vrud.gt. 25.) lrud = lrud + (vrud-25.) * 0.15
           if (vrud.lt.-25.) lrud = lrud + (vrud+25.) * 0.15
         endif
        endif
C
CD VAD330  Calculation of diff. thrust induced effective sideslip
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
        LBCT = -24.0 * LDCT2
C
C
C-------------------------------- SIDEFORCE SYSTEM -----------------------------
C
CD VAD340  Increment in sideforce due to thrust effects, landing gear and beta
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
CC         Sideforce terms due diff. thrust, landing gear and beta are
CC         Calculated
C
        LCYDCT = -.0230 * LBCT
C
C
C Protection limits and fades on beta
C
        labeta  = abs(vbeta)
        lbeta2 = vbeta
        if (labeta.gt.90)then
          lfbeta = abs(sin(vbeta/57.3))
        else
          lfbeta = 1.
        endif
        if(labeta .gt. 20.)then
          lbeta2 = 20.*vbeta/labeta*lfbeta
        endif
c
        LSP1 = AMAX1(0.,amin1(.25,vct1))
        LSP2 = AMAX1(0.,amin1(.25,vct2))
c
        L5 = ( LB1 + (LB6-LB1)*(-4.)*AMAX1(-.25,AMIN1(0.,(LSP1-LSP2)))
     &             + (LB7-LB1)*(-4.)*AMAX1(-.25,AMIN1(0.,(LSP2-LSP1))) )
     &       * (LB3 + (1.-LB3)*hcoef)
C
        lcydct=lcydct-l5*ldum*ldct2*(-.037*LB4+.0896)
c_pve        if(vflaps.eq.15)lcydct=lcydct-ldct2*(-.037*vbeta+.0896)
c
        LCYGER = -.003*lBETA2*LDUC
c_pve        LCYGER = -.003*VBETA*LDUC
C
CD VAD350  Increment in sideforce due to effect of roll and yaw rates.
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
CC         Calculate sideforce terms due to roll and yaw rates
C
        VCYPHAT = VDCYP   * VPHAT
        VCYRHAT = VDCYR   * VRHAT
C
CD VAD360  Increment in sideforce due to aileron
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
CC         Calculate sideforce term due to aileron deflection
C
        VCYDA   = VCYDA   * VVDA
C
CD VAD370  Increment in sideforce due to rudder
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
CC         Calculate sideforce term due to rudder deflection
C
        VCYRUD  = VCYRUD * LRUD * qtailr
C
CD VAD380  Increment in sideforce due to spoilers
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
CC         Calculate sideforce terms due to spoiler deflections
C
        VCYDSPI = VCYDSPI * VCSPI
        VCYDSPO = VCYDSPO * VCSPO
C
CD VAD390  Total sideforce coefficient
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
CC         Calculate total sideforce coefficient
C
        VCY = VCYBAS+VCYPHAT+VCYRHAT+VCYDA+VCYRUD+VCYDSPI+VCYDSPO
     &        +LCYDCT+lcyger
C
C
C-------------------------------- ROLL SYSTEM ----------------------------------
C
CD VAD400  Increment in roll due to Ct effects
CR         IAR Calculation
CC ---------------------------------------------------------------------------
CC         Calculate roll term due to Ct effects
C
        if (vflaps.lt.15)then
          ltemp = lg
        else
          ltemp = lg + (1. - lg)*(vflaps-15)/20.
        endif
        LCRDCT =  -.008 * LBCT * Ltemp
        LCRDCT2 = 0.0
        if(LDCT2.gt.0.0) then
          ldct2s = -1.
        else
          ldct2s = 1.
        endif
          if(abs(ldct2).gt.0.3)then
              lcrdct2 = LDUM * ROLL1 * ldct2s
          elseif(abs(ldct2).lt.0.20)then
              lcrdct2 = 0.00
          else
              lcrdct2 = LDUM * (abs(ldct2) - 0.20)/0.1 * ROLL1 * ldct2s
          endif
C
CD VAD410  Increment in roll due to effect of roll and yaw rates.
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate roll terms due to roll and yaw rates
C
C
        VCRPHAT = VDCRP * VPHAT
        VCRRHAT = VDCRR * VRHAT
C
C
CD VAD420  Increment in roll due to aileron
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate roll term due to aileron deflection
C
        VCRDA   = VFCRDA   * VVDA       !  VAIL = -VVDA
C
C
CD VAD430  Increment in roll due to rudder
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate roll term due to rudder deflection
C
        VCRRUD  = VFCRRUD  * LRUD * qtailr
C
C
CD VAD440  Increment in roll due to spoilers
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate roll terms due to spoiler deflections
C
        VCRDSPI = VFCRDSPI * VCSPI
        VCRDSPO = VFCRDSPO * VCSPO
C
C
CD VAD450  Increment in roll due to effect of ice
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate roll terms due to icing effects
 
        LCRICE  = (VTAIL-VTAIR) * VFCRICE
C
C
CD VAD460  Total roll coefficient
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate total roll coefficient
C
        VCLLS =VCRWB+VCRPHAT+VCRRHAT+VCRDA+VCRRUD+VCRDSPI+VCRDSPO
     &          +LCRDCT  + 0.0049*LVCTSUM + LCRICE
     &          + hcoef*(lcrdct2*Lvctsum)
C
C
C-------------------------------- YAW SYSTEM -----------------------------------
C
C
CD VAD470  Increment in yaw due to thrust effects
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate yaw term due to thrust effects
C
C
        LCNDCT = 0.0005 * LBCT
        LCNDCT2 = 0.0
        YAW1 = 0.05+0.008*VDUC     !YAW1=0.05 for LG Up, 0.058 for LG Dn.
        YAW1= ldct2s * YAW1
 
        lcndct=lcndct-l5*ldum*ldct2*(-0.006319*LB5 - .008708)
C_pve        lcndct=lcndct-ldum*ldct2*(-0.006319*Vbeta - .008708)
C
          if(abs(ldct2).gt.0.3)then
              lcndct2 = LDUM * YAW1
          elseif(abs(ldct2).lt.0.20)then
              lcndct2 = 0.00
          else
              lcndct2 = LDUM * (abs(ldct2) - 0.20)/0.1 * YAW1
          endif
c
c	air speed blending term for differential thrust induced
c       moments
c
      ascoef = amin1(1.0,(amax1 (ascm,(1.0 - ( 30. - VDYNPR  )/10.))))
C
CD VAD480  Increment in yaw due to effect of roll and yaw rates.
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate yaw terms due to roll and yaw rates
C
        VCNPHAT = VDCNP * VPHAT
        VCNRHAT = VDCNR * VRHAT
C
CD VAD490  Increment in yaw due to aileron
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
CC         Calculate yaw term due to aileron deflection
C
        VCNDA   = VFCNDA   * VVDA            !  VAIL = -VVDA
C
CD VAD495  Increment in yaw due to reverse thrust
CR         CAE Calculation
CC ---------------------------------------------------------------------------
C
CC         Calculate secondary yaw effects of reverse thrust
C
        LDCNCRT = VDUMMYR(16)
C
CD VAD500  Increment in yaw due to rudder
CR         IAR Calculation
CC ---------------------------------------------------------------------------
C
CC         Calculate yaw term due to rudder deflection
C
        VCNRUD  = VFCNRUD  * LRUD * qtailr
C
CD VAD510  Increment in yaw due to spoilers
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate yaw terms due to spoiler deflections
C
        VCNDSPI = VFCNDSPI * VCSPI
        VCNDSPO = VFCNDSPO * VCSPO
C
CD VAD520  Total yaw coefficient
CR         IAR Calculation
CC ---------------------------------------------------------------------------
 
CC         Calculate total yaw coefficient
C
        VCNS =VCNWB+VCNPHAT+VCNRHAT+VCNDA+VCNRUD+VCNDSPI+VCNDSPO
     &       +LCNDCT - 0.042*LVCTSUM + 0.0003*VALPHA+.0012
     &        - hcoef * ascoef * ( lcndct2*Lvctsum ) + LDCNCRT
     &       + AMIN1(1.,AMAX1(0.,((VCTSUM-CTMIN)*CTG1)))*CTG2
C
c	coefficients above reflect 5 Nov rudder trim analysis
c	old coefficient was -.025 Lvctsum +.0003 *valpha +0.0
c	sb.
c
C
       RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00468 VAD010  First pass assignments
C$ 00486 VAD020  Limited Ct values
C$ 00527 VAD040  Load factor aerodynamic terms
C$ 00538 VAD050  Stall factor term
C$ 00576 VAD055  Dynamic pressure at tail
C$ 00597 VAD060  Basic Lift
C$ 00605 VAD070  Increment in Lift due to wing tosion
C$ 00614 VAD080  Increment in lift due to ground effects
C$ 00641 VAD090  Increment in lift due to Ct effects
C$ 00659 VAD100  Increment in lift due to elevator
C$ 00667 VAD110  Increment in lift due to spoilers
C$ 00677 VAD120  Increment in lift due to icing
C$ 00686 VAD130  Increment in lift due to Effect of pitch rate
C$ 00695 VAD140  Total lift coefficient
C$ 00709 VAD160  Basic Pitching moment
C$ 00718 VAD165  Increment in pitching moment due to flap transition effects
C$ 00775 VAD168  Increment in pitching moment due to gear transition effects
C$ 00832 VAD170  Increment in pitching moment due to ground effects
C$ 00842 VAD180  Increment in pitching moment due to Ct effects
C$ 00855 VAD190  Increment in pitching moment due to elevator
C$ 00863 VAD200  Increment in pitching moment due to gear
C$ 00872 VAD210  Increment in pitching moment due to icing
C$ 00881 VAD220  Increment in pitching moment  due to c.g. effects
C$ 00890 VAD230  Increment in pitching moment due to pitch rate
C$ 00899 VAD240  Total pitching moment coefficient.
C$ 00912 VAD250  Basic drag
C$ 00919 VAD260  Reduction in drag due to ground effects
C$ 00933 VAD270  Increment in drag due to Ct effects
C$ 00950 VAD280  Increment in drag due to gear
C$ 00959 VAD290  Increment in drag due to spoilers
C$ 00969 VAD300  INcrement in drag due to icing effects
C$ 00978 VAD310  Total drag coefficient
C$ 00990 VAD320  Calculation of reduced rudder deflection
C$ 01005 VAD330  Calculation of diff. thrust induced effective sideslip
C$ 01014 VAD340  Increment in sideforce due to thrust effects, landing gear and
C$ 01050 VAD350  Increment in sideforce due to effect of roll and yaw rates.
C$ 01059 VAD360  Increment in sideforce due to aileron
C$ 01067 VAD370  Increment in sideforce due to rudder
C$ 01075 VAD380  Increment in sideforce due to spoilers
C$ 01084 VAD390  Total sideforce coefficient
C$ 01096 VAD400  Increment in roll due to Ct effects
C$ 01121 VAD410  Increment in roll due to effect of roll and yaw rates.
C$ 01132 VAD420  Increment in roll due to aileron
C$ 01141 VAD430  Increment in roll due to rudder
C$ 01150 VAD440  Increment in roll due to spoilers
C$ 01160 VAD450  Increment in roll due to effect of ice
C$ 01169 VAD460  Total roll coefficient
C$ 01183 VAD470  Increment in yaw due to thrust effects
C$ 01211 VAD480  Increment in yaw due to effect of roll and yaw rates.
C$ 01220 VAD490  Increment in yaw due to aileron
C$ 01228 VAD495  Increment in yaw due to reverse thrust
C$ 01236 VAD500  Increment in yaw due to rudder
C$ 01244 VAD510  Increment in yaw due to spoilers
C$ 01253 VAD520  Total yaw coefficient
