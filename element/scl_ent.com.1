#!  /bin/csh -f
#!  $Revision: SCL_ENT - Enter/Extract a SCALDATA Data File V1.1 (MT) May-91$
#!
#! &
#! @$SCALING_.
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
if ( "$argv[1]" == "Y" ) then
 set verbose
 set verify
endif 
if ! ("$argv[2]" == "EXTRACT" || "$argv[2]" == "ENTER") exit
set argv[3]="`revl -'$argv[3]'`"
set argv[4]="`revl -'$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
#
if ("$argv[2]" == "ENTER") then
  set FSE_LINE="`sed -n '1p' $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
  set FSE_FILE="`cvfs '$FSE_FILE'`"
  set FSE_SAVE=$SIMEX_DIR/enter/$FSE_FILE:t
  setenv fse_select "$FSE_FILE"
  setenv fse_source "$FSE_SAVE"
  setenv fse_target "$argv[4]"
  setenv fse_action "R"
  fse_enter
endif
#
if ("$argv[2]" == "EXTRACT") then
  set FSE_MAIN=""
  set EOFL=`sed -n '$=' "$argv[3]"`
  if ($EOFL == 0) exit
  set lcount=1
#
  while ($lcount <= $EOFL)
    set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
    set stat=$status
    if ($stat != 0) then
      echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
      exit $stat
    endif
    @ lcount = $lcount + 1
#
    set FSE_CODE="`echo '$FSE_LINE' | cut -c1-3`"
    set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
    if ("$FSE_CODE" == "SR ") then
      if ("$FSE_FILE" != "$FSE_MAIN") then
        set FSE_MAIN="$FSE_FILE"
        echo "0ECBSR $FSE_FILE" >>$argv[4]
      endif
    else
      if ("$FSE_CODE" == "IR ") echo "0ECBIR $FSE_FILE" >>$argv[4]
    endif
  end
endif
# 
exit

