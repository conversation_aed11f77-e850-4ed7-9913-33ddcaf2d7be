
/*****************************************************************************

  'Title                SERVO CONTROLLER LOGIC PROGRAM
  'Module_ID            SERVOLOGIC
  'Entry_point          SERVOLOGIC
  'Documentation
  'Customer             GENERIC
  'Application          Scaling and validation of servo controller gains
  'Author               NORM BLUTEAU
  'Date                 MAY 1991

  'System               MOTION
  'Iteration_rate       0.033 msec
  'Process              Synchronous process

*****************************************************************************
C'Revision_History
03 may 92 NORM Mapping to motion spare to host cdb for debugging
03 may 92 NORM Map labels now global

  'References

*/


#include "mot_define.h"
#include "mot_ext.h"
#include <math.h>
#include <adio.h>
extern float MKCOTEST;
extern float MKCOW;
extern float MKCOA;
extern float MKCOB;
extern float MKCOE;
extern float MKCODAMP;
extern float MKCOWAVE;
extern float MKCOAMP;
extern float MKCOFREQ;
extern int   MKCOLOC;
static float m_kia0;
static float m_ka1;
static float m_ka2;
void servologic()
{

static float m_j1kco;	/* previous values of KCO */
static float m_j2kco;
static float m_j3kco;
static float m_j4kco;
static float m_j5kco;
static float m_j6kco;
static float sp0;
/*
* 	-----------------------------------
**	compute servocontroler denominators
* 	-----------------------------------
*/

/*
* validate kco and make them all the same
*
*	note: kco always positive
*/

 	if (J1KCO != m_j1kco )
	{
		sp0 = MAX(J1KCO,0.0);
		J1KCO = sp0;
		J2KCO = sp0;
		J3KCO = sp0;
		J4KCO = sp0;
		J5KCO = sp0;
		J6KCO = sp0;
	}
 	else if (J2KCO != m_j2kco )
	{
		sp0 = MAX(J2KCO,0.0);
		J1KCO = sp0;
		J2KCO = sp0;
		J3KCO = sp0;
		J4KCO = sp0;
		J5KCO = sp0;
		J6KCO = sp0;
	}
 	else if (J3KCO != m_j3kco )
	{
		sp0 = MAX(J3KCO,0.0);
		J1KCO = sp0;
		J2KCO = sp0;
		J3KCO = sp0;
		J4KCO = sp0;
		J5KCO = sp0;
		J6KCO = sp0;
	}
 	else if (J4KCO != m_j4kco )
	{
		sp0 = MAX(J4KCO,0.0);
		J1KCO = sp0;
		J2KCO = sp0;
		J3KCO = sp0;
		J4KCO = sp0;
		J5KCO = sp0;
		J6KCO = sp0;
	}
 	else if (J5KCO != m_j5kco )
	{
		sp0 = MAX(J5KCO,0.0);
		J1KCO = sp0;
		J2KCO = sp0;
		J3KCO = sp0;
		J4KCO = sp0;
		J5KCO = sp0;
		J6KCO = sp0;
	}
 	else if (J6KCO != m_j6kco )
	{
		sp0 = MAX(J6KCO,0.0);
		J1KCO = sp0;
		J2KCO = sp0;
		J3KCO = sp0;
		J4KCO = sp0;
		J5KCO = sp0;
		J6KCO = sp0;
	}

	m_j1kco = J1KCO;
	m_j2kco = J2KCO;
	m_j3kco = J3KCO;
	m_j4kco = J4KCO;
	m_j5kco = J5KCO;
	m_j6kco = J6KCO;

/*
* validate kbet ( always positive !)
*/

	J1KBET = MAX ( J1KBET, 0. ) ;
	J2KBET = MAX ( J2KBET, 0. ) ;
	J3KBET = MAX ( J3KBET, 0. ) ;
	J4KBET = MAX ( J4KBET, 0. ) ;
	J5KBET = MAX ( J5KBET, 0. ) ;
	J6KBET = MAX ( J6KBET, 0. ) ;
/*
* 	SCALING OF TUNING GAINS
*/
	J1KVS   = J1KV*KV_SC     + KV_OFS;
	J1KLS   = J1KL*KL_SC     + KL_OFS;
	J1KBETS = J1KBET*KBET_SC + KBET_OFS;
	J1KMIDS = J1KMID*KMID_SC + KMID_OFS;
	J1KCOS  = max(J1KCO*KCO_SC + KCO_OFS , 0.0 );
	J1KCOGS = J1KCOG*KCOG_SC + KCOG_OFS;
	J1KCOWS = J1KCOW*KCOW_SC + KCOW_OFS;

	J2KVS   = J2KV*KV_SC     + KV_OFS;
	J2KLS   = J2KL*KL_SC     + KL_OFS;
	J2KBETS = J2KBET*KBET_SC + KBET_OFS;
	J2KMIDS = J2KMID*KMID_SC + KMID_OFS;
	J2KCOS  = max(J2KCO*KCO_SC + KCO_OFS , 0.0 );
	J2KCOGS = J2KCOG*KCOG_SC + KCOG_OFS;
	J2KCOWS = J2KCOW*KCOW_SC + KCOW_OFS;

	J3KVS   = J3KV*KV_SC     + KV_OFS;
	J3KLS   = J3KL*KL_SC     + KL_OFS;
	J3KBETS = J3KBET*KBET_SC + KBET_OFS;
	J3KMIDS = J3KMID*KMID_SC + KMID_OFS;
	J3KCOS  = max(J3KCO*KCO_SC + KCO_OFS , 0.0 );
	J3KCOGS = J3KCOG*KCOG_SC + KCOG_OFS;
	J3KCOWS = J3KCOW*KCOW_SC + KCOW_OFS;

	J4KVS   = J4KV*KV_SC     + KV_OFS;
	J4KLS   = J4KL*KL_SC     + KL_OFS;
	J4KBETS = J4KBET*KBET_SC + KBET_OFS;
	J4KMIDS = J4KMID*KMID_SC + KMID_OFS;
	J4KCOS  = max(J4KCO*KCO_SC + KCO_OFS , 0.0 );
	J4KCOGS = J4KCOG*KCOG_SC + KCOG_OFS;
	J4KCOWS = J4KCOW*KCOW_SC + KCOW_OFS;

	J5KVS   = J5KV*KV_SC     + KV_OFS;
	J5KLS   = J5KL*KL_SC     + KL_OFS;
	J5KBETS = J5KBET*KBET_SC + KBET_OFS;
	J5KMIDS = J5KMID*KMID_SC + KMID_OFS;
	J5KCOS  = max(J5KCO*KCO_SC + KCO_OFS , 0.0 );
	J5KCOGS = J5KCOG*KCOG_SC + KCOG_OFS;
	J5KCOWS = J5KCOW*KCOW_SC + KCOW_OFS;

	J6KVS   = J6KV*KV_SC     + KV_OFS;
	J6KLS   = J6KL*KL_SC     + KL_OFS;
	J6KBETS = J6KBET*KBET_SC + KBET_OFS;
	J6KMIDS = J6KMID*KMID_SC + KMID_OFS;
	J6KCOS  = max(J6KCO*KCO_SC + KCO_OFS , 0.0 );
	J6KCOGS = J6KCOG*KCOG_SC + KCOG_OFS;
	J6KCOWS = J6KCOW*KCOW_SC + KCOW_OFS;

/*
* 	COMPUTE FORCE FILTER PARAMETERS ( ASSUMING SERVO RUNS AT 2000 HZ )
*       YITIM = .0005 sec
*/
/*********************
        MKCOA = MKCOW*MKCOW;
        MKCOB = 2*MKCODAMP*MKCOW;
        MKCOE = 1 / ( 1 + MKCOB*.0005 + MKCOA*.0005*.0005 );

        m_kia0 = 1 / ( 1 + MKCOW*.0005 + .25*MKCOA*.0005*.0005 );
        m_ka1  = -2 + .5*MKCOA*.0005*.0005;
        m_ka2  = 1 - MKCOW*.0005 + .25*MKCOA*.0005*.0005;
*************************/

	J1KCOWDEN = 1.0 / ( 1.0 + J1KCOWS * .0005) ;
	J2KCOWDEN = 1.0 / ( 1.0 + J2KCOWS * .0005) ;
	J3KCOWDEN = 1.0 / ( 1.0 + J3KCOWS * .0005) ;
	J4KCOWDEN = 1.0 / ( 1.0 + J4KCOWS * .0005) ;
	J5KCOWDEN = 1.0 / ( 1.0 + J5KCOWS * .0005) ;
	J6KCOWDEN = 1.0 / ( 1.0 + J6KCOWS * .0005) ;
}

/*****************************************************************************

  'Title                SERVO CONTROLLER PROGRAM
  'Module_ID            SERVO
  'Entry_point          SERVO
  'Documentation
  'Customer             GENERIC
  'Application          motion servo controller main
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history

  'References

*/

/*
* 	external declaration
*/
extern float task_table[27];
static float m_pxcf[6];
static int m_buwiring=1;

int ADIO_FAST = TRUE;

/*
      local variables
*/
static float m_count    ;  /*subbandding counter*/
static float sp0      ;  /* scratchpad */
static float sp1      ;  /* scratchpad */
/*
*	I/O scaling factors
*/
static float m_xa_scal ;  /* position signal input scaling */
static float m_xa_sc2 ;  /* position signal input scaling */
static float m_fa_scal ;  /* force signal input scaling */
static float m_ic_scal ;  /* current command output scaling */
static int m_sign = 1  ;  /* sign of ditter */
static int m_ditter = 5 ; /* ditter amplitude */

/* jack depend local variables */

static float m_fa1[6]           ;  /* Previous value of FA */
static float m_faf1[6]          ;  /* Previous value of FAF */

/*****
*static float m_y1[6];
*static float m_y2[6];
*static float m_x1[6];
*static float m_x2[6];
*static float m_int1[6];
*static float m_int2[6];
*static float m_fap[6];
*static float m_fapp[6];
*static float m_fafp[6];
*static float m_fafpp[6];
*static float m_time;
***********************/

static 	int i,
	m_psite = 1,
	m_sfpass = 1;

void SERVO()
{

/*
*     ----------
*     CODE START
*     ----------
*/
if(ADIO_FAST)
  adio_sync();

/*
*  -------------------------
*  FIRST PASS INITIALISATION
*  -------------------------
*/
if ( ( m_sfpass ) || (SITE != m_psite) )
{

	m_psite = SITE;


	if( SITE == HTF )
	{
      	 	IO_STATUS1 = adio_init(8);
	}
	if( SITE >= HTF )
	{
      		IO_STATUS1 = adio_init(ADIO_SLOT1);
	}
	if( SITE >= HTF )
	{
      		IO_STATUS2 = adio_init(ADIO_SLOT2);
      		IO_STATUS3 = adio_init(ADIO_SLOT3);
     		IO_STATUS4 = adio_init(ADIO_SLOT4);
      		IO_STATUS5 = adio_init(ADIO_SLOT5);
	}


	if(IO_STATUS1 == 1)
      	{
      		READ_ADIO1 = TRUE;
 		WRITE_ADIO1 = TRUE;
                adio_build_input(ADIO_SLOT1,&ADIO_AIP1);
                adio_build_output(ADIO_SLOT1,&ADIO_AOP1);
      	}

      	if(IO_STATUS2 == 1)
      	{
      		READ_ADIO2 = TRUE;
 		WRITE_ADIO2 = TRUE;
                adio_build_input(ADIO_SLOT2,&ADIO_AIP2);
                adio_build_output(ADIO_SLOT2,&ADIO_AOP2);
      	}
      	if(IO_STATUS3 == 1)
      	{
      		READ_ADIO3 = TRUE;
 		WRITE_ADIO3 = TRUE;
                adio_build_input(ADIO_SLOT3,&ADIO_AIP3);
                adio_build_output(ADIO_SLOT3,&ADIO_AOP3);
      	}
      	if(IO_STATUS4 == 1)
      	{
      		READ_ADIO4 = TRUE;
 		WRITE_ADIO4 = TRUE;
                adio_build_input(ADIO_SLOT4,&ADIO_AIP4);
      	}
      	if(IO_STATUS5 == 1)
      	{
      		READ_ADIO5 = TRUE;
 		WRITE_ADIO5 = TRUE;
                adio_build_input(ADIO_SLOT5,&ADIO_AIP5);
      	}

	/*
	*	--------------------------------------------------
	*	SCALING FACTORS FOR INPUTS/OUTPUTS
	*       Force transducer : 11250 or 20000 lbs for 10 volts
	*	( BU R1=R2=R11=R12 = 100K or 55k
	*	--------------------------------------------------
	*/
	if ( MFORCESCAL==F11250 )
	{
	      	m_fa_scal = (11250./32767.);
	}
	else if ( MFORCESCAL==F20000 )
	{
	      	m_fa_scal = (20000./32767.);
	}

	/*
	*	-------------------------------------------------------------------
	*	Position transducer : MKJSCALE inches for 9.0 or 10.0 volts
	*	-------------------------------------------------------------------
	*/
	if ( MPOSSCAL==P9 )
	/*
	* 	9.0 volts full range
	*/
	{
		m_xa_scal = ( MKJSCALE / (32767.* 9./10.) );
		/*
		*	+/- 9.5 volt for testing 600 jack only
		*/
		m_xa_sc2 = 0.0008673;
	}
	else if ( MPOSSCAL==P10 )
	/*
	* 	10.0 volts full range
	*/
	{
	      	m_xa_scal = ( MKJSCALE / 32767 );
	}

/*
*	-----------------------------------------------------------------------
*	Current output : 40 mAmps for 4.622 volts ( saturation of Buffer unit )
*	-----------------------------------------------------------------------
*/
      	m_ic_scal = (4.622/10.)*32767/40.;   /* 4.622 v = 40 mAmps */
	/*
	*	init jack commands to actual jack position
	*/
	J1XCF = J1XAC;
	J2XCF = J2XAC;
	J3XCF = J3XAC;
	J4XCF = J4XAC;
	J5XCF = J5XAC;
	J6XCF = J6XAC;

	J1XC  = J1XAC;
	J2XC  = J2XAC;
	J3XC  = J3XAC;
	J4XC  = J4XAC;
	J5XC  = J5XAC;
	J6XC  = J6XAC;

	J1MXC = J1XAC;
	J2MXC = J2XAC;
	J3MXC = J3XAC;
	J4MXC = J4XAC;
	J5MXC = J5XAC;
	J6MXC = J6XAC;

	J1MJXC= J1XAC;
	J2MJXC= J2XAC;
	J3MJXC= J3XAC;
	J4MJXC= J4XAC;
	J5MJXC= J5XAC;
	J6MJXC= J6XAC;

	J1MJP = J1XAC;
	J2MJP = J2XAC;
	J3MJP = J3XAC;
	J4MJP = J4XAC;
	J5MJP = J5XAC;
	J6MJP = J6XAC;

	m_pxcf[JACK1]= J1XAC;
        m_pxcf[JACK2]= J2XAC;
        m_pxcf[JACK3]= J3XAC;
        m_pxcf[JACK4]= J4XAC;
        m_pxcf[JACK5]= J5XAC;
        m_pxcf[JACK6]= J6XAC;

	m_sfpass = FALSE;

	return;
}			/*end of if ( ( m_sfpass ) || (SITE != m_psite) )*/


/*
*     ---------------------------
*     00. read buffer units inputs
*     ---------------------------
*/
if(!ADIO_FAST)
{
  if ( SITE == HTF)
  {
	if (READ_ADIO1)
	{
		IO_STATUS1 = adio_read(8,&ADIO_AIP1,&ADIO_DIP1);
	}
  }
  else
  {
	if (READ_ADIO1)
	{
		IO_STATUS1 = adio_read(ADIO_SLOT1,&ADIO_AIP1,&ADIO_DIP1);
	}
  }
  if (READ_ADIO2)
  {
      	IO_STATUS2 = adio_read(ADIO_SLOT2,&ADIO_AIP2,&ADIO_DIP2);
  }
  if (READ_ADIO3)
  {
      	IO_STATUS3 = adio_read(ADIO_SLOT3,&ADIO_AIP3,&ADIO_DIP3);
  }
}
/*
*     ---------------
*     0. scale inputs
*     ---------------
*/

J1FAINP  = ((ADIO_AIP1[1]<<16)>>16) * m_fa_scal;
J1XAINP  = ((ADIO_AIP1[0]<<16)>>16) * m_xa_scal;

if(SITE != HTF )                             /* from HTF emulator */
{
	J2FAINP  = ((ADIO_AIP1[5]<<16)>>16) * m_fa_scal;
	J2XAINP  = ((ADIO_AIP1[4]<<16)>>16) * m_xa_scal;
}

J3FAINP  = ((ADIO_AIP2[1]<<16)>>16) * m_fa_scal;
J3XAINP  = ((ADIO_AIP2[0]<<16)>>16) * m_xa_scal;
J4FAINP  = ((ADIO_AIP2[5]<<16)>>16) * m_fa_scal;
J4XAINP  = ((ADIO_AIP2[4]<<16)>>16) * m_xa_scal;
J5FAINP  = ((ADIO_AIP3[1]<<16)>>16) * m_fa_scal;
/*      J5XAINP  = (ADIO_AIP3[0]) * m_xa_sc2;  for 600 only */
J5XAINP  = ((ADIO_AIP3[0]<<16)>>16) * m_xa_scal;
J6FAINP  = ((ADIO_AIP3[5]<<16)>>16) * m_fa_scal;
J6XAINP  = ((ADIO_AIP3[4]<<16)>>16) * m_xa_scal;

/*
*     -----------------------------------------------------------------
*     FORCE OFFSET
*     -----------------------------------------------------------------
*/
/***************************************
J1FAC  =  J1FAINP - J1FAOFS  ;
J2FAC  =  J2FAINP - J2FAOFS  ;
J3FAC  =  J3FAINP - J3FAOFS  ;
J4FAC  =  J4FAINP - J4FAOFS  ;
J5FAC  =  J5FAINP - J5FAOFS  ;
J6FAC  =  J6FAINP - J6FAOFS  ;
***************************************/

J1FAC  =  J1FAINP ;
J2FAC  =  J2FAINP ;
J3FAC  =  J3FAINP ;
J4FAC  =  J4FAINP ;
J5FAC  =  J5FAINP ;
J6FAC  =  J6FAINP ;


/*         unique ADIO DIP packed into indiv budips  */

J1BUDIP =   ADIO_DIP1 & 0XF;

if(SITE != HTF )                             /* from HTF emulator */
{
	J2BUDIP = ( ADIO_DIP1 & 0XF00 )  >> 8;
}

J3BUDIP =   ADIO_DIP2 & 0XF;
J4BUDIP = ( ADIO_DIP2 & 0XF00 )  >> 8;
J5BUDIP =   ADIO_DIP3 & 0XF;
J6BUDIP = ( ADIO_DIP3 & 0XF00 )  >> 8;



/*
*
*     -----------------------------------------------------------------
*     2. INTERPOLATED RATE FOR POS. AND ACC. COMMAND INTEGRATOR
*	THIS SECTION RUNS AT SAME ITERATION RATE AS MAIN ( 500 HZ )
*     -----------------------------------------------------------------
*/

m_count++;

if (HOSTUPDT)
{
	sp0 = 1.0 / m_count;

	J1XCD = ( J1XC - J1XCF ) * sp0;
	J2XCD = ( J2XC - J2XCF ) * sp0;
	J3XCD = ( J3XC - J3XCF ) * sp0;
	J4XCD = ( J4XC - J4XCF ) * sp0;
	J5XCD = ( J5XC - J5XCF ) * sp0;
	J6XCD = ( J6XC - J6XCF ) * sp0;

	J1VCD = ( J1VC - J1VCF ) * sp0;
	J2VCD = ( J2VC - J2VCF ) * sp0;
	J3VCD = ( J3VC - J3VCF ) * sp0;
	J4VCD = ( J4VC - J4VCF ) * sp0;
 	J5VCD = ( J5VC - J5VCF ) * sp0;
	J6VCD = ( J6VC - J6VCF ) * sp0;

	J1ACD = ( J1AC - J1ACF ) * sp0;
	J2ACD = ( J2AC - J2ACF ) * sp0;
	J3ACD = ( J3AC - J3ACF ) * sp0;
	J4ACD = ( J4AC - J4ACF ) * sp0;
 	J5ACD = ( J5AC - J5ACF ) * sp0;
	J6ACD = ( J6AC - J6ACF ) * sp0;

        HOSTUPDT = FALSE;
        m_count = 0.;
}
/*
* Compute sin for KCO testing
*
*
*m_time = m_time + YITIM;
*MKCOWAVE = MKCOAMP*sin ( MKCOFREQ*TWOPI*m_time );
*/

/*
*	--------------------------------------
*	call servocontroller macro for jack #1
*	--------------------------------------
*/

#undef CHAN
#define CHAN JACK1

#include "mot_servo.h"
#include "mot_servo.mac"

/*
*	--------------------------------------
*	call servocontroller macro for jack #2
*	--------------------------------------
*/

#undef CHAN
#define CHAN JACK2

#include "mot_servo.h"
#include "mot_servo.mac"

/*
*	--------------------------------------
*	call servocontroller macro for jack #3
*	--------------------------------------
*/

#undef CHAN
#define CHAN JACK3

#include "mot_servo.h"
#include "mot_servo.mac"

/*
*	--------------------------------------
*	call servocontroller macro for jack #4
*	--------------------------------------
*/

#undef CHAN
#define CHAN JACK4

#include "mot_servo.h"
#include "mot_servo.mac"

/*
*	--------------------------------------
*	call servocontroller macro for jack #5
*	--------------------------------------
*/

#undef CHAN
#define CHAN JACK5

#include "mot_servo.h"
#include "mot_servo.mac"

/*
*	--------------------------------------
*	call servocontroller macro for jack #6
*	--------------------------------------
*/

#undef CHAN
#define CHAN JACK6

#include "mot_servo.h"
#include "mot_servo.mac"

/*****************
*if ( MKCOTEST != 0.0 )
*{
*	ADIO_AOP1[4] = J1FAINP * (32767.*MAPGAIN1 );
*	ADIO_AOP1[5] = J1FAF   * (32767.*MAPGAIN2 );
*	ADIO_AOP1[6] = J1IC    * (32767.*MAPGAIN3 );
*}
****************************/
/*	-----------------------------------------------------------
*     	count number of iterations used to compute average velocity
*       -----------------------------------------------------------
*/
if ( ! VELINPROG )VELAVCOUNT = VELAVCOUNT + 1 ;
/*
*	----------------------------
*	outputs signals to ADIO card
*	----------------------------
*/

/*
*	indiv budops packed into unique ADIO DOP WORD
*/

ADIO_DOP1 = J1BUDOP | J2BUDOP << 4 | MABORTDOP << 8 | MABORTDOP << 12;
ADIO_DOP2 = J3BUDOP | J4BUDOP << 4 | MABORTDOP << 8 | MABORTDOP << 12;
ADIO_DOP3 = J5BUDOP | J6BUDOP << 4 | MABORTDOP << 8 | MABORTDOP << 12;

if (m_sign == 1 )
{
	m_sign = -1  ;
}
else
{
	m_sign = 1  ;
}

i = m_ditter * m_sign;

ADIO_AOP1[0]  = J1IC * m_ic_scal + i;
ADIO_AOP1[1]  = J2IC * m_ic_scal + i;
ADIO_AOP2[0]  = J3IC * m_ic_scal + i;
ADIO_AOP2[1]  = J4IC * m_ic_scal + i;
ADIO_AOP3[0]  = J5IC * m_ic_scal + i;
ADIO_AOP3[1]  = J6IC * m_ic_scal + i;

/* note : aop [2] to [7] of ADIO used by mapping program  */

/*
*	--------------------------
*	write buffet units outputs
*	--------------------------
*/
if(! ADIO_FAST)
{
  if(SITE==HTF)
  {
	if(WRITE_ADIO1)
	{
	      	IO_STATUS1 = adio_write(8,&ADIO_AOP1,&ADIO_DOP1);
	}
  }
  else
  {
	if(WRITE_ADIO1)
	{
	      	IO_STATUS1 = adio_write(ADIO_SLOT1,&ADIO_AOP1,&ADIO_DOP1);
	}
  }

  if(WRITE_ADIO2)
  {
      	IO_STATUS2 = adio_write(ADIO_SLOT2,&ADIO_AOP2,&ADIO_DOP2);
  }

  if(WRITE_ADIO3)
  {
      	IO_STATUS3 = adio_write(ADIO_SLOT3,&ADIO_AOP3,&ADIO_DOP3);
  }
}
/*
* 	Start A/D conversion for all cards ( fast and slow cards )
*/

if(ADIO_FAST)
  adio_qio();
else
  adio_sync();


/*
*
**	timings
*
*/
/*****************************************************
MAXT_30 = MAX ( MAXT_30 , task_table[26] ) ;
MAXT_500 = MAX ( MAXT_500 , task_table[17] ) ;
MAXT_2000 = MAX ( MAXT_2000 , task_table[8] ) ;
******************************************************/

}
/*****************************************************************************

  'Title                MOTION JACK VELOCITY COMPUTING
  'Module_ID            MVELOCITY
  'Entry_point          mvelocity()
  'Documentation
  'Customer             QANTAS
  'Application          compute jack velocity from average position
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec ?????
  'Process              Synchronous process

*****************************************************************************

  'Revision_history

  'References

*/

void mvelocity()
{

static int 	m_select = 0,
		mv_first=1;

static float 	m_inv_count,
		sp22,
		sp0;

static double 	m_avxac1[6];

/*
*  ---------------------------------------------------------------------
** first pass init
*  ---------------------------------------------------------------------
*/

if (mv_first)
{
	m_avxac1[JACK1] = J1XAC;
	m_avxac1[JACK2] = J2XAC;
	m_avxac1[JACK3] = J3XAC;
	m_avxac1[JACK4] = J4XAC;
	m_avxac1[JACK5] = J5XAC;
	m_avxac1[JACK6] = J6XAC;
	mv_first = FALSE;
}			/* end of if(mv_first )	*/

/*
*   --------------------------------------------------------------------
*   compute average position from sum of position during VELAVCOUNT iter
*   prevent 2000 hz band to interrupt and write into variables used here
*   --------------------------------------------------------------------
*/

VELINPROG = TRUE;

m_inv_count = 1./VELAVCOUNT;

J1AVXAC = J1SUMXAC * m_inv_count;
J2AVXAC = J2SUMXAC * m_inv_count;
J3AVXAC = J3SUMXAC * m_inv_count;
J4AVXAC = J4SUMXAC * m_inv_count;
J5AVXAC = J5SUMXAC * m_inv_count;
J6AVXAC = J6SUMXAC * m_inv_count;

	/*
	*   --------------------------------------------------------------------
	*   reset sum of position and iteration count for next computation
	*   --------------------------------------------------------------------
	*/
	J1SUMXAC = 0.0;
	J2SUMXAC = 0.0;
	J3SUMXAC = 0.0;
	J4SUMXAC = 0.0;
	J5SUMXAC = 0.0;
	J6SUMXAC = 0.0;

	VELAVCOUNT = 0.0;

VELINPROG = FALSE;

/*
*   --------------------------------------------------------------------
*   computing actual velocity from average position
*   --------------------------------------------------------------------
*/

sp0 = m_inv_count * 2000.  ;       /* assuming servo runs at 2000 */

	J1VAC = ( J1AVXAC - m_avxac1[JACK1] ) *sp0  ;
	J2VAC = ( J2AVXAC - m_avxac1[JACK2] ) * sp0   ;
	J3VAC = ( J3AVXAC - m_avxac1[JACK3] ) * sp0   ;
	J4VAC = ( J4AVXAC - m_avxac1[JACK4] ) * sp0   ;
	J5VAC = ( J5AVXAC - m_avxac1[JACK5] ) * sp0   ;
	J6VAC = ( J6AVXAC - m_avxac1[JACK6] ) * sp0   ;


/*
*   --------------------------------------------------------------------
*   update previous iteration values of average position
*   --------------------------------------------------------------------
*/

m_avxac1[JACK1] = J1AVXAC;
m_avxac1[JACK2] = J2AVXAC;
m_avxac1[JACK3] = J3AVXAC;
m_avxac1[JACK4] = J4AVXAC;
m_avxac1[JACK5] = J5AVXAC;
m_avxac1[JACK6] = J6AVXAC;


/*
*   --------------------------------------------------------------------
*   computing velocity error
*   --------------------------------------------------------------------
*/

J1VE = J1VAC - J1VR;
J2VE = J2VAC - J2VR;
J3VE = J3VAC - J3VR;
J4VE = J4VAC - J4VR;
J5VE = J5VAC - J5VR;
J6VE = J6VAC - J6VR;

}   /* end of mvelocity */

/****************************************************************************

            +==================================================+
            |                                                  |
            |        Jack model for testing                    |
            |                                                  |
            +==================================================+
*/
void jack()
{
static int 	i,
		m_j1budop,m_j2budop,m_j3budop,m_j4budop,m_j5budop,m_j6budop,
		m_j1budip,m_j2budip,m_j3budip,m_j4budip,m_j5budip,m_j6budip;

static float 	m_j1ic,m_j2ic,m_j3ic,m_j4ic,m_j5ic,m_j6ic, 	/* local current */
		m_j1icp,m_j2icp,m_j3icp,m_j4icp,m_j5icp,m_j6icp,/* previous value ic*/
		m_j1xa=(-1.),m_j2xa=(-1.),m_j3xa=(-1.),     /* local jack pos */
		m_j4xa=(-1.),m_j5xa=(-1.),m_j6xa=(-1.),
		m_j1fa,m_j2fa,m_j3fa,m_j4fa,m_j5fa,m_j6fa,          /* local jack force */
		m_accgain = 1.,
		m_weight=1000.;
/*
*   code start
*/

if ( SITE == CTS ) J1XAOFS = 0.;

J2XAOFS = 0.;
J3XAOFS = 0.;
J4XAOFS = 0.;
J5XAOFS = 0.;
J6XAOFS = 0.;

/*
*     -----------------------------------------------------------------
*       JACK MODEL
*
*	Select between SITE = CTS ( 1 )    Emulation of jack #1-6
*		       SITE = HTF ( 2 )    Emulation of jack #2-6, #1 is tower
*
*     -----------------------------------------------------------------
*/


if ( SITE == CTS ) m_j1budop =   ADIO_DOP1 & 0XF;
m_j2budop = ( ADIO_DOP1 & 0XF0 )  >> 4;
m_j3budop =   ADIO_DOP2 & 0XF;
m_j4budop = ( ADIO_DOP2 & 0XF0 )  >> 4;
m_j5budop =   ADIO_DOP3 & 0XF;
m_j6budop = ( ADIO_DOP3 & 0XF0 )  >> 4;

if ( SITE == CTS ) m_j1ic = ADIO_AOP1[0] * ( 40./32767.);
m_j2ic = ADIO_AOP1[1] * ( 40./32767.);
m_j3ic = ADIO_AOP2[0] * ( 40./32767.);
m_j4ic = ADIO_AOP2[1] * ( 40./32767.);
m_j5ic = ADIO_AOP3[0] * ( 40./32767.);
m_j6ic = ADIO_AOP3[1] * ( 40./32767.);

/*------
* jack 1
*-------*/

if ( SITE == CTS )
{

/* normal mode */

if (  ( m_j1budop==NORMALDOP) || ( m_j1budop==(NORMALDOP|TOGGLEDOP) ) )
{
	m_j1budip = NORMALDIP;

	m_j1xa = m_j1xa + (m_j1ic-J1ICOFS) * YITIM/J1KV;
}

/* standby mode */

if (  ( m_j1budop==STANDBYDOP) || ( m_j1budop==(STANDBYDOP|TOGGLEDOP)) )
{
	m_j1budip = STANDBYDIP;

	m_j1xa = m_j1xa- .0000667777;/*  down in 5 sec @2000Hz */

}

} /* end of if ( SITE == CTS ) */

JZACCEL = (m_j1ic - m_j1icp)*m_accgain/YITIM;

m_j1fa = (JZACCEL *.01) + m_weight;

m_j1icp = m_j1ic;

/*
*	current feedback
*/

J1IAC = J1IC;

/*
*	position limiting
*/

m_j1xa = limit(m_j1xa,-MKJSCALE,MKJSCALE);

/*------
* jack 2
*-------*/


/* normal mode */

if (  ( m_j2budop==NORMALDOP) || ( m_j2budop==(NORMALDOP|TOGGLEDOP) ) )
{
	m_j2budip = NORMALDIP;

	m_j2xa = m_j2xa + (m_j2ic-J2ICOFS) * YITIM/J2KV ;

}

/* standby mode */

if (  ( m_j2budop==STANDBYDOP) || ( m_j2budop==(STANDBYDOP|TOGGLEDOP)) )
{
	m_j2budip = STANDBYDIP;


	m_j2xa = m_j2xa - .0000667777;         /*  down in 5 sec @2000Hz */
}

m_j2fa =  (m_j2ic - m_j2icp)*.01/YITIM + m_weight;

m_j2icp = m_j2ic ;

/*
*	current feedback
*/

J2IAC = J2IC;

/*
*	position limiting
*/

m_j2xa = limit(m_j2xa ,-MKJSCALE,MKJSCALE);


/*------
* jack 3
*-------*/

/* normal mode */

if (  ( m_j3budop==NORMALDOP) || ( m_j3budop==(NORMALDOP|TOGGLEDOP) ) )
{
	m_j3budip = NORMALDIP;

	m_j3xa = m_j3xa + (m_j3ic-J3ICOFS) * YITIM/J3KV;

}

/* standby mode */

if (  ( m_j3budop==STANDBYDOP) || ( m_j3budop==(STANDBYDOP|TOGGLEDOP)) )
{
	m_j3budip = STANDBYDIP;

	m_j3xa = m_j3xa- .0000667777;/*  down in 5 sec @2000Hz */

}

m_j3fa = (m_j3ic - m_j3icp)*.01/YITIM + m_weight;

m_j3icp = m_j3ic;

/*
*	current feedback
*/

J3IAC = J3IC;

/*
*	position limiting
*/

m_j3xa = limit(m_j3xa,-MKJSCALE,MKJSCALE);

/*------
* jack 4
*-------*/


/* normal mode */

if (  ( m_j4budop==NORMALDOP) || ( m_j4budop==(NORMALDOP|TOGGLEDOP) ) )
{
	m_j4budip = NORMALDIP;

	m_j4xa = m_j4xa + (m_j4ic-J4ICOFS) * YITIM/J4KV ;

}

/* standby mode */

if (  ( m_j4budop==STANDBYDOP) || ( m_j4budop==(STANDBYDOP|TOGGLEDOP)) )
{
	m_j4budip = STANDBYDIP;

	m_j4xa = m_j4xa - .0000667777;         /*  down in 5 sec @2000Hz */
}

m_j4fa =  (m_j4ic - m_j4icp)*.01/YITIM + m_weight;

m_j4icp = m_j4ic ;

/*
*	current feedback
*/

J4IAC = J4IC;

/*
*	position limiting
*/

m_j4xa = limit(m_j4xa ,-MKJSCALE,MKJSCALE);


/*------
* jack 5
*-------*/


/* normal mode */

if (  ( m_j5budop==NORMALDOP) || ( m_j5budop==(NORMALDOP|TOGGLEDOP) ) )
{
	m_j5budip = NORMALDIP;

	m_j5xa = m_j5xa + (m_j5ic-J5ICOFS) * YITIM/J5KV;

}

/* standby mode */

if (  ( m_j5budop==STANDBYDOP) || ( m_j5budop==(STANDBYDOP|TOGGLEDOP)) )
{
	m_j5budip = STANDBYDIP;

	m_j5xa = m_j5xa- .0000667777;/*  down in 5 sec @2000Hz */

}

m_j5fa = (m_j5ic - m_j5icp)*.01/YITIM + m_weight;

m_j5icp = m_j5ic;

/*
*	current feedback
*/

J5IAC = J5IC;

/*
*	position limiting
*/

m_j5xa = limit(m_j5xa,-MKJSCALE,MKJSCALE);


/*------
* jack 6
*-------*/


/* normal mode */

if (  ( m_j6budop==NORMALDOP) || ( m_j6budop==(NORMALDOP|TOGGLEDOP) ) )
{
	m_j6budip = NORMALDIP;

	m_j6xa = m_j6xa + (m_j6ic-J6ICOFS) * YITIM/J6KV ;

}

/* standby mode */

if (  ( m_j6budop==STANDBYDOP) || ( m_j6budop==(STANDBYDOP|TOGGLEDOP)) )
{
	m_j6budip = STANDBYDIP;

	m_j6xa = m_j6xa - .0000667777;         /*  down in 5 sec @2000Hz */
}

m_j6fa =  (m_j6ic - m_j6icp)*.01/YITIM + m_weight;

m_j6icp = m_j6ic ;

/*
*	current feedback
*/

J6IAC = J6IC;

/*
*	position limiting
*/

m_j6xa = limit(m_j6xa ,-MKJSCALE,MKJSCALE);



/*
*
** 	send inputs to BU
*
*/

if ( SITE == CTS ) ADIO_DIP1 = m_j1budip | m_j2budip << 8 ;
ADIO_DIP2 = m_j3budip | m_j4budip << 8 ;
ADIO_DIP3 = m_j5budip | m_j6budip << 8 ;

if ( SITE == CTS ) ADIO_AIP1[0] =  m_j1xa * 32767.*MKINVSCALE;
if ( SITE == CTS ) ADIO_AIP1[4] =  m_j2xa * 32767.*MKINVSCALE;
ADIO_AIP2[0] =  m_j3xa * 32767.*MKINVSCALE;
ADIO_AIP2[4] =  m_j4xa * 32767.*MKINVSCALE;
ADIO_AIP3[0] =  m_j5xa * 32767.*MKINVSCALE;
ADIO_AIP3[4] =  m_j6xa * 32767.*MKINVSCALE;

if ( SITE == CTS ) ADIO_AIP1[1] =  m_j1fa * 32767./10000.;
if ( SITE == CTS ) ADIO_AIP1[5] =  m_j2fa * 32767./10000.;
ADIO_AIP2[1] =  m_j3fa * 32767./10000.;
ADIO_AIP2[5] =  m_j4fa * 32767./10000.;
ADIO_AIP3[1] =  m_j5fa * 32767./10000.;
ADIO_AIP3[5] =  m_j6fa * 32767./10000.;

}


 /*****************************************************************************

  'Title                DN1 LOGIC EMULATOR FOR CTS-HTF TESTS
  'Module_ID            DN1LOGIC
  'Entry_point          DN1LOGIC
  'Documentation
  'Customer             QANTAS
  'Application          emulates P/B inputs on the cabinet
  'Author               NORM BLUTEAU
  'Date                 Feb 1991

  'System               MOTION
  'Iteration_rate       0.033 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history

  'References

*/

void dn1logic()
{

/*
C
C      LOCAL VARIABLES
C
*/

static int      i,
		l_first = 1;		/* first pass flag */

static float   m_sp0,
	       m_sp1;



/*--------------------------------
CD	first pass init
----------------------------------
*/

if ( l_first )
{

	L2M_CABSTATE = CAB_ON_NORMAL;

 	l_first = FALSE;
}




/*--------------------------------
CD	cabinet modes
----------------------------------
*/
/*
* 	Normal / test P/B
*/

if( (B_NORMTEST)&&(L2M_CABSTATE==CAB_ON_NORMAL) )
{
	L2M_CABSTATE = CAB_MOT_TEST_ATTITUDE;
	B_NORMTEST = FALSE;

} /* end of if(B_NORMTEST) */

if( (B_NORMTEST)&&(L2M_CABSTATE>=CAB_MOT_TEST_ATTITUDE) )
{
	L2M_CABSTATE = CAB_ON_NORMAL;
	B_NORMTEST = FALSE;

} /* end of if(B_NORMTEST) */

/*
* 	attitude / actuator P/B
*/

if( (B_ATTACT)&&(L2M_CABSTATE==CAB_MOT_TEST_ATTITUDE) )
{
	L2M_CABSTATE = CAB_MOT_TEST_ACTUATOR;
	B_ATTACT = FALSE;

}

if( (B_ATTACT)&&(L2M_CABSTATE==CAB_MOT_TEST_ACTUATOR) )
{
	L2M_CABSTATE = CAB_MOT_TEST_ATTITUDE;
	B_ATTACT = FALSE;

}

/*
* 	COMPUTER MODE FLAG
*/
if( MOTION.mtpmode  )
{
	L2M_CABSTATE = CAB_MOT_TEST_COMPUTER;
}
else
{
	L2M_CABSTATE = CAB_MOT_TEST_ATTITUDE;
}


/*--------------------------------
CD	MOTION ON
----------------------------------
*/

/*--------------------------------
CD	FAILURE LEVELS
----------------------------------
*/

L2M_FAILEVEL = 99;



}		/* end of file JACK.C */

/****************************************************************************
            +==================================================+
            |                                                  |
            |          DN1 motion mapping utility for tuning   |
            |                 AOP5
            +==================================================+
*/
extern float mtselect(int,int);

int map_ena1 = 0,
	   map_ena2 = 0, 	/*enable mapping using mtselect*/
	   map_ena3 = 0, 	/*enable mapping of float using address*/
	   map_ena4 = 0, 	/*enable mapping of int using address*/
	   map_ena5 = 0, 	/*enable mapping of float to host using add */
	   tune_cmd = 0,
	   tune_rsp = 0,
	   tune_pot = 0,
       	   tune_faf =0,
	   *iadd1,	/* POINTER TO ADDRESS OF VARIABLE TO MAP OUT */
	   *iadd2,	/* POINTER TO ADDRESS OF VARIABLE TO MAP OUT */
	   *iadd3,	/* POINTER TO ADDRESS OF VARIABLE TO MAP OUT */
	   *iadd4,	/* POINTER TO ADDRESS OF VARIABLE TO MAP OUT */
     	   *iadd5,	/* POINTER TO ADDRESS OF VARIABLE TO MAP OUT */
	   *iadd6;	/* POINTER TO ADDRESS OF VARIABLE TO MAP OUT */


float *fadd1,
	     *fadd2,
	     *fadd3,
	     *fadd4,
	     *fadd5,
	     *fadd6;

int map_limit=32750,
	   map_sp1,
	   map_sp2,
	   map_sp3,
	   map_sp4,
	   map_sp5,
	   map_sp6,
	   map_sp7,
	   map_sp8,
	   map_sp9;

/*
*
*   code start
*
*/
void MAP()
{

/*
*	CAA TEST : MAP TO PATCH PANEL, USING ADIO#2 AND ADIO#3
*/
if( MTRSLTMODE == MAPOUT )
{
	map_sp1 = mtselect(MAP_KEY1,JACK1) * (32767.*MAPGAIN1 );
	map_sp2 = mtselect(MAP_KEY2,JACK2) * (32767.*MAPGAIN2 );
	map_sp3 = mtselect(MAP_KEY3,JACK3) * (32767.*MAPGAIN3 );
	map_sp4 = mtselect(MAP_KEY4,JACK4) * (32767.*MAPGAIN4 );
	map_sp5 = mtselect(MAP_KEY5,JACK5) * (32767.*MAPGAIN5 );
	map_sp6 = mtselect(MAP_KEY6,JACK6) * (32767.*MAPGAIN6 );
	map_sp7 = mtselect(MAP_KEY7,JACK1) * (32767.*MAPGAIN7 );

	ADIO_AOP2[2] = 	limit(map_sp1, -map_limit, map_limit );
	ADIO_AOP2[3] = 	limit(map_sp2, -map_limit, map_limit );
	ADIO_AOP2[4] = 	limit(map_sp3, -map_limit, map_limit );
	ADIO_AOP2[5] = 	limit(map_sp4, -map_limit, map_limit );
	ADIO_AOP2[6] = 	limit(map_sp5, -map_limit, map_limit );
	ADIO_AOP2[7] = 	limit(map_sp6, -map_limit, map_limit );
	ADIO_AOP3[2] = 	limit(map_sp7, -map_limit, map_limit );
}

if(map_ena1)
{



	if(map_ena3)
	{
	ADIO_AOP1[2] = *fadd1 * (32767.*MAPGAIN1 );
	ADIO_AOP1[3] = *fadd2 * (32767.*MAPGAIN2 );
	ADIO_AOP1[4] = *fadd3 * (32767.*MAPGAIN3 );
	ADIO_AOP1[5] = *fadd4 * (32767.*MAPGAIN4 );
	ADIO_AOP1[6] = *fadd5 * (32767.*MAPGAIN5 );
	ADIO_AOP1[7] = *fadd6 * (32767.*MAPGAIN6 );
	}
	if(map_ena4)
	{
	ADIO_AOP1[2] = *iadd1 * (32767.*MAPGAIN1 );
	ADIO_AOP1[3] = *iadd2 * (32767.*MAPGAIN2 );
	ADIO_AOP1[4] = *iadd3 * (32767.*MAPGAIN3 );
	ADIO_AOP1[5] = *iadd4 * (32767.*MAPGAIN4 );
	ADIO_AOP1[6] = *iadd5 * (32767.*MAPGAIN5 );
	ADIO_AOP1[7] = *iadd6 * (32767.*MAPGAIN6 );
	}
	if(map_ena5)
	{
	MISPRE[0] = *fadd1; 
	MISPRE[1] = *fadd2; 
	MISPRE[2] = *fadd3; 
	MISPRE[3] = *fadd4; 
	MISPRE[4] = *fadd5;
	}

	/*-----------------------------------------------------------*/

	if(map_ena2)
	{
	ADIO_AOP1[6] = mtselect(MTIN_KEY,MTJACK) * (32767.*MAPGAIN1 );
	ADIO_AOP1[7] = mtselect(MTOUT_KEY,MTJACK) * (32767.*MAPGAIN2 );
	}
	/*-----------------------------------------------------------*/
/*
*       limit aop to avoid overflow
*/
	ADIO_AOP1[2] = 	limit(ADIO_AOP1[2], -map_limit, map_limit );
	ADIO_AOP1[3] = 	limit(ADIO_AOP1[3], -map_limit, map_limit );
	ADIO_AOP1[4] = 	limit(ADIO_AOP1[4], -map_limit, map_limit );
	ADIO_AOP1[5] = 	limit(ADIO_AOP1[5], -map_limit, map_limit );
	ADIO_AOP1[6] = 	limit(ADIO_AOP1[6], -map_limit, map_limit );
	ADIO_AOP1[7] = 	limit(ADIO_AOP1[7], -map_limit, map_limit );

} /* END OF if(map_ena1) */


}
