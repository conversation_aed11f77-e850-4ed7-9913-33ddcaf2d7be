/******************************************************************************
C
C                             GENERIC BUNGEE MACRO
C 'Revision_history
C
C  01-JUN-1992    MIKE EKLUND
C        RERELEASED
C        - FADES IN BUNGEE GAIN NOW USING AN EXPONENTIAL INCREASE
C
C  04-MAR-1992    M ZAHN
C        Added bungee force level gains CBUNGEE and FBUNGEE, which multiplied
C        together determine the net force level gain (thus setting either
C        gain to zero disconnects the bungee).  In the corresponding xrf
C        the labels should be selected to represent the location of the
C        bungee (e.g. CECFWDBNG for a forward bungee or CECAFTBNG for an aft 
C        bungee).  The bungee is now disconnected if the gearing calibration 
C        mode is selected on either side.
C        During reconnection, the force levels are faded in in an exponential
C        manner to prevent excessive accelerations.
C
C  25-NOV-1991    RICHARD FEE
C        KBUNG is a gain only on Differential Position, BNGDMP is a gain
C        only on Differential Velocity (previously had been an overall 
C        gain on both terms). 
C
C  16-AUG-1991    RICHARD FEE
C        STANDARD TEXT FILE FOR DFC-FILE-MAKING-UTILITY SET UP
C        ALSO ALL MACROS SITE TESTED - COMPLETE RE-RELEASE.
C
C  22-MAY-1991    RICHARD FEE
C        INITIAL MASTER MACRO
C
C '
C
*******************************************************************************
*/

/*
CC     This macro is designed to model a bungee, torque tube or cable
CC interconnect in which the force level varies linearly with position
CC difference once the breakout force has been exceeded.
C
*/


  if (TRUE)
  {
    static float posn,
                 damp,
                 bnggn = 1.0,
                 bngnl,
                 bngpl,
                 bngnsl,
                 bngpsl;

    if (CCALMOD || FCALMOD || (CBDMODE && FBDMODE))
    {
       bnggn = 0.;
    }
    else
    {
       if (bnggn < BNGINIT)
       {
          bnggn = BNGINIT;
       }
    }
    bnggn = max(0.0,min(CBUNGEE*FBUNGEE,bnggn*(YITIM*BNGLAG+1.0)));
    bngnl = bnggn * BNGNL;
    bngpl = bnggn * BNGPL;
#if defined(BNGNSL) && defined(BNGPSL)
    bngnsl = bnggn * BNGNSL;
    bngpsl = bnggn * BNGPSL;
#endif

    posn = FPOS - CPOS;
    damp = (FVEL - CVEL) * BNGDMP;

    CBUNF = (KBUNG * posn) + damp;

#if defined(BNGNSL) && defined(BNGPSL)
    CBUNF = limit(CBUNF,(bngnl-bngnsl*abs(posn)),(bngpl+bngpsl*abs(posn)));
#else
    CBUNF = limit(CBUNF,bngnl,bngpl);
#endif
    FBUNF = -CBUNF;
  }

#if defined(BNGNSL) && defined(BNGPSL)
#undef  BNGNSL
#undef  BNGPSL
#endif

#undef  CPOS
#undef  FPOS
#undef  CVEL
#undef  FVEL
#undef  BNGDMP
#undef  KBUNG
#undef  BNGNL
#undef  BNGPL
#undef  CBUNF
#undef  FBUNF
#undef  CCALMOD
#undef  FCALMOD
#undef  CBDMODE
#undef  FBDMODE
#undef  BNGINIT
#undef  BNGLAG
#undef  CBUNGEE
#undef  FBUNGEE
