#!  /bin/csh -f
#   $Revision: CAL_CMP - Apply CAL to a calibration file V1.1 (MT) May-91$
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#
if ($#argv < 2) then
   setenv ARG1 ""
   setenv ARG2 ""
else
   setenv ARG1 $argv[1]
   setenv ARG2 $argv[2]
#  echo "%FSE-E-MISSINGARGVS, No arguments for Calibration.."
#  exit
endif
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set CAE_SHIP="`logicals -t CAE_SHIP`"
cd  "$CAE_SHIP"
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_TEMP=$SIMEX_DIR/work/calt_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/call_$FSE_UNIK
#
echo '&$*.cdb' >$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
unalias cal
cal $ARG1 $ARG2
rm $FSE_LIST
echo "You must do a SCALDATA now ..."
exit
