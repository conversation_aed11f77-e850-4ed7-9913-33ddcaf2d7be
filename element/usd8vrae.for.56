C'Title              RAE Bedford Microbursts
C'Module_ID          USD8VRAE
C'Entry_point        RAE
C'Documentation      TBD
C'Application        Computation of RAE Bedford Microburst Wind Vectors
C'Author             Department 24, Flight
C'Date               May 1, 1991
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C     +-----------------------------------------+
C     |                                         |
C     |           R A E   B E D F O R D         |
C     |                                         |
C     |          M I C R O B U R S T S          |
C     |                                         |
C     +-----------------------------------------+
C
C
C  -------------------------------------------------------------------------
C
C       Notes on Turbulence Implementation:
C
C       1) The TURBULENCE parameters FRACG and RDECAY are set at a constant
C          values of 0. and .7, respectively.
C
C       2) The frame time label, FRAMET1, has been set at a constant value
C          of 33 (ms).
C
C     -------------------------------------------------------------------------
C
C'Revision_History
C
C  usd8vrae.for.4 20Dec1991 17:47 usd8 paulv
C       < reorder unconditional goto 2010 >
C
C  usd8vrae.for.3 20Dec1991 17:43 usd8 PAULVE
C       < ADD IDENT LABEL >
C
C'
C
      SUBROUTINE USD8VRAE
C
      IMPLICIT NONE
C
C
C'Ident
C
       CHARACTER*55   REV /
     -  '$Source: usd8vrae.for.4 20Dec1991 17:47 usd8 paulv  $'/
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
CQ    USD8 XRFTEST(*)
C
C  INPUTS
C
CP    USD8
CPI  R  RXMISGPX,  RXMISHDG,  RXMISLAT,  RXMISLON,  RXMISRLE,
CPI  T  TAMBGAIN,  TAMBXOFF,  TAMBYOFF,  TAMCORE,   TAMRAD,    TCMMICRO,
CPI  V  VATURB,    VCSRW,     VCSWDIR,   VESHR,     VH,        VNSHR,
CPI  V  VRAE,      VSNRW,     VSNWDIR,   VVE,       VVSHR,     VWIND,
CPI  V  VXD,       VYD,
CPI  Y  YLUNIFN,
C
C  OUTPUTS
C
CPO  T  TAMBLAT,   TAMBLON,   TCMCAT,    TCMSTORM,  TCMTURLO,
CPO  V  VEMB,      VNMB,      VVMB
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:07:06 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLON(5)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
C$
      REAL*4   
     &  RXMISHDG(5)    !  7 TRUE HDG/MAG DECLINATION(DEG)      [DEG]
     &, TAMBGAIN       ! MICROBURST INTENSITY (0-5)
     &, TAMBXOFF       ! MICROBURST X POS OFFSET             [KM   ]
     &, TAMBYOFF       ! MICROBURST Y POS OFFSET             [KM   ]
     &, VATURB         ! TURBULENCE INTENSITY FOR FLIGHT
     &, VCSRW          ! COS OF RWY TRUE HEADING
     &, VCSWDIR        ! COSINE OF WIND DIRECTION AT A/C
     &, VESHR          ! EAST/WEST COMPONENT OF WIND SHEAR      [kts]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VNSHR          ! NORTH/SOUTH COMPONENT OF WIND SHEAR    [kts]
     &, VSNRW          ! SIN OF RWY TRUE HEADING
     &, VSNWDIR        ! SINE OF WIND DIRECTION AT A/C
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, VVSHR          ! VERTICAL COMPONENT OF WIND SHEAR       [kts]
     &, VWIND          ! WIND SPEED AT A/C                     [ft/s]
     &, VXD            ! X DIST. TO TOUCHDOWN AREA ON R/W        [ft]
     &, VYD            ! Y DIST. TO TOUCHDOWN AREA ON R/W        [ft]
     &, YLUNIFN(8)     ! Random number uniform distribution
C$
      INTEGER*2
     &  RXMISGPX(5)    ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RXMISRLE(5)    !  8 RUNWAY LENGTH (FEET)                [FT]
     &, TAMCORE        ! MICROBURST HEIGHT (0-6000)          [Feet ]
     &, TAMRAD         ! MICROBURST RADIUS OF CORE (0-3000)  [Feet ]
C$
      LOGICAL*1
     &  TCMMICRO       ! ACTIVATE MICROBURST
     &, VRAE           ! USE RAE BEDFORD MICROBURST AND TURBULENCE
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  TAMBLAT        ! MICROBURST LATITUDE                 [Degs ]
     &, TAMBLON        ! MICROBURST LONGITUDE                [Degs ]
     &, VEMB           ! Y-WIND VELOCITY DUE MICROBURST        [ft/s]
     &, VNMB           ! X-WIND VELOCITY DUE MICROBURST        [ft/s]
     &, VVMB           ! Z-WIND VELOCITY DUE MICROBURST        [ft/s]
C$
      LOGICAL*1
     &  TCMCAT         ! CLEAR AIR TURBULENCE
     &, TCMSTORM       ! THUNDERSTORM TURBULENCE (HV)
     &, TCMTURLO       ! LOW LEVEL TURBULENCE
C$
      LOGICAL*1
     &  DUM0000001(1268),DUM0000002(15092),DUM0000003(923)
     &, DUM0000004(708),DUM0000005(924),DUM0000006(40)
     &, DUM0000007(12),DUM0000008(36),DUM0000009(36)
     &, DUM0000010(2692),DUM0000011(55116),DUM0000012(40)
     &, DUM0000013(170),DUM0000014(229027),DUM0000015(6)
     &, DUM0000016(7267),DUM0000017(58)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLUNIFN,DUM0000002,VRAE,DUM0000003,VVE,DUM0000004
     &, VH,DUM0000005,VNSHR,VESHR,VVSHR,DUM0000006,VXD,VYD,DUM0000007
     &, VCSWDIR,VSNWDIR,VWIND,DUM0000008,VEMB,VNMB,VVMB,DUM0000009
     &, VATURB,DUM0000010,VCSRW,VSNRW,DUM0000011,RXMISLAT,RXMISLON
     &, DUM0000012,RXMISHDG,RXMISRLE,DUM0000013,RXMISGPX,DUM0000014
     &, TCMCAT,TCMTURLO,TCMSTORM,DUM0000015,TCMMICRO,DUM0000016
     &, TAMCORE,TAMRAD,DUM0000017,TAMBXOFF,TAMBYOFF,TAMBLAT,TAMBLON
     &, TAMBGAIN  
C------------------------------------------------------------------------------
C
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
      LOGICAL   LCMRTURB           ! RAE Bedford turbulence active
      LOGICAL   LTEST1/.FALSE./    ! Disguise microburst entry option
      LOGICAL   LTEST2/.FALSE./    ! Disguise microburst entry option
      LOGICAL   LNTEST/.TRUE./     ! Set number of gusts per second
      LOGICAL   LPTEST2/.TRUE./    ! Additional limiting of turb. gusts
C
      INTEGER*4 II,LKK,KKK,NKK,MKK,IIK1,IIK2,
     +          IIZ1,IIZ2,IR,JJCOMP,
     +          LLCOU
C
      REAL*4 X,Y,H,X0,Y0,H0,XC,YC,HC,
     +       RAD,VWN0,VWE0,SHRFAC,SHRF9,H0R,HC1,
     +       DV,HI,VDV,VZV,V(350),
     +       XK2FPS,DEGTOR,FPSTKT,VWKT0,CPSIW,
     +       SPSIW,HI1,SGNH,
     +       VXV,VYV,VHV,VXI,VYI,VHI,VDI,VZI,
     +       VWNL0,VWEL0,VWDL,VWNK,VWEK,LVWKLL,LVWKLU,VWKL0,
     +       VORFAC,LGAIN,
     +       LWFACT,LVFACT,LRUNLEN,
     +       LPSIM,LPSIMR,LDVL,LVDI,LVDV
C
      REAL*4 LLUPP/1.7/      ! Fade limits to disguise M.E. (Microburst Entry)
      REAL*4 LLLOW/.7/       ! Fade limits to disguise M.E. (Microburst Entry)
      REAL*4 LFADE1          ! Microburst entry fade
      REAL*4 LINC/.01/       ! M.E. fade step per iteration
      REAL*4 LSTEP/1./       ! M.E. gain
      REAL*4 LP1/.5/         ! RAE low frequency turbulence gust limit
      REAL*4 LP2/1./         ! RAE mid frequency turbulence gust limit
      REAL*4 LP3/2.5/        ! RAE high frequency turbulence gust limit
C
C
      REAL*4   PI/3.141592654 /     ! Pi
      REAL*4   FT_TO_M / 0.3048 /
C
C     -------------------------------------------------------------------------
C       TURBULANCE
C
        REAL*4 RUG,RVG,RWG,HT0,HT1,HT2,HT3,RH01,RH12,RH23,
     1         FH1,FH2,FH3,SIGMA,XMU,FF1,FF2,FF3,FRTIME,
     4         DFF(3,3),DF(3),LSP0,LSP1,
     5         A1,A2,A3,FF(3,3),
     7         V1,V2,V3,R1,R2,R3,P,
     8         VSTEP,DELV3,DELV2,DELV1,LVV1,LVV2,LVV3,
     9         USIGW,VSIGW,WSIGW,FRACG,RDECAY,
     1         USIGF,VSIGF,WSIGF,FRAMET1,USIG,VSIG,WSIG,
     2         VWDK,USIGTMIN,VSIGTMIN,WSIGTMIN,
     3         UTURB,VTURB,WTURB,VWNL,VWEL,PSIWD,LTURB
C
        INTEGER*2 IDC(3,3),MSW(3,3),MDC(3),XYZ,NUM,
     1            IBP1,IBP2,IBP3,
     2         MBP1,MBP2,MBP3,
     3         MRT1,MRT2,MRT3,
     4         NMAX,NGUSTS,LNGUSTS,ICWIND,NG,IFLAG
C
      PARAMETER (XK2FPS=1.689,      ! KTS. TO FT/S
     +           DEGTOR=0.01746,    ! DEG. TO RAD
     +           FPSTKT=0.5921)     ! FT/S TO KTS.
C
      DATA NG/16/
      DATA VWDL/0.0/
      DATA LVFACT/.7/
      DATA LWFACT/1./
C
C  SET UP FRACG,RDECAY
C
      DATA FRACG,RDECAY/0.0,0.7/
C
C
C  DATA FOR TURBULENCE
C  TURB. VALUES FOR NO WINDSHEAR (ROUGH AIR)
C
      DATA USIGF,VSIGF,WSIGF/4.0,4.0,2.0/
      DATA USIGW,VSIGW,WSIGW/0.2,0.2,0.13/
      DATA USIGTMIN,VSIGTMIN,WSIGTMIN/1.0,1.0,5./
      DATA ICWIND/1/
C
C     -------------------------------------------------------------------------
C
C       DATA FOR VORTEX ROUTINE
C       -----------------------
C
C
C   X0,Y0 & H0 ARE THE COORDS OF CENTRE OF VORTEX RING(FT)
C   VPSID & VPHID ARE ANGLES OF ROTATION CLOCKWISE FROM NORTH
C   & STBD DOWN OF VORTEX PLANE(DEG)
C   RAD IS RADIUS OF VORTEX RING(FT)
C   VORFAC IS SCALING OF WIND VELOCITIES
C
C
        DATA IIK1/13/,IIK2/11/,IIZ1/13/,IIZ2/11/
C
       DATA VORFAC/175./
       DATA KKK/0./
       DATA LKK/0./
       DATA SHRFAC/1./
       DATA LVWKLU/50./
       DATA LVWKLL/30./
       DATA FRAMET1/33./
C
      Common/Vort/V
C
      ENTRY RAE
C
CD VJ0010  Calculate microburst size and location.
C
      IF (TCMMICRO) THEN
C
C   MICROBURST LAT,LON
C
        LLCOU = LLCOU + 1
        IF (LLCOU .GE. 30) THEN
          LLCOU = 0
          LRUNLEN = (RXMISRLE(3)-RXMISGPX(3))/6076.115
          TAMBLAT = RXMISLAT(3) + ((-TAMBXOFF - LRUNLEN)*VCSRW
     &             - TAMBYOFF * VSNRW) * (1/60.)
C
          LSP1 = COS(TAMBLAT*DEGTOR)
          IF (LSP1 .LT. .01 ) LSP1 = .01
          TAMBLON = RXMISLON(3) + ((-TAMBXOFF - LRUNLEN)*VSNRW
     &             + TAMBYOFF * VCSRW)*(1/60.)/LSP1
C
        ENDIF
C
C        RAD = AMAX0(TAMRAD,1)
C
        IF (TAMRAD.EQ.0.)TAMRAD = 2500
        RAD = TAMRAD
        IF (RAD .LT. 1.0) RAD = 1.0
C
        H0  = TAMCORE
C
C   CALCULATIONS
C
        X  = VXD*VCSRW-VYD*VSNRW+RXMISGPX(3)*VCSRW
        Y  = VXD*VSNRW+VYD*VCSRW+RXMISGPX(3)*VSNRW
        H  = VH
        X0 = -TAMBXOFF*6076.1*VCSRW - TAMBYOFF*6076.1*VSNRW
        Y0 = -TAMBXOFF*6076.1*VSNRW + TAMBYOFF*6076.1*VCSRW
        H0R= H0/RAD
C
CD VJ0020  Find  coords relative to vortex centre for both vortex and image
C
        XC = (X - X0)/RAD
        YC = (Y - Y0)/RAD
        HC = H/RAD - H0R
        LDVL = DV
        DV = SQRT(XC*XC + YC*YC)
        HI = HC + 2.0*H0R
C
C
CD VJ0030  Return from call if a/c is outside microburst region
C
C Fade out microburst winds if any
C
        IF (DV .GT. 5.) THEN
          VWDK  = VWDL * FPSTKT
          VWNL0 = -VWIND*VCSWDIR-VNSHR*VCSRW+VESHR*VSNRW
          VWEL0 = -VWIND*VSNWDIR-VNSHR*VSNRW-VESHR*VCSRW
          VWDL  = -VVSHR
C
C   SCALING OF WINDS
C
          VWNL = VWNL0*SHRFAC
          VWEL = VWEL0*SHRFAC
          GOTO 2020
        ENDIF
C
CD VJ0040  Find velocities in vortex and image
C
        HC1 = ABS(HC)
        CALL Z9985(IIZ1,IIZ2,V,DV,HC1,KKK,LKK,VZV,VDV)
        SGNH = SIGN(1.0,HC)
        VDV = SGNH*VDV*VORFAC
        VZV = VZV*VORFAC
C
C   FIND VELOCITIES IN VORTEX IMAGE
C
        HI1 = ABS(HI)
        CALL Z9985(IIK1,IIK2,V,DV,HI1,MKK,NKK,VZI,VDI)
        SGNH = SIGN(1.0,HI)
        VDI = SGNH*VDI*VORFAC
        VZI = VZI*VORFAC
C
C OPTIONAL CODE TO REDUCE HEADWIND ON ENTERING SHEAR TO MASK IT BETTER
C
        IF (LTEST1)THEN
          IF(LDVL.GT.DV)THEN
            LFADE1 = AMIN1(LLUPP,AMAX1(LLLOW,LFADE1-LINC))
          ELSE
            LFADE1 = AMIN1(LLUPP,AMAX1(LLLOW,LFADE1+LINC))
          ENDIF
          LVDV = VDV * LFADE1
          LVDI = VDI * LFADE1
        ELSEIF(LTEST2)THEN
          IF (ABS(XC) .GT. 0.001) THEN
            LPSIM = ATAN2(YC, XC)
          ELSEIF (ABS(YC) .GT. 0.001) THEN
            LPSIM = (PI / 2.0) * SIGN(1.0, YC)
          ELSE
            LPSIM = 0.0
          ENDIF
          LPSIMR = LPSIM - (RXMISHDG(3)/57.3)
          IF(LPSIMR .GT.  PI )LPSIMR = LPSIMR - 2*PI
          IF(LPSIMR .LT. -PI )LPSIMR = LPSIMR + 2*PI
          LFADE1 = AMIN1(LLUPP,AMAX1(LLLOW,(1.+LSTEP*COS(LPSIMR))))
          LVDV = VDV * LFADE1
          LVDI = VDI * LFADE1
        ELSE
          LFADE1 = 1.
          LVDV = VDV * LFADE1
          LVDI = VDI * LFADE1
        ENDIF
 
C
C   TRANSPOSE VORTEX VELOCITIES BACK TO EARTH AXES
C
        VXV = 0.0
        IF(DV.NE.0.0)VXV= LVDV*XC/DV
        VYV = 0.0
        IF(DV.NE.0.0)VYV= LVDV*YC/DV
        VHV = -VZV
C
C   TRANSPOSE VORTEX IMAGE VELOCITIES BACK TO EARTH AXES
C
        VXI = 0.0
        IF(DV.NE.0.0)VXI= LVDI*XC/DV
        VYI = 0.0
        IF(DV.NE.0.0)VYI= LVDI*YC/DV
        VHI = -VZI
C
CD VJ0060  Calculate output wind velocities
C
        VWDK = VWDL*FPSTKT  ! (1 iteration late)
C
        VWNL0 = VXV - VXI
        VWEL0 = VYV - VYI
        VWDL = VHV - VHI
C
C   SCALING OF WINDS
C
        VWNL = VWNL0*SHRFAC
        VWEL = VWEL0*SHRFAC
C
 2020   CONTINUE
C
      ELSE
C
        VWDK  = VWDL * FPSTKT
        VWNL0 = -VWIND*VCSWDIR-VNSHR*VCSRW+VESHR*VSNRW
        VWEL0 = -VWIND*VSNWDIR-VNSHR*VSNRW-VESHR*VCSRW
        VWDL  = -VVSHR
C
C   SCALING OF WINDS
C
        VWNL = VWNL0*SHRFAC
        VWEL = VWEL0*SHRFAC
C
      ENDIF
C
CD VJ0080  Include RAE turbulence effects
C
      IF (VRAE)  THEN
        LTURB = VATURB * 3.
        IF (.NOT. LCMRTURB) THEN
          TCMTURLO = .FALSE.
          TCMCAT   = .FALSE.
          TCMSTORM = .FALSE.
          IFLAG = 0
          UTURB = 0.0
          VTURB = 0.0
          WTURB = 0.0
C
C  CHECK ON RANGE
C
          IF (LNTEST)THEN
             NGUSTS=NG
          ELSE
             NGUSTS = NG*(VVE*XK2FPS/200.0)
             IF(NGUSTS.LT.1)NGUSTS = 1
             NMAX = 1000.0/FRAMET1
             IF(NGUSTS.GT.NMAX)NGUSTS = NMAX
          ENDIF
C
          USIG = 0.
          VSIG = 0.
          WSIG = 0.
          VWDL = 0.
C
         ELSE
C
C TURB IS REQUIRED
C
          NGUSTS = NG*(VVE*XK2FPS/200.0)
          IF(NGUSTS.LT.1)NGUSTS = 1
          NMAX = 1000.0/FRAMET1
          IF(NGUSTS.GT.NMAX)NGUSTS = NMAX
          IF (LNTEST)NGUSTS=NG
C
          IF (TCMSTORM .OR. TCMCAT .OR. TCMTURLO)VRAE=.FALSE.
          VWNK    = VWNL * FPSTKT
          VWEK    = VWEL * FPSTKT
          VWKL0   = SQRT(VWNK*VWNK + VWEK*VWEK)
C
          IF (VWNK .EQ. 0.) THEN
            PSIWD = 90.
          ELSE
            PSIWD = ATAN(VWEK/VWNK)
          ENDIF
          IF(PSIWD.LT.0)PSIWD=PSIWD+360.0
          IF(PSIWD.GE.360.0)PSIWD=PSIWD-360.0
C
          CPSIW = COS(PSIWD*DEGTOR)
          SPSIW = SIN(PSIWD*DEGTOR)
C
C SET TURB. R.M.S. FOR DOWNBURST SHEARS ACCORDING TO LOCAL WIND
C    STRENGTH , WITH UPPER AND LOWER LIMITS.
C
          IF(VWKL0 .GT. LVWKLU)THEN
            LSP0 = LVWKLU
          ELSEIF(VWKL0 .GT. LVWKLL)THEN
            LSP0 = VWKL0
          ELSE
            LSP0 = LVWKLL
          ENDIF
C
          USIG = (0.1*LSP0 + 0.2*ABS(VWDK))*(LTURB)
          VSIG = (USIG)*(LTURB)*LVFACT
          WSIG = (0.07*LSP0 + 0.7*ABS(VWDK))*(LTURB)*LWFACT
C
C ENSURE TURB. R.M.S. DOES NOT FALL BELOW A CERTAIN PRESET LEVEL
C
          IF (LTURB.GT..01)THEN
           IF(USIG.LT.USIGTMIN) USIG=USIGTMIN
           IF(VSIG.LT.VSIGTMIN) VSIG=VSIGTMIN
           IF(WSIG.LT.WSIGTMIN) WSIG=WSIGTMIN
          ENDIF
C
C
        ENDIF
C
        H = VH
C
        GOTO 2000
C
C          ROUTINE GUSTS(RUG,RVG,RWG,FRAMET1,H)
C          ---------------------------------
C
2000    CONTINUE        ! ENTRY POINT
C
C*****
C     THIS IS A DRAFT COPY OF SUBROUTINE GUSTS
C*****
C     J.RILEY AND B.N.TOMLINSON
C     1 NOVEMBER 1975
C
C   ********************************************************************
C   ********************************************************************
C   **            COPYRIGHT (C) CONTROLLER , HMSO , LONDON 1984
C   **                        ALL RIGHTS RESERVED
C   **
C   **   CONDITIONS OF USE OF THIS DRAFT COPY :-
C   **
C   **     1. THIS SOFTWARE SHALL NOT BE PASSED TO ANY OTHER PERSONS OR
C   **     ORGANISATION WITHOUT WRITTEN PERMISSION FROM THE R.A.E. .
C   **
C   **     2. THIS SOFTWARE IS BELIEVED TO BE FREE FROM ANY MAJOR DEFECT
C   **     BUT NO WARRANTY IS GIVEN AND NO LIABILITY ACCEPTED FOR THE
C   **     CONSEQUENCES OF ANY DEFECTS .
C   **
C   ********************************************************************
C   ********************************************************************
C*****
C*****
C     THIS SUBROUTINE CALCULATES 3 COMPONENTS OF GUST VELOCITY
C     WITH AN EXPONENTIAL DISTRIBUTION DEFINED IN SUBROUTINE VSTEP
C*****
C     ARGUMENTS
C          RUG,RVG,RWG - GUST VELOCITY COMPONENTS
C          FRAMET1      - FRAMETIME OF SIMULATION IN MILLISECONDS
C          H        - C.G.HEIGHT
C     OTHER INPUT PARAMETERS (LABELLED COMMON 'FRN')
C          FRACG    - GLOBAL INTERMITTENCY PARAMETER, IN RANGE 0.0 TO 1.
C                     INTERMITTENCY INCREASES AS F APPROACHES 1.0
C                     NEVER SET F = 1.0
C          RDECAY   - DECAY RATIO, TYPICALLY 0.7
C          NGUSTS   - NUMBER OF GUSTS PER SECOND
C  GUST RAMP LENGTHS ARE DETERMINED BY NGUSTS AND SPEED V
C      SHORTEST RAMP IS
C                       LSHORT = V/NGUSTS
C      FOR NGUSTS = 4 AND V = 200 FT/SEC, LSHORT = 50FT
C      LSHORT SHOULD BE KEPT FIXED BY VARYING NGUSTS IN PROPORTION TO SP
C*****
C     USES ROUTINES RANDU, VSTEP AND FDC
C     AND SEEDVAL
C*****
C*****
C     INITIALISE ONLY ON FIRST ENTRY
C*****
      IF(LNGUSTS.NE.NGUSTS)THEN
        IFLAG = 0
        LNGUSTS = NGUSTS
      ENDIF
      IF(IFLAG.NE.0) GO TO 2999
C  REDUCTION IN W COMPONENT STARTS AT HT0 = 2500 FT
C     EACH CONSTITUENT IS ELIMINATED IN TURN
      IFLAG=1
      HT0= 2500.0
      HT1=  800.0
      HT2=  200.0
      HT3=   50.0
      RH01 = 1.0/(HT0 - HT1)
      RH12 = 1.0/(HT1 - HT2)
      RH23 = 1.0/(HT2 - HT3)
      FH1=1.0
      FH2=1.0
      FH3=1.0
C*****
C     PARAMETERS FOR AMPLITUDE DENSITY AND SPECTRAL DENSITY
C*****
      SIGMA=0.9
      XMU=SQRT(2*(1.-FRACG))/SIGMA
C*****
C     SET LOCAL INTERMITTENCY
C*****
      FF3 = 1.0-FRACG
      FF2 = FF3/(SQRT(2.0))
      FF1 = FF3/2.0
C*****
C     INTERVALS OF FREQUENCY BANDS
C*****
      FRTIME = FRAMET1/1000.0
      MBP1=16/(FRTIME*NGUSTS)
      MBP2=4/(FRTIME*NGUSTS)
      MBP3=1/(FRTIME*NGUSTS)
C*****
C     INTERVALS FOR D-C SIGNAL
C*****
      MDC(1)=240/(FRTIME*NGUSTS)
      MDC(2)=60/(FRTIME*NGUSTS)
      MDC(3)=15/(FRTIME*NGUSTS)
C*****
C     INTERVALS FOR D-C RISETIME
C*****
      MRT1=80/(FRTIME*NGUSTS)
      MRT2=20/(FRTIME*NGUSTS)
      MRT3=5/(FRTIME*NGUSTS)
C*****
C     RISE INCREMENTS (-1 TO +1)
C*****
      DF(1)=2./MRT1
      DF(2)=2./MRT2
      DF(3)=2./MRT3
C
C
        DO NUM=1,3
        DO XYZ=1,3
C
        DFF(XYZ,NUM) = DF(NUM)
C
C****
C     DECORRELATOR OUTPUTS
C****
C
        FF(XYZ,NUM) = 0.
C
C*****
C     COUNTERS FOR DECORRELATOR OUTPUTS
C*****
C
        IDC(XYZ,NUM) =0
C
C*****
C     COUNTERS FOR SWITCHING DECORRELATOR OUTPUTS
C*****
C
        MSW(XYZ,NUM) = 0
C
        ENDDO
        ENDDO
C
C
C*****
C     RELATIVE AMPLITUDES FOR FREQUENCY FIT
C*****
      A1=1.0
      A2=A1*(FLOAT(MBP2)/FLOAT(MBP1))**.333333
      A3=A1*(FLOAT(MBP3)/FLOAT(MBP1))**.333333
C
C*****
C     COUNTERS FOR RANDOM-STEP GENERATION
C*****
      IBP1=0
      IBP2=0
      IBP3=0
C*****
C     BPF1,2,3 OUTPUTS
C*****
      V1=0.
      V2=0.
      V3=0.
C*****
C     FRACTIONAL POWERS OF RDECAY FOR INTERPOLATION
C*****
      R3=RDECAY**(1./MBP3)
      R2=RDECAY**(1./MBP2)
      R1=RDECAY**(1./MBP1)
C*****
 2999 CONTINUE
C*****
C     SIMULATION BEGINS
C*****
C*****
C     HIGH FREQUENCY CONSTITUENT. GUST GENERATED EVERY MBP3 STEPS
C*****
      IBP3=IBP3+1-(IBP3/MBP3)*MBP3
      IF(IBP3.NE.1)GOTO2300
        P=YLUNIFN(1)
        IR=P
        P=ABS(P-IR)
      DELV3=VSTEP(P,FF3,XMU)/MBP3
      LVV3=V3
 2300 V3=LVV3*R3**IBP3+DELV3*IBP3
C
C  Limit V3 to stop occaisional large bumps from being generated.
C
      IF (LPTEST2)THEN
        IF (V3 .GT. LP3)V3=LP3
        IF (V3 .LT. -LP3)V3=-LP3
      ENDIF
C*****
C     MID FREQUENCY CONSTITUENT. GUST GENERATED EVERY MBP2 STEPS
C*****
      IBP2=IBP2+1-(IBP2/MBP2)*MBP2
      IF(IBP2.NE.1)GOTO2200
        P=YLUNIFN(2)
        IR=P
        P=ABS(P-IR)
      DELV2=VSTEP(P,FF2,XMU)/MBP2
      LVV2=V2
 2200 V2=LVV2*R2**IBP2+DELV2*IBP2
C
C  Limit V2 to stop occaisional large bumps from being generated.
C
      IF (LPTEST2)THEN
        IF (V2 .GT. LP2)V2=LP2
        IF (V2 .LT. -LP2)V2=-LP2
      ENDIF
C*****
C     LOW FREQUENCY CONSTITUENT. GUST GENERATED EVERY MBP1 STEPS
C*****
      IBP1=IBP1+1-(IBP1/MBP1)*MBP1
      IF(IBP1.NE.1)GOTO 2100
        P=YLUNIFN(3)
        IR=P
        P=ABS(P-IR)
      DELV1=VSTEP(P,FF1,XMU)/MBP1
      LVV1=V1
 2100 V1=LVV1*R1**IBP1+DELV1*IBP1
C
C  Limit V1 to stop occaisional large bumps from being generated.
C
      IF (LPTEST2)THEN
        IF (V1 .GT. LP1)V1=LP1
        IF (V1 .LT. -LP1)V1=-LP1
      ENDIF
C*****
C     D-C STEPS
C*****
C
        DO NUM = 1,3
        DO XYZ = 1,3
C
C*****
C     SUBROUTINE FDC
C*****
C     J.RILEY AND B.N.TOMLINSON
C     1 NOVEMBER 1975
C*****
C*****
C     RANDOM SWITCHING FUNCTION, SWITCHING F3 RANDOMLY BETWEEN
C     -1 AND +1 AT A FREQUENCY CONTROLLED BY MDC(NUM) AND WITH
C     A RISE TIME CONTROLLED BY DF3
C*****
C*****
C     MODULO MDC(NUM) COUNTER. SWITCH DECISION OCCURS AT IDC(XYZ,NUM)=1
C*****
      IDC(XYZ,NUM)=IDC(XYZ,NUM)+1-(IDC(XYZ,NUM)/
     1             MDC(NUM))*MDC(NUM)
      IF(IDC(XYZ,NUM).NE.1)GO TO 2301
        P=YLUNIFN(XYZ+(3*NUM))
        IR=P
        P=ABS(P-IR)
C*****
C     SWITCH IF RANDOM NUMBER GREATER THAN 0.5
C*****
      IF(P.GT..5)GO TO 2302
C*****
C     COUNT NUMBER OF NO SWITCH DECISIONS. IF MORE THAN TWO,FORCE A SWIT
C*****
      MSW(XYZ,NUM)=MSW(XYZ,NUM)+1
      IF(MSW(XYZ,NUM).NE.3)GO TO 2301
 2302 MSW(XYZ,NUM)=0
      DFF(XYZ,NUM)=-DFF(XYZ,NUM)
C*****
C     INCREMENT BY DFF() AND LIMIT TO -1 OR +1
C*****
 2301 FF(XYZ,NUM)=FF(XYZ,NUM)+DFF(XYZ,NUM)
      IF(FF(XYZ,NUM).GT.1.)FF(XYZ,NUM)=1.
      IF(FF(XYZ,NUM).LT.-1.)FF(XYZ,NUM)=-1.
C
        ENDDO
        ENDDO
C
C  CALCULATE HEIGHT FUNCTIONS
      IF(H.GE.HT0)GO TO 2900
      FH1 = (H - HT1)*RH01
      IF(H.GE.HT1)GO TO 2222
      FH1 = 0.0
      FH2 = (H - HT2)*RH12
      IF(H.GE.HT2)GO TO 2222
      FH1=0.0
      FH2=0.0
      FH3 = (H - HT3)*RH23
      IF(H.GE.HT3)GO TO 2222
      FH1=0.0
      FH2=0.0
      FH3=0.0
      GOTO 2222
 2900 CONTINUE
      FH1=1.0
      FH2=1.0
      FH3=1.0
 2222 CONTINUE
C*****
C     COMBINING TO GIVE OUTPUT
C*****
      RUG = A1*FF(1,1)*V1 + A2*FF(1,2)*V2 + A3*FF(1,3)*V3
      RVG = A1*FF(2,1)*V1 + A2*FF(2,2)*V2 + A3*FF(2,3)*V3
      RWG = A1*FF(3,1)*V1*FH1 + A2*FF(3,2)*V2*FH2 + A3*FF(3,3)*V3*FH3
C
      GOTO 2010      !RETURN FROM GUSTS
C
2010    CONTINUE
C
        UTURB = USIG*RUG
        VTURB = VSIG*RVG
        WTURB = WSIG*RWG
C
        IF (TCMMICRO .AND. (DV .LT. 5.)) THEN
          LGAIN = .4 * TAMBGAIN
          VNMB = VWNL*LGAIN - (UTURB*CPSIW - VTURB*SPSIW)
          VEMB = VWEL*LGAIN - (VTURB*CPSIW + UTURB*SPSIW)
          VVMB = VWDL*LGAIN - WTURB
        ELSE
          VNMB = - (UTURB*CPSIW - VTURB*SPSIW)
          VEMB = - (VTURB*CPSIW + UTURB*SPSIW)
          VVMB = - WTURB
        ENDIF
C
      ELSE
C
        VNMB=VWNL
        VEMB=VWEL
        VVMB=VWDL
C
      ENDIF
C
      LCMRTURB = VRAE
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00263 VJ0010  Calculate microburst size and location.
C$ 00300 VJ0020  Find  coords relative to vortex centre for both vortex and imag
C$ 00310 VJ0030  Return from call if a/c is outside microburst region
C$ 00327 VJ0040  Find velocities in vortex and image
C$ 00390 VJ0060  Calculate output wind velocities
C$ 00419 VJ0080  Include RAE turbulence effects
C
C
        SUBROUTINE Z9985(M,N,R,D,H,Col,Row,RVZ,RVD)
C
C
C Subroutine to be use with RAE Beford's windshear
C This routine is not general , it is tailor for the VORTEX routine only.
C As it is only used in VORTEX it will be quicker this way.
C
C Purpose
C -------
C To linearly interpolate for 2 dependant variable from 2 independant
C variables
C
C Arguments
C ---------
C   M   =   Dummy argument to make compatable with RAE routine
C   N   =   Dummy argument to make compatable with RAE routine
C   R   =   Dummy array argument to make compatable with RAE routine
C   D   =   Column independant variable
C   H   =   Row independant variable
C Col   =   Column from last times call
C Row   =   Row from last times call
C RVZ   =   First dependant variable
C RVD   =   Second dependant variable
C
C
        Common/Vort/V(350)
        Integer*2       Col,Row
        Dimension       R(*)
        Dimension       DV(13),HV(11),VV(13,22),
     &                  RVZD(13,11),VDD(13,11)
C
C Equivalence arrays to the data structure of data array V
        Equivalence     (V,DV),(V(1+13),HV),(V(1+13+11),RVZD),
     &                  (V(1+13+11+(13*11)),VDD),(VV,RVZD)
C If first time set Row & Column to 1
        If (Row .eq. 0)Then
           Col = 1
           Row = 1
        End If
C
C Find top left hand corner of box in which point lies.
C Example:
C                       D = 0.3 Columns
C                (1)     (2)  |  (3)     (4)     (5)
C                0.0     0.2  |  0.4     0.6     0.8   etc
C    R  (1) 0.0           |   |
C    O                    |
C    W                    |
C    S  (2) 0.2           |
C                 Top left|
C                   (2,3) |
C       (3) 0.4-----------|--
C          ______
C H = 0.5
C       (4) 0.6
C
C
C       (5) 0.8
C        etc
C
C                            Process columns first.
C
C Check for direction to search in
C
        If (D .ge. DV(Col))Then
C Set up DO loop to search up for B match point
              Do K=Col,13
                 If(D .lt. DV(K))Then
C Found point in array
                   Col = K - 1
C Go find Row
                   Goto 10
                 End If
              End Do
C Above top limit
              Col = 13
        Else
C
C Set up DO loop to search down for B match point
              Do K=Col,1,-1
                 If(D .ge. DV(K))Then
C Found point in array
                     Col = K
C Go find ROW
                     Go to 10
                 End If
              End Do
C Must be below smallest value
              Col = 1
        End If
C
C                            Search for Row no.
C
C Check for direction to search in
C
10      If (H .ge. HV(Row))Then
C Set up DO loop to search up for B match point
              Do K=Row,11
                 If(H .lt. HV(K))Then
C Found point in array
                   Row = K - 1
C Go interpolate data
                   Goto 20
                 End IF
              End Do
C Above top limit
              Row = 11
        Else
C
C Set up DO loop to search down for B match point
              Do K=Row,1,-1
                 If(H .ge. HV(K))Then
C Found point in array
                     Row = K
C Go interpolate data
                     Goto 20
                 End If
              End Do
C Must be below smallest value
              Row = 1
        End If
C                 Here to interpolated data
C
C  VV matrix for vertical component & plus offset for direction component
C  ----------------------------------------------------------------------
C Calculate multiplication factor for columns
C
20      If (Col .lt. 13 .and. D .ge. DV(1) )Then
            Delta_D = DV(Col+1) - DV(Col)
            Factor_D = ( D - DV(Col) )/Delta_D
        Else
C Must be at limit of matrix columns
            Factor_D = 0
        End If
C Calculate multiplication factor for Rows
C
        If (Row .lt. 11 .and. H. ge. HV(1) )Then
            Delta_H = HV(Row+1) - HV(Row)
            Factor_H = ( H - HV(Row) )/Delta_H
        Else
C Must be at limit of matrix rows
            Factor_H = 0
        End If
C
C Set up DO loop for both Vertical & Directional component calculation
        Do K=0,11,11
C
C Interpolate between top left and top right corners of box
        D_row_0 = (( VV(Col+1,Row+K) - VV(Col,Row+K))*
     &          Factor_D) + VV(Col,Row+K)
C
C Interpolate between bottom left and bottom right corners of box
        D_row_1 = (( VV(Col+1,Row+1+K) - VV(Col,Row+1+K))*
     &          Factor_D) + VV(Col,Row+1+K)
C
C Interpolate between both interpolated points & transfer output
         If (K .eq. 0) Then
          RVZ = ( (D_row_1 - D_row_0) * Factor_H) + D_row_0
         Else
          RVD = ( (D_row_1 - D_row_0) * Factor_H) + D_row_0
         End If
        End Do
        Return
        End
C
C
C
C
      FUNCTION VSTEP(P,FF,XMU)
C*****
C     FUNCTION VSTEP
C*****
C     J.RILEY AND B.N.TOMLINSON
C     1 NOVEMBER 1975
C*****
C*****
C     CALCULATES GUST AMPLITUDE VSTEP FOR RANDOM NUMBER P
C     ACCORDING TO SPECIFIED EXPONENTIAL DISTRIBUTION
C*****
C*****
C     A NON-ZERO GUST IS PRODUCED ON (1-FF)*100 PERCENT OF OCCASIONS
C*****
      EPS=(1.-FF)*0.5
      DP=P-.5
      IF(DP.GT.EPS)GOTO1
      EPSM=-EPS
      IF(DP.LT.EPSM)GOTO2
      VSTEP=0.
      RETURN
    1 SIGN=-1.0
      PP=1.-P
      GOTO3
    2 SIGN=1.
      PP=P
C*****
C     LIMIT ARGUMENT SO THAT LOG DOES NOT BLOW UP
C*****
    3 X = 2.0*PP/FF
      IF(X.LT.1.0E-10)X=1.0E-10
      VSTEP = SIGN*ALOG(X)/XMU
      RETURN
      END
C
      Block Data INIT_V
C
C -- This subroutine will initialize the array V
C
      Real*4
     &          V (350)        !Vortex Data
      Integer*4 II             !Array index
 
C
      Common /VORT/ V
C
      Data
     &    (V(II),II=1,310)
     &                 /   0.000,   0.200,   0.400,   0.600,   0.800,
     &   0.900,   1.000,   1.100,   1.200,   1.600,   2.000,   4.000,
     &   50000,   0.000,   0.200,   0.400,   0.600,   0.800,   1.000,
     &   1.200,   1.600,   2.000,   4.000,   50000,  -0.393,  -0.399,
     &  -0.418,  -0.447,  -0.445,  -0.348,  -0.110,   0.051,   0.095,
     &   0.056,   0.029,   0.000,   0.000,  -0.373,  -0.377,  -0.387,
     &  -0.396,  -0.362,  -0.296,  -0.217,   0.018,   0.060,   0.050,
     &   0.027,   0.000,   0.000,  -0.320,  -0.320,  -0.318,  -0.303,
     &  -0.256,  -0.219,  -0.186,  -0.044,   0.004,   0.035,   0.023,
     &   0.000,   0.000,  -0.257,  -0.254,  -0.244,  -0.223,  -0.187,
     &  -0.165,  -0.147,  -0.071,  -0.030,   0.018,   0.017,   0.000,
     &   0.000,  -0.198,  -0.194,  -0.184,  -0.166,  -0.141,  -0.128,
     &  -0.117,  -0.073,  -0.044,   0.004,   0.011,   0.000,   0.000,
     &  -0.149,  -0.147,  -0.138,  -0.125,  -0.108,  -0.100,  -0.093,
     &  -0.067,  -0.047,  -0.005,   0.006,   0.000,   0.000,  -0.113,
     &  -0.111,  -0.105,  -0.095,  -0.085,  -0.080,  -0.075,  -0.059,
     &  -0.045,  -0.011,   0.001,   0.000,   0.000,  -0.066,  -0.065,
     &  -0.062,  -0.058,  -0.054,  -0.052,  -0.050,  -0.043,  -0.036,
     &  -0.015,  -0.005,   0.000,   0.000,  -0.040,  -0.040,  -0.039,
     &  -0.037,  -0.036,  -0.035,  -0.034,  -0.030,  -0.027,  -0.015,
     &  -0.007,   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,
     &   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,
     &   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,
     &   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,
     &   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,
     &   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,
     &   0.000,  -0.021,  -0.047,  -0.088,  -0.152,  -0.180,  -0.179,
     &  -0.144,  -0.102,  -0.024,  -0.008,   0.000,   0.000,   0.000,
     &  -0.031,  -0.067,  -0.110,  -0.152,  -0.162,  -0.157,  -0.139,
     &  -0.114,  -0.039,  -0.015,   0.000,   0.000,   0.000,  -0.032,
     &  -0.064,  -0.096,  -0.118,  -0.121,  -0.118,  -0.108,  -0.095,
     &  -0.044,  -0.019,   0.000,   0.000,   0.000,  -0.027,  -0.053,
     &  -0.074,  -0.087,  -0.089,  -0.087,  -0.082,  -0.075,  -0.042,
     &  -0.021,   0.000,   0.000,   0.000,  -0.021,  -0.040,  -0.055,
     &  -0.064,  -0.065,  -0.064,  -0.062,  -0.058,  -0.038,  -0.021,
     &   0.000,   0.000,   0.000,  -0.016,  -0.029,  -0.040,  -0.047,
     &  -0.048,  -0.048,  -0.047,  -0.045,  -0.032,  -0.020,   0.000,
     &   0.000,   0.000,  -0.008,  -0.016,  -0.022,  -0.026,  -0.027,
     &  -0.027,  -0.027,  -0.027,  -0.022,  -0.016,   0.000,   0.000,
     &   0.000,  -0.005,  -0.009,  -0.012,  -0.015,  -0.016,  -0.016,
     &  -0.017,  -0.017,  -0.015,  -0.012,   0.000,   0.000,   0.000,
     &   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,
     &   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,
     &   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,   0.000,
     &   0.000,   0.000,   0.000,   0.000/
C
      END
