/******************************************************************************
C                                                                              
C 'Title                                                                      
C 'Module_ID                                                                  
C 'Entry_point                                                                 
C 'Customer                                                                    
C 'Application                                                                 
C 'Author                                                                      
C 'Date                                                                        
C                                                                              
C 'System               FLIGHT CONTROLS                                        
C 'Iteration_rate                                                              
C 'Process              Synchronous process                                    
C                                                                              
C 'Include files                                                               
C                                                                              
C 'Subroutines called                                                          
C                                                                              
C 'References                                                                  
C                                                                              
C    1)                                                                        
C                                                                              
C'Revision_history
C
C                                                                              
*******************************************************************************
*/                                                                             
                                                                               
#include <cf_site.h>      
#include "cf_def.h"
#include <adio.h>         
#include <math.h>         
#include <servocal.h>     
#include <fspring.h>
#include "usd8crxrf.ext"  
                          
/*                                                                             
C  ---------------                                                             
C  Local Variables                                                             
C  ---------------                                                             
*/                                                                             
                                                                               
camid()                                                                        
{                                                                              
                                                                               
static  int      c_first = TRUE,  /* first pass flag                      */   
                 ca_cplot=1,
                 ca_fplot=1;
                                                                               
                                                                               
/*                                                                             
C -----------------------------------------------------------------------------
CD CAM010 FIRST PASS                                                           
C -----------------------------------------------------------------------------
C                                                                              
CR Not Applicable                                                              
C                                                                              
*/                                                                             
                                                                               
  if (c_first)                                                                 
  { 
    c_first    =  FALSE;                                                   
  }                                                                        
     
/*
C    ---------------------------
C    Anolog Output 
C    ---------------------------
*/
     
     if (CACPLOT==0)
       {
       if (CACPLOT!=ca_cplot) CACSCALE = 100;
       ADIO_AOP[1] = CACXP*(32767/CACSCALE); 
     }

     if (CACPLOT==1)
       {
       if (CACPLOT!=ca_cplot) CACSCALE = 100;
       ADIO_AOP[1] = CACFPOS*(32767/CACSCALE); 
     }
     if (CACPLOT==2)
       {
       if (CACPLOT!=ca_cplot) CACSCALE = 100;
       ADIO_AOP[1] = CACDPOS*(32767/CACSCALE); 
     }
     if (CACPLOT==3)
       {
       if (CACPLOT!=ca_cplot) CACSCALE = 100;
       ADIO_AOP[1] = CACQPOS*(32767/CACSCALE); 
     }
     
     if (CACPLOT==4)
       {
       if (CACPLOT!=ca_cplot) CACSCALE = 30;
       ADIO_AOP[1] = CACSPOS*(32767/CACSCALE); 
     }
     
     if (CACPLOT==5)
       {
       if (CACPLOT!=ca_cplot) CACSCALE = 5;
       ADIO_AOP[1] = CACPE*(32767/CACSCALE); 
     }
     
     if (CACPLOT==6)
       {
       if (CACPLOT!=ca_cplot) CACSCALE = 100;
       ADIO_AOP[1] = CACAFOR*(32767/CACSCALE); 
     }

     ca_cplot = CACPLOT;


     if (CAFPLOT==0)
       {
       if (CAFPLOT!=ca_fplot) CAFSCALE = 100;
       ADIO_AOP[5] = CAFXP*(32767/CAFSCALE); 
     }
     
     if (CAFPLOT==1)
       {
       if (CAFPLOT!=ca_fplot) CAFSCALE = 100;
       ADIO_AOP[5] = CAFFPOS*(32767/CAFSCALE); 
     }
     
     if (CAFPLOT==2)
       {
       if (CAFPLOT!=ca_fplot) CAFSCALE = 100;
       ADIO_AOP[5] = CAFDPOS*(32767/CAFSCALE); 
     }
     
     if (CAFPLOT==3)
       {
       if (CAFPLOT!=ca_fplot) CAFSCALE = 30;
       ADIO_AOP[5] = CAFQPOS*(32767/CAFSCALE); 
     }
     
     if (CAFPLOT==4)
       {
       if (CAFPLOT!=ca_fplot) CAFSCALE = 30;
       ADIO_AOP[5] = CAFSPOS*(32767/CAFSCALE); 
     }
     
     if (CAFPLOT==5)
       {
       if (CAFPLOT!=ca_fplot) CAFSCALE = 5;
       ADIO_AOP[5] = CAFPE*(32767/CAFSCALE); 
     }
     
     if (CAFPLOT==6)
       {
       if (CAFPLOT!=ca_fplot) CAFSCALE = 100;
       ADIO_AOP[5] = CAFAFOR*(32767/CAFSCALE); 
     }

     ca_fplot = CAFPLOT;
}
