C'Title                 DASH-8 Engine Controls
C'Module ID             USD8EL ( ENGEL )
C'Model Report #
C'Customer              USAir
C'Application           Simulation of the DASH-8 Engine Control System
C'Author                <PERSON><PERSON><PERSON><PERSON>oges
C'Date                  17 May 1988
C
C'System                Engines
C'Itrn_rate             133 msec
C
C
C'References
C
C   [1]  BOEING CANADA DE HAVILLAND DIVISION, DASH-8 MAINTENANCE
C        MANUAL, VOLUME 7 & 8
C
C   [2]  BOEING CANADA DE HAVILLAND DIVISION, DASH-8 MAINTENANCE
C        MANUAL, SER 300, VOLUME 7 & 8
C
C   [3]  DIR #AA2RL2-71867-78
C
C   [4]  DE HAVILLAND DASH 8 WIRING MANUAL
C
C   [5]  Dash8 Service Bulletin
C        \\caefil01\dept_f3\Engines\F5_Engines\Projects\Dash-8-100-300\Data\Beta
C
C   [6]  Beta lockout Review
C         \\caefil01\dept_f3\Engines\F5_Engines\Projects\Dash-8-100-300\Data\Bet
C
C   [7] DIR AA2RL2-71867-78
C
C'Revision_History
C
C  usd8el.for.5 25Mar2006 02:05 usd8 Tom
C       < Made Uncommanded Feather malfunction part of NP underspeed canx >
C
C  usd8el.for.4  8Mar1994 02:18 usd8 TDM
C       < COA S81-2-067 AutoFX TEST/PWR Uptrim check correct operation >
C
C  usd8el.for.3  3Sep1993 03:39 usd8 W. Pin
C       < Use correct label for PLA < FLT IDLE +12 to fix intermittent T/O
C         warn horn on landing. >
C
C  usd8el.for.2  9Aug1993 21:17 usd8 S.GOULD
C       < Changed engine throttle switched used for T/O Warning Horn to
C         make it make sense and consistent with -100. >
C
C  usd8el.for.1  8Aug1993 07:54 usd8 S.GOULD
C       < Added code required for T/O warning horn as per attachements
C         from Montreal >
C
C* USD8EL.FOR;001   17Feb1993 09:15 USD8 LD
C    A/F sequence is latched when initiated until the
C    SELECT sw is toggle.
C
C  usd8el.for.13  4Dec1992 17:41 usd8 LD
C       < Implement 300 Nh limit for overspeed >
C
C  usd8el.for.12  2Dec1992 07:17 usd8 LD
C       < Change the relight enveloppe for 300 serie >
C
C  usd8el.for.11 30Nov1992 23:14 usd8 LD
C       < Relays er4k13 and 14 was not reset with A/F switch @ off >
C
C  usd8el.for.10 30Nov1992 20:14 usd8 LD
C       < Adjust the 2 sec timer for select lt on start >
C
C  usd8el.for.9 29Nov1992 23:58 usd8 LD
C       < Change setting of NH for ECU ON and put A/F prop cb >
C
C  usd8el.for.8 29Nov1992 02:36 usd8 LD
C       < Change logic for ECU ON & MANUAL lt on 300 >
C
C  usd8el.for.7 26Nov1992 21:41 usd8
C       <   >
C
C  usd8el.for.6 26Nov1992 21:16 usd8 LD
C       < Redefine S/F microsw. for 300 serie >
C
C  usd8el.for.5 26Nov1992 00:29 usd8 LD
C       < Put assignment for ITT warning light. >
C
C  usd8el.for.4 23Jun1992 07:22 usd8 LD
C       < Uptrim light come on faster during test. >
C
C  usd8el.for.3 23Jun1992 06:20 usd8 LD
C       < Mistake in propeller A/F test switch logic. >
C
C  usd8el.for.2 23Jun1992 05:31 usd8 LD
C       < Add ground flag in quiet taxi mode. >
C
C  usd8el.for.1 22Jun1992 16:19 usd8 Set the
C       < Set PLA sw #6 & #7 to 1. as customer request. >
C
C   #(080) 13-May-92 LD
C         Change setting of ARM light as customer request.
C
C   #(068) 27-Apr-92
C
C
C  usd8el.for.7  8Mar1992 22:40 usd8 LD
C       < Ground relay was not correct on propeller timer. >
C
C  usd8el.for.6  8Mar1992 21:48 usd8 LD
C       < Coded ITT overtemperature. >
C
C  usd8el.for.5  8Feb1992 12:00 usd8 LD
C       < Change logic for NH overspeed test. >
C
C  usd8el.for.4  8Feb1992 10:31 usd8 LD
C       < Change C140 for Feather signal. >
C
C  usd8el.for.3 29Jan1992 09:13 usd8 LD
C       < Start sw solenoid was not delatching. >
C
C  usd8el.for.2 29Jan1992 08:20 usd8 LD
C       < Introduce calculation of IDESNORM for S-300. >
C
C  usd8el.for.1 29Jan1992 07:55 usd8 LD
C       < Change setting of ESCLMN switch for CLA. >
C
C   #(110) 16-Jan-92 JD
C         MODIFIED CL MICROSW FOR QUIET TAXI
C
C   #(097) 10-Jan-92 JDELAGE
C         MODIFIED TORQUE MOTOR INHIBIT SIGNAL
C
C
C      [1] DASH 8, Wiring Diagrams Manual, Series 100, Ch. 61 and
C           73 to 80, Volume 4, Boeing Canada de Havilland Division,
C           October 3, 1988.
C
C       [2] DASH 8, Wiring Diagrams Manual, Series 300, Ch. 61 and
C           73 to 80, Boeing Canada de Havilland Division,
C           May 22, 1990.
C
C
C'Subroutine_Name
C
      SUBROUTINE USD8EL
C
C
      IMPLICIT NONE
C
C
C'Purpose
C
C     The Engine Controls module involves the implementation
C   of these following engine control systems :
C
C    - Main Engine Control
C        - Throttle Control Microswitch Unit
C        - HP Fuel Shut Off Valve
C        - Ground Idle Gate Control
C
C    - Engine Fuel Distribution and Control System
C        - Engine Fuel Supply System
C        - Clogging of HP Fuel Pump Outlet Filter Indicating System
C        - Electronic Control Unit (ECU) Logic Design
C
C    - Engine Starting Control and Indicating System
C        - Main Engine Starting Control
C        - Engine Starting Control ( logic with BPCU and GCU )
C        - Engine Starter Characteristics
C        - Engine Starting Indicating System
C
C    - Engine Ignition Control and Indicating
C        - Exciter Ignition Power Supply Available Flag
C        - Sparking Plug Operation Flag
C        - Continuous Relight Annunciator Light
C
C    - Engine Starting and Operating Characteristics
C        - Engine Starting Operating Enveloppe
C        - Engine Operating Characteristic for Starting and Relighting
C        - Dynamic Condition for Engine Start and LightUp
C        - Engine Flame Flag
C
C    - Propellers Controlling System
C        - Power Management Selector Switch
C        - Propeller Synchrophaser
C        - Propeller Feathering System
C          -- Mechanical Feather Valve
C          -- Electrical Feather Solenoid
C          -- Feather Solenoid / Condition Lever Control
C          -- Feather Solenoid / Fire Handle Control
C          -- Feather Solenoid / Auto Feather System
C          -- Feather Solenoid / Low Pitch Protection
C          -- Feather Solenoid
C          -- A/F Prop CTL & A/F Prop Drive Signal Inhib
C          -- Feathering Pump Time Delay Control Relay
C          -- Feathering Pump Power Relay
C          -- Feathering Pump Operating Flag
C        - Overspeed Test System
C        - Propeller NP Governing Cancel
C          -- Prop NP Governing Cancel by the Condition Lever
C          -- NP Governing Cancel with Engine Fire Handle Activation
C          -- NP Governing with AutoFeather Signal
C          -- NP Governing Cancel with AutoFeather Test
C          -- NP Governing Cancel with PROP BRAKE Controlled (ENG 2 ONLY)
C          -- Prop NP Governing Cancel Flag
C        - Propeller Brake System
C          -- Propeller Brake Control
C          -- Propeller Brake - Hydromechanical Unit
C          -- Propeller Brake Indicating System
C        - Propeller Ice Protection System
C
C    - Engine Oil System
C        - Oil Distribution Eng 2 Oil Cooling air Rgltn
C        - Oil Distribution Cooling air Rgltn / Eng Moving Flap
C        - Oil Indicating / LOP Warning
C
C     in order to reproduce the response and performance of the
C   PW120 ENGINE/ HS14SF-5 PROPELLER control systems for the
C   DASH-8 flight simulator.
C
C
C'Include_files
C
C       Not applicable
C
C'Data_Base_Variables
C     *****************************************************
C     *         C O M M O N    D A T A    B A S E         *
C     *****************************************************
C
C Inputs
C
CPI    USD8
C
C   ******* INPUTS  *******
C
C   * Integer inputs
C
CPI  & HEMODE    ,HRENG     ,
CPI  & YITAIL    ,
C
C   * Real inputs
C
CPI  & ECLACTL   ,
CPI  & EBETA42   ,
CPI  & EITT      ,
CPI  & ENH       ,ENHC     ,ENHG    ,ENHR    ,
CPI  & ENP       ,ENPG     ,
CPI  & EPLACTL   ,EPOE     ,
CPI  & EQTX      ,
CPI  & ETT1      ,
CPI  & EWFEI     ,
CPI  & UWCAS     ,VHH       ,
CPI  & IAZZSP00  , IAZZSP01 ,
C
C   * logical inputs
C
CPI  & AGFPS59   ,AGFPS60   ,
CPI  & AERDK1    ,AERDK2    ,AFFA(2)   ,AMRLK1    ,
CPI  & DBFBHI    ,DBFBOI    ,

CPI  & BIDLMNF   ,BIDRMNF   ,BIDLSC    ,BIDRSC    ,
CPI  & BILC07    ,BILC08    ,
CPI  & BILF04    ,BILG03    ,BILG10    ,BILK04    ,BILL06    ,
CPI  & BILR09    ,
CPI  & BILJ10    ,BILS02    ,BILS04    ,BILS09    ,
CPI  & BIRA01    ,BIRA04    ,
CPI  & BIRB05    ,BIRB09    ,BIRC05    ,BIRD03    ,BIRG10    ,BIRH03    ,
CPI  & BIRH10    ,BIRJ07    ,BIRK05    ,BIRQ04    ,
CPI  & BIRM05    ,BIRL05    ,BILJ05    ,BILM08    ,
CPI  & BIVA05    ,BIVA07    ,BIVB07    ,BIVD07    ,BIVE07    ,BIVF05   ,

CPI  & BPLR09    ,BPRB09    ,

CPI  & IDESAF    ,IDESAFT   ,IDESAFT2  ,IDESALF   ,IDESALF2  ,
CPI  & IDESALU   ,IDESALU2  ,
CPI  & IDESCUM   ,IDESCUM2  ,IDESCUO   ,IDESCUO2  ,
CPI  & IDESBB    ,IDESBB2   ,IDESBBN   ,IDESBBN2  ,IDESBBP   ,IDESBBP2 ,
CPI  & IDESBY    ,IDESBY2   ,IDESEL1   ,IDESEL2   ,
CPI  & IDESEN1   ,IDESEN2   ,
CPI  & IDESTOP   ,IDESMCL   ,IDESMCP   ,IDESMCR   ,IDESSPH   ,
CPI  & IDESMAXP  ,IDESNORM  ,
CPI  & IDESOIT   ,IDESOIT2  ,
CPI  & IDESPRT   ,IDESPT1A  ,IDESPT1B  ,IDESPT2A  ,IDESPT2B  ,
CPI  & IDESCLS3  ,IDESCLS4  ,IDESCS3P  ,IDESCS4P  ,
CPI  & IDESCLS5  ,IDESCLS6  ,IDESCLS7  ,IDESCLS8  ,IDESCS14  ,
CPI  & IDESCS15  ,
CPI  & IDESPLS1  ,IDESPS6H  ,IDESPS7H  ,IDESPS9F  ,IDESS10F  ,
CPI  & IDESPLS3  ,IDESPLS4  ,IDESPLS6  ,IDESPLS7  ,IDESPLS8  ,
CPI  & IDESPLS9  ,IDESPS10  ,IDESPS11  ,IDESPS12  ,IDESPS13  ,
CPI  & IDESPS09  ,IDESPS23  ,IDESPS24  ,
CPI  & IDESOVP   ,IDESOVP2  ,IDESOVH   ,IDESOVH2  ,
CPI  & IDESS1    ,IDESS2    ,
CPI  & IDESI1    ,IDESI2    ,IDESIM1   ,IDESIM2   ,
CPI  & EFDESIN1  ,EFDESIN2  ,
CPI  & EFZEL     ,UGRA      ,
CPI  & TCFENG    ,TCMFSTRT  ,

CPI  & TF30221   , TF30222   , TF30231   , TF30232   ,
CPI  & TF30241   , TF30242   , TF30251   , TF30252   ,
CPI  & TF30351   , TF30352   , TF30391   , TF30392   ,
CPI  & TF71111(2), TF71011(2), TF71581(2), TF71641   , TF71642,
CPI  & TF71201(2), TF71211(2), TF71341(2), TF71441   , TF71442,
CPI  & TF71501   , TF71502   , TF71631(2), TF71271(2), TF71614,
CPI  & TF71615,
CPI  & TF71621(2),
C
CPI  & VBOG      , AGFWOWN   , VREPOS    , TCRSYST   ,
C
C    *******  OUTPUTS  *******
C
C    * Logical outputs
C
CPO  & ED$AFA    ,ED$AFP    ,ED$AFP2   ,ED$AFS    ,
CPO  & ED$AFU    ,ED$AFU2   ,
CPO  & ED$BYO    ,ED$BYO2   ,ED$BYC    ,ED$BYC2   ,
CPO  & ED$BYH    ,ED$BYH2   ,
CPO  & ED$ECUM   ,ED$ECUM2  ,ED$ECUO   ,ED$ECUO2  ,
CPO  & ED$ENF    ,ED$ENF2   ,ED$ENR    ,
CPO  & ED$FLX    ,ED$HORN   ,
CPO  & ED$LOP    ,ED$LOP2   ,
CPO  & ED$MAN    ,ED$MAN2   ,
CPO  & ED$OITT   ,ED$OITT2  ,
CPO  & ED$PB113  ,ED$PB213  ,ED$PB124  ,ED$PB224  ,
CPO  & ED$PGR    ,ED$PGR2   ,
CPO  & ED$PWUP   ,
CPO  & ED$SAT    ,ED$SELS   ,ED$SELT   ,ED$ST     ,ED$SPH    ,
C
CPO  & EFAFS     ,EFARM     ,EFFAIL    ,EFFE      ,EFAST     ,
CPO  & EFBB      ,EFHBOVG   ,EFHBOVV   ,
CPO  & EFCRR     ,EFCLR     ,EFAILFIX  ,
CPO  & EFECU     ,EFECUF    ,EFECUM    ,EFECUR    ,EFENRS    ,EFIDEM  ,
CPO  & EFEXCA    ,EFEXCB    ,EFEAM     ,
CPO  & EFFEI     ,EFFGA     ,
CPO  & EFLM      ,
CPO  & EFMFCS    ,
CPO  & EFNHOS    ,EFNPL     ,
CPO  & EFPBBH1   ,EFPBBH2   ,EFPOV     ,EFPFS     ,
CPO  & EFRPR     ,
CPO  & EFSCUA    ,EFSCUAF   ,EFSCUB    ,EFSCUFD   ,EFSCUIN   ,
CPO  & EFSCUFF   ,EFSCUUP   ,EFSCUT    ,EFSCTEST  ,
CPO  & EFSTC     ,EFSTT     ,EFSYNC    ,EFSCU     ,EFSCUFE   ,


CPO  & ER1K1     ,ER1K2     ,ER1K3     ,ER1K4     ,
CPO  & ER2K1     ,ER2K2     ,ER2K3     ,ER2K4     ,ER2K5    ,
CPO  & ER2K6     ,ER2K7     ,ER2K8     ,ER2K9     ,
CPO  & ER3K3(2)  ,
CPO  & ER4K1     ,ER4K2     ,ER4K3     ,ER4K4     ,ER4K5    ,
CPO  & ER4K6     ,ER4K7     ,ER4K8     ,ER4K9     ,ER4K10   ,
CPO  & ER4K11    ,ER4K12    ,ER4K13    ,ER4K14    ,ER4K15   ,
CPO  & ER4K16    ,ER4K17    ,
CPO  & ER5K1     ,ER5K2     ,
CPO  & ER53K1    ,ER53K2    ,ER53K5    ,ER53K6    ,ER53K7   ,
CPO  & ER53K8    ,ER53K9    ,ER53K10   ,ER53K11   ,ER53K12  ,
CPO  & ER6K4     ,ER6K5     ,
CPO  & ER7K1     ,ER7K2     ,ER7K3     ,

CPO  & ESPBP11   ,ESPBP12   ,
CPO  & ESCLS3    ,ESCLS4    ,ESCLS3P   ,ESCLS4P   ,ESCLMN1   ,ESCLMN2 ,
CPO  & ESCLS5    ,ESCLS6    ,ESCLS7    ,ESCLS8    ,ESCLS14   ,ESCLS15 ,
CPO  & ESPLS1    ,ESPLS6H   ,ESPLS7H   ,ESPLS9F   ,ESPLS10F  ,
CPO  & ESPLS3    ,ESPLS4    ,ESPLS6    ,ESPLS7    ,ESPLS8    ,ESPLS9  ,
CPO  & ESPLS10   ,ESPLS11   ,ESPLS12   ,ESPLS13   ,ESPLS23   ,ESPLS24 ,
CPO  & ESLOP     ,
CPO  & ETS3      ,IDAASPL4  ,
C
CPO  & TCM0FSTR  ,
C
C    * Real outputs
C
CPO  & EPPAUX    ,
CPO  & ETIM7K3   ,ETIMRK3   ,ETIMRK4   ,ETIM4K8    ,ETIM4K9    ,
CPO  & EVFF      ,EVFPE     ,EVMF
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  9-Jan-2013 19:51:49
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd8.xeq.81
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  EBETA42(2)     ! PROPELLER BLADE ANGLE @ 42 IN.         [DEG]
     &, ECLACTL(2)     ! CONDITION LEVER ANGLE               [DEGREE]
     &, EITT(2)        ! ACTUAL INTER TURBINE TEMP                [C]
     &, ENH(2)         ! PHYSICAL HP COMPRESSOR SPEED             [%]
     &, ENHC(2)        ! PHYSICAL CORR. HP COMP. SPEED          [RPM]
     &, ENHG(2)        ! NH VALUE FOR ECU GOVEROR               [RPM]
     &, ENHR(2)        ! PHYSICAL HP COMP. SPEED                [RPM]
     &, ENP(2)         ! PHYSICAL PROPELLER SPEED                 [%]
     &, ENPG(2)        ! PROP SPEED FOR ECU/PCU GOVER           [RPM]
     &, EPLACTL(2)     ! POWER LEVER ANGLE                   [DEGREE]
     &, EPOE(2)        ! ENGINE OIL PRESSURE                   [PSIG]
     &, EQTX(2)        ! TORQUE VAL FOR ATPCS PURPOSE             [%]
     &, ETT1           ! FREE STREAM TOTAL TEMP.                  [C]
     &, EWFEI(2)       ! ENG FUEL FLOW AT ENGINE INLET       [LBS/HR]
     &, IAZZSP00       ! PROP INST ENGINE 1 >1000 RPM SW       AI017
     &, IAZZSP01       ! PROP INST ENGINE 2 >1000 RPM SW       AI018
     &, UGRA(3)        !  Radio altitude                        [ft]
     &, UWCAS          !  Calibrated Airspeed in Knots
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
C$
      INTEGER*4
     &  HEMODE(4)      ! MODE OF ENGINES PROGRAM
     &, HRENG          ! ENGINE REPOSITION INDEX
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  AERDK1         ! L gen contactor control relay
     &, AERDK2         ! R gen contactor control relay
     &, AFFA(2)        ! Fuel available eng 1
     &, AGFPS59        ! PSEU eq59  [B44] AUX RELAY DRIVER #2
     &, AGFPS60        ! PSEU eq60  [C45] AUX RELAY DRIVER #3
     &, AGFWOWN        ! NOSE   gear WOW SYS 1 S11
     &, AMRLK1         ! Positive seeking lamp relay
     &, BIDLMNF        ! 28 V DC BUS L MAIN FEEDER      PDLMNF DIDUMY
     &, BIDLSC         ! 28 V DC BUS L SECONDARY (DCL)  PDLSC  DIDUMY
     &, BIDRMNF        ! 28 V DC BUS R MAIN FEEDER      PDRMNF DIDUMY
     &, BIDRSC         ! 28 V DC BUS R SECONDARY (DCR)  PDRSC  DIDUMY
     &, BILC07         ! GND SPLRS CONT (100 ONLY)   27 PDLMN  DI2068
     &, BILC08         ! SAT/FLX (300 ONLY)         *27 PDLMN  DI2079
     &, BILF04         ! PROP AUX PUMP IND           61 PDLES  DI2038
     &, BILG03         ! PROP BETA LTS               61 PDLES  DI2028
     &, BILG10         ! PLA TEMP AF CONT (300 ONLY) 21 PDLES  DI209F
     &, BILJ05         ! ENG 1 IGN                   74 PDLES  DI204C
     &, BILJ10         ! MFC ENG 1  (SERIE 300)      73 PDLES  DI2101
     &, BILK04         ! ECU ENG 1                   73 PDLES  DI203C
     &, BILL06         ! ENG 1 TORQUE COND           73 PDLES  DI205F
     &, BILM08         ! MFC ENG 2                   73 PDLES  DI2082
     &, BILR09         ! PROP DEICE CONT 1           30 PDLSC  DI2097
     &, BILS02         ! IND LCD HTR                *61 PDLSC  DI2021
     &, BILS04         ! PROP AUX PUMP 1             61 PAON   DI2043
     &, BILS09         ! PROP O/SPD                  61 PDLSC  DI2098
     &, BIRA01         ! IND LCD HTR                *61 PDRSC  DI2160
     &, BIRA04         ! PROP AUX PUMP 2             61 PAON   DI2193
     &, BIRB05         ! INTAKE DEFLECT ACT 1        30 PDRSC  DI2205
     &, BIRB09         ! PROP DEICE CONT 2           30 PDRSC  DI2249
     &, BIRC05         ! INTAKE DEFLECT ACT 2        30 PDRSC  DI2206
     &, BIRD03         ! PROP SYNC                   61 PDRSC  DI2185
      LOGICAL*1
     &  BIRG10         ! PLA TEMP AF CONT (300 ONLY) 21 PDRES  DI225F
     &, BIRH03         ! ECU ENG 2                   73 PDRES  DI2189
     &, BIRH10         ! MFC ENG 2  (SERIE 300)      73 PDRES  DI2260
     &, BIRJ07         ! MFC ENG 1                   73 PDRES  DI222E
     &, BIRK05         ! ENG 2 TORQUE COND           73 PDRES  DI220D
     &, BIRL05         ! ENG 2 IGN                   74 PDRES  DI220E
     &, BIRM05         ! ENG START                   80 PDRES  DI220F
     &, BIRQ04         ! PROP ALTN FEATH             61 PDRMN  DI2201
     &, BIVA05         ! INTK LIP HTR ENG 1          30 PAVLA  DI2138
     &, BIVA07         ! L PROP DEICE PH A           30 PAVLA  DI2144
     &, BIVB07         ! L PROP DEICE PH B           30 PAVLB  DI2145
     &, BIVD07         ! R PROP DEICE PH A           30 PAVRA  DI2147
     &, BIVE07         ! R PROP DEICE PH B           30 PAVRB  DI2148
     &, BIVF05         ! INTK LIP HTR ENG 2          30 PAVRC  DI213D
     &, BPLR09         ! PROP DEICE CONT 1           30 PDLSC  DO2097
     &, BPRB09         ! PROP DEICE CONT 2           30 PDRSC  DO2249
     &, DBFBHI(2)      ! BLEED SYST CONT POWER AVAILABLE
     &, DBFBOI(2)      ! BLEED SYST CONT PWR TO HBOV
     &, EFDESIN1       ! Engine ignition 1 @ NORM                 [-]
     &, EFDESIN2       ! Engine ignition 2 @ NORM                 [-]
     &, EFZEL          ! ENG. CONTROL MOD. FREEZE FLAG            [-]
     &, IDESAF         ! Autofeather SELECT sw                 DI009E
     &, IDESAFT        ! Prop no 1 AUTOFEATHER TEST sw         DI0100
     &, IDESAFT2       ! Prop no 2 AUTOFEATHER TEST sw         DI0101
     &, IDESALF        ! Eng 1 ALT FEATHER sw @ FEATHERED      DI0102
     &, IDESALF2       ! Eng 2 ALT FEATHER sw @ FEATHERED      DI0104
     &, IDESALU        ! Eng 1 ALT FEATHER sw @ UNFEATHERED    DI0103
     &, IDESALU2       ! Eng 2 ALT FEATHER sw @ UNFEATHERED    DI0105
     &, IDESBB         ! Prop 1 Beta Backup sw @ BACKUP        DI0327
     &, IDESBB2        ! Prop 2 Beta Backup sw @ BACKUP        DI032A
     &, IDESBBN        ! Prop 1 Beta Backup sw @ normal        DI0328
      LOGICAL*1
     &  IDESBBN2       ! Prop 2 Beta Backup sw @ normal        DI032B
     &, IDESBBP        ! Prop 1 Beta Backup sw @ P/L SW        DI0329
     &, IDESBBP2       ! Prop 2 Beta Backup sw @ P/L SW        DI032C
     &, IDESBY         ! Eng 1 intake bypass door              DI008B
     &, IDESBY2        ! Eng 2 intake bypass door              DI008C
     &, IDESCLS3       ! CL microswitch                        DI002F
     &, IDESCLS4       ! CL microswitch                        DI0034
     &, IDESCLS5       ! CL microswitch                        DI0030
     &, IDESCLS6       ! CL microswitch                        DI0035
     &, IDESCLS7       ! CL microswitch                        DI0031
     &, IDESCLS8       ! CL microswitch                        DI0036
     &, IDESCS14       ! CL microswitch                        DI0037
     &, IDESCS15       ! CL microswitch                        DI0032
     &, IDESCS3P       ! CL microswitch                        DI002E
     &, IDESCS4P       ! CL microswitch                        DI0033
     &, IDESCUM        ! Eng 1 ECU MODE @ MANUAL               DI0097
     &, IDESCUM2       ! Eng 2 ECU MODE @ MANUAL               DI0099
     &, IDESCUO        ! Eng 1 ECU MODE @ ON                   DI0096
     &, IDESCUO2       ! Eng 2 ECU MODE @ ON                   DI0098
     &, IDESEL1        ! SELECT engine start sw @ 1            DI020E
     &, IDESEL2        ! SELECT engine start sw @ 2            DI020D
     &, IDESEN1        ! Enrichment test sw @ P/L SW 1         DI0326
     &, IDESEN2        ! Enrichment test sw @ P/L SW 2         DI0324
     &, IDESI1         ! Engine ignition 1 @ NORM              DI020C
     &, IDESI2         ! Engine ignition 2 @ NORM              DI020A
     &, IDESIM1        ! Engine ignition 1 @ MANUAL            DI020B
     &, IDESIM2        ! Engine ignition 2 @ MANUAL            DI0209
     &, IDESMAXP       ! Eng. 1&2 MAX/REDUCED power switch     DIDUMY
     &, IDESMCL        ! Engine ECU selector @ MCL             DI0092
     &, IDESMCP        ! Engine ECU selector @ MCP             DI0091
     &, IDESMCR        ! Engine ECU selector @ MCR             DI0093
      LOGICAL*1
     &  IDESNORM       ! Engine ECU selector @ NORM            DIDUMY
     &, IDESOIT        ! Eng 1 ITT overtemperature             DI0082
     &, IDESOIT2       ! Eng 2 ITT overtemperature             DI0083
     &, IDESOVH        ! Overspeed test sw @ NH                DI0340
     &, IDESOVH2       ! Overspeed test sw @ NH                DI0342
     &, IDESOVP        ! Overspeed test sw @ PROP              DI0341
     &, IDESOVP2       ! Overspeed test sw @ PROP              DI0343
     &, IDESPLS1       ! PL microswitch                        DI0013
     &, IDESPLS3       ! PL microswitch                        DI0017
     &, IDESPLS4       ! PL microswitch                        DI0022
     &, IDESPLS6       ! PL microswitch                        DI0029
     &, IDESPLS7       ! PL microswitch                        DI001C
     &, IDESPLS8       ! PL microswitch                        DI002A
     &, IDESPLS9       ! PL microswitch                        DI0019
     &, IDESPRT        ! Propeller test sw.                    DI0178
     &, IDESPS09       ! PL microswitch                        DI0079
     &, IDESPS10       ! PL microswitch                        DI0026
     &, IDESPS11       ! PL microswitch                        DI001E
     &, IDESPS12       ! PL microswitch                        DI002C
     &, IDESPS13       ! PL microswitch                        DI001F
     &, IDESPS23       ! PL microswitch                        DI001A
     &, IDESPS24       ! PL microswitch                        DI0027
     &, IDESPS6H       ! PL microswitch                        DI002D
     &, IDESPS7H       ! PL microswitch                        DI0020
     &, IDESPS9F       ! PL microswitch                        DI0010
     &, IDESPT1A       ! Prop. timer #1 above -10 deg.         DI0177
     &, IDESPT1B       ! Prop. timer #1 below -10 deg.         DI0176
     &, IDESPT2A       ! Prop. timer #2 above -10 deg.         DI0174
     &, IDESPT2B       ! Prop. timer #2 below -10 deg.         DI0175
     &, IDESS1         ! START engine sw select for eng 1      DI0207
     &, IDESS10F       ! PL microswitch                        DI002B
      LOGICAL*1
     &  IDESS2         ! START engine sw select for eng 2      DI020F
     &, IDESSPH        ! SYNCHRO PHASE sw @ ON                 DI0370
     &, IDESTOP        ! Engine ECU selector @ TOP             DI0090
     &, TCFENG         ! FREEZE/ENGINES
     &, TCMFSTRT       ! ENGINE QUICK START
     &, TCRSYST        ! ALL SYSTEM RESET
     &, TF30221        ! BLADE HEATING FAILS (1-3) LEFT
     &, TF30222        ! BLADE HEATING FAILS (1-3) RIGHT
     &, TF30231        ! BLADE HEATING FAIL (2-4) LEFT
     &, TF30232        ! BLADE HEATING FAIL (2-4) RIGHT
     &, TF30241        ! ENGINE DOORS FAIL TO CLOSE 1
     &, TF30242        ! ENGINE DOORS FAIL TO CLOSE 2
     &, TF30251        ! ENGINE DOORS FAIL TO OPEN 1
     &, TF30252        ! ENGINE DOORS FAIL TO OPEN 2
     &, TF30351        ! ENGINE HEATER INLET FAIL LEFT
     &, TF30352        ! ENGINE HEATER INLET FAIL RIGHT
     &, TF30391        ! PROP DE ICE PROTECT CB TRIP LEFT
     &, TF30392        ! PROP DE ICE PROTECT CB TRIP RIGHT
     &, TF71011(2)     ! ECU FAULT 1
     &, TF71111(2)     ! FLAMEOUT LEFT
     &, TF71201(2)     ! AUTOFEATHER FAIL LEFT
     &, TF71211(2)     ! AUTOFEATHER TEST FAIL LEFT
     &, TF71271(2)     ! PROP AUTOFEATHER SYSTEM FAIL LEFT
     &, TF71341(2)     ! PROP LOW PITCH IN FLIGHT LEFT
     &, TF71441        ! IGNITION FAIL LEFT
     &, TF71442        ! IGNITION FAIL RIGHT
     &, TF71501        ! SPEED SENSOR FAIL LEFT
     &, TF71502        ! SPEED SENSOR FAIL RIGHT
     &, TF71581(2)     ! FUEL NOT AVAIL AT CL MOVEMENT LEFT
     &, TF71614        ! BETA LOCKOUT LEFT ENGINE FAIL
     &, TF71615        ! BETA LOCKOUT RIGHT ENGINE FAIL
      LOGICAL*1
     &  TF71621(2)     ! PROP MANUAL FEATHER FAIL LEFT
     &, TF71631(2)     ! PROP UNCOMMANDED FEATHER LEFT
     &, TF71641        ! OVERSPEED TEST FAILS LEFT
     &, TF71642        ! OVERSPEED TEST FAILS RIGHT
     &, VBOG           ! ON GROUND FLAG
     &, VREPOS         ! REPOSITION FLAG
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  EPPAUX(2)      ! PROPELLER AUXILIARY PRESSURE PUMP      [PSI]
     &, ETIM4K8        ! PUMP 2 DELAY ON RELAY ER4K8            [SEC]
     &, ETIM4K9        ! PUMP 1 DELAY ON RELAY ER4K9            [SEC]
     &, ETIM7K3        ! TIME DELAY ON RELAY ER7K3              [SEC]
     &, ETIMRK3        ! TIMER ER3K3 SELECT RELAY               [SEC]
     &, ETIMRK4        ! TIMER ER3K4 SELECT RELAY               [SEC]
     &, EVFF(2)        ! HP FUEL SO VALVE POSITION                [-]
     &, EVFPE(2)       ! PROPELLER FEATHER VALVE                  [-]
     &, EVMF(2)        ! MECHANICAL FEATHER VALVE POS         [COEFF]
C$
      LOGICAL*1
     &  ER4K17         ! ENG T/O WARN HORN RELAY
     &, ED$AFA         ! Autofeather ARM lt                    DO0750
     &, ED$AFP         ! No 1 Feather aux. pump ON lt          DO074F
     &, ED$AFP2        ! No 2 Feather aux. pump ON lt          DO0756
     &, ED$AFS         ! Autofeather SELECT lt                 DO0778
     &, ED$AFU         ! Eng 1 Alt. Feather @ UNFEATHER sol.   DO007A
     &, ED$AFU2        ! Eng 2 Alt. Feather @ UNFEATHER sol.   DO007B
     &, ED$BYC         ! Eng 1 intake CLOSED lt                DO0767
     &, ED$BYC2        ! Eng 2 intake CLOSED lt                DO076A
     &, ED$BYH         ! Eng 1 intake HTR lt                   DO0768
     &, ED$BYH2        ! Eng 2 intake HTR lt                   DO076B
     &, ED$BYO         ! Eng 1 intake OPEN lt                  DO0766
     &, ED$BYO2        ! Eng 2 intake OPEN lt                  DO0769
     &, ED$ECUM        ! Eng 1 ECU mode MANUAL lt              DO074E
     &, ED$ECUM2       ! Eng 2 ECU mode MANUAL lt              DO0758
     &, ED$ECUO        ! Eng 1 ECU mode ON lt                  DO074D
     &, ED$ECUO2       ! Eng 2 ECU mode ON lt                  DO0757
     &, ED$ENF         ! ENGINE FAIL captain lt                DO0751
     &, ED$ENF2        ! ENGINE FAIL F/O lt                    DO0752
     &, ED$ENR         ! ENRICHMENT test lt                    DO0779
     &, ED$FLX         ! Eng. 1&2 MAX/REDUCED power FLX lt     DO075F
     &, ED$HORN        ! BETA LOCKOUT WARNING HORN             DO0150
     &, ED$LOP         ! No 1 ENG OIP PRESS                    DO0821
     &, ED$LOP2        ! No 2 ENG OIP PRESS                    DO0820
     &, ED$MAN         ! No 1 ENG MANUAL                       DO0708
     &, ED$MAN2        ! No 2 ENG MANUAL                       DO0707
     &, ED$OITT        ! ENGINE 1 ITT OVERTEMP LT              DO079B
     &, ED$OITT2       ! ENGINE 2 ITT OVERTEMP LT              DO079C
     &, ED$PB113       ! Prop. #1 blades 1&3 light             DO0749
     &, ED$PB124       ! Prop. #1 blades 2&4 light             DO074A
     &, ED$PB213       ! Prop. #2 blades 1&3 light             DO074B
      LOGICAL*1
     &  ED$PB224       ! Prop. #2 blades 2&4 light             DO074C
     &, ED$PGR         ! Propeller ground range #1 lt          DO049D
     &, ED$PGR2        ! Propeller ground range #2 lt          DO049E
     &, ED$PWUP        ! Power UPTRIM lt                       DO0742
     &, ED$SAT         ! Eng. 1&2 MAX/REDUCED power SAT lt     DO075E
     &, ED$SELS        ! SELECT eng. start sw. solenoid        DO0137
     &, ED$SELT        ! SELECT eng. start sw. lt              DO0139
     &, ED$SPH         ! Synchro Phase lt                      DO075D
     &, ED$ST          ! START eng. start lt                   DO0138
     &, EFAFS(2)       ! AUTOFEATHER SELECT SIGNAL                [-]
     &, EFAILFIX(2)    ! FAILED FIX LOGIC FLAG FOR TORQUE MOTOR   [-]
     &, EFARM(2)       ! ENG ARM LIGHT 28V DC SCU SIGNAL          [-]
     &, EFAST(2)       ! ENGINE FAST START FLAG                   [-]
     &, EFBB(2)        ! BETA BACKUP FEATHER VALVE SIGNAL         [-]
     &, EFCLR(2)       ! ECU MAX CLIMB RATING SIGNAL              [-]
     &, EFCRR(2)       ! ECU MAX CRUISE RATING SIGNAL             [-]
     &, EFEAM(2)       ! MECH FTR VALVE POS FLAG                  [-]
     &, EFECU(2)       ! ECU NORMAL OPER. FLAG                    [-]
     &, EFECUF(2)      ! ECU FAULT LATCH SIGNAL                   [-]
     &, EFECUM(2)      ! ECU MANUAL MODE SIGNAL                   [-]
     &, EFECUR(2)      ! ECU REVERSION RELAY SIGNAL               [-]
     &, EFENRS(2)      ! ENRICH SOLENOID                          [-]
     &, EFEXCA(2)      ! SPARKING PLUG A [T=ACTIVE]               [-]
     &, EFEXCB(2)      ! SPARKING PLUG B [T=ACTIVE]               [-]
     &, EFFAIL(2)      ! ENG FAIL LIGHT 28V DC SCU SIGNAL         [-]
     &, EFFE(2)        ! PROPELLER FEATHER VALVE SIGNAL           [-]
     &, EFFEI(2)       ! FUEL AVAIL FLAG AT ENG INLET             [-]
     &, EFFGA(2)       ! ECU FUEL GOVERNING ARM SIGNAL RESET      [-]
     &, EFHBOVG(2)     ! SCU GROUND HBOV T/M SIGNAL ( S-300 )     [-]
     &, EFHBOVV(2)     ! SCU 28V DC HBOV T/M SIGNAL ( S-300 )     [-]
     &, EFIDEM(2)      ! STARTER AVAILABILITY FLAG                [-]
      LOGICAL*1
     &  EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, EFMFCS(2)      ! 28V DC MFC SOL SIGNAL                    [-]
     &, EFNHOS(2)      ! ECU NH OVERSPEED SIGNAL                  [-]
     &, EFNPL(2)       ! PROP NP GOVERNING CANCEL FLAG            [-]
     &, EFPBBH1(2)     ! PROPELLER BRUSH BLOCK HOUSING BLADE 1    [-]
     &, EFPBBH2(2)     ! PROPELLER BRUSH BLOCK HOUSING BLADE 2    [-]
     &, EFPFS(2)       ! ECU PROPELLER FEATHERED SIGNAL           [-]
     &, EFPOV(2)       ! PROPELLER OVERSPEED GOVERNOR SIGNAL      [-]
     &, EFRPR(2)       ! ECU REDUCED POWER RATING SIGNAL          [-]
     &, EFSCTEST(2)    ! ENG SCU TORQUE TEST SIGNAL               [-]
     &, EFSCU(2)       ! ENG SCU 28V DC HI SIGNAL                 [-]
     &, EFSCUA(2)      ! SCU ARM PROP. DISCRETE SIGNAL ( S-300 )  [-]
     &, EFSCUAF(2)     ! SCU AUTOFEATHER ENABLE SIGNAL            [-]
     &, EFSCUB(2)      ! ECU BLEED SWITCH SIGNAL ( S-300 )        [-]
     &, EFSCUFD(2)     ! SCU FEATHER DISCRETE SIGNAL ( S-300 )    [-]
     &, EFSCUFE(2)     ! SCU FEATHER SIGNAL                       [-]
     &, EFSCUFF(2)     ! SCU FAIL FIX RELAY SIGNAL ( S-300 )      [-]
     &, EFSCUIN(2)     ! ECU INHIBIT/RESET DISCRETE ( S-300 )     [-]
     &, EFSCUT(2)      ! SCU ENGINE TRIM DISCRETE SIGNAL ( S-300 )[-]
     &, EFSCUUP(2)     ! ENG SCU UPTRIM SIGNAL TO OTHER ENG       [-]
     &, EFSTC(2)       ! ENG. START CONTROL SIGNAL TO GCU         [-]
     &, EFSTT(2)       ! ENG. START TERMINATE SIGNAL TO GCU       [-]
     &, EFSYNC         ! SYNCHROPHASER SYSTEM SIGNAL              [-]
     &, ER1K1          ! ENG 1 OIL PRESSURE RELAY                 [-]
     &, ER1K2          ! ENG 2 OIL PRESSURE RELAY                 [-]
     &, ER1K3          ! ENG 1 INTAKE ADAPTER RELAY               [-]
     &, ER1K4          ! ENG 2 INTAKE ADAPTER RELAY               [-]
     &, ER2K1          ! PROP. 1 TIMER NO 1 DEICE RELAY           [-]
     &, ER2K2          ! PROP. 2 TIMER NO 1 DEICE RELAY           [-]
     &, ER2K3          ! PROP. 1 TIMER NO 2 DEICE RELAY           [-]
     &, ER2K4          ! PROP. 2 TIMER NO 2 DEICE RELAY           [-]
      LOGICAL*1
     &  ER2K5          ! PROP. DEICE SELECT TIMER RELAY           [-]
     &, ER2K6          ! PROP NO 2 DEICE PH B RELAY               [-]
     &, ER2K7          ! PROP NO 1 DEICE PH B RELAY               [-]
     &, ER2K8          ! PROP NO 2 DEICE PH A RELAY               [-]
     &, ER2K9          ! PROP NO 1 DEICE PH A RELAY               [-]
     &, ER3K3(2)       ! ECU OVERSPEED TEST RESET RELAY           [-]
     &, ER4K1          ! ENG 1 INTERLOCK RELAY                    [-]
     &, ER4K10         ! ENG 2 POWER UPTRIM RELAY                 [-]
     &, ER4K11         ! ENG 1 POWER UPTRIM RELAY                 [-]
     &, ER4K12         ! ALTERNATE FEATHER RELAY                  [-]
     &, ER4K13         ! ENG 1 FEATHER RELAY ( S-300 )            [-]
     &, ER4K14         ! ENG 2 FEATHER RELAY ( S-300 )            [-]
     &, ER4K15         ! ENG 1 POWER UPTRIM RELAY ( S-300 )       [-]
     &, ER4K16         ! ENG 2 POWER UPTRIM RELAY ( S-300 )       [-]
     &, ER4K2          ! ENG 2 INTERLOCK RELAY                    [-]
     &, ER4K3          ! PROP 1 TEST RELAY                        [-]
     &, ER4K4          ! PROP 2 TEST RELAY                        [-]
     &, ER4K5          ! ARM CONTROL RELAY                        [-]
     &, ER4K6          ! PUMP 2 CONTROL RELAY                     [-]
     &, ER4K7          ! PUMP 1 CONTROL RELAY                     [-]
     &, ER4K8          ! PUMP 2 CONTROL TD RELAY                  [-]
     &, ER4K9          ! PUMP 1 CONTROL TD RELAY                  [-]
     &, ER53K1         ! ENG 1 UPTRIM RELAY  ( S-300 )            [-]
     &, ER53K10        ! ENG 2 LOC ON RELAY  ( S-300 )            [-]
     &, ER53K11        ! ENG 1 BLEED VALVE OVERRIDE RELAY         [-]
     &, ER53K12        ! ENG 2 BLEED VALVE OVERRIDE RELAY         [-]
     &, ER53K2         ! ENG 2 UPTRIM RELAY  ( S-300 )            [-]
     &, ER53K5         ! ENG 1 LOC ON RELAY  ( S-300 )            [-]
     &, ER53K6         ! ENG 2 LOC ON RELAY  ( S-300 )            [-]
     &, ER53K7         ! ENG 1 LOC ON RELAY  ( S-300 )            [-]
     &, ER53K8         ! ENG 2 LOC ON RELAY  ( S-300 )            [-]
      LOGICAL*1
     &  ER53K9         ! ENG 1 LOC ON RELAY  ( S-300 )            [-]
     &, ER5K1          ! ENG 1 REVERSION RELAY                    [-]
     &, ER5K2          ! ENG 2 REVERSION RELAY                    [-]
     &, ER6K4          ! ENG. 2 OVERTORQUE RELAY                  [-]
     &, ER6K5          ! ENG. 1 OVERTORQUE RELAY                  [-]
     &, ER7K1          ! ENG 1 START CONTROL RELAY                [-]
     &, ER7K2          ! ENG 2 START CONTROL RELAY                [-]
     &, ER7K3          ! TIME DELAY RELAY                         [-]
     &, ESCLMN1        ! ENG 1 CL MSW [ T= CLA=MIN RPM ]          [-]
     &, ESCLMN2        ! ENG 2 CL MSW [ T= CLA=MIN RPM ]          [-]
     &, ESCLS14        ! ENG 2 CL MSW [ T= CLA=MIN RPM ]          [-]
     &, ESCLS15        ! ENG 1 CL MSW [ T= CLA=MIN RPM ]          [-]
     &, ESCLS3         ! ENG 1 CL MSW [ T= CLA=MAX RPM ]          [-]
     &, ESCLS3P        ! ENG 1 CL MSW [ T= CLA=1/2(SF&SO) ]       [-]
     &, ESCLS4         ! ENG 2 CL MSW [ T= CLA=MAX RPM ]          [-]
     &, ESCLS4P        ! ENG 2 CL MSW [ T= CLA=1/2(SF&SO) ]       [-]
     &, ESCLS5         ! ENG 1 CL MSW [ T= CLA=MAX RPM ]          [-]
     &, ESCLS6         ! ENG 2 CL MSW [ T= CLA=MAX RPM ]          [-]
     &, ESCLS7         ! ENG 1 CL MSW [ T= CLA=ST&FEAT ]          [-]
     &, ESCLS8         ! ENG 2 CL MSW [ T= CLA=ST&FEAT ]          [-]
     &, ESLOP(2)       ! PRESS SW OIL LO ENG 1                    [-]
     &, ESPBP11        ! PROP 1 LOW PITCH MSW [ T= FLT IDLE-2.5 ] [-]
     &, ESPBP12        ! PROP 1 LOW PITCH MSW [ T= FLT IDLE-2.5 ] [-]
     &, ESPLS1         ! ENG 1 PL MSW [ T= PLA > FLT IDLE + 12 ]  [-]
     &, ESPLS10        ! ENG 2 PL MSW [ T= PLA < FLT IDLE - 3 ]   [-]
     &, ESPLS10F       ! ENG 2 PL MSW [ T= PLA > FLT IDLE + 28 ]  [-]
     &, ESPLS11        ! ENG 1 PL MSW [ T= PLA < FLT IDLE - 3 ]   [-]
     &, ESPLS12        ! ENG 2 PL MSW [ T= PLA > FLT IDLE + 28 ]  [-]
     &, ESPLS13        ! ENG 1 PL MSW [ T= PLA > FLT IDLE + 28 ]  [-]
     &, ESPLS23        ! ENG 1 PL MSW [ T= PLA < FLT IDLE + 12 ]  [-]
     &, ESPLS24        ! ENG 2 PL MSW [ T= PLA < FLT IDLE + 12 ]  [-]
      LOGICAL*1
     &  ESPLS3         ! ENG 1 PL MSW [ T= PLA > FLT IDLE - 3 ]   [-]
     &, ESPLS4         ! ENG 2 PL MSW [ T= PLA > FLT IDLE - 3 ]   [-]
     &, ESPLS6         ! ENG 2 PL MSW [ T= PLA < FLT IDLE + 12 ]  [-]
     &, ESPLS6H        ! ENG 2 PL MSW [ T= PLA > FLT IDLE + 28 ]  [-]
     &, ESPLS7         ! ENG 1 PL MSW [ T= PLA < FLT IDLE + 12 ]  [-]
     &, ESPLS7H        ! ENG 1 PL MSW [ T= PLA > FLT IDLE + 28 ]  [-]
     &, ESPLS8         ! ENG 2 PL MSW [ T= PLA = FLT IDLE ]       [-]
     &, ESPLS9         ! ENG 1 PL MSW [ T= PLA = FLT IDLE ]       [-]
     &, ESPLS9F        ! ENG 1 PL MSW [ T= PLA > FLT IDLE + 28 ]  [-]
     &, ETS3           ! ENG. 1 THERMOSTAT OUTSIDE TEMP           [-]
     &, IDAASPL4       ! Spare dip for ancillaries             DIDUMY
     &, TCM0FSTR       ! ENGINE QUICK START
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(4864),DUM0000003(4)
     &, DUM0000004(4195),DUM0000005(2),DUM0000006(13)
     &, DUM0000007(1265),DUM0000008(1376),DUM0000009(972)
     &, DUM0000010(1),DUM0000011(6),DUM0000012(2),DUM0000013(138)
     &, DUM0000014(331),DUM0000015(63),DUM0000016(128)
     &, DUM0000017(85),DUM0000018(42),DUM0000019(22)
     &, DUM0000020(89),DUM0000021(1),DUM0000022(2585)
     &, DUM0000023(8),DUM0000024(1595),DUM0000025(3648)
     &, DUM0000026(456),DUM0000027(920),DUM0000028(1240)
     &, DUM0000029(72351),DUM0000030(1219),DUM0000031(3)
     &, DUM0000032(988),DUM0000033(72),DUM0000034(80)
     &, DUM0000035(32),DUM0000036(380),DUM0000037(68)
     &, DUM0000038(32),DUM0000039(544),DUM0000040(32)
     &, DUM0000041(24),DUM0000042(24),DUM0000043(8)
     &, DUM0000044(8),DUM0000045(48),DUM0000046(164)
     &, DUM0000047(3),DUM0000048(7),DUM0000049(9),DUM0000050(9)
     &, DUM0000051(2),DUM0000052(2),DUM0000053(3),DUM0000054(4)
     &, DUM0000055(25),DUM0000056(8),DUM0000057(4)
     &, DUM0000058(10),DUM0000059(21),DUM0000060(2)
     &, DUM0000061(2),DUM0000062(2219),DUM0000063(1079)
     &, DUM0000064(58),DUM0000065(283),DUM0000066(341)
     &, DUM0000067(201307),DUM0000068(16),DUM0000069(199)
     &, DUM0000070(7038),DUM0000071(6378),DUM0000072(3)
     &, DUM0000073(2),DUM0000074(4),DUM0000075(142)
     &, DUM0000076(8),DUM0000077(7),DUM0000078(3),DUM0000079(8)
     &, DUM0000080(18),DUM0000081(15790)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,BIDLMNF,BIDRMNF,DUM0000003
     &, BIDLSC,BIDRSC,DUM0000004,ED$HORN,ED$MAN,ED$MAN2,ED$LOP
     &, ED$LOP2,DUM0000005,ED$SELS,ED$SELT,ED$ST,ED$PB113,ED$PB213
     &, ED$PB124,ED$PB224,ED$ENR,ED$BYO,ED$BYO2,ED$BYC,ED$BYC2
     &, ED$BYH,ED$BYH2,ED$ECUM,ED$ECUM2,ED$ECUO,ED$ECUO2,ED$PWUP
     &, ED$AFP,ED$AFP2,ED$AFA,ED$AFS,ED$AFU,ED$AFU2,ED$FLX,ED$SAT
     &, ED$OITT,ED$OITT2,DUM0000006,ED$SPH,ED$PGR,ED$PGR2,ED$ENF
     &, ED$ENF2,DUM0000007,BPLR09,BPRB09,DUM0000008,IAZZSP00
     &, IAZZSP01,DUM0000009,IDESPT1A,IDESPT1B,IDESPT2A,IDESPT2B
     &, IDESPRT,IDESI1,IDESIM1,IDESI2,IDESIM2,IDESEL1,IDESEL2
     &, IDESS1,IDESS2,IDESBBP,IDESBBP2,IDESBBN,IDESBBN2,IDESBB
     &, IDESBB2,IDESEN1,DUM0000010,IDESEN2,IDESOVH,IDESOVH2,IDESOVP
     &, IDESOVP2,IDESMAXP,DUM0000011,IDESOIT,IDESOIT2,IDESBY
     &, IDESBY2,IDESTOP,IDESMCP,IDESMCL,IDESMCR,IDESNORM,IDESCUM
     &, IDESCUM2,IDESCUO,IDESCUO2,IDESAF,IDESAFT,IDESAFT2,IDESALF
     &, IDESALF2,DUM0000012,IDESALU,IDESALU2,IDESSPH,IDESCLS3
     &, IDESCLS4,IDESCS3P,IDESCS4P,IDESCLS5,IDESCLS6,IDESCLS7
     &, IDESCLS8,IDESCS14,IDESCS15,IDESPLS1,IDESPLS3,IDESPLS4
     &, IDESPLS6,IDESPLS7,IDESPS6H,IDESPS7H,IDESPLS8,IDESPLS9
     &, IDESPS10,IDESPS9F,IDESS10F,IDESPS09,IDESPS11,IDESPS12
     &, IDESPS13,IDESPS23,IDESPS24,DUM0000013,IDAASPL4,DUM0000014
     &, BILC08,DUM0000015,BILS02,BIRA01,DUM0000016,BILG10,BIRG10
     &, DUM0000017,BILC07,DUM0000018,BIVA07,BIVD07,BIVB07,BIVE07
     &, BILR09,BIRB09,DUM0000019,BIVA05,BIVF05,BIRB05,BIRC05
     &, DUM0000020,BILS09,BIRQ04,BILS04,BIRA04,BILF04,BIRD03
     &, BILG03,BILK04,BIRH03,BILJ10,BIRH10,BIRJ07,BILM08,BILL06
     &, BIRK05,BILJ05,BIRL05,DUM0000021,BIRM05,DUM0000022,VREPOS
     &, DUM0000023,VBOG,DUM0000024,VHH,DUM0000025,HEMODE,DUM0000026
     &, HRENG,DUM0000027,UWCAS,DUM0000028,UGRA,DUM0000029,DBFBHI
     &, DBFBOI,DUM0000030,EFAST,DUM0000031,EFZEL,DUM0000032,ENP
      COMMON   /XRFTEST   /
     &  DUM0000033,EBETA42,DUM0000034,EVMF,DUM0000035,ENPG,DUM0000036
     &, ETT1,DUM0000037,ENH,ENHC,DUM0000038,ENHR,DUM0000039,EVFF
     &, DUM0000040,ECLACTL,EPLACTL,DUM0000041,ENHG,DUM0000042
     &, EQTX,DUM0000043,EWFEI,DUM0000044,EITT,DUM0000045,EPOE
     &, DUM0000046,ER1K1,ER1K2,ER1K3,ER1K4,ETS3,DUM0000047,ER2K1
     &, ER2K2,ER2K3,ER2K4,ER2K5,ER2K6,ER2K7,ER2K8,ER2K9,DUM0000048
     &, EFPBBH1,EFPBBH2,DUM0000049,ER4K13,ER4K14,ER4K15,ER4K16
     &, EFSCUFD,ER3K3,EFPOV,DUM0000050,ETIMRK3,ETIMRK4,ER4K1
     &, ER4K2,ER4K3,ER4K4,ER4K5,ER4K6,ER4K7,ER4K8,ER4K9,ER4K10
     &, ER4K11,ER4K12,ESPLS1,ESPLS3,ESPLS4,ESPLS6,ESPLS7,ESPLS8
     &, ESPLS9,ESPLS10,ESPLS11,ESPLS12,ESPLS13,ESPLS23,ESPLS24
     &, ESPLS6H,ESPLS7H,ESPLS9F,ESPLS10F,EFAFS,EFARM,DUM0000051
     &, EFEAM,EFFAIL,EFNPL,EFSCTEST,EFSCU,EFSCUAF,EFSCUFE,DUM0000052
     &, EFSCUUP,DUM0000053,ETIM4K8,ETIM4K9,EPPAUX,EVFPE,EFSYNC
     &, ESPBP11,ESPBP12,EFFE,EFBB,ER5K1,ER5K2,DUM0000054,ESCLS3
     &, ESCLS4,ESCLS3P,ESCLS4P,ESCLS5,ESCLS6,ESCLS7,ESCLS8,ESCLS14
     &, ESCLS15,ESCLMN1,ESCLMN2,EFAILFIX,EFCLR,EFCRR,DUM0000055
     &, EFECU,EFECUF,EFECUM,EFECUR,EFENRS,EFFEI,EFFGA,EFNHOS
     &, EFPFS,EFRPR,EFMFCS,DUM0000056,ER53K1,ER53K2,ER53K5,ER53K6
     &, ER53K7,ER53K8,ER53K9,ER53K10,ER53K11,ER53K12,EFHBOVG
     &, EFHBOVV,EFSCUA,EFSCUB,DUM0000057,EFSCUFF,EFSCUIN,EFSCUT
     &, DUM0000058,EFLM,EFEXCA,EFEXCB,EFDESIN1,EFDESIN2,ER6K4
     &, ER6K5,DUM0000059,ESLOP,DUM0000060,ER7K1,ER7K2,ER7K3,DUM0000061
     &, ETIM7K3,EFIDEM,EFSTC,EFSTT,DUM0000062,AERDK1,AERDK2,DUM0000063
     &, AGFWOWN,DUM0000064,AGFPS59,AGFPS60,DUM0000065,AFFA,DUM0000066
     &, AMRLK1,DUM0000067,TCFENG,DUM0000068,TCRSYST,DUM0000069
     &, TCMFSTRT,DUM0000070,TCM0FSTR,DUM0000071,TF30221,TF30222
     &, TF30231,TF30232,DUM0000072,TF30241,TF30242,TF30251,TF30252
     &, DUM0000073,TF30351,TF30352,DUM0000074,TF30391,TF30392
     &, DUM0000075,TF71271,TF71621,TF71341,TF71631,DUM0000076
     &, TF71111,DUM0000077,TF71641,TF71642,DUM0000078,TF71201
      COMMON   /XRFTEST   /
     &  TF71211,TF71614,TF71615,TF71011,TF71581,DUM0000079,TF71441
     &, TF71442,DUM0000080,TF71501,TF71502,DUM0000081,ER4K17
C------------------------------------------------------------------------------
C
C
       INCLUDE 'disp.com'   !NOFPC
C
C'Local_Variables
C
C     ***************************************************
C     *         L O C A L     V A R I A B L E S         *
C     ***************************************************
C
C'IDENT
C
      CHARACTER*55
     &  REV  /
     -  '$Source: usd8el.for.5 25Mar2006 02:05 usd8 Tom    $'/ ! FOR IDENT ( VA
C
      LOGICAL*1
     &  TF71541(2)    ! ENGINE ECU FAIL MALFUNCTION           [-]
C
C
      INTEGER*4
C
     &        I         ! Eng. index  ( 1  = left, 2 = right )
C
      REAL*4
     &  DAFTR(2)  , ! A/F timer after uptrim                [sec]
     &  DHORNT    , ! BETA LOCK OUT HORN DELAY TIMER        [sec]
     &  DFSR(2)   , ! Power setting reset timer             [sec.]
     &  DT        , ! Iteration time constant               [sec.]
     &  DTIM(2)   , ! Fast start timer                      [sec.]
     &  DTINV     , ! INVERTED ITERATION TIME CONSTANT      [1/SEC.]
     &  DSYNC     , ! RLY SYNCHRO MICRO BREAKS PROT. TIMER  [SEC.]
     &  EA$NPR(2) , ! PROP RPM INDICATOR ANALOG INPUT       [RPM]
     &  LACTBY(2) ,
     &  PLAARM(2) , ! PLA setting for autofeather arming    [deg]
     &  QWF(2)    , ! FUEL QUANT. IN LINE                   [KG]
     &  RTRBY(2)  ,
C !FM+
C !FM   8-Aug-93 07:48:47 s.gould
C !FM    < added code for t/o warning horn as per attachments sent to field >
C !FM
     &  TIM1      , ! Timer for T/O warning horn            [sec]
C !FM-
     &  TIM4/2.0/ , ! Timer for HRENG reposition            [sec]
     &  TIMPT1A   ,
     &  TIMPT2A   ,
     &  VFF(2)    , ! FUEL SHUTOFF VALVE POSITION           [0-1]
     &  VRL       , ! LOWER VALUE FOR RLT CONDITION         [KNOTS]
     &  VRU       , ! UPPER VALUE FOR RLT CONDITION         [KNOTS]
     &  XNPG(2)     ! ENGINE NP SPEED VALUE FOR EFNPL LOGIC [RPM]

C
C
      LOGICAL*1
     &  D100         ,  ! DASH-8/100 series                         [-]
     &  D300         ,  ! DASH-8/300 series                         [-]
     &  DESVOHI      ,  ! NH overspeed switch from prev. iteration  [-]
     &  EBETALK      ,  ! BETA LOCKOUT SYSTEM OPTION FLAG           [-]
     &  BLS_MANTRG(2),  !BLS manual mode Latch                      [-]
     &  ESPLS2       ,  ! BETA ECU Manual MODE+Igniters Microswitch [-]
     &  ESPLMSW9     ,  ! BETA LOCKOUT SYSTEM HORN Microswitch      [-]
     &	EFBLSA(2)    ,  ! BETA LOCKOUT SYSTEM ACTIVE                [-]
     &  EFBLSLEVALT(2), ! BETA LOCKOUT SYS THR and ALT condition    [-]
     &  FAFTR(2)     ,  ! A/F timer flag after uptrim               [-]
     &  FAFTRT(2)    ,  ! A/F timer flag for equation reduction     [-]
     &  FAFQX        ,  ! A/F low torque during A/F test            [-]
     &  FARM(2)      ,  ! ARM initial condition for A/F             [-]
     &  FARMI        ,  ! ARM initiation condition for A/F          [-]
     &  FARMS        ,  ! ARM sustain condition for A/F             [-]
     &  FAUTOC(2)    ,  ! Autofeather ARM switch signal             [-]
     &  FAUXP(2)     ,  ! Propeller aux. pressure pump signal       [-]
     &  FALTFEA(2)   ,  ! Alt. feather sw. @ FEATHER position       [-]
     &  FALTUNF(2)   ,  ! Alt. feather sw. @ UNFEATHER position     [-]
     &  FDESIM(2)    ,  ! ignition sw @ manual                      [-]
     &  FDESIO(2)    ,  ! ignition sw @ OFF                         [-]
     &  FECU(2)      ,  ! ECU POWER SUPPLY FLAG                     [-]
     &  FFS0(2)      ,  ! Fast start available flag                 [-]
     &  FHRENG       ,  ! Flag for reposition                       [-]
     &  FINTER       ,  ! Interlock ground signal                   [-]
     &  FMS(2)       ,  ! MINIMUM CONDITION FOR FLAME               [-]
     &  FMAS(2)      ,  ! ABSOLUTE CONDITION FOR FLAME              [-]
     &  FNH(2)       ,  ! NH SPEED SUSTAINING FLAG                  [-]
     &  FNHO(2)      ,  ! ECU / NH OVERSPEED DETECTION FLAG         [-]
     &  FNHTRI       ,  ! ECU OVERSPEED TEST TRIGGER SIGNAL         [-]
     &  FNHU(2)      ,  ! ECU / NH UNDERSPEED DETECTION FLAG        [-]
     &  FNHUS(2)     ,  ! ECU / NH UNDERSPEED DETECTION / FEATHER   [-]
     &  FPASS /.TRUE./ ,  ! First pass flag                         [-]
     &  FPSR(2)      ,  ! POWER SETTING RESET FLAG                  [-]
     &  FRATC        ,  ! Rating select switch position DECK C      [-]
     &  FRLT         ,  ! AMBIENT CONDITION RELIGHT FLAG            [-]
     &  FSCTESTI(2)  ,  ! EFSCTEST of previous iter.                [-]
     &  FSTRT(2)     ,  ! ECU / LOWER NH (25%) INTERLOCK            [-]
     &  FSYNC        ,  ! RLY SYNCHRO POWER SUPPLY FLAG             [-]
     &  FSYNCT       ,  !
     &  FT1CH1       ,
     &  FT1CH2       ,
     &  FT1CH3       ,
     &  FT1CH4       ,
     &  FT2CH1       ,
     &  FT2CH2       ,
     &  FT2CH3       ,
     &  FT2CH4       ,
     &  FTIM110      ,
     &  FTIM120      ,
     &  FTIM210      ,
     &  FTIM220      ,
     &  FTH(2)       ,  ! High torque signal for A/F
     &  FTL(2)       ,  ! Low torque signal for A/F
     &  FTOG(2)      ,  ! Take off flag ( pla at t/o and idestop )
     &  FTRDI(2)     ,  ! Feather discrete input for ecu nh underspeed
     &  FUPTRIM(2)   ,  ! Uptrim flag
     &  FWF(2)       ,  ! Fuel avail. for flame
     &  PFSTT(2)     ,  ! Previous state of start terminate flag
     &  PSELS(2)     ,  ! Previous state of start solenoid
     &  SELS(2)      ,  ! Start Select solenoid flag
     &  STO          ,  ! Pwr mgt sel sw @ TO
     &  SMCT         ,  ! Pwr mgt sel sw @ MCT
     &  SCRZ         ,  ! Pwr mgt sel sw @ CRZ
     &  SCLB         ,  ! Pwr mgt sel sw @ CLB
     &  SPL          ,  ! Scratch pad logic
     &  SPLS3DEL     ,  ! SPLS3 after time delay                            [-]
     &  SPLS4DEL     ,  ! SPLS4 after time delay                            [-]
     &  HORNCB          ! BETA WARN HORN CB                                 [-]
	




C'Constants
C
C    *************************************
C    *         C O N S T A N T S         *
C    *************************************
C     NOTE :  Constants name ending with a "T" are iteration time
C             dependent and are evaluated in the first pass section.
C
      REAL*4
     &   C3_100 /2.0/  , ! Quiet Taxi Mode trigging             [Deg]
     &   C3_102 /-10.0/ , ! CLA ANGLE MIN RPM - (MINUS)          [DEG.]
     &   C3_104 /-22./ , ! CLA ANGLE START                      [DEG.]
     &   C3_106 /39.1/ , ! CLA ANGLE MAX RPM                    [DEG.]
     &   C3_220 /35000./, ! Nh overspeed limit                  [RPM]
     &   !C3_222 / 8325./ ! ECU LOWER NH (25%) INTERLOCK         [RPM]
     &   C3_222 / -100./ ! ECU LOWER NH REMOVED ROY (-300)

C
      REAL*4
     &   C100 /0.1/    , ! Opening rate of
     &   C102 / 15.0 / , ! RLY MICRO BREAKS PROTECTION TDLY     [SEC.]
     &   C104 / 70.0 / , ! PROPELLER SPEED FOR SYNCHRO. OPER.   [%]
     &   C105 / 20.5 / , ! LOW PITCH IN FLIGHT MALF BETA TRIG PT[DEG]
     &   C106 /-41.0/  , ! HP FUEL SO VALVE INTERLOCK           [COEFF.]
     &   C107 /-43.5/  , ! HP FUEL SO VALVE CLOSED POSITION     [0-1]
     &   C109 /  3.  / , ! FUEL SO VALVE MOVEMENT CONTROL       [-]
     &   C109I         , ! FUEL SO VALVE MOVEMENT CONTROL       [-]
     &   C111 /14.6666/, ! FUEL SO VALVE MOVEMENT CONTROL       [-]
     &   C116 /25000./ , ! MAX. A/C OPERATING ALTITUDE          [FT]
     &   C118 /15000./ , ! A/C OPERATING ALTITUDE               [FT]
     &   C120 /125./   , ! A/C OPERATING SPEED                  [KNOTS]
     &   C122 /20000./ , ! A/C OPERATING ALTITUDE               [FT]
     &   C124 /165./   , ! A/C OPERATING SPEED                  [KNOTS]
     &   C132 / 60.0 / , ! A/C OPERATING SPEED (LO)             [KNOTS]
     &   C134 / 10.0 / , ! MIN. NH VALUE TO SUSTAIN FLAME       [%]
     &   C136 / 3.0  / , ! MIN. FUEL QUANT IN LINE FOR FLAME    [KG]
     &   C138 / 5.0  / , ! FAST START TIME                      [SEC.]
     &   C140 /-23.0/  , ! MECH. FTR VLV WRT CLA COCKPIT POS.   [DEG.]
     &   C142 /63.0/   , ! NH speed for select sw. to delatch   [%]
C
     &   C144 /4.0/    , ! CLA ANGLE MIN RPM                    [DEG.]
     &   C144A/-3.0/   , ! CLA ANGLE MIN RPM (RANGE FOR MICROSW)[DEG.]
     &   C145 /-10.0/  , ! CLA ANGLE MIN RPM - (MINUS)          [DEG.]
     &   C146 /-22./   , ! CLA ANGLE START                      [DEG.]
     &   C146A/-28./   , ! CLA ANGLE START (RANGE FOR MICROSW)  [DEG.]
     &   C147 /-35./   , ! CLA ANGLE 1/2(SF&SO)                 [DEG.]
     &   C148 /39.1/   , ! CLA ANGLE MAX RPM                    [DEG.]
C
     &   C150A/1.000/  , ! PLA ANGLE FLIGHT IDLE + 12           [DEG.]
     &   C150 /12.000/ , ! PLA ANGLE FLIGHT IDLE + 12           [DEG.]
     &   C151 /-3.000/ , ! PLA ANGLE FLIGHT IDLE - 3            [DEG.]
     &   C152 /28.000/ , ! PLA ANGLE FLIGHT IDLE + 28           [DEG.]
     &   C152a/22.000/ , ! PLA ANGLE FLIGHT IDLE + 28           [DEG.]
     &   C153 / 4.000/ , ! PLA ANGLE FLIGHT IDLE                [DEG.]
     &   C154 /-4.000/ , ! PLA ANGLE FLIGHT IDLE (FOR MICROSW)  [DEG.]
     &   C155 /-1.000/ , ! PLA ANGLE FLIGHT IDLE                [DEG.]
C
     &   C160 /50.0/   , ! IND TORQUE TO ARM/DISARM             [%]
     &   C162 /22.0/   , ! IND TORQUE TO TRIGGER UPTRIM         [%]
     &   C163 /50.0/   , ! IND TORQUE TO TRIGGER UPTRIM         [%]
     &   C164 /3.0/    , ! TIME DELAY BETWEEN UPTRIM & A/F      [SEC]
     &   C166 /180.0 / , ! NP SPEED FOR NP GOVER CANCEL (DECR)  [RPM]
     &   C168 /360.0 / , ! NP SPEED FOR NP GOVER CANCEL (INCR)  [RPM]
     &   C170 /15.0/   , ! D-100 time delay on Aux. Pressure pump [sec]
     &   C172 /-40./   , ! CLA position for fast start available
C
     &   C220 /33966./ , ! ECU NH OVERSPEED LOGIC VALUE         [RPM]
     &   C221 /20004./ , ! ECU NH UNDERSPEED LOGIC VALUE        [RPM]
     &   C222 / 9990./ , ! ECU LOWER NH (30%) INTERLOCK         [RPM]
     &   C223 / 10.0 / , ! ECU UNDERSPEED PULSE COUNTER         [ITER.]
     &   C224 / 500. / , ! ECU NH MIN VALUE FOR FNHU TO BE TRUE [RPM]
C
     &   C230 /-2.5/   , ! Beta switch setting                  [Deg]
     &   C232 /16.0/   , ! Beta switch setting                  [Deg]
     &   C300 /1000.0/ , ! trigger speed for beta lockout option[RPM]
     &   C310 /0.5/    , ! HORN S9 microswitch anti-bounce delay[s]
     &   C311 /3.3/    , ! NP > 1000 IND threshold              [-]
C                        ! (NP IND Test button pressed
C                        ! (AMM 61-42-00-00)

     &   KSPLS3T /2.0/ , ! SPLS3 time delay                     [s]
     &   KSPLS4T /2.0/ , ! SPLS4 time delay                     [s]
     &   SPLS3T        , ! SPLS3 TIMER                          [s]
     &   SPLS4T          ! SPLS4 TIMER                          [s]
C
C'Entry_Point
C
      ENTRY  ENGL

CD  **************************
CD  *  MODULE FREEZE FLAGS   *
CD  **************************
CD'
C
      IF ( TCFENG .OR. EFZEL ) RETURN
C
C
CD  ****************************
CD  *  FIRST PASS CALCULATION  *
CD  ****************************
CD'
C
        IF ( FPASS ) THEN
           FPASS  =  .FALSE.

           D100 = YITAIL .EQ. 226      ! DASH 8 / SERIE 100
           D300 = YITAIL .EQ. 230      ! DASH 8 / SERIE 300

           DT     =  YITIM
           DTINV  =  1.0 / DT

           IF( D100 )THEN
             C170 = 15.0
           ELSE IF( D300 )THEN
             C170 = 18.0
             C220 = C3_220
           ENDIF

           C109I = 1./C109

           DFSR(1) = 0.
           DFSR(2) = 0.
           FNHU(1) = .FALSE.
           FNHU(2) = .FALSE.

           EBETALK = .TRUE.
        ENDIF
C


CD  ::::::::::::::::::::::::::::::::::::::::::::::::::::::
CD  ::   POWER & CONDITION LEVER ACTUATOR MICROSWITCH   ::
CD  ::::::::::::::::::::::::::::::::::::::::::::::::::::::
CD'

CD100   CONDITION LEVER MICR.   [-]
C
        IF( D100 )THEN
C
          ESCLS3  = ( ECLACTL(1) .GE. C148 )      ! .OR. IDESCLS3
          ESCLS4  = ( ECLACTL(2) .GE. C148 )      ! .OR. IDESCLS4
          ESCLS3P = ( ECLACTL(1) .GE. C147 )      ! .OR. IDESCS3P
          ESCLS4P = ( ECLACTL(2) .GE. C147 )      ! .OR. IDESCS4P
          ESCLS7  = ECLACTL(1) .LE. C146
          ESCLS8  = ECLACTL(2) .LE. C146
          ESCLS14 = ( ECLACTL(2) .LE. C145 )      ! .OR. IDESCS14
          ESCLS15 = ( ECLACTL(1) .LE. C145 )      ! .OR. IDESCS15
          ESCLMN1 = ( ECLACTL(1) .LE. C144 .AND.
     &              ECLACTL(1) .GE. C144A )     ! .OR. IDESCS15
          ESCLMN2 = ( ECLACTL(2) .LE. C144 .AND.
     &              ECLACTL(2) .GE. C144A )      ! .OR. IDESCS14
C
        ELSE IF( D300 )THEN
C
          ESCLS3  = ( ECLACTL(1) .GE. C3_106 )      ! .OR. IDESCLS3
          ESCLS4  = ( ECLACTL(2) .GE. C3_106 )      ! .OR. IDESCLS4
          ESCLS5  = ( ECLACTL(1) .GE. C3_100 )      ! .OR. IDESCLS5
          ESCLS6  = ( ECLACTL(2) .GE. C3_100 )      ! .OR. IDESCLS6
          ESCLS7  = ( ECLACTL(1) .LE. C3_104 )
          ESCLS8  = ( ECLACTL(2) .LE. C3_104 )
          ESCLS14 = ( ECLACTL(2) .LE. C3_102 )      ! .OR. IDESCS14
          ESCLS15 = ( ECLACTL(1) .LE. C3_102 )      ! .OR. IDESCS15
C
        ENDIF
C
CD110   POWER LEVER MICR.   [-]
C
        ESPLS1  = ( EPLACTL(1) .GE. C150 )      ! .OR. IDESPLS1
        ESPLS2  = ( EPLACTL(1) .GE. C155 ) .AND.
     &            ( EPLACTL(2) .GE. C155 )
C
C REF: [4], 61-41-01
C  , 31-53-01
C      Email from the 2U0C client:
C From: Kowach, Roy [mailto:<EMAIL>]
C Sent: November 26, 2012 9:57 AM
C To: David Romero
C Cc: Fowler, Jamie
C Subject: RE: 2U0C- BLS microswitches clarifications
C
C David,
C Preliminary research (Bombardier e-mail) indicates that the failure needed to
C Regards,
C Roy
C
C
        ESPLS3  = ( EPLACTL(1) .GE. C155 ) .AND.
     &            .NOT. TF71614	!BETA LOCKOUT SYSTEM MALFUNCTION
C
C
C       BETA LOCK OUT horn S9 microswitch anti-bounce
C
        IF ( IDESPS09 ) THEN
            DHORNT = AMAX1( 0.0, DHORNT - DT )
        ELSE IF ( .NOT. IDESPS09 ) THEN
            DHORNT = C310
        ENDIF
        ESPLMSW9 = (DHORNT .LE. 0.0)
C
        ESPLS4  = ( EPLACTL(2) .GE. C155 ) .AND.
     &            .NOT. TF71615	!BETA LOCKOUT SYSTEM MALFUNCTION
        ESPLS6  = ( EPLACTL(2) .LE. C150A )      ! .OR. IDESPLS6
        ESPLS6H = ( EPLACTL(2) .GE. C152 )      ! .OR. IDESPS6H
        ESPLS7  = ( EPLACTL(1) .LE. C150A ) ! .OR. IDESPLS7
        ESPLS7H = ( EPLACTL(1) .GE. C152 )      ! .OR. IDESPS7H
        ESPLS8  = ( EPLACTL(2) .LE. C153 .AND.
     &              EPLACTL(2) .GE. C154 )      ! .OR. IDESPLS8
        ESPLS9  = ( EPLACTL(1) .LE. C153 .OR.
     &              EPLACTL(2) .GE. C154 )      ! .OR. IDESPLS9
        ESPLS9F = ( EPLACTL(1) .GE. C152 )      ! .OR. IDESPS9F
        ESPLS10 = ( EPLACTL(2) .LE. C151 )      ! .OR. IDESPS10
        ESPLS10F= ( EPLACTL(2) .GE. C152 )      ! .OR. IDESS10F
        ESPLS11 = ( EPLACTL(1) .LE. C151 )      ! .OR. IDESPS11
        ESPLS12 = ( EPLACTL(2) .LE. C152a )     ! .OR. IDESPS12
        ESPLS13 = ( EPLACTL(1) .LE. C152a )     ! .OR. IDESPS13
        ESPLS23 = ( EPLACTL(1) .LE. C150 )      ! .OR. IDESPS23
        ESPLS24 = ( EPLACTL(2) .LE. C150 )      ! .OR. IDESPS24

CD  ::::::::::::::::::::::::::::::::::::
CD  ::   ENGINE INTAKE BYPASS DOORS   ::
CD  ::       AND ADAPTER HEATERS      ::
CD  ::   REF[1]: Page 101             ::
CD  ::           Sch. 30-21-00        ::
CD  ::::::::::::::::::::::::::::::::::::
CD'

CD200   OUTSIDE AIR THERMOSTAT

        ETS3 = ETT1 .LT. 15.6

        IF ( ETS3 ) THEN
          ER1K1 = ESLOP(1) .AND. IDESBY .AND. BIRB05
          ER1K2 = ESLOP(2) .AND. IDESBY2 .AND. BIRC05
        ELSE
          ER1K1 = .FALSE.
          ER1K2 = .FALSE.
        ENDIF

        ER1K3 = BIVA05 .AND. ER1K1 .AND. .NOT.TF30351
        ER1K4 = BIVF05 .AND. ER1K2 .AND. .NOT.TF30352

        IF ( IDESBY .AND. BIRB05 .AND. .NOT.TF30251 ) THEN
          RTRBY(1) = C100
        ELSE IF ( .NOT.IDESBY .AND. BIRB05 .AND. .NOT.TF30241 ) THEN
          RTRBY(1) = -C100
        ELSE
          RTRBY(1) = 0.0
        ENDIF
        IF ( IDESBY2 .AND. BIRC05 .AND. .NOT.TF30252 ) THEN
          RTRBY(2) = C100
        ELSE IF ( .NOT.IDESBY2 .AND. BIRC05 .AND. .NOT.TF30242 ) THEN
          RTRBY(2) = -C100
        ELSE
          RTRBY(2) = 0.0
        ENDIF

        LACTBY(1) = AMIN1( 1.0, AMAX1( 0.0, LACTBY(1) + RTRBY(1) ))
        LACTBY(2) = AMIN1( 1.0, AMAX1( 0.0, LACTBY(2) + RTRBY(2) ))

        IF( BIRB05 ) THEN
          ED$BYO  = LACTBY(1) .EQ. 1.0
          ED$BYC  = LACTBY(1) .EQ. 0.0
          ED$BYH  = LACTBY(1) .EQ. 1.0 .AND. ER1K3
        ELSE
          ED$BYO  = .FALSE.
          ED$BYC  = .FALSE.
          ED$BYH  = .FALSE.
        ENDIF

        IF( BIRC05 ) THEN
          ED$BYO2 = LACTBY(2) .EQ. 1.0
          ED$BYC2 = LACTBY(2) .EQ. 0.0
          ED$BYH2 = LACTBY(2) .EQ. 1.0 .AND. ER1K4
        ELSE
          ED$BYO2 = .FALSE.
          ED$BYC2 = .FALSE.
          ED$BYH2 = .FALSE.
        ENDIF


CD  :::::::::::::::::::::::::::::::::
CD  ::   PROPELLER DEICE SYSTEM    ::
CD  ::   REF[1]: Page 103/104      ::
CD  ::           Sch. 30-61-00     ::
CD  :::::::::::::::::::::::::::::::::

CD300  PROP DE-ICE CB TRIP MALFUNCTION LOGIC  [-]

        BPLR09 = TF30391 .AND. BILR09 .AND. ( IDESPT1A .OR. IDESPT1B )
        BPRB09 = TF30392 .AND. BIRB09 .AND. ( IDESPT2A .OR. IDESPT2B )

        FTIM110 = IDESPT1A .AND. BILR09 .AND. .NOT.IDESPT1B
        FTIM120 = IDESPT1B .AND. BILR09 .AND. .NOT.IDESPT1A
        FTIM210 = IDESPT2A .AND. BIRB09 .AND. .NOT.IDESPT2B
        FTIM220 = IDESPT2B .AND. BIRB09 .AND. .NOT.IDESPT2A

        IF ( FTIM110 ) THEN
          IF ( TIMPT1A .GT. 70. ) TIMPT1A = 0.0
          TIMPT1A = TIMPT1A + DT
          FT1CH1 = TIMPT1A .LE. 10.0
          FT1CH2 = TIMPT1A .GT. 10.0 .AND.
     &           TIMPT1A .LE. 20.0
          FT1CH3 = TIMPT1A .GT. 20.0 .AND.
     &           TIMPT1A .LE. 30.0
          FT1CH4 = TIMPT1A .GT. 30.0 .AND.
     &           TIMPT1A .LE. 40.0
        ELSE IF ( FTIM120 ) THEN
          IF ( TIMPT1A .GT. 80. ) TIMPT1A = 0.0
          TIMPT1A = TIMPT1A + DT
          FT1CH1 = TIMPT1A .LE. 20.0
          FT1CH2 = TIMPT1A .GT. 20.0 .AND.
     &           TIMPT1A .LE. 40.0
          FT1CH3 = TIMPT1A .GT. 40.0 .AND.
     &           TIMPT1A .LE. 60.0
          FT1CH4 = TIMPT1A .GT. 60.0 .AND.
     &           TIMPT1A .LE. 80.0
        ELSE
          TIMPT1A = 0.0
          FT1CH1  = .FALSE.
          FT1CH2  = .FALSE.
          FT1CH3  = .FALSE.
          FT1CH4  = .FALSE.
        ENDIF

        IF ( FTIM210 ) THEN
          IF ( TIMPT2A .GT. 70. ) TIMPT2A = 0.0
          TIMPT2A = TIMPT2A + DT
          FT2CH1 = TIMPT2A .LE. 10.0
          FT2CH2 = TIMPT2A .GT. 10.0 .AND.
     &           TIMPT2A .LE. 20.0
          FT2CH3 = TIMPT2A .GT. 20.0 .AND.
     &           TIMPT2A .LE. 30.0
          FT2CH4 = TIMPT2A .GT. 30.0 .AND.
     &           TIMPT2A .LE. 40.0
        ELSE IF ( FTIM220 ) THEN
          IF ( TIMPT2A .GT. 80. ) TIMPT2A = 0.0
          TIMPT2A = TIMPT2A + DT
          FT2CH1 = TIMPT2A .LE. 20.0
          FT2CH2 = TIMPT2A .GT. 20.0 .AND.
     &           TIMPT2A .LE. 40.0
          FT2CH3 = TIMPT2A .GT. 40.0 .AND.
     &           TIMPT2A .LE. 60.0
          FT2CH4 = TIMPT2A .GT. 60.0 .AND.
     &           TIMPT2A .LE. 80.0
        ELSE
          TIMPT2A = 0.0
          FT2CH1  = .FALSE.
          FT2CH2  = .FALSE.
          FT2CH3  = .FALSE.
          FT2CH4  = .FALSE.
        ENDIF

        ER2K5 = ( IDESPT2A .OR. IDESPT2B ) .AND. BIRB09

        IF ( ER2K5 ) THEN
          ER2K1 = ( .NOT.AGFPS59 .OR. IDESPRT ) .AND. FT2CH1
          ER2K3 = ( .NOT.AGFPS59 .OR. IDESPRT ) .AND. FT2CH3
          ER2K2 = ( .NOT.AGFPS60 .OR. IDESPRT ) .AND. FT2CH2
          ER2K4 = ( .NOT.AGFPS60 .OR. IDESPRT ) .AND. FT2CH4
        ELSE
          ER2K1 = ( .NOT.AGFPS59 .OR. IDESPRT ) .AND. FT1CH1
          ER2K3 = ( .NOT.AGFPS59 .OR. IDESPRT ) .AND. FT1CH3
          ER2K2 = ( .NOT.AGFPS60 .OR. IDESPRT ) .AND. FT1CH2
          ER2K4 = ( .NOT.AGFPS60 .OR. IDESPRT ) .AND. FT1CH4
        ENDIF

        ER2K6 = BIVE07 .AND. ER2K4 .AND. .NOT.TF30232
        ER2K7 = BIVB07 .AND. ER2K3 .AND. .NOT.TF30231
        ER2K8 = BIVD07 .AND. ER2K2 .AND. .NOT.TF30222
        ER2K9 = BIVA07 .AND. ER2K1 .AND. .NOT.TF30221

        EFPBBH1(1) = ER2K9
        EFPBBH1(2) = ER2K8
        EFPBBH2(1) = ER2K7
        EFPBBH2(2) = ER2K6

        IF ( BILR09 .OR. BIRB09 ) THEN
          ED$PB113 = ER2K9
          ED$PB213 = ER2K8
          ED$PB124 = ER2K7
          ED$PB224 = ER2K6
        ELSE
          ED$PB113 = .FALSE.
          ED$PB213 = .FALSE.
          ED$PB124 = .FALSE.
          ED$PB224 = .FALSE.
        ENDIF

CD  ::::::::::::::::::::::::::::::::::::::::::::
CD  ::   ENGINE HP & PROP. OVERSPEED TESTS    ::
CD  ::   REF[1]: Page 101                     ::
CD  ::           Sch. 61-22-00                ::
CD  ::::::::::::::::::::::::::::::::::::::::::::

CD400   PROPELLER OVERSPEED GOVERNOR SIGNAL   [-]

        EFPOV(1)  = IDESOVP .AND. BILS09
        EFPOV(2)  = EFPOV(1)

        IF ( D100 ) THEN

CD410   ECU OVERSPEED TEST SIGNAL   [-]
C       NOTE: signal for connector 7700-P3 pin 12

          EFNHOS(1) = ( (ENH(1) .GE. 75.) .AND. IDESOVH
     &                 .OR. ( EFNHOS(1) .AND. IDESOVH ))
     &                 .AND. BILS09 .AND. .NOT. TF71641
          EFNHOS(2) = ( (ENH(2) .GE. 75.) .AND. IDESOVH2
     &                 .OR. ( EFNHOS(2) .AND. IDESOVH ))
     &                 .AND. BILS09 .AND. .NOT. TF71642


CD420   ECU OVERSPEED TEST TRIGGER SIGNAL   [-]

CJD          FNHTRI = IDESOVH .AND. BILS09

CD430   ECU OVERSPEED TEST RESET RELAY SIGNAL   [-]

          IF ( .NOT.IDESOVH .AND. DESVOHI ) THEN
            ETIMRK3 = 0.1
            ETIMRK4 = 0.1
          ELSE
            ETIMRK3 = AMAX1( 0.0, ETIMRK3 - DT )
            ETIMRK4 = AMAX1( 0.0, ETIMRK4 - DT )
          ENDIF
          DESVOHI = IDESOVH

          ER3K3(1) = EFNHOS(1) .AND. ETIMRK3 .LE. 0.0 .AND. BILK04
          ER3K3(2) = EFNHOS(2) .AND. ETIMRK4 .LE. 0.0 .AND. BIRH03

CJD       ER3K3 = FNHTRI .AND. ETIMRK3 .LE. 0.0
CJD       ER3K4 = FNHTRI .AND. ETIMRK4 .LE. 0.0

        ENDIF


CD  :::::::::::::::::::::::::::::::::::::::::::::::::::::
CD  ::   PROP. AUTOFEATHER CONTROL SYSTEM  ( SER 300 ) ::
CD  ::   REF[2]: Page 111/112                          ::
CD  ::           Sch. 61-21-01                         ::
CD  :::::::::::::::::::::::::::::::::::::::::::::::::::::

        IF( D300 ) THEN

CD450   AUTOFEATHER ARMING LEVEL SIGNAL   [-]

          DO I = 1,2
            PLAARM(I) = 0.288 * ETT1 + 34.0
          ENDDO

CD460   AUTOFEATHER ARM SWITCH SIGNAL   [-]

          FAUTOC(1) = BILG10 .AND. ( EPLACTL(1) .GE. PLAARM(1) )
          FAUTOC(2) = BIRG10 .AND. ( EPLACTL(2) .GE. PLAARM(2) )

        ENDIF


CD  ::::::::::::::::::::::::::::::::::::::::::::::
CD  ::   PROP. AUTOFEATHER & ALTERNATE SYSTEM   ::
CD  ::   REF[1]: Page 101/102                   ::
CD  ::           Sch. 61-25-00                  ::
CD  ::::::::::::::::::::::::::::::::::::::::::::::

CD500   MECHANICAL FEATHER VALVE   [-]
C       Note: Signal for the mechanical feather valve.

        DO  I = 1,2
          IF ( (ECLACTL(I) .GT. C140) .OR. TF71621(I) ) THEN
            EVMF(I)  = 0.0
            EFEAM(I) = .FALSE.
          ELSE
            EVMF(I)  = 1.0
            EFEAM(I) = .TRUE.
          ENDIF
        ENDDO

CD510   ALTERNATE FEATHER SWITCH @ "FEATHERED" POSITION SIGNAL  [-]

        IF ( BIRQ04 ) THEN
          FALTFEA(1) = IDESALF  .AND. ESCLS15 .AND. .NOT.ESPLS11
          FALTFEA(2) = IDESALF2 .AND. ESCLS14 .AND. .NOT.ESPLS10

CD520   ALTERNATE FEATHER SWITCH @ "UNFEATHERED" POSITION SIGNAL  [-]

          FALTUNF(1) = IDESALU  .AND. .NOT.ESPLS11
          FALTUNF(2) = IDESALU2 .AND. .NOT.ESPLS10
        ELSE
          FALTFEA(1) = .FALSE.
          FALTFEA(2) = .FALSE.
          FALTUNF(1) = .FALSE.
          FALTUNF(2) = .FALSE.
        ENDIF

CD530   PROPELLER TEST RELAY SIGNAL  [-]
C !FM+
C !FM   6-Mar-94 01:47:45 Tom Miller
C !FM    < COA S81-2-067>
C !FM    < Autofeather test should only work when >
C !FM    < both PL's are at FLT IDLE! >
C !FM

C        ER4K3  = BILL06 .AND. IDESAFT .AND. IDESAF .AND. ESPLS7
C        ER4K4  = BIRK05 .AND. IDESAFT2 .AND. IDESAF .AND. ESPLS6

        ER4K3  = BILL06 .AND. IDESAFT .AND. IDESAF .AND.
     &             ESPLS7 .AND. ESPLS8 .AND. ESPLS9
        ER4K4  = BIRK05 .AND. IDESAFT2 .AND. IDESAF .AND.
     &             ESPLS6 .AND. ESPLS8 .AND. ESPLS9
C !FM-

C !FM+
C !FM   8-Aug-93 07:48:47 s.gould
C !FM    < added code for t/o warning horn as per attachments sent to field >
C !FM    < changed engines switches used to set Horn Relay from
C !FM      PLA < FLT IDLE -3  to PLA > FLT IDEL +12 after confirmation from
C !FM      the Operation Manuals Dash 8 - 300. >
C !FM
CD535   T/O WARNING HORN RELAY SIGNAL  [-]

        IF ( D300 ) THEN
CC          SPL = BIRK05 .AND. ( ER4K3 .OR. ER4K4 .OR.
CC     &                       ( BIRQ04 .AND. (ESPLS10 .OR. ESPLS11) ))
CC
          SPL = BIRK05 .AND. ( ER4K3 .OR. ER4K4 .OR.
C !FM+
C !FM   3-Sep-93 03:36:37 W. Pin
C !FM    < Use correct label for PLA < FLT IDLE +12. >
C !FM
CWP    &                 ( BIRQ04 .AND. (ESPLS6 .AND. ESPLS7) ))
     &                 ( BIRQ04 .AND. (ESPLS23 .AND. ESPLS24) ))
C !FM-
C
          IF ( SPL ) THEN
            TIM1 = 2.0
          ELSE
            TIM1 = AMAX1( 0.0, TIM1 -DT )
          ENDIF
C
          ER4K17 = TIM1 .GT. 0.0
        ENDIF
C
C !FM-

CD540   ENGINE INTERLOCK RELAY SIGNAL  [-]

        FINTER = ( .NOT.(ESPLS6 .AND. ESPLS7) .AND. IDESAF )  .OR.
     &              ER4K3 .OR. ER4K4

        ER4K2  = EFSCUFE(2) .AND. .NOT.ER4K1 .AND. FINTER
        ER4K1  = EFSCUFE(1) .AND. .NOT.ER4K2 .AND. FINTER


CD550   ARM CONTROL RELAY SIGNAL  [-]

        ER4K5  = EFARM(2)


CD560   ENGINE POWER UPTRIM RELAY SIGNAL  [-]

        IF ( D100 ) THEN
          ER4K10 = ( EFFAIL(2) .OR. ( BIRK05 .AND. ER6K4 .AND.
     &               .NOT. TF71631(2) ) ) .AND.
     &               .NOT.ER4K1 .AND. FINTER
          ER4K11 = ( EFFAIL(1) .OR. ( BILL06 .AND. ER6K5 .AND.
     &               .NOT. TF71631(1) ) ) .AND.
     &               .NOT.ER4K2 .AND. FINTER
        ELSE IF ( D300 ) THEN
          ER4K16 = ( EFFAIL(2) .OR. ( BIRK05 .AND. ER6K4 .AND.
     &               .NOT. TF71631(2) ) ) .AND.
     &               .NOT.ER4K1 .AND. FINTER
          ER4K15 = ( EFFAIL(1) .OR. ( BILL06 .AND. ER6K5 .AND.
     &               .NOT. TF71631(1) ) ) .AND.
     &               .NOT.ER4K2 .AND. FINTER
        ENDIF


CD570   UPTRIM CANCEL RELAY SIGNAL  [-]

        ER4K12 = BIRQ04 .AND. .NOT.IDESAF


CD580   ENGINE FEATHER RELAY ( SERIE-300 )   [-]

        IF ( D300 .AND. .NOT.ER4K12 ) THEN
          ER4K13 = BIRK05 .AND. ER4K2      .OR.
     &             BIRQ04 .AND. ESCLS14 .AND. IDESALF2
     &             .AND. .NOT.ESPLS10
          ER4K14 = BILL06 .AND. ER4K1      .OR.
     &             BIRQ04 .AND. ESCLS15 .AND. IDESALF
     &             .AND. .NOT.ESPLS11
        ELSE
          ER4K13 = .FALSE.
          ER4K14 = .FALSE.
        ENDIF


CD590   ENGINE SCU 28V DC HI SIGNAL  [-]
C       NOTE: signal for connector 7700-P3 pin 40

        EFSCU(1) = BILL06
        EFSCU(2) = BIRK05


CD600   PROPELLER SIMULATION TORQUE >50. DURING TEST SIGNAL  [-]
C       NOTE: signal for connector 7700-P3 pin 57

        EFSCTEST(1) = ER4K3
        EFSCTEST(2) = ER4K4

CD610   PROPELLER AUXILIARY PRESSURE PUMP RELAY SIGNAL  [-]

        SPL = FALTUNF(1)  .OR.  EFFE(1)

        IF ( SPL ) THEN
          ETIM4K9 = AMIN1( C170, ETIM4K9 + DT )
          ER4K9   = ETIM4K9 .GE. C170
        ELSE
          ETIM4K9 = 0.0
          ER4K9   = .FALSE.
        ENDIF
        ER4K7 = SPL .AND. .NOT. ER4K9

        SPL = FALTUNF(2)  .OR.  EFFE(2)

        IF ( SPL ) THEN
          ETIM4K8 = AMIN1( C170, ETIM4K8 + DT )
          ER4K8   = ETIM4K8 .GE. C170
        ELSE
          ETIM4K8 = 0.0
          ER4K8   = .FALSE.
        ENDIF
        ER4K6 = SPL .AND. .NOT. ER4K8


CD620   PROPELLER AUXILIARY PRESSURE PUMP SIGNAL  [-]

        FAUXP(1) = BIDRSC .AND. BILS04 .AND. ER4K7 .AND. .NOT.ER4K9
     &             .AND. .NOT. TF71631(1)
        FAUXP(2) = BIDLSC .AND. BIRA04 .AND. ER4K6 .AND. .NOT.ER4K8
     &             .AND. .NOT. TF71631(2)

        FAUXP(1) = BIDRSC .AND. BILS04 .AND. ER4K7 .AND. .NOT.ER4K9
        FAUXP(2) = BIDLSC .AND. BIRA04 .AND. ER4K6 .AND. .NOT.ER4K8

        IF ( FAUXP(1) ) THEN
          EPPAUX(1) = 1.0
        ELSE
          EPPAUX(1) = 0.0
        ENDIF
        IF ( FAUXP(2) ) THEN
          EPPAUX(2) = 1.0
        ELSE
          EPPAUX(2) = 0.0
        ENDIF


CD630   PROPELLER FEATHER VALVE SOLENOID SIGNAL [-]

        EFFE(1) = BILL06 .AND. ER4K1       .OR.
     &            FALTFEA(1)

        EFFE(2) = BIRK05 .AND. ER4K2       .OR.
     &            FALTFEA(2)


CD640   PROPELLER FEATHER VALVE POSITION  [-]
C       Note: Signal for the electrical feather valve.

        DO I = 1,2
          IF ( ( EFFE(I) .OR. EFBB(I) .OR. TF71631(I) )
     &         .AND. (.NOT. TF71271(I)) ) THEN
            EVFPE(I) = 1.0
          ELSE
            EVFPE(I) = 0.0
          ENDIF


CD650   PROPELLER NP FUEL UNDERSPEED GOVERNING CANCEL SIGNAL [-]
C       NOTE: signal for connector 7700-P3 pin 21

          IF( ENPG(I) .GT. C168 ) THEN
            XNPG(I) = C166
          ELSE
            XNPG(I) = C168
          ENDIF
	ENDDO

        IF( EFAST(1) ) THEN
          EFNPL(1) = .FALSE.
        ELSE IF( ENPG(1) .GT. XNPG(1) ) THEN
          EFNPL(1) = BILK04 .AND. ESCLS7   .OR.   ! CLA switch @ St & Feather
     &             BILL06 .AND. ER4K3      .OR.   ! During Autofeather test
     &             BIRK05 .AND. ER4K4      .OR.   ! During Autofeather test
     &             EFFE(1)                 .OR.   ! During Autofeather
C
C 25 March 2006  Tom Miller  Gripe # 23943
C
C This gripe comes up every three years or so and was complained about
C during acceptance way back in 1992 at CAE.
C
C Included uncommanded feather malfunction to give the indication
C crews are used to seeing when this happens (3 times recently).
C It was that under 140K, the torque would stay high and oscillate for
C a while when the "bad" engine PL was pulled back.  Same change to
C engine #2 below.
C
     &             TF71631(1)                     ! During FX valve fault
        ELSE
          EFNPL(1) = .TRUE.
        ENDIF

        IF( EFAST(2) ) THEN
          EFNPL(2) = .FALSE.
        ELSE IF( ENPG(2) .GT. XNPG(2) ) THEN
          EFNPL(2) = BIRH03 .AND. ESCLS8   .OR.
     &             BILL06 .AND. ER4K3      .OR.
     &             BIRK05 .AND. ER4K4      .OR.
     &             EFFE(2)                 .OR.
     &             TF71631(2)
        ELSE
          EFNPL(2) = .TRUE.
        ENDIF
C
CD660   PROPELLER FEATHERING SIGNAL   [-]
C       NOTE: signal for connector 7700-P3 pin 21

       IF( D100 ) THEN
         EFPFS(1) = EFNPL(1)              .OR.   ! NP fuel underspeed gov.
     &              ER6K5                        ! Overtorque from indicator
         EFPFS(2) = EFNPL(2)              .OR.
     &              ER6K4

CD662   AUTOFEATHER ENABLE SIGNAL ( LOCAL & OPPOSITE )    [-]
C       NOTE: signal for connector 7700-P3 pin 03 & 27

        IF ( IDESAF ) THEN
          EFAFS(1)   = .NOT.ESPLS13
     &                 .OR. ( IDESAFT  .AND. ESPLS8 .AND. ESPLS9 )
          EFAFS(2)   = .NOT.ESPLS12
     &                 .OR. ( IDESAFT2 .AND. ESPLS8 .AND. ESPLS9 )
          EFSCUAF(1) = EFAFS(2)
          EFSCUAF(2) = EFAFS(1)
        ELSE
          EFAFS(1)   = .FALSE.
          EFAFS(2)   = .FALSE.
          EFSCUAF(1) = .FALSE.
          EFSCUAF(2) = .FALSE.
        ENDIF


       ELSE IF( D300 ) THEN

CD664   PROPELLER FEATHERING SIGNAL   [-]
C       NOTE: signal for connector 7700-P4 pin F

         EFPFS(1) = ESCLS7 .OR. ER53K7
         EFPFS(2) = ESCLS8 .OR. ER53K8

CD666   AUTOFEATHER ENABLE SIGNAL ( LOCAL & OPPOSITE )    [-]
C       NOTE: signal for connector 7700-P3 pin M_ & W_

         IF ( IDESAF ) THEN
           EFAFS(1)   = FAUTOC(1)
     &                  .OR. ( IDESAFT  .AND. ESPLS8 .AND. ESPLS9 )
           EFAFS(2)   = FAUTOC(2)
     &                  .OR. ( IDESAFT2 .AND. ESPLS8 .AND. ESPLS9 )
           EFSCUAF(1) = EFAFS(2)
           EFSCUAF(2) = EFAFS(1)
         ELSE
           EFAFS(1)   = .FALSE.
           EFAFS(2)   = .FALSE.
           EFSCUAF(1) = .FALSE.
           EFSCUAF(2) = .FALSE.
         ENDIF
        ENDIF



CD680   AUTOFEATHER SELECTION : ARM INITIATION CONDITON  [-]

        FARMI = ( EFAFS(1) .AND. EFAFS(2) )
     &                    .OR.
     &          ( EFSCTEST(1) .OR. EFSCTEST(2) )

CD690   AUTOFEATHER SELECTION : ARM SUSTAIN CONDITON  [-]
C
        FARMS = ( EFAFS(1) .OR. EFAFS(2) )
     &                    .OR.
     &          ( EFSCTEST(1) .OR. EFSCTEST(2) )

CD695   AUTOFEATHER LOW TORQUE DURING A/F TEST  [-]

        FAFQX = ( ( EFSCTEST(1) .AND. .NOT.FSCTESTI(1) )
     &                          .AND.
     &            ( EFSCTEST(2) .AND. .NOT.FSCTESTI(2) ))
     &                     .OR.
     &          ( FAFQX .AND. ( EFSCTEST(1) .OR. EFSCTEST(2) ))

        FSCTESTI(1) = EFSCTEST(1)
        FSCTESTI(2) = EFSCTEST(2)


        DO I = 1,2

CD700   AUTOFEATHER SELECTION : EQUATION REDUCTION   [-]

          FAFTRT(I) = FARMS .AND. ( DAFTR(I) .LE. 0.0 )

CD710   AUTOFEATHER SELECTION : HIGH AND LOW TORQUE  [-]

          FTH(I) = EQTX(I) .GT. C160
          IF( FAFQX ) THEN
            FTL(I) = EQTX(I) .LT. C163
          ELSE
            FTL(I) = EQTX(I) .LT. C162
          ENDIF

CD720   AUTOFEATHER SELECTION : ARM CONDITON  [-]
C

        IF( .NOT. EFSCU(I) )THEN
          FARM(I) = .FALSE.
        ELSE IF ( TF71201(I) ) THEN
          FARM(I) = .FALSE.
        ELSE IF( FARMI )THEN
          FARM(I) = ( FTH(1) .AND. FTH(2) .AND. .NOT. EFSCUFE(I) )
     &                          .OR.
     &              ( FARM(I) .AND. .NOT. EFSCUFE(I) )
        ELSE
          FARM(I) = FARM(I) .AND. FAFTRT(I)
        ENDIF


CD730   AUTOFEATHER SELECTION : UPTRIM CONDITON  [-]

          FUPTRIM(I) = ( FARM(I) .AND. FTL(I) .AND. .NOT.TF71211(I) )
     &                             .OR.
     &                 ( FUPTRIM(I) .AND. FARMS )

CD740   AUTOFEATHER SELECTION : A/F TIMER  [-]

          IF ( FARM(I) .AND. FTL(I) ) THEN
            DAFTR(I) = AMAX1( 0.0, DAFTR(I) - DT )
          ELSE IF ( .NOT. FAFTR(I) ) THEN
            DAFTR(I) = C164
          ENDIF

CD750   AUTOFEATHER SELECTION : AUTOFEATHER SIGNAL   [-]

          FAFTR(I) = ( FARM(I) .AND. FTL(I) .AND. .NOT.TF71211(I) .AND.
     &                 ( DAFTR(I) .LE. 0.0 ) )
     &                         .OR.
     &               ( FAFTR(I) .AND. FARMS )

CD760   AUTOFEATHER SELECTION : ARM CONDITON  [-]
C       NOTE: signal for connector 7700-P3 pin 50

          EFARM(I) = FARM(I)

CD770   AUTOFEATHER SELECTION : "FAIL" LIGHT SIGNAL  [-]
C       NOTE: signal for connector 7700-P3 pin 42

         EFFAIL(I) = FUPTRIM(I)

CD780   AUTOFEATHER SELECTION : A/F SIGNAL  [-]
C       NOTE: signal for connector 7700-P3 pin 36

          EFSCUFE(I) = FAFTR(I) .OR. ( EFSCUFE(I) .AND. FARMS )
        ENDDO

CD790   UPTRIM SIGNAL TO OTHER ENGINE   [-]
C       NOTE: signal for connector 7700-P3 pin 20  ( SERIE 100 )
C       NOTE: signal for connector 7700-P2 pin E   ( SERIE 300 )

       IF ( D100 ) THEN
         EFSCUUP(1) = BIRK05 .AND. ER4K10        .OR.
     &                EFFE(2) .AND. .NOT.ER4K12
         EFSCUUP(2) = BILL06 .AND. ER4K11        .OR.
     &                EFFE(1) .AND. .NOT.ER4K12

       ELSE IF ( D300 ) THEN
         EFSCUUP(1) = BILK04 .AND. .NOT.ER53K1 .AND. .NOT.ER4K13
         EFSCUUP(2) = BIRH03 .AND. .NOT.ER53K2 .AND. .NOT.ER4K14
       ENDIF


CD800   AUXILIARY PRESSURE PUMP ADVISORY LIGHTS  [-]
C
        ED$AFP    = BILF04 .AND. FAUXP(1)
        ED$AFP2   = BILF04 .AND. FAUXP(2)

CD810   AUTOFEATHER "ARM" LIGHT  [-]

        ED$AFA  = ER4K5 .AND. EFARM(1)

CD820   AUTOFEATHER "SELECT" LIGHT  [-]

        ED$AFS  = BIRK05 .AND. IDESAF

CD830   ENGINE FAIL LIGHTS  [-]
C       NOTE: engine fail lights (pilot & copilot) are only provision

C        ED$ENF  = EFFAIL(1) .OR. EFFAIL(2)
C        ED$ENF2 = ED$ENF

CD840   POWER UPTRIM LIGHT  [-]
C       Note: The power uptrim light will come ON if
C          the normal Autofeather sequence is initiated
C          or the Feather solenoid valve opens ( Alternate
C          feather ).

        IF ( D100 ) THEN
          ED$PWUP = EFSCUUP(1) .OR. EFSCUUP(2)
        ELSE
          ED$PWUP = BIRK05 .AND. ER4K16     .OR.
     &              BILL06 .AND. ER4K15
        ENDIF

CD850   ALTERNATE FEATHER @ "UNFEATHER" SOLENOID  [-]

        ED$AFU  = .NOT.ESPLS11 .AND. .NOT.ER4K9
        ED$AFU2 = .NOT.ESPLS10 .AND. .NOT.ER4K8



CD  ::::::::::::::::::::::::::::::::::::::::
CD  ::   PROPELLER SYNCHROPHASE SYSTEM    ::
CD  ::   REF[1]: Page 101                 ::
CD  ::           Sch. 61-29-00            ::
CD  ::::::::::::::::::::::::::::::::::::::::


CD900   Power Management Selector Switch @ NORM

        IF ( D100 ) THEN
          IDESNORM = IDESMCP .OR. IDESMCL .OR. IDESMCR
        ENDIF


CD910   Power Management Selector Switch
C
        IF ( D100 ) THEN
          STO  = IDESTOP
        ELSE
          STO  = IDESTOP
          SMCT = IDESMCP
          SCLB = IDESMCL
          SCRZ = IDESMCR
       ENDIF
C
C
CD920   Propeller Synchrophaser
C
        IF ( BIRD03 ) THEN
          ED$SPH  =  IDESSPH
C          FSYNC  =  IDESSPH .AND. .NOT. STO
          FSYNC  =  IDESSPH
          IF ( FSYNC ) THEN
            DSYNC  =  C102
          ELSE
            DSYNC  =  DSYNC - DT
          ENDIF
          FSYNCT  =  DSYNC .GT. 0.0
          IF ( FSYNCT ) THEN
            EFSYNC  = (ENP(1) .GT. C104) .AND. (ENP(2) .GT. C104)
          ELSE
            EFSYNC  =  .FALSE.
          ENDIF
        ELSE
          DSYNC  =  0.0
          ED$SPH =  .FALSE.
          EFSYNC =  .FALSE.
        ENDIF

C *****************************************************************************
C * PROPELLER GROUND RANGE IND/BETA BACKUP SYSTEM                             *
C *****************************************************************************
C
C REF: [1] Page 101, Sch. 61-42-00
C      [3]
C      [5] 8-76-24, 8-76-15
C      [6]
C      [7]
C
C Propeller ground range indicating and beta backup protection is provided by
C a common system serving both propeller systems. The system is powered from
C the 28V dc left essential bus. Provision is made to test the system.
C
C Propeller ground range indication advises of propeller operation in the low
C pitch range by PROPELLER GROUND RANGE lights, one for each system, on the
C pilot's glareshield panel.
C
C Beta backup protection is provided to sense propeller blade angle decrease
C below a predetermined blade angle, with the power lever of the associated
C engine above FLT IDLE; to provide indication of the condition, the ground
C range indicating lights and to operate automatically to prevent further
C decrease in pitch.
C
CD 1000 Propeller Low Pitch switch [-]-----------------------------------
        ESPBP11 = ( EBETA42(1) + C230 ) .LE. C232
        ESPBP12 = ( EBETA42(2) + C230 ) .LE. C232

        IF ( BILG03 ) THEN

          IF (.NOT.ESPLS3) THEN
            SPLS3T = KSPLS3T
            SPLS3DEL = .FALSE.
          ELSE
            IF (.NOT.SPLS3DEL) THEN
              SPLS3T = SPLS3T - DT
            ENDIF
            SPLS3DEL = SPLS3T .LE. 0.0
          ENDIF

          IF (.NOT.ESPLS4) THEN
            SPLS4T = KSPLS4T
            SPLS4DEL = .FALSE.
          ELSE
            IF (.NOT.SPLS4DEL) THEN
              SPLS4T = SPLS4T - DT
            ENDIF
            SPLS4DEL = SPLS4T .LE. 0.0
          ENDIF

CD 1010 Feather Valve Solenoid signal [-]--------------------------------

          EFBLSLEVALT(1) = EBETALK .AND. (UGRA(1) .GT. 50.0) .AND.
     &                     .NOT.AGFWOWN .AND. .NOT.SPLS3DEL
          EFBLSLEVALT(2) = EBETALK .AND. (UGRA(1) .GT. 50.0) .AND.
     &                     .NOT.AGFWOWN .AND. .NOT.SPLS4DEL
          EFBLSA(1) = EFBLSLEVALT(1) .AND. ( ESPBP11 .OR. ENPG(1) .GT.
     &                C300 )
          EFBLSA(2) = EFBLSLEVALT(2) .AND. ( ESPBP12 .OR. ENPG(2) .GT.
     &                C300 )
          EFBB(1) = IDESBBN .AND. SPLS3DEL .AND. ESPBP11          .OR.
     &            IDESBB .AND. .NOT.SPLS3DEL .AND. ESPBP11        .OR.
     &            EFBLSA(1)
          EFBB(2) = IDESBBN2 .AND. SPLS4DEL .AND. ESPBP12         .OR.
     &            IDESBB2 .AND. .NOT.SPLS4DEL .AND. ESPBP12       .OR.
     &            EFBLSA(2)

CD 1015 Prop RPM analog mapping

          EA$NPR(1) = IAZZSP00
          EA$NPR(2) = IAZZSP01

CD 1020 Propeller Ground Range Light [-]---------------------------------
          ED$PGR  = ESPBP11 .OR.
     &    (IDESBBP .AND. .NOT. SPLS3DEL .AND.
     &    (EA$NPR(1) > C311))
          ED$PGR2 = ESPBP12 .OR.
     &    (IDESBBP2 .AND. .NOT. SPLS4DEL .AND.
     &    (EA$NPR(2) > C311))
        ELSE
         EFBB(1) = .FALSE.
         EFBB(2) = .FALSE.
         ED$PGR  = .FALSE.
         ED$PGR2 = .FALSE.
        ENDIF

		
CD 1040 BETA WARNING HORN [-]-----------------------------
C
C       BETA WARNING HORN CB MAPPING
C
        IF (D100 .OR. D300) THEN
          HORNCB = BILC08
        ELSE
          HORNCB = .FALSE.
        ENDIF
C
        IF (HORNCB .AND. ESPLMSW9 .AND.
     &	  (UGRA(1) .GT. 20.0) )THEN
	        ED$HORN = .TRUE.
        ELSE
          ED$HORN = .FALSE.
        ENDIF
C	
CD  ::::::::::::::::::::::::::::::::::::::::::
CD  ::   ENGINE ELECTRONIC CONTROL SYSTEM   ::
CD  ::   REF[1]: Page 101                   ::
CD  ::           Sch. 73-20-00              ::
CD  ::::::::::::::::::::::::::::::::::::::::::
CD'

CD1100   RATING SELECT DECK B   [-]
C        NOTE: signal for connector 7700-P3 pin 52

       EFRPR(1) = IDESMAXP
       EFRPR(2) = IDESMAXP

CD1105   ECU MANUAL MODE SELECTION   [-]
C        NOTE: signal for connector 7700-P3 pin 08
C
C        Ref: [5] 8-76-24
C             [6] 5.
C             [7]
C
C        BETA LOCKOUT SYSTEM
C
C        Latch manual mode lt when BLS is triggered
C
       IF( EFBLSLEVALT(1) .AND. .NOT. ESPLS2 )THEN
          BLS_MANTRG(1) = .TRUE.
       ELSE IF(AGFWOWN .OR. .NOT.(HRENG .EQ. -1) .OR. TCRSYST)THEN
          BLS_MANTRG(1) = .FALSE.
       ENDIF
C
       IF( EFBLSLEVALT(2) .AND. .NOT. ESPLS2 )THEN
          BLS_MANTRG(2) = .TRUE.
       ELSE IF(AGFWOWN .OR. .NOT.(HRENG .EQ. -1) .OR. TCRSYST)THEN
          BLS_MANTRG(2) = .FALSE.
       ENDIF
C
       IF(BLS_MANTRG(1))THEN
          EFECUM(1) = .TRUE.
       ELSE
         EFECUM(1) = IDESCUM
       ENDIF
C
       IF(BLS_MANTRG(2))THEN
         EFECUM(2) = .TRUE.
       ELSE
         EFECUM(2) = IDESCUM2
       ENDIF

       IF ( D100 ) THEN

CD1110   RATING SELECT DECK A   [-]
C        NOTE: signal for connector 7700-P3 pin 53 & 18

         EFCRR(1) = IDESTOP
         EFCRR(2) = IDESTOP
         EFCLR(1) = IDESTOP
         EFCLR(2) = IDESTOP

CD1120   RATING SELECT DECK C   [-]

         FRATC = IDESNORM

CD1130   REVERSION RELAY   [-]
C        NOTE: reversion relay is false when ECU is OFF
C
         ER5K1 = BILK04 .AND. EFECUR(1)
         ER5K2 = BIRH03 .AND. EFECUR(2)

CD1150   ECU NORMAL OPERATING SIGNAL  [-]
C        NOTE: signal for connector 7700-P3 pin 46

         FECU(1) = BILK04
     &             .AND. .NOT.ER3K3(1)
         FECU(2) = BIRH03
     &             .AND. .NOT.ER3K3(2)


       ELSE IF ( D300 ) THEN

CD1160   RATING LOGIC BIT 2   [-]
C        NOTE: signal for connector 7700-P2 pin CC

         EFCLR(1) = IDESMCP .OR. IDESMCL
         EFCLR(2) = IDESMCP .OR. IDESMCL

CD1165   RATING LOGIC BIT 1   [-]
C        NOTE: signal for connector 7700-P2 pin W

         EFCRR(1) = IDESMCP .OR. IDESMCR
         EFCRR(2) = IDESMCP .OR. IDESMCR

CD1170   RATING SELECT DECK C   [-]

         FRATC = IDESMCP .OR. IDESMCL .OR. IDESMCR

CD1180   REVERSION RELAY   [-]
C        NOTE: reversion relay is false when ECU is OFF

         ER53K9  = EFSCUFF(1)
         ER53K10 = EFSCUFF(2)

CD1190   ENGINE UPTRIM RELAY SIGNAL   [-]

         ER53K1 = ER4K16 .AND. BIRK05
         ER53K2 = ER4K15 .AND. BILL06

CD1200   ENGINE LOC ON RELAY SIGNAL   [-]

         ER53K5  = BILK04 .AND. ER53K9
         ER53K6  = BIRH03 .AND. ER53K10
         ER53K7  = ER6K5 .OR. EFNPL(1)
         ER53K8  = ER6K4 .OR. EFNPL(2)

CD1210   BLEED VALVE OVERRIDE RELAY SIGNAL   [-]

         ER53K11 = DBFBOI(1)
         ER53K12 = DBFBOI(2)

CD1230   ECU NORMAL OPERATING SIGNAL  [-]

         FECU(1) = BILK04
         FECU(2) = BIRH03

       ENDIF


       DO I = 1,2

CD1240   ECU / NH OVERSPEED DETECTION SIGNAL   [-]

         FNHO(I) = FECU(I) .AND. .NOT.EFECUM(I) .AND.
     &             ENHG(I) .GT. C220

CD1250  ECU FEATHER FOR NH UNDERSPEED SIGNAL  [-]
C       Note: FTRDI is EFNPL without the prop speed
C             to take in consideration.
C             FNHUS is not applicable for Dash-8
C
         FTRDI(1) = BILK04 .AND. ESCLS7   .OR.
     &              BILL06 .AND. ER4K3    .OR.
     &              BIRK05 .AND. ER4K4    .OR.
     &              EFFE(1)
C
         FTRDI(2) = BIRH03 .AND. ESCLS8   .OR.
     &              BILL06 .AND. ER4K3    .OR.
     &              BIRK05 .AND. ER4K4    .OR.
     &              EFFE(2)
C
         FNHUS(1) = .TRUE.
         FNHUS(2) = .TRUE.


CD1260   ECU / NH UNDERSPEED DETECTION SIGNAL   [-]

         FNHU(I) = FECU(I) .AND.
     &           ( ( EFECUM(I) .AND. FNHU(I) .AND. .NOT.FNHUS(I) ) .OR.
     &             ( .NOT. EFECUM(I) .AND. .NOT. EFAST(I) .AND.
     &               (( DFSR(I) .EQ. C223 ) .OR. FNHU(I) ) ))


CD1270   ECU / LOWER NH (30%) INTERLOCK SIGNAL   [-]

         IF( D100 )THEN
           FSTRT(I) = FECU(I) .AND. .NOT.EFECUM(I) .AND.
     &            ( (.NOT.TF71541(I) .AND. ENHG(I) .LT. C222) .OR.
     &              (     TF71541(I) .AND. FSTRT(I) ))
         ELSE IF( D300 )THEN
           FSTRT(I) = FECU(I) .AND. .NOT.EFECUM(I) .AND.
     &            ( (.NOT.TF71541(I) .AND. ENHG(I) .LT. C3_222) .OR.
     &              (     TF71541(I) .AND. FSTRT(I) ))
         ENDIF

CD1280   ECU TAKE OFF GROUND FLAG   [-]

         FTOG(1) = ESPLS9F .AND. ESCLS3 .AND. IDESTOP
         FTOG(2) = ESPLS10F .AND. ESCLS4 .AND. IDESTOP


CD1290   ECU FAIL FIXED LOGIC SIGNAL   [-]

         EFAILFIX(I) = FECU(I)
     &                 .AND. .NOT. EFECUM(I)
     &                 .AND. .NOT. FSTRT(I)
     &                 .AND.( ( EFECU(I) .AND. FTOG(I) ) .OR.
     &                        ( EFAILFIX(I) .AND. FTOG(I) ) )
     &                 .AND. .NOT. EFAST(I)


CD1300   POWER SETTING RESET SIGNAL   [-]

         IF ( FECU(I) .AND. EFECUM(I) ) THEN
           FPSR(I) = FNHUS(I)
         ELSE IF ( .NOT.FECU(I) ) THEN
           FPSR(I) = .TRUE.
         ELSE IF ( FPSR(I) ) THEN
           IF ( DFSR(I) .GE. 0.0 ) THEN
             DFSR(I)  =  0.0
             FPSR(I)  =  .FALSE.
           ENDIF
         ELSE IF ( EFAST(I) .OR. FTRDI(I) .OR. TF71011(I) ) THEN
           DFSR(I)  =  0.0
         ELSE IF ( DFSR(I) .NE. C223 ) THEN
           IF ( ENHG(I) .LT. C221 .AND. ENHG(I) .GT. C224 ) THEN
             DFSR(I)  =  AMIN1( C223, DFSR(I) + 1 )
           ELSE
             DFSR(I)  =  DFSR(I)
           ENDIF
         ENDIF


CD1310   ECU AVAILABILITY FLAG   [-]
C        NOTE: signal for connector 7700-P3 pin 46  ( SERIE 100 )
C        NOTE: signal for connector 7700-P2 pin J   ( SERIE 300 )

         EFECU(I)  =  FECU(I) .AND.
     &                .NOT. ( TF71011(I) .OR. FNHO(I)
     &                        .OR. FNHU(I) .OR. FSTRT(I) )

       ENDDO


       IF( D100 ) THEN

CD1330   ENRICHMENT SOLENOID SIGNAL   [-]
C        NOTE: signal for connector 7700-P3 pin 60 & 61

         EFENRS(1) = ER5K1 .AND. ( .NOT.ESPLS9F  .OR. FRATC )
         EFENRS(2) = ER5K2 .AND. ( .NOT.ESPLS10F .OR. FRATC )

CD1340   NP FUEL GOVERNOR SIGNAL   [-]
C        NOTE: signal for connector 7700-P3 pin 06

         EFFGA(1) = ESCLMN1 .AND. VBOG
         EFFGA(2) = ESCLMN2 .AND. VBOG

CD1350   ECU FAULT LATCH SIGNAL   [-]
C        NOTE: signal for connector 7700-P3 pin 02

C?       EFECUF(1) =
C?       EFECUF(2) =

CD1360   TORQUE MOTOR INHIBIT SIGNAL   [-]
C        NOTE: Signal for connector 7700-P3 pin 01
C              Torque motor is inhibited when ECU is OFF
C
         EFECUR(1) = EFECU(1) .AND. .NOT.EFECUM(1)
         EFECUR(2) = EFECU(2) .AND. .NOT.EFECUM(2)


       ELSE IF ( D300 ) THEN

CD1370   SCU ARM PROP. DISCRETE SIGNAL   [-]
C        NOTE: signal for connector 7700-P2 pin X_

         EFSCUA(1) = ESCLS5
         EFSCUA(2) = ESCLS6

CD1380   ECU INHIBIT/RESET DISCRETE SIGNAL   [-]
C        NOTE: signal for connector 7700-P2 pin EE

         EFSCUIN(1) = IDESCUM
         EFSCUIN(2) = IDESCUM2

CD1390   28V DC MFC SOL SIGNAL   [-]
C        NOTE: signal for connector 7700-P8 pin M

         EFMFCS(1) = BILJ10 .AND. ER53K5 .AND. IDESCUO   .OR.
     &               BIRJ07 .AND. ER53K5
         EFMFCS(2) = BIRH10 .AND. ER53K6 .AND. IDESCUO2  .OR.
     &               BILM08 .AND. ER53K6
         EFENRS(1) = EFMFCS(1)
         EFENRS(2) = EFMFCS(2)

CD1400   SCU FEATHER DISCRETE SIGNAL   [-]

         EFSCUFD(1) = BILL06 .AND. ER4K3
         EFSCUFD(2) = BIRK05 .AND. ER4K4

CD1410   BLEED SWITCH SIGNAL TO ECU   [-]
C        NOTE: signal for connector 7700-P2 pin C

         EFSCUB(1) = DBFBHI(1) .AND. .NOT.ER4K16
         EFSCUB(2) = DBFBHI(2) .AND. .NOT.ER4K15

CD1420   HBOV GROUND & 28V SIGNAL   [-]
C        NOTE: signal for connector 7700-P4 pin V & W

         EFHBOVG(1) = ER53K11    .OR.
     &                .NOT.ER53K11 .AND. EFECU(1)
         EFHBOVG(2) = ER53K12    .OR.
     &                .NOT.ER53K12 .AND. EFECU(2)

         EFHBOVV(1) = BILK04 .AND. ER53K11    .OR.
     &                .NOT.ER53K11 .AND. EFECU(1)
         EFHBOVV(2) = BIRH03 .AND. ER53K12    .OR.
     &                .NOT.ER53K12 .AND. EFECU(2)


CD1430   ENGINE TRIM DISCRETE   [-]
C        NOTE: signal for connector 7700-P2 pin X

C??         EFSCUT(1) =
C??         EFSCUT(2) =

CD1440   TORQUE MOTOR INHIBIT SIGNAL   [-]
C        NOTE: Signal for connector 7700-P3 pin 01
C              Torque motor is inhibited when ECU is OFF
C
         EFSCUFF(1) = EFECU(1) .AND. .NOT.EFECUM(1)
         EFSCUFF(2) = EFECU(2) .AND. .NOT.EFECUM(2)

       ENDIF


CD1450   ENRICHMENT TEST LIGHT SIGNAL  [-]
C        NOTE: according to w/d, idesen1 must be true for adv light to come on
C              and power lever microsw involved are ESPLS9F, 10F (T=PLA<FI+28)

       ED$ENR = ( BILK04 .OR. BIRH03 ) .AND.
     &          ( IDESEN1 .AND. (.NOT. ESPLS9F .OR. FRATC) ) .OR.
     &          ( IDESEN2 .AND. (.NOT. ESPLS10F .OR. FRATC) )


       IF ( D100 ) THEN

CD1460   ECU MODE "MANUAL" AND "ON" LIGHTS   [-]

         ED$ECUM  = BILK04 .AND. EFECUM(1)
         ED$ECUM2 = BIRH03 .AND. EFECUM(2)
         ED$ECUO  = BILK04 .AND. IDESCUO
         ED$ECUO2 = BIRH03 .AND. IDESCUO2

CD1470   ENGINE "MANUAL" LIGHT   [-]

         ED$MAN   = .NOT.ER5K1
         ED$MAN2  = .NOT.ER5K2

       ELSE IF ( D300 ) THEN

CD1480   ECU MODE "MANUAL" AND "ON" LIGHTS   [-]

         ED$ECUM  = BILK04 .AND. EFECUM(1)
         ED$ECUM2 = BIRH03 .AND. EFECUM(2)
         ED$ECUO  = BILK04 .AND. IDESCUO
         ED$ECUO2 = BIRH03 .AND. IDESCUO2

CD1490   ENGINE "MANUAL" LIGHT   [-]

         ED$MAN  = .NOT.ER53K9
         ED$MAN2 = .NOT.ER53K10

       ENDIF



CD  :::::::::::::::::::::::::::::::::::::::::
CD  ::   ENGINE LOW OIL PRESSURE WARNING   ::
CD  ::   REF[1]: Page 101                  ::
CD  ::           Sch. 79-36-00             ::
CD  :::::::::::::::::::::::::::::::::::::::::
CD'

CD1600   ENGINE LOW OIL PRESSURE WARNING   [-]

        ESLOP(1) = EPOE(1) .GT. 49.0 .OR.
     &             ESLOP(1) .AND. ( EPOE(1) .GT. 42.0 )
        ESLOP(2) = EPOE(2) .GT. 49.0 .OR.
     &             ESLOP(2) .AND. ( EPOE(2) .GT. 42.0 )

        ED$LOP  = .NOT. ESLOP(1)
        ED$LOP2 = .NOT. ESLOP(2)


CD  ::::::::::::::::::::::::::::::
CD  ::   ENGINE IGNITION        ::
CD  ::   REF[1]: Page 101       ::
CD  ::           Sch. 74-00-00  ::
CD  ::                          ::
CD  ::   ENGINE START CONTROL   ::
CD  ::   REF[1]: Page 101/102   ::
CD  ::           Sch. 80-10-00  ::
CD  ::::::::::::::::::::::::::::::
CD'

CD1700   ENGINE STARTER CHARACTERISTIC   [-]

        EFIDEM(1) = AERDK1 .AND. BIDLMNF .AND. ER7K1
        EFIDEM(2) = AERDK2 .AND. BIDRMNF .AND. ER7K2


CD1710   ENGINE START CONTROL RELAY   [-]

        ER7K1 = IDESS1 .AND. BIRM05 .AND. IDESEL1       .OR.
     &          ER7K1  .AND. BIRM05 .AND. IDESEL1
        ER7K2 = IDESS2 .AND. BIRM05 .AND. IDESEL2       .OR.
     &          ER7K2  .AND. BIRM05 .AND. IDESEL2


CD1720   ENGINE START CONTROL SIGNAL TO GCU   [-]

        EFSTC(1) = ER7K1
        EFSTC(2) = ER7K2


CD1730   ENGINE IGNITION SWITCH @ NORM   [-]

        EFDESIN1 = IDESI1 .AND. .NOT. IDESIM1
        EFDESIN2 = IDESI2 .AND. .NOT. IDESIM2

CD1740  ENGINE IGNITION SWITCH @MANUAL

        FDESIM(1)  = IDESIM1
        FDESIM(2)  = IDESIM2

CD1750  ENGINE IGNITION SW @ OFF

        FDESIO(1)  = .NOT. IDESI1 .AND. .NOT. IDESIM1
        FDESIO(2)  = .NOT. IDESI2 .AND. .NOT. IDESIM2

CD1760   SPARKING PLUG A & B SIGNAL   [-]
C
C       TF71614(5) Beta Lockout system :
C       Ref: [5] 8-76-24
C            [6] 5.
C            [7]
C
        IF ( TF71441 ) THEN
           EFEXCA(1) = .FALSE.
        !BETA LOCKOUT SYSTEM
        ELSE IF(EFBLSLEVALT(1) .AND. .NOT. ESPLS2 )THEN
           EFEXCA(1) = .TRUE.
        ELSE
           EFEXCA(1) = BILJ05 .AND.
     &                 ( EFDESIN1 .AND. ER7K1 .OR. FDESIM(1) )
        ENDIF
        IF ( TF71442 ) THEN
           EFEXCA(2) = .FALSE.
        !BETA LOCKOUT SYSTEM
        ELSE IF(EFBLSLEVALT(2) .AND. .NOT. ESPLS2 )THEN
           EFEXCA(2) = .TRUE.
        ELSE
           EFEXCA(2) = BIRL05 .AND.
     &                 ( EFDESIN2 .AND. ER7K2 .OR. FDESIM(2) )
        ENDIF
        EFEXCB(1) = EFEXCA(1)
        EFEXCB(2) = EFEXCA(2)


CD1770   ENGINE START TERMINATE SIGNAL TO GCU   [-]

        EFSTT(1) = .NOT. ER7K1 .AND. ENH(1) .GE. C142
        EFSTT(2) = .NOT. ER7K2 .AND. ENH(2) .GE. C142

CD1780   TIME DELAY ON START SELECTION    [-]

        IF ( ( EFSTT(1) .AND. .NOT. PFSTT(1) ) .OR.
     &       ( EFSTT(2) .AND. .NOT. PFSTT(2) ) ) THEN
          ETIM7K3 = 2.0
        ELSE
          ETIM7K3 = AMAX1( 0.0, ETIM7K3 - DT )
        ENDIF
C
CD1790   PREVIOUS STATUS OF START TERMINATE FLAG  [-]
C
        PFSTT(1) = EFSTT(1)
        PFSTT(2) = EFSTT(2)
C
CD1800   TIME DELAY RELAY ON START SELECTION    [-]
C
        ER7K3 = BIRM05 .AND. ( IDESEL1 .OR. IDESEL2 .OR.
     &                         ETIM7K3 .GT. 0.0 )
C
CD1810   SELECT & START LIGHTS   [-]
C
        ED$SELT = BIRM05 .AND. ER7K3
        ED$ST   = ER7K1 .AND. IDESEL1 .AND. BIRM05  .OR.
     &            ER7K2 .AND. IDESEL2 .AND. BIRM05

CD1820   START SOLENOID
C        NOTE: The hold in coil is energized when select sw is set
C              to 1 or 2 and the start terminate signal is false.
C
        SELS(1) = ( ENH(1) .LT. C142 .AND. .NOT.EFSTT(1) )   .OR.
     &            ( .NOT. EFSTT(1) .AND. TF71501  )
C
        SELS(2) = ( ENH(2) .LT. C142 .AND. .NOT.EFSTT(2) )   .OR.
     &            ( .NOT. EFSTT(2) .AND. TF71502  )
C
        IF ( BIRM05 ) THEN
           ED$SELS = ( SELS(1) .AND. SELS(2) )
     &                         .OR.
     &               ( SELS(1) .AND. .NOT.IDESEL2 )
     &                         .OR.
     &               ( SELS(2) .AND. .NOT.IDESEL1 )
        ELSE
           ED$SELS = .FALSE.
        ENDIF
C


CD       ::::::::::::::::::::::::::::::::::
CD       ::       FAST START FLAGS       ::
CD       ::       REF: CAE Engineering   ::
CD       ::::::::::::::::::::::::::::::::::
C
CD1900   ENGINE FAST START AVAILABLE DARK CONCEPT
C        REF: CAE Engineering

        FFS0(1) = ( .NOT.EFLM(1) .OR. EFLM(1) .AND.
     &               ( ENH(1) .LT. 68.0 ) )
     &            .AND. ( ECLACTL(1) .GT. C172 )
     &            .AND. BILJ05

        FFS0(2) = ( .NOT.EFLM(2) .OR. EFLM(2) .AND.
     &               ( ENH(2) .LT. 68.0 ) )
     &            .AND. ( ECLACTL(2) .GT. C172 )
     &            .AND. BIRL05

        TCM0FSTR  =  FFS0(1) .OR. FFS0(2)


CD1910   FAST START DURING A REPOSITION [-]
C        REF: CAE Engineering

        IF( (HRENG .EQ. 1) .AND. TCM0FSTR ) THEN
          FHRENG = TCM0FSTR
          TIM4   = 0.0
        ELSE IF( TIM4 .LT. 2.0 ) THEN
          FHRENG = TCM0FSTR
          TIM4   = TIM4 + DT
        ELSE
          FHRENG = .FALSE.
        ENDIF
C
CD1920   Engine Flame Flag

        DO I = 1,2
          IF ( ( TCMFSTRT .OR. FHRENG ) .AND. FFS0(I) ) THEN
            DTIM(I)  =  C138
          ELSE
            DTIM(I)  =  AMAX1( 0.0, DTIM(I) - DT )
          ENDIF
C
          EFAST(I) = DTIM(I) .GT. 0.0
       ENDDO


CD       ::::::::::::::::::::::::::::::::::::::
CD       ::       FUEL AVAILABLE FLAGS       ::
CD       ::       REF: CAE Engineering       ::
CD       ::::::::::::::::::::::::::::::::::::::
C
CD2000   Engine Fuel Supply System
C        Note: Fuel is not available at CL movement malf.
C
        DO  I = 1,2
C
          IF ( TF71581(I) ) THEN
             EVFF(I) = VFF(I)
          ELSE
             IF ( ECLACTL(I) .GT. C106 ) THEN
                EVFF(I)  =  1.0
             ELSE IF (ECLACTL(I) .LT. C107 ) THEN
                EVFF(I)  =  0.0
             ELSE
                EVFF(I)  =  C109I * ECLACTL(I) + C111
             ENDIF
             VFF(I) = EVFF(I)
          ENDIF
C
          EFFEI(I)  =    AFFA(I) .AND.
     &                 ( EVFF(I) .EQ. 1.0 )
C
        ENDDO


CD       :::::::::::::::::::::::::::::::::::
CD       ::       ENGINE FLAME FLAG       ::
CD       :::::::::::::::::::::::::::::::::::
C
CD2010   Engine Starting Operating Enveloppe
C
        IF ( D100 ) THEN
          IF ( VHH .LT. C116 ) THEN
            FRLT = ( (VHH .LT. C118) .AND. (UWCAS .LT. C120) )
     &                            .OR.
     &              ( (VHH .LT. C122) .AND. (UWCAS .LT. C124)
     &                                .AND. (UWCAS .GE. C120) )
          ELSE
            FRLT = .FALSE.
          ENDIF
        ELSE IF( D300 )THEN
          IF ( VHH .LE. 5000. ) THEN
            FRLT = UWCAS .LT. 200.
          ELSE IF ( VHH .LT. 20000 ) THEN
            FRLT = ( UWCAS .LT. 160. .AND. UWCAS .GT. 120. )
     &                         .OR.
     &             ( VHH .LT. ( -375.*UWCAS + 80000.) )
          ELSE
            FRLT = .FALSE.
          ENDIF
        ENDIF
C
CD2020   Engine Operating Characteristic for Starting and Relighting
C
        DO  I = 1,2
C
          FNH(I) = ENH(I) .GT. C134
          IF ( EFFEI(I) .AND. EWFEI(I) .GT. 0.0 ) THEN
            QWF(I) = QWF(I) + DT
            FWF(I) = QWF(I) .GT. C136
          ELSE
            QWF(I) = 0.0
            FWF(I) = .FALSE.
          ENDIF


CD2030   Dynamic Condition for Engine Start and LightUp

          FMS(I)  = FRLT .AND. ( EFEXCA(I) .OR. EFEXCB(I) )
          FMAS(I) = FNH(I) .AND. FWF(I) .AND. HEMODE(I) .NE. 4


CD2040  ENGINE FLAME FLAG  [-]
C
          EFLM(I)  =  ( EFLM(I) .OR. FMS(I) .OR. EFAST(I) )
     &                    .AND. FMAS(I)
     &                    .AND. .NOT. TF71111(I)
     &                    .AND. .NOT. ( HRENG .EQ. 0 )

        ENDDO

CD       ::::::::::::::::::::::::::::::::::::::::::
CD       ::       ENGINE ITT OVERTEMPERATURE     ::
CD       ::::::::::::::::::::::::::::::::::::::::::
C
C
          ED$OITT  = BILS02 .AND.
     &               ( ( EITT(1) .GT. 810. ) .OR. IDESOIT )
          ED$OITT2 = BIRA01 .AND.
     &               ( ( EITT(2) .GT. 810. ) .OR. IDESOIT2 )
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01075 **************************
C$ 01076 *  MODULE FREEZE FLAGS   *
C$ 01077 **************************
C$ 01078 '
C$ 01083 ****************************
C$ 01084 *  FIRST PASS CALCULATION  *
C$ 01085 ****************************
C$ 01086 '
C$ 01116 ::::::::::::::::::::::::::::::::::::::::::::::::::::::
C$ 01117 ::   POWER & CONDITION LEVER ACTUATOR MICROSWITCH   ::
C$ 01118 ::::::::::::::::::::::::::::::::::::::::::::::::::::::
C$ 01119 '
C$ 01121 100   CONDITION LEVER MICR.   [-]
C$ 01151 110   POWER LEVER MICR.   [-]
C$ 01204 ::::::::::::::::::::::::::::::::::::
C$ 01205 ::   ENGINE INTAKE BYPASS DOORS   ::
C$ 01206 ::       AND ADAPTER HEATERS      ::
C$ 01207 ::   REF[1]: Page 101             ::
C$ 01208 ::           Sch. 30-21-00        ::
C$ 01209 ::::::::::::::::::::::::::::::::::::
C$ 01210 '
C$ 01212 200   OUTSIDE AIR THERMOSTAT
C$ 01266 :::::::::::::::::::::::::::::::::
C$ 01267 ::   PROPELLER DEICE SYSTEM    ::
C$ 01268 ::   REF[1]: Page 103/104      ::
C$ 01269 ::           Sch. 30-61-00     ::
C$ 01270 :::::::::::::::::::::::::::::::::
C$ 01272 300  PROP DE-ICE CB TRIP MALFUNCTION LOGIC  [-]
C$ 01374 ::::::::::::::::::::::::::::::::::::::::::::
C$ 01375 ::   ENGINE HP & PROP. OVERSPEED TESTS    ::
C$ 01376 ::   REF[1]: Page 101                     ::
C$ 01377 ::           Sch. 61-22-00                ::
C$ 01378 ::::::::::::::::::::::::::::::::::::::::::::
C$ 01380 400   PROPELLER OVERSPEED GOVERNOR SIGNAL   [-]
C$ 01387 410   ECU OVERSPEED TEST SIGNAL   [-]
C$ 01398 420   ECU OVERSPEED TEST TRIGGER SIGNAL   [-]
C$ 01402 430   ECU OVERSPEED TEST RESET RELAY SIGNAL   [-]
C$ 01422 :::::::::::::::::::::::::::::::::::::::::::::::::::::
C$ 01423 ::   PROP. AUTOFEATHER CONTROL SYSTEM  ( SER 300 ) ::
C$ 01424 ::   REF[2]: Page 111/112                          ::
C$ 01425 ::           Sch. 61-21-01                         ::
C$ 01426 :::::::::::::::::::::::::::::::::::::::::::::::::::::
C$ 01430 450   AUTOFEATHER ARMING LEVEL SIGNAL   [-]
C$ 01436 460   AUTOFEATHER ARM SWITCH SIGNAL   [-]
C$ 01444 ::::::::::::::::::::::::::::::::::::::::::::::
C$ 01445 ::   PROP. AUTOFEATHER & ALTERNATE SYSTEM   ::
C$ 01446 ::   REF[1]: Page 101/102                   ::
C$ 01447 ::           Sch. 61-25-00                  ::
C$ 01448 ::::::::::::::::::::::::::::::::::::::::::::::
C$ 01450 500   MECHANICAL FEATHER VALVE   [-]
C$ 01463 510   ALTERNATE FEATHER SWITCH @ "FEATHERED" POSITION SIGNAL  [-]
C$ 01469 520   ALTERNATE FEATHER SWITCH @ "UNFEATHERED" POSITION SIGNAL  [-]
C$ 01480 530   PROPELLER TEST RELAY SIGNAL  [-]
C$ 01504 535   T/O WARNING HORN RELAY SIGNAL  [-]
C$ 01530 540   ENGINE INTERLOCK RELAY SIGNAL  [-]
C$ 01539 550   ARM CONTROL RELAY SIGNAL  [-]
C$ 01544 560   ENGINE POWER UPTRIM RELAY SIGNAL  [-]
C$ 01563 570   UPTRIM CANCEL RELAY SIGNAL  [-]
C$ 01568 580   ENGINE FEATHER RELAY ( SERIE-300 )   [-]
C$ 01583 590   ENGINE SCU 28V DC HI SIGNAL  [-]
C$ 01590 600   PROPELLER SIMULATION TORQUE >50. DURING TEST SIGNAL  [-]
C$ 01596 610   PROPELLER AUXILIARY PRESSURE PUMP RELAY SIGNAL  [-]
C$ 01621 620   PROPELLER AUXILIARY PRESSURE PUMP SIGNAL  [-]
C$ 01643 630   PROPELLER FEATHER VALVE SOLENOID SIGNAL [-]
C$ 01652 640   PROPELLER FEATHER VALVE POSITION  [-]
C$ 01664 650   PROPELLER NP FUEL UNDERSPEED GOVERNING CANCEL SIGNAL [-]
C$ 01710 660   PROPELLER FEATHERING SIGNAL   [-]
C$ 01719 662   AUTOFEATHER ENABLE SIGNAL ( LOCAL & OPPOSITE )    [-]
C$ 01739 664   PROPELLER FEATHERING SIGNAL   [-]
C$ 01745 666   AUTOFEATHER ENABLE SIGNAL ( LOCAL & OPPOSITE )    [-]
C$ 01765 680   AUTOFEATHER SELECTION : ARM INITIATION CONDITON  [-]
C$ 01771 690   AUTOFEATHER SELECTION : ARM SUSTAIN CONDITON  [-]
C$ 01777 695   AUTOFEATHER LOW TORQUE DURING A/F TEST  [-]
C$ 01791 700   AUTOFEATHER SELECTION : EQUATION REDUCTION   [-]
C$ 01795 710   AUTOFEATHER SELECTION : HIGH AND LOW TORQUE  [-]
C$ 01804 720   AUTOFEATHER SELECTION : ARM CONDITON  [-]
C$ 01820 730   AUTOFEATHER SELECTION : UPTRIM CONDITON  [-]
C$ 01826 740   AUTOFEATHER SELECTION : A/F TIMER  [-]
C$ 01834 750   AUTOFEATHER SELECTION : AUTOFEATHER SIGNAL   [-]
C$ 01841 760   AUTOFEATHER SELECTION : ARM CONDITON  [-]
C$ 01846 770   AUTOFEATHER SELECTION : "FAIL" LIGHT SIGNAL  [-]
C$ 01851 780   AUTOFEATHER SELECTION : A/F SIGNAL  [-]
C$ 01857 790   UPTRIM SIGNAL TO OTHER ENGINE   [-]
C$ 01873 800   AUXILIARY PRESSURE PUMP ADVISORY LIGHTS  [-]
C$ 01878 810   AUTOFEATHER "ARM" LIGHT  [-]
C$ 01882 820   AUTOFEATHER "SELECT" LIGHT  [-]
C$ 01886 830   ENGINE FAIL LIGHTS  [-]
C$ 01892 840   POWER UPTRIM LIGHT  [-]
C$ 01905 850   ALTERNATE FEATHER @ "UNFEATHER" SOLENOID  [-]
C$ 01912 ::::::::::::::::::::::::::::::::::::::::
C$ 01913 ::   PROPELLER SYNCHROPHASE SYSTEM    ::
C$ 01914 ::   REF[1]: Page 101                 ::
C$ 01915 ::           Sch. 61-29-00            ::
C$ 01916 ::::::::::::::::::::::::::::::::::::::::
C$ 01919 900   Power Management Selector Switch @ NORM
C$ 01926 910   Power Management Selector Switch
C$ 01938 920   Propeller Synchrophaser
C$ 01985 1000 Propeller Low Pitch switch [-]-----------------------------------
C$ 02011 1010 Feather Valve Solenoid signal [-]--------------------------------
C$ 02028 1015 Prop RPM analog mapping
C$ 02033 1020 Propeller Ground Range Light [-]---------------------------------
C$ 02048 1040 BETA WARNING HORN [-]-----------------------------
C$ 02065 ::::::::::::::::::::::::::::::::::::::::::
C$ 02066 ::   ENGINE ELECTRONIC CONTROL SYSTEM   ::
C$ 02067 ::   REF[1]: Page 101                   ::
C$ 02068 ::           Sch. 73-20-00              ::
C$ 02069 ::::::::::::::::::::::::::::::::::::::::::
C$ 02070 '
C$ 02072 1100   RATING SELECT DECK B   [-]
C$ 02078 1105   ECU MANUAL MODE SELECTION   [-]
C$ 02115 1110   RATING SELECT DECK A   [-]
C$ 02123 1120   RATING SELECT DECK C   [-]
C$ 02127 1130   REVERSION RELAY   [-]
C$ 02133 1150   ECU NORMAL OPERATING SIGNAL  [-]
C$ 02144 1160   RATING LOGIC BIT 2   [-]
C$ 02150 1165   RATING LOGIC BIT 1   [-]
C$ 02156 1170   RATING SELECT DECK C   [-]
C$ 02160 1180   REVERSION RELAY   [-]
C$ 02166 1190   ENGINE UPTRIM RELAY SIGNAL   [-]
C$ 02171 1200   ENGINE LOC ON RELAY SIGNAL   [-]
C$ 02178 1210   BLEED VALVE OVERRIDE RELAY SIGNAL   [-]
C$ 02183 1230   ECU NORMAL OPERATING SIGNAL  [-]
C$ 02193 1240   ECU / NH OVERSPEED DETECTION SIGNAL   [-]
C$ 02198 1250  ECU FEATHER FOR NH UNDERSPEED SIGNAL  [-]
C$ 02217 1260   ECU / NH UNDERSPEED DETECTION SIGNAL   [-]
C$ 02225 1270   ECU / LOWER NH (30%) INTERLOCK SIGNAL   [-]
C$ 02237 1280   ECU TAKE OFF GROUND FLAG   [-]
C$ 02243 1290   ECU FAIL FIXED LOGIC SIGNAL   [-]
C$ 02253 1300   POWER SETTING RESET SIGNAL   [-]
C$ 02275 1310   ECU AVAILABILITY FLAG   [-]
C$ 02288 1330   ENRICHMENT SOLENOID SIGNAL   [-]
C$ 02294 1340   NP FUEL GOVERNOR SIGNAL   [-]
C$ 02300 1350   ECU FAULT LATCH SIGNAL   [-]
C$ 02306 1360   TORQUE MOTOR INHIBIT SIGNAL   [-]
C$ 02316 1370   SCU ARM PROP. DISCRETE SIGNAL   [-]
C$ 02322 1380   ECU INHIBIT/RESET DISCRETE SIGNAL   [-]
C$ 02328 1390   28V DC MFC SOL SIGNAL   [-]
C$ 02338 1400   SCU FEATHER DISCRETE SIGNAL   [-]
C$ 02343 1410   BLEED SWITCH SIGNAL TO ECU   [-]
C$ 02349 1420   HBOV GROUND & 28V SIGNAL   [-]
C$ 02363 1430   ENGINE TRIM DISCRETE   [-]
C$ 02369 1440   TORQUE MOTOR INHIBIT SIGNAL   [-]
C$ 02379 1450   ENRICHMENT TEST LIGHT SIGNAL  [-]
C$ 02390 1460   ECU MODE "MANUAL" AND "ON" LIGHTS   [-]
C$ 02397 1470   ENGINE "MANUAL" LIGHT   [-]
C$ 02404 1480   ECU MODE "MANUAL" AND "ON" LIGHTS   [-]
C$ 02411 1490   ENGINE "MANUAL" LIGHT   [-]
C$ 02420 :::::::::::::::::::::::::::::::::::::::::
C$ 02421 ::   ENGINE LOW OIL PRESSURE WARNING   ::
C$ 02422 ::   REF[1]: Page 101                  ::
C$ 02423 ::           Sch. 79-36-00             ::
C$ 02424 :::::::::::::::::::::::::::::::::::::::::
C$ 02425 '
C$ 02427 1600   ENGINE LOW OIL PRESSURE WARNING   [-]
C$ 02438 ::::::::::::::::::::::::::::::
C$ 02439 ::   ENGINE IGNITION        ::
C$ 02440 ::   REF[1]: Page 101       ::
C$ 02441 ::           Sch. 74-00-00  ::
C$ 02442 ::                          ::
C$ 02443 ::   ENGINE START CONTROL   ::
C$ 02444 ::   REF[1]: Page 101/102   ::
C$ 02445 ::           Sch. 80-10-00  ::
C$ 02446 ::::::::::::::::::::::::::::::
C$ 02447 '
C$ 02449 1700   ENGINE STARTER CHARACTERISTIC   [-]
C$ 02455 1710   ENGINE START CONTROL RELAY   [-]
C$ 02463 1720   ENGINE START CONTROL SIGNAL TO GCU   [-]
C$ 02469 1730   ENGINE IGNITION SWITCH @ NORM   [-]
C$ 02474 1740  ENGINE IGNITION SWITCH @MANUAL
C$ 02479 1750  ENGINE IGNITION SW @ OFF
C$ 02484 1760   SPARKING PLUG A & B SIGNAL   [-]
C$ 02513 1770   ENGINE START TERMINATE SIGNAL TO GCU   [-]
C$ 02518 1780   TIME DELAY ON START SELECTION    [-]
C$ 02527 1790   PREVIOUS STATUS OF START TERMINATE FLAG  [-]
C$ 02532 1800   TIME DELAY RELAY ON START SELECTION    [-]
C$ 02537 1810   SELECT & START LIGHTS   [-]
C$ 02543 1820   START SOLENOID
C$ 02565 ::::::::::::::::::::::::::::::::::
C$ 02566 ::       FAST START FLAGS       ::
C$ 02567 ::       REF: CAE Engineering   ::
C$ 02568 ::::::::::::::::::::::::::::::::::
C$ 02570 1900   ENGINE FAST START AVAILABLE DARK CONCEPT
C$ 02586 1910   FAST START DURING A REPOSITION [-]
C$ 02599 1920   Engine Flame Flag
C$ 02612 ::::::::::::::::::::::::::::::::::::::
C$ 02613 ::       FUEL AVAILABLE FLAGS       ::
C$ 02614 ::       REF: CAE Engineering       ::
C$ 02615 ::::::::::::::::::::::::::::::::::::::
C$ 02617 2000   Engine Fuel Supply System
C$ 02641 :::::::::::::::::::::::::::::::::::
C$ 02642 ::       ENGINE FLAME FLAG       ::
C$ 02643 :::::::::::::::::::::::::::::::::::
C$ 02645 2010   Engine Starting Operating Enveloppe
C$ 02668 2020   Engine Operating Characteristic for Starting and Relighting
C$ 02682 2030   Dynamic Condition for Engine Start and LightUp
C$ 02688 2040  ENGINE FLAME FLAG  [-]
C$ 02697 ::::::::::::::::::::::::::::::::::::::::::
C$ 02698 ::       ENGINE ITT OVERTEMPERATURE     ::
C$ 02699 ::::::::::::::::::::::::::::::::::::::::::
