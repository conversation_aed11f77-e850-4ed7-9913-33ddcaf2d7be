C'Module_ID    USD8RX1
C'Customer     All
C'Application  A-Z radio station pages
C'Author       <PERSON>/<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON>
C'Date         Feb 88
C
C'Revision_history
C
C  usd8rx1.for.15 19Dec1994 16:27 ???? jd
C       < added comm type checks so comm stations won't be picked up as
C         gates. >
C
C       Modified by: PY
C       Tue Mar 10 11:00:00 1992
C       < Fixing Conversion to IBM of Rwy sorting logic >
C
C  usd8rx1.for.14  4Mar1992 16:10 usd8 PY
C       < Fixing precompilation errors >
C
C       Modified by: PY
C       Wed Mar 04 17:17:57 1992
C       < Removed TCMRWYOK >
C
C File: /group/radio/yee/md11/datd/usd8rx1.for.13
C       Modified by: PY
C       Mon Mar  2 17:17:57 1992
C       < Finished converting Rwy sorting to IBM >
C
C File: /group/radio/yee/md11/datd/usd8rx1.for.12
C       Modified by: PY
C       Mon Mar  2 17:01:20 1992
C       < Starting to convert rwy sorting to IBM >
C
C File: /group/radio/yee/md11/datd/usd8rx1.for.11
C       Modified by: PY
C       Mon Mar  2 16:16:49 1992
C       < Sorting Rwys  >
C
C File: /cae1/ship/usd8rx1.for.8
C       Modified by: jd
C       Thu Nov 14 16:26:36 1991
C       < tcmraok,tcmrach now in cdb >
C
C File: /cae1/ship/usd8rx1.for.4
C       Modified by: jd
C       Thu Oct 24 12:00:29 1991
C       < tcmraok,tcmrach not in cdb - internals for now >
C
C File: /cae1/ship/da88rx1.for.20
C       Modified by: jd
C       Thu Oct 24 11:44:00 1991
C       < changed ship name to USD8 >
C
C File: /cae1/ship/da88rx1.for.2
C       Modified by: R.HEIDT
C       Fri Sep 20 08:15:39 1991
C       < TCMRWYOK now being set in module REF >
C
C File: /cae1/ship/da88rx1.for.39
C       Modified by: MS
C       Tue Jul 30 16:59:05 1991
C       < REPLACED AW20 WITH DA88 >
C
C File: /cae1/ship/da88rx1.for.38
C       Modified by: MS
C       Tue Jul 30 16:56:08 1991
C       < BROUGHT IN NEW RX1 MODULE AND ADDED LOGIC FOR TCMRAOK, AND
C         TCMRACH >
C
C File: /cae1/ship/aw20rx1.for.2
C       Modified by: PY
C       Mon Jul 22 23:42:14 1991
C       < Removed AZ_SEL=AZ_SEL+1 from section 1810,not setting AZ_SEL to
C         if>4 >
C
C File: /cae1/ship/aw20rx1.for.32
C       Modified by: py
C       Sat Jul 13 20:23:52 1991
C       < Settin AZ_SEL to 1 if GE 4 >
C
C       Modified by: py
C       Thu Jul 11 10:18:00 1991
C       < Initialize FR_STATUS in first pass>
C
C       Modified by: PY
C       Fri Jul  5 14:05:22 1991
C       Removed WAIT state and renamed IO_STATUS by RXZXCODE
C
C File: /cae1/ship/aw20rx1.for.5
C       Modified by: PY
C       Thu Jul  4 23:05:22 1991
C       < Added check if I/O still in progress at 1200,1300 >
C
C File: /cae1/ship/aw20rx1.for.4
C       Modified by: py
C       Thu Jul  4 22:28:31 1991
C       < Commented MODEC=1 at 1200, 1300 >
C
C File: /cae/ship/aw20rx1.for.25
C       Modified by: PY
C       Tue May 21 18:56:25 1991
C       < Setting TCMRWYOK when I4RWYNAM is filled >
C
C File: /cae1/ship/ch11rx1.for.2
C       Modified by: PY
C       Wed May 15 09:43:53 1991
C       < Trying to fix frequencies rewritten >
C
C File: /cae/ship/ch11rx1.for.7
C       Modified by: P.YEE
C       Thu Feb 28 18:33:54 1991
C       < Changed NOT FR_STATUS TO NOT(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0) >
C
C File: /cae/ship/ch11rx1.for.6
C       Modified by: P.Y
C       Thu Feb 28 18:26:13 1991
C       <
C
C File: /cae/ship/ch11rx1.for.5
C       Modified by: P.Yee
C       Thu Feb 28 17:38:06 1991
C       < Deleted NOT from FR_STATUS >
C
C File: /cae/ship/ch11rx1.for.3
C       Modified by: P.YEE
C       Thu Feb 28 11:18:11 1991
C       < DELETED COPY_STATUS >
C
C File: /cae/ship/ch11rx1.for.2
C       Modified by: PY
C       Thu Feb 28 11:07:28 1991
C       < DELETED COPY_STATUS >
c
c  #/cae/ship/ch11rx1.for.5 13-Feb-91 PY
c      Changed RWXP*(1) to RWXP*
c  #/cae/ship/ch11rx1.for.2 13-Feb-91 PY
c      Changed XPREPOSAV to XPREPOSA
C
C'References
C
C        CAE Software development standard, 18 June 1984
C        CD 130931-01-8-300, Rev. A, CAE
C
C        Fortran-77, release 40 reference manual,
C        June 1983, GOULD.
C
C
      SUBROUTINE USD8RX1
C
      IMPLICIT NONE
C
C'Purpose
C
C  This program is used to display the A-Z pages on the I/F station.
C
C'Subroutine_called
CSEL+
CSEL C  S_READ
CSEL-
CIBM+
C    cae_aio_local_copy
C    cae_io_read
CIBM-
C
C'Data_base_variables
C
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL90:GLOBAL91
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL00:GLOBAL05
CSEL-         ------------------------
C
CP    USD8 RXZXFVLD    ,!RZX.DAT FULL    VALIDATION FLAG
CP   &  RXZXPVLD       ,!RZX.DAT PARTIAL VALIDATION FLAG
CP   &  RXZXFLG        ,!RZX.DAT I/O flag
CP   &  RXZXOPEN       ,!RZX.DAT open
CP   &  RWXINIT        ,!RZX initialization flag
CP   &  RWXFREEZ       ,!RZX freeze flag
CP   &  RXZXCODE       ,!RZX.DAT error
CP   &  RWXHDR         ,!Volatile page header
CP   &  RWXBUF         ,!Volatile page buffer
CP   &  RWXBUFM        ,!Volatile status buffer
CP   &  RXZXDCB        ,!RZX file DCB address
CP   &  RWROWPOS       ,!
CP   &  RWXMODE        ,!RZX mode select
CP   &  RWXMODE1       ,!RZX mode parameter 1
CP   &  RWXMODE2       ,!RZX mode parameter 2
CP   &  RWXTIFPG       ,!Radio aids page number, TOP CRT
CP   &  RWXBIFPG       ,!Radio aids page number, BOTTOM CRT
CP   &  RWXLWRAP       ,!Wrap around mode enable
CP   &  RWXNAME        ,!Station name for display
CP   &  RWXKILL        ,!Volatile for colour control on stn kill
CP   &  RWRADFP        ,!R/A first page in view
CP   &  RWRADLP        ,!R/A last  page in view
CP   &  RVNKIL         ,!Total no of killed stn
CP   &  RVNAKIL        ,!no of killed stn by autokill
CP   &  RVXKIL         ,!Index no of killed stn
CP   &  RVKILFLG       ,!Kill table updated flag
CP   &  RXTOUCH        ,!Touch screen I/F
CP   &  RWXPLINE       ,!Selected line number
CP   &  RWBUTTON       ,!I/F buttons arrangement option
CP   &  RWAZMODI       ,!A-Z page mode on first access
CP   &  XPFAIL         ,!FAIL stn button
CP   &  XPFAILAV       ,!FAIL stn button available
CP   &  XPCLR          ,!CLEAR stn button
CP   &  XPCLRAV        ,!CLEAR stn button available
CP   &  XPCALLST       ,!CLEAR ALL stn button
CP   &  XPCALLAV       ,!CLEAR ALL stn button available
CP   &  XPREPOSA       ,!REPOS stn button available
CP   &  XPREPOS        ,!REPOS stn button
CP   &  TCMRAOK        ,!A-Z Page displayed on top/bottom crt
CP   &  TCMRACH        ,!A-Z SWAP PAGE on top/bottom crt
CP   &  RWXPFWD        ,!Page forward  from I/F
CP   &  RWXPBCK        ,!Page backward from I/F
CP   &  RWXPREV        ,!Previous page flag for R/A page
CP   &  TAPOSN         ,!Reposition number
CP   &  TAREFRUN       ,!Reference runway record number
CP   &  TCPOSN         ,!Reposition number (CNIA)
CP   &  TAXPOS         ,!Index # of reposition
CP   &  TCMREPOS       ,!Reposition flag
CP   &  TAXKILL        ,!Index no of stn fail/clear
CP   &  TAKILTYP       ,!Fail stn request STATUS
CP   &  TCRALLST       ,!Clear all stn request flag
C
CP   &  RXAGAT         ,!maximum number of airport gate
CP   &  RXARWYS        ,!maximum number of airport runway
CP   &  RXARPT         ,!ICAO code for the present airport
C                       !in GATE/RWY buffer
C
CP   &  RXBGAT         ,!actual number of airport gate
CP   &  RXBRWYS        ,!actual number of airport runway
C
CP   &  RXGATNAM       ,!gate for reference runway airport
CP   &  RXGATIDX       ,!gate RZ index  number
CP   &  RXGATREC       ,!gate RZ record number
C
CP   &  RXRWYNAM       ,!runway for reference runway airport
CP   &  RXRWYIDX       ,!runway RZ index  number
CP   &  RXRWYREC       ,!runway RZ record number
CP   &  RXRWYAVL       ,!runway available flag
C
CP   &  RLAIRFLD       ,!ICAO/IATA name
CP   &  RLAIRIAT       ,!IATA name
CP   &  RLAIRICA       ,!ICAO name
CP   &  RLRWYSET       ,!RUNWAY/GATE name
CP   &  RLRECORD       ,!associated RZ record number
CP   &  RLREADY        ,!Request completed
C
CP   &  RUPLAT         ,!A/C latitude
CP   &  RUPLON         ,!A/C longitude
CP   &  RUCOSLAT       ,!Cosine of aircraft latitude
CP   &  RXZILAT        ,!Station latitude
CP   &  RXZILON        ,!Station longitude
CP   &  RXZIFRE        ,!Station frequency
CP   &  RXZIIDX        ,!Index number of station selected
CP   &  RXZIREC        ,!Record no of station selected
CP   &  RXZIIDE        ,!Station ident
CP   &  RXZITYP        ,!Station type
CP   &  RXZIDENT       ,!Requested ident
CP   &  RXZILINE       ,!Line selected
CP   &  RXZI0LIN       ,!Dark concept on selected line
CP   &  RXZIMORE       ,!Select for more
CP   &  RXZI0MOR       ,!Dark concept for select for more
CP   &  RXZIVAL        ,!Selected ident status
CP   &  RXZIRSEL       ,!Reselect ident on line
CP   &  RXZIDISP       ,!Display buffer
CP   &  RXZINO         ,!Number of idents to process
CP   &  RWDISREC       ,!Station data RZ record
CP   &  RXACCESS        !Station access
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:10:08 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RUCOSLAT       ! COS A/C LAT
     &, RXZIFRE(5,4)   ! STATION FREQUENCY
     &, RXZILAT(5,4)   ! STATION LATITUDE                      [DEG]
     &, RXZILON(5,4)   ! STATION LONGITUDE                     [DEG]
     &, TAXPOS         ! REPOSITION STN/POS INDEX
C$
      INTEGER*4
     &  RLAIRFLD       ! ICAO/IATA AIRPORT CODE (REQUEST)
     &, RLAIRIAT       ! ICAO/IATA AIRPORT CODE (IATA)
     &, RLAIRICA       ! ICAO/IATA AIRPORT CODE (ICAO)
     &, RLRECORD       ! RECORD # OF SELECTED RWY
     &, RLRWYSET(2)    ! RUNWAY IDENTIFICATION
     &, RVNAKIL        ! NUMBER OF AUTOKILLED STATIONS
     &, RVXKIL(32)     ! INDEX OF STNS   KILLED BY INSTR.
     &, RWDISREC       !    RZ RECORD NUMBER
     &, RWROWPOS(4)    ! ROW INPUT FOR R/A PAGE
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXARPT         ! RWY/GATE AIRPORT ICAO NAME
     &, RXGATIDX(20)   ! GATE RZ INDEX
     &, RXGATREC(20)   ! GATE RZ RECORD
     &, RXRWYIDX(20)   ! RUNWAY RZ INDEX
     &, RXRWYREC(20)   ! RUNWAY RZ RECORD
     &, RXZIIDX(4)     ! INDEX NUMBER OF STATION SELECTED
     &, RXZIREC(4)     ! RECORD NO OF STATION SELECTED
     &, RXZXCODE(4)    ! RZX.DAT  ERROR
     &, RXZXDCB(3)     ! RZX.DAT  DCB
     &, TAKILTYP       ! IDENTIFIES STATION TYPE THAT FAILED
     &, TAPOSN         ! REPOSITION INDEX
     &, TAREFRUN       ! REFERENCE RUNWAY
     &, TAXKILL        ! INDEX NO. TO KIL/RESET STN
     &, TCPOSN         ! REPOSITION INDEX
C$
      INTEGER*2
     &  RVNKIL         ! NO. OF STATIONS KILLED BY INSTR.
     &, RWAZMODI       ! A-Z PAGE INITIAL MODE
     &, RWBUTTON       ! I/F BUTTON ARRANGEMENT (R/A PAGES)
     &, RWXBIFPG(2)    ! A-Z PAGE # , BOTTOM CRT
     &, RWXMODE        ! RZX MODE SELECT
     &, RWXMODE1       ! RZX MODE PARAMETER 1
     &, RWXMODE2       ! RZX MODE PARAMETER 2
     &, RWXPLINE       ! SELECTED LINE ON A-Z PAGE
     &, RWXTIFPG(2)    ! A-Z PAGE # , TOP CRT
     &, RXAGAT         ! MAX NO OF AIRPORT GATE
     &, RXARWYS        ! MAX NO OF AIRPORT RUNWAYS
     &, RXBGAT         ! ACTUAL NO. OF AIRPORT GATE
     &, RXBRWYS        ! ACTUAL NO. OF AIRPORT RUNWAYS
     &, RXZINO         ! NUMBER OF IDENTS TO PROCESS
C$
      LOGICAL*1
     &  RVKILFLG       ! CHANGE KILL SUMMARY
     &, RWRADFP(2)     ! R/A FIRST PAGE IN VIEW
     &, RWRADLP(2)     ! R/A LAST PAGE IN VIEW
     &, RWXFREEZ       ! RZX FREEZE FLAG
     &, RWXINIT        ! RZX INITIALIZATION FLAG
     &, RWXKILL(34)    ! KILL STATION ON LINE
     &, RWXLWRAP       ! R/A PAGES WRAP AROUND MODE ENABLE
     &, RWXPBCK(2)     ! PAGE BACKWARD REQUEST TO R/A PAGE
     &, RWXPFWD(2)     ! PAGE FORWARD REQUEST TO R/A PAGE
     &, RWXPREV(2)     ! PREVIOUS PAGE REQUEST TO R/A PAGE
     &, RXRWYAVL(20)   ! RUNWAY NAME AVAIL FLAGS
     &, RXTOUCH        ! TOUCH SCREEN I/F
     &, RXZI0LIN(5,4)  ! DARK CONCEPT ON SELECTED LINE
     &, RXZI0MOR(4)    ! DARK CONCEPT FOR SELECT FOR MORE
     &, RXZILINE(5,4)  ! LINE SELECTED
     &, RXZIMORE(4)    ! SELECT FOR MORE
     &, RXZIRSEL(4)    ! RESELECT IDENT ON LINE
     &, RXZIVAL(4)     ! SELECTED IDENT STATUS
     &, RXZXFLG(4)     ! RZX.DAT  I/O FLAG
     &, RXZXFVLD       ! RZX/RZ VALIDATION FLAG
     &, RXZXOPEN       ! RZX.DAT  OPEN
     &, RXZXPVLD       ! RZX/RZ PARTIAL  VALIDATION FLAG
     &, TCMRACH(2)     ! RADIO STATION PAGE SWAP FLAG
     &, TCMRAOK(2)     ! RADIO PAGE READY
     &, TCMREPOS       ! REPOSITION A/C
     &, TCRALLST       ! ALL STATION RESET
     &, XPCALLAV       ! CLR ALL STN BUTT AVAIL
     &, XPCALLST       ! CLR ALL STNS BUTT PUSHED
     &, XPCLR          ! CLR STN BUTTON PUSHED
     &, XPCLRAV        ! CLR STN AVAILABLE
     &, XPFAIL         ! FAIL STN BUTTON PUSHED
      LOGICAL*1
     &  XPFAILAV       ! FAIL STN AVAILABLE
     &, XPREPOS        ! REPOS TO STATION
     &, XPREPOSA       ! REPOS TO STATION AVAIL.
C$
      INTEGER*1
     &  RLREADY        ! RUNWAY RECORD REQUEST COMPLETED
     &, RWXBUF(64,24)  ! A-Z PAGE BUFFER
     &, RWXBUFM(4,34)  ! MESSAGE ON PAGE
     &, RWXHDR(64,10)  ! HEADER SECTION FOR RZX BUFFER
     &, RWXNAME(40)    ! STATION NAME TO SEARCH
     &, RXGATNAM(5,20) ! GATE FOR REF RWY AIRPORT
     &, RXRWYNAM(4,20) ! RWY FOR REF RWY AIRPORT
     &, RXZIDENT(8,4)  ! REQUESTED IDENT
     &, RXZIDISP(40,5,4)
C$                     ! DISPLAY BUFFER
     &, RXZIIDE(4,5,4) ! STATION IDENT
     &, RXZITYP(4,5,4) ! STATION TYPE
C$
      LOGICAL*1
     &  DUM0000001(38432),DUM0000002(8200),DUM0000003(84)
     &, DUM0000004(64),DUM0000005(54),DUM0000006(49)
     &, DUM0000007(274),DUM0000008(300),DUM0000009(1156)
     &, DUM0000010(1),DUM0000011(22),DUM0000012(26)
     &, DUM0000013(652),D*********(32),DUM0000015(20)
     &, DUM0000016(27264),DUM0000017(584),DUM0000018(4)
     &, DUM0000019(167),DUM0000020(2),DUM0000021(8)
     &, DUM0000022(3902),DUM0000023(18),DUM0000024(15)
     &, DUM0000025(3),DUM0000026(16),DUM0000027(6)
     &, DUM0000028(1),DUM0000029(1),DUM0000030(4),DUM0000031(35242)
     &, DUM0000032(7),DUM0000033(25),DUM0000034(183839)
     &, DUM0000035(6703),DUM0000036(69),DUM0000037(1919)
     &, DUM0000038(16),DUM0000039(12),DUM0000040(144)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RUPLAT,RUPLON,RUCOSLAT,DUM0000002,RXZXDCB
     &, DUM0000003,RXZXFLG,DUM0000004,RXZXCODE,DUM0000005,RXZXOPEN
     &, DUM0000006,RXZXFVLD,RXZXPVLD,DUM0000007,RVXKIL,DUM0000008
     &, RVNAKIL,DUM0000009,RVNKIL,RVKILFLG,DUM0000010,RXACCESS
     &, DUM0000011,RXAGAT,RXARWYS,DUM0000012,RXBGAT,RXBRWYS,DUM0000013
     &, RXRWYREC,RXRWYIDX,RXGATREC,RXGATIDX,D*********,RXARPT
     &, RXGATNAM,DUM0000015,RXRWYNAM,RXRWYAVL,DUM0000016,RWDISREC
     &, DUM0000017,RLRWYSET,RLAIRFLD,RLAIRIAT,RLAIRICA,RLRECORD
     &, DUM0000018,RLREADY,DUM0000019,RWXMODE,RWXMODE1,RWXMODE2
     &, DUM0000020,RWXHDR,RWXBUF,RWXNAME,RWXBUFM,DUM0000021,RWXKILL
     &, RWXINIT,RWXFREEZ,DUM0000022,RWXTIFPG,RWXBIFPG,DUM0000023
     &, RWBUTTON,RWAZMODI,DUM0000024,RXTOUCH,RWXLWRAP,DUM0000025
     &, RWROWPOS,DUM0000026,RWXPLINE,DUM0000027,RWXPFWD,RWXPBCK
     &, RWXPREV,DUM0000028,RWRADFP,RWRADLP,DUM0000029,RXZILAT
     &, RXZILON,RXZIFRE,RXZIIDX,RXZIREC,DUM0000030,RXZIIDE,RXZITYP
     &, RXZIDENT,RXZILINE,RXZI0LIN,RXZIMORE,RXZI0MOR,RXZIVAL
     &, RXZIRSEL,RXZIDISP,RXZINO,DUM0000031,XPCALLAV,XPCALLST
     &, DUM0000032,XPCLRAV,XPCLR,XPFAILAV,XPFAIL,DUM0000033,XPREPOS
     &, XPREPOSA,DUM0000034,TCRALLST,DUM0000035,TCMRAOK,TCMRACH
     &, DUM0000036,TCMREPOS,DUM0000037,TAXKILL,DUM0000038,TAKILTYP
     &, DUM0000039,TAREFRUN,TAPOSN,TAXPOS,DUM0000040,TCPOSN    
C------------------------------------------------------------------------------
C
C * Parameters
C
      REAL*4      INT_TO_DEG
      INTEGER*4   MODEDEF,TRXAGAT,TRXARWYS,MTIPG,MTIELPG,MTIEL
      INTEGER*4   M_TICQI,M_TIAQI,M_TIDQI,M_IDPG,M_IDSTNS
      INTEGER*4   MAX_LIN,IDENT_OPT
      PARAMETER  (MODEDEF = 1 )   !IDEN/ICAO default option
      PARAMETER  (TRXAGAT = 20)   !maximum number of internal array
      PARAMETER  (TRXARWYS= 20)   !maximum number of internal array
      PARAMETER  (MTIPG   = 3 )   !Maximum Top Index PaGes
      PARAMETER  (MTIELPG = 24 )  !Maximum Top Index ELements per PaGe
      PARAMETER  (MTIEL   = MTIPG * MTIELPG ) !Maximum Top Index ELements
      PARAMETER  (M_TICQI = 2  )  !Total number of ICao Quick Index record
      PARAMETER  (M_TIAQI = 1  )  !Total number of IAta Quick Index record
      PARAMETER  (M_TIDQI = 1  )  !Total number of index Quick Index record
      PARAMETER  (M_IDPG  = 4  )  !Total number of different ident pages
      PARAMETER  (M_IDSTNS = 20)  !Total number of idents to sort
      PARAMETER  (MAX_LIN = 5)    !Total number of lines
      PARAMETER  (IDENT_OPT = 2)  !Idents display option
      PARAMETER  (INT_TO_DEG = 180./32767) !Integer -> degrees conversion
C
C
C * External declaration
C
CVAX+
CVAX       EXTERNAL   RZX_AST
CVAX-
C
C * Include files.
C
      INCLUDE 'rx.inc'                    !NOFPC
CVAX+
CVAX       INCLUDE 'cae$par:io.par/nolist'     !NOFPC
CVAX-
C
C
C * Local variables declarations.
C
C
C
      INTEGER*4     BLOCKN        !Disk block to read
      INTEGER*4     TICQI         !Total number of ICao Quick Index records
      INTEGER*4     TIAQI         !Total number of IAta Quick Index records
      INTEGER*4     TIDQI         !Total number of ident Quick Index records
      INTEGER*4     RZXRCNT       !record count in file
      INTEGER*4     BLOCK         !# of next info block
      INTEGER*4     SECTOR        !disk sector to read
      INTEGER*4     BYTE_CNT      !byte count to read
      INTEGER*4     ERROR         !I/O status for internal write
      INTEGER*4     I,J,K,II,III  !loop index
      INTEGER*4     MODE          !active mode in view (page)
      INTEGER*4     MODE1         !internal copy of RWXMODE1
      INTEGER*4     MODEH         !active mode in view (header)
      INTEGER*4     MODEO         !option selected for IDEN/ICAO display
      INTEGER*4     PMODEO        !previous selected option
      INTEGER*4     MODEC         !command active mode
      INTEGER*4     IC_MODE       !active mode for ICAO/IATA section
      INTEGER*4     LINE_NO       !decoded line number
      INTEGER*4     INDX_NO       !decoded station index  number
      INTEGER*4     RECD_NO       !decoded station record number
      INTEGER*4     CNTR(24)      !iteration counter
      INTEGER*4     FTIME/25/     !FAIL volatile display time
      INTEGER*4     RTIME/15/     !REPN volatile display time
      INTEGER*4     LOCIFAIL      !Fail station index number
      INTEGER*4     LOCITOPE      !last vector used in TOP index
      INTEGER*4     LOCICAOE      !last vector used in ICAO index
      INTEGER*4     LOCIATAE      !last vector used in IATA index
      INTEGER*4     LOCITOPX(MTIEL)!TOP index , index pointer
      INTEGER*4     AZ_REC        !RZ record # for A-Z pages
      INTEGER*4     IC_REC        !RZ record # for ICAO info
      INTEGER*4     IA_REC        !RZ record # for IATA info
      INTEGER*4     ID_REC        !RZX record # for ident info
      INTEGER*4     OLDICSEC      !Old sector accessed for ICAO data
      INTEGER*4     OLDIOSEC      !Old sector accessed for IOBUF data
      INTEGER*4     OLDICBLK      !Old block accessed for ICAO data
      INTEGER*4     OLDIOBLK      !Old block accessed for IOBUF data
      INTEGER*4     IC_OFF        !offset for the start of ICAO/IATA sec
                                  !into the record
      INTEGER*4     IC_RWYS       !offset for the first runway
      INTEGER*4     MATCH         !offset for the match RWY/GATE string
      INTEGER*4     IC_GATE       !offset for the first gate
      INTEGER*4     IC_MKRS       !offset for the first marker
      INTEGER*4     AZ_SEL        !step to execute in RZX for A-Z pages
      INTEGER*4     IC_SEL        !step to execute in RZX for ICAO/IATA
      INTEGER*4     ID_SEL        !step to execute in RZX for Ident
      INTEGER*4     MAX_TOP       !maximum top index page
      INTEGER*4     MAX_IND       !maximum index page
      INTEGER*4     MAX_STN       !maximum station page
      INTEGER*4     IOS           !return error code
      INTEGER*4     MODEDISP(2)   !ident/icao mode select
      INTEGER*4     AZ_LINE       !Selected row number from A-Z pages
C
      INTEGER*4     REPN          !'REPN'
      INTEGER*4     RWY           !'RWY '
      INTEGER*4     GATE          !'GATE'
      INTEGER*4     IDEN          !'IDEN'
      INTEGER*4     ICAO          !'ICAO'
      INTEGER*4     FAIL          !'FAIL'
      INTEGER*4     RW            !'RW  '
      INTEGER*4     IM            !'IM  '
      INTEGER*4     OM            !'OM  '
      INTEGER*4     MM            !'MM  '
      INTEGER*4     T             !'T   '
      INTEGER*4     BLANK         !'    '
      INTEGER*2     RW2           !'RW'
      INTEGER*2     IM2           !'IM'
      INTEGER*2     OM2           !'OM'
      INTEGER*2     MM2           !'MM'
      PARAMETER (REPN = X'5245504E', RWY  = X'52575920')
      PARAMETER (GATE = X'47415445', IDEN = X'4944454E')
      PARAMETER (ICAO = X'4943414F', FAIL = X'4641494C')
      PARAMETER (RW   = X'52572020', IM   = X'494D2020')
      PARAMETER (OM   = X'4F4D2020', MM   = X'4D4D2020')
      PARAMETER (T    = X'54202020', BLANK= X'20202020')
      PARAMETER (RW2  = X'5257',     IM2  = X'494D')
      PARAMETER (OM2  = X'4F4D',     MM2  = X'4D4D')
C
      CHARACTER*28  HDRINIT(5,2)  !A-Z page header
      INTEGER*4     HDR           !HDRINIT array pointer
C
      INTEGER*4     ST_TYPE       !Selected station type
      INTEGER*1     ST_TYP(4)     !
      EQUIVALENCE  (ST_TYPE , ST_TYP)
C
      INTEGER*4     BUFMI4(34)    !used for equiv with RWXBUFM
      EQUIVALENCE  (RWXBUFM(1,1) , BUFMI4(1))
C
      LOGICAL*4     SSTATUS       !sector I/O status flag
C
CIBM+
      INTEGER*4    REC_NUM            !1536 byte record number
      INTEGER*4    REC_SIZE           !Number of bytes in 1 record
      INTEGER*4    STRT_POS           !Offset in bytes from start of record
      INTEGER*4    FR_STATUS(RZX_IOM) !Status of function request
      INTEGER*1     L_AIRFLD(4)   !Local copy of RLAIRFLD in bytes
      INTEGER*1     L_AIRIAT(4)   !Local copy of RLAIRIAT in bytes
      INTEGER*1     I1RWYSET(8)   !Local copy of RLRWYSET in bytes
      INTEGER*1     RLMODE        !Access mode for ICAO/IATA searching
CIBM-
CVAX+
CVAX       BYTE          L_AIRFLD(4)   !Local copy of RLAIRFLD in bytes
CVAX       BYTE          L_AIRIAT(4)   !Local copy of RLAIRIAT in bytes
CVAX       BYTE          I1RWYSET(8)   !Local copy of RLRWYSET in bytes
CVAX       BYTE          RLMODE        !Access mode for ICAO/IATA searching
CVAX-
      CHARACTER*4   C_AIRFLD      !Local copy of RLAIRFLD in string
      CHARACTER*4   C_AIRIAT      !Local copy of RLAIRIAT in string
      INTEGER*2     I2RWYSET(4)   !Local copy of RLRWYSET in half word
      CHARACTER*8   C8RWYSET      !Local copy of RLRWYSET in string
      EQUIVALENCE ( L_AIRFLD , RLAIRFLD , C_AIRFLD )
      EQUIVALENCE ( L_AIRIAT , RLAIRIAT , C_AIRIAT )
      EQUIVALENCE ( I1RWYSET , RLRWYSET , C8RWYSET , I2RWYSET )
      EQUIVALENCE  (RLREADY , RLMODE )
C
      LOGICAL*1     VALID         !Data is valid
      LOGICAL*1     AZ_TOP        !A-Z page display on Top crt
      LOGICAL*1     AZ_BOT        !A-Z page display on Bot crt
      LOGICAL*1     ACTNFLG       !flag signalling a reposition
      LOGICAL*1     RWY_TYP       !ICAO/IATA runway indicator
      LOGICAL*1     MKR_TYP       !ICAO/IATA marker indicator
      LOGICAL*1     GAT_TYP       !ICAO/IATA gate   indicator
      LOGICAL*1     COM_TYP       !ICAO/IATA com    indicator
      LOGICAL*1     ICL_UPD       !update CDB RWY/GATE buffer
      LOGICAL*1     LOOK          !DO WHILE loop flag
      LOGICAL*1     FOUND         !Find what we where looking for
      LOGICAL*1     SWAPMODEO     !exchange ICAO and IDEN field
      LOGICAL*1     RZX_FIRST     !first time radio aids page is called
      LOGICAL*1     SWAP_PAGE     !swap between the two radio aids pages
      LOGICAL*1     C_COLOR       !compute colour on page
      LOGICAL*1     OLD_KILL      !prev status kill upd flag
      LOGICAL*1     OLD_FAIL      !prev status fail button
      LOGICAL*1     OLD_CLR       !prev status clear button
      LOGICAL*1     OLD_CALL      !prev status clear all stn button
      LOGICAL*1     I_CLRAV       !internal value of XPCLRAV
      LOGICAL*1     LINPUT        !Enable line selection
      LOGICAL*1     LSPACE        !Code for ASCII space
CIBM+
      INTEGER*1     SPACE /X'20'/   !Code for ASCII space
CIBM-
CVAX+
CVAX       BYTE          SPACE /' '/   !Code for ASCII space
CVAX-
      EQUIVALENCE  (LSPACE , SPACE )
      CHARACTER*36  RZX_NAME      !character representation of RWXNAME
      EQUIVALENCE  (RWXNAME , RZX_NAME)
C
CVAX+
CVAX       CHARACTER*512 RZXSPAD(3)    !RZX scratch pad
CVAX-
      CHARACTER*80 MESSAGE/' '/   !error message TO_CONSOLE
      CHARACTER*4  TMP4           !temporary variable
      CHARACTER*28 LOCCTOPX(2,MTIEL)!TOP index information
C
CIBM+
      INTEGER*1     ICAOBUF(1536*M_TICQI)!ICAO index information
CIBM-
CVAX+
CVAX       BYTE         ICAOBUF(1536*M_TICQI)!ICAO index information
CVAX-
      CHARACTER*4  LOCCICAO(384*M_TICQI)!ICAO index information
      INTEGER*4    LOCIICAO(384*M_TICQI)!ICAO index information
      EQUIVALENCE ( LOCCICAO , LOCIICAO , ICAOBUF )
C
CIBM+
      INTEGER*1     IATABUF(1536*M_TIAQI)!IATA index information
CIBM-
CVAX+
CVAX       BYTE         IATABUF(1536*M_TIAQI)!IATA index information
CVAX-
      CHARACTER*4  LOCCIATA(384*M_TIAQI)!IATA index information
      INTEGER*4    LOCIIATA(384*M_TIAQI)!IATA index information
      EQUIVALENCE ( LOCCIATA , LOCIIATA , IATABUF )
C
C Internal buffer used for BLOCK I/O in A-Z pages
C
CIBM+
      INTEGER*1     IOBUF(1536)   !Station I/O buffer (main)
CIBM-
CVAX+
CVAX       BYTE          IOBUF(1536)   !Station I/O buffer (main)
CVAX-
      INTEGER*4     IOBUFI4(16,24)!integer*4 access   (main)
      INTEGER*4     IOBUF4I(384)  !integer*4 full access
      INTEGER*4     ZXIATI4(2,192)!integer*4 access for ICAO and IATA storage
      CHARACTER*4   ZXIATC4(2,192)!string*4  access for ICAO and IATA storage
      CHARACTER*64  IOBUFC64(24)  !string_64 access   (main)
      EQUIVALENCE  (IOBUF , IOBUFI4 , IOBUF4I , IOBUFC64 ,
     &                      ZXIATI4 , ZXIATC4 )
C
C Internal buffer used for BLOCK I/O in ICAO/IATA section
C
CIBM+
      INTEGER*1     ZXBUF(1536)   !Station I/O buffer (main)
      INTEGER*1     ZXICAI1(20,76)!integer*1 access for ICAO storage
CIBM-
CVAX+
CVAX       BYTE          ZXBUF(1536)   !Station I/O buffer (main)
CVAX       BYTE          ZXICAI1(20,76)!integer*1 access for ICAO storage
CVAX-
      INTEGER*4     ZXICAI4(5,76) !integer*4 access for ICAO storage
      INTEGER*2     ZXICAI2(10,76)!integer*2 access for ICAO storage
      LOGICAL*1     ZXICAL1(20,76)!logical*1 access for ICAO storage
      CHARACTER*20  ZXICAC20(76)  !string*20 access for ICAO storage
      EQUIVALENCE  (ZXBUF(1) , ZXICAI4  , ZXICAI2 , ZXICAL1 ,
     &                         ZXICAI1  , ZXICAC20 )
C
      INTEGER*4     RWXBUFI4(16,24)!integer*4 access for CDB buffer
      CHARACTER*64  RWXBUFC64(24) !string_64 access for CDB buffer
      EQUIVALENCE  (RWXBUF , RWXBUFI4 , RWXBUFC64 )
C
      INTEGER*4     RWXHDRI4(16,10)!integer*4 access for CDB header
      INTEGER*4     RWXHDR4I(160) !integer*4 access
      CHARACTER*64  RWXHDRC64(10) !string_64 access for CDB header
      CHARACTER*4   RWXHCOMM(32)  !comment at the end of the line
      EQUIVALENCE  (RWXHDR , RWXHDRI4 , RWXHCOMM  ,
     &                       RWXHDR4I , RWXHDRC64 )
C
C Variables used for RWY/GATE storage buffer
C
      INTEGER*1     TRXGATNAM(5,TRXAGAT)!local work copy of RXGATNAM
      INTEGER*4     TRXGATIDX(TRXAGAT)  !local work copy of RXGATIDX
      INTEGER*4     TRXGATREC(TRXAGAT)  !local work copy of RXGATREC
C
      INTEGER*4     TRXRWYNAM(TRXAGAT)  !local work copy of RXRWYNAM
      LOGICAL*1     TL1RWYNAM(4,TRXAGAT)!local work copy of RXRWYNAM
CVAX+
CVAX      BYTE          TI1RWYNAM(4,TRXARWYS)!local work copy of RXRWYNAM
CVAX-
C
CIBMSEL+
      INTEGER*1     TI1RWYNAM(4,TRXARWYS)!local work copy of RXRWYNAM
CIBMSEL-
      INTEGER*4     I4RWYNAM(TRXAGAT)   !equivakent to      RXRWYNAM
      INTEGER*4     TRXRWYIDX(TRXAGAT)  !local work copy of RXRWYIDX
      INTEGER*4     TRXRWYREC(TRXAGAT)  !local work copy of RXRWYREC
C
      EQUIVALENCE  (RXRWYNAM  , I4RWYNAM  )
      EQUIVALENCE  (TRXRWYNAM , TL1RWYNAM )
      EQUIVALENCE  (TRXRWYNAM , TI1RWYNAM )
C
      INTEGER*2     TRXBGAT     !local work copy of RXBGAT
      INTEGER*2     TRXBRWYS    !local work copy of RXBRWYS
      INTEGER*2     M_TRXBGAT   !maximum of RXBGAT
      INTEGER*2     M_TRXBRWYS  !maximum of RXBRWYS
C
C PY+
      INTEGER*4     RWYNAM              !Opposite runway name
CVAX+
CVAX      BYTE          I1RWYNAM (4)        !Opposite runway name
CVAX-
C
CIBMSEL+
      INTEGER*1     I1RWYNAM (4)        !Opposite runway name
CIBMSEL-
      EQUIVALENCE  (RWYNAM,I1RWYNAM)
C
      INTEGER*4     OPP_RWY (TRXARWYS)  !Pairs of opposite runway pointers
      INTEGER*4     RWY_PTR (TRXARWYS)  !Sorted runway pointers
      LOGICAL*1     MATCHED (TRXARWYS)  !Matched runway flag
      LOGICAL*1     NOTSORTED           !Data is not sorted
C
      INTEGER*4     RWY1_I4             !Runway name
      INTEGER*4     RWY2_I4             !Runway name
      CHARACTER*4   RWY1_C4             !Runway name
      CHARACTER*4   RWY2_C4             !Runway name
CVAX+
CVAX      BYTE          RWY1_LETTER (4)     !Letter of runway name
CVAX      BYTE          RWY2_LETTER (4)     !Letter of runway name
CVAX-
C
CIBMSEL+
          INTEGER*1     RWY1_LETTER (4)     !Letter of runway name
          INTEGER*1     RWY2_LETTER (4)     !Letter of runway name
CIBMSEL-
C
      EQUIVALENCE (RWY1_I4, RWY1_C4, RWY1_LETTER)
      EQUIVALENCE (RWY2_I4, RWY2_C4, RWY2_LETTER)
C PY-
C
C -- Internal variables used for ident processing
C
      Character*6   CIDETOPX (M_TIDQI * 256) !Ident top index, C*6 access
      Integer*4     I4DETOPX (M_TIDQI * 384) !Ident top index, I*4 access
CIBM+
      INTEGER*1     IIDETOPX (M_TIDQI * 1536)!Ident top index, I*1 access
CIBM-
CVAX+
CVAX       BYTE          IIDETOPX (M_TIDQI * 1536)!Ident top index, I*1 access
CVAX-
      Equivalence   (CIDETOPX,IIDETOPX,I4DETOPX)
C
      Character*40  ZIDISP (5,M_IDPG)        !Display buffer
CIBM+
      INTEGER*1     ZIDISP1 (40,5,M_IDPG)    !Display buffer
CIBM-
CVAX+
CVAX       BYTE          ZIDISP1 (40,5,M_IDPG)    !Display buffer
CVAX-
      Equivalence   (RXZIDISP,ZIDISP,ZIDISP1)
      Character*15  L_L_STR                  !LAT/LON string
      Character*8   IDENT                    !Ident to process
      Character*8   INP_IDE  (M_IDPG)        !Input ident from page
      Character*8   OLINP_IDE (M_IDPG)       !Old input ident
      Character*8   STN_IDE (M_IDSTNS,M_IDPG)!Station ident
      Character*6   ZX_IDC6I (256)           !C*6 data access (index)
      Character*6   ZX_IDC6D (4,64)          !C*6 data access (data)
      Character*6   T_IDE                    !Temporary ident
      Character*4   STN_TYP (M_IDSTNS,M_IDPG)!Station type
      Integer*4     I4ST_TYP (M_IDSTNS,M_IDPG) !Station type (I*4)
CIBM+
      INTEGER*1     I1ST_TYP (M_IDSTNS,M_IDPG) !Station type (I*1)
CIBM-
CVAX+
CVAX       BYTE          I1ST_TYP (M_IDSTNS,M_IDPG) !Station type (I*1)
CVAX-
      Equivalence   (STN_TYP,I4ST_TYP,I1ST_TYP)
      Character*4   T_TYP                    !Temporary type
      Integer*4     I4T_TYP                  !Temporary type (I*4)
      Equivalence   (T_TYP,I4T_TYP)
      Character*4   ZIIDEC(MAX_LIN,M_IDPG)   !Ident
      Integer*4     ZIIDE (MAX_LIN,M_IDPG)   !Ident
      Equivalence   (RXZIIDE, ZIIDEC, ZIIDE)
      Real*4        STN_LAT (M_IDSTNS,M_IDPG)!Station latitude
      Real*4        STN_LON (M_IDSTNS,M_IDPG)!Station longitude
      Real*4        STN_FRE (M_IDSTNS,M_IDPG)!Station frequency
      Real*4        STN_RNG (M_IDSTNS,M_IDPG)!Range to station (deg^2)
      Real*4        T_RNG                    !Temporary range
      Real*4        T_FRE                    !Temporary frequency
      Real*4        T_LAT                    !Temporary latitude
      Real*4        T_LON                    !Temporary longitude
      Integer*4     INDEX                    !Loop counter
      Integer*4     LINE                     !Line counter
      Integer*4     LIN_PTR (M_IDPG)         !Line pointer
      Integer*4     NO_LINE (M_IDPG)         !Number of lines displayed
      Integer*4     NO_STNS (M_IDPG)         !Number of stations displayed
      Integer*4     RXZIND                   !Index pointer
      Integer*4     STN_CNT                  !Counter
      Integer*4     STN_IDX (M_IDSTNS,M_IDPG)!Station index number
      Integer*4     STN_REC (M_IDSTNS,M_IDPG)!Station index number
      Integer*4     T_IDX                    !Temporary index number
      Integer*4     T_REC                    !Temporary record number
      Integer*4     ZITYP (MAX_LIN,M_IDPG)   !Type
      Equivalence   (RXZITYP, ZITYP)
      Integer*4     ZX_IDI4B (6,64)          !I*4 data access
      Integer*2     ZX_IDI2B (12,64)         !I*2 data access
CIBM+
      INTEGER*1     ZX_IDI1B (24,64)         !I*1 data access
      INTEGER*1     ZX_IDBUF (1536)          !Ident data buffer
      INTEGER*1     STAR/X'2A'/                !'*'
CIBM-
CVAX+
CVAX       BYTE          ZX_IDI1B (24,64)         !I*1 data access
CVAX       BYTE          ZX_IDBUF (1536)          !Ident data buffer
CVAX       BYTE          STAR/'*'/                !'*'
CVAX-
      Logical*1     NEWIDENT                 !Process new ident
      Logical*1     SORTED                   !Data is sorted flag
      Equivalence (ZX_IDBUF,ZX_IDC6I,ZX_IDC6D,
     &             ZX_IDI1B,ZX_IDI2B,ZX_IDI4B)
      Equivalence (RXZIDENT,INP_IDE)
C PY+
CIBM+
      Integer*1
     &          DUMX30,               ! DUMMY PARAMETERS
     &          DUMX45,               ! DUMMY PARAMETERS
     &          DUMXL ,               ! DUMMY PARAMETERS
     &          DUMXR                 ! DUMMY PARAMETERS
C
      PARAMETER ( DUMX30 = '30'X )
      PARAMETER ( DUMX45 = '45'X )
      PARAMETER ( DUMXL  = '4C'X )
      PARAMETER ( DUMXR  = '52'X )
CIBM-
C PY-
C
      DATA  HDRINIT / 'PAGE FWD'
     -,               'PAGE BACK'
     -,               'A-Z INDEX'
     -,               'A-Z TOP INDEX'
     -,               'IDENT/ICAO DISPLAY  SET  TO '
     -,               'PAGE   FWD'
     -,               'PAGE   BACK'
     -,               'A-Z    INDEX'
     -,               'A-Z TOPINDEX'
     -,               'IDENT /ICAO'   /
     &,     FR_STATUS / RZX_IOM * 1/
C
C
C
      ENTRY RXRZX
C
C Execute the first pass initialization
C
      IF (.NOT.RWXINIT) THEN
        MODE     = 3
        MODEH    = 0
        AZ_REC   = 0
        AZ_SEL   = 1
        IC_SEL   = 1
        ID_SEL   = 1
        MODEC    = 1
        OLDIOSEC = -1
        OLDICSEC = -1
        RXZIND   = RXZINO
        DO I = 1, M_IDPG
          OLINP_IDE (I) = INP_IDE (I)
        ENDDO
        IF (RXTOUCH) THEN
          HDR = 2
        ELSE
          HDR = 1
        END IF
C
C Validate RWBUTTON setting
C
        IF (RWBUTTON.LT.1 .OR. RWBUTTON.GT.5) THEN
          MESSAGE = '    RWBUTTON label not initialized in DISPINIT'
          CALL TO_CONSOLE(MESSAGE)
          MESSAGE = '    Should be initialized to 1, 2, 3, 4 or 5'
          CALL TO_CONSOLE(MESSAGE)
          RETURN
        ENDIF
C
        RWXINIT  = .TRUE.
      ENDIF
C
CIBM+
      IF ((FR_STATUS (1) .EQ. 1) .AND. (RXZXCODE (1) .EQ. 1)) THEN
        RXZXFLG(1) = .TRUE.
      ENDIF
CIBM-
C
      IF (RXZXCODE(1).EQ.1 .AND. RXZXFLG(1) .AND. RXZXFVLD ) THEN
         GOTO (100,200,300) AZ_SEL
      ENDIF
      GOTO 10000
C
C
C  Read in local buffer TOP INDEX information
C
 100  CONTINUE
C
      MAX_TOP  = ZX_LAZQI - ZX_FAZQI + 1
      MAX_IND  = ZX_LAZIN - ZX_FAZIN + 1
      MAX_STN  = ZX_LAZST - ZX_FAZST + 1
C
      IF (AZ_REC .LT. ZX_FAZQI) THEN
        AZ_REC = ZX_FAZQI
      ELSE IF (AZ_REC .GT. ZX_LAZQI) THEN
        AZ_REC = ZX_LAZQI
      ENDIF
C
CIBM+
      RXZXCODE(1) = 0
      RXZXFLG(1)  = .FALSE.
      FR_STATUS (1) = 0
      REC_NUM = AZ_REC-1
      REC_SIZE = 1536
      BYTE_CNT = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS(1),RXZXCODE(1),%VAL(RXZXDCB(1))
     &       ,%VAL(REC_SIZE),IOBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
C
      AZ_SEL = AZ_SEL + 1
CIBM-
CSEL++        ------- SEL Code -------
CSEL       BYTE_CNT    = 1536
CSEL       RXZXCODE(1) = 0
CSEL       RXZXFLG(1)  = .FALSE.
CSEL       SECTOR = 2 * (AZ_REC - 1)
CSEL       CALL S_READ(RZX_FDB(0,1),SSTATUS,,IOBUFI4,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZXCODE(1) = IAND(RZX_FDB(5,1),X'0000FFFF')
CSEL         RXZXFLG(1)  = .TRUE.
CSEL       ENDIF
CSEL       AZ_SEL = AZ_SEL + 1
CSEL-         ------------------------
CVAX+
CVAX       BYTE_CNT    = 1536
CVAX       RXZXCODE(1) = 0
CVAX       RXZXFLG(1)  = .FALSE.
CVAX       BLOCKN   = 3*AZ_REC - 2
CVAX       CALL NBLKIORW (%VAL(RXZXDCB),IOBUF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZXCODE(1),%VAL(IO$_READVBLK),%REF(RZXSPAD(1)),
CVAX      &               RXZXFLG(1),RZX_AST,)
CVAX       AZ_SEL = AZ_SEL + 1
CVAX-
      GOTO 10000
C
C  Save the TOP INDEX information into the local buffer
C  the format will be as follow:
C
C  |  first name in index page  |   Last name in index page  | record number
C          LOCCTOPX(1,i)                 LOCCTOPX(2,i)           LOCITOPX(i)
C
C
 200  I = AZ_REC - ZX_FAZQI
      !check the presents S/W limits
      IF ( I+1 .GT. MTIPG ) THEN
        MESSAGE = ' '
        MESSAGE = ' %NAV, Fatal error in RX1 module. '
        CALL TO_CONSOLE(MESSAGE)
        RXZXCODE(1)    = -999
        GOTO 10000
      ENDIF
C
      DO J = 1 , MTIELPG
        K = I*MTIELPG + J
        LOCCTOPX(1,K) = IOBUFC64(J)(ZX_PSNI1:ZX_PENI1)
        LOCCTOPX(2,K) = IOBUFC64(J)(ZX_PSNI2:ZX_PENI2)
        LOCITOPX(K)   = IOBUFI4(ZX_PBLK,J)
        IF (IOBUFI4(ZX_PBLK,J) .NE. 0) LOCITOPE = K
      ENDDO
      IF (LOCITOPE .EQ. K) THEN
        IF (AZ_REC .EQ. ZX_LAZQI) THEN
          AZ_SEL = AZ_SEL + 1
        ELSE
          AZ_SEL = AZ_SEL - 1
          AZ_REC = AZ_REC + 1
        ENDIF
      ELSE
        AZ_SEL = AZ_SEL + 1
      ENDIF
      GOTO 10000
C
C
C Determine which mode of operation
C
 300  CONTINUE
C
      XPCALLAV = ((RVNKIL - RVNAKIL) .GT. 0)
      !clear all stations button
      IF (XPCALLST) THEN
        XPCALLST = .FALSE.
        TCRALLST = .TRUE.
      ENDIF
C
      AZ_TOP = TCMRAOK(1)
      AZ_BOT = TCMRAOK(2)
C
      IF ( AZ_TOP .OR. AZ_BOT ) THEN        ! A-Z page in view
C
        IF ( AZ_TOP ) THEN                  ! Get selected line number
          IF ( RWROWPOS(1).NE.0 ) THEN      ! of top CRT
            AZ_LINE     = RWROWPOS(1)
            RWROWPOS(1) = 0
          ELSE IF ( RWXPLINE.NE.0 ) THEN
            AZ_LINE     = RWXPLINE
            RWXPLINE    = 0
          END IF
        ENDIF
C
        IF ( AZ_BOT ) THEN                  ! Get selected line number
          IF ( RWROWPOS(3).NE.0 ) THEN      ! of bottom CRT
            AZ_LINE     = RWROWPOS(3)
            RWROWPOS(3) = 0
          ELSE IF ( RWXPLINE.NE.0 ) THEN
            AZ_LINE     = RWXPLINE
            RWXPLINE    = 0
          END IF
        ENDIF
C
        !delay to reset FAIL and REPN display to blank
        IF (ACTNFLG) THEN
          ACTNFLG = .FALSE.
          DO I = 1,24
            IF (CNTR(I).GT.0) THEN
              ACTNFLG = .TRUE.
              CNTR(I) = CNTR(I) - 1
              IF (CNTR(I).EQ.0) THEN
                BUFMI4(I) = BLANK
              ENDIF
            ENDIF
          ENDDO
         ENDIF
C
C Check if any change in kill status
C
         IF (RVKILFLG .AND. .NOT. OLD_KILL) C_COLOR  = .TRUE.
C
C If first time verify which mode to be used
C
        IF ( RZX_FIRST ) THEN                        ! First access
          RZX_FIRST = .FALSE.
          IF ( RWXPREV(1) ) THEN
            RWXPREV(1)  = .FALSE.
            RWXMODE2 = AZ_REC
          ELSE IF ( RWAZMODI.EQ.8 ) THEN   ! Top index
            AZ_LINE = 6
          ELSE IF ( RWAZMODI.EQ.4 ) THEN   ! Index
            AZ_LINE = 5
          ELSE IF ( RWAZMODI.EQ.10 ) THEN   ! Use first letters of stn name
            IF ( RWXMODE1.LE.0 ) AZ_LINE = 6   ! Top index if no letter specifie
          ENDIF
        ENDIF
C
        LINPUT = MODE.NE.2 .OR. RWBUTTON.NE.4 .OR. XPFAIL .OR. XPREPOS
C
        IF ( AZ_LINE.EQ.3 .OR. RWXPFWD(1) ) THEN    ! Page forward
          RWXPFWD(1) = .FALSE.
          AZ_LINE     = 3
          RWXMODE     = 2
        ELSE IF ( AZ_LINE.EQ.4 .OR. RWXPBCK(1) ) THEN ! Page backward
          RWXPBCK(1) = .FALSE.
          AZ_LINE     = 4
          RWXMODE     = 3
        ELSE IF ( AZ_LINE.EQ.5 ) THEN                ! A-Z index
          RWXMODE = 4
          AZ_REC = ZX_FAZIN
        ELSE IF ( AZ_LINE.EQ.6 ) THEN                ! A-Z top index
          RWXMODE = 8
          AZ_REC = ZX_FAZQI
        ELSE IF ( AZ_LINE.EQ.7 .AND. MODE.EQ.2 ) THEN  ! IDENT/ICAO toggle
          RWXMODE = 12
        ELSE IF ( AZ_LINE.GE.11 .AND. AZ_LINE.LE.34
     &            .AND. LINPUT ) THEN                ! Line selected
          LINE_NO = AZ_LINE - 10
          RWXMODE = 6
        ELSE IF ( RWXMODE1.NE.0 ) THEN               ! Access proper page using
          RWXMODE = 10                               ! first letters of name
        ELSE IF ( RWXMODE2.NE.0 ) THEN               ! Access a particular recor
          RWXMODE = 9
        ELSE IF ( C_COLOR ) THEN                     ! Line colouring
          RWXMODE = 14
        ENDIF
        !prevents buttons activation
        IF ( AZ_LINE.NE.0 .AND. AZ_LINE.LE.10 ) THEN
          IF ( XPFAIL .OR. XPCLR .OR. XPREPOS ) THEN
            XPFAIL  = .FALSE.
            XPCLR   = .FALSE.
            XPREPOS = .FALSE.
            RWXMODE = 0
          ENDIF
        ENDIF
        !reset line number
        AZ_LINE = 0
C
        !new input to process?
        IF ( RWXMODE .NE. 0 ) THEN
          MODEC   = RWXMODE
          RWXMODE = 0
        ENDIF
        !dark concept for fail and clear stn button
        XPFAILAV = MODE .EQ. 2
        XPREPOSA = MODE .EQ. 2
        XPCLRAV  = I_CLRAV
      ELSE IF ( .NOT. RZX_FIRST) THEN
        RZX_FIRST = .TRUE.
        MODEC     = 1
        XPFAILAV  = .FALSE.
        XPCLRAV   = .FALSE.
        I_CLRAV   = .FALSE.
        XPREPOSA  = .FALSE.
        XPFAIL    = .FALSE.
        XPCLR     = .FALSE.
        XPREPOS   = .FALSE.
        DO I = 1,24
          BUFMI4(I)  = BLANK
          CNTR(I)    = 0
        ENDDO
      ENDIF
C
      GOTO (10000,             !next step ICAO/IATA
     &      1200,              !forward
     &      1300,              !backward
     &      1400,              !index
     &      1500,              !spare
     &      1600,              !select by line
     &      1700,              !page
     &      1800,              !top index
     &      1900,              !record number
     &      2000,2100,         !name
     &      2200,2300,         !change option for IDENT/ICAO
     &      2400 ) MODEC       !compute color change
C
      MODEC = 1
      GOTO 9999
C
C
C Display on screen the next page
C
 1200 CONTINUE
      MODEC     = 1
      IF (MODE .EQ. 1) THEN
        !index mode selected
        AZ_REC = AZ_REC + 1
        IF (AZ_REC .LT. ZX_FAZIN) THEN
          AZ_REC = ZX_FAZIN
        ELSE IF (AZ_REC .GT. ZX_LAZIN) THEN
          IF (RWXLWRAP) THEN
            AZ_REC = ZX_FAZIN
          ELSE
            AZ_REC = ZX_LAZIN
          ENDIF
        ENDIF
        GOTO 1410
      ELSE IF (MODE .EQ. 2) THEN
        !page mode selected
        AZ_REC = AZ_REC + 1
        IF (AZ_REC .LT. ZX_FAZST) THEN
          AZ_REC = ZX_FAZST
        ELSE IF (AZ_REC .GT. ZX_LAZST) THEN
          IF (RWXLWRAP) THEN
            AZ_REC = ZX_FAZST
          ELSE
            AZ_REC = ZX_LAZST
          ENDIF
        ENDIF
        GOTO 1710
      ELSE IF (MODE .EQ. 3) THEN
        !top index mode selected
        AZ_REC = AZ_REC + 1
        IF (AZ_REC .LT. ZX_FAZQI) THEN
          AZ_REC = ZX_FAZQI
        ELSE IF (AZ_REC .GT. ZX_LAZQI) THEN
          IF (RWXLWRAP) THEN
            AZ_REC = ZX_FAZQI
          ELSE
            AZ_REC = ZX_LAZQI
          ENDIF
        ENDIF
        GOTO 1810
      ENDIF
      GOTO 9999
C
C Display on screen the previous page
C
 1300 CONTINUE
      MODEC    = 1
      IF (MODE .EQ. 1) THEN
        !index mode selected
        AZ_REC = AZ_REC - 1
        IF (AZ_REC .GT. ZX_LAZIN) THEN
          AZ_REC = ZX_LAZIN
        ELSE IF (AZ_REC .LT. ZX_FAZIN) THEN
          IF (RWXLWRAP) THEN
            AZ_REC = ZX_LAZIN
          ELSE
            AZ_REC = ZX_FAZIN
          ENDIF
        ENDIF
        GOTO 1410
      ELSE IF (MODE .EQ. 2) THEN
        !page mode selected
        AZ_REC = AZ_REC - 1
        IF (AZ_REC .GT. ZX_LAZST) THEN
          AZ_REC = ZX_LAZST
        ELSE IF (AZ_REC .LT. ZX_FAZST) THEN
          IF (RWXLWRAP) THEN
            AZ_REC = ZX_LAZST
          ELSE
            AZ_REC = ZX_FAZST
          ENDIF
        ENDIF
        GOTO 1710
      ELSE IF (MODE .EQ. 3) THEN
        !index mode selected
        AZ_REC = AZ_REC - 1
        IF (AZ_REC .GT. ZX_LAZQI) THEN
          AZ_REC = ZX_LAZQI
        ELSE IF (AZ_REC .LT. ZX_FAZQI) THEN
          IF (RWXLWRAP) THEN
            AZ_REC = ZX_LAZQI
          ELSE
            AZ_REC = ZX_FAZQI
          ENDIF
        ENDIF
        GOTO 1810
      ENDIF
      GOTO 9999
C
C Display on screen the selected index pages
C
 1400 CONTINUE       !index page
      MODEO  = MODEDEF
      IF (AZ_REC .LT. ZX_FAZIN) THEN
        AZ_REC = ZX_FAZIN
      ELSE IF (AZ_REC .GT. ZX_LAZIN) THEN
        AZ_REC = ZX_LAZIN
      ENDIF
C
 1410 CONTINUE
CIBM+
      RXZXCODE(1) = 0
      RXZXFLG(1)  = .FALSE.
      FR_STATUS (1) = 0
      REC_NUM = AZ_REC-1
      REC_SIZE = 1536
      BYTE_CNT    = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS(1),RXZXCODE(1),%VAL(RXZXDCB(1))
     &       ,%VAL(REC_SIZE),RWXBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
CIBM-
CSEL++        ------- SEL Code -------
CSEL       BYTE_CNT    = 1536
CSEL       RXZXCODE(1) = 0
CSEL       RXZXFLG(1)  = .FALSE.
CSEL       SECTOR      = 2*(AZ_REC - 1)
CSEL       CALL S_READ(RZX_FDB(0,1),SSTATUS,,RWXBUFI4,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZXCODE(1) = IAND(RZX_FDB(5,1),X'0000FFFF')
CSEL         RXZXFLG(1)  = .TRUE.
CSEL       ENDIF
CSEL-         ------------------------
CVAX+
CVAX       BYTE_CNT    = 1536
CVAX       RXZXCODE(1) = 0
CVAX       RXZXFLG(1)  = .FALSE.
CVAX       BLOCKN   = AZ_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZXDCB),RWXBUF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZXCODE(1),%VAL(IO$_READVBLK),%REF(RZXSPAD(1)),
CVAX      &               RXZXFLG(1),RZX_AST,)
CVAX-
      MODEC    = 14
      MODE     = 1
      SWAP_PAGE = .TRUE.
      RWRADFP(1)  = AZ_REC .EQ. ZX_FAZIN
      RWRADLP(1)  = AZ_REC .EQ. ZX_LAZIN
      GOTO 9999
C
C Spare
C
 1500 CONTINUE
      GOTO 9999
C
C Activate the appropriate line number
C
 1600 CONTINUE
C
      GOTO ( 1601 ,               ! repos button
     &       1602 ,               ! fail and clear buttons
     &       1602 ,               ! fail, clear and repos buttons
     &       1601 ,               ! stn toggle and repos buttons
     &       1602 )  RWBUTTON     ! stn toggle button
C
C
C
 1601 CONTINUE
C
C--- Index
C
      IF (MODE .EQ. 1) THEN
C
        AZ_REC = RWXBUFI4(16,LINE_NO)
        MODE   = 2
        GOTO 1700
C
C--- Station page
C
      ELSE IF (MODE .EQ. 2) THEN
C
        INDX_NO = RWXBUFI4(15,LINE_NO)
        RECD_NO = RWXBUFI4(16,LINE_NO)
        DO I = 1,4
          IF ( RXTOUCH ) THEN
            ST_TYP(I) = RWXBUF(I+39,LINE_NO)
          ELSE
            ST_TYP(I) = RWXBUF(I+34,LINE_NO)
          ENDIF
        ENDDO
C
C--- Reposition to station
C
        IF ( XPREPOS ) THEN
C
          TCMREPOS = .TRUE.
          TAXPOS   = INDX_NO
          TCPOSN   = 0
          BUFMI4(LINE_NO) = REPN
          ACTNFLG         = .TRUE.
          CNTR(LINE_NO)   = RTIME
          MODEC    = 1
          XPREPOS  = .FALSE.
C
C--- Toggle station
C
        ELSE IF (RWBUTTON.EQ.1 .OR. XPFAIL) THEN
C
          IF ( ST_TYPE.EQ.RWY  .OR. ST_TYPE.EQ.GATE ) THEN
C
            MODEC  = 1
            XPFAIL = .FALSE.
C
          ELSE IF (.NOT.RWXKILL(LINE_NO) .AND. TAXKILL.EQ.0) THEN
C
            TAXKILL  = INDX_NO
            XPFAIL   = .FALSE.
            TAKILTYP = -1
            RWXKILL(LINE_NO) = .TRUE.
            BUFMI4(LINE_NO)  = FAIL
            ACTNFLG          = .TRUE.
            CNTR(LINE_NO)    = FTIME
            I_CLRAV          = .TRUE.
            MODEC            = 1
C
          ELSE IF (RWXKILL(LINE_NO) .AND. TAXKILL.EQ.0) THEN
C
            TAXKILL  = INDX_NO
            TAKILTYP = 0
            RWXKILL(LINE_NO) = .FALSE.
            MODEC    = 1
            XPFAIL = .FALSE.
C
          ENDIF
        ENDIF
C
C
C--- Top index
C
      ELSE IF (MODE .EQ. 3) THEN
C
        AZ_REC = RWXBUFI4(16,LINE_NO)
        MODE   = 1
        GOTO 1400
C
      ENDIF
      GOTO 9999
C
C Activate the appropriate line number
C
 1602 CONTINUE
C
C--- Index
C
      IF (MODE .EQ. 1) THEN
C
        AZ_REC = RWXBUFI4(16,LINE_NO)
        MODE   = 2
        GOTO 1700
C
C
C--- Station page
C
      ELSE IF (MODE .EQ. 2) THEN
C
        INDX_NO = RWXBUFI4(15,LINE_NO)
        RECD_NO = RWXBUFI4(16,LINE_NO)
        DO I = 1,4
          IF ( RXTOUCH ) THEN
            ST_TYP(I) = RWXBUF(I+39,LINE_NO)
          ELSE
            ST_TYP(I) = RWXBUF(I+34,LINE_NO)
          ENDIF
        ENDDO
C
C--- Toggle stn
C
        IF ( XPFAIL .AND. RWBUTTON.EQ.5 ) THEN
C
          IF ( ST_TYPE.EQ.RWY  .OR. ST_TYPE.EQ.GATE ) THEN
C
            MODEC  = 1
            XPFAIL = .FALSE.
C
          ELSE IF (.NOT.RWXKILL(LINE_NO) .AND. TAXKILL.EQ.0) THEN
C
            TAXKILL  = INDX_NO
            XPFAIL   = .FALSE.
            TAKILTYP = -1
            RWXKILL(LINE_NO) = .TRUE.
            BUFMI4(LINE_NO)  = FAIL
            ACTNFLG          = .TRUE.
            CNTR(LINE_NO)    = FTIME
            I_CLRAV          = .TRUE.
            MODEC            = 1
C
          ELSE IF (RWXKILL(LINE_NO) .AND. TAXKILL.EQ.0) THEN
C
            TAXKILL  = INDX_NO
            TAKILTYP = 0
            RWXKILL(LINE_NO) = .FALSE.
            MODEC    = 1
            XPFAIL = .FALSE.
C
          ENDIF
C
C--- Fail stn
C
        ELSE IF ( XPFAIL ) THEN
C
          IF (.NOT.RWXKILL(LINE_NO)) THEN
C
            IF ( ST_TYPE.EQ.RWY  .OR. ST_TYPE.EQ.GATE ) THEN
C
              XPFAIL = .FALSE.
              MODEC  = 1
C
            ELSE IF (TAXKILL.EQ.0) THEN
C
              TAXKILL  = INDX_NO
              XPFAIL   = .FALSE.
              TAKILTYP = -1
              RWXKILL(LINE_NO) = .TRUE.
              BUFMI4(LINE_NO)  = FAIL
              ACTNFLG          = .TRUE.
              CNTR(LINE_NO)    = FTIME
              I_CLRAV          = .TRUE.
              MODEC            = 1
C
            ENDIF
C
          ELSE
C
            XPFAIL = .FALSE.
            MODEC  = 1
C
          ENDIF
C
C--- Clear station
C
        ELSE IF ( XPCLR ) THEN
C
          IF (RWXKILL(LINE_NO)) THEN
C
            IF (TAXKILL.EQ.0) THEN
              TAXKILL  = INDX_NO
              XPCLR    = .FALSE.
              TAKILTYP = 0
              RWXKILL(LINE_NO) = .FALSE.
              MODEC    = 1
            ENDIF
C
          ELSE
C
            XPCLR = .FALSE.
            MODEC = 1
C
          ENDIF
C
C--- Reposition to station
C
        ELSE IF ( XPREPOS .OR. RWBUTTON.EQ.2 .OR. RWBUTTON.EQ.5 ) THEN
C
          TCMREPOS = .TRUE.
          TAXPOS   = INDX_NO
          TCPOSN   = 0
          BUFMI4(LINE_NO) = REPN
          ACTNFLG         = .TRUE.
          CNTR(LINE_NO)   = RTIME
          MODEC    = 1
          XPREPOS  = .FALSE.
C
        ENDIF
C
C
C--- Top index
C
      ELSE IF (MODE .EQ. 3) THEN
C
        AZ_REC = RWXBUFI4(16,LINE_NO)
        MODE   = 1
        GOTO 1400
C
      ENDIF
      GOTO 9999
C
C Display on screen the selected station pages
C
 1700 CONTINUE      !station page
      MODEO  = MODEDEF
      IF (AZ_REC .LT. ZX_FAZST) THEN
        AZ_REC = ZX_FAZST
      ELSE IF (AZ_REC .GT. ZX_LAZST) THEN
        AZ_REC = ZX_LAZST
      ENDIF
C
 1710 CONTINUE
CIBM+
        RXZXCODE(1) = 0
        RXZXFLG(1)  = .FALSE.
        FR_STATUS (1) = 0
        REC_NUM = AZ_REC-1
        REC_SIZE = 1536
        BYTE_CNT    = 1536
        STRT_POS = 0
        CALL CAE_IO_READ(FR_STATUS(1),RXZXCODE(1),%VAL(RXZXDCB(1))
     &       ,%VAL(REC_SIZE),RWXBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
C
CIBM-
CSEL++        ------- SEL Code -------
CSEL       BYTE_CNT    = 1536
CSEL       RXZXCODE(1) = 0
CSEL       RXZXFLG(1)  = .FALSE.
CSEL       SECTOR      = 2*(AZ_REC - 1)
CSEL       CALL S_READ(RZX_FDB(0,1),SSTATUS,,RWXBUFI4,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZXCODE(1) = IAND(RZX_FDB(5,1),X'0000FFFF')
CSEL         RXZXFLG(1)  = .TRUE.
CSEL       ENDIF
CSEL-         ------------------------
CVAX+
CVAX       BYTE_CNT    = 1536
CVAX       RXZXCODE(1) = 0
CVAX       RXZXFLG(1)  = .FALSE.
CVAX       BLOCKN   = AZ_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZXDCB),RWXBUF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZXCODE(1),%VAL(IO$_READVBLK),%REF(RZXSPAD(1)),
CVAX      &               RXZXFLG(1),RZX_AST,)
CVAX-
      MODEC = 13
      MODE  = 2
      SWAP_PAGE = .TRUE.
      RWRADFP(1)  = AZ_REC .EQ. ZX_FAZST
      RWRADLP(1)  = AZ_REC .EQ. ZX_LAZST
      GOTO 9999
C
C Display on screen the selected top index pages
C
 1800 CONTINUE     !top index page
      MODEO  = MODEDEF
      IF (AZ_REC .LT. ZX_FAZQI) THEN
        AZ_REC = ZX_FAZQI
      ELSE IF (AZ_REC .GT. ZX_LAZQI) THEN
        AZ_REC = ZX_LAZQI
      ENDIF
C
 1810 CONTINUE
CIBM+
      RXZXCODE(1) = 0
      RXZXFLG(1)  = .FALSE.
      FR_STATUS (1) = 0
      REC_NUM = AZ_REC-1
      REC_SIZE = 1536
      BYTE_CNT    = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS(1),RXZXCODE(1),%VAL(RXZXDCB(1))
     &       ,%VAL(REC_SIZE),RWXBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
CIBM-
CSEL++        ------- SEL Code -------
CSEL       BYTE_CNT    = 1536
CSEL       RXZXCODE(1) = 0
CSEL       RXZXFLG(1)  = .FALSE.
CSEL       SECTOR      = 2*(AZ_REC - 1)
CSEL       CALL S_READ(RZX_FDB(0,1),SSTATUS,,RWXBUFI4,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZXCODE(1) = IAND(RZX_FDB(5,1),X'0000FFFF')
CSEL         RXZXFLG(1)  = .TRUE.
CSEL       ENDIF
CSEL-         ------------------------
CVAX+
CVAX       BYTE_CNT    = 1536
CVAX       RXZXCODE(1) = 0
CVAX       RXZXFLG(1)  = .FALSE.
CVAX       BLOCKN   = AZ_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZXDCB),RWXBUF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZXCODE(1),%VAL(IO$_READVBLK),%REF(RZXSPAD(1)),
CVAX      &               RXZXFLG(1),RZX_AST,)
CVAX-
      MODEC = 14
      MODE  = 3
      SWAP_PAGE = .TRUE.
      RWRADFP(1)  = AZ_REC .EQ. ZX_FAZQI
      RWRADLP(1)  = AZ_REC .EQ. ZX_LAZQI
      GOTO 9999
C
C Display on screen the given record
C
 1900 CONTINUE
      AZ_REC = RWXMODE2
      RWXMODE2 = 0
      IF (AZ_REC .LT. ZX_FAZQI) THEN
        GOTO 1500
      ELSE IF (AZ_REC .LT. ZX_FAZIN) THEN
        GOTO 1800
      ELSE IF (AZ_REC .LT. ZX_FAZST) THEN
        GOTO 1400
      ELSE IF (AZ_REC .LE. ZX_LAZST) THEN
        GOTO 1700
      ENDIF
      GOTO 9999
C
C Display on screen the first page with the given name
C
 2000 CONTINUE
      BLOCK  = 0
      MODE1 = RWXMODE1
      K = MODE1
      RWXMODE1 = 0
      IF (RZX_NAME(1:K) .LE. LOCCTOPX(1,1)(1:K)) BLOCK = LOCITOPX(1)
      DO I = 1,LOCITOPE
        IF (BLOCK .EQ. 0) THEN
          IF (RZX_NAME(1:K) .LE. LOCCTOPX(2,I)(1:K)      ) THEN
            BLOCK = LOCITOPX(I)
          ELSE IF (I .EQ. LOCITOPE ) THEN
            BLOCK = LOCITOPX(I)
          ELSE IF (LOCITOPX(I) .EQ. 0) THEN
            BLOCK = LOCITOPX(I-1)
          ENDIF
        ENDIF
      ENDDO
C
CIBM+
      SECTOR = 2*(BLOCK - 1)
      IF ( SECTOR .NE. OLDIOSEC ) THEN
        OLDIOSEC    = SECTOR
        RXZXCODE(1) = 0
        RXZXFLG(1)   = .FALSE.
        FR_STATUS (1) = 0
        REC_NUM = BLOCK-1
        REC_SIZE = 1536
        BYTE_CNT = 1536
        STRT_POS = 0
        CALL CAE_IO_READ(FR_STATUS(1),RXZXCODE(1),%VAL(RXZXDCB(1))
     &       ,%VAL(REC_SIZE),IOBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
C
      ENDIF
CIBM-
CSEL++        ------- SEL Code -------
CSEL       SECTOR = 2*(BLOCK - 1)
CSEL       IF ( SECTOR .NE. OLDIOSEC ) THEN
CSEL         OLDIOSEC    = SECTOR
CSEL         BYTE_CNT    = 1536
CSEL         RXZXCODE(1) = 0
CSEL         RXZXFLG(1)  = .FALSE.
CSEL         CALL S_READ(RZX_FDB(0,1),SSTATUS,,IOBUFI4,BYTE_CNT,SECTOR)
CSEL         IF (.NOT.SSTATUS) THEN
CSEL           RXZXCODE(1) = IAND(RZX_FDB(5,1),X'0000FFFF')
CSEL           RXZXFLG(1)  = .TRUE.
CSEL         ENDIF
CSEL       ENDIF
CSEL-         ------------------------
CVAX+
CVAX       BLOCKN   = BLOCK*3 - 2
CVAX       BYTE_CNT = 1536
CVAX       RXZXCODE(1) = 0
CVAX       RXZXFLG(1) = .FALSE.
CVAX       CALL NBLKIORW (%VAL(RXZXDCB),IOBUF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZXCODE(1),%VAL(IO$_READVBLK),%REF(RZXSPAD(1)),
CVAX      &               RXZXFLG(1),RZX_AST,)
CVAX-
      MODEC = 11
      GOTO 9999
C
 2100 CONTINUE
      BLOCK  = 0
      K = MODE1
      IF (RZX_NAME(1:K) .LE. IOBUFC64(1)(1:K) ) THEN
        BLOCK = IOBUFI4(16,1)
      ENDIF
      DO I = 1,24
        IF (BLOCK .EQ. 0) THEN
          IF (RZX_NAME(1:K) .LE. IOBUFC64(I)(32:31+K) ) THEN
            BLOCK = IOBUFI4(16,I)
          ELSE IF (I .EQ. 24) THEN
            BLOCK = IOBUFI4(16,I)
          ELSE IF (IOBUFI4(16,I) .EQ. 0) THEN
            BLOCK = IOBUFI4(16,I-1)
          ENDIF
        ENDIF
      ENDDO
      AZ_REC = BLOCK
      GOTO 1700
C
C Check for option selected
C
 2200 CONTINUE
      MODEC = 1
      IF (MODEO .EQ. 1) THEN
        MODEO = 2
      ELSE IF (MODEO .EQ. 2) THEN
        MODEO = 1
      ENDIF
C
      IF (MODEO .NE. PMODEO) SWAPMODEO = .TRUE.
      PMODEO = MODEO
C
 2300 IF (MODE .EQ. 2) THEN
        IF ( MODEO .EQ. 1) THEN
          MODEDISP(1) = IDEN
          MODEDISP(2) = T
          MODEH       = 0
        ELSE
          MODEDISP(1) = ICAO
          MODEDISP(2) = BLANK
          MODEH       = 0
        ENDIF
        IF (SWAPMODEO .OR. MODEO.NE.MODEDEF) THEN
          SWAPMODEO = .FALSE.
          DO I = 1,24
            TMP4 = RWXBUFC64(I)(ZX_PSIDE:ZX_PEIDE)
            RWXBUFC64(I)(ZX_PSIDE:ZX_PEIDE) =
     &                  RWXBUFC64(I)(ZX_PSICA:ZX_PEICA)
            RWXBUFC64(I)(ZX_PSICA:ZX_PEICA) = TMP4
          ENDDO
        ENDIF
      ENDIF
      MODEC = 14
C
C
C
C Compute line colouring
C
2400  CONTINUE
      I_CLRAV = .FALSE.
      DO I = 1,24
        RWXKILL(I) = .FALSE.
        IF (RVNKIL.NE.0 .AND. MODE.EQ.2) THEN
          DO J = 1,RVNKIL
            IF (RWXBUFI4(15,I).EQ.RVXKIL(J)) THEN
              RWXKILL(I) = .TRUE.
              I_CLRAV    = .TRUE.
            ENDIF
          ENDDO
        ELSE
          IF ( BUFMI4(I) .EQ. FAIL ) THEN
            BUFMI4(I) = BLANK
            CNTR(I)   = 0
          ENDIF
        ENDIF
        IF ( .NOT.C_COLOR ) THEN
          BUFMI4(I) = BLANK
        CNTR(I)   = 0
        ENDIF
      ENDDO
      IF (.NOT.C_COLOR) ACTNFLG = .FALSE.
      MODEC   = 1
      C_COLOR = .FALSE.
      GOTO 9999
C
C
 9999 CONTINUE
C
C Display the appropriate header associate with the page content.
C
      IF (MODE .EQ. 0) THEN
        IF (MODEH .NE. 0) THEN
          MODEH = 0
          DO I = 1,160
            RWXHDR4I(I) = BLANK    !initialize the header
          ENDDO                          !with space
        ENDIF
      ELSE IF (MODE .EQ. 1) THEN
        !index page
        IF (MODEH .NE. 1) THEN
          MODEH = 1
          III   = 3
          RWXHDRC64(III)(1:) = HDRINIT(1,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(2,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(3,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(4,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = ' '
        ENDIF
      ELSE IF (MODE .EQ. 2) THEN
        !page
        IF (MODEH .NE. 2) THEN
          MODEH = 2
          III = 3
          RWXHDRC64(III)(1:) = HDRINIT(1,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(2,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(3,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(4,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(5,HDR)
          RWXHDRI4(8,III)    = MODEDISP(1)
          RWXHDRI4(9,III)    = MODEDISP(2)
        ENDIF
      ELSE IF (MODE .EQ. 3) THEN
        !top index
        IF (MODEH .NE. 3) THEN
          MODEH = 3
          III = 3
          RWXHDRC64(III)(1:) = HDRINIT(1,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(2,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(3,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = HDRINIT(4,HDR)
          III = III + 1
          RWXHDRC64(III)(1:) = ' '
        ENDIF
      ENDIF
C
C swap the I/F page if necessary to speed up refresh cycle
C
      IF (SWAP_PAGE) THEN
        SWAP_PAGE = .FALSE.
        IF (MODE.EQ.3) THEN
          WRITE(RWXHDRC64(9),99000,IOSTAT=IOS)(AZ_REC-ZX_FAZQI+1),
     &           MAX_TOP
        ELSE IF (MODE.EQ.1) THEN
          WRITE(RWXHDRC64(9),99000,IOSTAT=IOS)(AZ_REC-ZX_FAZIN+1),
     &           MAX_IND
        ELSE IF (MODE.EQ.2) THEN
          WRITE(RWXHDRC64(9),99000,IOSTAT=IOS)(AZ_REC-ZX_FAZST+1),
     &           MAX_STN
        ENDIF
        IF (IOS.NE.0) THEN
          MESSAGE = '%RX1, Error converting page number'
          CALL TO_CONSOLE(MESSAGE)
        ENDIF
        TCMRACH(1) = TCMRAOK(1)
        TCMRACH(2) = TCMRAOK(2)
      ENDIF
C
C
10000 CONTINUE
C
CIBM+
      IF ((FR_STATUS (2) .EQ. 1) .AND. (RXZXCODE (2) .EQ. 1)) THEN
        RXZXFLG (2) = .TRUE.
      ENDIF
CIBM-
      IF (  RXZXCODE(2).EQ.1 .AND. RXZXFLG(2)
     &    .AND. RXZXFVLD .AND. ZX_FICIA .NE.0) THEN
         GOTO (10100,10200,10300,10400,10500,10600,10700) IC_SEL
       ENDIF
      GOTO 30000
C
C
10100 CONTINUE
C
C Read quick index for ICAO
C Save the ICAO QUICK INDEX information into the local buffer
C the format will be as follow:
C
C       +------------------------------+
C       |  first ICAO of first  record |
C       |  first ICAO of second record |
C       |  first ICAO of third  record |
C       |    "    "   "    "       "
C       |  first ICAO of last   record |
C       |  last  ICAO of last   record |
C       +------------------------------+
C
      TICQI       = ZX_LICQI - ZX_FICQI + 1
      IF ( TICQI.GT.M_TICQI .OR. TICQI.EQ.0 ) THEN
        MESSAGE = ' '
        MESSAGE = ' %NAV, Fatal error in RX1 module '
        CALL TO_CONSOLE(MESSAGE)
        RXZXCODE(2) = -998
        GOTO 30000
      ELSE
        BYTE_CNT = TICQI*1536
      ENDIF
C
      IC_REC      = ZX_FICQI
CIBM+
      RXZXCODE(2) = 0
      RXZXFLG(2)   = .FALSE.
      FR_STATUS (2) = 0
      REC_NUM = IC_REC-1
      REC_SIZE = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS(2),RXZXCODE(2),%VAL(RXZXDCB(2))
     &       ,%VAL(REC_SIZE),LOCIICAO,REC_NUM,BYTE_CNT,STRT_POS)
C
      IC_SEL = IC_SEL + 1
CIBM-
CSEL++        ------- SEL Code -------
CSEL       SECTOR      = 2*(IC_REC - 1)
CSEL       CALL S_READ(RZX_FDB(0,2),SSTATUS,,LOCIICAO,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZXCODE(2) = IAND(RZX_FDB(5,2),X'0000FFFF')
CSEL         RXZXFLG(2)  = .TRUE.
CSEL       ENDIF
CSEL       IC_SEL = IC_SEL + 1
CSEL-         ------------------------
CVAX+
CVAX       BLOCKN   = IC_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZXDCB),ICAOBUF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZXCODE(2),%VAL(IO$_READVBLK),%REF(RZXSPAD(2)),
CVAX      &               RXZXFLG(2),RZX_AST,)
CVAX       IC_SEL = IC_SEL + 1
CVAX-
      GOTO 30000
C
10200 CONTINUE
C
C     Find out the last location of the last ICAO
C
      LOOK = .TRUE.
      J = 1
      DO WHILE ( LOOK )
        IF ( LOCIICAO(J) .EQ. BLANK ) THEN
          LOOK = .FALSE.
          J = J - 1
        ELSE IF ( J .GE. TICQI*384 ) THEN
          LOOK = .FALSE.
        ELSE
          J = J + 1
        ENDIF
      ENDDO
      LOCICAOE = J
      IF ( LOCICAOE .LE. 0 ) THEN
        MESSAGE = ' '
        MESSAGE = ' %NAV, Fatal error in RX1 module , bad RZX files'
        CALL TO_CONSOLE(MESSAGE)
        RXZXCODE(2) = -996
        GOTO 30000
      ENDIF
C
      IC_SEL = IC_SEL + 1
      GOTO 30000
C
10300 CONTINUE
C
C Read quick index for IATA
C Save the IATA QUICK INDEX information into the local buffer
C the format will be as follow:
C
C       +------------------------------+
C       |  first IATA of first  record |
C       |  first IATA of second record |
C       |  first IATA of third  record |
C       |    "    "   "    "       "
C       |  first IATA of last   record |
C       |  last  IATA of last   record |
C       +------------------------------+
C
      TIAQI       = ZX_LIAQI - ZX_FIAQI + 1
      IF ( TIAQI.GT.M_TIAQI .OR. TIAQI.EQ.0 ) THEN
        MESSAGE = ' '
        MESSAGE = ' %NAV, Fatal error in RX1 module '
        CALL TO_CONSOLE(MESSAGE)
        RXZXCODE(2) = -997
        GOTO 30000
      ELSE
        BYTE_CNT = TIAQI*1536
      ENDIF
C
      IC_REC      = ZX_FIAQI
CIBM+
      RXZXCODE(2) = 0
      RXZXFLG(2)   = .FALSE.
      FR_STATUS (2) = 0
      REC_NUM = IC_REC-1
      REC_SIZE = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS(2),RXZXCODE(2),%VAL(RXZXDCB(2))
     &       ,%VAL(REC_SIZE),LOCIIATA,REC_NUM,BYTE_CNT,STRT_POS)
C
      IC_SEL = IC_SEL + 1
CIBM-
CSEL++        ------- SEL Code -------
CSEL       SECTOR      = 2*(IC_REC - 1)
CSEL       CALL S_READ(RZX_FDB(0,2),SSTATUS,,LOCIIATA,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZXCODE(2) = IAND(RZX_FDB(5,2),X'0000FFFF')
CSEL         RXZXFLG(2)  = .TRUE.
CSEL       ENDIF
CSEL       IC_SEL = IC_SEL + 1
CSEL-         ------------------------
CVAX+
CVAX       BLOCKN   = IC_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZXDCB),IATABUF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZXCODE(2),%VAL(IO$_READVBLK),%REF(RZXSPAD(2)),
CVAX      &               RXZXFLG(2),RZX_AST,)
CVAX       IC_SEL = IC_SEL + 1
CVAX-
      GOTO 30000
C
10400 CONTINUE
C
C     Find out the last location of the last ICAO
C
      LOOK = .TRUE.
      J = 1
      DO WHILE ( LOOK )
        IF ( LOCIIATA(J) .EQ. BLANK ) THEN
          LOOK = .FALSE.
          J = J - 1
        ELSE IF ( J .GE. TIAQI*384 ) THEN
          LOOK = .FALSE.
        ELSE
          J = J + 1
        ENDIF
      ENDDO
      LOCIATAE = J
C
      IC_SEL = IC_SEL + 1
      IC_MODE = 1    !Start by looking for ICAO ident
      GOTO 30000
C
10500 CONTINUE
C
C  Check what parameter is supply (IATA or ICAO)
C
C  RLMODE code definitions:
C
C           0       ==> idle mode (waiting for a command)
C           1 - 50  ==> error code
C                       1  = IATA/ICAO code not present on first pass
C                       2  =
C                       3  = IATA      code not present on second pass
C                       4  =
C                       5  =
C                       6  =
C
C           40 - 50 ==> internal error code
C                       40 =
C                       41 =
C                       42 =
C                       43 =
C                       44 =
C                       45 =
C
C           51      ==> lock for a command
C           60 - 70 ==> internal use
C           99      ==> computation complete succesfully
C           100     ==> request RWY/GATE record number
C           101     ==> request RWY/GATE record number and
C                       update CDB GATE/RWY buffer
C           102     ==> update CDB GATE/RWY buffer
C
C
C
      IF ( RLMODE .GE. 100 ) THEN
        IF ( IC_MODE.EQ.1 .OR. IC_MODE.EQ.2 ) THEN
          FOUND = .FALSE.
          IF ( IC_MODE .EQ. 2 ) THEN
C
C
C           Get the ICAO ident for the given IATA ident
C
C            1      4 5      8
C           +-----------------+
C       1   |  IATA  |  ICAO  |
C           |   "    |   "    |
C       192 |  IATA  |  ICAO  |
C           +-----------------+
C
C
            I     = 1
            FOUND = .FALSE.
            LOOK  = .TRUE.
            DO WHILE ( LOOK .AND. I.LE.192 )
              IF ( RLAIRFLD .EQ. ZXIATI4(1,I) ) THEN
                FOUND = .TRUE.
                LOOK  = .FALSE.
                RLAIRFLD = ZXIATI4(2,I)
              ELSE
                I = I + 1
              ENDIF
            ENDDO
          ENDIF
C
          IF ( IC_MODE.EQ.1 .OR. FOUND ) THEN
            !ICAO
            FOUND = .FALSE.
CIBM++
            IF ( RLAIRFLD.LT.LOCIICAO(1) .OR.
     &           RLAIRFLD.GT.LOCIICAO(LOCICAOE)) THEN
CIBM-         ------------------------
CSEL++        ------- SEL Code -------
CSEL             IF ( RLAIRFLD.LT.LOCIICAO(1) .OR.
CSEL      &           RLAIRFLD.GT.LOCIICAO(LOCICAOE)) THEN
CSEL-         ------------------------
CVAX+
CVAX             IF ( C_AIRFLD .LT. LOCCICAO(1) .OR.
CVAX      &           C_AIRFLD .GT. LOCCICAO(LOCICAOE)) THEN
CVAX-
            ELSE
              IF ( RLAIRFLD .EQ. LOCIICAO(LOCICAOE)) THEN
                I     = LOCICAOE - 1
                FOUND = .TRUE.
              ELSE
                I    = 1
                LOOK = .TRUE.
                DO WHILE ( LOOK .AND. I.LT.LOCICAOE )
CIBM++
                  IF ((RLAIRFLD .GE. LOCIICAO (I)    .AND.
     &                 RLAIRFLD .LT. LOCIICAO (I+1)) .OR.
     &                 RLAIRFLD .EQ. LOCIICAO (I))    THEN
CIBM-         ------------------------
CSEL++        ------- SEL Code -------
CSEL                   IF ((RLAIRFLD .GE. LOCIICAO (I)    .AND.
CSEL      &                 RLAIRFLD .LT. LOCIICAO (I+1)) .OR.
CSEL      &                 RLAIRFLD .EQ. LOCIICAO (I))    THEN
CSEL-         ------------------------
CVAX+
CVAX                   IF ((C_AIRFLD .GE. LOCCICAO(I)    .AND.
CVAX      &                 C_AIRFLD .LT. LOCCICAO(I+1)) .OR.
CVAX      &                 C_AIRFLD .EQ. LOCCICAO (I))    THEN
CVAX-
                    LOOK  = .FALSE.
                    FOUND = .TRUE.
                  ELSE
                    I = I + 1
                  ENDIF
                ENDDO
              ENDIF
            ENDIF
          ENDIF
C
          IC_REC = 0
          IF ( FOUND ) THEN
            IC_REC = ZX_FICIA + I - 1
CIBM+
            SECTOR = 2*(IC_REC - 1)
            IF ( SECTOR .NE. OLDICSEC ) THEN
              OLDICSEC    = SECTOR
              RXZXCODE(2) = 0
              RXZXFLG(2)   = .FALSE.
              FR_STATUS (2) = 0
              REC_NUM = IC_REC-1
              REC_SIZE = 1536
              BYTE_CNT = 1536
              STRT_POS = 0
              CALL CAE_IO_READ(FR_STATUS(2),RXZXCODE(2),%VAL(RXZXDCB(2))
     &       ,%VAL(REC_SIZE),ZXICAI4,REC_NUM,BYTE_CNT,STRT_POS)
C
            ENDIF
            IC_SEL = IC_SEL + 1
CIBM-
CSEL++        ------- SEL Code -------
CSEL             SECTOR = 2*(IC_REC - 1)
CSEL             IF ( SECTOR .NE. OLDICSEC ) THEN
CSEL               OLDICSEC    = SECTOR
CSEL               BYTE_CNT    = 1536
CSEL               RXZXCODE(2) = 0
CSEL               RXZXFLG(2)  = .FALSE.
CSEL               CALL S_READ(RZX_FDB(0,2),SSTATUS,,ZXICAI4,BYTE_CNT,SECTOR)
CSEL               IF (.NOT.SSTATUS) THEN
CSEL                 RXZXCODE(2) = IAND(RZX_FDB(5,2),X'0000FFFF')
CSEL                 RXZXFLG(2)  = .TRUE.
CSEL               ENDIF
CSEL             ENDIF
CSEL             IC_SEL  = IC_SEL + 1
CSEL-         ------------------------
CVAX+
CVAX             BLOCKN   = IC_REC*3 - 2
CVAX             IF ( BLOCKN .NE. OLDICBLK ) THEN
CVAX               OLDICBLK = BLOCKN
CVAX               BYTE_CNT = 1536
CVAX               RXZXCODE(2) = 0
CVAX               RXZXFLG(2) = .FALSE.
CVAX               CALL NBLKIORW (%VAL(RXZXDCB),ZXBUF,%VAL(BLOCKN),
CVAX      &                       %VAL(BYTE_CNT),RXZXCODE(2),
CVAX      &                       %VAL(IO$_READVBLK),%REF(RZXSPAD(2)),
CVAX      &                       RXZXFLG(2),RZX_AST,)
CVAX             ENDIF
CVAX             IC_SEL  = IC_SEL + 1
CVAX-
          ELSE IF ( IC_MODE .EQ. 1 ) THEN
            IC_SEL = IC_SEL + 1
          ELSE IF ( IC_MODE .EQ. 2 ) THEN
            IC_MODE  = 1
            RLMODE   = 1
            RLRECORD = -1
          ENDIF
        ELSE
          IC_MODE  = 1
          RLMODE   = 40
          RLRECORD = -1
        ENDIF
      ENDIF
      GOTO 20000
C
C
C
10600 CONTINUE
C
      IF ( IC_MODE.EQ.1 .OR. IC_MODE.EQ.2 ) THEN
C
C        1      4 5        9 10  12 13    16 17    20
C       +--------------------------------------------+
C       |        |  RUNWAY  |      |        |        |
C       |  ICAO  |  GATE    | IATA | RECORD |  INDEX |
C       |        |  MARKER  |      |        |        |
C       +--------------------------------------------+
C
        I     = 1
        LOOK  = IC_REC .NE. 0
        FOUND = .FALSE.
        DO WHILE ( LOOK .AND. I.LE.76 )
          IF ( RLAIRFLD .EQ. ZXICAI4(1,I) ) THEN
            FOUND = .TRUE.
            LOOK  = .FALSE.
          ELSE
            I = I + 1
          ENDIF
        ENDDO
        IC_OFF = 0
C
        IF ( FOUND ) THEN
          IC_OFF = I
          IC_SEL = IC_SEL + 1
        ELSE IF ( IC_MODE.EQ.1 .AND. LOCIATAE.GT.0 ) THEN
          IC_MODE = 2
          FOUND   = .FALSE.
CIBM++
          IF ( RLAIRFLD.LT.LOCIIATA(1) .OR.
     &         RLAIRFLD.GT.LOCIIATA(LOCIATAE)) THEN
CIBM-
CSEL-         ------------------------
CSEL++        ------- SEL Code -------
CSEL           IF ( RLAIRFLD.LT.LOCIIATA(1) .OR.
CSEL      &         RLAIRFLD.GT.LOCIIATA(LOCIATAE)) THEN
CSEL-         ------------------------
CVAX+
CVAX           IF ( C_AIRFLD .LT. LOCCIATA(1) .OR.
CVAX      &         C_AIRFLD .GT. LOCCIATA(LOCIATAE) ) THEN
CVAX-
          ELSE
            IF ( RLAIRFLD .EQ. LOCIIATA(LOCIATAE)) THEN
              I     = LOCIATAE - 1
              FOUND = .TRUE.
            ELSE
              I    = 1
              LOOK = .TRUE.
              DO WHILE ( LOOK .AND. I.LT.LOCIATAE )
CIBM++
                IF ( RLAIRFLD.GE.LOCIIATA(I) .AND.
     &               RLAIRFLD.LT.LOCIIATA(I+1)    ) THEN
CIBM-         ------------------------
CSEL++        ------- SEL Code -------
CSEL                 IF ( RLAIRFLD.GE.LOCIIATA(I) .AND.
CSEL      &               RLAIRFLD.LT.LOCIIATA(I+1)    ) THEN
CSEL-         ------------------------
CVAX+
CVAX                IF ( C_AIRFLD .GE. LOCCIATA(I) .AND.
CVAX      &              C_AIRFLD .LT. LOCCIATA(I+1)    ) THEN
CVAX-
                  LOOK  = .FALSE.
                  FOUND = .TRUE.
                ELSE
                  I = I + 1
                ENDIF
              ENDDO
            ENDIF
          ENDIF
          IF ( FOUND ) THEN
            IA_REC = ZX_FIAIN + I - 1
CIBM+
            SECTOR = 2*(IA_REC - 1)
            IF ( SECTOR .NE. OLDIOSEC ) THEN
              OLDIOSEC    = SECTOR
              RXZXCODE(2) = 0
              RXZXFLG(2)   = .FALSE.
              FR_STATUS (2) = 0
              REC_NUM = IA_REC-1
              REC_SIZE = 1536
              BYTE_CNT = 1536
              STRT_POS = 0
              CALL CAE_IO_READ(FR_STATUS(2),RXZXCODE(2),%VAL(RXZXDCB(2))
     &       ,%VAL(REC_SIZE),ZXIATI4,REC_NUM,BYTE_CNT,STRT_POS)
C
            ENDIF
            IC_SEL = IC_SEL - 1
CIBM-
CSEL++        ------- SEL Code -------
CSEL             SECTOR = 2*(IA_REC - 1)
CSEL             IF ( SECTOR .NE. OLDIOSEC ) THEN
CSEL               OLDIOSEC    = SECTOR
CSEL               BYTE_CNT    = 1536
CSEL               RXZXCODE(2) = 0
CSEL               RXZXFLG(2)  = .FALSE.
CSEL               CALL S_READ(RZX_FDB(0,2),SSTATUS,,ZXIATI4,BYTE_CNT,SECTOR)
CSEL               IF (.NOT.SSTATUS) THEN
CSEL                 RXZXCODE(2) = IAND(RZX_FDB(5,2),X'0000FFFF')
CSEL                 RXZXFLG(2)  = .TRUE.
CSEL               ENDIF
CSEL             ENDIF
CVAX             IC_SEL  = IC_SEL - 1
CSEL-         ------------------------
CVAX+
CVAX             BLOCKN   = IA_REC*3 - 2
CVAX             IF ( BLOCKN .NE. OLDIOBLK ) THEN
CVAX               OLDIOBLK = BLOCKN
CVAX               BYTE_CNT = 1536
CVAX               RXZXCODE(2) = 0
CVAX               RXZXFLG(2) = .FALSE.
CVAX               CALL NBLKIORW (%VAL(RXZXDCB),IOBUF,%VAL(BLOCKN),
CVAX      &                       %VAL(BYTE_CNT),RXZXCODE(2),
CVAX      &                       %VAL(IO$_READVBLK),%REF(RZXSPAD(2)),
CVAX      &                       RXZXFLG(2),RZX_AST,)
CVAX             ENDIF
CVAX             IC_SEL  = IC_SEL - 1
CVAX-
          ELSE
            IC_MODE  = 1
            RLMODE   = 2
            RLRECORD = -1
            IC_SEL = IC_SEL - 1
          ENDIF
        ELSE IF ( IC_MODE .EQ. 2 ) THEN
          IC_MODE  = 1
          RLMODE   = 2
          RLRECORD = -1
          IC_SEL = IC_SEL - 1
        ENDIF
      ELSE
        RLRECORD = -1
        RLMODE   = 41
        IC_MODE  = 1
        IC_SEL   = IC_SEL - 1
      ENDIF
      GOTO 20000
C
10700 CONTINUE
C
C      1      4 5        9 10  12 13    16 17    20
C     +--------------------------------------------+
C     |        |  RUNWAY  |      |        |        |
C     |  ICAO  |  GATE    | IATA | RECORD |  INDEX |
C     |        |  MARKER  |      |        |        |
C     +--------------------------------------------+
C
C Ready to access ICAO/IATA data
C
      IF ( RLMODE.GE.100 .AND. IC_OFF.NE.0 ) THEN
        I       = IC_OFF
        IC_RWYS = 0
        IC_GATE = 0
        IC_MKRS = 0
        ICL_UPD = RLMODE.EQ.101 .OR. RLMODE.EQ.102
C
        IF ( ICL_UPD ) THEN
          TRXBGAT  = 0
          TRXBRWYS = 0
          M_TRXBGAT  = TRXBGAT
          M_TRXBRWYS = TRXBRWYS
        ENDIF
C
        LOOK = .NOT. (RLRWYSET(1).EQ.RW   .AND. .NOT.ICL_UPD)
C
        IF (LOOK) THEN
          MATCH = 0
        ELSE
          MATCH = I
        END IF
C
        DO WHILE (LOOK)
          RWY_TYP = ZXICAI2(3,I) .EQ. RW2
          MKR_TYP = ZXICAI2(3,I) .EQ. IM2 .OR.
     &              ZXICAI2(3,I) .EQ. MM2 .OR.
     &              ZXICAI2(3,I) .EQ. OM2
          COM_TYP =
     &              ( ZXICAC20(I)(5:7) .EQ. 'AAS' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'ACC' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'ACP' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'APP' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'ARR' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'ATA' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'ATD' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'ATI' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'AWO' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'CLD' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'CPT' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'CTL' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'DEP' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'DIR' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'EMR' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'FIS' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'FLT' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'FSS' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'GCA' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'GND' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'GTE' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'HEL' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'HF ' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'INF' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'MUL' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'OPS' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'PAR' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'RAD' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'RDO' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'RDR' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'RMP' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'RSA' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'TCA' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'TMA' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'TRS' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'TWR' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'UCA' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'UHF' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'UNI' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'VOL' ) .OR.
     &              ( ZXICAC20(I)(5:7) .EQ. 'ACR' )
          GAT_TYP = .NOT. ( COM_TYP .OR. RWY_TYP .OR. MKR_TYP )
C          GAT_TYP = .NOT.( RWY_TYP .OR. MKR_TYP )
C
          IF (IC_GATE .EQ. 0) THEN
            IF ( GAT_TYP ) IC_GATE = I
          ENDIF
          IF (IC_RWYS .EQ. 0) THEN
            IF ( RWY_TYP ) IC_RWYS = I
          ENDIF
          IF (IC_MKRS .EQ. 0) THEN
            IF ( MKR_TYP ) IC_MKRS = I
          ENDIF
C
          VALID = RLAIRFLD .EQ. ZXICAI4(1,I)
C
          IF (ZXICAI4(2,I) .EQ. RLRWYSET(1)) THEN
            IF ( ZXICAI1(9,I) .EQ. I1RWYSET(5) ) THEN
              MATCH = I
              IF ( .NOT. ICL_UPD ) LOOK = .FALSE.
            ENDIF
          ENDIF
C
          IF ( ICL_UPD .AND. VALID ) THEN
            IF ( GAT_TYP ) THEN
              M_TRXBGAT = M_TRXBGAT + 1
              IF ( TRXBGAT .LT. RXAGAT ) THEN
                TRXBGAT = TRXBGAT + 1
                DO J = 1,5
CIBM+
                  TRXGATNAM(J,TRXBGAT) = ZXICAI1(J+4,I)
CIBM-
CSEL+
CSEL                  TRXGATNAM(J,TRXBGAT) = ZXICAL1(J+4,I)
CSEL-
                ENDDO
                TRXGATIDX(TRXBGAT) = ZXICAI4(5,I)
                TRXGATREC(TRXBGAT) = ZXICAI4(4,I)
              ENDIF
            ELSE IF ( RWY_TYP ) THEN
              M_TRXBRWYS = M_TRXBRWYS + 1
              IF ( TRXBRWYS .LT. RXARWYS ) THEN
                TRXBRWYS = TRXBRWYS + 1
                DO J = 1,3
C                  TL1RWYNAM(J,TRXBRWYS) = ZXICAL1(J+6,I)
CIBM+
                  TL1RWYNAM(J,TRXBRWYS) = ZXICAL1(J+6,I)
CIBM-
                ENDDO
                TL1RWYNAM(4,TRXBRWYS) = LSPACE
                TRXRWYIDX(TRXBRWYS) = ZXICAI4(5,I)
                TRXRWYREC(TRXBRWYS) = ZXICAI4(4,I)
              ENDIF
            ENDIF
          ENDIF
C
          IF ( (LOOK .OR. MATCH.EQ.0) .AND.
     &         ( I.GE.76 .OR. .NOT.VALID ) ) THEN
            IF ( MATCH .EQ. 0 ) THEN
              IF ( I2RWYSET(1) .EQ. RW2 ) THEN
                MATCH = IC_RWYS
              ELSE
                MATCH = IC_GATE
              ENDIF
            ENDIF
            IF ( MATCH .EQ. 0 ) MATCH = IC_OFF
            LOOK = .FALSE.
          ELSE
            I = I + 1
          ENDIF
        ENDDO
C
        IF ( RLMODE.EQ.100 .OR. RLMODE.EQ.101 ) THEN
          RLRWYSET(1) = ZXICAI4(2,MATCH)
CIBM++
          RLRWYSET(2) = IAND(ZXICAI4(3,MATCH),X'FF000000') + X'00202020'
CIBM-         ------------------------
CSEL++        ------- SEL Code -------
CSEL           RLRWYSET(2) = IAND(ZXICAI4(3,MATCH),X'FF000000') + X'00202020'
CSEL-         ------------------------
CVAX+
CVAX           RLRWYSET(2) = IAND(ZXICAI4(3,MATCH),'000000FF'X) + '20202000'X
CVAX-
          RLRECORD    = ZXICAI4(4,MATCH)
          IF (IC_MODE .EQ. 1) THEN
            !input is ICAO
            RLAIRFLD = ZXICAI4(1,MATCH)
            RLAIRICA = RLAIRFLD
CIBM++
            RLAIRIAT = IAND (ZXICAI4(3,MATCH),X'00FFFFFF') * 256
CIBM-         ------------------------
CSEL++        ------- SEL Code -------
CSEL            RLAIRIAT = IAND (ZXICAI4(3,MATCH),X'00FFFFFF') * 256
CSEL-         ------------------------
CVAX+
CVAX             RLAIRIAT = IAND (ZXICAI4(3,MATCH),'FFFFFF00'X) / 256
CVAX-
          ELSE
            !input is IATA
CIBM++
            RLAIRFLD = IAND (ZXICAI4(3,MATCH),X'00FFFFFF') * 256
CIBM-         ------------------------
CSEL++        ------- SEL Code -------
CSEL             RLAIRFLD = IAND (ZXICAI4(3,MATCH),X'00FFFFFF') * 256
CSEL-         ------------------------
CVAX+
CVAX             RLAIRFLD = IAND (ZXICAI4(3,MATCH),'FFFFFF00'X) / 256
CVAX-
            RLAIRICA = ZXICAI4(1,MATCH)
            RLAIRIAT = RLAIRFLD
          ENDIF
        ENDIF
C
        IF ( ICL_UPD )  THEN
          RXBGAT  = TRXBGAT
          RXBRWYS = TRXBRWYS
          RXARPT  = BLANK
          IF ( RXBGAT .GT. 0 ) THEN
            DO I = 1,RXBGAT
              DO J = 1,5
                RXGATNAM(J,I) = TRXGATNAM(J,I)
              ENDDO
              RXGATIDX(I) = TRXGATIDX(I)
              RXGATREC(I) = TRXGATREC(I)
            ENDDO
          ENDIF
C
C Empty the section of the buffer which is not used
C
          IF ( RXBGAT .LT. TRXAGAT ) THEN
            DO I = RXBGAT+1,TRXAGAT
              DO J = 1,5
C                RXGATNAM(J,I) = LSPACE
CIBM+
                RXGATNAM(J,I) = SPACE
CIBM-
             ENDDO
              RXGATIDX(I) = 0
              RXGATREC(I) = 0
            ENDDO
          ENDIF
C
C PY+
C          IF ( RXBRWYS .GT. 0 ) THEN
C            DO I = 1,RXBRWYS
C              I4RWYNAM(I) = TRXRWYNAM(I)
C              RXRWYIDX(I) = TRXRWYIDX(I)
C              RXRWYREC(I) = TRXRWYREC(I)
C              RXRWYAVL(I) = .TRUE.
C            ENDDO
C          ENDIF
C
C Empty the section of the buffer which is not used
C
C          IF ( RXBRWYS .LT. TRXARWYS ) THEN
C            DO I = RXBRWYS+1,TRXARWYS
C              I4RWYNAM(I) = BLANK
C              RXRWYIDX(I) = 0
C              RXRWYREC(I) = 0
C              RXRWYAVL(I) = .FALSE.
C            ENDDO
C          ENDIF
C
C          RXARPT  = ZXICAI4(1,MATCH)
C
C
          IF ( RXBRWYS .LE. 0 ) THEN
            Do I = 1, TRXARWYS
              I4RWYNAM (I) = '20202020'X
              RXRWYIDX (I) = 0
              RXRWYREC (I) = 0
              RXRWYAVL (I) = .FALSE.
            Enddo
          ELSE
            Do I = 1, TRXARWYS
              MATCHED (I) = .FALSE.
              OPP_RWY (I) = 0
              RWY_PTR (I) = I
            Enddo
C
            NOTSORTED = .TRUE.
C
C -- Sort the runways as follows:
C    alphabetically by name in the order L, R, C, " "
C    e.g.: 07L 25L 07R 25R 07C 25C 18 36
C
C            Do While (NOTSORTED)
C              NOTSORTED = .FALSE.
C              Do J = 1, RXBRWYS - 1
CSELVAX+
CSELVAX                If (TI1RWYNAM (3,RWY_PTR (J)) .EQ. 'R') Then
CSELVAX                  RWY1_LETTER (1) = '4C'X - '45'X
CSELVAX                Else
CSELVAX                  RWY1_LETTER (1) = '4C'X - TI1RWYNAM (3, RWY_PTR (J))
CSELVAX-
CIBM+
C                If (TI1RWYNAM (3,RWY_PTR (J)) .EQ. DUMXR) Then
C                  RWY1_LETTER (1) = DUMXL - DUMX45
C                Else
C                  RWY1_LETTER (1) = DUMXL - TI1RWYNAM (3,RWY_PTR (J))
CIBM-
C                Endif
C                RWY1_LETTER (2) = TI1RWYNAM (1,RWY_PTR (J))
C                RWY1_LETTER (3) = TI1RWYNAM (2,RWY_PTR (J))
C                RWY1_LETTER (4) = TI1RWYNAM (4,RWY_PTR (J))
C
CSELVAX+
CSELVAX                If (TI1RWYNAM (3,RWY_PTR (J + 1)) .EQ. 'R') Then
CSELVAX                  RWY2_LETTER (1) = '4C'X - '45'X
CSELVAX                Else
CSELVAX                  RWY2_LETTER (1) = '4C'X
CSELVAX-
CIBM+
C                If (TI1RWYNAM (3,RWY_PTR (J + 1)) .EQ. DUMXR) Then
C                  RWY2_LETTER (1) = DUMXL - DUMX45
C                Else
C                  RWY2_LETTER (1) = DUMXL
CIBM-
C     &                                 - TI1RWYNAM (3,RWY_PTR (J + 1))
C                Endif
C                RWY2_LETTER (2) = TI1RWYNAM (1,RWY_PTR (J + 1))
C                RWY2_LETTER (3) = TI1RWYNAM (2,RWY_PTR (J + 1))
C                RWY2_LETTER (4) = TI1RWYNAM (4,RWY_PTR (J + 1))
CVAX+
CVAX                If (RWY1_C4 .GT. RWY2_C4) Then
CVAX-
CIBMSEL+
C                If (RWY1_I4 .GT. RWY2_I4) Then
CIBMSEL-
C                  NOTSORTED = .TRUE.
C                  II = RWY_PTR (J)
C                  RWY_PTR (J) = RWY_PTR (J + 1)
C                  RWY_PTR (J + 1) = II
C                Endif
C              Enddo
C            Enddo
C
C -- Sort the runways in pairs
C
            I = 1
            K = 1
            Do While (I .LE. RXBRWYS)
              III = RWY_PTR (I)
              If (.NOT. MATCHED (III)) Then
                OPP_RWY (K) = III
C
C -- Determine what the name of the opposite runway is
C
CSELVAX+
CSELVAX                II = (TI1RWYNAM (1,III) - '30'X) * 10
CSELVAX     &                          + (TI1RWYNAM (2,III) - '30'X)
CSELVAX-
CIBM+
                II = (TI1RWYNAM (1,III) - DUMX30) * 10
     &                          + (TI1RWYNAM (2,III) - DUMX30)
CIBM-
                II = II + 18
                If (II .GT. 36) II = II - 36
                RWYNAM = '20202020'X
CSELVAX+
CSELVAX                I1RWYNAM (1) = (II / 10) + '30'X
CSELVAX                I1RWYNAM (2) = MOD (II, 10) + '30'X
CSELVAX                If (TI1RWYNAM (3,III) .EQ. 'L') Then
CSELVAX                  I1RWYNAM (3) = 'R'   !L -> R
CSELVAX                Elseif (TI1RWYNAM (3,III) .EQ. 'R') Then
CSELVAX                  I1RWYNAM (3) = 'L'   !R -> L
CSELVAX-
C
CIBM+
                I1RWYNAM (1) = (II / 10) + DUMX30
                I1RWYNAM (2) = MOD (II, 10) + DUMX30
                If (TI1RWYNAM (3,III) .EQ. DUMXL) Then
                  I1RWYNAM (3) = DUMXR   !L -> R
                Elseif (TI1RWYNAM (3,III) .EQ. DUMXR) Then
                  I1RWYNAM (3) = DUMXL   !R -> L
CIBM-
                Else
                  I1RWYNAM (3) = TI1RWYNAM (3,III)  !Others remain the same
                Endif
                II = 1
                Do While (II .LE. RXBRWYS)
                  If (RWYNAM .EQ. TRXRWYNAM (RWY_PTR (II))) Then
                    MATCHED (RWY_PTR (II)) = .TRUE.
                    OPP_RWY (K + 1) = RWY_PTR (II)
                    II = RXBRWYS + 1
                  Endif
                  II = II + 1
                Enddo
C
C -- If the opposite runway is not in the data, then mark then
C    runway pointer
C
                If (II .NE. RXBRWYS + 2) Then
                  OPP_RWY (K + 1) = -1   !Runway not found
                Endif
                K = K + 2
              Endif
              I = I + 1
            Enddo
C
            DO I = 1, TRXARWYS
              If (OPP_RWY (I) .LE. 0) Then
                I4RWYNAM (I) = '20202020'X
                RXRWYIDX (I) = 0
                RXRWYREC (I) = 0
                RXRWYAVL (I) = .FALSE.
              Else
                I4RWYNAM (I) = TRXRWYNAM (OPP_RWY (I))
                RXRWYIDX (I) = TRXRWYIDX (OPP_RWY (I))
                RXRWYREC (I) = TRXRWYREC (OPP_RWY (I))
                RXRWYAVL (I) = .TRUE.
              Endif
            ENDDO
          ENDIF
C
          RXARPT  = ZXICAI4(1,MATCH)
C PY-
C
C Check for any CDB buffer too small. If to small, send
C a message to the console.
C
          IF ( M_TRXBGAT .GT. RXAGAT ) THEN
            WRITE(MESSAGE,901)'RXBGAT  (GATE)',M_TRXBGAT,RXAGAT
            CALL TO_CONSOLE(MESSAGE)
          ENDIF
C
          IF ( M_TRXBRWYS .GT. RXARWYS ) THEN
            WRITE(MESSAGE,901)'RXBRWYS (RWY) ',M_TRXBRWYS,RXARWYS
            CALL TO_CONSOLE(MESSAGE)
          ENDIF
        ENDIF
        RLMODE = 99
      ELSE
        RLMODE   = 40
        RLRECORD = -1
      ENDIF
C
      IC_MODE = 1
      IC_SEL  = IC_SEL - 2
      GOTO 20000
C
C
20000 CONTINUE
CIBM+
      IF ((FR_STATUS (3) .EQ. 1) .AND. (RXZXCODE (3) .EQ. 1)) THEN
        RXZXFLG (3) = .TRUE.
      ENDIF
CIBM-
       If (RXZXCODE (3) .EQ. 1 .AND. RXZXFLG (3)
     &                 .AND. RXZXFVLD .AND. ZX_FIDST .NE. 0
     &                 .AND. RXZINO .GT. 0) Then
C
        Goto
     &(
     &       20100          !Read ident top index
     &      ,20200          !Read new ident & request index
     &      ,20300          !Get index data record
     &      ,20400          !Request data record
     &      ,20500          !Store data in internal arrays
     &      ,20600          !Sort data
     &      ,20700          !Output to dislay buffer
     &)     ,ID_SEL
        Goto 30000          !Should never be here
      Else
        Goto 30000
      Endif
C
20100 Continue
      TIDQI = ZX_LIDQI - ZX_FIDQI + 1
      If (TIDQI .GT. M_TIDQI) Then
        MESSAGE = ' '
        Call TO_CONSOLE (MESSAGE)
        MESSAGE = ' %NAV, Fatal error in RX1 module, ident section'
        Call TO_CONSOLE (MESSAGE)
        MESSAGE = ' '
        Call TO_CONSOLE (MESSAGE)
        RXZXCODE (3) = -998
        GOTO 30000
      Else
        If (M_IDPG .LT. RXZINO) Then
          MESSAGE = ' '
          Call TO_CONSOLE (MESSAGE)
          MESSAGE =
     &' %NAV, Partial error in RX1 module, number of ident pages'
     &//' processed reduced'
          Call TO_CONSOLE (MESSAGE)
          Write (MESSAGE,'(A,I6,A,I6,A)',IOSTAT=IOS)
     &' from ',RXZINO,' to ',M_IDPG,'. Update the parameter M_IDPG.'
          Call TO_CONSOLE (MESSAGE)
          MESSAGE = ' '
          Call TO_CONSOLE (MESSAGE)
          RXZINO = M_IDPG
        Endif
        BYTE_CNT = TIDQI * 1536
      Endif
C
      ID_REC       = ZX_FIDQI
CIBM+
      RXZXCODE(3) = 0
      RXZXFLG(3)   = .FALSE.
      FR_STATUS (3) = 0
      REC_NUM = ID_REC-1
      REC_SIZE = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS(3),RXZXCODE(3),%VAL(RXZXDCB(3))
     &       ,%VAL(REC_SIZE),I4DETOPX,REC_NUM,BYTE_CNT,STRT_POS)
C
      ID_SEL = ID_SEL + 1
CIBM-
CSEL++        ------- SEL Code -------
CSEL       SECTOR = 2 * (ID_REC - 1)
CSEL       Call S_READ (RZX_FDB (0,3)
CSEL      &            ,SSTATUS
CSEL      &           ,,I4DETOPX
CSEL      &            ,BYTE_CNT
CSEL      &            ,SECTOR)
CSEL       If (.NOT. SSTATUS) Then
CSEL         RXZXCODE (3) = IAND (RZX_FDB(5,3), X'0000FFFF')
CSEL         RXZXFLG  (3) = .TRUE.
CSEL       Endif
CSEL       ID_SEL = ID_SEL + 1
CSEL-         ------------------------
CVAX+
CVAX       SECTOR = ID_REC * 3 - 2
CVAX       Call NBLKIORW
CVAX      &            (%VAL (RXZXDCB)
CVAX      &            ,%REF (IIDETOPX)
CVAX      &            ,%VAL (SECTOR)
CVAX      &            ,%VAL (BYTE_CNT)
CVAX      &            ,RXZXCODE (3)
CVAX      &            ,%VAL (IO$_READVBLK)
CVAX      &            ,%REF (RZXSPAD (3))
CVAX      &            ,RXZXFLG (3)
CVAX      &            ,RZX_AST,)
CVAX       ID_SEL = ID_SEL + 1
CVAX-
      Goto 30000
C
20200 Continue
      RXZIND = RXZIND - 1
      If (RXZIND .LE. 0) RXZIND = RXZINO
C
C -- Selection for a line
C
      Do I = 1, MAX_LIN
        If (RXZILINE (I,RXZIND)) Then
          RXZILINE (I,RXZIND) = .FALSE.
          If (I .LE. NO_LINE (RXZIND)) Then
            RXZIIDX (RXZIND)
     &                  = STN_IDX (I + LIN_PTR (RXZIND) - 1,RXZIND)
            RXZIREC (RXZIND)
     &                  = STN_REC (I + LIN_PTR (RXZIND) - 1,RXZIND)
            RXZIVAL (RXZIND) = .TRUE.
            Do J = 1, MAX_LIN
              ZIDISP1 (1,J,RXZIND) = SPACE
            Enddo
            ZIDISP1 (1,I,RXZIND) = STAR
          Endif
        Endif
      Enddo
C
      If (RXZIRSEL (RXZIND)) Then          !Reselect requested ident
        INDEX = RXZIIDX (RXZIND)
        RXZIMORE (RXZIND) = .FALSE.
      Endif
C
      If (RXZIMORE (RXZIND)) Then          !Select MORE
        RXZIMORE (RXZIND) = .FALSE.
        LIN_PTR (RXZIND) = LIN_PTR (RXZIND) + MAX_LIN
        If (LIN_PTR (RXZIND) .GT. NO_STNS (RXZIND)) Then
          LIN_PTR (RXZIND) = 1
        Endif
        ID_SEL = 7                          !Display output
        Goto 30000                          !Exit
      Endif
C
      If (RXZIVAL (1)) Then
        If (RWDISREC .NE. RXZIREC (1)) Then
          If (RXACCESS (18,1) .EQ. 0) RXACCESS (18,1) = RXZIREC (1)
        Endif
      Endif
C
C -- Normal processing, wait for a new ident
C
      IDENT = INP_IDE (RXZIND)
C
      NEWIDENT = IDENT .NE. OLINP_IDE (RXZIND)
     &                              .OR. RXZIRSEL (RXZIND)
C
      If (NEWIDENT) Then
        OLINP_IDE (RXZIND) = IDENT
        NEWIDENT = .FALSE.
C
C -- Determine what index record has to be read
C
        FOUND = .FALSE.
        I = 1
        Do While (I .LE. 255 .AND..NOT. FOUND)
          If ((IDENT .GT. CIDETOPX (I)
     &               .AND. IDENT .LT. CIDETOPX (I + 1))
     &                     .OR. IDENT .EQ. CIDETOPX (I)) Then
            FOUND  = .TRUE.
            ID_REC = ZX_FIDIN + I - 1
          Endif
          I = I + 1
        Enddo
        If (.NOT. FOUND) Then               !Proceeding to next page
          NEWIDENT = .FALSE.
          ID_SEL = 7
          NO_STNS (RXZIND) = 0
        Else                                !Found requested ident
          BYTE_CNT = 1536
C
C  Issue the read
C
CIBM+
          RXZXCODE(3) = 0
          RXZXFLG(3)   = .FALSE.
          FR_STATUS (3) = 0
          REC_NUM = ID_REC-1
          REC_SIZE = 1536
          STRT_POS = 0
          CALL CAE_IO_READ(FR_STATUS(3),RXZXCODE(3),%VAL(RXZXDCB(3))
     &       ,%VAL(REC_SIZE),ZX_IDI4B,REC_NUM,BYTE_CNT,STRT_POS)
C
          ID_SEL = ID_SEL + 1
CIBM-
CVAX+
CVAX           SECTOR = ID_REC * 3 - 2
CVAX           CALL NBLKIORW
CVAX      &                (%VAL (RXZXDCB)
CVAX      &                ,%REF (ZX_IDBUF)
CVAX      &                ,%VAL (SECTOR)
CVAX      &                ,%VAL (BYTE_CNT)
CVAX      &                ,RXZXCODE (3)
CVAX      &                ,%VAL (IO$_READVBLK)
CVAX      &                ,%REF (RZXSPAD (3))
CVAX      &                ,RXZXFLG (3)
CVAX      &                ,RZX_AST,)
CVAX           ID_SEL = ID_SEL + 1
CVAX-
CSEL++        ------- SEL Code -------
CSEL           SECTOR = 2 * (ID_REC - 1)
CSEL           Call S_READ (RZX_FDB (0,3)
CSEL      &                ,SSTATUS
CSEL      &               ,,ZX_IDI4B
CSEL      &                ,BYTE_CNT
CSEL      &                ,SECTOR)
CSEL           If (.NOT. SSTATUS) Then
CSEL             RXZXCODE (3) = IAND (RZX_FDB(5,3), X'0000FFFF')
CSEL             RXZXFLG  (3) = .TRUE.
CSEL           Endif
CSEL           ID_SEL = ID_SEL + 1
CSEL-         ------------------------
C
        Endif
      Endif
      Goto 30000
C
20300 Continue
C
      FOUND = .FALSE.
      I = 1
      Do While (I .LE. 255 .AND..NOT. FOUND)
        If ((IDENT .GT. ZX_IDC6I (I)
     &            .AND. IDENT .LT. ZX_IDC6I (I + 1))
     &                  .OR. IDENT .EQ. ZX_IDC6I (I)) Then
          FOUND = .TRUE.
          ID_REC = ZX_FIDST + ((ID_REC - ZX_FIDIN) * 256) + I - 1
        Endif
        I = I + 1
      Enddo
C
      If (.NOT. FOUND) Then
        NEWIDENT = .FALSE.
        ID_SEL = 7
        NO_STNS (RXZIND) = 0
      Else
        ID_SEL = ID_SEL + 1
      Endif
C
20400 Continue
C
      BYTE_CNT = 1536
CIBM+
      RXZXCODE(3) = 0
      RXZXFLG(3)   = .FALSE.
      FR_STATUS (3) = 0
      REC_NUM = ID_REC-1
      REC_SIZE = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FR_STATUS(3),RXZXCODE(3),%VAL(RXZXDCB(3))
     &       ,%VAL(REC_SIZE),ZX_IDI4B,REC_NUM,BYTE_CNT,STRT_POS)
C
      ID_SEL = ID_SEL + 1
CIBM-
C
CVAX+
CVAX       SECTOR = ID_REC * 3 - 2
CVAX       Call NBLKIORW
CVAX      &            (%VAL (RXZXDCB)
CVAX      &            ,%REF (ZX_IDBUF)
CVAX      &            ,%VAL (SECTOR)
CVAX      &            ,%VAL (BYTE_CNT)
CVAX      &            ,RXZXCODE (3)
CVAX      &            ,%VAL (IO$_READVBLK)
CVAX      &            ,%REF (RZXSPAD (3))
CVAX      &            ,RXZXFLG (3)
CVAX      &            ,RZX_AST,)
CVAX       ID_SEL = ID_SEL + 1
CVAX-
C
CSEL++        ------- SEL Code -------
CSEL       SECTOR = 2 * (ID_REC - 1)
CSEL       Call S_READ
CSEL      &          (RZX_FDB (0,3)
CSEL      &          ,SSTATUS
CSEL      &         ,,ZX_IDI4B
CSEL      &          ,BYTE_CNT
CSEL      &          ,SECTOR)
CSEL       If (.NOT. SSTATUS) Then
CSEL         RXZXCODE (3) = IAND (RZX_FDB (5,3), X'0000FFFF')
CSEL         RXZXFLG  (3) = .TRUE.
CSEL       Endif
CSEL       ID_SEL = ID_SEL + 1
CSEL-         ------------------------
C
      Goto 30000
C
20500 Continue
C
C Idents are stored as follows:
C
C    0       6      8       12      16     20    22    24
C    +-------+------+--------+-------+------+-----+-----+
C    | IDENT | TYPE | RECORD | INDEX | FREQ | LAT | LON |
C    +-------+------+--------+-------+------+-----+-----+
C
      I = 1
      FOUND = .FALSE.
      NO_STNS (RXZIND) = 0
      Do While (IDENT (1:6) .NE. ZX_IDC6D (1,I) .AND. I .LT. 65)
        I = I + 1
      Enddo
C
      Do While (IDENT (1:6) .EQ. ZX_IDC6D (1,I) .AND. I .LT. 65)
        NO_STNS (RXZIND) = NO_STNS (RXZIND) + 1
        If (NO_STNS (RXZIND) .LE. M_IDSTNS) Then
          STN_IDE (NO_STNS (RXZIND),RXZIND) = ZX_IDC6D (1,I)
          If (ZX_IDI1B (7,I) .EQ. 3) Then
            STN_FRE (NO_STNS (RXZIND),RXZIND)
     &                              = ZX_IDI4B (5,I) * 0.01
          Else
            STN_FRE (NO_STNS (RXZIND),RXZIND)
     &                              = ZX_IDI4B (5,I) * 0.001
          Endif
          STN_IDX (NO_STNS (RXZIND),RXZIND) = ZX_IDI4B (4,I)
          STN_REC (NO_STNS (RXZIND),RXZIND) = ZX_IDI4B (3,I)
          STN_LAT (NO_STNS (RXZIND),RXZIND) = ZX_IDI2B (11,I)
     &                                                  * INT_TO_DEG
          STN_LON (NO_STNS (RXZIND),RXZIND) = ZX_IDI2B (12,I)
     &                                                  * INT_TO_DEG
          Call CNV_TYP (ZX_IDI1B (7,I)
     &                       ,STN_TYP (NO_STNS (RXZIND),RXZIND))
C
C  Calculate range in degrees ^ 2
C
          STN_RNG (NO_STNS (RXZIND),RXZIND) =
     &             (SNGL (RUPLAT)
     &                   - STN_LAT (NO_STNS (RXZIND),RXZIND)) ** 2 +
     &             ((SNGL (RUPLON)
     &               - STN_LON (NO_STNS (RXZIND),RXZIND))*RUCOSLAT) ** 2
        Endif
        I = I + 1
      Enddo
      If (NO_STNS (RXZIND) .GT. M_IDSTNS) Then
        MESSAGE = ' '
        Call TO_CONSOLE (MESSAGE)
        Write (MESSAGE,902,IOSTAT=IOS) IDENT, NO_STNS (RXZIND), M_IDSTNS
        Call TO_CONSOLE (MESSAGE)
        NO_STNS (RXZIND) = M_IDSTNS
      Endif
      If (NO_STNS (RXZIND) .EQ. 0) Then
        ID_SEL = 7
        LIN_PTR (RXZIND) = 1
        Goto 30000
      Else
        ID_SEL = ID_SEL + 1
      Endif
C
20600 Continue
C
C -- Sort stations
C
      I = 1
      SORTED = .FALSE.
      If (NO_STNS (RXZIND) .GT. 1) Then
        Do While (.NOT. SORTED)
        SORTED = .TRUE.
          Do I = 1, NO_STNS (RXZIND) - 1
            If (STN_RNG (I,RXZIND) .GT. STN_RNG (I+1,RXZIND)) Then
              T_IDE = STN_IDE (I,RXZIND)
              T_TYP = STN_TYP (I,RXZIND)
              T_RNG = STN_RNG (I,RXZIND)
              T_IDX = STN_IDX (I,RXZIND)
              T_REC = STN_REC (I,RXZIND)
              T_FRE = STN_FRE (I,RXZIND)
              T_LAT = STN_LAT (I,RXZIND)
              T_LON = STN_LON (I,RXZIND)
C
              STN_IDE (I,RXZIND) = STN_IDE (I + 1,RXZIND)
              STN_TYP (I,RXZIND) = STN_TYP (I + 1,RXZIND)
              STN_RNG (I,RXZIND) = STN_RNG (I + 1,RXZIND)
              STN_IDX (I,RXZIND) = STN_IDX (I + 1,RXZIND)
              STN_REC (I,RXZIND) = STN_REC (I + 1,RXZIND)
              STN_FRE (I,RXZIND) = STN_FRE (I + 1,RXZIND)
              STN_LAT (I,RXZIND) = STN_LAT (I + 1,RXZIND)
              STN_LON (I,RXZIND) = STN_LON (I + 1,RXZIND)
C
              STN_IDE (I + 1,RXZIND) = T_IDE
              STN_TYP (I + 1,RXZIND) = T_TYP
              STN_RNG (I + 1,RXZIND) = T_RNG
              STN_IDX (I + 1,RXZIND) = T_IDX
              STN_REC (I + 1,RXZIND) = T_REC
              STN_FRE (I + 1,RXZIND) = T_FRE
              STN_LAT (I + 1,RXZIND) = T_LAT
              STN_LON (I + 1,RXZIND) = T_LON
              SORTED = .FALSE.
            Endif
          Enddo
        Enddo
      Endif
      LIN_PTR (RXZIND) = 1
      RXZIVAL (RXZIND) = .FALSE.
      RXZIIDX (RXZIND) = 0
      ID_SEL = ID_SEL + 1
C
20700 Continue
      If (NO_STNS (RXZIND) .EQ. 0) Then
        Do I = 1, MAX_LIN
          ZIDISP (I,RXZIND) = X'20'
          RXZILAT (I, RXZIND) = 0.
          RXZILON (I, RXZIND) = 0.
          RXZIFRE (I, RXZIND) = 0.
          ZIIDE   (I, RXZIND) = BLANK
          ZITYP   (I, RXZIND) = BLANK
          RXZI0LIN (I,RXZIND) = .FALSE.
        Enddo
        ZIDISP (MAX_LIN/2,RXZIND) = '** INVALID IDENT **'
        RXZIVAL (RXZIND) = .FALSE.
        RXZIIDX (RXZIND) = 0
        RXZIREC (RXZIND) = 0
      Else
        LINE = 1
        STN_CNT = LIN_PTR (RXZIND) + LINE - 1
        Do While (LINE .LE. MAX_LIN
     &               .AND. STN_CNT .LE. NO_STNS (RXZIND))
          If (IDENT_OPT .EQ. 1) Then
            If (STN_FRE (STN_CNT,RXZIND) .EQ. 0.0) Then
              Write (ZIDISP (LINE,RXZIND), 801, IOSTAT=IOS)
     &               STN_IDE (STN_CNT,RXZIND),STN_TYP (STN_CNT,RXZIND)
            Else
              Write (ZIDISP (LINE,RXZIND), 800, IOSTAT=IOS)
     &                STN_IDE (STN_CNT,RXZIND)
     &               ,STN_TYP (STN_CNT,RXZIND)
     &               ,STN_FRE (STN_CNT,RXZIND)
            Endif
          Elseif (IDENT_OPT .EQ. 2) Then
            ZIDISP (LINE,RXZIND) = ' '
            Call LAT_LON (STN_LAT(STN_CNT,RXZIND), L_L_STR, .TRUE.)
            Write (ZIDISP (LINE, RXZIND)  (2:11), '(A)'
     &             , IOSTAT=IOS) L_L_STR (1:10)
            Call LAT_LON (STN_LON(STN_CNT,RXZIND), L_L_STR, .FALSE.)
            Write (ZIDISP (LINE, RXZIND) (22:31), '(A)'
     &             , IOSTAT=IOS) L_L_STR (1:10)
            Write (ZIDISP (LINE, RXZIND) (14:17), '(A)'
     &             ,IOSTAT = IOS) STN_TYP (STN_CNT,RXZIND)
          Endif
          RXZILAT (LINE, RXZIND) = STN_LAT (STN_CNT,RXZIND)
          RXZILON (LINE, RXZIND) = STN_LON (STN_CNT,RXZIND)
          RXZIFRE (LINE, RXZIND) = STN_FRE (STN_CNT,RXZIND)
          ZIIDEC  (LINE, RXZIND) = STN_IDE (STN_CNT,RXZIND)
          ZITYP   (LINE, RXZIND) = I4ST_TYP(STN_CNT,RXZIND)
          RXZI0LIN (LINE,RXZIND) = .TRUE.
          LINE = LINE + 1
          STN_CNT = LIN_PTR (RXZIND) + LINE - 1
        Enddo
        If (LINE .LE. MAX_LIN) Then
          Do J = LINE, MAX_LIN
            ZIDISP (J,RXZIND) = X'20'
            RXZILAT (J, RXZIND) = 0.
            RXZILON (J, RXZIND) = 0.
            RXZIFRE (J, RXZIND) = 0.
            ZIIDE   (J, RXZIND) = BLANK
            ZITYP   (J, RXZIND) = BLANK
            RXZI0LIN (J,RXZIND) = .FALSE.
          Enddo
        Endif
        NO_LINE (RXZIND) = LINE - 1
C
        If (NO_STNS (RXZIND) .GT. MAX_LIN) Then
          RXZI0MOR (RXZIND) = .TRUE.
C
C  Reselect station if applicable
C
          If (RXZIIDX (RXZIND) .NE. 0) Then
            Do I = 1, NO_LINE (RXZIND)
              If (RXZIIDX (RXZIND) .EQ.
     &              STN_IDX (LIN_PTR (RXZIND) + I - 1, RXZIND)
     &                      .OR.
     &             RXZIREC (RXZIND) .EQ.
     &              STN_REC (LIN_PTR (RXZIND) + I - 1, RXZIND)) Then
                RXZILINE (I, RXZIND) = .TRUE.
              Endif
            Enddo
          Endif
        Else
          RXZI0MOR (RXZIND) = .FALSE.
        Endif
C
C -- Force selection on station if only one is found
C
        If (NO_STNS (RXZIND) .EQ. 1) RXZILINE (1,RXZIND) = .TRUE.
      Endif
      RXZIRSEL (RXZIND) = .FALSE.
      ID_SEL = 2
      Goto 30000
30000 Continue
99000 FORMAT ('    PAGE ',I4,' OF ',I4)
800   FORMAT (1X,A4,1X,'Type: ',A4,1X,'Freq: ',F7.3)
801   FORMAT (1X,A4,1X,'Type: ',A4)
901   FORMAT (
     &' NAV buffer ',A15,' too small, required:',I4,' Actual max:',I4)
902   FORMAT (
     &' IDENT buffers too small, required:',I6,' Actual max: ',I4)
C
      RETURN
      END
C
      Subroutine CNV_TYP (FMT_TYP, STN_TYP)
C
C  This subroutine will convert a 2-byte coded station type to a
C  4-character ASCII string
C
      Implicit None
C
CSEL+
CSEL       Character
CSEL      &          STN_TYP*4      !Station type (ASCII)
CSEL      &         ,TYPES (8,15)*4 !All possible types
CSEL C
CSEL       BYTE
CSEL      &          FMT_TYP (2)    !Formatted type
CSEL C
CSEL-
CVAX+
CVAX       Character
CVAX      &          STN_TYP*4      !Station type (ASCII)
CVAX      &         ,TYPES (8,15)*4 !All possible types
CVAX C
CVAX       BYTE
CVAX      &          FMT_TYP (2)    !Formatted type
CVAX C
CVAX-
CIBM+
      INTEGER*1   FMT_TYP (2)    !Formatted type
      INTEGER*4   STN_TYP        !Station type (ASCII)
      INTEGER*4  MLS,  IGS  ,ILS  ,
     & ITAC ,IDME ,AILS ,VDME ,VTAC ,AVOR ,
     & VOR  ,TVOR ,VOT  ,NDB  ,LOC  ,LIM  ,
     & LMM  ,LOM  ,ADME ,DME  ,TAC  ,IM   ,
     & MM   ,OM   ,ZM   ,BBM  ,BM   ,FAN  ,
     & LPM  ,VHF  ,UHF  ,FM   ,HF   ,GATE ,
     & INT  ,DUM  ,RWY  ,TER  ,BLANK
C
      PARAMETER (
     & MLS  = X'4D4C5320', IGS  = X'49475320', ILS  = X'494C5320',
     & ITAC = X'49544143', IDME = X'49444D45', AILS = X'41494C53',
     & VDME = X'49544143', VTAC = X'49444D45', AVOR = X'41494C53',
     & VOR  = X'49544143', TVOR = X'49444D45', VOT  = X'41494C53',
     & NDB  = X'49544143', LOC  = X'49444D45', LIM  = X'41494C53',
     & LMM  = X'49544143', LOM  = X'49444D45', ADME = X'41494C53',
     & DME  = X'49544143', TAC  = X'49444D45', IM   = X'41494C53',
     & MM   = X'49544143', OM   = X'49444D45', ZM   = X'41494C53',
     & BBM  = X'49544143', BM   = X'49444D45', FAN  = X'41494C53',
     & LPM  = X'49544143', VHF  = X'49444D45', UHF  = X'41494C53',
     & FM   = X'49544143', HF   = X'49444D45', GATE = X'41494C53',
     & INT  = X'49544143', DUM  = X'49444D45', RWY  = X'41494C53',
     & TER  = X'49544143', BLANK= X'20202020')
C
CIBM-
CSEL+
CSEL       Data
CSEL      &          TYPES /
CSEL      &          'MLS ','IGS ','ILS ','ITAC','IDME','AILS','    ','    '
CSEL      &         ,'VDME','VTAC','AVOR','VOR ','TVOR','VOT ','    ','    '
CSEL      &         ,'NDB ','LOC ','LIM ','LMM ','LOM ','    ','    ','    '
CSEL      &         ,'ADME','DME ','TAC ','    ','    ','    ','    ','    '
CSEL      &         ,'IM  ','MM  ','OM  ','ZM  ','BBM ','BM  ','FAN ','LPM '
CSEL      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CSEL      &         ,'VHF ','UHF ','FM  ','    ','    ','    ','    ','    '
CSEL      &         ,'HF  ','    ','    ','    ','    ','    ','    ','    '
CSEL      &         ,'GATE','INT ','DUM ','    ','    ','    ','    ','    '
CSEL      &         ,'RWY ','    ','    ','    ','    ','    ','    ','    '
CSEL      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CSEL      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CSEL      &         ,'TER ','    ','    ','    ','    ','    ','    ','    '
CSEL      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CSEL      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CSEL      &/
CSEL-
CVAX+
CVAX       Data
CVAX      &          TYPES /
CVAX      &          'MLS ','IGS ','ILS ','ITAC','IDME','AILS','    ','    '
CVAX      &         ,'VDME','VTAC','AVOR','VOR ','TVOR','VOT ','    ','    '
CVAX      &         ,'NDB ','LOC ','LIM ','LMM ','LOM ','    ','    ','    '
CVAX      &         ,'ADME','DME ','TAC ','    ','    ','    ','    ','    '
CVAX      &         ,'IM  ','MM  ','OM  ','ZM  ','BBM ','BM  ','FAN ','LPM '
CVAX      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CVAX      &         ,'VHF ','UHF ','FM  ','    ','    ','    ','    ','    '
CVAX      &         ,'HF  ','    ','    ','    ','    ','    ','    ','    '
CVAX      &         ,'GATE','INT ','DUM ','    ','    ','    ','    ','    '
CVAX      &         ,'RWY ','    ','    ','    ','    ','    ','    ','    '
CVAX      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CVAX      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CVAX      &         ,'TER ','    ','    ','    ','    ','    ','    ','    '
CVAX      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CVAX      &         ,'    ','    ','    ','    ','    ','    ','    ','    '
CVAX      &/
CVAX-
C
      INTEGER*4 TYPES(8,15)      ! All types
     &         /
     &          MLS ,IGS ,ILS ,ITAC,IDME,AILS,BLANK,BLANK
     &         ,VDME,VTAC,AVOR,VOR ,TVOR,VOT ,BLANK,BLANK
     &         ,NDB ,LOC ,LIM ,LMM ,LOM ,BLANK,BLANK,BLANK
     &         ,ADME,DME ,TAC ,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,IM  ,MM  ,OM  ,ZM  ,BBM ,BM  ,FAN ,LPM
     &         ,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,VHF ,UHF ,FM  ,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,HF  ,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,GATE,INT ,DUM ,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,RWY ,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,TER ,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK
     &         ,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK,BLANK
     &/
C
C
      If (FMT_TYP (2) .GT. 8 .OR. FMT_TYP (1) .GT. 15) Then
 
        STN_TYP = BLANK
      Else
        STN_TYP = TYPES (FMT_TYP (2),FMT_TYP (1))
 
      Endif
      Return
      END
C
      SUBROUTINE LAT_LON(LA_LO,STRING,LATITUDE)
      IMPLICIT NONE
C
C FUNCTION: - convert lat/lon
C INPUTS  : - latitude or longitude flag
C           - real*8 lat/lon
C OUTPUTS : - Nddd:mm$ss.cc or Sddd:mm$ss.cc or Eddd:mm$ss.cc or Wddd:mm$ss.cc
C
C
C * Passed argument declaration.
C
      REAL*8        LA_LO       !lat/lon to convert to ASCII
      CHARACTER*(*) STRING      !output string
      LOGICAL*1     LATITUDE    !.true. => latitude conversion
                                !.false.=> longitude conversion
C
C * Local variables.
C
      REAL*8        LATLON      !local copy
      REAL*8        TEMP
      REAL*4        SECOND
      INTEGER*4     MINUTE
      INTEGER*4     DEGREE
C
      REAL*8        Z
      PARAMETER   ( Z = 59.99500D0 )
C
C
      LATLON = LA_LO
      IF ( LATITUDE ) THEN
        IF ( LATLON .GE. 0 ) THEN
          STRING(1:1) = 'N'
        ELSE
          STRING(1:1) = 'S'
          LATLON = -LATLON
        ENDIF
      ELSE
        IF ( LATLON .GE. 0 ) THEN
          STRING(1:1) = 'E'
        ELSE
          STRING(1:1) = 'W'
          LATLON = -LATLON
        ENDIF
      ENDIF
C
C DEGREES
C
      DEGREE = LATLON
C
C MINUTES
C
      TEMP = (LATLON - DEGREE)*60.0D0
      MINUTE  = TEMP
C
C SECONDS
C
      SECOND  = (TEMP-MINUTE)*60.0D0
      IF (SECOND .GE. Z ) THEN
        MINUTE = MINUTE + 1
        SECOND  = SECOND - Z
      ENDIF
C
      IF (MINUTE .GE. 60) THEN
        DEGREE = DEGREE + 1
        MINUTE = MINUTE - 60
      ENDIF
C
C ENCODE THE POSITION IN A CHARACTER STRING
C
      IF ( ABS(DEGREE).GT.999 ) THEN
        STRING(2:4) = '***'
      ELSE
CSEL+
CSEL         ENCODE(3,901,STRING(2:4))DEGREE
CSEL-
CVAX+
CVAX         ENCODE(3,901,STRING(2:4))DEGREE
CVAX-
CIBM+
      WRITE (901,STRING(2:4)) DEGREE
CIBM-
      ENDIF
      STRING(5:5) = ':'
CSEL+
CSEL       ENCODE(2,903,STRING(6:7))MINUTE
CSEL-
CVAX+
CVAX       ENCODE(2,903,STRING(6:7))MINUTE
CVAX-
CIBM+
      WRITE(903,STRING(6:7)) MINUTE
CIBM-
      IF ( STRING(6:6).EQ.' ' ) STRING(6:6) = '0'
      STRING(8:8) = ':'
CSEL+
CSEL       ENCODE(5,905,STRING(9:13))SECOND
CSEL-
CVAX+
CVAX       ENCODE(5,905,STRING(9:13))SECOND
CVAX-
CIBM+
      WRITE (905,STRING(9:13)) SECOND
CIBM-
      IF ( STRING( 9: 9).EQ.' ' ) STRING( 9: 9) = '0'
      IF ( STRING(10:10).EQ.' ' ) STRING(10:10) = '0'
C
      RETURN
 901  FORMAT(I3)
 903  FORMAT(I2)
 905  FORMAT(F5.2)
      END
