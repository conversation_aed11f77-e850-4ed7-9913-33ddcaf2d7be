C'Title             Digital Voice Message Playback Control
C'Module_ID         X9PLAY
C'Customer
C'Author            <PERSON><PERSON>
C'Date              18-Feb-91
C'Source            USD8X9PL.FOR
C'Purpose           To process message play requests on all channels
C'Revision_History
C
C  usd8x9pl.for.1 22Mar2007 01:00 usd8 Tom
C       < Provision for ATIS to say <PERSON><PERSON><PERSON> for KLGA 22A >
C
C     24-Feb-92 D.BOWNESS
C       Add decode of TIME readout (DVREADNUM).
C
C     14-Feb-92 D.BOWNESS
C       Added DVTEXT routine to read phrase descriptions.
C
C     12-Feb-92 D.BOWNESS
C       Add IBM code to close messages database.
C
C      5-Feb-92 D.BOWNESS
C       Add C'da to mark DAS specific code.
C
C     24-Jan-92 D.BOWNESS
C       Add CIBM/CSGI code for strtxrf offsets.
C
C     25-Nov-91 D.BOWNESS
C       Add 0.5 adjustment when assigning real to int for INDEX or
C       READOUT value.
C
C     12-Nov-91 23:54:17 D.BOWNESS
C       Before decoding DCBs, check for cancelled message play request.
C
C      8-Nov-91 01:30:40 D.BOWNESS
C       Fix DVINIT bug and use 6K max for buff_reads.
C
C     22-Oct-91 20:08:56 D.BOWNESS
C       Add check for "stopped" active channels (DAS).
C
C     21-Oct-91 21:44:21 D.BOWNESS
C       Modify digit selection algorithm in READNUM.
C
C     14-Oct-91 23:24:02 D.BOWNESS
C       Set X9CHNACT to YTSIMTM when playback or recording
C       started.
C
C     26-Sep-1991 D.BOWNESS
C       Use only one d.v. include file: stdx9pl.inc
C
C     25-Sep-1991 D.BOWNESS
C       Modify DVDECODE to use version 3.0 message definitions
C       (i.e. using dynamic index and offset blocks).
C
C      6-Sep-1991 D.BOWNESS
C       Modify processing of dynamic offset and flag of sub-DCB.
C
C     20-Aug-1991 D.BOWNESS
C       Begin conversion to SGI (SCTB)
C
C     16-Aug-1991 D.BOWNESS
C       Prefix all subroutines with DV to avoid integration conflicts.
C       Cancel message play request when DECODE error.
C
C     12-Aug-1991 D.BOWNESS
C       Many changes due to DVINIT bug on IBM.
C       Remove calls to cae_aio_local_copy.
C
C     31-jul-1991 10:20 B.Trudel ing.jr.
C       Conversion to IBM (CHINA MD-11)
C
C     10-May-91 D.BOWNESS
C       Modify DVSUBDCB to accept L1:I4 and I4:L1 operands.
C       Add code to close msg defn database on request.
C
C
      SUBROUTINE USD8X9PL
C     -------------------
      IMPLICIT NONE
C
C'Functions
      INTEGER  DVDECODE, DVINIT
CVAX
CVAX  EXTERNAL DVAST01
CVAXEND
C
C'Include_files
      INCLUDE 'usd8x9pl.inc'        !NOFPC
CVAX
CVAX  INCLUDE '($IODEF)/NOLIST'                  !NOFPC
CVAX  INCLUDE '($SSDEF)/NOLIST'                  !NOFPC
CVAXEND
CIBM
      INCLUDE 'cae_io.inc'          !NOFPC
CIBMEND
CSGI
CSGI -start cae_io.inc-------------------------------------------------   \C ---
CSGI                                                                      \C
CSGI  structure /io_info_struct /
CSGI    integer fd
CSGI    integer record_size
CSGI    integer status
CSGI    integer private
CSGI    integer bufferp
CSGI    integer file_namep
CSGI    integer o_flag
CSGI    integer record_num
CSGI    integer amount
CSGI    integer position
CSGI  end structure
CSGI                                                                      \C
CSGI -end cae_io.inc---------------------------------------------------   \C ---
CSGIEND
C
C'Common_blocks
CP    USD8
CP   .        X9CHNACT,X9PPOS,X9PLIS,X9MSGPLY,X9MSGPOS,X9PREQ,
CP   .        X9SPKR,X9PSTOP, X9PPTR,
CP   .        X9STATUS,YTSIMTM
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 21-Aug-2019 19:30:20 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*4   
     &  YTSIMTM        ! SIMULATOR TIME (SEC)
C$
      INTEGER*4
     &  X9CHNACT(10)   ! CHANNEL ACTIVE TIMER
     &, X9STATUS(50)   ! STATUS CODES
C$
      INTEGER*2
     &  X9MSGPLY(10)   ! MESSAGE PLAYING ON CHANNEL
     &, X9MSGPOS(10)   ! NEXT PLAY POSITION FOR MSG ON CHANNEL
     &, X9PLIS(32,10)  ! LIST OF PHRASES FOR PLAYING A MESSAGE
     &, X9PPOS(10)     ! CURRENT POSITION IN X9PLIS
     &, X9PPTR(10)     ! CURRENT PLAY POSITION IN ACTIVE BUFFER
     &, X9PREQ(10)     ! BEGIN PLAYING A LIST OF PHRASES
     &, X9SPKR(10)     ! SELECTED SPEAKER FOR CURRENT CHANNEL
C$
      LOGICAL*1
     &  X9PSTOP(10)    ! STOP PLAYBACK
C$
      LOGICAL*1
     &  DUM0000001(24),DUM0000002(128492),DUM0000003(76)
     &, DUM0000004(40),DUM0000005(30),DUM0000006(20)
     &, DUM0000007(164056)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YTSIMTM,DUM0000002,X9STATUS,DUM0000003,X9SPKR
     &, X9PLIS,X9PPOS,X9PREQ,DUM0000004,X9PSTOP,DUM0000005,X9MSGPLY
     &, X9MSGPOS,DUM0000006,X9CHNACT,DUM0000007,X9PPTR    
C------------------------------------------------------------------------------
CIBM
      INTEGER*4
     .          ftn_status
CIBMEND
CSGI
CSGI  RECORD /io_info_struct/ msgdat
CSGIEND
      INTEGER*4 NR_CHNS
      PARAMETER(NR_CHNS=10)
C
      INTEGER*4
     .          chn, p
     .,         dcb_rstat
     .,         dcb_actv     /0/
     .,         error
     .,         rstat        /0/
     .,         bytecnt
     .,         recsize    /256/
     .,         offset
     .,         recnum
     .,         istate       /1/
     .,         oX9PPTR(10)
     .,         perrcnt(10)
      INTEGER*2
     .          mx
     .,         Pmax/32/           ! Max num of phrases in one request
      LOGICAL*1
     .          dcb_ios
      CHARACTER
     .          datname*64
C
C=======================================================================
C
      ENTRY X9PLAY
C     ------------
C
C --                call DVINIT to open the messages database and
C                   initialize the digital voice directories
C
      IF( X9STATUS(9) .NE. 0 ) istate = X9STATUS(9)
      X9STATUS(9) = 0
      IF( istate .NE. 0 )THEN
        IF( istate .EQ. 1 )THEN
          istate = 2
          Datunr = 0
          DO chn = 1, NR_CHNS
            X9SPKR(chn) = 1
          ENDDO
          ! X9STATUS(11) = 50
        ELSE IF( istate .EQ. 2 )THEN
          rstat = DVINIT(datname)
          IF( rstat .EQ. 1 )THEN
            istate = 0
            X9STATUS(10) = IOR(X9STATUS(10),4)
CSGI
CSGI        msgdat.fd          = Datunr
CSGI        msgdat.record_size = 256
CSGI        msgdat.bufferp     = %LOC(DcbBuf)
CSGIEND
          ELSE IF( rstat .NE. 0 )THEN
            istate = 99
            X9STATUS(1) = 1001
            X9STATUS(2) = rstat
          ENDIF
        ELSE IF( istate .EQ. -1 )THEN
          rstat = 0
CVAX
CVAX      CALL BUFF_CLOSE( Datunr,
CVAX .                     rstat,
CVAX .                     dcb_ios,
CVAX .                     DVAST01 )
CVAXEND
CIBM
          CALL CAE_IO_CLOSE( ftn_status, dcb_rstat, %VAL(Datunr) )
          IF( ftn_status .NE. 1 )THEN
            X9STATUS(1) = 1009
            X9STATUS(2) = ftn_status
          ENDIF
CIBMEND
          X9STATUS(10) = IAND(X9STATUS(10),-5)
          Datunr = 0
          istate = 99
        ENDIF
        RETURN
      ENDIF
C --               End of "first pass" processing
C
C
C --               for each channel, if channel is free then
C                  check for msg play request
C
      DO chn = 1, NR_CHNS
        IF( X9CHNACT(chn) .LE. 1 )THEN
          IF( chn .EQ. dcb_actv ) dcb_actv = 0
          IF( X9MSGPLY(chn).NE.0 .AND. dcb_actv.EQ.0 )THEN
            mx = X9MSGPLY(chn)
            IF( mx .LT. 0 ) mx = -mx
            recnum = MsgI2(D_DEFREC,mx)
            bytecnt= ( MsgI2(D_DCBLEN, mx)
     .               + MsgI2(D_DYNLEN, mx)
     .               + MsgI2(D_OFFLEN, mx) ) * 2
            IF( recnum .LE. 0 )THEN
              X9MSGPLY(chn) = 0
              X9MSGPOS(chn) = 0
              X9CHNACT(chn) = -1
            ELSE
C
C ----             msg play requested, read msg definition DCBs
C
CVAX
CVAX          offset = 1
CVAX          CALL BUFF_READ( Datunr,
CVAX .                  %DESCR(DcbBuf),
CVAX .                  recnum,
CVAX .                  offset,
CVAX .                  bytecnt,
CVAX .                  dcb_rstat,
CVAX .                  dcb_ios,
CVAX .                  DVAST01 )
CVAXEND
CIBM
              ftn_status = 0
              dcb_rstat = 0
              recnum = recnum - 1
              offset = 0
              CALL CAE_IO_READ( ftn_status, dcb_rstat, %VAL(Datunr),
     .                          %VAL(256), DcbBuf(1),
     .                          recnum, bytecnt, offset )
              IF( ftn_status .EQ. 1 )THEN
                dcb_ios = .FALSE.
              ELSE
                X9STATUS(1) = 1021
                X9STATUS(2) = ftn_status
              ENDIF
CIBMEND
CSGI
CSGI          msgdat.record_num = recnum - 1
CSGI          msgdat.position = 0
CSGI          msgdat.amount = bytecnt
CSGI          CALL cae_io_read( msgdat, 12 )
CSGI          dcb_rstat = 0
CSGI          dcb_ios = .FALSE.
CSGIEND
              dcb_actv = chn
              X9PPOS(chn) = 0
              X9CHNACT(chn) = 2
              perrcnt(chn) = 0
            ENDIF
          ENDIF
C
C ----             if msg DCBs read completed, get phrase play list
C
        ELSE IF( X9CHNACT(chn) .EQ. 2 )THEN
CIBM
          IF( dcb_rstat .NE. 0 ) dcb_ios = .TRUE.
CIBMEND
CSGI
CSGI      IF( msgdat.status .NE. 999 )THEN
CSGI        dcb_ios = .TRUE.
CSGI        dcb_rstat = msgdat.status
CSGI      ENDIF
CSGIEND
C
          IF( dcb_ios .AND. dcb_rstat.NE.1 )THEN
            X9STATUS(1) = 1022
            X9STATUS(2) = dcb_rstat
            dcb_actv = 0
            X9MSGPLY(chn) = 0
            X9MSGPOS(chn) = 0
            X9CHNACT(chn) = -2
C
          ELSE IF( dcb_ios )THEN
            dcb_actv = 0
            X9CHNACT(chn) = 3
            mx = X9MSGPLY(chn)
            IF( mx .LT. 0 ) mx = -mx
            IF( mx .GT. 0 )THEN
              error = DVDECODE(mx, X9MSGPOS(chn),
     .                         X9PLIS(1,chn), Pmax, X9PPOS(chn),
     .                         X9SPKR(chn))
            ELSE
              error = 0
              X9PPOS(chn) = 0
            ENDIF
            IF( error .GE. 0 )THEN
C'dbg         WRITE(6,'(A,I3,(/10I5))') ' %X9PLAY, play,',chn,          !'dbg
C'dbg.          (X9PLIS(p,chn),p=1,X9PPOS(chn))
              X9PREQ(chn) = X9PPOS(chn)
              X9PPOS(chn) = 1
              IF( error .EQ. 0 ) X9MSGPOS(chn) = 0
            ELSE
              WRITE(6,*) '%X9PLAY, Error from dvdecode,',error
              X9STATUS(3) = chn
              X9STATUS(4) = error
              dcb_actv = 0
              X9MSGPLY(chn) = 0
              X9MSGPOS(chn) = 0
              X9CHNACT(chn) = -3
            ENDIF
          ENDIF
C
C ----             if active channel, check for end of msg playback
C
        ELSE
          IF( chn .EQ. dcb_actv ) dcb_actv = 0
          IF( X9PREQ(chn) .LE. 0 )THEN
C'dbg       WRITE(6,'(A,I5,A)') ' %X9PLAY, channel',chn,' ended'        !'dbg
            IF( X9MSGPOS(chn).LE.0 .AND. X9MSGPLY(chn).GE.0 )THEN
              X9CHNACT(chn) = 0
              X9MSGPLY(chn) = 0
            ELSE
              X9CHNACT(chn) = 1
            ENDIF
C
          ELSE
C+das
            IF( oX9PPTR(chn) .NE. X9PPTR(chn) )THEN
              perrcnt(chn) = 0
              oX9PPTR(chn) = X9PPTR(chn)
            ELSE
              perrcnt(chn) = 1 + perrcnt(chn)
              IF( perrcnt(chn) .GT. 50 )THEN !'dbg play stopped X9STATUS(11)
                X9PSTOP(chn) = .TRUE.
C'dbg           X9STATUS(12)=1+X9STATUS(12) !'dbg cnt chn hangups
                perrcnt(chn) = 0
              ENDIF
            ENDIF
C-das
          ENDIF
C
        ENDIF
      ENDDO
      RETURN
      END
C.......................................................................
C
      INTEGER FUNCTION DVDECODE*4(Msgp,Dcbp,Plis,Pmax,Pptr,Spkr)
C     ---------------------------
      IMPLICIT NONE
C
C'Purpose
C     To generate a list of phrases from a set of Digital Voice DCBs
C
C'Parameters
      INTEGER*2
     .    Msgp      ! Input,  points to message directory entry
     .,   Dcbp      ! In/Out, points to start/next DCB to process
     .,   Plis(*)   ! Output, generated list of phrases
     .,   Pmax      ! Input,  size of Plis
     .,   Pptr      ! Output, number of phrases generated
     .,   Spkr      ! Input,  selects recording set
C
C'Functions
      INTEGER  IAND, DVGETVAL, DVREADNUM, DVREADSTR
C
C'Include_files
      INCLUDE  'usd8x9pl.inc'                    !NOFPC
C
C'Local_variables
      INTEGER*4
     .    i2cnt
     .,   dynp
     .,   ofbp
     .,   i2p, i4p
     .,   i2xp
     .,   value, scale, trans, dynam, temp
     .,   i4dcb(2048)
      REAL*4
     .    r4val, r4sca, r4tra, r4dyn, r4temp
     .,   r4dcb(2048)
      INTEGER*2
     .    valtyp, scatyp, tratyp, dyntyp
     .,   dcbtyp
     .,   width
     .,   options
     .,   i2dcb(4096)
     .,   xphrase
     .,   rcrdset
     .,   i2tmp
     .,   SUBDCB /256/ ! to test option word
      LOGICAL*1
     .    reals
     .,   xresult
      CHARACTER
     .    string(16)*1
      EQUIVALENCE
     .    (i4dcb, DcbBuf)
     .,   (r4dcb, DcbBuf)
     .,   (i2dcb, DcbBuf)
     .,   (value, r4val)
     .,   (scale, r4sca)
     .,   (trans, r4tra)
     .,   (dynam, r4dyn)
     .,   (temp,  r4temp)
C=======================================================================
C
C --               Skip to requested start dcb
C
      temp  = 1
      i2p   = 0
      i2cnt = MsgI2(D_DCBLEN,Msgp)
      dynp = i2cnt + 1
      ofbp = dynp + MsgI2(D_DYNLEN,Msgp)
      IF( i2cnt .LE. 0 )THEN
        DVDECODE = -1
      ELSE
        DO WHILE( temp .LT. Dcbp )
          i2p = i2p + i2dcb(i2p+P_SIZ)
          temp = temp + 1
        ENDDO
        Dcbp = temp                    ! in case Dcbp was zero
C'dbg   WRITE(6,'(A,I4)')                                               !'dbg
C'dbg.    ' _Begin DECODEing...  Dcbp =', Dcbp
C
C --               Decode DCBs until phrase list is full, or
C                  Break DCB, or end of DCB buffer
C
        rcrdset = Hdr_MaxPhr * (Spkr-1)
        Pptr = 0
        DO WHILE( i2p .LT. i2cnt )
C'dbg     WRITE(6,'(3(A,I4))') ' __i2p =',i2p,                          !'dbg
C'dbg.      ', size =',i2dcb(i2p+1), ', type =',i2dcb(i2p+2)
          i4p = i2p / 2
          dcbtyp = i2dcb(i2p+P_DCBT)
          valtyp = i2dcb(i2p+P_VALT)
          options = i2dcb(i2p+P_OPT)
C
C ----                                           Break DCB
          IF( dcbtyp .EQ. 0 )THEN
            i2cnt = 0                            ! end decode loop
C
C ----                                           Phrase DCB
          ELSE IF( dcbtyp .EQ. 1 )THEN
            Pptr = Pptr + 1
            Plis(Pptr) = options + rcrdset
C
C ----                                           Select DCB
          ELSE IF( dcbtyp .EQ. 2 )THEN
            i2xp = i2p + P_COND
            IF( options .EQ. 0 )THEN
              temp = 1
              value = 1
            ELSE
              temp = 1
              value = valtyp
              valtyp = 1
            ENDIF
            DO WHILE( temp .LE. value )
              CALL DVSUBDCB(i2xp, dynp, ofbp,
     .                      valtyp, xresult, xphrase)
              IF( xresult )THEN
                Pptr = Pptr + 1
                Plis(Pptr) = xphrase + rcrdset
              ENDIF
              i2xp = i2xp + i2dcb(i2xp)
              temp = temp + 1
            ENDDO                                ! end of SELECT DCB
C
C ----                                           Index or Readout DCB
          ELSE IF( dcbtyp .LE. 4 )THEN
            scatyp = i2dcb(i2p+P_SCAT)
            tratyp = i2dcb(i2p+P_TRAT)
            dyntyp = i2dcb(i2p+P_DYNT)
            width  = i2dcb(i2p+P_WID)
            IF( width .LT. 1 ) width = 1
C
C ------            Evaluate EXPR, then continue processing DCB
C                   only if expression evaluatues to true.
C
            IF( IAND(i2dcb(i2p+P_OPT),SUBDCB) .EQ. 0 )THEN
              xresult = .TRUE.
            ELSE
              i2xp = i2p + P_EXSZ
              i2tmp = 1
              CALL DVSUBDCB(i2xp, dynp, ofbp,
     .                      i2tmp, xresult, xphrase)
            ENDIF
C
            IF( xresult )THEN
C ------                                         Is REAL processing used?
C
              reals = valtyp.EQ.3 .OR. scatyp.EQ.3 .OR. tratyp.EQ.3
     .           .OR. valtyp.EQ.10.OR. scatyp.EQ.10.OR. tratyp.EQ.10
C
C ------                                         Label Keyword
              i2tmp = DVGETVAL(i4dcb(i4p+P_VALO),
     .                         i2dcb(dynp), i2dcb(ofbp),
     .                         value, width, string)
              IF( reals .AND. valtyp.LE.2 ) r4val = REAL(value)
C
C ------                                         Scale Keyword
              IF( scatyp .GT. 0 )THEN
                IF( scatyp .LE. 4 )THEN
                  i2tmp = DVGETVAL(i4dcb(i4p+P_SCAO),
     .                             i2dcb(dynp), i2dcb(ofbp),
     .                             scale, width, string)
                ELSE
                  scale = i4dcb(i4p+P_SCAO)
                ENDIF
                IF( reals .AND. (scatyp.LE.2.OR.scatyp.EQ.9) )
     .            r4sca = REAL(scale)
                IF( reals )THEN
                  r4val = r4val * r4sca
                ELSE
                  value = value * scale
                ENDIF
              ENDIF
C
C ------                                         Translate Keyword
              IF( tratyp .GT. 0 )THEN
                IF( tratyp .LE. 4 )THEN
                  i2tmp = DVGETVAL(i4dcb(i4p+P_TRAO),
     .                             i2dcb(dynp), i2dcb(ofbp),
     .                             trans, width, string)
                ELSE
                  trans = i4dcb(i4p+P_TRAO)
                ENDIF
                IF( reals .AND. (tratyp.LE.2.OR.tratyp.EQ.9) )
     .            r4tra = REAL(trans)
                IF( reals )THEN
                  r4val = r4val + r4tra
                ELSE
                  value = value + trans
                ENDIF
              ENDIF
C
C ------                                         Before Keyword
              IF( i2dcb(i2p+P_BEF) .GT. 0 )THEN
                Pptr = Pptr + 1
                Plis(Pptr) = i2dcb(i2p+P_BEF) + rcrdset
              ENDIF
C
C ------                                         Index DCB, get phr nr
              IF( dcbtyp .EQ. 3 )THEN
                IF( reals ) value = r4val + 0.5
                IF( value.GE.1 .AND. value.LE. 1024 )THEN
                  Pptr = Pptr + 1
                  Plis(Pptr) = value + rcrdset
                ENDIF
C
C ------                                         Readout DCB, num or str?
              ELSE
                IF( valtyp .LE. 4 )THEN
                  IF( reals ) valtyp = 4
                  i2tmp = DVREADNUM(Plis,Pptr,rcrdset,valtyp,value,
     .                              width,i2dcb(i2p+P_DEC),
     .                              options)
                ELSE
                  i2tmp = DVREADSTR(Plis,Pptr,rcrdset,
     .                              string,width,options)
                ENDIF
              ENDIF
C
C ------                                         After Keyword
              IF( i2dcb(i2p+P_AFT).GT. 0 )THEN
                Pptr = Pptr + 1
                Plis(Pptr) = i2dcb(i2p+P_AFT) + rcrdset
              ENDIF
C
            ENDIF                                ! end of INDEX/READOUT DCB
C
C ----                                           DV Compiler Error
          ELSE
            ! TYPE *,'??? Unknown DCB type'
            i2cnt = -21
C
          ENDIF
          IF( i2dcb(i2p+P_SIZ) .LE. 0 ) i2cnt = -11  ! system error
          i2p = i2p + i2dcb(i2p+P_SIZ)
          IF( Pptr .GE. Pmax ) i2cnt = 0
          Dcbp = Dcbp + 1
        ENDDO
        IF( i2cnt .EQ. 0 )THEN
          DVDECODE = i2p
        ELSE IF( i2cnt .GT. 0 )THEN
          DVDECODE = 0
        ELSE
          DVDECODE = i2cnt
        ENDIF
      ENDIF
      RETURN
      END
C.......................................................................
C
      SUBROUTINE DVSUBDCB(dcbp,dynp,ofbp,nrexpr,resflg,resphr)
C     -------------------
      IMPLICIT NONE
C
C'Include_files
      INCLUDE 'usd8x9pl.inc'    !NOFPC
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'usd8xd.inc'      !NOFPC
C
C'Functions
      INTEGER   DVGETVAL
C
C'Parameters
      INTEGER*4 DCBP                            ! ptr to DCB buf
     .,         DYNP                            ! ptr to dyn index blks
     .,         OFBP                            ! ptr to offset blks
      INTEGER*2 NREXPR                          ! Total number of expression
      LOGICAL*1 RESFLG                          ! returned condition
      INTEGER*2 RESPHR                          ! Returned phrase
C
C'Local_variables
C
C DCB equivalences
      INTEGER*2 DCB(4096)
      INTEGER*4 I4DCB(2048)
      REAL*4    R4DCB(2048)
      EQUIVALENCE
     .    (I4DCB, DCBBUF)
     .,   (R4DCB, DCBBUF)
     .,   (DCB,   DCBBUF)
C
C stacks
      INTEGER*4 STACKI4(MAX_STACK)              ! stack of int*4 element
      LOGICAL*1 STACKL1(MAX_STACK)              ! stack of log*1 element
      REAL*4    STACKR4(MAX_STACK)              ! stack of real*4 element
CVAX
CVAX  BYTE
CVAXEND
CIBM
      INTEGER*1
CIBMEND
CSGI
CSGI  INTEGER*1
CSGIEND
     &          OPERATION(0:MAX_OPERATION)      ! expression stack
     & ,        OPERAND(MAX_OPERATION)          ! operand stack ptr
     &,         STACKI1(MAX_STACK)              ! stackl1 integer equivalent
C
C stack pointers
      INTEGER*4 CURPTR(5)                       ! ptr for ea data typ stack
     . ,        CUROP                           ! ptr in operation stack
     . ,        CURDAT                          ! ptr in operand stack
C
C  temporary
      INTEGER*4 TEMPI4(2)
      LOGICAL*1 TEMPL1(2)
      REAL*4    TEMPR4(2)
C
C  other
      INTEGER*4   DCB_OFFSET
     . ,          CURR_EXP          ! number of sub-dcb processed so far
     . ,          RETN              ! RETURN LABELs
     . ,          RSTAT
C
      INTEGER*2   CUR_SIZE          ! Last byte of current sub-dcb size
     . ,          CUR_OPER          ! current operation type
     . ,          CUR_TYPE          ! second operand type
     . ,          I2ONE /1/         ! for i2 arg in s/r call
CVAX
CVAX  BYTE
CVAXEND
CIBM
      INTEGER*1
CIBMEND
CSGI
CSGI  INTEGER*1
CSGIEND
     .            RUN_TYPE          ! operand type of operation executed
     . ,          RUN_OPER          ! operation being executed
     .,           TEMPI1(2)
C
      CHARACTER
     .            VALUEBUF(8)       ! Value buffer
C
      REAL*4      R4VALUE
      INTEGER*4   I4VALUE
      LOGICAL*1   L1VALUE
C
      EQUIVALENCE(R4VALUE, I4VALUE)
     & ,         (L1VALUE, VALUEBUF)
     & ,         (STACKL1(1), STACKI1(1) )
     & ,         (TEMPL1(1), TEMPI1(1))
C=======================================================================
C
      RESFLG     = .FALSE.
      DCB_OFFSET = DCBP
      CURR_EXP   = 0
C
      DO WHILE( (NREXPR.GT.CURR_EXP) .AND. (.NOT.RESFLG) )
         CUR_SIZE   = DCB(DCB_OFFSET) + DCB_OFFSET
         RESPHR     = DCB(DCB_OFFSET + COLOR_CODE)
         DCB_OFFSET = DCB_OFFSET + 2
C
         CURPTR(DTYP_I4)   = 1                    ! init stack ptrs
         CURPTR(DTYP_R4)   = 1
         CURPTR(DTYP_BYTE) = 1
         CUROP             = 1
         CURDAT            = 1
         RETN              = 1                    ! "subroutine" return posn
C
         DO WHILE (DCB_OFFSET .LT. CUR_SIZE)
            CUR_OPER   = DCB(DCB_OFFSET)
            DCB_OFFSET = DCB_OFFSET + 1
            IF (CUR_OPER .GT. 0) THEN
               IF (CUR_OPER .LT. 20) THEN         ! If operand found, call
                  GOTO 10000                      ! "ADD VALUE TO STACK"
 100              CONTINUE
C
               ELSE                               ! Else perform operation
                  IF (CUR_OPER .EQ. OP_LPAR) THEN
                     OPERATION(CUROP) = CUR_OPER  ! '(', do nothing
                     CUROP = CUROP + 1            ! only add on stack
                  ELSE
C
C                    while the operation on top of the stack has
C                    priority over the current operation, do the
C                    operation on the stack
C
                     DO WHILE( (CUROP .GT. 1) .AND.
     -               (OPER_STRENGTH(OPERATION(CUROP-1))
     -               .LE. OPER_STRENGTH(CUR_OPER)) )
                        GOTO 20000                ! Call "PERFORM OPERATION"
 200                    CONTINUE
                     ENDDO
C
C                    now add current operation on stack
C
                     IF (CUR_OPER .NE. OP_RPAR) THEN ! don't add ')' on stack
                        OPERATION(CUROP) = CUR_OPER
                        CUROP = CUROP + 1
                     ELSE                         ! remove the '('
                        CUROP = CUROP - 1         ! left on the stack
                     ENDIF
                  ENDIF
               ENDIF
            ENDIF
         ENDDO
C
C ---    Complete evaluation: Flush the operation stack
C
         RETN = 2
         DO WHILE (CUROP .NE. 1)
            GOTO 20000                            ! Call "PERFORM OPERATION"
 300        CONTINUE
         ENDDO
         RESFLG = STACKL1(1)
C
         CURR_EXP = CURR_EXP + 1
      ENDDO
C
      IF (.NOT. RESFLG) RESPHR = 0
C
      RETURN
C-----------------------------------------------------------------------
C
C --- subroutine "ADD VALUE TO STACK"
10000 CONTINUE
C
C --- This subroutine adds the current data element on the appropriate
C     stack and update the operation accordingly. It's using the variables
C     DCB, DCB_OFFSET, CUR_OPER, VALUEBUF and the stack's variables...
C
      IF( CUR_OPER .EQ. DTYP_I1 ) CUR_OPER = DTYP_BYTE
      RSTAT = 1 + DCB_OFFSET/2
      IF (CUR_OPER .LT. DTYP_CI4)  THEN ! not constant
         RSTAT = DVGETVAL(I4DCB(RSTAT),
     .                    DCB(DYNP), DCB(OFBP),
     .                    I4VALUE, I2ONE, VALUEBUF)
C
C --- add value on rigth value stack
C --- I*2 AND R*8 are type cast to I*4 and R*4 respectively
C
         GOTO (10010, 10010, 10030, 10010, 10050) CUR_OPER
         CONTINUE                        ! ERROR
10010    CUR_OPER =2                     ! i*2 or i*4
         STACKI4(CURPTR(2))   = I4VALUE
         GOTO 10500
10030    CUR_OPER = 3                    ! R*4 or R*8
         STACKR4(CURPTR(3))   = R4VALUE
         GOTO 10500
10050    STACKL1(CURPTR(5))   = L1VALUE  ! L*1
10500    CONTINUE
      ELSE IF (CUR_OPER .EQ. 9) THEN     ! I*4 constant
         CUR_OPER = 2
         STACKI4(CURPTR(2)) = I4DCB(RSTAT)
      ELSE                               ! R*4 constant
         CUR_OPER = 3
         STACKR4(CURPTR(3)) = R4DCB(RSTAT)
      ENDIF
C
C --- add pointer in operation stack
C
      OPERAND(CURDAT)  = CUR_OPER
      CURDAT           = CURDAT + 1
      CURPTR(CUR_OPER) = CURPTR(CUR_OPER) + 1
C
C --- Update DCB offset
C
      DCB_OFFSET = DCB_OFFSET + 2
C
      GOTO 100
C-----------------------------------------------------------------------
C
C --- subroutine "PERFORM OPERATION"
20000 CONTINUE
C
C --- This subroutine is called when we want to execute the last operation on
C     the  stack, it's updating the stack ptr works only with operation using
C     zero, one or two operand(s)
C
      CUROP    = CUROP  - 1
      CURDAT   = CURDAT - 1
      RUN_OPER = OPERATION(CUROP)
      RUN_TYPE = OPERAND(CURDAT)
      IF (OPER_CTN(RUN_OPER) .GT. 0) THEN
         CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
C
         GOTO (20100, 20010, 20020, 20100, 20030) RUN_TYPE
20010    TEMPI4(2) = STACKI4(CURPTR(DTYP_I4))
         GOTO 20100
20020    TEMPR4(2) = STACKR4(CURPTR(DTYP_R4))
         GOTO 20100
20030    TEMPL1(2) = STACKL1(CURPTR(DTYP_BYTE))
20100    CONTINUE
C
         IF (OPER_CTN(RUN_OPER) .EQ. 2) THEN ! there is a second operand
            CURDAT = CURDAT - 1
            CUR_TYPE = OPERAND(CURDAT)
            IF (RUN_TYPE .EQ. CUR_TYPE) THEN ! same type
               CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
C
               GOTO (20200, 20110, 20120, 20200, 20130) RUN_TYPE
20110          TEMPI4(1) = STACKI4(CURPTR(RUN_TYPE))
               GOTO 20200
20120          TEMPR4(1) = STACKR4(CURPTR(RUN_TYPE))
               GOTO 20200
20130          TEMPL1(1) = STACKL1(CURPTR(RUN_TYPE))
20200          CONTINUE
C
C ---- operand types not the same, check for some combinations
C      if allowable combination not found, pop stack and forget it
C
            ELSE IF(RUN_TYPE.EQ.DTYP_R4 .AND. CUR_TYPE.EQ.DTYP_I4)THEN
               RUN_TYPE         = DTYP_R4
               CURPTR(DTYP_I4)  = CURPTR(DTYP_I4) - 1
               TEMPR4(1)        = STACKI4(CURPTR(DTYP_I4)) ! type cast
C
            ELSE IF(RUN_TYPE.EQ.DTYP_I4 .AND. CUR_TYPE.EQ.DTYP_R4)THEN
               RUN_TYPE         = DTYP_R4
               TEMPR4(2)        = TEMPI4(2)                ! type cast
               CURPTR(DTYP_R4)  = CURPTR(DTYP_R4) - 1
               TEMPR4(1)        = STACKR4(CURPTR(DTYP_R4))
C
            ELSE IF(
     .      RUN_TYPE.EQ.DTYP_I4 .AND. CUR_TYPE.EQ.DTYP_BYTE
     .      )THEN
               RUN_TYPE         = DTYP_I4
               CURPTR(DTYP_BYTE)= CURPTR(DTYP_BYTE) - 1
               TEMPI4(1)        = STACKI1(CURPTR(DTYP_BYTE)) ! type cast
C
            ELSE IF(
     .      RUN_TYPE.EQ.DTYP_BYTE .AND. CUR_TYPE.EQ.DTYP_I4
     .      )THEN
               RUN_TYPE         = DTYP_I4
               TEMPI4(2)        = TEMPI1(2)                ! type cast
C
               CURPTR(DTYP_I4)  = CURPTR(DTYP_I4) - 1
               TEMPI4(1)        = STACKI4(CURPTR(DTYP_I4))
C
            ELSE
               CURPTR(CUR_TYPE) = CURPTR(CUR_TYPE) - 1
            ENDIF
         ENDIF
      ENDIF
C
C --- do operation
C
      GOTO (21010, 21020, 21030, 21040, 21050,
     .      21060, 21070, 21080, 21090, 21100,
     .      21110, 21120, 21130, 21140, 21150,
     .      21160, 21170) (RUN_OPER - 20)
      CONTINUE        ! ERROR UNKNOWN OPERATION
C
21010 IF (RUN_TYPE .EQ. DTYP_I4) THEN                              ! .EQ.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .EQ. TEMPI4(2)
         CURPTR(DTYP_BYTE)          = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .EQ. TEMPR4(2)
         CURPTR(DTYP_BYTE)          = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE                               ! ERROR
         CONTINUE
      ENDIF
      GOTO 21999
21020 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .GE.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .GE. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .GE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21030 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .LE.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .LE. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .LE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21040 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .NE.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .NE. TEMPI4(2)
         CURPTR(DTYP_BYTE) = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .NE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21050 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .LT.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .LT. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .LT. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21060 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .GT.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .GT. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .GT. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21070 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN                          ! .OR.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPL1(1) .OR. TEMPL1(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21080 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN                          ! .AND.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPL1(1) .AND. TEMPL1(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21090 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! +
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) + TEMPI4(2)
         CURPTR(DTYP_I4)     = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) + TEMPR4(2)
         CURPTR(DTYP_R4) = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21100 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! -
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) - TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) - TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21110 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! *
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) * TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) * TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21120 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! /
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) / TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) / TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21130 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN                          ! .NOT.
         STACKL1(CURPTR(DTYP_BYTE)) = .NOT. TEMPL1(2)
         CURPTR(DTYP_BYTE) = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE              ! ERROR
      ENDIF
      GOTO 21999
21140 GOTO 21999               ! (, should never happend
21150 GOTO 21999               ! ), should never happend
21160 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! unary -
         STACKI4(CURPTR(DTYP_I4)) = - TEMPI4(2)
         CURPTR(DTYP_I4) = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = - TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE              ! ERROR
      ENDIF
      GOTO 21999
21170 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! unary +
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE              ! ERROR
      ENDIF
      GOTO 21999
21999 GOTO (200, 300) RETN
      END
C.......................................................................
C
      INTEGER FUNCTION DVGETVAL(ptr,dynblk,offblk,retval,strlen,retstr)
C     -------------------------
      IMPLICIT NONE
C
C'Purpose
C     To get a value from the CDB using information in the dynamic
C     indexing blocks and the offset blocks.  Each dynamic index
C     requires a CDB access.
C
C'Parameters
      INTEGER*2
     .    dynblk(*),    ! Inp,    dynamic indexing blocks
     .    strlen        ! Inp,    number of characters to be copied from CDB
      INTEGER*4
     .    ptr,          ! Inp,    ptr to dynblk or offblk
     .    offblk(4,*),  ! Inp,    offset blocks for CDB labels
     .    retval        ! Out,    return value if numeric type
      CHARACTER*1
     .    retstr(*)     ! Out,    return string if byte type
C
C'Functions
      INTEGER   DVGETCDB
C
C'Local_variables
      INTEGER*4
     .    offp
     .,   dynp
     .,   dims
     .,   offset
     .,   rstat
     .,   x, i4tmp
      INTEGER*2
     .    a /1/
     .,   i2tmp(2)
      LOGICAL*1
     .    b
      EQUIVALENCE
     .   (i4tmp, i2tmp)
C'
      IF( ptr .LE. 65536 )THEN
        offp = ptr
      ELSE
        dynp = ptr - 65536
C'dbg   WRITE(6,'(A,I4,A/(6X,12I6,2X))')                                !'dbg
C'dbg.    ' ___dynblk',dynp,':', (dynblk(dynp+x),x=0,dynblk(dynp)-1)
        offp = dynblk(dynp + 2)
        i2tmp(1) = dynblk(dynp+4)
        i2tmp(2) = dynblk(dynp+5)
        offset = i4tmp
        dims = dynblk(dynp + 6)
        dynp = dynp + 8
        DO x = 1, dims
          IF( dynblk(dynp+1) .NE. 9 )THEN
            rstat = DVGETCDB(offblk(1,dynblk(dynp+2)), i4tmp, a, b)
C'dbg       WRITE(6,'(A,I4,A,4Z10.8,I9)')                               !'dbg
C'dbg.          ' ____offblk',dynblk(dynp+2),':',
C'dbg.          (offblk(rstat,dynblk(dynp+2)),rstat=1,4),i4tmp
          ELSE
            i2tmp(1) = dynblk(dynp+2)
            i2tmp(2) = dynblk(dynp+3)
          ENDIF
          offset = offset + ((i4tmp-1) * dynblk(dynp))
          dynp = dynp + 4
        ENDDO
        offblk(2,offp) = offset
      ENDIF
C
      rstat = DVGETCDB(offblk(1,offp), retval, strlen, retstr)
C'dbg WRITE(6,'(A,I4,A,4Z10.8,Z8)')                                     !'dbg
C'dbg.    ' ___offblk',offp,':',(offblk(x,offp),x=1,4),retval
C
      DVGETVAL = rstat
      RETURN
      END
C.......................................................................
C
      INTEGER FUNCTION DVGETCDB(offblk,retval,strlen,retstr)
C     -------------------------
      IMPLICIT NONE
C
C'Purpose
C     Makes one read from the CDB based on information in an offset block.
C
C'Parameters
      INTEGER*2
     .    strlen        ! Inp,    number of characters to be copied from CDB
      INTEGER*4
     .    offblk(4)     ! Inp,    offset block for CDB label
     .,   retval        ! Out,    return value if numeric type
      CHARACTER*1
     .,   retstr(*)     ! Out,    return string if byte type
C'
CP    USD8  YXSTRTXRF(*), YXENDXRF(*), X9STATUS
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 21-Aug-2019 19:30:21 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      INTEGER*4
     &  X9STATUS(50)   ! STATUS CODES
C$
      LOGICAL*1
     &  YXENDXRF0      !      end of base 0
     &, YXSTRTXRF      ! Start of CDB
     &, YXSTRTXRF0     ! Start of Base 0
C$
      LOGICAL*1
     &  DUM0000001(128518),DUM0000002(207053)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,YXSTRTXRF0,DUM0000001,X9STATUS,DUM0000002,YXENDXRF0 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF1      !      end of Base 1
     &, YXSTRTXRF1     ! Start of Base 1
C$
      LOGICAL*1
     &  DUM0100001(16787)
C$
      COMMON   /XRFTEST1  /
     &  YXSTRTXRF1,DUM0100001,YXENDXRF1 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF       ! End of CDB
     &, YXENDXRF2      ! End of Base 2
     &, YXSTRTXRF2     ! Start of Base 2
C$
      LOGICAL*1
     &  DUM0200001(65124)
C$
      COMMON   /XRFTEST2  /
     &  YXSTRTXRF2,DUM0200001,YXENDXRF2,YXENDXRF  
C------------------------------------------------------------------------------
      REAL*8
     .          r8xrf(0:1)
      REAL*4
     .          r4xrf(0:1)
     .,         r4val
      INTEGER*4
     .          i4xrf(0:1)
     .,         strtxrf(0:7)
     .,         offset
     .,         i4val
     .,         x
      INTEGER*2
     .          i2xrf(0:1)
      CHARACTER*1
     .          chxrf(0:1)
      LOGICAL*1
     .          fpass /.TRUE./
      EQUIVALENCE
     .         (YXSTRTXRF, r8xrf)
     .,        (YXSTRTXRF, r4xrf)
     .,        (YXSTRTXRF, i4xrf)
     .,        (YXSTRTXRF, i2xrf)
     .,        (YXSTRTXRF, chxrf)
     .,        (i4val,     r4val)
C=======================================================================
C
      IF( fpass )THEN
CVAX
CVAX    strtxrf(0) = %LOC(YXSTRTXRF)
CVAX    strtxrf(1) = %LOC(YXSTRTXRF1) - strtxrf(0)
CVAX    strtxrf(2) = %LOC(YXSTRTXRF2) - strtxrf(0)
CVAXEND
CIBM
        strtxrf(0) =  LOC(YXSTRTXRF)
        strtxrf(1) =  LOC(YXSTRTXRF1) - strtxrf(0)
        strtxrf(2) =  LOC(YXSTRTXRF2) - strtxrf(0)
CIBMEND
CSGI
CSGI    strtxrf(0) = %LOC(YXSTRTXRF)
CSGI    strtxrf(1) = %LOC(YXSTRTXRF1) - strtxrf(0)
CSGI    strtxrf(2) = %LOC(YXSTRTXRF2) - strtxrf(0)
CSGIEND
        strtxrf(0) = 0
        fpass = .FALSE.
      ENDIF
C
      DVGETCDB = 1
      offset = strtxrf(offblk(1)) + offblk(2)
C
      GOTO(        5,     ! L*1
     .             1,     ! L*2
     .             2,     ! L*4
     .             1,     ! I*2
     .             2,     ! I*4
     .            99,     ! I*8
     .             3,     ! R*4
     .             4,     ! R*8
     .            99      ! C*8
     .) offblk(4)
C
  99  CONTINUE
        DVGETCDB = -1
        retval = 0
        GOTO 999
C
   1  CONTINUE
        i4val = i2xrf(offset/2)
        GOTO 999
C
   2  CONTINUE
        i4val = i4xrf(offset/4)
        GOTO 999
C
   3  CONTINUE
        r4val = r4xrf(offset/4)
        GOTO 999
C
   4  CONTINUE
        r4val = r8xrf(offset/8)
        GOTO 999
C
   5  CONTINUE
      DO x = 0, (strlen-1)
        retstr(x+1) = chxrf(x+offset)
      ENDDO
      i4val = ICHAR(retstr(1))
      GOTO 999
C
 999  CONTINUE
      retval = i4val
      RETURN
      END
C.......................................................................
C
      INTEGER FUNCTION DVREADNUM(phrl,pptr,rset,typ,val,wid,dec,opt)
C     ------------------------
      IMPLICIT NONE
C'Purpose
C     To generate a list of phrases that will produce a numeric
C     readout of a given numeric value.  Note, the maximum value
C     that can be readout is 999 999.
C
C'Parameters
      INTEGER*4
     .    val       ! Inp,    value for readout
      INTEGER*2
     .    phrl(*)   ! Out,    buffer for phrase numbers
     .,   pptr      ! In/Out, start/next position in phrl
     .,   rset      ! Inp,    offset to select set of recordings
     .,   typ       ! Inp,    value type (I*4,R*4)
     .,   wid       ! Inp,    max number of phrases to be generated
     .,   dec       ! Inp,    number of digits after "decimal"
     .,   opt       ! In,     leading zeros or full readout
C
C'Local_variables
      INTEGER*4
     .          x
     .,         stack(32)
     .,         num, dig, pre
     .,         i4val
      REAL*4
     .          r4val
      INTEGER*2
     .          LDGZ /64/
     .,         SALL /2/
     .,         HHMM /512/
      LOGICAL*1
     .          minus
     .,         leadingzeros
     .,         digits
      EQUIVALENCE
     .         (i4val, r4val)
C=======================================================================
C
C --               Convert value to a positive integer, scaled to
C                  include digits right of decimal point.  Set minus
C                  flag if value was negative.
C
      DVREADNUM = 1
      leadingzeros = IAND(opt,LDGZ) .NE. 0
      digits = IAND(opt,SALL) .EQ. 0
C
      i4val = val
      IF( typ .LE. 2 )THEN
        num = i4val
      ELSE IF( IAND(opt,HHMM) .EQ. 0 )THEN
        num = INT(r4val*(10**dec))
      ELSE
        leadingzeros = .TRUE.          ! time readout
        digits = .TRUE.                ! convert secs to HHMM
        dec = 0
        wid = 4
        num = r4val / 3600
        num = (num * 100) + ((r4val - (num*3600)) / 60)
      ENDIF
      minus = .FALSE.
      IF( num .LT. 0 )THEN
        minus = .TRUE.
        num = -num
      ELSE IF( num .EQ. 0 )THEN
        pptr = pptr + 1
        phrl(pptr) = 30 + rset
        RETURN
      ENDIF
C
C --               Remove digits right to left, placing them on a
C                  stack.  First get digits right of decimal point.
C
      x = 0
      DO WHILE( x .LT. dec )
        x = x + 1
        stack(x) = MOD(num,10) + 30
        num = num / 10
      ENDDO
      stack(x) = stack(x) + 30
      IF( x .GT. 0 )THEN
        x = x + 1
        stack(x) = 58                      ! say "DECIMAL"
      ENDIF
C
C --               Get "units" if within format width.  If digit is
C                  zero then check for leading zero option.
C
      IF( x .LT. wid )THEN
        dig = MOD(num,10)
        num = num / 10
        IF( num .LE. 0 )THEN
          x = x + 1
          stack(x) = 60 + dig
        ELSE IF( dig .GT. 0 .OR. digits )THEN
          x = x + 1
          stack(x) = 60 + dig
        ENDIF
      ENDIF
C
C --               Get "tens" if within format width.  Depending on
C                  option, add to stack, zero, digit, "tens number",
C                  or combine with "unit" number to make "-teens".
C
      IF( x .LT. wid )THEN
        IF( num .LE. 0 )THEN
          IF( leadingzeros )THEN
            x = x + 1
            stack(x) = 30
          ENDIF
        ELSE
          pre = dig
          dig = MOD(num,10)
          num = num / 10
          IF( digits )THEN
            x = x + 1
            stack(x) = 30 + dig
          ELSE IF( dig .EQ. 1 )THEN
            IF( pre .GT. 0 )THEN
              stack(x) = stack(x) - 20
            ELSE
              x = x + 1
              stack(x) = 40
            ENDIF
          ELSE IF( dig .GT. 0 )THEN
            x = x + 1
            stack(x) = 48 + dig
          ENDIF
        ENDIF
      ENDIF
C
C --               Get "hundreds" if within format width.  Depending
C                  on option, add to stack, zero, digit, and possibly
C                  the word "hundred".
C
      IF( x .LT. wid )THEN
        IF( num .LE. 0 )THEN
          IF( leadingzeros )THEN
            x = x + 1
            stack(x) = 60
          ENDIF
        ELSE
          dig = MOD(num,10)
          num = num / 10
          IF( digits )THEN
            x = x + 1
            stack(x) = 60 + dig
          ELSE IF( dig .GT. 0 )THEN
            x = x + 1
            stack(x) = 71              ! say "HUNDRED"
            x = x + 1
            stack(x) = 60 + dig
          ENDIF
        ENDIF
      ENDIF
C
C --               Get "thousands" if within format width.  Depending
C                  on option, add to stack, zero, digit, and possibly
C                  the word "thousand".
C
      IF( x .LT. wid )THEN
        IF( num .LE. 0 )THEN
          IF( leadingzeros )THEN
            x = x + 1
            stack(x) = 30
          ENDIF
        ELSE
          dig = MOD(num,10)
          num = num / 10
          IF( digits )THEN
            x = x + 1
            stack(x) = 30 + dig
          ELSE
            x = x + 1
            stack(x) = 72              ! say "THOUSAND"
            IF( dig .GT. 0 )THEN
              x = x + 1
              stack(x) = 30 + dig
            ENDIF
          ENDIF
        ENDIF
      ENDIF
C
C --               Get "ten-thousands" if within format width.  Depending
C                  on option, add to stack, zero, digit, "tens number",
C                  or combine with "thousands" number to make "-teens".
C
      IF( x .LT. wid )THEN
        IF( num .LE. 0 )THEN
          IF( leadingzeros )THEN
            x = x + 1
            stack(x) = 60
          ENDIF
        ELSE
          pre = dig
          dig = MOD(num,10)
          num = num / 10
          IF( digits )THEN
            x = x + 1
            stack(x) = 60 + dig
          ELSE IF( dig .EQ. 1 )THEN
            IF( pre .GT. 0 )THEN
              stack(x) = stack(x) + 10
            ELSE
              x = x + 1
              stack(x) = 40
            ENDIF
          ELSE IF( dig .GT. 0 )THEN
            x = x + 1
            stack(x) = 48 + dig
          ENDIF
        ENDIF
      ENDIF
C
C --               Get "hundred-thousands" if within format width...
C
      IF( x .LT. wid )THEN
        IF( num .LE. 0 )THEN
          IF( leadingzeros )THEN
            x = x + 1
            stack(x) = 60
          ENDIF
        ELSE
          dig = MOD(num,10)
          IF( .NOT.digits )THEN
            x = x + 1
            stack(x) = 71              ! say "HUNDRED"
          ENDIF
          x = x + 1
          stack(x) = 60 + dig
        ENDIF
      ENDIF
C
C --               If necessary, stack the word "minus"
C
      IF( minus )THEN
        x = x + 1
        stack(x) = 59                  ! say "MINUS"
      ENDIF
C
C --               Unstack the readout into the phrase list
C
      DO x = (x), 1, -1
        pptr = pptr + 1
        phrl(pptr) = stack(x) + rset
      ENDDO
C
      RETURN
      END
C.......................................................................
C
      INTEGER FUNCTION DVREADSTR(phrl,pptr,rset,str,wid,opt)
C     ------------------------
      IMPLICIT NONE
C'Purpose
C     To generate a list of phrases that will produce an alpha-numeric
C     readout of a given character string
C
C'Parameters
      INTEGER*2
     .    phrl(*)   ! Out,    buffer for phrase numbers
     .,   pptr      ! In/Out, start/next position in phrl
     .,   rset      ! Inp,    offset to select set of recordings
     .,   wid       ! Inp,    length of string
     .,   opt       ! In,     options: runway
      CHARACTER*1
     .    str(*)    ! Inp,    string for readout
C
C'Local_variables
      INTEGER*4
     .    x
     .,   ALPHA     /-64/     ! offset to "ALPHA" phrase from char "A"
     .,   ZERO      /-18/     ! offset to "ZERO" phrase from char "0"
      INTEGER*2
     .    RUNWAY    /1/
C=======================================================================
C
      DVREADSTR = 1
      IF( IAND(opt,RUNWAY) .NE. 0 )THEN             ! runway readout
        IF( str(1) .NE. ' ' )THEN
          pptr = pptr + 1
          phrl(pptr) = ICHAR(str(1)) + ZERO + rset
        ENDIF
        IF( str(2) .NE. ' ' )THEN
          pptr = pptr + 1
          phrl(pptr) = ICHAR(str(2)) + ZERO + rset + 30
        ENDIF
        IF( str(3) .NE. ' ' )THEN
          pptr = pptr + 1
          IF( str(3) .EQ. 'L' )THEN
            phrl(pptr) = 112 + rset
          ELSE IF( str(3) .EQ. 'R' )THEN
            phrl(pptr) = 113 + rset
          ELSE IF( str(3) .EQ. 'A' )THEN         !say ALPHA for KLGA 22A
            phrl(pptr) = 1 + rset
          ELSE
            phrl(pptr) = 114 + rset
          ENDIF
        ENDIF
      ELSE
        DO x = 1, wid
          IF( str(x).GE.'A' .AND. str(x).LE.'Z' )THEN
            pptr = pptr + 1
            phrl(pptr) = ICHAR(str(x)) + ALPHA + rset
          ELSE IF( str(x).GE.'0' .AND. str(x).LE.'9' )THEN
            pptr = pptr + 1
            phrl(pptr) = ICHAR(str(x)) + ZERO + rset
          ENDIF
        ENDDO
      ENDIF
      RETURN
      END
C.......................................................................
C
      INTEGER FUNCTION DVINIT*4(fname)
C     -------------------------
C'Purpose
C     To initialize program variables from DV$MSGDAT.
C     Or to save directories and close DV$MSGDAT
C'
      IMPLICIT NONE
C
C'Functions
CVAX
CVAX  EXTERNAL DVAST01
CVAXEND
CIBM
      INTEGER*4
     .          cae_cvfs
     .,         LG ! cae string length function
CIBMEND
CSGI
CSGI  INTEGER*4
CSGI .          cae_cvfs
CSGI .,         LG ! cae string length function
CSGIEND
C
C'Include_files
      INCLUDE 'usd8x9pl.inc'           !NOFPC
CVAX
CVAX  INCLUDE '($IODEF)/NOLIST'        !NOFPC
CVAX  INCLUDE '($SSDEF)/NOLIST'        !NOFPC
CVAXEND
CIBM
      INCLUDE 'cae_io.inc'             !NOFPC
CIBMEND
CSGI
CSGI -start cae_io.inc-------------------------------------------------   \C ---
CSGI                                                                      \C
CSGI  structure /io_info_struct /
CSGI    integer fd
CSGI    integer record_size
CSGI    integer status
CSGI    integer private
CSGI    integer bufferp
CSGI    integer file_namep
CSGI    integer o_flag
CSGI    integer record_num
CSGI    integer amount
CSGI    integer position
CSGI  end structure
CSGI                                                                      \C
CSGI -end cae_io.inc---------------------------------------------------   \C ---
CSGI  RECORD  /io_info_struct/ msgdat
CSGIEND
C
C'Local_data
      INTEGER*4
     .          access
     .,         buffadd
     .,         recnum
     .,         bytecnt
     .,         offset
     .,         maxpos
     .,         fcnstat
     .,         rstat
     .,         istate
     .,         indx
     .,         unr
      CHARACTER
     .          version*5
     .,         tmpname*64
     .,         fname*(*)
      LOGICAL*4
     .          io_end
      LOGICAL*1
     .          ifpass /.TRUE./
C=======================================================================
C
      IF( ifpass )THEN
        io_end = .TRUE.
        rstat = 1
        access = 0
        istate = 1
        ifpass = .FALSE.
      ENDIF
      DVINIT = 0
C
 1    CONTINUE
      IF( .NOT.io_end )THEN
CVAX
CVAX    RETURN
CVAXEND
CIBM
        IF( rstat .EQ. 0 )THEN
          RETURN
        ELSE
          io_end = .TRUE.
        ENDIF
CIBMEND
CSGI
CSGI    IF( msgdat.status .EQ. 999 )THEN
CSGI      RETURN
CSGI    ELSE
CSGI      io_end = .TRUE.
CSGI      rstat = msgdat.status
CSGI    ENDIF
CSGIEND
      ENDIF
C
      GOTO (10, 20, 30, 40, 50, 60) istate
      IF( istate .LT. 0 )THEN
        DVINIT = istate
        ifpass = .TRUE.
      ELSE IF( istate .EQ. 0 )THEN
        DVINIT = 1
        Datunr = unr
        ifpass = .TRUE.
      ENDIF
      RETURN
C
C----------------------------
C -- INITIAL STATE 1: Open data file DV$MSGDAT
C----------------------------
C
 10   CONTINUE
      IF( access .EQ. 0 )THEN
CVAX
CVAX    access = 2
CVAX    bytecnt = 256
CVAX    maxpos = 1024
CVAX    fcnstat = 512
CVAX    IF( fname(1:1) .LE. ' ' ) fname = 'DV$MSGDAT'
CVAX    IF( Datunr .LE. 0 ) unr = 4
CVAX    CALL BUFF_OPEN( unr,     fname,
CVAX .                  bytecnt, access, maxpos,
CVAX .                  version, rstat,  io_end,
CVAX .                  DVAST01, fcnstat )
CVAX    access = 6144
CVAXEND
CIBM
        access = 1
        rstat = cae_cvfs('DV$MSGDAT',tmpname)
        IF( rstat .LT. 1 )THEN
C'dbg     WRITE(6,*) '%X9PLAY-INIT, unable to translate ',fname,rstat
          istate = -1
        ELSE
          CALL REV_CURR(tmpname, fname, 'dat', 0, access, rstat)
          indx = 1 + LG(fname)
          fname(indx:indx) = '\0'
          fcnstat = 0
          rstat = 0
          call cae_io_open( fcnstat, rstat, unr,
     .                      %VAL(256), fname, %VAL(7) )
          IF( fcnstat .EQ. 1 )THEN
            io_end = .FALSE.
          ELSE
C'dbg       WRITE(6,*) '%X9PL-INIT, cae_io_open failed',fcnstat
            istate = -1
          ENDIF
          access = 16384
        ENDIF
CIBMEND
CSGI
CSGI    access = 1
CSGI    rstat = cae_cvfs('DV$MSGDAT',tmpname)
CSGI    IF( rstat .LT. 1 )THEN
CSGI      WRITE(6,*) '%X9PLAY-INIT, unable to translate ',tmpname,rstat
CSGI      istate = -1
CSGI    ELSE
CSGI      CALL REV_CURR(tmpname, fname, 'dat', .FALSE., access, rstat)
CSGI      rstat = LG(fname) + 1
CSGI      fname(rstat:rstat) = '\0'
CSGI      msgdat.file_namep  = %LOC(fname)
CSGI      msgdat.o_flag      = 5 ! read only, old file
CSGI      msgdat.record_size = 256
CSGI      msgdat.status      = 999
CSGI      CALL cae_io_open( msgdat, 12 )
CSGI      rstat = 0
CSGI      io_end = .FALSE.
CSGI      access = 16384
CSGI    ENDIF
CSGIEND
      ELSE IF( rstat .NE. 1 )THEN
C'dbg   WRITE(6,*) '%X9PL-INIT, unable to open DV$MSGDAT ',rstat
        istate = -1
C
      ELSE
CSGI
CSGI    unr = msgdat.fd
CSGIEND
        istate = 2
        indx = 0
      ENDIF
      GOTO 1
C
C----------------------------
C -- INITIAL STATE 2: Read file header
C----------------------------
C
 20   CONTINUE
      IF( indx .EQ. 0 )THEN
        indx = 1
        bytecnt = 256
CVAX
CVAX    recnum = 1
CVAX    offset = 1
CVAX    CALL BUFF_READ( unr,     %DESCR(Hdr_Buf),
CVAX .                  recnum,  offset,  bytecnt,
CVAX .                  rstat,   io_end,  DVAST01 )
CVAXEND
CIBM
        recnum = 0
        offset = 0
        rstat = 0
        call cae_io_read( fcnstat, rstat, %VAL(unr),
     .                    %VAL(256), Hdr_Buf(1),
     .                    recnum, bytecnt, offset )
        IF( fcnstat .EQ. 1 )THEN
          io_end = .FALSE.
        ELSE
C'dbg     WRITE(6,*) '%X9PL-INIT2, cae_io_read failed',fcnstat
          istate = -2
        ENDIF
CIBMEND
CSGI
CSGI    msgdat.bufferp    = %LOC(Hdr_Buf(1))
CSGI    msgdat.record_num = 0
CSGI    msgdat.position   = 0
CSGI    msgdat.amount     = bytecnt
CSGI    msgdat.status     = 999
CSGI    CALL cae_io_read( msgdat, 12 )
CSGI    rstat = 0
CSGI    io_end = .FALSE.
CSGIEND
      ELSE IF ( rstat .NE. 1 ) THEN
C'dbg   WRITE(6,*) '%X9PL-INIT, unable to read file header ',rstat
        istate = -2
      ELSE
        istate = 3
        indx = 0
C'dbg   IF( Hdr_Ident .NE. DV_Ident ) WRITE(6,'(A,I4,A,I4,A)')
C'dbg.      ' %X9PL-INIT, Warning: header ident (',Hdr_Ident,
C'dbg.      ') not equal to software ident (',DV_Ident,')'
      ENDIF
      GOTO 1
C
C----------------------------
C -- INITIAL STATE 3: Read phrase directory
C----------------------------
C
 30   CONTINUE
      IF( indx .EQ. 0 )THEN
        indx = 1
        bytecnt = access
        maxpos = 16 * Hdr_MaxPhr       ! 16 bytes per phr dir entry
      ELSE IF( rstat .NE. 1 )THEN
C'dbg   WRITE(6,*) '%X9PL-INIT, unable to read phrase directory ',
C'dbg.              rstat
        indx = 1 + maxpos
        istate = -3
      ENDIF
C
      IF( indx .LE. maxpos )THEN
        recnum = Hdr_PdirRec
        offset = indx
        IF( bytecnt .GT. maxpos-indx ) bytecnt = maxpos-indx+1
        indx = indx + bytecnt
CVAX
CVAX    CALL BUFF_READ( unr,     %DESCR(Phr_Buf(offset)),
CVAX .                  recnum,  offset,   bytecnt,
CVAX .                  rstat,   io_end,   DVAST01 )
CVAXEND
CIBM
        rstat = 0
        call cae_io_read( fcnstat, rstat, %VAL(unr),
     .                    %VAL(256), Phr_Buf(offset),
     .                    recnum-1, bytecnt, offset-1 )
        IF( fcnstat .EQ. 1 )THEN
          io_end = .FALSE.
        ELSE
C'dbg     WRITE(6,*) '%X9PL-INIT3, cae_io_read failed',fcnstat
          istate = -3
        ENDIF
CIBMEND
CSGI
CSGI    msgdat.bufferp    = %LOC(Phr_Buf(offset))
CSGI    msgdat.record_num = recnum - 1
CSGI    msgdat.position   = offset - 1
CSGI    msgdat.amount     = bytecnt
CSGI    CALL cae_io_read( msgdat, 12 )
CSGI    rstat = 0
CSGI    io_end = .FALSE.
CSGIEND
      ELSE IF( rstat .EQ. 1 )THEN
        istate = 4
        indx = 0
      ENDIF
      GOTO 1
C
C----------------------------
C -- INITIAL STATE 4: Read phrase xrf
C----------------------------
C
 40   CONTINUE
      IF( indx .EQ. 0 )THEN
        indx = 1
        bytecnt = access
        maxpos = Hdr_MaxPcat * Hdr_MaxPhr        ! phr xrf size
      ELSE IF( rstat .NE. 1 )THEN
C'dbg   WRITE(6,*) '%X9PL-INIT, unable to read phrase xrf ',rstat
        indx = 1 + maxpos
        istate = -4
      ENDIF
C
      IF( indx .LE. maxpos )THEN
        recnum = Hdr_PxrfRec
        offset = indx
        IF( bytecnt .GT. maxpos-indx ) bytecnt = maxpos-indx+1
        indx = indx + bytecnt
CVAX
CVAX    CALL BUFF_READ( unr,     %DESCR(Pxrf_Buf(offset)),
CVAX .                  recnum,  offset,  bytecnt,
CVAX .                  rstat,   io_end,  DVAST01 )
CVAXEND
CIBM
        rstat = 0
        call cae_io_read( fcnstat, rstat, %VAL(unr),
     .                    %VAL(256), Pxrf(1,offset),
     .                    recnum-1, bytecnt, offset-1 )
        IF( fcnstat .EQ. 1 )THEN
          io_end = .FALSE.
        ELSE
C'dbg     WRITE(6,*) '%X9PL-INIT4, cae_io_read failed',fcnstat
          istate = -4
        ENDIF
CIBMEND
CSGI
CSGI    msgdat.bufferp    = %LOC(Pxrf(1,offset))
CSGI    msgdat.record_num = recnum - 1
CSGI    msgdat.position   = offset - 1
CSGI    msgdat.amount     = bytecnt
CSGI    CALL cae_io_read( msgdat, 12 )
CSGI    rstat = 0
CSGI    io_end = .FALSE.
CSGIEND
      ELSE IF( rstat .EQ. 1 )THEN
        istate = 5
        indx = 0
      ENDIF
      GOTO 1
C
C----------------------------
C -- INITIAL STATE 5: Read message directory
C----------------------------
C
 50   CONTINUE
      IF( indx .EQ. 0 )THEN
        indx = 1
        bytecnt = access
        maxpos = 16 * Hdr_MaxMsg       ! 16 bytes per phr dir entry
      ELSE IF( rstat .NE. 1 )THEN
C'dbg   WRITE(6,*) '%X9PL-INIT, unable to read msg xrf ',rstat
        indx = 1 + maxpos
        istate = -5
      ENDIF
C
      IF( indx .LE. maxpos )THEN
        recnum = Hdr_MdirRec
        offset = indx
        IF( bytecnt .GT. maxpos-indx ) bytecnt = maxpos-indx+1
        indx = indx + bytecnt
CVAX
CVAX    CALL BUFF_READ( unr,
CVAX .                  %DESCR(Msg_Buf(offset)),
CVAX .                  recnum,  offset,  bytecnt,
CVAX .                  rstat,   io_end,  DVAST01 )
CVAXEND
CIBM
        rstat = 0
        call cae_io_read( fcnstat, rstat, %VAL(unr),
     .                    %VAL(256), Msg_Buf(offset),
     .                    recnum-1, bytecnt, offset-1 )
        IF( fcnstat .EQ. 1 )THEN
          io_end = .FALSE.
        ELSE
C'dbg     WRITE(6,*) '%X9PL-INIT5, cae_io_read failed',fcnstat
          istate = -5
        ENDIF
CIBMEND
CSGI
CSGI    msgdat.bufferp    = %LOC(Msg_Buf(offset))
CSGI    msgdat.record_num = recnum - 1
CSGI    msgdat.position   = offset - 1
CSGI    msgdat.amount     = bytecnt
CSGI    CALL cae_io_read( msgdat, 12 )
CSGI    rstat = 0
CSGI    io_end = .FALSE.
CSGIEND
      ELSE
        istate = 6
        indx = 0
      ENDIF
      GOTO 1
C
C----------------------------
C -- INITIAL STATE 6: Read message xrf
C----------------------------
C
 60   CONTINUE
      IF( indx .EQ. 0 )THEN
        indx = 1
        bytecnt = access
        maxpos = Hdr_MaxMcat * Hdr_MaxMsg        ! msg xrf size
      ELSE IF( rstat .NE. 1 )THEN
C'dbg   WRITE(6,*) '%X9PL-INIT, unable to read msg xrf ',rstat
        indx = 1 + maxpos
        istate = -6
      ENDIF
C
      IF( indx .LE. maxpos )THEN
        recnum = Hdr_MxrfRec
        offset = indx
        IF( bytecnt .GT. maxpos-indx ) bytecnt = maxpos-indx+1
        indx = indx + bytecnt
CVAX
CVAX    CALL BUFF_READ( unr,     %DESCR(Mxrf_Buf(offset)),
CVAX .                  recnum,  offset,  bytecnt,
CVAX .                  rstat,   io_end,  DVAST01 )
CVAXEND
CIBM
        rstat = 0
        call cae_io_read( fcnstat, rstat, %VAL(unr),
     .                    %VAL(256), Mxrf(1,offset),
     .                    recnum-1, bytecnt, offset-1 )
        IF( fcnstat .EQ. 1 )THEN
          io_end = .FALSE.
        ELSE
C'dbg     WRITE(6,*) '%X9PL-INIT6, cae_io_read failed',fcnstat
          istate = -6
        ENDIF
CIBMEND
CSGI
CSGI    msgdat.bufferp    = %LOC(Mxrf(1,offset))
CSGI    msgdat.record_num = recnum - 1
CSGI    msgdat.position   = offset - 1
CSGI    msgdat.amount     = bytecnt
CSGI    CALL cae_io_read( msgdat, 12 )
CSGI    rstat = 0
CSGI    io_end = .FALSE.
CSGIEND
      ELSE IF( rstat .EQ. 1 )THEN
        istate = 0
        indx = 0
      ENDIF
      GOTO 1
C
      END
C.......................................................................
C
      INTEGER FUNCTION DVTEXT*4( state, first, last, list,
     .                           maxl, text, actl )
C     -------------------------
C'Purpose
C     To read phrase descriptions from the messages databas
C'
      IMPLICIT NONE
C
C'Inputs
      INTEGER*2
     .    state         ! current processing state
     .,   first         ! first nr in range or zero if list used
     .,   last          ! nr of phrases descriptions to read
     .,   list(*)       ! list of phrase numbers
     .,   maxl          ! size of receiving text field
C
C'Outputs
      CHARACTER
     .    text(*)*(*)   ! phrase descriptions read
      INTEGER*2
     .    actl(*)       ! actual text string length
C
C     DVTEXT returns the next processing state and a status code.
C     status = 0 : processing in progress
C     status > 0 : processing completed properly
C     status < 0 : processing stopped due to error
C
C'Functions
CVAX
CVAX  EXTERNAL DVAST01
CVAXEND
C
C'Include_files
      INCLUDE 'usd8x9pl.inc'           !NOFPC
CVAX
CVAX  INCLUDE '($IODEF)/NOLIST'        !NOFPC
CVAX  INCLUDE '($SSDEF)/NOLIST'        !NOFPC
CVAXEND
CIBM
      INCLUDE 'cae_io.inc'             !NOFPC
CIBMEND
CSGI
CSGI -start cae_io.inc-------------------------------------------------   \C ---
CSGI                                                                      \C
CSGI  structure /io_info_struct /
CSGI    integer fd
CSGI    integer record_size
CSGI    integer status
CSGI    integer private
CSGI    integer bufferp
CSGI    integer file_namep
CSGI    integer o_flag
CSGI    integer record_num
CSGI    integer amount
CSGI    integer position
CSGI  end structure
CSGI                                                                      \C
CSGI -end cae_io.inc---------------------------------------------------   \C ---
CSGI  RECORD  /io_info_struct/ msgdat
CSGIEND
C
C'Local_data
      INTEGER*4
     .          recnum
     .,         bytecnt
     .,         offset
     .,         fcnstat
     .,         rstat
     .,         textx
     .,         phrx
      LOGICAL*4
     .          io_end
      LOGICAL*1
     .          cpylen
     .,         range
C=======================================================================
C
      IF( Datunr .LE. 0 )THEN
        DVTEXT = -91
        RETURN
      ENDIF
C
C --- Initial state:  Setup ptrs, counters, etc.
C
      IF( state .EQ. 0 )THEN
        state = 1
        IF( maxl .GT. 0 )THEN
          cpylen = .FALSE.
          bytecnt= maxl
        ELSE IF( maxl .LT. 0 )THEN
          cpylen = .TRUE.
          bytecnt= -maxl
        ELSE
          cpylen = .FALSE.
          bytecnt= 64
        ENDIF
C
        textx = 1
        IF( first .LE. 0 )THEN
          range = .FALSE.
          phrx = list(1)
        ELSE
          range = .TRUE.
          phrx = first
        ENDIF
      ENDIF
C
C
C --- State 1:  Queue request to read text.
C
      DVTEXT = 0
      IF( state .EQ. 1 )THEN
        state = 2
        IF( PhrI2(D_TXTLEN, phrx) .LE. 0 )THEN
          text(textx) = ' '
          rstat = 1
          io_end = .TRUE.
        ELSE
          recnum = Hdr_PtxtRec
          offset = PhrI4(D_TXTPTR, phrx)
CVAX
CVAX      CALL BUFF_READ( Datunr,  %DESCR(text(textx)),
CVAX .                    recnum,  offset,  bytecnt,
CVAX .                    rstat,   io_end,  DVAST01 )
CVAXEND
CIBM
          rstat = 0
          call cae_io_read( fcnstat, rstat, %VAL(Datunr),
     .                      %VAL(256), text(textx),
     .                      recnum-1, bytecnt, offset-1 )
          IF( fcnstat .EQ. 1 )THEN
            io_end = .FALSE.
          ELSE
            DVTEXT = -1
            state = fcnstat
          ENDIF
CIBMEND
CSGI
CSGI      msgdat.bufferp    = %LOC(text(textx))
CSGI      msgdat.record_num = recnum - 1
CSGI      msgdat.position   = offset - 1
CSGI      msgdat.amount     = bytecnt
CSGI      CALL cae_io_read( msgdat, 12 )
CSGI      rstat = 0
CSGI      io_end = .FALSE.
CSGIEND
        ENDIF
C
C
C --- State 2:  Wait for read to complete; check completion status; and
c               either terminate process or setup for next read.
C
      ELSE IF( state .EQ. 2 )THEN
CIBM
        IF( rstat .NE. 0 ) io_end = .TRUE.
CIBMEND
CSGI
CSGI    IF( msgdat.status .NE. 999 )THEN
CSGI      io_end = .TRUE.
CSGI      rstat = msgdat.status
CSGI    ENDIF
CSGIEND
        IF( io_end .AND. (rstat .NE. 1) )THEN
          DVTEXT = -2
          state = rstat
        ELSE IF( io_end )THEN
          state = 1
          IF( cpylen ) actl(textx) = PhrI2(D_TXTLEN, phrx)
          textx = textx + 1
          IF( textx .GT. last )THEN
            state = 0
            DVTEXT = 1
          ELSE IF( range )THEN
            phrx = phrx + 1
          ELSE
            phrx = list(textx)
          ENDIF
        ENDIF
C
C --- Invalid state
C
      ELSE
        DVTEXT = -99
        state = 0
C
      ENDIF
      RETURN
      END
C.......................................................................
C     dummy buff i/o ast routine
CVAX
CVAX  SUBROUTINE DVAST01
CVAX  IMPLICIT NONE
CVAX  RETURN
CVAX  END
CVAXEND
