#!  /bin/csh -f
#!  $Revision: PGC_CMP - Apply the Page Compiler V1.2 Jul-91$
#!
#!  Version 1.0: <PERSON><PERSON>
#!     - Initial version of this script
#!  Version 1.1: <PERSON> (23-May-91)
#!     - removed reference to /cae/simex_plus/support
#!  Version 1.2: <PERSON><PERSON> (10-Jul-91)
#!     - added reference to cdb file in header
#!
set FSE_UNIK="`pid`.tmp.1"
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set FSE_TEMP=$SIMEX_DIR/work/pgct_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/pgcl_$FSE_UNIK
#
echo '&pgcinit.dat'  >$FSE_TEMP
echo '&$.cdb'       >>$FSE_TEMP
echo '@$.'          >>$FSE_TEMP
echo '&$*.xsl'      >>$FSE_TEMP
echo '@cdb_spare.'  >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
unalias pgcom
pgcom $*
rm $FSE_LIST
exit
