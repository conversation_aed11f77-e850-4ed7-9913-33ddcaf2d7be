* THIS FILE IDENTIFIES INTELLIGENT CARDS THAT REPORT STATUS TO CCU
*
DIAG_BEGIN
* 3   7         17  21   26   31    37  41   46   51  ! column
*DMC  CABINET   SLT PG   SEG  OFF   SIZ APP  CLK  SYS
  10  FC601A    01  0000 0000 0000  01  ARI  YES  A       !ARINC 429 IN
  10  FC601A    03  0000 0000 0000  01  ARI  YES  A       !ARINC 429 OUT
*
* START OF SOUND CHASSIS
* 
**** SLOT # 20 = TONE-1
*
  0E  F2A1      14  0001 A600 1600  03  SOU  NO   A
  0E  F2A1      14  0001 A600 1C80  01  SOU  NO   A
  0E  F2A1      14  0001 A600 1CC0  01  SOU  NO   A
  0E  F2A1      14  0001 A600 1D80  02  SOU  NO   A
  0E  F2A1      14  0001 A600 1D90  02  SOU  NO   A
  0E  F2A1      14  0001 A600 1E1E  09  SOU  NO   A
*
**** SLOT # 17 = IMPACT-1
*
  0E  F2A1      11  0001 A000 1600  03  SOU  NO   A
  0E  F2A1      11  0001 A000 1C80  01  SOU  NO   A
  0E  F2A1      11  0001 A000 1CC0  01  SOU  NO   A
  0E  F2A1      11  0001 A000 1D80  02  SOU  NO   A
  0E  F2A1      11  0001 A000 1D90  02  SOU  NO   A
  0E  F2A1      11  0001 A000 1E1E  09  SOU  NO   A
*
**** SLOT # 16 = NOISE-1
*
  0E  F2A1      10  0001 9E00 1600  03  SOU  NO   A
  0E  F2A1      10  0001 9E00 1C80  01  SOU  NO   A
  0E  F2A1      10  0001 9E00 1CC0  01  SOU  NO   A
  0E  F2A1      10  0001 9E00 1D80  02  SOU  NO   A
  0E  F2A1      10  0001 9E00 1D90  02  SOU  NO   A
  0E  F2A1      10  0001 9E00 1E1E  09  SOU  NO   A
*
**** SLOT # 14 = MIXER-1
*
  0E  F2A1      0E  0001 9A00 1600  03  SOU  NO   A
  0E  F2A1      0E  0001 9A00 1C80  01  SOU  NO   A
  0E  F2A1      0E  0001 9A00 1CC0  01  SOU  NO   A
  0E  F2A1      0E  0001 9A00 1D80  02  SOU  NO   A
  0E  F2A1      0E  0001 9A00 1D90  02  SOU  NO   A
  0E  F2A1      0E  0001 9A00 1E1E  09  SOU  NO   A
*
**** SLOT # 13 = MIXER-1
*
  0E  F2A1      0D  0001 9800 1600  03  SOU  NO   A
  0E  F2A1      0D  0001 9800 1C80  01  SOU  NO   A
  0E  F2A1      0D  0001 9800 1CC0  01  SOU  NO   A
  0E  F2A1      0D  0001 9800 1D80  02  SOU  NO   A
  0E  F2A1      0D  0001 9800 1D90  02  SOU  NO   A
  0E  F2A1      0D  0001 9800 1E1E  09  SOU  NO   A
*
**** SLOT # 12 = MIXER-1
*
  0E  F2A1      0C  0001 9600 1600  03  SOU  NO   A
  0E  F2A1      0C  0001 9600 1C80  01  SOU  NO   A
  0E  F2A1      0C  0001 9600 1CC0  01  SOU  NO   A
  0E  F2A1      0C  0001 9600 1D80  02  SOU  NO   A
  0E  F2A1      0C  0001 9600 1D90  02  SOU  NO   A
  0E  F2A1      0C  0001 9600 1E1E  09  SOU  NO   A
*
DIAG_END
*
:SOU:
1000 Foreground is not running
2000 TSD BUS error
3000 Board doesn't work properly
4000 TSD bus synchronization lost
5000 DAC board is defective
7000 TSD BUS error, check DMC board 
:END:
DIAG_END
