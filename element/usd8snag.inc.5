C'Title              Include File (Function Generation) For Sound Module
C'Module_ID          usd8snh
C'Entry_point        NASND
C'Documentation      SDD
C'Customer           USAIR
C'Application        Simulation of the DASH 8-100/300 Cockpit Acoustics
C'Author             <PERSON><PERSON>, <PERSON><PERSON>
C'Date               August 1991  , September 1991
C
C'System             Sound
C'Computer_type      IBM RISC 6000 Series
C'Iteration_rate     133 msec 
C'Process            SP0C0
C
C'Revision_History
C
C  usd8snag.inc.4  1Jul1992 12:30 usd8 m.ward 
C       < routine fg_map replaced with fgendebg for fgen debug >
C
C  usd8snag.inc.3 14Apr1992 15:30 usd8 M.WARD 
C       < REMOVED CALL TO FG_MAP (OSU NOT READY) >
C
C  usd8snag.inc.2  1Feb1992 09:13 usd8 Kaiser 
C       < Fixed header >
C
C
      IF( FSTLPASS ) THEN
C
C       Translate logical name
C       ----------------------
        TRNL_STATUS = CAE_TRNL('SND_BIN',FGLEN,FGENFILE,1)
        IF (TRNL_STATUS.NE.1) THEN
          FGENSKIP = .TRUE.
          WRITE(6,*)"CAE_TRNL function call was not successfully
     &               processed for sound."
          WRITE(6,*)"Status of function is: ", TRNL_STATUS
          GOTO 0010
        ENDIF
C
C       Generate a map section for the FGEN bin file
C       --------------------------------------------
C !FM+
C !FM   1-Jul-92 12:30:00 M.WARD
C !FM    < ADDED CODE FOR FGEN DEBUG >
C !FM
        IF (FGENDEBG) THEN
          CALL FGDBGMAP(FGENCORE,'SOUND',CSIZE)
        ENDIF
C !FM-
C
C       Load FGEN bin file
C       ------------------
        CALL FGLOAD(FGENFILE(1:FGLEN),FGENCORE(0),FGENSIZ3,FGL_STATUS)
        IF (FGL_STATUS.NE.1) THEN
          FGENSKIP = .TRUE.
          WRITE(6,*)"Subroutine FGLOAD was not successfully called
     &               for sound"
          WRITE(6,*)"Status of subroutine is: ", FGL_STATUS
          GOTO 0010
        ENDIF  
C
C       Initialize the FGEN database
C       ----------------------------
        FGENADDR = LOC(FGENCORE(0))
        FGI_STATUS = FGINIT(FGENADDR,NAFGZONE,NAFCLASS)
        IF (FGI_STATUS.NE.1) THEN
          FGENSKIP = .TRUE.
          WRITE(6,*)"Subroutine FGINIT was not successfully called
     &               for sound."
          WRITE(6,*)"Status of subroutine is: ", FGI_STATUS
        ENDIF
0010    CONTINUE
        FSTLPASS = .FALSE.
      ENDIF
C
      IF (.NOT.FGENSKIP) THEN
C
C       Perform interpolation
C       ---------------------
        FGG_STATUS = FGGO(FGENADDR)
        IF (FGG_STATUS.NE.1) THEN
          WRITE(6,*)"Subroutine FGGO was not successfully called
     &               for sound."
          WRITE(6,*)"Status of subroutine is: ", FGG_STATUS
        ENDIF
      ENDIF
