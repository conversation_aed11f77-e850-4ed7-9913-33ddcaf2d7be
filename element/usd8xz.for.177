C'Name                  I.F. Miscellaneous
C'Module_ID             USD8XZ
C'PDD_#
C'Customer              USAIR DASH-8
C'Author                <PERSON><PERSON>
C'Date                  Nov-1991
C'Origin                AW37
C'Application
C
C     This program takes care of miscellaneous processing required
C     by I/F.
C'
      SUBROUTINE USD8XZ
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.6 in Gould-to-IBM mode 04/02/91 - 11:47 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Revision_history
C
C  usd8xz.for.29 26Oct2023 20:25 usd8 Tom    
C       < made heading reposition per faa gripe >
C
C  usd8xz.for.28 17Jul2019 20:27 usd8 Tom
C       < Replaced BWI data with KSFO >
C
C  usd8xz.for.27  4Feb2019 20:00 usd8 Tom
C       < Changed moderate to .66 since the page is sending .66 >
C
C  usd8xz.for.26 26Aug2010 00:30 usd8 Tom
C       < Ad new runway to CLT runway table >
C
C  usd8xz.for.25  5Feb2009 03:41 usd8 Tom
C       < Fixed runway ID changes for PHL, DCA, GSO & CLT >
C
C  usd8xz.for.24 14Mar2007 04:33 usd8 Tom
C       < Swapped KLWB for KLGA >
C
C  usd8xz.for.23  1Mar2006 02:20 usd8 Tom
C       < Put in data for Roanoke >
C
C  usd8xz.for.22  8Jul1998 12:25 usd8 Tom
C       < Adjusted T/O min RVR per FAA gripe >
C
C  usd8xz.for.21 15Oct1996 01:22 usd8 JWM
C       < < COA S81-1-083 ERO2  Corrected map data table alignment > >
C
C  usd8xz.for.20 17Mar1996 01:27 usd8 JDH
C       < COA S81-1-113  Added logic to make G/S x 2 button flash >
C
C  usd8xz.for.19 14Mar1995 02:30 usd8 TOM M
C       < COA S81-1-094 >
C
C  usd8xz.for.18 15Nov1994 01:21 usd8 Tom M
C       < COA S81-1-085 Additional hazards pages >
C
C  usd8xz.for.17 20Dec1993 06:00 usd8 JDH
C       < COA S81-1-047  Added logic to reset AHFACC after time delay
C         and to add AHFACC to All Quantity reset >
C
C  usd8xz.for.16 14Jul1993 09:03 usd8 des
C       < modified the cat I II III cloud tops to 6000 ft. >
C
C  usd8xz.for.15 14Jul1993 08:55 usd8 des
C       < modified cat I II III cloud tops to 6000 ft. >
C
C  usd8xz.for.14 20Dec1992 21:30 usd8 M.WARD
C       < FIX FOR SPR 9013 - DISABLE REPOSITION DURING RAP >
C
C  usd8xz.for.13 16Dec1992 13:33 usd8 steve W
C       < added Stick pusher resevoit empty >
C
C  usd8xz.for.12 28Aug1992 17:13 usd8 M.WARD
C       < LABEL FOR GROUND ELECTRICS TCM0ELEC1 COMPUTED IN USD8AE.FOR, FIX
C         FOR GROUND POWER IN AIR >
C
C  usd8xz.for.11 27Aug1992 09:01 usd8 m.ward
C       < moved computation of old_0elec outside of if block >
C
C  usd8xz.for.10 13Jul1992 14:21 usd8 Baon
C       < High GW and AFT CG logic changed on warning popup >
C
C  usd8xz.for.9  9Jul1992 12:21 usd8 M.WARD
C       < LABEL RASADF REPLACED WITH RFRAUDIO >
C
C  usd8xz.for.8  7Jul1992 10:04 usd8 M.WARD
C       < I ADDED SOME LOGIC FOR THE MASTER CAUTION LIGHT. SEE FIELD
C         MARKER COMMENTS >
C
C  usd8xz.for.7 22Jun1992 11:13 usd8 tom
C       < set tcmautbr to TRUE on first pass. >
C
C  usd8xz.for.6 15Jun1992 11:33 usd8 M.WARD
C       < ADDED RESET FOR TCAS INTRUDER ON TOTAL RESET >
C
C  usd8xz.for.5 27Mar1992 15:53 usd8 M.WARD
C       < NEW LABEL FOR MAINTENANCE RESET AVAILABLE FOR ECS >
C
C  usd8xz.for.4 29Jan1992 20:02 usd8 Baon
C       < Initialize Crash inhibit to TRUE and Set Crash Inhibit on Total
C         reset >
C
C  usd8xz.for.3 23Jan1992 18:46 usd8 Baon
C       < Init hazard popup number to 341 >
C
C File: /cae1/ship/usd8xz.for.2
C       Modified by: exit
C       Mon Dec 16 22:48:47 1991
C       <
C
C File: /cae1/ship/usd8xz.for.14
C       Modified by: baon
C       Wed Nov 20 12:14:14 1991
C       < Setting gray concept flags of malfs to true in firstpass for
C         testing >
C
C File: /cae1/ship/usd8xz.for.2
C       Modified by: baon
C       Fri Nov 15 17:50:28 1991
C       < Changed SHIPID to USD8 from AW37 >
C
C File: /cae1/ship/aw37xz.for.21
C       Modified by: m.lambidonis
C       Tue Nov 12 21:31:18 1991
C       < created a delay between tcrtot and tcrsyst call to delaymulti
C         because  it was causing an overflow of the delay table. >
C
C File: /cae1/ship/aw37xz.for.37
C       Modified by: S. Hilal
C       Mon Sep 23 19:17:14 1991
C       < modified microburst scaling logic >
C
C File: /cae1/ship/aw37xz.for.33
C       Modified by: nt
C       Wed Sep 18 15:54:47 1991
C       < old values of ftl votemp = new values of if tatemp see va.for
C         module >
C
C File: /cae1/ship/aw37xz.for.31
C       Modified by: nt
C       Wed Sep 18 11:28:08 1991
C       < set tcmchatm to true for reset wind/temp in FIND_EC section >
C
C File: /cae1/ship/aw37xz.for.29
C       Modified by: nt
C       Tue Sep 17 18:51:17 1991
C       < logic to reset wind/temp in FIND_EC section >
C
C File: /cae1/ship/aw37xz.for.25
C       Modified by: S.HILAL
C       Tue Sep 10 15:18:15 1991
C       < MICROBURST RADIUS SCALING LOGIC >
C
C File: /cae1/ship/aw37xz.for.24
C       Modified by: S. Hilal
C       Thu Sep  5 13:38:54 1991
C       < modifying microburst page logic >
C
C File: /cae1/ship/aw37xz.for.22
C       Modified by: M.LAMBIDONIS
C       Thu Aug 29 10:46:45 1991
C       < ADDED MISC PRESET LOGIC >
C
C File: /cae1/ship/aw37xz.for.20
C       Modified by: M.LAMBIDONIS
C       Thu Aug 29 10:11:18 1991
C       < ADDED MORE LOGIC FOR VISUAL LIGHTS >
C
C File: /cae1/ship/aw37xz.for.17
C       Modified by: nt
C       Thu Aug 15 08:32:15 1991
C       < corrected TADATE/TATIMEL logic >
C
C File: /cae1/ship/aw37xz.for.16
C       Modified by: sb
C       Wed Aug 14 16:37:23 1991
C       < modifying popup callup logic >
C
C File: /cae1/ship/aw37xz.for.15
C       Modified by: SB
C       Wed Aug 14 16:07:04 1991
C       < modifying runway validation logic >
C
C File: /cae1/ship/aw37xz.for.12
C       Modified by: sb
C       Wed Aug 14 15:04:11 1991
C       < added logic for input validation for runway conditions >
C
C File: /cae1/ship/aw37xz.for.8
C       Modified by: nt
C       Thu Aug  8 15:07:51 1991
C       < added microburst scaling logic (see da88xz) >
C
C File: /cae1/ship/aw37xz.for.6
C       Modified by: nt
C       Thu Aug  8 14:47:15 1991
C       < modified reset logic for aw37 (see rwl737xz & bfblxz) >
C
C File: /cae1/ship/aw37xz.for.4
C       Modified by: nt
C       Mon Jul 29 17:56:53 1991
C       < put back tmmilson & tarvrtd, commented out tcr0malf see xm >
C
C File: /cae1/ship/aw37xz.for.2
C       Modified by: nt
C       Mon Jul 22 15:04:32 1991
C       < modified aw20 for aw37: subroutine name & cp statement. also
C         removed vdspr, tmmilson & tarvrtd cdb labels >
C
C File: /cae1/ship/aw20xz.for.2
C       Modified by: M.LAMBIDONIS
C       Thu Jul 18 22:19:10 1991
C       < Added logic to calculated TMMILSON >
C
C File: /cae1/ship/aw20xz.for.8
C       Modified by: M.lambidonis
C       Wed Jul 17 18:04:26 1991
C       < Corrected tarvrtd calculations >
C
C File: /cae1/ship/aw20xz.for.5
C       Modified by: M.LAMBIDONIS
C       Wed Jul 17 16:39:59 1991
C       < ADDED LOGIC FOR TARVRTD >
C
C File: /cae1/ship/aw20xz.for.2
C       Modified by: Gordon C
C       Mon Jul  8 16:01:35 1991
C       < Removed XSD from CP block >
C
C File: /cae1/ship/aw20xz.for.2
C       Modified by: M.LAMBIDONIS
C       Sat Jun 15 23:47:50 1991
C       <
C
C File: /cae/ship/aw20xz.for.2
C       Modified by: M.lambidonis
C       Thu May 23 19:54:41 1991
C       < Changed push back tolerence level before dark concept >
C
C
C'
C
C'Include_Files
C
C'
C'External_routines
C
      EXTERNAL
     &         DELAYPUT,
     &         DELAYMULTI
C
C'Parameters
C
      REAL*4    BANDWIDTH
      REAL*4    NM_FT
     -,         Z1, Z2, Z4
     -,         KRATE
     -,         KFAST
     -,         RAD
     -,         IASLEWEW
     -,         IASLEWNS
C
      PARAMETER(
     -            BANDWIDTH = 0.266666666,   ! module bandwidth (seconds)
     -            NM_FT     = 6076.115
     -,           Z1        = 2.74298E-6
     -,           Z2        = 0.0174533
     -,           Z4        = 0.01666667
     -,           KRATE     = 0.3
     -,           RAD       = 0.017453293
     -         )
C
C
C'Functions
C
      INTEGER*4
     &          ADDR,                   ! address function
     &          ABS                     ! absolute function
C
C'Local_variables
C
      REAL*8     R8DATE
C
      INTEGER*4
     &          TIMER,                  ! counter for master caution reset
     &          DUMI4,                  ! working I*4
     &          I,J,K,                  ! miscellaneous looping indexes
     &          ADF_TOTR(20),           ! total reset delay flags
     &          ADF_ALLQ(20),           ! all qty reset delay flags
     &          ADF_ALLT(20),           ! all temp reset delay flags
     &          ADF_SYST(20),           ! system reset delay flags
     &          I4RWYNAM(20),           ! I4 value of RXRWYNAM
     &          I4RWYDES(10),           ! I4 value of XZRWYDES
     &          OLD_ICAO,               ! OLD I4 VALUE OF TAICAO1
     &          O_XZPOSN,               ! old value of XZPOSN
     &          ICAO,                   ! I4 VALUE OF TAICAO1
     &          I4TARWY1                ! I4 value of TARWY1
C
      INTEGER*2
     &          SAVEPAGE/0/,            ! Saved page for crash reset
     &          DUMI2(2),               ! redefines DUMI4
     &          NMF_ALLT,               ! # delay flags for all temp rst
     &          NMF_ALLQ,               ! # delay flags for all qty rst
     &          NMF_TOTR,               ! # delay flags for total reset
     &          NMF_SYST,               ! # delay flags for system reset
     &          OLD_MBPROF/0/,          ! old microburst profile #
     &          O_XZWXR,
     &          PAGE_RESET(15),         ! Warning reset page number call-up
     &          TOGGLETIME              ! Timer for G/S x 2 toggle
C
      LOGICAL*1
     &          FIRSTPASS/.TRUE./,        ! first pass flag
     &          TMR_STOP/.FALSE./,        ! stopwatch currently stopped
     &          TMR_START/.FALSE./,       ! stopwatch currently started
     &          TMR_RESET/.TRUE./,        ! stopwatch currently reset
     &          OLD_0ELEC/.FALSE./,       ! old value of TCM0ELEC
     &          OLD_ENVI/.FALSE./,        ! old value of TCRENVI
     &          OLD_TOTR/.FALSE./,        ! old value of TCRTOT
     &          OLD_ALLQ/.FALSE./,        ! old value of TCRALLQ
     &          OLD_ALLT/.FALSE./,        ! old value of TCRALLT
     &          OLD_SYST/.FALSE./,        ! old value of TCRSYST
     &          OLD_CAMLF/.FALSE./,       ! old value of TCRMALF
     &          OLD_CRASH/.FALSE./,       ! old value of TCRASH
     &          OLD_XZCRASH/.FALSE./,     ! old value of XZCRASH
     &          OLD_0CRASH/.FALSE./,      ! old value of TCR0ASH
     &          OLD_WGRAPH/.FALSE./,      ! old value of XZWGRAPH
     &          OLD_TGRAPH/.FALSE./,      ! old value of XZTGRAPH
     &          OLD_AHFACC/.FALSE./,      ! old value of AHFACC
     &          GET_TIME/.TRUE./,         ! get current time of day
     &          O_XZTRACK,                ! Old value of XZTRACK
     &          O_TCFTOT,                 ! Old value of TCFTOT
     &          O_TCMSTORM,               ! Old value of TCMSTORM
     &          O_TCMCAT,                 ! Old value of TCMCAT
     &          O_TCMTURLO,               ! Old value of TCMTURLO
     &          O_TCM0PUSH,               ! Old value of TCM0PUS
     &          O_ATRCA,                  ! Old value of TCMATRCA
     &          O_ATRLT,                  ! Old value of TCMATRLT
     &          O_ATRRT,                  ! Old value of TCMATRRT
     &          O_ATRCB,                  ! Old value of TCMATRCB
     &          O_RUFLT,                  ! Old value of RUFLT
     &          TCRTOT_ACT,               ! tcrtot activation logical
     &          NEWCAT,                   ! New category storm is selected
     &          O_TCMALTSL,               ! Old value of TCMALTSL
     &          O_TCMAVIAS,               ! Old value of TCMAVIAS
     &          O_TCMHDGSL,               ! Old value of TCMHDGSL
     &          O_TCMPOSL,                ! Old value of TCMPOSL
     &          O_TCMSLEWUP,              ! Old value of TCMSLEWUP
     &          O_TCMSLEWDN,              ! Old value of TCMSLEWDN
     &          O_TCMSLEWRT,              ! Old value of TCMSLEWRT
     &          O_TCMSLEWLT,              ! Old value of TCMSLEWLT
     &          MAP_TO                    ! "MAP TO" feature selected
C
      LOGICAL*1
     &          O_TCMACJAX/.FALSE./       ! Old value of TCMACJAX
     &,         O_TCMCHKS/.FALSE./        ! Old value of TCMCHKS
     &,         O_TCMBURST/.FALSE./       ! Old value of TCMBURST
     &,         O_TCMSHEAR/.FALSE./       ! Old value of TCMSHEAR
     &,         O_TCMAUTBR/.FALSE./       ! Old value of TCMAUTBR
C
      INTEGER*4
     &          STARTXREF,              ! starting address of CDB
     &          TVSTOFF,                ! starting address of TV labels
     &          TVENDOFF,               ! ending address of TV labels
     &          TFSTOFF,                ! starting address of TF labels
     &          TFENDOFF,               ! ending address of TF labels
     &          T0STOFF,                ! starting address of T0 labels
     &          T0ENDOFF,               ! ending address of T0 labels
     &          T1STOFF,                ! starting address of T1 labels
     &          T1ENDOFF,               ! ending address of T1 labels
     &          BPSTOFF,                ! starting address of BP labels
     &          BPENDOFF                ! ending address of BP labels
C
      LOGICAL*1
     &          TFMALFS(0:0),           ! redefines TF labels in CDB
     &          T0MALFS(0:0),           ! redefines T0 labels in CDB
     &          T1MALFS(0:0),           ! redefines T1 labels in CDB
     &          BPMALFS(0:0)            ! redefines BP labels in CDB
C
      REAL*4
     &          TVMALFS(0:0)            ! redefines TV labels in CDB
C
      INTEGER*2
     &          NUMTFS,                 ! number of TF labels
     &          NUMT0S,                 ! number of T0 labels
     &          NUMT1S,                 ! number of T1 labels
     &          NUMTVS,                 ! number of TV labels
     &          NUMBPS                  ! number of BP labels
C
      INTEGER*2 RWYS                    ! RUNWAY INDEX
      REAL*4
     &          O_TAMBXOFF,             ! Old value of TAMBYOFF
     &          O_TAMBYOFF,             ! Old value of TAMBYOFF
     &          O_XZMBXOFF,             ! Old value of XZMBYOFF
     &          O_XZMBYOFF,             ! Old value of XZMBYOFF
     &          OLD_TIMEL,              ! Old value of TATIMEL
     &          O_TAGW,                 ! Old value of TAGW
     &          O_TAFUEL,               ! Old value of TAFUEL
     &          O_TACG                  ! Old value of TACG
C
      REAL*4
     &          O_TACGBARX              ! Old values of TACGBARX
     &,         O_TARWBARX              ! Old values of TARWBARX
     &,         CG_INCR                 ! Increment value of CG
C
      CHARACTER*4  REFRWY                 ! Reference runway code
     &,            C_RXRWYNAM(20)         ! Equivalence to RXRWYNAM
C
      CHARACTER*1  TEMPDES(20)            ! EQUIVALENCE TO XZHDRDES
C
      INTEGER*2 RUNWAY,                   ! Used in Runway # Calculation
     &          HDRIDX                    ! Header index number
C
      INTEGER*4
     &          ROUGH_INCR              ! Increment value of runway rough
C
C
C
C  ====================================
C  CAVOK,CATI,CATII,CAT3A,CAT3B,0/0,VFR
C  ====================================
C
      INTEGER*1 F,T
      PARAMETER ( F =  0,
     -            T = -1 )
C
      REAL*4    VIS_VIS(8)            ! values for visibility
      REAL*4    VIS_RVR(8)            ! values for RVR
      REAL*4    VIS_RVR1(8)           ! values for RVR 1 = TCHDN
      REAL*4    VIS_RVR2(8)           ! values for RVR 2 = MID POINT
      REAL*4    VIS_RVR3(8)           ! values for RVR 3 = STOP END
      REAL*4    VIS_CEIL(8)           ! values for cloud ceiling
      REAL*4    VIS_CLDT(8)           ! values for cloud top
      REAL*4    VIS_FGTOP(8)          ! values for cloud top
      REAL*4    VIS_FGTL(8)           ! values for cloud top
      REAL*4    VIS_FGPAT(8)          ! values for cloud top
      INTEGER*2 VIS_APRB(8)           ! values for approach lights
      INTEGER*2 VIS_RWB(8)           ! values for runway lights
      INTEGER*2 VIS_VASI(8)           ! values for VASI lights
      INTEGER*2 VIS_HORB(8)           ! values for horizon lights
      INTEGER*2 VIS_ENVB(8)           ! values for environ. brightness
      INTEGER*2 VIS_TAXI(8)           ! values for taxiway lights
      LOGICAL*1 VIS_SCUD(8)           ! values for scud
      LOGICAL*1 VIS_PFOG(8)           ! values for patchy fog
      LOGICAL*1 VIS_GFOG(8)           ! values for ground fog
C
      INTEGER*2 VIS_IND               ! visual table index
      INTEGER*2 VIS_ITERATIONS        ! visual iterations
      INTEGER*2 IND,
     &          ALL_LIGHTS            ! Same as CDB label TAALLITE
C
      LOGICAL*1 VIS_COND(8),          ! visual conditions flag table
     &          VIS_LIGHT(8),
     &          O_VASI                ! old value of TCMVASI
C
      CHARACTER    C9DATE*9, C8TIME*8, DATE*12
      CHARACTER*5  TIME_DAY          ! EQUIVALENCED TO XZATDAY
      CHARACTER*6  WEATHER           ! EQUIVALENCED TO XZAWXR
      CHARACTER*6  BRAKING           ! EQUIVALENCED TO XZABRKAC
      CHARACTER*5  TURB_TYP          ! EQUIVALENCED TO XZATURTY
      CHARACTER*4  TURB_INT          ! EQUIVALENCED TO XZATURIN
      CHARACTER*8  ICING             ! EQUIVALENCED TO XZAICING
      CHARACTER*10 RWY_CONT          ! EQUIVALENCED TO XZARWYCO
C
C --- GND TRAFFIC LABELS
C
      INTEGER*2   NUM_VISRWY_ACT
      INTEGER*2   MAX_VIS
      INTEGER*2   MAX_RWY
      INTEGER*2   MAX_SGI
      INTEGER*2   POP1_WIND
      PARAMETER  (
     -            MAX_VIS   = 23
     - ,          MAX_RWY   = 16
     - ,          MAX_SGI   = 2
     - ,          POP1_WIND = 6
     -           )
 
C
      CHARACTER*4   VISUAL_ICAO (MAX_VIS)
     -,             C_ICAO
     -,             C_TAVRWID(30)
     -,             C_CURRWY
     -,             O_CURRWY
     -,             DUMMY_RWY
     -,             C_TM_ICAO1
     -,             C_TM_RWY1
     -,             C_ICAO_TBL(14)
     -,             C_RWY_TBL(14)
C
      LOGICAL*1     ICAO_FOUND/.FALSE./
     -,             RWY_FOUND/.FALSE./
     -,             O_RWY_FOUND/.FALSE./
     -,             RWY_CHECK/.FALSE./
     -,             RWY_FLAG/.FALSE./
     -,             O_XPCRTEX/.FALSE./
C
      INTEGER*2     O_TAREFRUN
     -,             O_TAVRWIND/0/            ! Old value of TAVRWIND
     -,             O_TADSPAPP/0/            ! Old value of TADSPAPP
     -,             O_TAEDTAPP/0/            ! Old value of TAEDTAPP
     -,             O_TADSPTDZ/0/            ! Old value of TADSPTDZ
     -,             O_TAEDTTDZ/0/            ! Old value of TAEDTTDZ
     -,             O_TADSPEDG/0/            ! Old value of TADSPEDG
     -,             O_TAEDTEDG/0/            ! Old value of TAEDTEDG
     -,             O_TADSPVAS/0/            ! Old value of TADSPVAS
     -,             O_TAEDTVAS/0/            ! Old value of TAEDTVAS
     -,             O_TADSPTAX/0/            ! Old value of TADSPTAX
     -,             O_TAEDTTAX/0/            ! Old value of TAEDTTAX
     -,             O_TADSPSTR/0/            ! Old value of TADSPSTR
     -,             O_TAEDTSTR/0/            ! Old value of TAEDTSTR
     -,             O_TADSPCEN/0/            ! Old value of TADSPCEN
     -,             O_TAEDTCEN/0/            ! Old value of TAEDTCEN
     -,             O_TADSPALL/0/            ! Old value of TADSPALL
     -,             O_TAEDTALL/0/            ! Old value of TAEDTALL
     -,             O_TADSPRW/0/             ! Old value of TADSPRW
     -,             O_TAEDTRW/0/             ! Old value of TAEDTRW
     -,             O_TADSPHOR/0/            ! Old value of TADSPHOR
     -,             O_TAEDTHOR/0/            ! Old value of TAEDTHOR
     -,             O_TADSPENV/0/            ! Old value of TADSPENV
     -,             O_TAEDTENV/0/            ! Old value of TAEDTENV
     -,             UPPER_PG/80/             ! Upper crt page number
     -,             LOWER_PG/80/             ! Lower crt page number
C
      INTEGER*4
     -              ICAO_IDX/0/              ! Index of VISUAL_ICAO
     -,             O_ICAO_IDX/0/            ! Old index of VISUAL_ICAO
     -,             REF_IDX/0/               ! Index of visual data base
     -,             RWY_IDX/0/               ! Index of VISUAL_ICAO
     -,             O_RWY_IDX/0/             ! Old index of VISUAL_ICAO
C
      CHARACTER*4   C_RWY1,VIS_RWY(MAX_RWY,MAX_VIS)
C
      DATA
     -  VISUAL_ICAO  / 'KPHL','KSFO','KEWN','KABE','KMDT'
     -,                'KMIA','KBOS','KPIT','KJFK','KCLT'
     -,                'KROA','KGSO','KDCA','KLGA','KDFW'
     -,                'KLAS','KLAX','KPHX','KRNO','KSEA'
     -,                'KSLC','KPSP','KSNA'/
C
      DATA VIS_RWY
     -   /
     -     '27L ','    ','27R ','    ',     ! KPHL
     -     '09L ','    ','09R ','    ',
     -     '17  ','    ','35  ','    ',
     -     '08  ','    ','26  ','    ',
C
     -     '01L ','    ','01R ','    ',     ! KSFO
     -     '10L ','    ','10R ','    ',
     -     '19L ','    ','19R ','    ',
     -     '28L ','    ','28R ','    ',
C
     -     '04  ','    ','22  ','    ',     ! KEWN
     -     '13  ','    ','31  ','    ',
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    ',
C
     -     '06  ','    ','13  ','    ',     ! KABE
     -     '24  ','    ','31  ','    ',
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    ',
C
     -     '13  ','    ','31  ','    ',     ! KMDT
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    ',
C
     -     '09L ','    ','27R ','    ',     ! KMIA
     -     '09R ','    ','27L ','    ',
     -     '12  ','    ','30  ','    ',
     -     '    ','    ','    ','    ',
C
     -     '04L ','    ','04R ','    ',     ! KBOS
     -     '22L ','    ','22R ','    ',
     -     '15R ','    ','33L ','    ',
     -     '09  ','    ','27  ','    ',
C
     -     '10L ','    ','10R ','    ',     ! KPIT
     -     '10C ','    ','32  ','    ',
     -     '14  ','    ','28L ','    ',
     -     '28R ','    ','28C ','    ',
C
     -     '04L ','    ','04R ','    ',     ! KJFK
     -     '13L ','    ','13R ','    ',
     -     '22L ','    ','22R ','    ',
     -     '31L ','    ','31R ','    ',
C
     -     '36L ','    ','36C ','    ',     ! KCLT
     -     '36R ','    ','18L ','    ',
     -     '18C ','    ','18R ','    ',
     -     '05  ','    ','23  ','    ',
C
     -     '33  ','    ','15  ','    ',     ! KROA
     -     '24  ','    ','06  ','    ',
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    ',
C
     -     '05R ','    ','14  ','    ',     ! KGSO
     -     '23L ','    ','32  ','    ',
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    ',
C
     -     '04  ','    ','15  ','    ',     ! KDCA
     -     '19  ','    ','22  ','    ',
     -     '33  ','    ','01  ','    ',
     -     '    ','    ','    ','    ',
C
     -     '04  ','    ','22  ','    ',     ! KLGA
     -     '13  ','    ','31  ','    ',
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    ',
C
     -     '35R ','35L ','17L ','13R ',     ! KDFW
     -     '18L ','31L ','36R ','31R ',
     -     '36L ','13L ','18R ','17R ',
     -     '17C ','35C ','    ','    ',
C
     -     '25R ','    ','07L ','    ',     ! KLAS
     -     '01L ','    ','19R ','    ',
     -     '25L ','    ','07R ','    ',
     -     '01R ','    ','19L ','    ',
C
     -     '25R ','    ','07L ','    ',     ! KLAX
     -     '25L ','    ','07R ','    ',
     -     '06R ','    ','24L ','    ',
     -     '24R ','    ','06L ','    ',
C
     -     '07R ','    ','25L ','    ',     ! KPHX
     -     '07L ','    ','25R ','    ',
     -     '26  ','    ','08  ','    ',
     -     '    ','    ','    ','    ',
C
     -     '16R ','    ','34L ','    ',     ! KRNO
     -     '16L ','    ','34R ','    ',
     -     '07  ','    ','25  ','    ',
     -     '    ','    ','    ','    ',
C
     -     '34R ','    ','16R ','    ',     ! KSEA
     -     '34L ','    ','16L ','    ',
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    ',
C[A
     -     '17  ','    ','35  ','    ',     ! KSLC
     -     '16L ','    ','34R ','    ',
     -     '16R ','    ','34L ','    ',
     -     '    ','    ','    ','    ',
C
     -     '13R ','    ','31R ','    ',     ! KPSP
     -     '13L ','    ','31L ','    ',
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    ',
C
     -     '01L ','    ','19L ','    ',     ! KSNA
     -     '01R ','    ','19R ','    ',
     -     '    ','    ','    ','    ',
     -     '    ','    ','    ','    '
     -   /
C
      DATA
     -    C_ICAO_TBL / 'KPHL','KSFO','KEWN','KABE','KMDT'
     -,                'KMIA','KBOS','KPIT','KJFK','KCLT'
     -,                'KROA','KGSO','KDCA','KLGA' /
C
      DATA
     -    C_RWY_TBL  / '27L ','01L ','04  ','06  ','31  '
     -,                '09R ','04R ','10R ','04L ','36R '
     -,                '33  ','14  ','19  ','22  ' /
C
C --- MAP RELATED LABELS
C
C
      INTEGER*2  MAX_TUN         ! Maximum number of tuned stations
     .,          MAX_KIL         ! Maximum number of killed stations
C
      PARAMETER (MAX_TUN  = 10   ! Maximum number of tuned stations
     .,          MAX_KIL  = 20   ! Maximum number of killed stations
     .          )
C
      REAL*4       ILS_SGNL_1,  ILS_SGNL_2,  ILS_SGNL_3
      REAL*4       NDB_SGNL_1,  NDB_SGNL_2,  NDB_SGNL_3
      REAL*4       VOR_SGNL_1,  VOR_SGNL_2,  VOR_SGNL_3
      INTEGER*4    ILS_INDX_1,  ILS_INDX_2,  ILS_INDX_3
      INTEGER*4    NDB_INDX_1,  NDB_INDX_2,  NDB_INDX_3
      INTEGER*4    VOR_INDX_1,  VOR_INDX_2,  VOR_INDX_3
      INTEGER*4    TUNED  (MAX_TUN), TUNEPTR
      INTEGER*4    KILLED (MAX_KIL)
C
C --- LABELS FROM THE FOURTH DIMENSION
C
      INTEGER*2 STNSUMPAGE,MLFSUMPAGE,CRASHPG/67/
      LOGICAL*1 TCMSTATM,TCMHDMAG,XZCRASH
C
C'Data_Base_Variables
C
C
CP    USD8
C
C
C
C ---------------------------------< TM,TMH >-------------------------------
C
CP   -    TMMILSON,
CP   -    TMHTUNED,
CP   -    TMHICAO1,   TMHRWY1,
CP   -    TMICAOSL,   TMHAUTO,
C
C ---------------------------------< TCM >-------------------------------
C
CP   -    TCMTMRST,   TCMADOR,  TCMETRIC,  TCMAVHDG,
CP   -    TCMELEC1,   TCMELEC2, TCMPNEU1,  TCMPNEU2,
CP   -    TCMRWYSL,   TCMRWYAV, TCMSPARE,  TCMVASI,
CP   -    TCMSCUD,    TCMGFOG,  TCMPFOG,   TCMSTORM,
CP   -    TCMCAT,     TCMTURLO, TCMATRCA,  TCMATRLT,
CP   -    TCMATRRT,   TCMATRCB, TCMCHKS,   TCMACJAX,
CP   -    TCMPBLT,    TCMPBRT,  TCMBURST,  TCMDRWY,
CP   -    TCMICE,     TCMRFRWY, TCMICRWY,  TCMPIRWY,
CP   -    TCMARMDO,   TCMDOOR,  TCMSHEAR,
CP   -    TCMSARWL,
CP   -    TCMALLTS,   TCMCHATM,
CP   -    TCMRAOK,    TCMAUTBR,
CP   -    TCMVRWSL,   TCMVRWAV,  TCMVRWIN,
CP   -    TCMALTSL,   TCMAVIAS,  TCMHDGSL, TCMPOSL,
CP   -    TCMSLEWUP,  TCMSLEWLT, TCMSLEWF, TCMSLEWRT,
CP   -    TCMSLEWDN,  TCMCRINB,  TCM0SLEWF,
CP   -    TCM0SLWUP,  TCM0SLWDN, TCM0SLWLT, TCM0SLWRT,
C
C ---------------------------------< TCM0 >------------------------------
C
CP   -    TCM0PNEU, TCM0ACJX,  TCM0PUSH,   TCM0ELC1,
CP   -    TCM0CHKS,   TCM0RAIN, TCM0CRIN,  TCM0VRID,
CP   -    TCM0ACGW,   TCM0ACFL, TCM0ACCG,
C
C ---------------------------------< TA >--------------------------------
C
CP   -    TATIMES,   TATIMEL,    TADATE,
CP   -    TAPOSN,    TARWY1,
CP   -    TAVSWTCH,  TADOKBRT,   TADCSBRT,  TATAXI,
CP   -    TARMPBRT,
CP   -    TAENVBRT,  TALTGBRT,
CP   -    TASKYBRT,  TAAIRBRT,   TAVASI,    TAVISIB,
CP   -    TARVR,     TARVR1,     TARVR2,    TARVR3,
CP   -    TACEILNG,  TACLDTOP,   TAAPRBRT,  TAHORBRT,
CP   -    TAALLBRT,  TAMISFRE,   TAQNH,     TATEMP,
CP   -    TAWDIR,    TAWSPD,     TATURB,    TAMBPROF,
CP   -    TAWSPROF,  TAMBGAIN,   TAWSGAIN,  TABRAKE,
CP   -    TAAIRPRT,  TAVSRWNM,   TARFRWY,   TAHDGSET,
CP   -    TAICAO1,   TAPAGE,     TAHDGMAG,
CP   -    TARWYSLH,  TARWYSNW,   TARWYICE,  TAVMODE,
CP   -    TAICEQTY,  TARWYWET,
CP   -    TARVRTD,
CP   -    TAMBXOFF,  TAMBYOFF,   TAMBYPOS,  TAMBXPOS,
CP   -    TAFLVL,
CP   -    TAMBRAD,   TAPAGERC,
CP   -    TAREFRUN,
CP   -    TAIWDIR,   TAIWSPD,    TAITEMP,    TAIFLVL,
CP   -    TATWDIR,   TATWSPD,    TATTEMP,    TATFLVL,
CP   -    TATLPSRT,  TAHAZ,      TATCCRS,    TCM0SCN,
CP   -    TAVRW(*),  TADSP(*),   TAEDT(*),   TATCNAC,
CP   -    TAGW,      TACG,       TAFUEL,     TAGUST,
CP   -    TAGWMIN,   TAGWMAX,    TACGMIN,    TACGMAX,
CP   -    TAFUELMN,  TAFUELMX,
CP   -    TASLEWNS,  TASLEWEW,
CP   -    TAHDG,     TAALT,      TAIAS,      TASPARE,
CP   -    TACGBARX,  TACGBARY,   TARWBARX,   TARWBARY,
CP   -    TAFOGTOP,  TAFOGTL,    TAFOGPAT,   TASPDUP,
CP   -    TAAPRBRT1, TATDZLT1,   TARWYEDG1,  TASTROBE1,
CP   -    TAVASI1,   TARWYMID1,  TAALLRWY1,
C
C ------------------------------------< TCR >----------------------------
C
CP   -    TCRALLT,   TCRMALF,    TCRSYST,   TCRIDGQ,
CP   -    TCRSPCL,   TCRALLST,   TCREOILT,  TCRHOILT,
CP   -    TCRAOILT,  TCRAEGT,    TCRBRKT,   TCRACOND,
CP   -    TCRFIRX,   TCRIDG,     TCRBATT,   TCROXY,
CP   -    TCRCBPRE,  TCRALLQ,    TCRARPM,   TCRRAT,
CP   -    TCRENGF,   TCRGEN,     TCRCARF,   TCREOILQ,
CP   -    TCRAOILQ,  TCRHOILQ,   TCRHFLQ,   TCROUTCB,
CP   -    TCRTOT,    TCRASH,     TCRENVI,   TCRFUELT,
CP   -    TCREGT,    TCRCSD,     TCRCSDOT,  TCRCSDOQ,
CP   -    TCRIDGT,   TCRACONS,   TCRMAINT,  TCMRWYOK,
CP   -    TCRACTAT,  TCRHQTY1,   TCRHQTY2,  TCRHQTY3,
CP   -    TCRHQTY4,  TCRBTMP,    TCRPKTMP,  TCRTCAS,
C
C -------------------------------------< TCR0 >--------------------------
C
CP   -    TCR0(*),
C
C ----------------------------------< XZ >-----------------------------------
C
CP   -     XZSTNDES,  XZSTNINX,   XZTRACK,   XZSPDDEV,
CP   -     XZSMORE,   XZSMORAV,   XZLVFFRZ,  XZPOSN,
CP   -     XZSTHDR,   XZSPECPG,   XZRWYMAV,  XZWXR,
CP   -     XZRWYMOR,  XZRWYDES,   XZHDRDES,  XZATDAY,
CP   -     XZAWXR,    XZABRKAC,   XZATURTY,  XZATURIN,
CP   -     XZAICING,  XZARWYCO,   XZPRESET,
CP   -     XZWGRAPH,  XZTGRAPH,   XZMBSCAL,
CP   -     XZMBXOFF,  XZMBYOFF,   XZWARN,    XZSTATUS,
CP   -     XZWRNRST,
C
C ---------------------------------< TR >------------------------------------
C
CP   -     TRSNPREC,  TRSETREC,   TRRAPON,
C
C ----------------------------------< X* >-----------------------------------
C
CP   -     XPCALLAV,   XSARM,
CP   -     XSPRUPD,
C
C ----------------------------------< R* >-----------------------------------
C
CP   -     RWSLINE,   RVNKIL,     RVK,       RUFLT,
CP   -     RXRWYNAM,  RTAVAR,     RXARPT,    RXBRWYS,
CP   -     RXMISFRE,  RXMISIAT,   RXMISELE,  RVRTD,
CP   -     RXMISGPX,  RXMISRWE,   RBCGGPX,   RXMISTYN,
CP   -     RBSILS,    RBILSIDX,   RFRAUDIO,
CP   -     RANDBIDX,  RBSVOR,     RBVORIDX,
CP   -     RUPLAT,    RUPLON,     RBFVOI,
C
C ----------------------------------< V* >-----------------------------------
C
CP   -     VBOG,      VACST,     VUG,      VTRIM,
CP   -     VSAELE,    VREF,      VOTEMP,   VH,
CP   -     VVNS,      VVEW,      VTOTWIND, VATURB,
CP   -     VM,
C
C ----------------------------------< T* >-----------------------------------
C
CP   -     TCF0FLPO,  TCATMSB,   TCFTOT,
CP   -     TCATMBLS,  TCATMFP,   TCATMHP,  TCATMFL,
CP   -     TCFFLPOS,  TCFPOS,    TCFFUEL,  TCFALT,
CP   -     TCFIAS,    TCFHDG,
CP   -     TVSTART,   TVEND,     TFSTART,  TFEND,
CP   -     T0START,   T0END,     T1START,  T1END,
C
C -------------------------------< OTHER >-----------------------------------
C
CP   -     XOPOPUP,    TLSTART,   TLNST,    TLTIME,
CP   -     TASOUND,
CP   -     YXSTRTXRF,  YIFREZ,    UWCAS,
CP   -     SLAAPENG,   SLAPENG,   CIETSPOS,
CP   -     CRTRIM,     CELVL,     CELVR,    CATRIM, CWCYLMT,
CP   -     IDESTOP,    IDESAF,    IDAFP,    IDAFP2,
CP   -     IDAHDP,     IDAHDP2,   IDDBSBI,  IDDBSB2,
CP   -     IDESI1,     IDESI2,    EFDESIN1, EFDESIN2,
CP   -     IDAWS1,     IDAWS2,    IDAWS3,
CP   -     IDAWS4,     IDAWS5,    IDAWS6,
CP   -     BP0,        BP9999,    AM$CRST,
CP   -     DOFAI,      DBFBI,     AHFACC,
CP   -     XPCRTEX
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 19-Oct-2023 17:48:06 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  CATRIM         ! AILERON TRIM POSITION
     &, CELVL          ! LEFT ELEVATOR POSITION
     &, CELVR          ! RIGHT ELEVATOR POSITION
     &, CIETSPOS       ! PITCH TRIM TAB SURFACE POSITION        [DEG]
     &, CRTRIM         ! RUDDER SURFACE TRIM ANGLE              [DEG]
     &, RBCGGPX(3)     ! A/C C/G TO GP ANTENNA (X-AXIS)         [FT]
     &, RBSILS(3)      ! ILS SIGNAL STRENGHT
     &, RBSVOR(3)      ! VOR SIGNAL STRENGTH
     &, RFRAUDIO(30)   ! AUDIO REAL LABEL
     &, RTAVAR         ! MAGNETIC VARIATION                    [DEG]
     &, RVRTD          ! DISTANCE TO TOUCHDOWN                  [NM]
     &, TAALT          ! ALTITUDE SLEW RATE (-1 TO +1)
     &, TACEILNG       ! CLOUD CEILING                       [Feet ]
     &, TACG           ! CENTER OF GRAVITY                   [%MAC ]
     &, TACGBARX       ! CG X-COORD FOR TIGERS
     &, TACGBARY       ! CG Y-COORD FOR TIGERS
     &, TACGMAX        ! MAX CENTER OF GRAVITY               [%MAC ]
     &, TACGMIN        ! MIN CENTER OF GRAVITY               [%MAC ]
     &, TACLDTOP       ! CLOUD TOP                           [Feet ]
     &, TAFLVL(5)      ! FLIGHT LEVEL FOR TEMPERATURE        [Feet ]
     &, TAFOGTOP       ! FOG TOP                             [Feet ]
     &, TAFUEL         ! TOTAL FUEL QTY                      [LBS  ]
     &, TAFUELMN       ! MIN TOTAL FUEL QTY                  [LBS  ]
     &, TAFUELMX       ! MAX TOTAL FUEL QTY                  [LBS  ]
     &, TAGUST         ! WIND GUST INTENSITY
     &, TAGW           ! GROSS WEIGHT                        [LBS  ]
     &, TAGWMAX        ! MAX GROSS WEIGHT                    [LBS  ]
     &, TAGWMIN        ! MIN GROSS WEIGHT                    [LBS  ]
     &, TAHDG          ! HEADING SLEW RATE  (-1 TO +1)
     &, TAHDGMAG       ! A/C MAGNETIC HEADING
     &, TAHDGSET       ! A/C HEADING                         [Degs ]
      REAL*4   
     &  TAIAS          ! AIRSPEED SLEW RATE (-1 TO +1)
     &, TAICEQTY       ! ICING QUANTITY
     &, TAIFLVL        ! INTERMEDIATE FLT LEVEL
     &, TAITEMP        ! INTERMEDIATE WIND TEMPERATURE DEG C
     &, TAIWDIR        ! INTERMEDIATE WIND DIRECTION
     &, TAIWSPD        ! INTERMEDIATE WIND SPEED
     &, TAMBGAIN       ! MICROBURST INTENSITY (0-5)
     &, TAMBRAD(5)     ! MICROBURST CELL RADIUS
     &, TAMBXOFF       ! MICROBURST X POS OFFSET             [KM   ]
     &, TAMBXPOS       ! MICROBURST X-POSITION
     &, TAMBYOFF       ! MICROBURST Y POS OFFSET             [KM   ]
     &, TAMBYPOS       ! MICROBURST Y-POSITION
     &, TAMISFRE       ! DISPLAYS FREQUENCY AS REAL NUMBER
     &, TAQNH          ! SEA LEVEL BARO PRESSURE             [Inchs]
     &, TARVR          ! RVR                                 [Mtres]
     &, TARVR1         ! RVR AT TOUCHDOWN                    [Mtres]
     &, TARVR2         ! RVR AT MIDPOINT                     [Mtres]
     &, TARVR3         ! RVR AT STOP_END                     [Mtres]
     &, TARVRTD        ! DISTANCE OF A/C FROM THRESHOLD
     &, TARWBARX       ! RUNWAY ROUGHNESS X-COORD FOR TIGERS
     &, TARWBARY       ! RUNWAY ROUGHNESS Y-COORD FOR TIGERS
     &, TARWYICE       ! ICY RWY, (<1-%PATCHY, 1-FULLY COVERED)
     &, TARWYSLH       ! SLUSH ON RUNWAY                     [CM   ]
     &, TARWYSNW       ! SNOW ON RUNWAY                      [CM   ]
     &, TARWYWET       ! WET RWY, (<1-%PATCHY, 1-WET, >1-FLOODED)
     &, TASLEWEW       ! SLEW POS EAST (>0) OR WEST (<0)
     &, TASLEWNS       ! SLEW POS NORTH (>0) OR SOUTH (<0)
     &, TASOUND        ! SOUND VOLUME (0-100%)
     &, TASPARE(5)     ! SPARE LABEL
     &, TASPDUP        ! SPEED UP RATE (0-100%)
     &, TATEMP(5)      ! TEMPERATURE AT FLIGHT LEVEL         [DegsC]
      REAL*4   
     &  TATFLVL        ! TROPOPAUSE FLT LEVEL
     &, TATIMEL        ! SIMULATOR TIME                   [HH:MM:SS]
     &, TATIMES        ! SHORT TIME                          [MM:SS]
     &, TATLPSRT       ! TEMPERATURE LAPSE RATE (DEG/1000FT)
     &, TATTEMP        ! TROPOPAUSE WIND TEMPERATURE DEG C
     &, TATURB(5)      ! TURBULENCE
     &, TATWDIR        ! TROPOPAUSE WIND DIRECTION
     &, TATWSPD        ! TROPOPAUSE WIND SPEED
     &, TAVISIB        ! VISIBILITY                          [Mtres]
     &, TAWDIR(5)      ! WIND DIRECTION AT FLIGHT LEVEL      [Degs ]
     &, TAWSGAIN       ! WINDSHEAR INTENSITY
     &, TAWSPD(5)      ! WIND SPEED AT FLIGHT LEVEL          [Knots]
     &, TLNST          ! LESSON/STEP INPUT
     &, TLTIME         ! LESSON TIME                         [Secs ]
     &, TVEND          ! Last Variable Malfunctions
     &, TVSTART        ! First Variable Malfunctions
     &, UWCAS          !  Calibrated Airspeed in Knots
     &, VACST          ! AIRCRAFT STATIONARY FLAG (1=MOVING)
     &, VATURB         ! TURBULENCE INTENSITY FOR FLIGHT
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VM             ! MACH NUMBER
     &, VOTEMP(5)      ! PREVIOUS VALUE OF TATEMP
     &, VREF           ! APPROACH SPEED                         [kts]
     &, VSAELE         ! ELEVATION                              [FT]
     &, VTOTWIND       ! TOTAL WIND SPD AT A/C                 [ft/s]
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
     &, XZMBSCAL(5)    ! MICROBURST CELL SCALE
     &, XZMBXOFF       ! MICROBURST X OFFSET
      REAL*4   
     &  XZMBYOFF       ! MICROBURST Y OFFSET
     &, XZSPDDEV       ! SPEED DEVIATION (HI)                [Knots]
C$
      INTEGER*4
     &  RANDBIDX(3)    ! 31 STATION INDEX NUMBER
     &, RBILSIDX(3)    ! 31 STATION INDEX NUMBER
     &, RBVORIDX(3)    ! 31 STATION INDEX NUMBER
     &, RWSLINE        ! LINE # ON STN KILL PAGE
     &, RXARPT         ! RWY/GATE AIRPORT ICAO NAME
     &, RXMISFRE(5)    ! 30 FREQUENCY
     &, TAFOGTL        ! FOG TOP LOWER                       [Mtres]
     &, TAPOSN         ! REPOSITION INDEX
     &, TAREFRUN       ! REFERENCE RUNWAY
     &, TCATMFL        ! CNIA ATM FLAP LEVER POSITION
     &, TMHTUNED(10)   ! STATION TUNED
C$
      INTEGER*2
     &  RVNKIL         ! NO. OF STATIONS KILLED BY INSTR.
     &, RXBRWYS        ! ACTUAL NO. OF AIRPORT RUNWAYS
     &, RXMISELE(5)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RXMISGPX(5)    ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RXMISRWE(5)    ! 60 RUNWAY END TO THRESHOLD (FEET)      [FT]
     &, TAAIRBRT       ! A/P BEACON  LIGHTS (0-5)
     &, TAAIRPRT       ! DATA BASE NUMBER (0-127)
     &, TAALLBRT       ! ALL LIGHTS BRIGTHNESS (0-5)
     &, TAALLRWY1(20)  ! ALL LIGHTS ON RUNWAY
     &, TAAPRBRT(10)   ! APPROACH LIGHTS   (0-5)
     &, TAAPRBRT1(20)  ! APPROACH LIGHTS   (0-5)
     &, TABRAKE        ! BRAKING ACTION (0-4)
     &, TADCSBRT(4)    ! DCS BRIGHTNESS (0-5)
     &, TADOKBRT(4)    ! DOCKING FACILITY BRIGHTNESS (0-5)
     &, TADSPALL       ! DISPLAY ALL LIGHT
     &, TADSPAPP       ! DISPLAY APP LIGHT
     &, TADSPCEN       ! DISPLAY CENTER LIGHT
     &, TADSPEDG       ! DISPLAY EDGE LIGHT
     &, TADSPENV       ! DISPLAY ENVIRONMENT LIGHT
     &, TADSPHOR       ! DISPLAY HORIZONTAL BRT LIGHT
     &, TADSPRW        ! DISPLAY RUNWAY LIGHT
     &, TADSPSTR       ! DISPLAY STROBE LIGHT
     &, TADSPTAX       ! DISPLAY TAXI LIGHT
     &, TADSPTDZ       ! DISPLAY TDZ LIGHT
     &, TADSPVAS       ! DISPLAY VASI LIGHT
     &, TAEDTALL       ! EDIT ALL LIGHT
     &, TAEDTAPP       ! EDIT APP LIGHT
     &, TAEDTCEN       ! EDIT CENTER LIGHT
     &, TAEDTEDG       ! EDIT EDGE LIGHT
     &, TAEDTENV       ! EDIT ENVIRONMENT LIGHT
     &, TAEDTHOR       ! EDIT HORIZONTAL BRT LIGHT
      INTEGER*2
     &  TAEDTRW        ! EDIT RUNWAY LIGHT
     &, TAEDTSTR       ! EDIT STROBE LIGHT
     &, TAEDTTAX       ! EDIT TAXI LIGHT
     &, TAEDTTDZ       ! EDIT TDZ LIGHT
     &, TAEDTVAS       ! EDIT VASI LIGHT
     &, TAENVBRT       ! ENVIRONMENT LIGHTS (0-5)
     &, TAFOGPAT       ! FOG PATCHINESS (0-7)
     &, TAHAZ          ! HAZARD POP-UP PAGE NUMBER
     &, TAHORBRT       ! HORIZON BRIGHTNESS (0-5)
     &, TALTGBRT       ! LIGHTNING BRIGHTNESS (0-5)
     &, TAMBPROF       ! MICROBURST PROFILE (0-6)
     &, TAPAGE(4)      ! PAGE REQUEST TO SGI
     &, TAPAGERC(15,2) ! PAGE REQUEST TO SGI (1:upper, 2:lower)
     &, TARFRWY        ! RUNWAY ROUGHNESS
     &, TARMPBRT       ! RAMP LIGHTS (0-5)
     &, TARWYEDG1(20)  ! RUNWAY EDGE LIGHTS  (0-5)
     &, TARWYMID1(20)  ! RUNWAY CENTER LIGHTS  (0-5)
     &, TASKYBRT       ! STARS BRIGHTNESS (0-5)
     &, TASTROBE1(20)  ! STROBE LIGHTS     (0-5)
     &, TATAXI         ! TAXI LIGHTS (0-5)
     &, TATCCRS        ! COLLISION COURSE SELECTION
     &, TATCNAC        ! NUMBER OF INTRUDER A/C
     &, TATDZLT1(20)   ! TDZL LIGHTS TYPE
     &, TAVASI(5)      ! VASI LIGHTS  (0-5)
     &, TAVASI1(20)    ! VASI LIGHTS  (0-5)
     &, TAVMODE        ! L.L DAY/DAWN/DUSK/NIGHT
     &, TAVRWALL(30)   ! BUFF CONTAINS ALL LIGHT OF EACH RWY
     &, TAVRWAPP(30)   ! BUFF CONTAINS APP. LIGHT OF EACH RWY
     &, TAVRWCEN(30)   ! BUFF CONTAINS CENTER LT OF EACH RWY
     &, TAVRWEDG(30)   ! BUFF CONTAINS EDGE LIGHT OF EACH RWY
     &, TAVRWENV(30)   ! BUFF CONTAINS ENVIRON. LT OF EACH RWY
      INTEGER*2
     &  TAVRWHOR(30)   ! BUFF CONTAINS HORIZON BRT OF EACH RWY
     &, TAVRWIND       ! INDEX OF CURRENTLY SELECTED RUNWAY
     &, TAVRWLT(30)    ! BUFF CONTAINS RWY LIGHT OF EACH RWY
     &, TAVRWSTR(30)   ! BUFF CONTAINS STROBE LT OF EACH RWY
     &, TAVRWTAX(30)   ! BUFF CONTAINS TAXI LIGHT OF EACH RWY
     &, TAVRWTDZ(30)   ! BUFF CONTAINS TDZ LIGHT OF EACH RWY
     &, TAVRWVAS(30)   ! BUFF CONTAINS VASI LIGHT OF EACH RWY
     &, TAVRWYIDX      ! VISUAL LIGHT RWY INDEX
     &, TAVSWTCH(64)   ! VISUAL LIGHT SWITCHES (0-5)
     &, TAWSPROF       ! WINDSHEAR PROFILE (0-12)
     &, XZPOSN         ! REPOSITION INDEX
     &, XZPRESET       ! PRESET SETUP SELECTION
     &, XZSTNINX(4)    ! FAILED STN SUMMARY ENTRY INDEX
     &, XZWXR          ! WEATHER SELECTION
C$
      LOGICAL*1
     &  AHFACC         ! Accumulator pressurize flag (brake hand pump)
     &, AM$CRST        ! Caut lts sys reset signal      40-026 DO0625
     &, BP0            ! FIRST BP - DUMMY             0 PAON   DODUMY
     &, BP9999         ! SPARE                       88 PAON   DODUMY
     &, CWCYLMT        ! Stick Pusher charge empty
     &, DBFBI(2)       ! HP BLEED CONT TRIP FLAG
     &, DOFAI(2)       ! FLT COMP/CAB DUCT OVHT
     &, EFDESIN1       ! Engine ignition 1 @ NORM                 [-]
     &, EFDESIN2       ! Engine ignition 2 @ NORM                 [-]
     &, IDAFP          ! Tank 1 auxiliary pump sw       14-370 DI009C
     &, IDAFP2         ! Tank 2 auxiliary pump sw       14-370 DI009D
     &, IDAHDP         ! Stby hyd pump 1 sw             14-116 DI045C
     &, IDAHDP2        ! Stby hyd pump 2 sw             14-116 DI045D
     &, IDAWS1         ! Flap control sw 1              12-023 DIDUMY
     &, IDAWS2         ! Flap control sw 2              12-023 DIDUMY
     &, IDAWS3         ! Flap control sw 3              12-023 DIDUMY
     &, IDAWS4         ! Flap control sw 4              12-023 DIDUMY
     &, IDAWS5         ! Flap control sw 5              12-023 DIDUMY
     &, IDAWS6         ! Flap control sw 6              12-023 DIDUMY
     &, IDDBSB2        ! R BLEED SWITCH @BLEED (-300/300A)     DI0566
     &, IDDBSBI        ! L BLEED SWITCH @BLEED (-300/300A)     DI0568
     &, IDESAF         ! Autofeather SELECT sw                 DI009E
     &, IDESI1         ! Engine ignition 1 @ NORM              DI020C
     &, IDESI2         ! Engine ignition 2 @ NORM              DI020A
     &, IDESTOP        ! Engine ECU selector @ TOP             DI0090
     &, RBFVOI(3)      ! RECEIVER POWERED AND ILS MODE SELECTED
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, SLAAPENG       ! Any autopilot engaged flag
     &, SLAPENG(2)     ! autopilot engaged
     &, T0END          ! Last Discrete Malfunctions
     &, T0START        ! First Discrete Malfunctions
      LOGICAL*1
     &  T1END          ! Last Variable Malfunctions
     &, T1START        ! First Variable Malfunctions
     &, TAVRWID(4,30)  ! VISUAL RUNWAY NAMES AT REF. AIRPORT
     &, TAVRWINP(4)    ! CURRENT RUNWAY NAME (INPUTTABLE)
     &, TCATMBLS       ! CNIA ATM BLEED SWITCH
     &, TCATMFP        ! CNIA ATM FUEL PUMP STATUS
     &, TCATMHP        ! CNIA ATM HYDRAULICS PUMPS STATUS
     &, TCATMSB        ! CNIA ATM SPEED BRAKE POSITION
     &, TCF0FLPO       ! FLIGHT FREEZE AVAIL
     &, TCFALT         ! FREEZE/ALTITUDE  (01)
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFFUEL        ! FREEZE/FUEL
     &, TCFHDG         ! FREEZE/HEADING
     &, TCFIAS         ! FREEZE/AIRSPEED
     &, TCFPOS         ! FREEZE/POSITION
     &, TCFTOT         ! FREEZE/TOTAL
     &, TCM0ACCG(10)   ! A/C LOADING CG
     &, TCM0ACFL(10)   ! A/C LOADING FUEL
     &, TCM0ACGW(10)   ! A/C LOADING GROSS WEIGHT
     &, TCM0ACJX       ! A/C ON JACKS
     &, TCM0CHKS       ! WHEEL CHOCKED
     &, TCM0CRIN       ! CRASH INHIBIT
     &, TCM0ELC1       ! EXT POWER AVAILABLE FLAG #1
     &, TCM0PNEU       ! EXTERNAL AIR AVAIL
     &, TCM0PUSH       ! PUSHBACK - PROC ACTIVATE
     &, TCM0RAIN       ! RAIN ON RUNWAY AVAIL
     &, TCM0SCN(5)     ! TCAS SCENARIO DARK CONCEPT
     &, TCM0SLEWF      ! DARK CONCEPT FOR FAST SLEW
     &, TCM0SLWDN      ! DARK CONCEPT FOR SLEW DOWN/SOUTH
     &, TCM0SLWLT      ! DARK CONCEPT FOR SLEW LEFT/WEST
     &, TCM0SLWRT      ! DARK CONCEPT FOR SLEW RIGHT/EAST
      LOGICAL*1
     &  TCM0SLWUP      ! DARK CONCEPT FOR SLEW UP/NORTH
     &, TCM0VRID(30)   ! VISUAL RUNWAY NAMES THIRD COLOR FLAG
     &, TCMACJAX       ! A/C ON JACKS
     &, TCMADOR        ! ARM ALL DOORS
     &, TCMALLTS       ! ALL LIGHTS
     &, TCMALTSL       ! ALT SLEW IN PROGRESS
     &, TCMARMDO(21)   ! DOORS ARMED
     &, TCMATRCA       ! AIR TRAFFIC CENTER ABOVE
     &, TCMATRCB       ! AIR TRAFFIC CENTER BELOW
     &, TCMATRLT       ! AIR TRAFFIC LEFT
     &, TCMATRRT       ! AIR TRAFFIC RIGHT
     &, TCMAUTBR       ! AUTO BRIGHTNESS
     &, TCMAVHDG       ! HDG ASSOCIATED BOOLEAN
     &, TCMAVIAS       ! IAS ASSOCIATED BOOLEAN
     &, TCMBURST       ! MICROBURST ACTIVATE
     &, TCMCAT         ! CLEAR AIR TURBULENCE
     &, TCMCHATM       ! CHANGE IN TEMP/WIND PROFILE
     &, TCMCHKS        ! WHEELS CHOCKED
     &, TCMCRINB       ! CRASH INHIBIT
     &, TCMDOOR        ! DOOR MALFUNCTION
     &, TCMDRWY        ! DRY RUNWAY
     &, TCMELEC1       ! EXTERNAL ELECTRIC POWER #1
     &, TCMELEC2       ! EXTERNAL ELECTRIC POWER #2
     &, TCMETRIC       ! METRIC UNIT FLAG
     &, TCMGFOG        ! GROUND FOG
     &, TCMHDGSL       ! HDG SLEW IN PROGRESS
     &, TCMICE         ! ALL SYSTEMS ICE
     &, TCMICRWY       ! RUNWAY ICE
     &, TCMPBLT        ! PUSHBACK - LEFT
     &, TCMPBRT        ! PUSHBACK - RIGHT
     &, TCMPFOG        ! PATCHY FOG SELECT
      LOGICAL*1
     &  TCMPIRWY       ! PATCHY RUNWAY (ICE OR RAIN)
     &, TCMPNEU1       ! EXTERNAL AIR #1
     &, TCMPNEU2       ! EXTERNAL AIR #2
     &, TCMPOSL        ! POS SLEW IN PROGRESS
     &, TCMRAOK(2)     ! RADIO PAGE READY
     &, TCMRFRWY(24)   ! 3RD COLOR FOR REF. RUNWAY
     &, TCMRWYAV(24)   ! RUNWAY SELECTION AVAILABLE
     &, TCMRWYOK       ! RUNWAY AVAILABLE FLAG
     &, TCMRWYSL(24)   ! SELECT REFERENCE RUNWAY
     &, TCMSARWL(10)   ! SET ALL LIGHTS ON RUNWAY
     &, TCMSCUD        ! SCUD
     &, TCMSHEAR       ! WINDSHEAR ACTIVATE SELECTED PROF
     &, TCMSLEWDN      ! SLEW DIRECTION DOWN/SOUTH
     &, TCMSLEWF       ! SLEW FAST
     &, TCMSLEWLT      ! SLEW DIRECTION LEFT/WEST
     &, TCMSLEWRT      ! SLEW DIRECTION RIGHT/EAST
     &, TCMSLEWUP      ! SLEW DIRECTION UP/NORTH
     &, TCMSPARE(20)   ! SPARE
     &, TCMSTORM       ! THUNDERSTORM TURBULENCE (HV)
     &, TCMTMRST       ! TIMER RESET/START/STOP
     &, TCMTURLO       ! LOW LEVEL TURBULENCE
     &, TCMVASI        ! VISUAL VASI LIGHTS
     &, TCMVRWAV(30)   ! VISUAL RUNWAY AVAIL. FOR EDITING
     &, TCMVRWIN       ! VISUAL RUNWAY NAMES INPUT FLAG
     &, TCMVRWSL(30)   ! VISUAL RUNWAY SELECT FOR EDITING
     &, TCR0ABRK       ! AUTO BRAKE RESET
     &, TCR0ACND       ! AIRCONDITIONING RESET
     &, TCR0ACON       ! AIRCONDITIONING TEMP SELECTION
     &, TCR0ACTA       ! AIRCONDITIONING TEMP TO AMBIENT
     &, TCR0ADG        ! AIR DRIVEN GENERATOR
     &, TCR0AEGT       ! APU EGT
      LOGICAL*1
     &  TCR0ALLQ       ! ALL QUANTITIES
     &, TCR0ALLS       ! ALL STATION RESET
     &, TCR0ALLT       ! ALL TEMPERATURES
     &, TCR0AMAL       ! ALL MALFUNCTION ON PAGE
     &, TCR0AOLQ       ! APU OIL QUANTITY
     &, TCR0AOLT       ! APU OIL TEMPERATURE
     &, TCR0ARPM       ! APU AIRSPEED
     &, TCR0ASH        ! CRASH
     &, TCR0ATNK       ! ALL TANKS REFILL
     &, TCR0BATT       ! BATT CHARGE
     &, TCR0BITE       ! INFLIGHT FAULT RESET
     &, TCR0BRAC       ! BRAKE ACCUMULATOR RESET
     &, TCR0BRKT       ! BRAKE TEMPERATURE RESET
     &, TCR0BTMP       ! BATTERY TEMPERATURE RESET - AVAIL FLAG
     &, TCR0CABT       ! CABIN TEMPERATURE
     &, TCR0CARF       ! CARGO FIRE RESET
     &, TCR0CBPA       ! CABIN PRESS RESET TO AMBIENT
     &, TCR0CBPR       ! CABIN PRESSURE RESET
     &, TCR0CBPRE      ! CABIN PRESS RESET DARK CON
     &, TCR0CGCC       ! CGCC FAULT RESET
     &, TCR0CSD        ! CSD RECONNECT
     &, TCR0CSDQ       ! CSD OIL QUANTITY RESET
     &, TCR0CSDT       ! CSD OIL TEMP RESET
     &, TCR0ECAM       ! ECAM MESSAGES
     &, TCR0ECSF       ! ECS CRITICAL FAULT
     &, TCR0EGT        ! ENGINE TEMPERATURE RESET
     &, TCR0ELEC       ! ELECTRICAL SYSTEM RESET
     &, TCR0EMAB       ! EMERGENCY AIR BOTTLE
     &, TCR0ENGF       ! ENGINE FIRE RESET
     &, TCR0ENVI       ! ENVIRONMENT STANDARD RESET
     &, TCR0EOFF       ! ENGINE FAST SHUTDOWN
      LOGICAL*1
     &  TCR0EOLQ       ! ENGINE OIL QUANTITY
     &, TCR0EOLT       ! ENGINE OIL TEMPERATURE
     &, TCR0FIRE       ! FIRE
     &, TCR0FIRX       ! FIRE BOTTLE
     &, TCR0FMS        ! FMS INIT RESET
     &, TCR0FMSR       ! FMS REINITIALIZATION
     &, TCR0FUEL       ! FUEL TEMPERATURE
     &, TCR0GEN        ! GENERATOR RESET FLAG
     &, TCR0HFLQ       ! REFILL HYDRAULIC
     &, TCR0HOLQ       ! HYDRAULIC OIL QUANTITY
     &, TCR0HOLT       ! HYDRAULIC OIL TEMPERATURE
     &, TCR0HQT1       ! HYDRAULIC QTY SYS 1
     &, TCR0HQT2       ! HYDRAULIC QTY SYS 2
     &, TCR0HQT3       ! HYDRAULIC QTY SYS 3
     &, TCR0HQT4       ! HYDRAULIC QTY SYS 4
     &, TCR0ICE        ! RESET ICING
     &, TCR0IDG        ! IDG RECONNECT
     &, TCR0IDGQ       ! IDG OIL QUANTITY
     &, TCR0IDGT       ! IDG OIL TEMPERATURE
     &, TCR0LG         ! LANDING GEAR RESET
     &, TCR0MAIN       ! ALL MAINTENANCE
     &, TCR0MALF       ! ALL MALFUNCTION
     &, TCR0MFCL       ! CLEAR ALL COLLECTED MALF AVAIL.
     &, TCR0OILQ       ! ALL OIL QUANTITIES RESET
     &, TCR0OILT       ! OIL TEMPERATURE
     &, TCR0OUTC       ! RESET OUTSIDE CB'S
     &, TCR0OXY        ! OXYGEN BOTTLE
     &, TCR0PASO       ! PASSENGER OXYGEN
     &, TCR0PKTM       ! PACK TEMPERATURE RESET - AVAIL FLAG
     &, TCR0PNEU       ! PNEUMATICS DUCT TEMPERATURE
     &, TCR0PWRF       ! POWER FAIL RESET
      LOGICAL*1
     &  TCR0RAT        ! RESET RAM AIR TURINE
     &, TCR0SHTS       ! SHAFT SHEAR RESET
     &, TCR0SPCL       ! SPECIAL EFFECTS RESET
     &, TCR0STBH       ! STANDBY HORIZON/ERECT RESET
     &, TCR0SYST       ! ALL SYSTEM RESET
     &, TCR0TCAS       ! TCAS RESET
     &, TCR0TEFL       ! TE FLAPS ASYMMETRY RESET ON
     &, TCR0TIRB       ! TIREBURST RESET
     &, TCR0TIRP       ! TIRE PRESSURE RESET
     &, TCR0TIRT       ! TIRE TEMPERATURE RESET
     &, TCR0TOT        ! TOTAL
     &, TCR0VISY       ! VISUAL SYSTEM RESET
     &, TCR0WNDS       ! WIND SHIELD TEMPERATURE
     &, TCRACOND       ! AIRCONDITIONING RESET
     &, TCRACONS       ! AIRCONDITIONING TEMP SELECTION
     &, TCRACTAT       ! AIRCONDITIONING TEMP TO AMBIENT
     &, TCRAEGT        ! APU EGT
     &, TCRALLQ        ! ALL QUANTITIES
     &, TCRALLST       ! ALL STATION RESET
     &, TCRALLT        ! ALL TEMPERATURES
     &, TCRAOILQ       ! APU OIL QUANTITY
     &, TCRAOILT       ! APU OIL TEMPERATURE
     &, TCRARPM        ! APU AIRSPEED
     &, TCRASH         ! CRASH
     &, TCRBATT        ! BATT CHARGE
     &, TCRBRKT        ! BRAKE TEMPERATURE RESET
     &, TCRBTMP        ! BATTERY TEMPERATURE RESET
     &, TCRCARF        ! CARGO FIRE RESET
     &, TCRCBPRE       ! CABIN PRESSURE RESET
     &, TCRCSD         ! CSD RECONNECT
     &, TCRCSDOQ       ! CSD OIL QUANTITY RESET
      LOGICAL*1
     &  TCRCSDOT       ! CSD OIL TEMP RESET
     &, TCREGT         ! ENGINE TEMPERATURE RESET
     &, TCRENGF        ! ENGINE FIRE RESET
     &, TCRENVI        ! ENVIRONMENT STANDARD RESET
     &, TCREOILQ       ! ENGINE OIL QUANTITY
     &, TCREOILT       ! ENGINE OIL TEMPERATURE
     &, TCRFIRX        ! FIRE BOTTLE
     &, TCRFUELT       ! FUEL TEMPERATURE
     &, TCRGEN         ! GENERATOR RESET FLAG
     &, TCRHFLQ        ! REFILL HYDRAULIC
     &, TCRHOILQ       ! HYDRAULIC OIL QUANTITY
     &, TCRHOILT       ! HYDRAULIC OIL TEMPERATURE
     &, TCRHQTY1       ! HYDRAULIC QTY SYS 1
     &, TCRHQTY2       ! HYDRAULIC QTY SYS 2
     &, TCRHQTY3       ! HYDRAULIC QTY SYS 3
     &, TCRHQTY4       ! HYDRAULIC QTY SYS 4
     &, TCRIDG         ! IDG RECONNECT
     &, TCRIDGQ        ! IDG OIL QUANTITY
     &, TCRIDGT        ! IDG OIL TEMPERATURE
     &, TCRMAINT       ! MAINTENANCE
     &, TCRMALF        ! ALL MALFUNCTION
     &, TCROUTCB       ! RESET OUTSIDE CB'S
     &, TCROXY         ! OXYGEN BOTTLE
     &, TCRPKTMP       ! PACK TEMPERATURE RESET
     &, TCRRAT         ! RESET RAM AIR TURINE
     &, TCRSPCL        ! SPECIAL EFFECTS RESET
     &, TCRSYST        ! ALL SYSTEM RESET
     &, TCRTCAS        ! TCAS RESET
     &, TCRTOT         ! TOTAL RESET
     &, TFEND          ! Last Discrete Malfunctions
     &, TFSTART        ! First Discrete Malfunctions
      LOGICAL*1
     &  TLSTART        ! LESSON START BUTTON
     &, TMHAUTO        ! MAP - AUTO MODE
     &, TMHICAO1(4)    ! MAP CENTER ICAO CODE
     &, TMHRWY1(4)     ! MAP CENTER RUNWAY NAME
     &, TMICAOSL(25)   ! MAP TO ICAO CODE SELECTION FLAG
     &, TMMILSON       ! ILS AVAILABLE  & SELECTED
     &, TRRAPON        ! PLAYBACK ON
     &, TRSETREC       ! SETUP RECALL ON
     &, TRSNPREC       ! SNAPSHOT RECALL ON
     &, VBOG           ! ON GROUND FLAG
     &, XPCALLAV       ! CLR ALL STN BUTT AVAIL
     &, XPCRTEX        ! CRT EXCHANGE
     &, XZABRKAC(6)    ! BRAKING ACTION DESCRIPTION
     &, XZAICING(8)    ! ICING DESCRIPTION
     &, XZARWYCO(10)   ! RUNWAY CONTAMINATION
     &, XZATDAY(10)    ! TIME OF DAY DESCRIPTION
     &, XZATURIN(8)    ! TURBULENCE INTENSITY
     &, XZATURTY(8)    ! TURBULENCE TYPE DESCRIPTION
     &, XZAWXR(6)      ! WEATHER DESCRIPTION
     &, XZHDRDES(20)   ! MAP HEADER DESCRIPTION
     &, XZLVFFRZ(10)   ! LOW VIS FLT FREEZE TRIGGER
     &, XZRWYDES(4,10) ! RUNWAY LIST DESCRIPTION
     &, XZRWYMAV       ! MORE RUNWAYS AVAILABLE
     &, XZRWYMOR       ! MORE RUNWAYS
     &, XZSMORAV       ! MORE RA STATIONS AVAILABLE
     &, XZSMORE        ! DISPLAYS MORE RA STATIONS
     &, XZSPECPG       ! SPECIAL PAGE IN EFFECT
     &, XZSTATUS(30)   ! AIRCRAFT STATUS DISPLAY
     &, XZSTHDR        ! DISPLAY STATUS HEADER
     &, XZSTNDES(36,4) ! FAILED STN SUMMARY DESCRIPTION
     &, XZTGRAPH       ! RESET TEMPERATURE GRAPH
      LOGICAL*1
     &  XZTRACK        ! MAP HEADER TRACK ERASE REQUEST
     &, XZWARN(30)     ! HEADER WARNINGS
     &, XZWGRAPH       ! RESET WIND GRAPH
     &, XZWRNRST(30)   ! HEADER WARNINGS RESET
     &, YIFREZ         ! Simulator Total Freeze Flag
     &, YXSTRTXRF      ! Start of CDB
C$
      INTEGER*1
     &  RVK(36,32)     ! KILLED STN NAME/TYPE
     &, RXMISIAT(4,5)  ! 69 IATA CODE (ASCII)
     &, RXMISTYN(5)    !  4 TYPE NUMBER
     &, RXRWYNAM(4,20) ! RWY FOR REF RWY AIRPORT
     &, TADATE(12)     ! DATE DISPLAY DD/MMM/YY
     &, TAICAO1(4)     ! DEPARTURE ICAO CODE
     &, TARWY1(4)      ! DEPARTURE RWY CODE
     &, TAVRWYNM(4,24) ! VISUAL RUNWAY NAME
     &, TAVSRWNM(4,24) ! VISUAL RUNWAY NAME
C$
      LOGICAL*1
     &  DUM0000001(8),DUM0000002(9516),DUM0000003(537)
     &, DUM0000004(499),DUM0000005(2179),DUM0000006(43)
     &, DUM0000007(1),DUM0000008(29),DUM0000009(8)
     &, DUM0000010(58),DUM0000011(24),DUM0000012(3)
     &, DUM0000013(3470),DUM0000014(871),DUM0000015(32)
     &, DUM0000016(512),DUM0000017(200),DUM0000018(640)
     &, DUM0000019(96),DUM0000020(252),DUM0000021(76)
     &, DUM0000022(668),DUM0000023(672),DUM0000024(2564)
     &, DUM0000025(2608),DUM0000026(5728),DUM0000027(40)
     &, DUM0000028(24),DUM0000029(124),DUM0000030(465)
     &, DUM0000031(32),DUM0000032(3171),DUM0000033(3168)
     &, DUM0000034(48),DUM0000035(603),DUM0000036(144)
     &, DUM0000037(588),DUM0000038(572),DUM0000039(4744)
     &, DUM0000040(148),DUM0000041(24),DUM0000042(54)
     &, DUM0000043(1127),DUM0000044(1060),DUM0000045(1256)
     &, DUM0000046(1004),DUM0000047(120),DUM0000048(25756)
     &, DUM0000049(85),DUM0000050(120),DUM0000051(150)
     &, DUM0000052(250),DUM0000053(18941),DUM0000054(872)
     &, DUM0000055(3257),DUM0000056(2729),DUM0000057(2574)
     &, DUM0000058(461),DUM0000059(38),DUM0000060(51)
     &, DUM0000061(28),DUM0000062(80),DUM0000063(2)
     &, DUM0000064(15479),DUM0000065(145),DUM0000066(190)
     &, DUM0000067(21),DUM0000068(3),DUM0000069(1)
     &, DUM0000070(2),DUM0000071(21),DUM0000072(2)
     &, DUM0000073(1),DUM0000074(9),DUM0000075(174626)
     &, DUM0000076(8305),DUM0000077(6),DUM0000078(34)
     &, DUM0000079(1),DUM0000080(1),DUM0000081(3),DUM0000082(1)
     &, DUM0000083(1),DUM0000084(3),DUM0000085(1),DUM0000086(10)
     &, DUM0000087(2),DUM0000088(1),DUM0000089(1),DUM0000090(1)
     &, DUM0000091(3),DUM0000092(13),DUM0000093(1)
     &, DUM0000094(5),DUM0000095(7),DUM0000096(7),DUM0000097(2)
      LOGICAL*1
     &  DUM0000098(2),DUM0000099(6),DUM0000100(1),DUM0000101(1)
     &, DUM0000102(17),DUM0000103(11),DUM0000104(1)
     &, DUM0000105(33),DUM0000106(3),DUM0000107(9)
     &, DUM0000108(41),DUM0000109(71),DUM0000110(40)
     &, DUM0000111(5944),DUM0000112(26),DUM0000113(3)
     &, DUM0000114(4),DUM0000115(27),DUM0000116(1)
     &, DUM0000117(5),DUM0000118(296),DUM0000119(11)
     &, DUM0000120(4),DUM0000121(2),DUM0000122(11)
     &, DUM0000123(14),DUM0000124(4),DUM0000125(40)
     &, DUM0000126(10),DUM0000127(14),DUM0000128(16)
     &, DUM0000129(8),DUM0000130(32),DUM0000131(12)
     &, DUM0000132(64),DUM0000133(32),DUM0000134(52)
     &, DUM0000135(4),DUM0000136(40),DUM0000137(16)
     &, DUM0000138(164),DUM0000139(16),DUM0000140(16)
     &, DUM0000141(20),DUM0000142(40),DUM0000143(12)
     &, DUM0000144(8),DUM0000145(2),DUM0000146(14)
     &, DUM0000147(8),DUM0000148(16),DUM0000149(8)
     &, DUM0000150(12),DUM0000151(44),DUM0000152(34)
     &, DUM0000153(36),DUM0000154(36),DUM0000155(8)
     &, DUM0000156(4),DUM0000157(88),DUM0000158(54)
     &, DUM0000159(603),DUM0000160(4),DUM0000161(29)
     &, DUM0000162(51),DUM0000163(162),DUM0000164(188)
     &, DUM0000165(8),DUM0000166(420),DUM0000167(1)
     &, DUM0000168(98),DUM0000169(11),DUM0000170(3)
     &, DUM0000171(4),DUM0000172(5),DUM0000173(45)
     &, DUM0000174(23),DUM0000175(1),DUM0000176(1450)
     &, DUM0000177(276),DUM0000178(482),DUM0000179(69)
     &, DUM0000180(480),DUM0000181(14311)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,DUM0000001,YIFREZ,DUM0000002,AM$CRST,DUM0000003
     &, BP0,DUM0000004,BP9999,DUM0000005,IDDBSBI,IDDBSB2,DUM0000006
     &, IDESI1,DUM0000007,IDESI2,DUM0000008,IDESTOP,DUM0000009
     &, IDESAF,DUM0000010,IDAHDP,IDAHDP2,DUM0000011,IDAWS1,IDAWS2
     &, IDAWS3,IDAWS4,IDAWS5,IDAWS6,DUM0000012,IDAFP,IDAFP2,DUM0000013
     &, VBOG,DUM0000014,VUG,DUM0000015,VM,DUM0000016,VVNS,VVEW
     &, DUM0000017,VH,DUM0000018,VACST,DUM0000019,VOTEMP,DUM0000020
     &, VTOTWIND,DUM0000021,VATURB,DUM0000022,VREF,DUM0000023
     &, VTRIM,DUM0000024,UWCAS,DUM0000025,CIETSPOS,DUM0000026
     &, CELVL,CELVR,DUM0000027,CATRIM,DUM0000028,CRTRIM,DUM0000029
     &, CWCYLMT,DUM0000030,SLAAPENG,DUM0000031,SLAPENG,DUM0000032
     &, VSAELE,DUM0000033,RUPLAT,RUPLON,DUM0000034,RUFLT,DUM0000035
     &, RTAVAR,DUM0000036,RANDBIDX,DUM0000037,RBILSIDX,DUM0000038
     &, RBVORIDX,DUM0000039,RBSVOR,DUM0000040,RBSILS,DUM0000041
     &, RBCGGPX,DUM0000042,RBFVOI,DUM0000043,RVRTD,DUM0000044
     &, RWSLINE,RVK,RVNKIL,DUM0000045,RXBRWYS,DUM0000046,RXARPT
     &, DUM0000047,RXRWYNAM,DUM0000048,RXMISELE,RXMISTYN,DUM0000049
     &, RXMISFRE,DUM0000050,RXMISGPX,DUM0000051,RXMISRWE,DUM0000052
     &, RXMISIAT,DUM0000053,DBFBI,DUM0000054,DOFAI,DUM0000055
     &, EFDESIN1,EFDESIN2,DUM0000056,AHFACC,DUM0000057,TLNST
     &, DUM0000058,TLSTART,DUM0000059,TLTIME,DUM0000060,TMHAUTO
     &, DUM0000061,TMHTUNED,DUM0000062,TMICAOSL,TMHICAO1,TMHRWY1
     &, TMMILSON,DUM0000063,TARVRTD,DUM0000064,XPCRTEX,XPCALLAV
     &, DUM0000065,XZATURTY,XZATURIN,XZAICING,XZABRKAC,XZARWYCO
     &, XZATDAY,XZAWXR,DUM0000066,XZSTHDR,XZTGRAPH,XZWGRAPH,DUM0000067
     &, XZSMORE,DUM0000068,XZSMORAV,DUM0000069,XZSTNINX,XZSTNDES
     &, XZLVFFRZ,DUM0000070,XZSPECPG,DUM0000071,XZSPDDEV,XZWXR
     &, DUM0000072,XZPRESET,XZPOSN,XZRWYDES,XZRWYMOR,XZRWYMAV
     &, XZTRACK,XZHDRDES,DUM0000073,XZMBXOFF,XZMBYOFF,XZMBSCAL
     &, TAMBYPOS,TAMBXPOS,TAMBRAD,DUM0000074,XZSTATUS,XZWARN
      COMMON   /XRFTEST   /
     &  XZWRNRST,DUM0000075,TRSETREC,TRSNPREC,DUM0000076,TCFTOT
     &, TCFFLPOS,TCFALT,TCFIAS,TCFHDG,DUM0000077,TCFPOS,TCFFUEL
     &, DUM0000078,TCRTOT,TCRMALF,DUM0000079,TCRSYST,TCRALLQ
     &, TCRALLT,TCRMAINT,DUM0000080,TCRASH,TCRENVI,TCRSPCL,TCROUTCB
     &, DUM0000081,TCRBATT,TCRFIRX,TCROXY,DUM0000082,TCRIDG,TCRIDGT
     &, TCRIDGQ,TCRHQTY1,TCRHQTY2,TCRHQTY3,TCRHQTY4,TCRHOILQ
     &, TCRHOILT,TCRAEGT,TCRARPM,TCRAOILQ,TCRAOILT,TCRFUELT,DUM0000083
     &, TCRACOND,TCRACONS,TCRACTAT,DUM0000084,TCRCBPRE,DUM0000085
     &, TCRCSD,TCRCSDOQ,TCRCSDOT,DUM0000086,TCRBRKT,DUM0000087
     &, TCRRAT,DUM0000088,TCRALLST,DUM0000089,TCRENGF,TCRCARF
     &, TCRHFLQ,TCRGEN,DUM0000090,TCREGT,TCREOILQ,TCREOILT,DUM0000091
     &, TCRTCAS,DUM0000092,TCF0FLPO,TCR0TOT,TCR0MALF,TCR0AMAL
     &, TCR0SYST,TCR0ALLQ,TCR0ALLT,TCR0MAIN,TCR0OILQ,TCR0ASH
     &, TCR0ENVI,TCR0OUTC,TCR0SPCL,TCR0MFCL,TCR0VISY,TCR0BATT
     &, TCR0FIRX,TCR0OXY,TCR0PASO,TCR0IDG,TCR0IDGT,TCR0IDGQ,TCR0HQT1
     &, TCR0HQT2,TCR0HQT3,TCR0HQT4,TCR0HOLQ,TCR0HOLT,TCR0AEGT
     &, TCR0ARPM,TCR0AOLQ,TCR0AOLT,TCR0FUEL,TCR0ADG,TCR0ACND
     &, TCR0ACON,TCR0ACTA,TCR0ECSF,TCR0ELEC,TCR0LG,TCR0SHTS,TCR0CBPR
     &, TCR0CBPA,TCR0CSD,TCR0CSDQ,TCR0CSDT,TCR0EMAB,TCR0TEFL
     &, TCR0WNDS,TCR0OILT,TCR0STBH,TCR0TIRB,TCR0TIRT,TCR0TIRP
     &, TCR0BRKT,TCR0BRAC,TCR0ABRK,TCR0RAT,TCR0ATNK,TCR0ALLS
     &, TCR0ICE,TCR0ENGF,TCR0CARF,TCR0HFLQ,TCR0GEN,TCR0ECAM,TCR0EGT
     &, TCR0EOLQ,TCR0EOLT,TCR0EOFF,TCR0BITE,TCR0FMSR,TCR0FMS
     &, TCR0TCAS,TCR0CGCC,TCR0FIRE,TCR0PNEU,TCR0CABT,TCR0PWRF
     &, TCR0CBPRE,TCMSHEAR,DUM0000093,TCMBURST,TCMCAT,TCMTURLO
     &, TCMSTORM,DUM0000094,TCMCHATM,DUM0000095,TCMICE,DUM0000096
     &, TCMDRWY,DUM0000097,TCMPIRWY,DUM0000098,TCMICRWY,DUM0000099
     &, TCMPNEU1,TCMPNEU2,TCMELEC1,TCMELEC2,DUM0000100,TCMACJAX
     &, TCMCHKS,DUM0000101,TCMPBLT,TCMPBRT,DUM0000102,TCMVASI
     &, DUM0000103,TCMSCUD,DUM0000104,TCMPFOG,TCMGFOG,DUM0000105
     &, TCMALLTS,DUM0000106,TCMAUTBR,DUM0000107,TCMSARWL,DUM0000108
      COMMON   /XRFTEST   /
     &  TCMATRLT,TCMATRRT,TCMATRCA,TCMATRCB,DUM0000109,TAAPRBRT1
     &, TATDZLT1,TARWYEDG1,TASTROBE1,TAVASI1,DUM0000110,TARWYMID1
     &, TAALLRWY1,TAVRWYIDX,DUM0000111,TCMRWYSL,TCMRWYAV,DUM0000112
     &, TCMRWYOK,TCMRAOK,DUM0000113,TAPAGE,TAPAGERC,DUM0000114
     &, TCMSLEWUP,TCMSLEWDN,TCMSLEWLT,TCMSLEWRT,TCMSLEWF,TCM0SLWUP
     &, TCM0SLWDN,TCM0SLWLT,TCM0SLWRT,TCM0SLEWF,DUM0000115,TCMADOR
     &, TCMDOOR,DUM0000116,TCMARMDO,DUM0000117,TCMAVHDG,TCMAVIAS
     &, DUM0000118,TCMETRIC,TCMCRINB,DUM0000119,TCMRFRWY,DUM0000120
     &, TCM0PNEU,DUM0000121,TCM0ACJX,TCM0CHKS,TCM0PUSH,TCM0CRIN
     &, DUM0000122,TCM0RAIN,DUM0000123,TCM0ACGW,TCM0ACFL,TCM0ACCG
     &, DUM0000124,TAFLVL,DUM0000125,TATEMP,TAWSPD,TAWDIR,DUM0000126
     &, TAMBPROF,TAWSPROF,DUM0000127,TAWSGAIN,TAGUST,TAQNH,DUM0000128
     &, TATURB,TAMBXOFF,TAMBYOFF,DUM0000129,TAMBGAIN,DUM0000130
     &, TAIAS,TAALT,TAHDG,DUM0000131,TAHDGSET,TAHDGMAG,DUM0000132
     &, TAIWDIR,TAIWSPD,TAITEMP,TAIFLVL,TATWDIR,TATWSPD,TATTEMP
     &, TATFLVL,DUM0000133,TATLPSRT,DUM0000134,TAICEQTY,DUM0000135
     &, TARWYICE,TARWYWET,TARWYSNW,TARWYSLH,TARFRWY,TABRAKE,DUM0000136
     &, TARWBARX,TARWBARY,TACGBARX,TACGBARY,TAGW,TAGWMIN,TAGWMAX
     &, TACG,TACGMIN,TACGMAX,DUM0000137,TAFUEL,TAFUELMN,TAFUELMX
     &, DUM0000138,TAVISIB,TARVR,TARVR1,TARVR2,TARVR3,TACEILNG
     &, TACLDTOP,TAFOGTOP,DUM0000139,TAVMODE,DUM0000140,TAALLBRT
     &, TAENVBRT,DUM0000141,TAAPRBRT,DUM0000142,TAVASI,DUM0000143
     &, TAHORBRT,DUM0000144,TATAXI,TAAIRBRT,TARMPBRT,DUM0000145
     &, TALTGBRT,TASKYBRT,TAVSWTCH,TADOKBRT,TADCSBRT,DUM0000146
     &, TAAIRPRT,DUM0000147,TAVRWYNM,DUM0000148,TAVSRWNM,DUM0000149
     &, TAFOGPAT,DUM0000150,TAFOGTL,DUM0000151,TAHAZ,DUM0000152
     &, TAMISFRE,DUM0000153,TAREFRUN,TAPOSN,DUM0000154,TASLEWNS
     &, TASLEWEW,DUM0000155,TAICAO1,DUM0000156,TARWY1,DUM0000157
     &, TATIMEL,TATIMES,DUM0000158,TADATE,DUM0000159,TCMTMRST
     &, DUM0000160,TCMPOSL,TCMHDGSL,TCMALTSL,DUM0000161,TCMSPARE
     &, DUM0000162,TCM0SCN,DUM0000163,TASOUND,DUM0000164,TATCCRS
      COMMON   /XRFTEST   /
     &  TATCNAC,DUM0000165,TASPDUP,DUM0000166,TASPARE,DUM0000167
     &, TRRAPON,DUM0000168,TCATMFL,DUM0000169,TCATMFP,DUM0000170
     &, TCATMHP,DUM0000171,TCATMSB,DUM0000172,TCATMBLS,DUM0000173
     &, TCM0ELC1,DUM0000174,TCRBTMP,TCR0BTMP,TCRPKTMP,TCR0PKTM
     &, TCMVRWSL,TCMVRWAV,TCM0VRID,TAVRWID,TCMVRWIN,TAVRWINP
     &, DUM0000175,TAVRWIND,TAVRWAPP,TAVRWTDZ,TAVRWEDG,TAVRWVAS
     &, TAVRWTAX,TAVRWSTR,TAVRWCEN,TAVRWALL,TAVRWLT,TAVRWHOR
     &, TAVRWENV,TADSPAPP,TADSPTDZ,TADSPEDG,TADSPVAS,TADSPTAX
     &, TADSPSTR,TADSPCEN,TADSPALL,TADSPRW,TADSPHOR,TADSPENV
     &, TAEDTAPP,TAEDTTDZ,TAEDTEDG,TAEDTVAS,TAEDTTAX,TAEDTSTR
     &, TAEDTCEN,TAEDTALL,TAEDTRW,TAEDTHOR,TAEDTENV,DUM0000176
     &, TVSTART,DUM0000177,TVEND,TFSTART,DUM0000178,TFEND,T1START
     &, DUM0000179,T1END,T0START,DUM0000180,T0END,DUM0000181
     &, RFRAUDIO  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  XOPOPUP(60)    ! ASSOCIATED BOOLEAN TO POP WINDOW
     &, XSARM(50)      ! ARMED MALFUNCTION FLAGS
     &, XSPRUPD(2)     ! UPDATE PRESELECT FLAG
C$
      LOGICAL*1
     &  DUM0200001(15177),DUM0200002(63),DUM0200003(20942)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,XOPOPUP,DUM0200002,XSPRUPD,DUM0200003,XSARM     
C------------------------------------------------------------------------------
C
C
C --- Equivalences
C
      EQUIVALENCE
     &         (TFMALFS,  TFSTART),
     &         (TVMALFS,  TVSTART),
     &         (BPMALFS,  BP0),
     &         (T0MALFS,  T0START),
     &         (T1MALFS,  T1START),
     &         (DUMI2,    DUMI4),
     &         (I4TARWY1, TARWY1),
     &         (I4RWYNAM, RXRWYNAM),
     &         (C_RXRWYNAM, RXRWYNAM),
     &         (I4RWYDES, XZRWYDES),
     &         (ICAO,     TAICAO1),
     &         (C_TM_ICAO1, TMHICAO1),
     &         (C_ICAO,   TAICAO1),
     &         (TARWY1,   C_RWY1),
     &         (C_TM_RWY1,  TMHRWY1),
     &         (TAVRWID(1,1), C_TAVRWID(1)),
     &         (TAVRWINP, C_CURRWY),
     &         (RXMISIAT(1,3), REFRWY),
     &         (TADATE,     DATE),
     &         (TIME_DAY,   XZATDAY),
     &         (WEATHER,    XZAWXR ),
     &         (BRAKING,    XZABRKAC),
     &         (TURB_TYP,   XZATURTY),
     &         (TURB_INT,   XZATURIN),
     &         (ICING,      XZAICING),
     &         (RWY_CONT,   XZARWYCO)
C
C --- USD8 RECEIVERS
C
      EQUIVALENCE  ( TUNED      , TMHTUNED      )
      EQUIVALENCE  ( ILS_SGNL_1 , RBSILS(1)     ),
     -             ( ILS_SGNL_2 , RBSILS(2)     ),
     -             ( ILS_INDX_1 , RBILSIDX(1)   ),
     -             ( ILS_INDX_2 , RBILSIDX(2)   ),
     -             ( NDB_SGNL_1 , RFRAUDIO(1)   ),
     -             ( NDB_SGNL_2 , RFRAUDIO(2)   ),
     -             ( NDB_INDX_1 , RANDBIDX(1)   ),
     -             ( NDB_INDX_2 , RANDBIDX(2)   ),
     -             ( VOR_SGNL_1 , RBSVOR(1)     ),
     -             ( VOR_SGNL_2 , RBSVOR(2)     ),
     -             ( VOR_INDX_1 , RBVORIDX(1)   ),
     -             ( VOR_INDX_2 , RBVORIDX(2)   )
C
C
C
C       XZWXR =   1        2       3       4        5      6      7      8
C                CAVOK    CAT_I   CAT_II  CAT_III  0/0    T/O   CLEAR    NP
 
C PLEM-2ULD This is now controlled in VISI.C
C PLEM      DATA
C PLEM     & VIS_VIS /43.0,0.37272,0.21742,0.12424,0.000,0.12400,
C PLEM     &          43.0,2.30154/,
C PLEM     & VIS_RVR /32480,1968.0,1148.0,656.0,0.000,600.0,
C PLEM     &          0.0,12152.2/,
C PLEM     & VIS_RVR1/32480,1968.0,1148.0,656.0,0.000,600.0,
C PLEM     &          0.0,12152.2/,
C PLEM     & VIS_RVR2/32480,1968.0,1148.0,656.0,0.000,600.0,
C PLEM     &          0.0,12152.2/,
C PLEM     & VIS_RVR3/32480,1968.0,1148.0,656.0,0.000,600.0,
C PLEM     &          0.0,12152.2/,
C
C PLEM     & VIS_CEIL/      0,    200,    100,     50,    0,     50,
C PLEM     &                0,    800    /,
C + s81-2-xxx
C PLEM    & VIS_CLDT/      0,   6000,   6000,   6000,    0,   2000,
C - s81-2-xxx
C PLEM     &                0,   3000    /,
C
C PLEM     & VIS_FGTOP/   500,    500,    500,    500,    0,    500,
C PLEM     &                0,    500    /,
C PLEM     & VIS_FGTL/    200,    200,    200,    200,    0,    200,
C PLEM     &                0,    200    /,
C PLEM     & VIS_FGPAT/     0,      0,      0,      0,    0,      0,
C PLEM    &                0,      0    /,
C
C PLEM     & VIS_APRB/      3,      5,      5,      5,    3,      5,
C PLEM     &                5,      3    /,
C PLEM     & VIS_RWB /      3,      5,      5,      5,    3,      5,
C PLEM     &                5,      3    /,
C PLEM     & VIS_VASI/      2,      5,      5,      5,    2,      5,
C PLEM     &                5,      2    /,
C PLEM     & VIS_HORB/      3,      3,      3,      3,    3,      5,
C PLEM     &                5,      3    /,
C PLEM     & VIS_ENVB/      3,      3,      3,      3,    3,      5,
C PLEM     &                5,      3    /,
C PLEM     & VIS_TAXI/      5,      5,      5,      5,    5,      5,
C PLEM     &                5,      5    /,
C
C PLEM     & VIS_SCUD/      F,      F,      F,      F,    F,      F,
C PLEM     &                F,      F    /,
C PLEM     & VIS_GFOG/      F,      T,      T,      T,    F,      T,
C PLEM     &                F,      T    /,
C PLEM     & VIS_PFOG/      F,      F,      F,      F,    F,      F,
C PLEM     &                F,      F    /
C
      DATA
C
     & PAGE_RESET/   25,  25,  29,  61,  41,  42,  32,
     &               34,  35,  35,   7,  35,  32,  35,
     &               35  /
C
C
C --- PROGRAM START
C
      ENTRY XZMISC
C     ============
C
C  Find Codes:
C
C      FIND_IF :   Miscellaneous I/F
C      FIND_RA :   Radio Aids
C      FIND_FS :   Fail Summary
C      FIND_AC :   Aircraft Maintenance
C      FIND_EC :   Environment Control
C      FIND_FR :   Freeze/Resets
C      FIND_VC :   Visual Control
C
C
C *************************************************************************
C *************************************************************************
C
C   F I R S T    P A S S
C
C *************************************************************************
C *************************************************************************
C
      IF (FIRSTPASS) THEN
C
C     -- Get offsets specifying start and end of malfunction and
C        CB groupings, and determine number of elements of each type
C
         STARTXREF    = ADDR(YXSTRTXRF)
         TVSTOFF      = ADDR(TVSTART)  - STARTXREF
         TVENDOFF     = ADDR(TVEND)    - STARTXREF
         TFSTOFF      = ADDR(TFSTART)  - STARTXREF
         TFENDOFF     = ADDR(TFEND)    - STARTXREF
         BPSTOFF      = ADDR(BP0)      - STARTXREF
         BPENDOFF     = ADDR(BP9999)   - STARTXREF
         T0STOFF      = ADDR(T0START)  - STARTXREF
         T0ENDOFF     = ADDR(T0END)    - STARTXREF
         T1STOFF      = ADDR(T1START)  - STARTXREF
         T1ENDOFF     = ADDR(T1END)    - STARTXREF
         NUMTVS       = (TVENDOFF - TVSTOFF)*0.25
         NUMTFS       = (TFENDOFF - TFSTOFF)
         NUMBPS       = (BPENDOFF - BPSTOFF)
         NUMT0S       = (T0ENDOFF - T0STOFF)
         NUMT1S       = (T1ENDOFF - T1STOFF)
C
C ---    Temporarily setting gray concept flags to true for testing
C        purpose, this code will have to be removed when SYSTEM S/W
C        are installed on site
C
C'USD8         DO I=0, NUMT0S
C'USD8            T0MALFS(I) = .TRUE.
C'USD8         ENDDO
C'USD8         DO I=0, NUMT1S
C'USD8            T1MALFS(I) = .TRUE.
C'USD8         ENDDO
C
C     -- Set up reset delay flags for various resets
C
         ADF_TOTR(1)   = ADDR(TCRALLT)       ! Total Reset
         ADF_TOTR(2)   = ADDR(TCRALLQ)
         ADF_TOTR(3)   = ADDR(TCRSYST)
         ADF_TOTR(4)   = ADDR(TCRENVI)
         ADF_TOTR(5)   = ADDR(TCRMALF)
         ADF_TOTR(6)   = ADDR(TCRASH)
         ADF_TOTR(7)   = ADDR(TCRCSDOQ)
         ADF_TOTR(8)   = ADDR(TCRSPCL)
         ADF_TOTR(9)   = ADDR(TCRALLST)
         ADF_TOTR(10)  = ADDR(TCRTOT)
C !FM+
C !FM  15-Jun-92 11:34:28 M.WARD
C !FM    < RESET TCAS INTRUDER ON TOTAL RESET >
C !FM
         ADF_TOTR(11)  = ADDR(TCRTCAS)
         NMF_TOTR      = 11
C !FM-
C
         ADF_ALLT(1)  = ADDR(TCREOILT)    ! All temp reset
         ADF_ALLT(2)  = ADDR(TCRHOILT)
         ADF_ALLT(3)  = ADDR(TCRAOILT)
         ADF_ALLT(4)  = ADDR(TCRAEGT)
         ADF_ALLT(5)  = ADDR(TCRFUELT)
         ADF_ALLT(6)  = ADDR(TCREGT)
         ADF_ALLT(7)  = ADDR(TCRBRKT)
         ADF_ALLT(8)  = ADDR(TCRACOND)
         ADF_ALLT(9)  = ADDR(TCRACONS)
         ADF_ALLT(10) = ADDR(TCRIDGT)
         ADF_ALLT(11) = ADDR(TCRCSDOT)
         ADF_ALLT(12) = ADDR(TCRACTAT)
         ADF_ALLT(13) = ADDR(TCRBTMP)
         ADF_ALLT(14) = ADDR(TCRPKTMP)
         ADF_ALLT(15) = ADDR(TCRALLT)
         NMF_ALLT     = 15
C
         ADF_SYST(1)  = ADDR(TCRIDG)      ! system reset
         ADF_SYST(2)  = ADDR(TCRBATT)
         ADF_SYST(3)  = ADDR(TCRCBPRE)
         ADF_SYST(4)  = ADDR(TCRALLQ)      ! (CHECK)
         ADF_SYST(5)  = ADDR(TCRARPM)
         ADF_SYST(6)  = ADDR(TCRAEGT)
         ADF_SYST(7)  = ADDR(TCRRAT)      ! Ram Air Turbine
         ADF_SYST(8)  = ADDR(TCRENGF)     ! Engine Fire Bootle
         ADF_SYST(9)  = ADDR(TCRGEN)      ! Generator
         ADF_SYST(10) = ADDR(TCRCARF)     ! Cargo fire extinguishing
         ADF_SYST(11) = ADDR(TCRIDG)
         ADF_SYST(12) = ADDR(TCRMAINT)
         ADF_SYST(13) = ADDR(TCRCSDOT)
         ADF_SYST(14) = ADDR(TCRCSD)
         ADF_SYST(15) = ADDR(TCRSYST)
         NMF_SYST     = 15
C
         ADF_ALLQ(1)  = ADDR(TCREOILQ)     ! All quantity reset
         ADF_ALLQ(2)  = ADDR(TCRAOILQ)
         ADF_ALLQ(3)  = ADDR(TCRHOILQ)
         ADF_ALLQ(4)  = ADDR(TCRHFLQ)
         ADF_ALLQ(5)  = ADDR(TCRHQTY1)
         ADF_ALLQ(6)  = ADDR(TCRHQTY2)
         ADF_ALLQ(7)  = ADDR(TCRHQTY3)
         ADF_ALLQ(8)  = ADDR(TCRHQTY4)
         ADF_ALLQ(9)  = ADDR(TCRIDGQ)
         ADF_ALLQ(10) = ADDR(TCRFIRX)
         ADF_ALLQ(11) = ADDR(TCROXY)
         ADF_ALLQ(12) = ADDR(TCRBATT)
         ADF_ALLQ(13) = ADDR(TCRARPM)
         ADF_ALLQ(14) = ADDR(TCRCBPRE)
         ADF_ALLQ(15) = ADDR(TCRALLQ)
         NMF_ALLQ     = 15
C
         TABRAKE = 1   ! BRAKING CONDITION
         TARFRWY = 1   ! RUNWAY ROUGHNESS
         TAMBGAIN = 0  ! MICROBURST INTENSITY (0-5)
C
         O_XZMBXOFF = XZMBXOFF
         O_XZMBYOFF = XZMBYOFF
         O_TAMBXOFF = TAMBXOFF
         O_TAMBYOFF = TAMBYOFF
C
         O_TACGBARX = TACGBARX ! Input label for CG on TIGERS page
         O_TARWBARX = TARWBARX ! Input label for rwy rough on TIGERS
C
         TAHAZ = 341 ! No hazards
         TAVRWIND = 0
         TCMCRINB = .TRUE.
C
         DO I=1,8
           TCM0ACGW(I) = .TRUE.
           TCM0ACFL(I) = .TRUE.
           TCM0ACCG(I) = .TRUE.
         ENDDO
C
         TAGW     = 30000
         TACG     = 0.27
C'USD8         O_TAGW   = TAGW
C'USD8         O_TAFUEL = TAFUEL
C'USD8         O_TACG   = TACG
C
C'USD8+
         TCR0TOT  = .TRUE.   ! Available at all times
         TCR0ALLT = .TRUE.
         TCR0ALLQ = .TRUE.
         O_XPCRTEX = XPCRTEX
C'USD8-
C 2ULD-PLEM+    TCAS Dark Concept
         TCM0SCN(1) = .TRUE.
         TCM0SCN(2) = .TRUE.
         TCM0SCN(3) = .TRUE.
         TCM0SCN(4) = .TRUE.
         TCM0SCN(5) = .TRUE.
C 2ULD-PLEM-
         FIRSTPASS = .FALSE.
      ENDIF
C
C
C FIND_IF
C *************************************************************************
C *************************************************************************
C
C  I N S T R U C T O R     F A C I L I T I E S
C
C *************************************************************************
C *************************************************************************
C
C --------------------------------------------------------------------------
C --- CURRENT TIME OF DAY
C --------------------------------------------------------------------------
C
C --- Every 10 minutes or so, call routine to get time and store
C     in CDB label TATIMEL (time value in seconds).
C     Each pass, update the time value by this module's band width
C
      IF (TATIMEL .GT. OLD_TIMEL + 600) GET_TIME = .TRUE.
      IF (GET_TIME) THEN
         GET_TIME  = .FALSE.
         CALL GET_DATE(C9DATE,C8TIME)
         DATE(1:9) = C9DATE(1:9)
         TATIMEL   = ((ICHAR(C8TIME(1:1))-48)*10
     .               +(ICHAR(C8TIME(2:2))-48))*3600
     .              + ((ICHAR(C8TIME(4:4))-48)*10
     .               +(ICHAR(C8TIME(5:5))-48))*60
     .              +  ((ICHAR(C8TIME(7:7))-48)*10
     .               +(ICHAR(C8TIME(8:8))-48))
         OLD_TIMEL = TATIMEL
C
      ELSE
         TATIMEL = TATIMEL + BANDWIDTH
      ENDIF
C
C --- Lesson plan time display
C
      IF ( TLNST .GT. 1.0 ) THEN
        TLTIME = TLTIME + BANDWIDTH
      ELSE
        TLTIME = 0.0
      ENDIF
C
C --------------------------------------------------------------------------
C -- STOPWATCH HANDLING
C --------------------------------------------------------------------------
C
C --- When input on stopwatch, determine current state, and either
C     start, stop, or rest the stopwatch
C
      IF ( TCMTMRST ) THEN
         TCMTMRST = .FALSE.
         IF ( TMR_STOP ) THEN           ! if stopped, reset timer
            TMR_RESET = .TRUE.
            TMR_STOP  = .FALSE.
            TMR_START = .FALSE.
         ELSE IF ( TMR_START ) THEN     ! if started, stop timer
            TMR_RESET = .FALSE.
            TMR_STOP  = .TRUE.
            TMR_START = .FALSE.
         ELSE IF ( TMR_RESET ) THEN     ! if reset, start timer
            TMR_RESET = .FALSE.
            TMR_STOP  = .FALSE.
            TMR_START = .TRUE.
         ENDIF
      ENDIF
C
      IF ( TMR_START ) TATIMES = TATIMES + BANDWIDTH
C
      IF ( TMR_RESET ) TATIMES = 0.0
C
C --------------------------------------------------------------------------
C  LOGIC FOR XZPRESET
C --------------------------------------------------------------------------
C
C      IF (XZPRESET.GT.1000) THEN
C          IF (XZPRESET .EQ. 1002) THEN
C              TARWYSNW = 1
C              TCMDRWY  = .FALSE.
C              TARWYWET = 0
C              TARWYSLH = 0
C              TARWYICE = 0
C              TAALLBRT = 5
C              TCMALLTS = .TRUE.
C          ENDIF
C          XZPRESET = 0
C      ENDIF
C
C
C ------------------------------------------------------------------------
C LOGIC FOR WARNING MESSAGES ON UPPER CRT HEADER
C ------------------------------------------------------------------------
C
      XZWARN(1) = ( TAVISIB  .LT. 0.21742    ! Weather below CAT II
     .    .OR.      ( TARVR    .GT. 0 .AND. TARVR    .LT. 1148)
     .    .OR.      ( TACEILNG .GT. 0 .AND. TACEILNG .LT. 100 ) )
C
      XZWARN(2) = ( VTOTWIND .GT. 40.0
     .    .OR.      VATURB   .GT. 0.0 )! High wind/turb
C
      XZWARN(3) = ( TATCNAC .GT. 0 )   ! TCAS Traffic
C
      XZWARN(4) = ( RVNKIL .GT. 0 )    ! Station failed
C
      XZWARN(5) = ( TARFRWY .GT. 1  .OR. .NOT. TCMDRWY) ! RWY rough/contam
C
      XZWARN(6) = ( ((ABS(TATURB(1)) .GT. 0.01) .AND.  ! Severe WX
     .               (TCMCAT.OR.TCMTURLO.OR.TCMSTORM))
     .    .OR.      TAMBPROF .NE. 0
     .    .OR.      TAWSPROF .NE. 0 )
C
      XZWARN(7) = ( TAGW .GT. 34700 .OR.
     .              TACG .GT. 0.38 )        ! High GW/CG
C
      XZWARN(8) = ( TCMACJAX .OR. TCMCHKS)  ! A/C on jacks/chocks
C
      XZWARN(9) = TCR0MAIN   ! Maintenance required
C
      XZWARN(10) = ( TCR0EOLQ .OR.       ! Engine oil
     .               TCR0AOLQ .OR.       ! APU oil
     .               TCR0HOLQ .OR.       ! Hydraulic oil
     .               TCR0HFLQ .OR.       ! Hydraulic reservoirs
     .               TCR0HQT1 .OR.
     .               TCR0HQT2 .OR.
     .               TCR0HQT3 .OR.
     .               TCR0HQT4 .OR.
     .               TCR0IDGQ )
C
      XZWARN(11) = TLNST .GT. 1.0
C
      XZWARN(12) = ( TCFTOT .OR. TCFFLPOS
     .    .OR.       TCFPOS .OR. TCFFUEL
     .    .OR.       TCFALT .OR. TCFIAS
     .    .OR.       TCFHDG )
C
      XZWARN(13) =   TASOUND .LT. 100.0
C
      XZWARN(18) = ( XZWARN(1) .OR. XZWARN(2) .OR. XZWARN(3)   ! WX warn
     .    .OR.       XZWARN(4) .OR. XZWARN(5) .OR. XZWARN(6) )
C
      XZWARN(19) = ( XZWARN(7) .OR. XZWARN(8)    ! A/C warn
     .    .OR.       XZWARN(9) .OR. XZWARN(10) )
C
      XZWARN(20) = ( XZWARN(11) .OR. XZWARN(12)  ! SIM CTRL
     .    .OR.       XZWARN(13) )
C
C --- Warning reset logic
C
      DO I=1,15
        IF ( XZWRNRST(I) ) THEN
          XZWRNRST(I) = .FALSE.
          TAPAGE(1)   = PAGE_RESET(I)
        ENDIF
      ENDDO
C
C --- CRT exchange logic for USD8
C
      IF ( XPCRTEX .NEQV. O_XPCRTEX ) THEN
        UPPER_PG = TAPAGERC(1,1)
        LOWER_PG = TAPAGERC(1,2)
        TAPAGERC(1,1) = LOWER_PG
        TAPAGERC(1,2) = UPPER_PG
        O_XPCRTEX = XPCRTEX
      ENDIF
C
C --- A/C quick set page dark concept logic
C
C'USD8      IF ( TAGW   .NE. O_TAGW   .OR.
C'USD8     .     TAFUEL .NE. O_TAFUEL .OR.
C'USD8     .     TACG   .NE. O_TACG ) THEN
C
C'USD8        IF ( ABS(TAGW - 28000)  .LT. 2.0 .OR.
C'USD8     .       ABS(TAFUEL - 2000) .LT. 2.0 .OR.
C'USD8     .       ABS(TACG - 0.15)   .LT. 0.01 ) THEN
C'USD8C
C'USD8          TCM0ACGW(8) = ABS(TAGW - 28000)  .GE. 2.0
C'USD8          TCM0ACFL(8) = ABS(TAFUEL - 2000) .GE. 2.0
C'USD8          TCM0ACCG(8) = ABS(TACG - 0.15)   .GE. 0.01
C'USD8          DO I=1,7
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8        ELSEIF ( ABS(TAGW - 29000)  .LT. 2.0 .OR.
C'USD8     .           ABS(TAFUEL - 2500) .LT. 2.0 .OR.
C'USD8     .           ABS(TACG - 0.18)   .LT. 0.01 ) THEN
C'USD8C
C'USD8          TCM0ACGW(7) = ABS(TAGW - 29000)  .GE. 2.0
C'USD8          TCM0ACFL(7) = ABS(TAFUEL - 2500) .GE. 2.0
C'USD8          TCM0ACCG(7) = ABS(TACG - 0.18)   .GE. 0.01
C'USD8          DO I=1,6
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8          TCM0ACGW(8) = .FALSE.
C'USD8          TCM0ACFL(8) = .FALSE.
C'USD8          TCM0ACCG(8) = .FALSE.
C'USD8        ELSEIF ( ABS(TAGW - 30000)  .LT. 2.0 .OR.
C'USD8     .           ABS(TAFUEL - 3000) .LT. 2.0 .OR.
C'USD8     .           ABS(TACG - 0.21)   .LT. 0.01 ) THEN
C'USD8C
C'USD8          TCM0ACGW(6) = ABS(TAGW - 30000)  .GE. 2.0
C'USD8          TCM0ACFL(6) = ABS(TAFUEL - 3000) .GE. 2.0
C'USD8          TCM0ACCG(6) = ABS(TACG - 0.21)   .GE. 0.01
C'USD8          DO I=1,5
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8          DO I=7,8
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8        ELSEIF ( ABS(TAGW - 31000)  .LT. 2.0 .OR.
C'USD8     .           ABS(TAFUEL - 3500) .LT. 2.0 .OR.
C'USD8     .           ABS(TACG - 0.24)   .LT. 0.01 ) THEN
C'USD8C
C'USD8          TCM0ACGW(5) = ABS(TAGW - 31000)  .GE. 2.0
C'USD8          TCM0ACFL(5) = ABS(TAFUEL - 3500) .GE. 2.0
C'USD8          TCM0ACCG(5) = ABS(TACG - 0.24)   .GE. 0.01
C'USD8          DO I=1,4
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8          DO I=6,8
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8        ELSEIF ( ABS(TAGW - 32000)  .LT. 2.0 .OR.
C'USD8     .           ABS(TAFUEL - 4000) .LT. 2.0 .OR.
C'USD8     .           ABS(TACG - 0.27)   .LT. 0.01 ) THEN
C'USD8C
C'USD8          TCM0ACGW(4) = ABS(TAGW - 32000)  .GE. 2.0
C'USD8          TCM0ACFL(4) = ABS(TAFUEL - 4000) .GE. 2.0
C'USD8          TCM0ACCG(4) = ABS(TACG - 0.27)   .GE. 0.01
C'USD8          DO I=1,3
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8          DO I=5,8
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8        ELSEIF ( ABS(TAGW - 33000)  .LT. 2.0 .OR.
C'USD8     .           ABS(TAFUEL - 4500) .LT. 2.0 .OR.
C'USD8     .           ABS(TACG - 0.30)   .LT. 0.01 ) THEN
C'USD8C
C'USD8          TCM0ACGW(3) = ABS(TAGW - 33000)  .GE. 2.0
C'USD8          TCM0ACFL(3) = ABS(TAFUEL - 4500) .GE. 2.0
C'USD8          TCM0ACCG(3) = ABS(TACG - 0.30)   .GE. 0.01
C'USD8          DO I=1,2
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8          DO I=4,8
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8        ELSEIF ( ABS(TAGW - 33900)  .LT. 2.0 .OR.
C'USD8     .           ABS(TAFUEL - 5000) .LT. 2.0 .OR.
C'USD8     .           ABS(TACG - 0.34)   .LT. 0.01 ) THEN
C'USD8C
C'USD8          TCM0ACGW(2) = ABS(TAGW - 33900)  .GE. 2.0
C'USD8          TCM0ACFL(2) = ABS(TAFUEL - 5000) .GE. 2.0
C'USD8          TCM0ACCG(2) = ABS(TACG - 0.34)   .GE. 0.01
C'USD8C
C'USD8          TCM0ACGW(1) = .FALSE.
C'USD8          TCM0ACFL(1) = .FALSE.
C'USD8          TCM0ACCG(1) = .FALSE.
C'USD8          DO I=3,8
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8        ELSEIF ( ABS(TAGW - 34500)  .LT. 2.0 .OR.
C'USD8     .           ABS(TAFUEL - 5680) .LT. 2.0 .OR.
C'USD8     .           ABS(TACG - 0.38)   .LT. 0.01 ) THEN
C'USD8C
C'USD8          TCM0ACGW(1) = ABS(TAGW - 34500)  .GE. 2.0
C'USD8          TCM0ACFL(1) = ABS(TAFUEL - 5680) .GE. 2.0
C'USD8          TCM0ACCG(1) = ABS(TACG - 0.38)   .GE. 0.01
C'USD8C
C'USD8          DO I=2,8
C'USD8            TCM0ACGW(I) = .FALSE.
C'USD8            TCM0ACFL(I) = .FALSE.
C'USD8            TCM0ACCG(I) = .FALSE.
C'USD8          ENDDO
C'USD8        ELSE
C'USD8          DO I=1,8
C'USD8            TCM0ACGW(I) = .TRUE.
C'USD8            TCM0ACFL(I) = .TRUE.
C'USD8            TCM0ACCG(I) = .TRUE.
C'USD8          ENDDO
C'USD8        ENDIF
C'USD8C
C'USD8      ENDIF
C'USD8C
C'USD8      O_TAGW   = TAGW
C'USD8      O_TAFUEL = TAFUEL
C'USD8      O_TACG   = TACG
C
C====================================
C --- GROUND SPEED X 2 BUTTON LOGIC
C====================================
C
      IF (TASPDUP .GT. 0) THEN
        IF (TOGGLETIME .GT. 7) THEN
          TCMSPARE(4) = .FALSE.
          TOGGLETIME = 0
        ELSEIF (TOGGLETIME .GT. 4) THEN
          TCMSPARE(4) = .TRUE.
          TOGGLETIME = TOGGLETIME + 1
        ELSE
          TOGGLETIME = TOGGLETIME + 1
        ENDIF
      ELSE
        TCMSPARE(4) = .FALSE.
        TOGGLETIME = 0
      ENDIF
C
C=========================
C --- SLEW CONTROL LOGIC
C=========================
C
      IF (TCMALTSL .AND. .NOT. O_TCMALTSL) THEN
        TCMAVIAS = .FALSE.
        TCMHDGSL = .FALSE.
        TCMPOSL  = .FALSE.
        TCM0SLWUP   = .TRUE.
        TCM0SLWDN   = .TRUE.
        TCM0SLWLT   = .FALSE.
        TCM0SLWRT   = .FALSE.
        TCM0SLEWF   = .TRUE.
        TCMSLEWUP   = .FALSE.
        TCMSLEWLT   = .FALSE.
        TCMSLEWF    = .FALSE.
        TCMSLEWRT   = .FALSE.
        TCMSLEWDN   = .FALSE.
      ELSE IF (TCMAVIAS .AND. .NOT. O_TCMAVIAS) THEN
        TCMALTSL = .FALSE.
        TCMHDGSL = .FALSE.
        TCMPOSL  = .FALSE.
        TCM0SLWUP   = .TRUE.
        TCM0SLWDN   = .TRUE.
        TCM0SLWLT   = .FALSE.
        TCM0SLWRT   = .FALSE.
        TCM0SLEWF   = .TRUE.
        TCMSLEWUP   = .FALSE.
        TCMSLEWLT   = .FALSE.
        TCMSLEWF    = .FALSE.
        TCMSLEWRT   = .FALSE.
        TCMSLEWDN   = .FALSE.
      ELSE IF (TCMHDGSL .AND. .NOT. O_TCMHDGSL) THEN
        TCMALTSL = .FALSE.
        TCMAVIAS = .FALSE.
        TCMPOSL  = .FALSE.
        TCM0SLWUP   = .FALSE.
        TCM0SLWDN   = .FALSE.
        TCM0SLWLT   = .TRUE.
        TCM0SLWRT   = .TRUE.
        TCM0SLEWF   = .TRUE.
        TCMSLEWUP   = .FALSE.
        TCMSLEWLT   = .FALSE.
        TCMSLEWF    = .FALSE.
        TCMSLEWRT   = .FALSE.
        TCMSLEWDN   = .FALSE.
      ELSE IF (TCMPOSL .AND. .NOT. O_TCMPOSL) THEN
        TCMALTSL = .FALSE.
        TCMAVIAS = .FALSE.
        TCMHDGSL  = .FALSE.
        TCM0SLWUP   = .TRUE.
        TCM0SLWDN   = .TRUE.
        TCM0SLWLT   = .TRUE.
        TCM0SLWRT   = .TRUE.
        TCM0SLEWF   = .TRUE.
        TCMSLEWUP   = .FALSE.
        TCMSLEWLT   = .FALSE.
        TCMSLEWF    = .FALSE.
        TCMSLEWRT   = .FALSE.
        TCMSLEWDN   = .FALSE.
      ENDIF
C
      O_TCMALTSL = TCMALTSL
      O_TCMAVIAS = TCMAVIAS
      O_TCMHDGSL = TCMHDGSL
      O_TCMPOSL  = TCMPOSL
C
C --- SET DIRECTION EXCLUSIVELY FOR ALL SLEWS
C
      IF (TCMSLEWUP .AND. .NOT. O_TCMSLEWUP) THEN
        TCMSLEWDN = .FALSE.
      ELSE IF (TCMSLEWRT .AND. .NOT. O_TCMSLEWRT) THEN
        TCMSLEWLT = .FALSE.
      ELSE IF (TCMSLEWLT .AND. .NOT. O_TCMSLEWLT) THEN
        TCMSLEWRT = .FALSE.
      ELSE IF (TCMSLEWDN .AND. .NOT. O_TCMSLEWDN) THEN
        TCMSLEWUP = .FALSE.
      ENDIF
C
      O_TCMSLEWUP = TCMSLEWUP
      O_TCMSLEWRT = TCMSLEWRT
      O_TCMSLEWLT = TCMSLEWLT
      O_TCMSLEWDN = TCMSLEWDN
C
C --- Turn off all SLEW's when slew page is removed
C
      IF ( TCMALTSL .OR.
     .     TCMAVIAS .OR.
     .     TCMHDGSL .OR.
     .     TCMPOSL ) THEN
        IF ( TAPAGERC(7,1) .NE. 337 .AND.
     .       TAPAGERC(7,2) .NE. 337 .AND.
     .       TAPAGERC(8,1) .NE. 337 .AND.
     .       TAPAGERC(8,2) .NE. 337 ) THEN
          TCMALTSL = .FALSE.
          TCMAVIAS = .FALSE.
          TCMHDGSL = .FALSE.
          TCMPOSL  = .FALSE.
        ENDIF
      ENDIF
C
C --- Make slew direction unavailable when slew is inactive
C
      IF (.NOT.TCMALTSL  .AND.
     .    .NOT.TCMAVIAS  .AND.
     .    .NOT.TCMHDGSL  .AND.
     .    .NOT.TCMPOSL) THEN
        TCM0SLWUP   = .FALSE.
        TCM0SLWDN   = .FALSE.
        TCM0SLWLT   = .FALSE.
        TCM0SLWRT   = .FALSE.
        TCM0SLEWF   = .FALSE.
        TCMSLEWUP   = .FALSE.
        TCMSLEWLT   = .FALSE.
        TCMSLEWF    = .FALSE.
        TCMSLEWRT   = .FALSE.
        TCMSLEWDN   = .FALSE.
      ENDIF
C
C --- HEADING SLEW HANDLING
C
      IF ( TCMHDGSL ) THEN
        IF ( TCMSLEWF ) THEN
          IF ( TCMSLEWRT ) THEN
            TAHDG = 2
          ELSE IF ( TCMSLEWLT ) THEN
            TAHDG = -2
          ELSE
            TAHDG = 0
          ENDIF
        ELSE
          IF ( TCMSLEWRT ) THEN
            TAHDG = 1
          ELSE IF ( TCMSLEWLT ) THEN
            TAHDG = -1
          ELSE
            TAHDG = 0
          ENDIF
        ENDIF
      ELSE
        TAHDG = 0
      ENDIF
C
C --- ALTITUDE SLEW HANDLING
C
      IF ( TCMALTSL ) THEN
        IF ( TCMSLEWF ) THEN
          IF ( TCMSLEWUP ) THEN
            IF ( VH .GT. 25000.0 ) THEN
              TCMSLEWUP = .FALSE.
              TCMSLEWF  = .FALSE.
            ELSE
              TAALT = 2
            ENDIF
          ELSE IF ( TCMSLEWDN ) THEN
            IF ( VH .LT. 10.0 ) THEN
              TCMSLEWDN = .FALSE.
              TCMSLEWF  = .FALSE.
            ELSE
              TAALT = -2
            ENDIF
          ELSE
            TAALT = 0
          ENDIF
        ELSE
          IF ( TCMSLEWUP ) THEN
            IF ( VH .GT. 25000.0 ) THEN
              TCMSLEWUP = .FALSE.
            ELSE
              TAALT = 1
            ENDIF
          ELSE IF ( TCMSLEWDN ) THEN
            IF ( VH .LT. 10.0 ) THEN
              TCMSLEWDN = .FALSE.
            ELSE
              TAALT = -1
            ENDIF
          ELSE
            TAALT = 0
          ENDIF
        ENDIF
      ELSE
        TAALT = 0
      ENDIF
C
C --- IAS SLEW HANDLING
C
      IF ( TCMAVIAS ) THEN
        IF ( TCMSLEWF ) THEN
          IF ( TCMSLEWUP ) THEN
            IF ( VM .GT. 0.95 ) THEN
              TCMSLEWUP = .FALSE.
              TCMSLEWF  = .FALSE.
            ELSE
              TAIAS = 2
            ENDIF
          ELSE IF ( TCMSLEWDN ) THEN
            IF ( VUG .LT. -16.9 ) THEN
              TCMSLEWDN = .FALSE.
              TCMSLEWF  = .FALSE.
            ELSE
              TAIAS = -2
            ENDIF
          ELSE
            TAIAS = 0
          ENDIF
        ELSE
          IF ( TCMSLEWUP ) THEN
            IF ( VM .GT. 0.95 ) THEN
              TCMSLEWUP = .FALSE.
            ELSE
              TAIAS = 1
            ENDIF
          ELSE IF ( TCMSLEWDN ) THEN
            IF ( VUG .LT. -16.9 ) THEN
              TCMSLEWDN = .FALSE.
            ELSE
              TAIAS = -1
            ENDIF
          ELSE
            TAIAS = 0
          ENDIF
        ENDIF
      ELSE
        TAIAS = 0
      ENDIF
C
C --- POSITION SLEW HANDLING
C
      IF (TCMPOSL) THEN
C
        IF (TCMSLEWF) THEN
          KFAST = 3.0
        ELSE
          KFAST = 1.0
        ENDIF
C
C ---   Calculate NS/EW velocities (ft/s) for slewing
C
        IASLEWNS = 0.0
        IASLEWEW = 0.0
C
        IF (TCMSLEWUP .OR. TCMSLEWDN) THEN
          IF (TCMSLEWUP) THEN
            IASLEWNS = COS(RTAVAR*RAD)
          ELSE
            IASLEWNS = -COS(RTAVAR*RAD)
          ENDIF
          TASLEWNS = IASLEWNS*KFAST
        ELSE
          TASLEWNS = 0.0
        ENDIF
C
        IF (TCMSLEWRT .OR. TCMSLEWLT) THEN
          IF (TCMSLEWRT) THEN
            IASLEWEW = SIN((RTAVAR+90)*RAD)
          ELSE
            IASLEWEW = -SIN((RTAVAR+90)*RAD)
          ENDIF
          TASLEWEW = IASLEWEW*KFAST
        ELSE
          TASLEWEW = 0.0
        ENDIF
C
        IF (TASLEWNS .NE. 0.0 .OR. TASLEWEW .NE. 0.0) THEN
          RUPLAT = RUPLAT + BANDWIDTH*Z1*VVNS +
     &      (TASLEWNS*TASLEWNS*TASLEWNS*ABS(TASLEWNS)*KRATE*Z4)*0.25
C
          RUPLON = RUPLON + ( BANDWIDTH*Z1*VVEW +
     &      (TASLEWEW*TASLEWEW*TASLEWEW*ABS(TASLEWEW)*KRATE*Z4)*0.25 )
     &      / COS(SNGL(RUPLAT)*Z2)
        ENDIF
      ELSE
        TASLEWNS = 0.0
        TASLEWEW = 0.0
      ENDIF
C
C --- A/C QUICK SET PAGE STATUS LOGIC HANDLING
C
      XZSTATUS(1) = ( SLAAPENG   .OR.
     .                SLAPENG(1) .OR.
     .                SLAPENG(2) )     ! Autopilot engaged
C
      XZSTATUS(2) = .NOT. ( CRTRIM .GE. -0.5 .AND.
     .                      CRTRIM .LE. 0.5 )       ! Rudder trim not set
C
      XZSTATUS(3) = .NOT. ( CIETSPOS .GE. -2.4 .AND.
     .                      CIETSPOS .LE. 3.8 )     ! Elevator trim not set
C
      XZSTATUS(4) = .NOT. ( CATRIM .GT. -0.5 .AND.
     .                      CATRIM .LT. 0.5 )       ! Aileron trim not set
C
      XZSTATUS(5) = .NOT. IDESTOP  ! ECU not TOP
C
C'USD8      XZSTATUS(6) = .NOT. TCATMFP ! AUX PUMPS off
      XZSTATUS(6) = ( IDAFP .AND. IDAFP2 ) ! AUX PUMPS off
C
      XZSTATUS(7) = .NOT. IDESAF  ! Autofeather not selected
C
C'USD8      XZSTATUS(8) = .NOT. TCATMHP ! STBY PUMPS off
      XZSTATUS(8) = .NOT. ( IDAHDP .OR. IDAHDP2 ) ! STBY PUMPS off
C
      XZSTATUS(9) = TCATMBLS      ! Bleeds on
C
      XZSTATUS(10) = .NOT. ( EFDESIN1 .AND. EFDESIN2) ! Iginition off
C
      XZSTATUS(11) = TCATMFL .NE. 3   ! Flaps not set for T/O
C
C --- Ice input handling
C
      IF ( TCMICRWY ) THEN
        TCMICRWY = .FALSE.
        TCMPIRWY = .FALSE.
        TARWYICE = TARWYICE + 0.0002
        TARWYWET = 0.0
        TARWYSLH = 0.0
        TARWYSNW = 0.0
        TCMDRWY  = .FALSE.
      ENDIF
C
      IF ( TCMBURST .AND. .NOT. O_TCMBURST ) THEN
        TCMSHEAR = .FALSE.
        TAWSPROF = 0
      ENDIF
      O_TCMBURST = TCMBURST
C
      IF ( TCMSHEAR .AND. .NOT. O_TCMSHEAR ) THEN
        TCMBURST = .FALSE.
        TAMBPROF = 0
      ENDIF
      O_TCMSHEAR = TCMSHEAR
C
C FIND_RA
C *************************************************************************
C *************************************************************************
C
C   R A D I O    A I D S
C
C *************************************************************************
C *************************************************************************
C
C
C --------------------------------------------------------------------------
C  RUNWAY SELECTION
C --------------------------------------------------------------------------
C
       IF (ICAO .NE. OLD_ICAO) THEN
C
           I = 1
           ICAO_IDX = 0
           ICAO_FOUND = .FALSE.
           DO WHILE (I.LE.MAX_VIS .AND. .NOT.ICAO_FOUND)
             IF (VISUAL_ICAO(I) .EQ. C_ICAO) THEN
               ICAO_IDX = I
               O_ICAO_IDX = 0
               ICAO_FOUND = .TRUE.
             ELSE
               I = I + 1
             ENDIF
           ENDDO
C
           DO RWYS=1,20
              TCMRWYAV(RWYS) = .FALSE.
           ENDDO
           IF (TCMRWYOK) THEN
             DO RWYS=1,20
                IF (I4RWYNAM(RWYS).EQ.X'20202020') THEN
                    TCMRWYAV(RWYS) = .FALSE.
                ELSE
                    TCMRWYAV(RWYS) = .TRUE.
                ENDIF
                I4RWYDES(RWYS) = I4RWYNAM(RWYS)
             ENDDO
C
             TCMRWYOK = .FALSE.
             OLD_ICAO = ICAO
           ENDIF
C !\FM+
C !FM  15-Nov-94 01:05:16 Tom Miller
C !FM    < COA S81-1-085 add CLT,GSO,LWB>
C !FM
C !FM+
C !FM  14-Mar-95 02:19:20 Tom Miller
C !FM    < COA S81-1-094 add PIT,IND,DCA>
C !FM
C
C ---  Visual page handling (USAIR DASH-8 specific)
C
           IF (C_ICAO .EQ. 'KBWI') THEN
             TAHAZ = 342
           ELSE IF (C_ICAO .EQ. 'KPHL') THEN
             TAHAZ = 343
           ELSE IF (C_ICAO .EQ. 'KABE') THEN
             TAHAZ = 344
           ELSE IF (C_ICAO .EQ. 'KMDT') THEN
             TAHAZ = 345
           ELSE IF (C_ICAO .EQ. 'KEWN') THEN
             TAHAZ = 346
           ELSE IF (C_ICAO .EQ. 'KCLT') THEN
             TAHAZ = 370
           ELSE IF (C_ICAO .EQ. 'KGSO') THEN
             TAHAZ = 371
           ELSE IF (C_ICAO .EQ. 'KLWB') THEN
             TAHAZ = 372
           ELSE IF (C_ICAO .EQ. 'KDCA') THEN
             TAHAZ = 373
           ELSE IF (C_ICAO .EQ. 'KIND') THEN
             TAHAZ = 374
           ELSE IF (C_ICAO .EQ. 'KPIT') THEN
             TAHAZ = 375
           ELSE
             TAHAZ = 341
           ENDIF
C
C !FM-
C !FM-
C
       ENDIF
       DO RWYS=1,20
           IF (TCMRWYSL(RWYS))  I4TARWY1 = I4RWYNAM(RWYS)
           TCMRWYSL(RWYS) = .FALSE.
C'USD8+
           TCMRFRWY(RWYS) = I4TARWY1 .EQ. I4RWYNAM(RWYS)
C'USD8-
       ENDDO
C
C
C ------------------------------------------------------------------------
C   REPOSITION INDEX NUMBER
C ------------------------------------------------------------------------
C
       IF (XZPOSN.NE.0) THEN
         IF (XZPOSN.NE.O_XZPOSN) THEN
CMW           IF ( .NOT. RUFLT ) THEN
C !FM+
C !FM  20-Dec-92 21:30:04 M.WARD
C !FM    < FIX FOR SPR 9013 - SELECTION OF REPOSITION NOT ALLOWED DURING
C !FM      RAP >
C !FM
           IF ( .NOT. RUFLT .AND..NOT. TRRAPON ) THEN
C !FM-
             TAPOSN=XZPOSN
           ELSE
             XZPOSN = O_XZPOSN
           ENDIF
         ELSEIF   (.NOT.RUFLT.AND.O_RUFLT)  THEN
           O_XZPOSN=0
           XZPOSN=0
         ENDIF
       ENDIF
       O_XZPOSN = XZPOSN
       O_RUFLT  = RUFLT
C
C --------------------------------------------------------
C -- Set Flags for Radio Aids when A-Z page is displayed.
C --------------------------------------------------------
C
      TCMRAOK(1)= (TAPAGE(1) .EQ. 31)  ! UPPER CRT
      TCMRAOK(2)= (TAPAGE(3) .EQ. 31) ! LOWER CRT
C
C --------------------------------------------------------
C -- Sets Logic for TCAS Dark Concept on IOS  - 2ULD PLEM
C --------------------------------------------------------
C
        IF(TATCCRS.EQ.0.AND.TATCNAC.GT.0)THEN
          DO I = 1,5
           TCM0SCN(I) = .FALSE.
          ENDDO
        ELSEIF(TATCCRS.EQ.0)THEN
          DO I = 1,5
           TCM0SCN(I) = .TRUE.
          ENDDO
        ELSEIF(TATCCRS.EQ.5)THEN
           TCM0SCN(5) = .TRUE.
           DO I = 1,4
            TCM0SCN(I) = .FALSE.
           ENDDO
        ELSEIF(TATCCRS.EQ.4)THEN
          TCM0SCN(4) = .TRUE.
          TCM0SCN(1) = .FALSE.
          TCM0SCN(2) = .FALSE.
          TCM0SCN(3) = .FALSE.
          TCM0SCN(5) = .FALSE.
        ELSEIF(TATCCRS.EQ.3)THEN
          TCM0SCN(3) = .TRUE.
          TCM0SCN(1) = .FALSE.
          TCM0SCN(2) = .FALSE.
          TCM0SCN(4) = .FALSE.
          TCM0SCN(5) = .FALSE.
        ELSEIF(TATCCRS.EQ.2)THEN
          TCM0SCN(1) = .FALSE.
          TCM0SCN(2) = .TRUE.
          TCM0SCN(3) = .FALSE.
          TCM0SCN(4) = .FALSE.
          TCM0SCN(5) = .FALSE.
        ELSEIF(TATCCRS.EQ.1)THEN
          TCM0SCN(1) = .TRUE.
          DO I = 2,5
           TCM0SCN(I) = .FALSE.
          ENDDO
        ENDIF
C
C
C FIND_AC
C *************************************************************************
C *************************************************************************
C
C  A I R C R A F T   M A I N T E N A N C E
C
C *************************************************************************
C *************************************************************************
C
C ------------------------------------------------------------------------
C    A/C JACKS   &  CHOKS
C ------------------------------------------------------------------------
C
C
C --- Aircraft on jacks available - exclusion with choks available
C
C'USD8      TCM0ACJX  = (VBOG.AND.VACST.EQ.0..AND..NOT.TCMCHKS ).OR.TCMACJAX
      TCM0ACJX  = (VBOG.AND.VACST.EQ.0.0).OR.TCMACJAX
C
C --- Choks available - exclusion with aircraft on jacks available
C
C'USD8      TCM0CHKS  = (VBOG.AND.VACST.EQ.0..AND..NOT.TCMACJAX).OR.TCMCHKS
      TCM0CHKS  = (VBOG.AND.VACST.EQ.0.0).OR.TCMCHKS
C
C --- Ensure the mutual exclusive properties of these two functions
C
      IF ( TCMACJAX .AND. .NOT. O_TCMACJAX ) THEN
        TCMCHKS = .FALSE.
      ENDIF
      O_TCMACJAX = TCMACJAX
C
      IF ( TCMCHKS .AND. .NOT. O_TCMCHKS ) THEN
        TCMACJAX = .FALSE.
      ENDIF
      O_TCMCHKS = TCMCHKS
C
C ------------------------------------------------------------------------
C    PUSH BACK
C ------------------------------------------------------------------------
C
C -- Set Dark concept flag for Pushback available (Modified V.PUPO)
C
C
      TCM0PUSH = VBOG.AND.(VUG.LE.5.0 .AND. VUG .GE. -10.)
C
      IF (O_TCM0PUSH .NEQV. TCM0PUSH) THEN
         TCMPBLT = .FALSE.
         TCMPBRT = .FALSE.
      ENDIF
      O_TCM0PUSH = TCM0PUSH
C
C
C ------------------------------------------------------------------------
C EXTERNAL AIR/ELECTRICS
C ------------------------------------------------------------------------
C
C --- External air/electrics are available when a/c on ground
C     and stationary
C
C
C  V. PUPO   DELETED VBOG CONDITION AND INCREASED VUG TOL.
C
CMW       TCM0ELEC = (VUG.LT.3.5 .AND. VUG.GT.-3.5)
      TCM0PNEU = (VUG.LT.3.5 .AND. VUG.GT.-3.5)
C
C --- If A/C starts moving, external air and electric are disconnected
C
      IF (.NOT.TCM0ELC1) THEN
        TCMELEC1 = .FALSE.
        TCMELEC2 = .FALSE.
        TCMPNEU1 = .FALSE.
        TCMPNEU2 = .FALSE.
      ENDIF
C
C
C ------------------------------------------------------------------------
C   BRAKE ACCUMULATOR RECHARGE
C ------------------------------------------------------------------------
C
C --- RESET AHFACC AFTER TIME DELAY
C
      IF (AHFACC .AND. .NOT. OLD_AHFACC) CALL DELAYPUT(AHFACC)
      OLD_AHFACC = AHFACC
C
C
C ------------------------------------------------------------------------
C   DOORS
C ------------------------------------------------------------------------
C
C --- ARM DOORS logic
C
C      IF (TCMDOOR) THEN
C         TCMDOOR = .FALSE.
C         IF (TCMADOR) THEN      ! Arm the doors
C            TCMARMDO(1) = .TRUE.
C            TCMARMDO(2) = .TRUE.
C            TCMARMDO(3) = .TRUE.
C            TCMARMDO(4) = .TRUE.
C            TCMARMDO(7) = .TRUE.
C            TCMARMDO(8) = .TRUE.
C         ELSE                   ! Disarm the doors
C            TCMARMDO(1) = .FALSE.
C            TCMARMDO(2) = .FALSE.
C            TCMARMDO(3) = .FALSE.
C            TCMARMDO(4) = .FALSE.
C            TCMARMDO(7) = .FALSE.
C            TCMARMDO(8) = .FALSE.
C         ENDIF
C      ENDIF
C
C --- Update button status
C
C      TCMADOR = TCMARMDO(1) .AND. TCMARMDO(2) .AND.
C     &          TCMARMDO(3) .AND. TCMARMDO(4) .AND.
C     &          TCMARMDO(7) .AND. TCMARMDO(8)
C
C
C FIND_EC
C *************************************************************************
C *************************************************************************
C
C   E N V R O N M E N T     C O N T R O L
C
C *************************************************************************
C *************************************************************************
C
C -------------------------------------------------------------------------
C --- MICROBURSTS
C -------------------------------------------------------------------------
C
C --- When microburst profile changed, set intensity to 100%
C
      IF (OLD_MBPROF .NE. TAMBPROF) THEN
         IF (TAMBPROF .NE. 0) TAMBGAIN = 1.0
         OLD_MBPROF = TAMBPROF
      ENDIF
C
C -----------------------------------------------------------------------
C    MICROBURST MAPPING SCALE
C -----------------------------------------------------------------------
C
      IF (XZMBYOFF .NE. O_XZMBYOFF) THEN
         O_XZMBYOFF = XZMBYOFF
         TAMBYOFF = (XZMBYOFF/260 * 1) - 0.5
      ENDIF
C
      IF (XZMBXOFF .NE. O_XZMBXOFF) THEN
         O_XZMBXOFF = XZMBXOFF
         TAMBXOFF = (XZMBXOFF/510 *13) - 5
      ENDIF
C
      IF ( TAMBXOFF .NE. O_TAMBXOFF ) THEN
        O_TAMBXOFF = TAMBXOFF
        TAMBXPOS = TAMBXOFF
      ENDIF
C
      IF ( TAMBYOFF .NE. O_TAMBYOFF ) THEN
        O_TAMBYOFF = TAMBYOFF
        TAMBYPOS = TAMBYOFF
      ENDIF
C
C -------------------------------------------------------------------------
C  CALCULATION OF RADIUS SCALE FOR MICROBURSTS
C -------------------------------------------------------------------------
C
C  On microburst page:
C  1 nm = 49.2 pixels, 1 ft = 49.2/6076 pixels
C  TAMBRAD(*) is sent by flight in feet. To compute the cell's scale on the
C  page, the cell's radius must be converted into pixels, then divided by
C  the cell's radius on the page (radius=22.125 pixels).
C  Hence, divide radius by 6076 / 49.2 * 22.125 = 2732.348
C  to obtain cell's scale.
C
C      XZMBSCAL(1) = TAMBRAD(1)/2732.348
C      XZMBSCAL(2) = TAMBRAD(2)/2732.348
C      XZMBSCAL(3) = TAMBRAD(3)/2732.348
C      XZMBSCAL(4) = TAMBRAD(4)/2732.348
C      XZMBSCAL(5) = TAMBRAD(5)/2732.348
C
C--------------------------------------------------------------------------
C --- BARCHART HANDLING FOR CG & RUNWAY ROUGHNESS
C--------------------------------------------------------------------------
C
       IF ( TACGBARX .NE. O_TACGBARX ) THEN  ! CG input handling
C
         O_TACGBARX = TACGBARX
         CG_INCR = TACGBARX*0.0006765   ! ((0.38-0.15)/340)=0.0006765
         IF ( CG_INCR .GE. 0 ) THEN
C !FM           TACG = 0.15 + CG_INCR
           TACG = 0.38 - CG_INCR
C !FM         ELSE
C !FM           TACG = TACGMIN
         ENDIF
C
         IF ( TACG .LT. TACGMIN ) TACG = TACGMIN
         IF ( TACG .GT. TACGMAX ) TACG = TACGMAX
C
       ENDIF
C
       IF ( TARWBARX .NE. O_TARWBARX ) THEN  ! Roughness input handle
         O_TARWBARX = TARWBARX
         ROUGH_INCR = TARWBARX*0.006944   ! (5/720)=0.006944
         IF ( ROUGH_INCR .GE. 0 .AND.
     .        ROUGH_INCR .LE. 5 ) THEN
           TARFRWY = ROUGH_INCR
         ELSE
           IF ( ROUGH_INCR .LT. 0 ) THEN
             TARFRWY = 0
           ELSE
             TARFRWY = 5
           ENDIF
         ENDIF
       ENDIF
C
C -------------------------------------------------------------------------
C -- ENVIRONMENT STANDARD RESET
C -------------------------------------------------------------------------
C
C --- Set Standard Environment reset available
C
      TCR0ENVI = ABS(TAQNH-29.92) .GT. 0.1      ! QNH
     &   .OR.    ABS(TATEMP(1)-15.0) .GT. 0.1   ! Flight level temp
     &   .OR.    ABS(TATEMP(2)-15.0+0.00198*RXMISELE(3)) .GT. 0.1
     &   .OR.    ABS(TATEMP(3)+24.63) .GT. 0.1
     &   .OR.    ABS(TATEMP(4)+56.50) .GT. 0.1
     &   .OR.    ABS(TATEMP(5)+56.50) .GT. 0.1
     &   .OR.    ABS(TATLPSRT+1.98) .GT. 0.1    ! Temp lapse rate
     &   .OR.    ABS(TAWSPD(2)) .GT .0.1        ! Airfield wind speed
     &   .OR.    ABS(TAWDIR(2)) .GT .0.1        ! Airfield wind dir
     &   .OR.    ABS(TAITEMP+4.8) .GT. 0.1      ! Intermediate wind temp
     &   .OR.    ABS(TAIFLVL-10000) .GT. 1.0    ! Intermediate alt
     &   .OR.    ABS(TAIWSPD) .GT. 0.1          ! Intermediate wind spd
     &   .OR.    ABS(TAIWDIR) .GT. 0.1          ! Intermediate wind dir
     &   .OR.    ABS(TATFLVL-36089) .GT. 1.0    ! Tropopause alt
     &   .OR.    ABS(TATTEMP+56.5) .GT. 0.1     ! Tropopause wind temp
     &   .OR.    ABS(TATWSPD) .GT. 0.1          ! Tropopause wind spd
     &   .OR.    ABS(TATWDIR) .GT. 0.1          ! Tropopause wind dir
     &   .OR.    ((ABS(TATURB(1)) .GT. 0.01) .AND. ! Turbulence
     &           (TCMCAT.OR.TCMTURLO.OR.TCMSTORM))
     &   .OR.    ABS(TAMBGAIN) .GT. 0.01        ! Microburst intens
     &   .OR.    TAMBPROF .NE. 0
     &   .OR.    TAWSPROF .NE. 0
C
C
C --- Set ISA Standard Environment
C
      IF (TCRENVI.AND..NOT.OLD_ENVI) THEN
C
C'FLIGHT         TAQNH      = 29.92    ! QNH
C'FLIGHT         TATEMP(1)  = 15       ! Sea level temp (deg C)
C'FLIGHT         TATEMP(2)  = 15.0 - 0.00198*RXMISELE(3)
C'FLIGHT         TATEMP(3)  = -24.63
C'FLIGHT         TATEMP(4)  = -56.50
C'FLIGHT         TATEMP(5)  = -56.50
         TATLPSRT   = 1.98     ! Temp lapse rate (deg C/1000ft)
C'FLIGHT         TAWSPD(2)  = 0.0      ! Airfield wind speed
C'FLIGHT         TAWDIR(2)  = 0.0      ! Airfield wind dir
         TAITEMP    = -4.8     ! Intermediate wind temp (deg C)
         TAIFLVL    = 10000    ! Intermediate alt (ft)
         TAIWSPD    = 0.0      ! Intermediate wind spd
         TAIWDIR    = 0.0      ! Intermediate wind dir
         TATFLVL    = 36089    ! Tropopause alt (ft)
         TATTEMP    = 56.5     ! Tropopause wind temp (deg C)
         TATWSPD    = 0.0      ! Tropopause wind spd
         TATWDIR    = 0.0      ! Tropopause wind dir
         TATURB(1)  = 0.0      ! Turbulence intens
         TAGUST     = 0.0      ! Gust intensity
         TAMBGAIN   = 0.0      ! Microburst intens
         TAMBPROF = 0          ! no microburst profile
         TAWSPROF = 0          ! no windshear profile
         TCMCAT   = .FALSE.    ! clear air turbulence
         TCMTURLO = .FALSE.    ! low level turbulence
         TCMSTORM = .FALSE.    ! thunderstorm turbulence
         TCMBURST = .FALSE.    ! downburst off
         TCMSHEAR = .FALSE.    ! Windshear off
         TCMDRWY  = .TRUE.     ! Dry runway condition
         TCMICE   = .FALSE.    !
         TCMPFOG  = .FALSE.
         TCMGFOG  = .FALSE.
         TCMSCUD  = .FALSE.
         TCMSTATM = .TRUE.      ! temp reset
         TCMCHATM = .TRUE.
         CALL DELAYPUT(TCRENVI)
       ENDIF
       OLD_ENVI = TCRENVI
C
C ----------------------------------------------------------------------
C    TEMP RESET / WIND RESET
C-----------------------------------------------------------------------
C
      IF (XZTGRAPH .AND. .NOT.OLD_TGRAPH) THEN
         DO I = 1, 4   ! 1 = sea level, 2 = grd, 3 = interm, 4 = tropo
           TATEMP(I) = 15 - 0.00198*TAFLVL(I)
           VOTEMP(I) = TATEMP(I)                 ! old values = new values
           TCMCHATM  = .TRUE.                    ! see va.f module
         ENDDO
      ENDIF
      OLD_TGRAPH = XZTGRAPH
C
      IF (XZWGRAPH .AND. .NOT.OLD_WGRAPH) THEN
         TAWDIR(2) = 0        ! reset all to 0 see aw37.cdb
         TAWDIR(3) = 0
         TAWDIR(4) = 0
         TAWSPD(2) = 0
         TAWSPD(3) = 0
         TAWSPD(4) = 0
         TCMCHATM  = .TRUE.   ! see va.f module
      ENDIF
      OLD_WGRAPH = XZWGRAPH
C
C
C ---
C
      TIME_DAY = '     '  ! 5 CHARS
      IF  (TAVMODE .EQ. 0) TIME_DAY = ' DAY '
      IF  (TAVMODE .EQ. 1) TIME_DAY = 'NIGHT'
      IF  (TAVMODE .EQ. 2) TIME_DAY = 'DUSK '
      IF  (TAVMODE .EQ. 3) TIME_DAY = 'DAWN '
C
      WEATHER =  '      ' ! 6 CHARS
      IF  (XZWXR .EQ. 1 )  WEATHER  = 'CAVOK '
      IF  (XZWXR .EQ. 2 )  WEATHER  = 'CAT I '
      IF  (XZWXR .EQ. 3 )  WEATHER  = 'CAT II'
      IF  (XZWXR .EQ. 4 )  WEATHER  = 'CATIII'
      IF  (XZWXR .EQ. 5 )  WEATHER  = ' 0/0  '
      IF  (XZWXR .EQ. 6 )  WEATHER  = ' T/O  '
      IF  (XZWXR .EQ. 7 )  WEATHER  = ' CLEAR'
      IF  (XZWXR .EQ. 8 )  WEATHER  = ' NP   '
C
      BRAKING =  '      '  ! 6 CHARS
      IF (TABRAKE .EQ. 1 ) BRAKING  = ' GOOD '
      IF (TABRAKE .EQ. 2 ) BRAKING  = 'MEDIUM'
      IF (TABRAKE .EQ. 3 ) BRAKING  = ' POOR '
C
      TURB_TYP =  '     '  ! 5 CHARS
      IF (TCMSTORM) TURB_TYP = 'STORM'
      IF (TCMCAT  ) TURB_TYP = 'CLEAR'
      IF (TCMTURLO) TURB_TYP = 'LOW L'
C
      TURB_INT = '    '  ! 4 CHARS
      IF (TATURB(1) .EQ. 1.00) TURB_INT = 'HVY '
      IF (TATURB(1) .EQ. 0.66) TURB_INT = 'MOD '
      IF (TATURB(1) .EQ. 0.33) TURB_INT = 'LGHT'
C
      ICING   = '        '  ! 8 CHARS
      IF (TAICEQTY.EQ.1.00) ICING = ' SEVERE '
      IF (TAICEQTY.EQ.0.66) ICING = 'MODERATE'
      IF (TAICEQTY.EQ.0.33) ICING = ' LIGHT  '
C
      RWY_CONT  = '          ' ! 10 CHARS
      IF (TCMDRWY)               RWY_CONT = '   DRY    '
      IF (TARWYWET.EQ.1.00)      RWY_CONT = '   WET    '
      IF (TARWYWET.EQ.0.25)      RWY_CONT = '1/4  WATER'
      IF (TARWYSLH.EQ.0.25)      RWY_CONT = '1/4  SLUSH'
      IF (TARWYSLH.EQ.0.50)      RWY_CONT = '1/2  SLUSH'
      IF (TARWYSNW.EQ.1.00)      RWY_CONT = '   SNOW   '
      IF (TARWYICE.EQ.1.00)      RWY_CONT = '   ICE    '
      IF (TARWYICE.EQ.0.50)      RWY_CONT = 'PATCHY ICE'
C
C
C FIND_FR
C *************************************************************************
C *************************************************************************
C
C   F R E E Z E S     &      R E S E T S
C
C *************************************************************************
C *************************************************************************
C
C --- Flight freeze available
C
       TCF0FLPO = .NOT.(VTRIM .EQ. 0.0 .OR. RUFLT .OR. TCR0ASH)
C'USD8      TCF0FLPO = .NOT. TCR0ASH .AND. (.NOT.RUFLT)
C
 
C
C      TCR0HOLT  =  AHTC.GT.80..OR.AHTL.GT.80..OR.AHTR.GT.80.  ! HYD oil
C
C      TCR0EOLQ  =  EQOIL(1).LT.18.5.OR.EQOIL(2).LT.18.5       ! ENG oil qty
C
C      TCR0HOLQ  =  AHQC.LT.3.0.OR.AHQL.LT.6.0.OR.AHQR.LT.6.0  ! HYD oil qty
C
C      TCR0CSDT  =  AETOUTL.GT.400..OR.AETOUTR.GT.400.         ! CSD oil temp
C
C      TCR0CSD   =  TCR0CSDT.OR.TCR0CSD
C
C      TCR0EGT   =  .NOT.EFLM(1).OR..NOT.EFLM(2)               ! ENG EGT
C
C      TCR0BATT  =  AEQBATM.LT.40.OR.AEQBATA.LT.40             ! Battery charge
C
C      TCR0BRKT  =  ABTP.GT.100.0                              ! Brake temp
C
C      TCR0OXY   =  AMOXHP.LT.200.0                            ! Crew oxygen
C
C      TCR0FIRX  =  TCR0FIRX.OR.ARFAUWS                        ! Fire bottles
C
C --- Fuel temp reset
C
C      TCR0FUEL  =      (AFTFC.GT.20. )
C     -            .OR. (AFTFL.GT.20. )
C     -            .OR. (AFTFR.GT.20. )
C     -            .OR. (AFTFC.LT.-40.)
C     -            .OR. (AFTFL.LT.-40.)
C     -            .OR. (AFTFR.LT.-40.)
C
      TCR0MAIN = ( TCR0LG   .OR.
     -             TCR0BATT .OR.
     -             TCR0FIRX .OR.
     -             TCR0TIRB .OR.
     -             TCR0PKTM .OR.
     -             DOFAI(1) .OR.
     -             DOFAI(2) .OR.
     -             DBFBI(1) .OR.
     -             DBFBI(2) .OR.
     -             TCR0CBPRE.OR.
C !FM+
C !FM  16-Dec-92 13:32:05 Steve Walkington
C !FM    < Added stick pusher charge empty maint req >
C !FM
     -             CWCYLMT)
C !FM-
C
C ------------------------------------------------------------------------
C -- TOTAL FREEZE HANDLING
C ------------------------------------------------------------------------
C
C
      IF (.NOT.(TRSNPREC.OR.TRSETREC.OR.TRRAPON) ) YIFREZ = TCFTOT
C
C ------------------------------------------------------------------------
C  SPECIAL HANDLING OF TOTAL RESET
C ------------------------------------------------------------------------
C
      IF (TCRTOT_ACT.AND.(.NOT.TCRTOT)) THEN
         CALL DELAYMULTI(ADF_SYST,NMF_SYST)
         TCRTOT_ACT = .FALSE.
      ENDIF
C
C ------------------------------------------------------------------------
C -- TOTAL RESET
C ------------------------------------------------------------------------
C
C --- Activate all resets except ISA
C
      IF (TCRTOT.AND..NOT.OLD_TOTR) THEN
        TCMCRINB = .TRUE.
        TCRALLT  = .TRUE.
        TCRALLQ  = .TRUE.
        TCRSYST  = .TRUE.
        TCRENVI  = .TRUE.
        TCRMALF  = .TRUE.
        TCRASH   = .TRUE.
C
        TCRCSDOQ = .TRUE.
        TCRSPCL  = .TRUE.     ! check
        TCRALLST = .TRUE.     ! check
        CALL DELAYMULTI(ADF_TOTR,NMF_TOTR)
C
C --- OTHER RESETS
C
        TCMELEC1 = .TRUE.
        TCMELEC2 = .TRUE.
        TCMPNEU1 = .TRUE.
        TCMPNEU2 = .TRUE.
        TAICEQTY = 0
        TCMDRWY  = .TRUE.
        TARWYWET = 0
        TARWYSLH = 0
        TARWYICE = 0
        TARWYSNW = 0
        TCMACJAX = .FALSE.
        TCMCHKS  = .FALSE.
        TATURB(1)= 0
        TCRTOT_ACT = .TRUE.
       ENDIF
      OLD_TOTR = TCRTOT
C
C
C ------------------------------------------------------------------------
C --- ALL TEMPERATURE RESET
C ------------------------------------------------------------------------
C
C --- Reset all temperature values
C
      IF (TCRALLT .AND. .NOT.OLD_ALLT) THEN
         TCREOILT = .TRUE.           ! Engine oil
         TCRHOILT = .TRUE.           ! Hydraulic oil
         TCRAOILT = .TRUE.           ! APU oil
         TCRAEGT  = .TRUE.           ! APU egt
         TCRFUELT = .TRUE.           ! Fuel temperature
         TCREGT   = .TRUE.           ! Engine ITT
         TCRBRKT  = .TRUE.           ! Brakes
         TCRACOND = .TRUE.           ! Air condition
         TCRACONS = .TRUE.           ! Cabin temp
         TCRIDGT  = .TRUE.           ! IDG OIL temp
         TCRCSDOT = .TRUE.
         TCRACTAT = .TRUE.
         TCRBTMP  = .TRUE.
         TCRPKTMP  = .TRUE.
         CALL DELAYMULTI(ADF_ALLT,NMF_ALLT)
      ENDIF
      OLD_ALLT = TCRALLT
C
C ------------------------------------------------------------------------
C -- ALL QUANTITY RESET
C ------------------------------------------------------------------------
C
C --- Reset all quantities
C
      IF (TCRALLQ. AND. .NOT.OLD_ALLQ) THEN
         TCREOILQ = .TRUE.               ! Engine oil
         TCRAOILQ = .TRUE.               ! APU oil
         TCRHOILQ = .TRUE.               ! Hydraulic oil          CHECK
         TCRHFLQ  = .TRUE.               ! Hydraulic reservoirs   CHECK
         TCRHQTY1 = .TRUE.
         TCRHQTY2 = .TRUE.
         TCRHQTY3 = .TRUE.
         TCRHQTY4 = .TRUE.
         TCRIDGQ  = .TRUE.
         TCRFIRX  = .TRUE.
         TCROXY   = .TRUE.
         TCRBATT  = .TRUE.
         TCRARPM  = .TRUE.
         TCRCBPRE = .TRUE.
         AHFACC   = .TRUE.               ! Brake accumulator pressurize
         CALL DELAYMULTI(ADF_ALLQ,NMF_ALLQ)
      ENDIF
      OLD_ALLQ = TCRALLQ
C
C ------------------------------------------------------------------------
C -- ALL SYSTEMS RESET
C ------------------------------------------------------------------------
C
C --- Reset all maintenance systems.
C
C
      IF (TCRSYST .AND. .NOT.OLD_SYST) THEN
         TCRIDG   = .TRUE.               ! Reconnect IDG
         TCRBATT  = .TRUE.               ! Recharge battery
         TCRCBPRE = .TRUE.               ! reset cabin pressure
         TCRARPM  = .TRUE.               ! APU RPM
         TCRAEGT  = .TRUE.               ! APU EGT
         TCRRAT   = .TRUE.               ! Ram Air Turbine
         TCRENGF  = .TRUE.               ! Engine Fire Bootle
         TCRGEN   = .TRUE.               ! Generator
         TCRCARF  = .TRUE.               ! Cargo fire extinguishing
         TCRIDG   = .TRUE.               ! IDG reconnect
         TCRMAINT = .TRUE.               ! Maint. reset
         TCRCSDOT = .TRUE.
         TCRCSD   = .TRUE.
         IF (.NOT.TCRTOT) TCRALLQ  = .TRUE.  ! all quantities
         IF (.NOT.TCRTOT) CALL DELAYMULTI(ADF_SYST,NMF_SYST)
      ENDIF
      OLD_SYST = TCRSYST
C
C
C ------------------------------------------------------------------------
C    MALFUNCTIONS RESETS
C ------------------------------------------------------------------------
C
C --- Clear all active and armed malfunctions, and circuit breakers
C
CNT see xm      TCR0MALF = .TRUE.
C
      IF (TCRMALF .AND. .NOT.OLD_CAMLF) THEN
C
C     -- Set delay reset for TCRMALF
C
         CALL DELAYPUT(TCRMALF)
C
C     -- clear all active malfunctions (discrete and variable)
C
         DO I=0, NUMTVS
            TVMALFS(I) = 0.0
         ENDDO
C
         DO I=0, NUMTFS
            TFMALFS(I) = .FALSE.
         ENDDO
C
C     -- clear all circuit breakers
C
         DO I=0, NUMBPS
            BPMALFS(I) = .FALSE.
         ENDDO
      ENDIF
      OLD_CAMLF = TCRMALF
C
C
C
C --------------------------------------------------------------------------
C   CRASH RESET
C --------------------------------------------------------------------------
C
C --- When Crash Reset selected from the CRASH PAGE
C
      IF (TCRASH .NEQV. OLD_CRASH) THEN
         IF (TCRASH) THEN
            TAPAGE(3) = SAVEPAGE        ! Go to previous page
         ENDIF
         OLD_CRASH = TCRASH
      ENDIF
C
C --- When Crash Reset selected from the RESETS/FREEZE PAGE
C
      IF (XZCRASH .NEQV. OLD_XZCRASH) THEN
         IF (XZCRASH) THEN
            TCRASH    = .TRUE.
            CALL DELAYPUT(TCRASH)
            OLD_CRASH = .TRUE.
         ENDIF
         OLD_XZCRASH = XZCRASH
      ENDIF
C
C --- When A/C has crashed, display causes of crash page
C
      IF (TCR0ASH .NEQV. OLD_0CRASH) THEN
         IF (TCR0ASH) THEN             ! A/C just crashed
           SAVEPAGE = TAPAGE(3)
           TAPAGE(3) = CRASHPG
         ELSE
           IF ( TAPAGE(3) .EQ. CRASHPG ) THEN
             TAPAGE(3) = SAVEPAGE        ! Reset selected
           ENDIF
         ENDIF
         OLD_0CRASH = TCR0ASH
      ENDIF
C
C
C --- Crash INHIBIT dark concept
C
      TCM0CRIN = .NOT. TCR0ASH
C
C
C FIND_VC
C *************************************************************************
C *************************************************************************
C
C      V I S U A L      C O N T R O L
C
C *************************************************************************
C *************************************************************************
C
C
C
      IF (ICAO .EQ. OLD_ICAO) THEN  ! Logic for TCMRWYOK is done
        IF (C_RWY1 .NE. '    ') THEN
          IF (ICAO_IDX .NE. O_ICAO_IDX) THEN
            IF (ICAO_FOUND) THEN
              I = 1
              RWY_FOUND = .FALSE.
              RWY_IDX = 0
              DO WHILE (I.LE.MAX_RWY .AND. .NOT.RWY_FOUND)
C'BAON                IF (VIS_RWY(I,ICAO_IDX) .EQ. C_RWY1) THEN
                IF (C_RXRWYNAM(I) .EQ. C_RWY1) THEN
                  RWY_IDX = I
                  O_RWY_IDX = 0
                  RWY_FOUND = .TRUE.
                  C_CURRWY  = C_RWY1
                  NUM_VISRWY_ACT = 1
                  TCMVRWAV(10+I) = .TRUE.
                  DO J=1,I-1
                    TCMVRWAV(10+J) = .FALSE.
                  ENDDO
                  DO J=I+1,MAX_RWY
                    TCMVRWAV(10+J) = .FALSE.
                  ENDDO
                ELSE
                  I = I + 1
                ENDIF
              ENDDO
C
              DO I=1,MAX_RWY
C'BAON                C_TAVRWID(I) = VIS_RWY(I,ICAO_IDX)
                C_TAVRWID(I) = C_RXRWYNAM(I)
              ENDDO
C
              DO I=1,10                   ! Available logic
                TCMVRWAV(I) = .FALSE.
                J = (I*2) -1
                IF (C_TAVRWID(J).NE.'    ') THEN
                  TCMVRWAV(I) = .TRUE.
                ENDIF
C PLEM                TAVRWAPP(I) = 3
C PLEM                TAVRWTDZ(I) = 3
C PLEM                TAVRWEDG(I) = 3
C PLEM                TAVRWVAS(I) = 3
C PLEM                TAVRWTAX(I) = 3
C PLEM                TAVRWSTR(I) = 3
C PLEM                TAVRWCEN(I) = 3
C PLEM                TAVRWALL(I) = 3
C PLEM                TAVRWLT(I)  = 3
C PLEM                TAVRWHOR(I) = 3
C PLEM                TAVRWENV(I) = 3
              ENDDO
C
              TCMAUTBR = .TRUE.
C
            ELSE
              DO I=1,MAX_RWY
                C_TAVRWID(I) = '    '
              ENDDO
              DO I=1,30                   ! Available logic
                TCMVRWAV(I) = .FALSE.
              ENDDO
              RWY_IDX = 0
              O_RWY_IDX = 0
              RWY_FOUND = .FALSE.
            ENDIF
            O_ICAO_IDX = ICAO_IDX
          ENDIF
        ENDIF
      ENDIF
C
C --  Runway select on VISUAL popup
C     ( Array element from 11:30 of TCMVRWSL is used. )
C
      IF ( ICAO_FOUND ) THEN
        I = 1
        RWY_CHECK = .FALSE.
        DO WHILE ( I .LE. 20 .AND. .NOT. RWY_CHECK )
          IF (TCMVRWSL(I+10)) THEN
            TCMVRWSL(I+10) = .FALSE.
            RWY_CHECK = .TRUE.
            DUMMY_RWY = C_RXRWYNAM(I)
            IF ( DUMMY_RWY .NE. C_CURRWY ) THEN
              IF ( NUM_VISRWY_ACT .GT. 0 .AND.
     .             NUM_VISRWY_ACT .LE. 3 ) THEN
                C_CURRWY = DUMMY_RWY
                TCMVRWIN = .TRUE.
              ENDIF
            ENDIF
          ELSE
            I = I + 1
          ENDIF
        ENDDO
      ENDIF
C
C --- Visual Runway input handling
C
      IF (TCMVRWIN) THEN
        TCMVRWIN = .FALSE.
        TCMAUTBR = .FALSE.
        IF (C_CURRWY .NE. O_CURRWY) THEN
          IF (ICAO_FOUND) THEN
            I = 1
            O_RWY_FOUND = RWY_FOUND
            RWY_FOUND = .FALSE.
            RWY_IDX = 0
            DO WHILE (I.LE.MAX_RWY .AND. .NOT.RWY_FOUND)
C'BAON              IF (VIS_RWY(I,ICAO_IDX) .EQ. C_CURRWY) THEN
              IF (C_RXRWYNAM(I) .EQ. C_CURRWY) THEN
                RWY_IDX = I
                O_RWY_IDX = 0
                RWY_FOUND = .TRUE.
                IF ( NUM_VISRWY_ACT .GT. 0 .AND.
     .               NUM_VISRWY_ACT .LE. 3 ) THEN
                  TCMVRWAV(10+I) = .TRUE.
                  J = MOD(I,2)
                  IF (J .EQ. 0) THEN
                    TCMVRWAV(10+I-1) = .FALSE.
                  ELSE
                    TCMVRWAV(10+I+1) = .FALSE.
                  ENDIF
C
C --- Logic to keep track of number of selected vis runways
C
                  NUM_VISRWY_ACT = 0
                  DO J=1,MAX_RWY
                    IF ( TCMVRWAV(10+J) )
     .                NUM_VISRWY_ACT = NUM_VISRWY_ACT + 1
                  ENDDO
C
                ENDIF
              ELSE
                I = I + 1
              ENDIF
            ENDDO
            IF (.NOT. RWY_FOUND) THEN
              C_CURRWY = O_CURRWY
              RWY_IDX  = O_RWY_IDX
              RWY_FOUND = O_RWY_FOUND
            ENDIF
          ELSE
            C_CURRWY = O_CURRWY
          ENDIF
        ENDIF
      ENDIF
C
C --- Auto/Manual state transition logic
C
      IF ( TCMAUTBR .NEQV. O_TCMAUTBR ) THEN
        IF ( .NOT. TCMAUTBR ) THEN
          IF (ICAO_FOUND) THEN
            I = 1
            RWY_FLAG = .FALSE.
            DO WHILE (I.LE.MAX_RWY .AND. .NOT.RWY_FLAG)
              IF (C_RXRWYNAM(I) .EQ. C_CURRWY) THEN
                RWY_FLAG = .TRUE.
                NUM_VISRWY_ACT = 1
                TCMVRWAV(10+I) = .TRUE.
                DO J=1,I-1
                  TCMVRWAV(10+J) = .FALSE.
                ENDDO
                DO J=I+1,MAX_RWY
                  TCMVRWAV(10+J) = .FALSE.
                ENDDO
              ELSE
                I = I + 1
              ENDIF
            ENDDO
          ENDIF
        ENDIF
      ENDIF
      O_TCMAUTBR = TCMAUTBR
C
C --- Logic for any change in Visual Active Runway
C
      IF (RWY_FOUND) THEN
        IF (RWY_IDX .NE. O_RWY_IDX) THEN
          IF (RWY_IDX .GT. 0) THEN
C'BAON            C_CURRWY = VIS_RWY(RWY_IDX,ICAO_IDX)
            C_CURRWY = C_RXRWYNAM(RWY_IDX)
C
            J = MOD(RWY_IDX,2)
            IF (J .EQ. 0) THEN
              REF_IDX = RWY_IDX*0.5
            ELSE
              REF_IDX = (RWY_IDX*0.5) + 1
            ENDIF
C
C PLEM            TADSPAPP = TAAPRBRT1(TAVRWYIDX)
C PLEM            TADSPTDZ = TATDZLT1(TAVRWYIDX)
C PLEM            TADSPEDG = TARWYEDG1(TAVRWYIDX)
C PLEM            TADSPVAS = TAVASI1(TAVRWYIDX)
C PLEM            TADSPTAX = TAVRWTAX(TAVRWYIDX)
C PLEM            TADSPSTR = TASTROBE1(TAVRWYIDX)
C PLEM            TADSPCEN = TARWYMID1(TAVRWYIDX)
C PLEM            TADSPALL = TAALLRWY1(TAVRWYIDX)
C PLEM            TADSPRW  = TAVRWLT(TAVRWYIDX)
C PLEM            TADSPHOR = TAVRWHOR(TAVRWYIDX)
C PLEM            TADSPENV = TAVRWENV(TAVRWYIDX)
          ENDIF
          O_RWY_IDX = RWY_IDX
        ENDIF
      ELSE
        REF_IDX = 0
      ENDIF
      O_CURRWY  = C_CURRWY
C
C --- Visual runway EDIT popup selection logic
C     ( Array element from 1:10 of TCMVRWSL is used. )
C
C PLEM      DO I=1,10
C PLEM        IF (TCMVRWSL(I)) THEN
C PLEM          TAEDTAPP = TAAPRBRT1(I)
C PLEM          TAEDTTDZ = TATDZLT1(I)
C PLEM          TAEDTEDG = TARWYEDG1(I)
C PLEM          TAEDTVAS = TAVASI1(I)
C PLEM          TAEDTTAX = TAVRWTAX(I)
C PLEM          TAEDTSTR = TASTROBE1(I)
C PLEM          TAEDTCEN = TARWYMID1(I)
C PLEM          TAEDTALL = TAALLRWY1(I)
C PLEM          TAEDTRW  = TAVRWLT(I)
C PLEM          TAEDTHOR = TAVRWHOR(I)
C PLEM          TAEDTENV = TAVRWENV(I)
C PLEM          TAVRWIND = I
C PLEM          TCMVRWSL(I) = .FALSE.
C PLEM          GOTO 555
C PLEM        ENDIF
C PLEM      ENDDO
C
C PLEM 555  CONTINUE
C
      IF (TAVRWYIDX .GT. 0) THEN
C
        IF (TADSPALL .NE. O_TADSPALL) THEN
          TAALLRWY1(TAVRWYIDX) = TADSPALL
        ENDIF
C
        IF (TADSPAPP .NE. O_TADSPAPP) THEN
          TAAPRBRT1(TAVRWYIDX) = TADSPAPP
        ENDIF
C
        IF (TADSPTDZ .NE. O_TADSPTDZ) THEN
          TATDZLT1(TAVRWYIDX) = TADSPTDZ
        ENDIF
C
        IF (TADSPEDG .NE. O_TADSPEDG) THEN
          TARWYEDG1(TAVRWYIDX) = TADSPEDG
        ENDIF
C
        IF (TADSPVAS .NE. O_TADSPVAS) THEN
          TAVASI1(TAVRWYIDX) = TADSPVAS
        ENDIF
C
        IF (TADSPSTR .NE. O_TADSPSTR) THEN
          TASTROBE1(TAVRWYIDX) = TADSPSTR
        ENDIF
C
        IF (TADSPCEN .NE. O_TADSPCEN) THEN
          TARWYMID1(TAVRWYIDX) = TADSPCEN
        ENDIF
 
        TADSPAPP = TAAPRBRT1(TAVRWYIDX)
        TADSPTDZ = TATDZLT1(TAVRWYIDX)
        TADSPEDG = TARWYEDG1(TAVRWYIDX)
        TADSPVAS = TAVASI1(TAVRWYIDX)
        TADSPSTR = TASTROBE1(TAVRWYIDX)
        TADSPCEN = TARWYMID1(TAVRWYIDX)
        TADSPALL = TAALLRWY1(TAVRWYIDX)
		
        O_TADSPALL = TADSPALL
        O_TADSPAPP = TADSPAPP
        O_TADSPTDZ = TADSPTDZ
        O_TADSPEDG = TADSPEDG
        O_TADSPVAS = TADSPVAS
        O_TADSPSTR = TADSPSTR
        O_TADSPCEN = TADSPCEN
C
C
      ENDIF
C
C PLEM      IF (TAVRWIND .GT. 0) THEN
C
C PLEM        IF (TAEDTALL .NE. O_TAEDTALL) THEN
C PLEM          TAALLRWY1(TAVRWIND) = TAEDTALL
C PLEM          TAEDTAPP = TAEDTALL
C PLEM          TAEDTTDZ = TAEDTALL
C PLEM          TAEDTEDG = TAEDTALL
C PLEM          TAEDTVAS = TAEDTALL
C PLEM          TAEDTTAX = TAEDTALL
C PLEM          TAEDTSTR = TAEDTALL
C PLEM          TAEDTCEN = TAEDTALL
C PLEM          TAEDTRW  = TAEDTALL
C PLEM          TAEDTHOR = TAEDTALL
C PLEM          TAEDTENV = TAEDTALL
C PLEM          IF (TAVRWIND .EQ. TAVRWYIDX) TADSPALL = TAEDTALL
C PLEM          O_TAEDTALL = TAEDTALL
C PLEM        ENDIF
C
C PLEM        IF (TAEDTAPP .NE. O_TAEDTAPP) THEN
C PLEM          TAAPRBRT1(TAVRWIND) = TAEDTAPP
C PLEM          IF (TAVRWIND .EQ. TAVRWYIDX) TADSPAPP = TAEDTAPP
C PLEM          O_TAEDTAPP = TAEDTAPP
C PLEM        ENDIF
C
C PLEM        IF (TAEDTTDZ .NE. O_TAEDTTDZ) THEN
C PLEM          TATDZLT1(TAVRWIND) = TAEDTTDZ
C PLEM          IF (TAVRWIND .EQ. TAVRWYIDX) TADSPTDZ = TAEDTTDZ
C PLEM          O_TAEDTTDZ = TAEDTTDZ
C PLEM        ENDIF
C
C PLEM        IF (TAEDTEDG .NE. O_TAEDTEDG) THEN
C PLEM          TARWYEDG1(TAVRWIND) = TAEDTEDG
C PLEM          IF (TAVRWIND .EQ. TAVRWYIDX) TADSPEDG = TAEDTEDG
C PLEM          O_TAEDTEDG = TAEDTEDG
C PLEM        ENDIF
C
C PLEM        IF (TAEDTVAS .NE. O_TAEDTVAS) THEN
C PLEM          TAVASI1(TAVRWIND) = TAEDTVAS
C PLEM          IF (TAVRWIND .EQ. TAVRWYIDX) TADSPVAS = TAEDTVAS
C PLEM          O_TAEDTVAS = TAEDTVAS
C PLEM        ENDIF
C
C PLEM        IF (TAEDTSTR .NE. O_TAEDTSTR) THEN
C PLEM          TASTROBE1(TAVRWIND) = TAEDTSTR
C PLEM          IF (TAVRWIND .EQ. TAVRWYIDX) TADSPSTR = TAEDTSTR
C PLEM          O_TAEDTSTR = TAEDTSTR
C PLEM        ENDIF
C
C PLEM        IF (TAEDTCEN .NE. O_TAEDTCEN) THEN
C PLEM          TARWYMID1(TAVRWIND) = TAEDTCEN
C PLEM          IF (TAVRWIND .EQ. TAVRWYIDX) TADSPCEN = TAEDTCEN
C PLEM          O_TAEDTCEN = TAEDTCEN
C PLEM        ENDIF
C
C PLEM      ENDIF
C
C
C --------------------------------------------------------------------------
C     CAVOK,  CAT1, CAT2, CAT3, CAT3A, CAT3B
C --------------------------------------------------------------------------
C
C -- Only one of CAT1, CAT2, CAT3, CAVOK,0/0,T/O,CLEAR,NP are
C    available at one time (use most recently selected)
C    If a visual button is selected for which data is stored in the
C    visual table, set the visual table index.
C
C
C PLEM      IF (XZWXR .NE. O_XZWXR) THEN
C PLEM        VIS_IND   = XZWXR
C PLEM        TAVISIB   = VIS_VIS  (VIS_IND)
C PLEM        TARVR     = VIS_RVR  (VIS_IND)
C PLEM        TARVR1    = VIS_RVR1 (VIS_IND)
C PLEM        TARVR2    = VIS_RVR2 (VIS_IND)
C PLEM        TARVR3    = VIS_RVR3 (VIS_IND)
C PLEM        TACEILNG  = VIS_CEIL (VIS_IND)
C PLEM        TACLDTOP  = VIS_CLDT (VIS_IND)
C PLEM        TCMSCUD   = VIS_SCUD(VIS_IND)
C PLEM       TCMGFOG   = VIS_GFOG(VIS_IND)
C PLEM        TCMPFOG   = VIS_PFOG(VIS_IND)
C'USD8+
C PLEM        TAFOGTOP  = VIS_FGTOP(VIS_IND)
C PLEM        TAFOGTL   = VIS_FGTL(VIS_IND)
C PLEM        TAFOGPAT  = VIS_FGPAT(VIS_IND)
C'USD8-
C PLEM      ENDIF
C PLEM      O_XZWXR  = XZWXR
C
C -- Every 10 iterations, check for visual conditions matching those in
C    visual table.  The button flags are then updated with the proper st
C    ie. If the button is currently on, but the values no longer
C    match, then it is turned off.  If the values match, but the button
C    on, then it is turned on.
C
C
C PLEM      VIS_ITERATIONS = VIS_ITERATIONS + 1
C
C PLEM      IF (VIS_ITERATIONS .GT. 10) THEN
C PLEM        VIS_ITERATIONS  = 0
C PLEM        DO IND = 1,8                 ! search for any existing conditions
C PLEM          VIS_COND(IND)  = .FALSE.   ! assume condition doesnt exist
C PLEM          VIS_LIGHT(IND) = .FALSE.
C
C PLEM             IF (TAVISIB  .EQ. VIS_VIS(IND)) THEN
C PLEM               IF (TARVR    .EQ. VIS_RVR(IND)) THEN
C PLEM                 IF (TARVR1   .EQ. VIS_RVR1(IND)) THEN
C PLEM                   IF (TARVR2   .EQ. VIS_RVR2(IND)) THEN
C PLEM                     IF (TARVR3   .EQ. VIS_RVR3(IND)) THEN
C PLEM                       IF (TACEILNG .EQ. VIS_CEIL(IND)) THEN
C PLEM                         IF (TACLDTOP .EQ. VIS_CLDT(IND)) THEN
C PLEM                           IF (TCMSCUD  .EQV. VIS_SCUD(IND)) THEN
C PLEM                             IF (TCMGFOG  .EQV. VIS_GFOG(IND)) THEN
C PLEM                               IF (TCMPFOG  .EQV. VIS_PFOG(IND)) THEN
C
C PLEM                                    VIS_COND(IND) = .TRUE.  ! condition ex
C
C PLEM                               ENDIF
C PLEM                             ENDIF
C PLEM                           ENDIF
C PLEM                         ENDIF
C PLEM                       ENDIF
C PLEM                     ENDIF
C PLEM                   ENDIF
C PLEM                 ENDIF
C PLEM               ENDIF
C PLEM             ENDIF
C PLEM        ENDDO
C
C --- Keep activated if all conditions satisfy
C
C PLEM        NEWCAT = .FALSE.
C PLEM        DO IND=1,8
C PLEM          IF ( VIS_COND(IND) ) THEN
C PLEM                  XZWXR  = IND
C PLEM                  NEWCAT = .TRUE.
C PLEM          ENDIF
C PLEM        ENDDO
C PLEM        IF (.NOT. NEWCAT) THEN
C PLEM               XZWXR = 0
C PLEM               O_XZWXR = 0
C PLEM        ENDIF
C
C PLEM     ENDIF
C
C --------------------------------------------------------------------------
C     SPECIAL EFFECTS
C --------------------------------------------------------------------------
C
      IF (TCMSTORM .NEQV. O_TCMSTORM ) THEN
         IF (TCMSTORM) THEN
             TCMCAT     = .FALSE.
             TCMTURLO   = .FALSE.
             TATURB(1)  = 0.33
         ELSE
             TATURB(1)  = 0
         ENDIF
      ELSEIF (TCMCAT .NEQV.  O_TCMCAT) THEN
         IF (TCMCAT) THEN
             TCMSTORM   = .FALSE.
             TCMTURLO   = .FALSE.
             TATURB(1)  = 0.33
         ELSE
             TATURB(1)  = 0
         ENDIF
      ELSEIF (TCMTURLO .NEQV. O_TCMTURLO) THEN
         IF (TCMTURLO) THEN
             TCMCAT     = .FALSE.
             TCMSTORM   = .FALSE.
             TATURB(1)  = 0.33
         ELSE
             TATURB(1)  = 0
         ENDIF
      ENDIF
      O_TCMSTORM = TCMSTORM
      O_TCMCAT   = TCMCAT
      O_TCMTURLO = TCMTURLO
C
C -------------------------------------------------------------------
C Runway Input Validation
C -------------------------------------------------------------------
C
C If wet runway selected then display temp change popup if
C temperature is less than 0
C
C      IF (TCMSPARE(1)) THEN
C         IF (TATEMP(2) .LT. 0) THEN
C            TCMSPARE(3) = .TRUE.
C            CALL DELAYPUT (TCMSPARE(3))
C         ENDIF
C         TCMSPARE(1) = .FALSE.
C      ENDIF
C
C If slush, snow or ice selected then display temp change popup if
C temperature is greater than 5
C
C      IF (TCMSPARE(2)) THEN
C         IF (TATEMP(2) .GT. 5) THEN
C            TCMSPARE(3) = .TRUE.
C            CALL DELAYPUT (TCMSPARE(3))
C         ENDIF
C         TCMSPARE(2) = .FALSE.
C      ENDIF
C
C --------------------------------------------------------------------------
C    VISUAL TRAFFIC
C --------------------------------------------------------------------------
C
C
      IF     (TCMATRCA .AND. .NOT. O_ATRCA ) THEN
C
              TCMATRLT  = .FALSE.
              TCMATRRT  = .FALSE.
              TCMATRCB  = .FALSE.
C
      ELSEIF (TCMATRLT .AND. .NOT. O_ATRLT ) THEN
C
              TCMATRCA  = .FALSE.
              TCMATRRT  = .FALSE.
              TCMATRCB  = .FALSE.
C
      ELSEIF (TCMATRRT .AND. .NOT. O_ATRRT ) THEN
C
              TCMATRCA  = .FALSE.
              TCMATRLT  = .FALSE.
              TCMATRCB  = .FALSE.
C
      ELSEIF (TCMATRCB .AND. .NOT. O_ATRCB ) THEN
C
              TCMATRCA  = .FALSE.
              TCMATRLT  = .FALSE.
              TCMATRRT  = .FALSE.
C
      ENDIF
C
      O_ATRCA  =  TCMATRCA
      O_ATRLT  =  TCMATRLT
      O_ATRRT  =  TCMATRRT
      O_ATRCB  =  TCMATRCB
C
C
C FIND_MP
C *************************************************************************
C *************************************************************************
C
C      M A P    F U N C T I O N S
C
C *************************************************************************
C *************************************************************************
C --------------------------------------------------------------------------
C  CONVERSION FROM SYSTEM TO I/F LABELS
C --------------------------------------------------------------------------
C
C -- Copy Frequency into spare label for page 27 display
C
      TAMISFRE = ( RXMISFRE(3) / 1000.00 )
C
C
C -- Computation of TARVRTD
C
      IF (RXMISGPX(3).EQ.0) THEN
        TARVRTD = RVRTD*NM_FT - (1000                   -RBCGGPX(1))
      ELSE
        TARVRTD = RVRTD*NM_FT - (RXMISGPX(3)-RXMISRWE(3)-RBCGGPX(1))
      ENDIF
      TARVRTD = TARVRTD*(-1)
C
C -- Is an ILS available on this rwy and is it tuned ??
C
C'USD8+
      IF ( RBFVOI(1) .OR. RBFVOI(2) ) THEN
        IF ( RBFVOI(1) ) THEN
          TMMILSON = ( (RBSVOR(1) .GT. 0) .AND.
     .                 (RXMISTYN(3) .EQ. 1) )
        ELSE
          TMMILSON = ( (RBSVOR(2) .GT. 0) .AND.
     .                 (RXMISTYN(3) .EQ. 1) )
        ENDIF
      ELSE
        TMMILSON = .FALSE.
      ENDIF
C
C'USD8-
C'USD8      TMMILSON = (
C'USD8     .                   (       (RBSILS(1).NE.0)
C'USD8     .                     .OR.  (RBSILS(2).NE.0)
C'USD8     .                     .OR.  (RBSILS(3).NE.0)   )
C'USD8     .        .AND.
C'USD8     .                   ( RXMISTYN(3).EQ.1         )
C'USD8     .           )
C
C
C --------------------------< TUNED LOGIC START >------------------------------
C
          DO I=1,MAX_TUN
             TUNED(I) = 0
          ENDDO
          TUNEPTR = 1
C
C --- Tune ILS/VOR stations
C
          IF ( VOR_SGNL_1 .GT. 0.002 ) THEN
              IF ( ILS_INDX_1 .NE.0 ) THEN
                  TUNED (TUNEPTR) = ILS_INDX_1
                  TUNEPTR         = TUNEPTR + 1
              ENDIF
          ENDIF
C
          IF ( VOR_SGNL_2 .GT. 0.002 ) THEN
              IF ( ILS_INDX_2 .NE.0 ) THEN
                  TUNED (TUNEPTR) = ILS_INDX_2
                  TUNEPTR         = TUNEPTR + 1
              ENDIF
          ENDIF
C
C --- Tune ILS stations
C
C'USD8          IF ( ILS_SGNL_1 .GT. 0.002 ) THEN
C'USD8              IF ( ILS_INDX_1 .NE.0 ) THEN
C'USD8                  TUNED (TUNEPTR) = ILS_INDX_1
C'USD8                  TUNEPTR         = TUNEPTR + 1
C'USD8              ENDIF
C'USD8          ENDIF
C'USD8          IF ( ILS_SGNL_2 .GT. 0.002 ) THEN
C'USD8              IF ( ILS_INDX_2 .NE.0 ) THEN
C'USD8                  TUNED (TUNEPTR) = ILS_INDX_2
C'USD8                  TUNEPTR         = TUNEPTR + 1
C'USD8              ENDIF
C'USD8          ENDIF
C'USD8C         IF ( ILS_SGNL_3 .GT. 0.002 ) THEN
C'USD8C             IF ( ILS_INDX_3 .NE.0 ) THEN
C'USD8C                 TUNED (TUNEPTR) = ILS_INDX_3
C'USD8C                 TUNEPTR         = TUNEPTR + 1
C'USD8C             ENDIF
C'USD8C         ENDIF
C
C
C --- Tune NDB/ADF stations
C
          IF ( NDB_SGNL_1 .GT. 0.002 ) THEN
              IF ( NDB_INDX_1 .NE.0 ) THEN
                  TUNED (TUNEPTR) = NDB_INDX_1
                  TUNEPTR         = TUNEPTR + 1
              ENDIF
          ENDIF
          IF ( NDB_SGNL_2 .GT. 0.002 ) THEN
              IF ( NDB_INDX_2 .NE.0 ) THEN
                  TUNED (TUNEPTR) = NDB_INDX_2
                  TUNEPTR         = TUNEPTR + 1
              ENDIF
          ENDIF
C         IF ( NDB_SGNL_3 .GT. 0.002 ) THEN
C             IF ( NDB_INDX_3 .NE.0 ) THEN
C                 TUNED (TUNEPTR) = NDB_INDX_3
C                 TUNEPTR         = TUNEPTR + 1
C             ENDIF
C         ENDIF
C
C --- Tune VOR  stations
C
C'USD8          IF ( VOR_SGNL_1 .GT. 0.002 ) THEN
C'USD8              IF ( VOR_INDX_1 .NE.0 ) THEN
C'USD8                  TUNED (TUNEPTR) = VOR_INDX_1
C'USD8                  TUNEPTR         = TUNEPTR + 1
C'USD8              ENDIF
C'USD8          ENDIF
C'USD8          IF ( VOR_SGNL_2 .GT. 0.002 ) THEN
C'USD8              IF ( VOR_INDX_2 .NE.0 ) THEN
C'USD8                  TUNED (TUNEPTR) = VOR_INDX_2
C'USD8                  TUNEPTR         = TUNEPTR + 1
C'USD8              ENDIF
C'USD8          ENDIF
C'USD8C         IF ( VOR_SGNL_3 .GT. 0.002 ) THEN
C'USD8C             IF ( VOR_INDX_3 .NE.0 ) THEN
C'USD8C                 TUNED (TUNEPTR) = VOR_INDX_3
C'USD8C                 TUNEPTR         = TUNEPTR + 1
C'USD8C             ENDIF
C'USD8C         ENDIF
C
C ------------------------------------------------------------------------
C   MAP HEADER - A/C HEADING MAGNETIC SET
C ------------------------------------------------------------------------
C
      IF (TCMHDMAG) THEN
          TAHDGSET = TAHDGMAG
          TCMAVHDG = .TRUE.
          TCMHDMAG = .FALSE.
      ELSE
C
C change 27Oct2023
C          TAHDGMAG  = TAHDGSET - RTAVAR
          TAHDGMAG = TAHDGSET
      ENDIF
C
C -----------------------------------------------------------------------
C   "MAP TO" ICAO & RUNWAY NAME HANDLING
C -----------------------------------------------------------------------
C
      MAP_TO = ( TMICAOSL(1)  .OR. TMICAOSL(2) .OR.
     -           TMICAOSL(3)  .OR. TMICAOSL(4) .OR.
     -           TMICAOSL(5)  .OR. TMICAOSL(6) .OR.
     -           TMICAOSL(7)  .OR. TMICAOSL(8) .OR.
     -           TMICAOSL(9)  .OR. TMICAOSL(10) .OR.
     -           TMICAOSL(11) .OR. TMICAOSL(12) .OR.
     -           TMICAOSL(13) .OR. TMICAOSL(14) .OR.
     -           TMICAOSL(15) )
C
      IF (TMHAUTO) THEN
        IF (.NOT. MAP_TO) THEN
          C_TM_ICAO1 = C_ICAO
          C_TM_RWY1  = C_RWY1
        ENDIF
      ELSE
        IF (MAP_TO) THEN
          DO I=1,15
            IF (TMICAOSL(I)) THEN
              C_TM_ICAO1 = C_ICAO_TBL(I)
              C_TM_RWY1  = C_RWY_TBL(I)
              TMICAOSL(I) = .FALSE.
              GOTO 9999
            ENDIF
          ENDDO
        ENDIF
      ENDIF
C
 9999 CONTINUE
C
C
C --- Exit
C
C !FM+
C !FM   7-Jul-92 10:00:06 M.WARD
C !FM    < THIS LOGIC IS TO RESET THE MASTER CAUTION LIGHT EVERY 5 MINUTES
C !FM      TO PREVENT IT FROM HEATING UP AND STOPPING WORKING. I HAD TO PUT
C !FM      IT IN THIS MODULE AS THERE ARE NO ANCILLARY UNFREEZABLE MODULES >
C !FM
      IF ( VACST .EQ. 0 .OR. TCFFLPOS .OR. TCFTOT ) THEN
        TIMER = TIMER + 1
      ELSE
        TIMER = 0
      ENDIF
C
      IF ( TIMER .GT. 1200 ) THEN
        AM$CRST = .TRUE.
        TIMER = 0
      ENDIF
C !FM-
      RETURN
      END
