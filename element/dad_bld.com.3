#!  /bin/csh -f
#!
#!  $Revision: DAD_BLD - Build a DFC database file  Version 1.0 (GOF) 9/1991$
#!
#! &
#! %
#! @
#! &$.CDB
#! @$.
#! &$*.XSL
#! @CDB_SPARE.
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set CAE_DFC_PLUS=`logicals -t cae_dfc_plus`
#
set SIMEX_DIR="`logicals -t cae_simex_plus`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`"
cd $SIMEX_WORK
#
set FSE_MAKE=$SIMEX_WORK/dfct_$FSE_UNIK.tmp.1
#
set FSE_DATABASE=""
set FSE_SOURCE=""
set EOFL=`sed -n '$=' "$argv[3]"`

set FSE_LINE="`sed -n '1'p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  goto FSE_BUILD_END
endif
#
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
set tmp_name=`norev $FSE_FILE`
set FSE_NAME=$tmp_name:t
set FSE_NAME=$FSE_NAME:r
set FSE_TYPE=$tmp_name:e
#
set FSE_DATABASE="`echo $SIMEX_WORK/$FSE_NAME.$FSE_TYPE.1 | tr '[A-Z]' '[a-z]'`"
set FSE_MAP="`echo $SIMEX_WORK/$FSE_NAME.map.1 | tr '[A-Z]' '[a-z]'`"
set FSE_SOURCE="`echo $SIMEX_WORK/$FSE_NAME.dsr.1 | tr '[A-Z]' '[a-z]'`"
#
set FSE_LINE="`sed -n '2'p $argv[3]`"
set FSE_PARENT="`echo '$FSE_LINE' | cut -c4-`"
#
if (-e "$FSE_SOURCE") rm $FSE_SOURCE
touch $FSE_SOURCE
set lcount=3
FSE_BUILD_LIST:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    goto FSE_BUILD_END
  endif
  @ lcount = $lcount + 1
#
  set FSE_CODE="`echo '$FSE_LINE' | cut -c1`"
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
  set tmp_name=`norev $FSE_FILE`
  set FSE_NAME=$tmp_name:t
  set FSE_NAME=$FSE_NAME:r
  set FSE_TYPE=$tmp_name:e
#
  if ("$FSE_TYPE" == "doh") set FSE_HARDWARE=$FSE_FILE  
  if ("$FSE_TYPE" == "doc") set FSE_CONFIGURE=$FSE_FILE  
  if ("$FSE_TYPE" == "dox") set FSE_TRANSFER=$FSE_FILE  
  echo $FSE_FILE >>&$FSE_SOURCE
goto FSE_BUILD_LIST
#
FSE_BUILD_FULL:
if ("$FSE_DATABASE" == "") then
  echo "%FSE-E-NOSRC, No source file in the list."
  goto FSE_BUILD_END
endif
#
setenv SOURCE $FSE_SOURCE
setenv TARGET $FSE_MAKE
setenv SIMEX  " "
$CAE_DFC_PLUS/support/dfcp.com link \
  -output=\"$FSE_DATABASE\" \
  -map=\"$FSE_MAP\" \
   \"$FSE_HARDWARE\" \"$FSE_CONFIGURE\" \"$FSE_TRANSFER\"
set stat=$status
#
unsetenv SIMEX
unsetenv TARGET
unsetenv SOURCE
#
if ($stat != 0) then
  if (-e "$FSE_DATABASE") rm $FSE_DATABASE
  if (-e "$FSE_MAP") rm $FSE_MAP
  if (-e "$FSE_SOURCE") rm $FSE_SOURCE
  goto FSE_BUILD_END
endif
#
if (! -e "$FSE_DATABASE") then
  if (-e "$FSE_MAP") rm $FSE_MAP
  if (-e "$FSE_SOURCE") rm $FSE_SOURCE
  goto FSE_BUILD_END
endif
#
#mv $FSE_DATABASE $FSE_DATABASE
#
set FSE_INFO="`fmtime $FSE_DATABASE | cut -c1-17`"
if ("$FSE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_DATABASE)"
else
  touch $argv[4]
  echo "0MNBBU $FSE_PARENT,,,,," >>$argv[4]
  echo "1MMBBU $FSE_DATABASE,,,DAD_BLD.COM,,Produced by dfcp link on $FSE_INFO" >>$argv[4]
  echo "1CMTBU $FSE_MAP,,,DAD_BLD.COM,,Produced by dfcp link on $FSE_INFO" >>$argv[4]
  echo "1CMTBU $FSE_SOURCE,,,DAD_BLD.COM,,Produced by dad_bld on $FSE_INFO" >>$argv[4]
endif
#
FSE_BUILD_END:
#
if (-e "$FSE_MAKE") rm $FSE_MAKE
#
exit
