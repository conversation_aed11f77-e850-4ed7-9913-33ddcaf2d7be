#!  /bin/csh -f
#!  $Revision: PGL_BLD - Link page(s) object files V1.4 Feb-92$
#!
#! &linkpage.dat
#! &
#! @
#! ^
#!  Version 1.0: <PERSON><PERSON>
#!     - Initial version of this script
#!  Version 1.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!  Version 1.2: <PERSON><PERSON> (17-Jul-91)
#!     - changed listpage.dat to list_pgc.dat
#!  Version 1.3: <PERSON> (20-Aug-91)
#!     - write a character string instead of a numeric value in FSE_FULL.
#!  Version 1.4: <PERSON> (27-Feb-92)
#!     - replaced page.sgi by page.gra
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/pgll_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/pglm_$FSE_UNIK
#
set FSE_FULL="`revl $SIMEX_WORK/list_pgc.dat +`"
set FSE_PAGE="`grep list_pgc.dat $argv[3]`"
if ("$FSE_PAGE" == "") then
  echo >$FSE_LIST "$FSE_FULL"
endif
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set FSE_NAME="`norev $FSE_FILE`"
  set FSE_NAME="$FSE_NAME:t"
  if ("$FSE_PAGE" == "") then
    if (("$FSE_NAME" != "page.dat") && ("$FSE_NAME" != "linkpage.dat")) then
      echo "$FSE_FILE" >>$FSE_LIST
    endif
    if ("$FSE_NAME:e" == "obj") then
      set  FSE_NAME="`echo $FSE_NAME:r | cut -c5-`"
      echo "$FSE_NAME" >>$FSE_FULL
    endif
  else
    echo "$FSE_FILE" >>$FSE_LIST
  endif
end
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias pgclink
pgclink USER=SMP,INFILE=list_pgc.dat
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
echo >$argv[4] "0MNBBI page.gra"
set EOFL=`sed -n '$=' "$FSE_MAKE"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE="`sed -n '$lcount'p $FSE_MAKE`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $FSE_MAKE ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_KIND="`echo '$FSE_LINE' | cut -c1-12`"
  if ("$FSE_KIND" == " Generated  ") then
    set FSE_FILE="`echo '$FSE_LINE' | cut -c13-`"
    set FSE_INFO="`fmtime $FSE_FILE | cut -c1-17`"
    if ("$FSE_INFO" == "") then
      echo "%FSE-E-INFOFAILED, File information not available ($FSE_FILE)"
      rm $FSE_MAKE $argv[4]
      exit
    endif
#
    set FSE_NAME="`norev $FSE_FILE`"
    if ("$FSE_NAME:t" == "page.dat") then
      echo >>$argv[4] "1MRBOI $FSE_FILE,,,PGL_BLD.COM,,Produced by PGCLINK on $FSE_INFO"
      echo >>$argv[4] "2UNUUU list_pgc.dat"
    else
      echo >>$argv[4] "1CRTTI $FSE_FILE,,,,,Produced by PGCLINK on $FSE_INFO"
    endif
  endif
end
#
rm $FSE_MAKE
exit
