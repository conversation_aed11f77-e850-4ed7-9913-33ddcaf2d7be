C --- =======================================================================
C
C --- Name                    RAP control module
C --- Module_ID               TRSTR
C --- File_ID                 USD8TRC.FOR
C --- Documentation_no        -----
C --- Customer                USAIR USD8
C --- Author                  <PERSON>, ing./Eng.
C --- Date                    27-MAY-1991
C --- Application             RAP Control module
C
C --- =======================================================================
C
C'Revision_history
C
C  usd8trc.for.15 11Apr1992 14:41 usd8 LSAK
C       < Changed trmasage by trsetmsg to match setup edit page >
C
C  usd8trc.for.14 11Apr1992 14:16 usd8 LSAK
C       < added TRELREC logic for a recall from EL panel >
C
C  usd8trc.for.13 11Apr1992 12:57 usd8 lsak
C       < added trsnpav logic for EL panel >
C
C  usd8trc.for.12 11Apr1992 11:26 usd8 LSAK
C       < ADDED HCETMODE = 0 IN CFRESET >
C
C  usd8trc.for.11  8Apr1992 06:30 usd8 LSAK
C       < Should release MFADE after CDB recalled and timer elapsed >
C
C  usd8trc.for.10  8Apr1992 05:49 usd8 Louis S
C       < Release MFADE after recall finished >
C
C  usd8trc.for.9  3Apr1992 16:27 usd8 W. Pin
C       < I/O server initialized in sp0c0init.for.  Call to local_copy now
C         done by dispatcher. >
C
C  usd8trc.for.8  2Apr1992 01:37 usd8 R.Gatbo
C       < first time usair-dash 8 integration. Remove throttle backdrive
C         for now until the correct backdrive procedure is known. standby..
C         . >
C
C File: /usr1/cae/stdif/rap/usd8trc.for.7
C       Modified by: D.MONTREUIL
C       Thu Oct 31 12:33:52 1991
C       < During a rap around, make sure that we begin overwritting the
C         rap file with TRIOBASE==1. >
C
C File: /usr1/cae/stdif/rap/usd8trc.for.6
C       Modified by: D.MONTREUIL
C       Thu Oct 31 12:32:33 1991
C       < During a rap playback, make sure that if rap around have occured,
C          that the first I/O of the rap file will be TRIOBASE==1. >
C
C File: /usr1/cae/stdif/rap/usd8trc.for.5
C       Modified by: d.montreuil
C       Thu Oct 31 12:31:11 1991
C       < At the beginning of a rap playback, compute the rap starting
C         pointer on disk by using a multiple of tr16siz. >
C
C File: /usr1/cae/stdif/rap/usd8trc.for.4
C       Modified by: D.MONTREUIL
C       Thu Oct 31 12:25:17 1991
C       < At the end of the playback, put the time to go:  TRTIMETO=0.0 >
C
C File: /usr1/cae/stdif/rap/usd8trc.for.3
C       Modified by: D.MONTREUIL
C       Thu Oct 31 12:02:10 1991
C       < In section TRCONTRO=9, at the beginning of the playback in
C         progress, put back the state of hte sound mute before the
C         playback. >
C
C File: /usr1/cae/stdif/rap/usd8trc.for.2
C       Modified by: D.MONTREUIL
C       Thu Oct 31 12:00:35 1991
C       < AFTER A RECALL, PUT BACK THE OLD STATE OF THE SOUND MUTE. >
C
C File: /cae1/ship/usd8trc.for.2
C       Modified by: MOND
C       Wed Jun 26 17:09:29 1991
C       < AFTER A SNP RECALL SET TRSPCCOM(8)=TRUE, FOR VIFS.FOR MODULE TO
C         INITIALIZE HIS INTERNAL HDG LABEL:  LAACMHDR WITH THE CURRENT
C         CDB HEADING: TAACMHDR. >
C
C File: /cae1/rap/transf/usd8trc.for.3
C       Modified by: MOND
C       Wed Jun 19 02:52:45 1991
C       < AFTER A SNP RECALL, SET TRSPCCOM(10) = TRUE, FOR RT.FOR HANDLE
C         THE RECALL LIKE A REPOSITION. >
C
C File: /cae1/rap/usd8trc.for.8
C       Modified by: mond
C       Wed Jun 05 05:34:22 1991
C       < CALLING SCALING AND SCALOUT IF IN THE BACKDRIVE THROTTLE MODE.
C
C File: /cae1/rap/usd8trc.for.7
C       Modified by: mond
C       Wed Jun 05 04:28:22 1991
C       < at the end of a snapshot recall or a rap playback, wait until
C         the throttles are backdriven to the final positon before ending.
C         added a watch dog timer of 30 sec if the throttles are not
C         backdriven correctly. >
C
C File: /cae1/rap/usd8trc.for.6
C       Modified by: mond
C       Wed Jun 05 01:42:22 1991
C       < during a snapshot recall or a rap playback, backdrive the throttle
C         with the labels:  saeng(1)=t and sathcont(1)=cmd.  the cmd depend
C         on the desired value:  etla and the present value:  iaetla. >
C
C File: /cae1/rap/usd8trc.for.5
C       Modified by: mond
C       Thu May 30 04:49:22 1991
C       < during a rap record, compute a timer:  trrtime from 0 to
C         maxraptm, even during a rap around.  this is used to handle the
C         event marker. >
C
C File: /cae1/rap/usd8trc.for.4
C       Modified by: MOND
C       Thu May 30 04:45:51 1991
C       < DURING RAP AROUND RECORDING, DELETE THE OLD EVENT MARKER IF IT
C         IS OVERWRITTEN. >
C
C File: /cae1/rap/usd8trc.for.2
C       Modified by: mond
C       Thu May 30 03:24:27 1991
C       < added logic to handle event marker >
C
C File: /cae1/rap/usd8trc.for.5
C       Modified by: mond
C       Wed May 29 06:01:18 1991
C       < added a delay of 60 sec before starting the initialize and the
C         file opening at the beginning. >
C
C File: /cae1/rap/usd8trc.for.4
C       Modified by: MOND
C       Wed May 29 05:39:05 1991
C       < ADDED A CALL TO CAE_IO_LOCAL_COPY BECAUSE IT IS NOT DONE BY THE
C         DISP IN FOREGROUND. >
C
C      10-APR-91 23:27:40 MOND
C       NOW AT THE START OF A RAP PLAYBACK, TRBOFFR IS CALCULATED AS A MULTIPLE
C       OF 1.6 ms BUFFERS.  SINCE THE SCS ARE WRITTEN TO DISK AT A RATE
C       OF 1.6 ms.
C
C      9-Mar-91 23:27:40 MOND
C       PUT THE DECLARATION OF OLDTRQOPT INTO THE TRS.INC FILE
C
C      9-Mar-91 18:19:56 MOND
C       REPLACE THE LABEL:  RAPDISCSZ---->TRRCSIZ !!!!!!!
C
C       6-MAR-91 22:53:32 MOND
C       CHANGED THE LABEL USED AS THE OFFSET ON DISK DURING A SNAPSHOT STORE
C       TRBOFFW--->TRSOFFW.  BECAUSE IT IS ALREADY USED BY RAP.
C
C      27-Feb-91 22:53:32 MOND
C       RAP AROUND IS THE DEFAULT MODE.  THIS MODE IS CONTROL BY THE LABEL:
C       TRWRAPRQ.  TRWRAPRQ=TRUE:  RAP AROUND, TRWRAPRQ=FALSE:  NO RAP AROUND.
C
C      27-Feb-91 22:53:32 MOND
C       ADDED SOME NEW CODE TO HANDLE RAP AROUND, IN SECTION:
C       TRCONTRO=5 & TRCONTRO=6.
C
C      27-Feb-91 22:53:32 MOND
C       USE TRRAPSIZ INSTEAD OF TRRCSIZ TO REFLECTED THE SIZE OF
C       SCS BYTES & WORDS.
C
C      26-Feb-91 22:53:32 MOND
C       DURING A RAP RECORD IN SECTION:  TRCONTRO=4, INITIALIZE TRSOFFW
C       WITH THE OFSET CORRESPONDING TO THE SNP NUMBER, IF A SNP STORE
C       REQUEST IS MADE.
C
C      9-Feb-91 22:53:32 MOND
C       DISABLED TEMPORALY A SNP TAKE DURING A RAP RECORD.
C
C
C      5-Feb-91 23:08:59 MOND
C       ADDED LOGIC TO CHECK IF THE SNAPSHOT RECALL WAS COMING FROM THE
C       PTP PANEL;  IF SO, WE RECALL THE LAST SNAPSHOT TAKEN.
C
C      5-Feb-91 09:52:08 MOND
C       AFTER A SNP RECALL PUT THE SNP NUMBER BACK TO THE LASTSNP+1.
C
C
C      4-Feb-91 09:38:42 MOND
C       ADDED SOME LABEL TO BACKDRIVE THE C/F.
C
C      4-Feb-91 08:38:51 MOND\
C       CHANGE THE LABEL TRSNPREC FOR TRSNPREC/
C
C   #404 12-Dec-90 MOND
C         CHANGED TRFILDCBS AND TRFCBFIL FOR TRFILFCB
C
C   #403 12-Dec-90 MOND
C         CONVERTED TO VAX
C
C   #402 12-Nov-90 FALJ
C         STOP REC LOGIC UPDATED
C
C   #401 12-Nov-90 FALJ
C         DEMO # 2-10 LOGIC ADDED
C
C   #315 06-Nov-90 FALJ
C         TRRAPSIZ & TR16SIZ ADDED
C
C   #313 29-Oct-90 MOND/LS/NT
C         FIXING TRPROGCONTL ERROR IN SECTION 900
C
C   #312 21-Oct-90 NT
C         FIXED PLAYBACK ERROR: SET TRPROCCOM(I,15) TO TRUE IN
C         PASS1 TO END PLAYBACK PROCESS
C         ADDED SCB IO ERROR COUNTER IN TRCONTRO 05
C         FIXED RECALL ERROR AFTER RAP PLAYBACK: RESET TRNOXREF TO FALSE
C         BEFORE RECALL TO RECALL CDB SECTIONS
C         RECALL SCB #2 IN TRCONTRO SECTION 09 FOR THE BEGINNING OF PLB
C         LOCAL LABEL SCB2FIN IN TR.INC RAP COMMON
C
C   #306 15-Oct-90 NT
C         ADDED TRBUFFLG(3)=T IN TRCONTRO 01 (SCR STORE) & 07 (SCR RECALL)
C         TO MAKE SURE QIO IS READY
C
C   #305 13-Oct-90 MOND
C         UPDATE SECTION FOR RAP REPLAY TO CHECK FOR I/O COMP.
C
C   #304 13-Oct-90 MOND
C         ADD LOGIC TO CHECK IF I/O OF SCB IS FINISH BEFORE ISSUING AN OTHER
C         I/O
C
C   #303 10-Oct-90 FALJ
C         SCS BYTE NEW LOGIC ADDED
C
C   #301 28-Sep-90 FALJ
C         RAP AROUND LOGIC ADDED
C
C   #221 04-Sep-90 FALJ
C         RECORD IN PROGRESS - TRRAPTIM LIMIT ADDED
C
C   #220 04-Sep-90 FALJ
C         POWER FAILURE SNP STORE PARAMETER = 9000
C
C   #219 04-Sep-90 FALJ
C         INITIATE DEMO - SAVE DESCRIPTION - TRSETNUM -> DEM
C
C   #218 04-Sep-90 FALJ
C         INITIATE DEMO - SAVE DESCRIPTION - UPDATED
C
C   #217 29-Aug-90 FALJ
C         DEMO EDIT UPDATED
C
C   #216 29-Aug-90 FALJ
C         DEMO EDIT - TRPROTCD --> TRDEMCOD
C
C   #215 29-Aug-90 FALJ
C         TRDEMREC --> TRDEMPBK
C
C   #214 29-Aug-90 FALJ
C         TRDEMREP LOGIC ADDED IN
C
C   #213 29-Aug-90 FALJ
C         DEMO PLAYBACK REQUEST - TRBOFFR = 0
C
C   #212 29-Aug-90 FALJ
C         TRRAPREP = .T. WHEN DEMO PBK
C
C   #211 29-Aug-90 FALJ
C         DURING PLAYBACK TRTIMETO = TRTIME
C
C   #210 29-Aug-90 FALJ
C         TRTIMETO INIT TO TRDEMTM FOR DEM PBK
C
C   #209 29-Aug-90 FALJ
C         DEMO EDIT - TRBYXFER UPDATED
C
C   #208 23-Aug-90 FALJ
C         TRFILNO = DEM ADDED IN TRC8
C
C   #207 23-Aug-90 FALJ
C         400 - 7 - TRDEMPBK = .false.
C
C   #206 23-Aug-90 FALJ
C         DEMO INIT UPDATED
C
C   #205 23-Aug-90 FALJ
C         TRDEMTM LOGIC ADDED IN
C
C   #204 20-Aug-90 FALJ
C         PASS1 - TRPWRTSO = .false.
C
C   #203 13-Aug-90 FALJ
C         TRPWRSTO LOGIC ADDED
C
C   #202 13-Aug-90 FALJ
C         TRUATST = ACTIVE WHEN VALID TRANSFER
C
C   #105 05-Jul-90 FALJ
C         SET/UAT/DEM/PWR LOGIC ADDED IN
C
C   #104 05-Jul-90 FALJ
C         ----------------
C
C   #047 01-Jun-90 FALJ
C         PASS1 UPDATED WITH DIRECTORY AND TRMONUTN
C
C   #045 13-May-90 FALJ
C         SET-UP EDIT COMMENTED OUT TEMPORARELY
C
C   #044 12-May-90 FALJ
C         SCALL IN & OUT COMMENTED OUT
C
C   #043 30-Apr-90 FALJ
C         SCALL ADD ADJUSTED
C
C   #042 30-Apr-90 FALJ
C         TRSNPNUM = 1 ON SNP DEL
C
C   #041 30-Apr-90 FALJ
C         SCALL IN & OUT LOGIC ADDED IN
C
C   #040 30-Apr-90 FALJ
C         TRSNPREC = TRUE AFTER RECALL COMMENTED OUT
C
C   #039 25-Apr-90 FALJ
C         DELETE ALL SNAPSHOTS LOGIC UPDATED
C
C   #038 25-Apr-90 FALJ
C         TRJMP*** --> TRSNP***
C
C   #035 25-Apr-90 FALJ
C         ENTRY POINT ADDED IN
C
C   #034 24-Apr-90 FALJ
C         TCMSOUND LOGIC ADDED FOR PBK
C
C   #033 23-Apr-90 FALJ
C         MPLBK LOGIC ADDED
C
C   #032 23-Apr-90 FALJ
C         B***_BYT_CNT --> B***BYSZ
C
C   #031 20-Apr-90 FALJ
C         C/F MADE CRITICAL
C
C   #030 20-Apr-90 FALJ
C         ----------------
C
C   #029 20-Apr-90 FALJ
C         C/F LOGIC ADDED
C
C   #028 20-Apr-90 FALJ
C         SCR --> 100 MS BAND
C
C   #027 20-Apr-90 FALJ
C         SCR_BYT_CNT --> SCRN
C
C   #024 11-Apr-90 FALJ
C         SCR FREQ = 200 MS
C
C   #023 10-Apr-90 FALJ
C         400 - TRFILNO = SCR
C
C   #022 10-Apr-90 FALJ
C         B***_PTR = 1 ADDED IN REC IN PROG
C
C   #020 04-Apr-90 FALJ
C         TRBOFFR LOGIC UPDATED
C
C   #019 04-Apr-90 FALJ
C         PBK INIT UPDATED
C
C   #018 04-Apr-90 FALJ
C         REC & PBK INIT CLEANED
C
C   #017 03-Apr-90 FALJ
C         TRCONTRO = 5 CLEAN UP
C
C   #016 02-Apr-90 FALJ
C         TRMAXSPS INIT UPDATED
C
C   #015 02-Apr-90 FALJ
C         TRMAXSPS INITIALIZED
C
C   #014 28-Mar-90 FALJ
C         TRFILNO INIT ON SNP/STP RECALL
C
C   #013 28-Mar-90 FALJ
C         SNP NO. LIMIT ADJUSTED
C
C   #012 18-Mar-90 FALJ
C         JMPNUM COMMENTED OUT
C
C   #011 26-Mar-90 FALJ
C         UPDATES AFTER CDB UPD
C
C   #008 19-Mar-90 FALJ
C         TRSTOREC = .false. ADDED TO PASS1
C
C   #007 15-Mar-90 FALJ
C         NEW CDB LABELS INIT IN PASS1 SECT
C
C   #006 15-Mar-90 FALJ
C         ENTRIES UPDATED
C
C   #004 12-Mar-90 FALJ
C         ENTRY POINT REMOVED
C
C'
C
C --- =======================================================================
C
C
      SUBROUTINE USD8TRC
C                *******
C
C
      IMPLICIT NONE
C
C
C --- =======================================================================
C --- RAP INCLUDE FILE
C --- =======================================================================
C
C
CVAX
CVAX  INCLUDE 'USD8TR.INC/LIST'                   ! CDB        declarations
CVAXEND
C                                                 ! PARAMETER  declarations
CIBM
      INCLUDE 'usd8tr.inc'                        ! CDB        declarations
CIBMEND
C                                                 ! PARAMETER  declarations
C                                                 ! INTERFACE  declarations
C
C --- --------------------------------------------
C
CVAX
CVAX  INCLUDE 'USD8TRS.INC/LIST'                  ! RAP SPARE COMMON decl
CVAXEND
C                                                 ! PARAMETER  declarations
CIBM
      INCLUDE 'usd8trs.inc'                       ! RAP SPARE COMMON decl
CIBMEND
C                                                 ! PARAMETER  declarations
C
C
C
C --- =======================================================================
C --- LOCAL VARIABLES
C --- =======================================================================
C
C
C
C --- --------------------------------------------
      REAL*4
C --- ------
C
     -  TRINSTTI                                  ! instructor time input
     -, TRMRAPIN                                  ! inst time to go mRAP
     -, TRTIMETO                                  ! time left in RAP
     -, TREVTIM                                   ! event marker time
     -, DEADTHR                                   ! dead band for throttle.
     -, TRRTIME                                   ! continuous time
     -, TRTHRFWD                                  ! forw command throttle.
     -, TRTHRBWD                                  ! reverse command throttle.
     -, TRTHRRST                                  ! rest command throttle.
CJF  -, TRCONV                                    ! convert tine to blocks
CJF  -, TREVENTI                                  ! time of event marker
CJF  -, TRRAPTIM                                  ! RAP time limit for pbk
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  BASE                                      ! base,of rap for interface
     -, END                                       ! label 99000
     -, I                                         ! do loop indice
     -, IJ                                        ! do loop indice
     -, INTAV                                     ! average intv over last 8
     -, INTIMP                                    ! time left for peak value
     -, INTIMS                                    ! time left for 2ND highest
     -, INTOT                                     ! total  of last 8 intevals
     -, INTPEAK                                   ! peak intv of last few sec
     -, INTRV                                     ! interval for chrono misec
     -, INTRVP                                    ! page value of inteval
     -, INTSEC                                    ! second highest peak
     -, J                                         ! do loop indice
     -, K                                         ! do loop indice
     -, KL                                        ! do loop indice
     -, KOUNT                                     ! total frz flash timer
     -, LASTSET                                   ! last setup    number
     -, LASTSNP                                   ! last snapshot number
     -, MAXKOUNT                                  ! max val of KOUNT
     -, NEXT                                      ! label 905
     -, PASS1TIM                                  ! pass 1 completion timer
     -, PROGCNTL                                  ! label 900
     -, PROGEXE                                   ! label 999
     -, RAPNUM                                    ! RAP number
     -, SCALADD                                   ! scale address
     -, SCALSYS     (10)                          ! scale system
     -, SECRETCO                                  ! setup secret code
     -, SETNUM                                    ! setup number
     -, STRT        (3)                           ! for chrono internal use
     -, TEMPBASE                                  ! record base on sto/rec
     -, TOG                                       ! 100 ms exec toggle byte
     -, TPLYBK                                    ! playback status flag
     -, TRATTMR                                   ! auto throttle slew timer
     -, TREVENTB                                  ! block # of event mark
     -, TRSNPLST                                  ! lAST SNAPSHOT TAKEN
     -, TRMAXBLK                                  ! event max block ofset
     -, TROLDEXN                                  ! prev value of exercise #
     -, TROLDNUM                                  ! old value of edit jmpnum
     -, TRPROCIN    (NOPRC)                       ! process returned trindex
     -, TRPROGCN    (NOPRC,NOPST,NOPMD)           ! program control array
     -, TRUNFCNT                                  ! unfreeze type counter
     -, TRUNFTMR                                  ! unfreeze timer
     -, TRPWRCNT                                  ! PWR failure counter
     -, TRTHRTIMR                                 ! throttle Watch dog timer
C
CJF  -, TRRAPSIZ                                  ! Size of a 10  min RAP
CJF  -, TR16SIZ                                   ! Size of a 1.6 sec RAP
C
CJF  -, TRBOFFSR                                  ! write blk offset sto/rec
CJF  -, TRCFTMR                                   ! cf slow release timer
CJF  -, TRSETNUM                                  ! previous setup number
     -, SCBERRCNT/0/                              ! SCB error counter
C
     -, OLDSNPNUM                                 ! old value of snp #
     -, TEMPTRQ                                   ! temp trqopt
     -, ROUNDTIME
CIBM
     -, INIT_STAT
CIBMEND
C
     -, FIRSTIMER/0/                              ! Wait timer at start
     -, COPY_STAT                                 ! CAE_IO
C
C
C --- --------------------------------------------
      INTEGER*2
C --- ---------
C
     -  P1_STOP                                   ! RAP buffer 1 ending word
     -, P2_STOP                                   ! RAP buffer 2 ending word
     -, P3_STOP                                   !
     -, TRSNPNO                                   ! old SNP # for STP
     -, TRMARKNU                                  ! panel SNP #
     -, TRSETCMP                                  ! STP edit completion #
C
C
C
CVAX
CVAX  --------------------------------------------                        \C ---
CVAX  BYTE
CVAX  ---------                                                           \C ---
CVAXEND
C
CIBM
C --- --------------------------------------------
      INTEGER*1
C --- ---------
CIBMEND
C
     -  BLANKS                (34)                ! = CBLANKS
     -, ENTERCOD              (34)                ! = CENTERCODE
     -, ILSETUP               (34)                ! = CILSETUP
     -, ILUAT                 (34)                ! = CILUAT
     -, ILDEMO                (34)                ! = CILDEMO
     -, ILSNAPSH              (34)                ! = CILSNAPSHOT
     -, EXTIMTOG              (34)                ! = CEXTIMTOG
     -, NONEXIST              (34)                ! = CNONEXISTENT
     -, RP1_END                                   ! RAP buffer 1 end marker
     -, RP2_END                                   ! RAP buffer 2 end marker
     -, TRMESSAG              (34)                ! = TRMASAGE
CJF  -, TRSCRTCD              (4)                 !
C
C
C
C --- --------------------------------------------
      LOGICAL*1
C --- ---------
C
     -  DIRPREV                                   ! prev directory flag
     -, OLDSTORE                                  ! prev value of TRSTOREC
     -, PASS1                                     ! first pass flag
     -, PROCACTP                                  ! prev value of TRPROCAC
     -, SCALINF     (10)                          ! AOPs scalinf
     -, SCALOUTF    (10)                          ! AOPs scaloutf
     -, SNAPAV                                    ! at least one SNP exist
     -, TCF0TOT                                   ! TCF0TOT not decl in CDB
     -, TR800COM                                  ! just fin storinf 800 ms
     -, TR800MS                                   ! SNP store now possible
     -, TRAPACTV                                  ! rap active
     -, TRAPSTOP                                  ! prev value of TRRAPSTO
     -, TRAPTIMS                                  ! instruct input time to go
     -, TRBLKSEC                                  ! 2nd blk to be init in RAP
     -, TRCFCLEA                                  ! set CF test forces to 0
     -, TREDTPRO                                  ! protect request/status
     -, TRELREC                                   ! Snap recall from EL panel
     -, TRERASE                                   ! erase snapshot flag
     -, TRFREZDI                                  ! disable freeze
     -, TRINITIA                                  ! rap init in progress
     -, TRINTFIR                                  ! first pass in rap init
     -, TRIOERR                                   ! rap store i/o error
     -, TRIOJMPF                                  ! jump store compl flag
     -, TRSNPUNF                                  ! un freezes sim after rec
     -, TRMARK                                    ! pane SNP store request
     -, TRMARKPR                                  ! previous value of trmark
     -, TRMONOFR                                  ! inhibit motion freeze
     -, TRMONUT                                   ! fade motion to neutral
     -, TROLDPRO                                  ! previous value
     -, TROLDSET                                  ! old STP protect status
     -, TROTDTAV                                  ! throttle data available
     -, TRPLBKOL                                  ! old value of trplybk
     -, TRPROCCO    (NOPRC,0:NOPST)               ! process completion flags
     -, TRRAPREP                                  ! prev value of TRRAPREC
     -, TRDEMREP                                  ! prev value of TRDEMPBK
     -, TRRAPRES                                  ! PBK restoration in progrs
     -, TRRCRDOL                                  ! old value of trrecord
     -, TRRESINI                                  ! PBK restore initialize
     -, TRSETCHK                                  ! check STP request
     -, TRSETRCD                                  ! record temporary STP
     -, TRSETSTA                                  ! chosen STP occupied
     -, TRSETSTR                                  ! save/transfer temp STP
     -, TRSTRCIN                                  ! temp STP store in progr
CJF  -, TRTRANSS                                  ! indicates x-fer protect
     -, TRWAITM                                   ! suspend master flag
     -, TRWRAPRE                                  ! inst rap around request
     -, YIFRZIN                                   ! freeze panel inhibit flag
     -, LBLANCK                                   ! L BLANCK
     -, TR16TOG                                   ! 1.6 sec toggle
     -, CFRESET                                   ! C/F RESET BACKDRIVE
C
     -, TRNEWEV                                   ! NEW EVENT MARKER
CCDM     -, PTPSNPREC                                 ! Recall from the PTP
CJF  -, TREDTINP                                  ! edit in progress
CJF  -, TRPBKSTO                                  ! PBK stop flag
CJF  -, TRSETPRO                                  ! STP prot request/status
CJF  -, TRWRAPRN                                  ! rap around flag t=occured
C
C
C
C --- --------------------------------------------
      CHARACTER
C --- ---------
C
     -  CBLANKS                         * 34      ! blanks
     -, CENTERCODE                      * 34      ! enter code message
     -, CILSETUP                        * 34      ! illegal setup message
     -, CILUAT                          * 34      ! illegal uat   message
     -, CILDEMO                         * 34      ! illegal demo  message
     -, CILSNAPSHOT                     * 34      ! illegal snapshot message
     -, CEXTIMTOG                       * 34      ! excessive start time mess
     -, CNONEXISTENT                    * 34      ! non existent snapshot
     -, TRBLANK                         *  4      ! wipe out secret code disp
     -, TRSECRET                        *  4      ! secret prot toggle code
     -, CBLANCK                         *  1      ! C BLANCK
C
CJF  -, TRSCRTCDE                       *  4      ! prot toggle secret code
C
C
C
C --- =======================================================================
      EQUIVALENCE
C --- =======================================================================
C
     -  ( ILSNAPSH  , CILSNAPSHOT  )
     -, ( EXTIMTOG    , CEXTIMTOG    )
     -, ( ILSETUP     , CILSETUP     )
     -, ( ILUAT       , CILUAT       )
     -, ( ILDEMO      , CILDEMO      )
     -, ( NONEXIST  , CNONEXISTENT )
     -, ( ENTERCOD   , CENTERCODE   )
     -, ( BLANKS      , CBLANKS      )
     -, ( TRMESSAG   , TRSETMSG      )
     -, ( CBLANCK     , LBLANCK      )
     -, ( TRELREC    , TRSPCCOM(1)   )
C
     -, ( P1_STOP     , TRBUFR(1,1)  )
     -, ( P2_STOP     , TRBUFR(1,2)  )
     -, ( P3_STOP     , TRBUFR(1,3)  )
C
     -, ( TRTIMETO  , TRTIMTOG    )
     -, ( MFADE       , TRMONUT      )
C
CJF  -, ( IOBASE      , BASE         )
CJF  -, ( MPLBK       , TRMONOFR    )
CJF  -, ( TRSNAPAV    , TRCLBT(29)   )
CJF  -, ( TRAPACTV    , TRSPL(1)     )
CJF  -, ( TROTDTAV    , TRSPL(2)     )
C
CJF  -, ( TRCLIN(15)  , TRBOFFSRW    )
CJF  -, ( TRCLIN(30)  , INTRVP       )
CJF  -, ( TRCLIN(32)  , INTAV        )
CJF  -, ( TRCLIN(34)  , INTPEAK      )
CJF  -, ( TRCLIN(36)  , INTSEC       )
CJF  -, ( TRCLIN(37)  , TRSNPNO      )
CJF  -, ( TRCLBT(1)   , TRIOJMPFLG   )
CJF  -, ( TRCLBT(7)   , YIFRZIN      )
CJF  -, ( TRCLBT(10)  , TRIOERR      )
CJF  -, ( TRCLBT(13)  , TRFREZDIS    )
CJF  -, ( TRCLBT(15)  , TRERASE      )
CJF  -, ( TRCLBT(50)  , TRMARK       )
CJF  -, ( TRCLIN(49)  , TRMARKNUM    )
CJF  -, ( TRCLBT(16)  , TRSETRCD     )
CJF  -, ( TRCLBT(17)  , TRSETPROT    )
CJF  -, ( TRCLBT(18)  , TRSETSTR     )
CJF  -, ( TRCLIN(20)  , TRSETCMPNO   )
CJF  -, ( TRCLIN(37)  , TRSNPNO      )
CJF  -, ( TRCLBT(23)  , TREDTPRO     )
CJF  -, ( TRCLRL(10)  , TRINSTTI   )
CJF  -, ( TRCLBT(45)  , TRSCRTCD(1)  )
CJF  -, ( TRSCRTCD(1) , TRSCRTCDE    )
CJF  -, ( TRTEMBUF    , TRBUFR       )
C
C
C
C --- =======================================================================
      DATA
C --- =======================================================================
C
C                  1  2  3  1  2  3  1  2  3  1  2  3  1  2  3
C
     - TRPROGCN
C
     -/ 1, 0, 0, 2, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, ! SNP STORE
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! MODE = 01
C
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! SPARE
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! MODE = 02
C
     -  5,16, 0, 6, 0, 0, 7, 0, 0, 9, 0, 0, 8, 0, 0, ! SNP RECALL
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! MODE = 03
C
     - 10, 1, 0,11, 2, 0,12, 3, 0, 0, 0, 0, 0, 0, 0, ! RAP RECORD
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! MODE = 04
C
     -  1,16, 0, 2,17, 0, 3,16, 0, 5, 0, 0, 6, 0, 0, ! RAP PLAYBAC
     -  7, 0, 0, 9, 0, 0, 8, 0, 0,14, 0, 0,15, 0, 0, ! -----------
     -  5, 0, 0, 6, 0, 0, 7, 0, 0, 9, 0, 0, 8, 0, 0, ! MODE = 05
C
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! SPARE
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! MODE = 06
C
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! SPARE
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! MODE = 07
C
     - 30, 0, 0,31, 0, 0,32, 0, 0,33, 0, 0,38, 0, 0, ! INITIALIZE
     - 36, 0, 0,37, 0, 0,39, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! MODE = 08
C
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! SPARE
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! MODE = 09
C
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! SPARE
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! MODE = 10
C
     - 41, 0, 0,42, 0, 0,43, 0, 0,44, 0, 0,45, 0, 0, ! EDIT
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ! -----------
     -  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0/ ! MODE = 11
C
C ---
C
     -, CILSNAPSHOT  / 'INVALID SNAPSHOT NUMBER           ' /
     -, CEXTIMTOG    / 'EXCESSIVE START TIME VALUE        ' /
     -, CILSETUP     / 'INVALID SETUP NUMBER              ' /
     -, CILUAT       / 'INVALID UAT NUMBER                ' /
     -, CILDEMO      / 'INVALID DEMO NUMBER               ' /
     -, CNONEXISTENT / 'NON-EXISTENT SNAPSHOT             ' /
     -, CENTERCODE   / 'ENTER PROTECTION CODE             ' /
     -, CBLANKS      / '                                  ' /
     -, MAXKOUNT     / 30                                   /
     -, TRSETCHK     / .TRUE.                               /
     -, SECRETCO     / 8                                    /
     -, PASS1        / .TRUE.                               /
     -, TRSECRET     / '1234'                               /
     -, TRBLANK      / '    '                               /
     -, CBLANCK      / ' '                                  /
C
C ---
C
     -, SCALSYS      / '04'X     ! 1  - flight instruments
     -,                '08'X     ! 2  - control forces
     -,                '0C'X     ! 3  - auto-pilot
     -,                '14'X     ! 4  - radio aids
     -,                '1C'X     ! 5  - audio & sound
     -,                '20'X     ! 6  - environnement control system
     -,                '24'X     ! 7  - engines
     -,                '28'X     ! 8  - ancillaries
     -,                '34'X     ! 9  - spares
     -,                '38'X /   ! 10 - avionics
C
C --- SET TO TRUE THOSE WHICH ARE TO BE SCALE IN
C
CCDM     -, SCALINF      / .TRUE.    ! 1  - flight instruments
CCDM     -,                .FALSE.   ! 2  - control forces
CCDM     -,                .FALSE.   ! 3  - auto-pilot
CCDM     -,                .TRUE.    ! 4  - radio aids
CCDM     -,                .FALSE.   ! 5  - audio & sound
CCDM     -,                .TRUE.    ! 6  - environnement control system
CCDM     -,                .FALSE.   ! 7  - engines
CCDM     -,                .TRUE.    ! 8  - ancillaries
CCDM     -,                .FALSE.   ! 9  - spares
CCDM     -,                .TRUE.  / ! 10 - avionics
C
     -, SCALINF      / .TRUE.    ! 1  - flight instruments
     -,                .TRUE.   ! 2  - control forces
     -,                .FALSE.   ! 3  - auto-pilot
     -,                .TRUE.    ! 4  - radio aids
     -,                .TRUE.   ! 5  - audio & sound
     -,                .TRUE.    ! 6  - environnement control system
     -,                .TRUE.   ! 7  - engines
     -,                .TRUE.    ! 8  - ancillaries
     -,                .TRUE.   ! 9  - spares
     -,                .TRUE.  / ! 10 - avionics
C
C --- SET TO TRUE THOSE WHICH ARE TO BE SCALE OUT
C
     -, SCALOUTF     / .TRUE.    ! 1  - flight instruments
     -,                .TRUE.   ! 2  - control forces
     -,                .FALSE.   ! 3  - auto-pilot
     -,                .TRUE.    ! 4  - radio aids
     -,                .TRUE.   ! 5  - audio & sound
     -,                .TRUE.    ! 6  - environnement control system
     -,                .TRUE.   ! 7  - engines
     -,                .TRUE.    ! 8  - ancillaries
     -,                .TRUE.   ! 9  - spares
     -,                .TRUE.  / ! 10 - avionics
C
     -, DEADTHR      /   1.0                                /
     -, TRTHRFWD     / -10.0                                /
     -, TRTHRBWD     /  10.0                                /
     -, TRTHRRST     /   0.0                                /
C
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- =======================================================================
C
C
      ENTRY TRSTR
C           *****
C
C
C --- -----------------------------------------------------------------------
C --- Check for any errors.
C --- ---------------------
C
      IF ( TRERROR .NE. 0 ) THEN
C
        TREXEC = .TRUE.
C
      ENDIF
C
      IF ( TREXEC ) RETURN
C
C
C
C --- -----------------------------------------------------------------------
C --- Wait 60 secondes before starting the execution.
C --- ------------------------------------------
C
      IF ( FIRSTIMER .LT. WAITIME ) THEN
         FIRSTIMER  = FIRSTIMER + 1
         RETURN
      ENDIF
C
C
C
C --- -----------------------------------------------------------------------
C --- First pass logic.  Initialize and open files
C --- --------------------------------------------
C
      IF ( PASS1 ) THEN
C
CIBM
      IOSTATE  = 0
      I = LOC(YXSTRTXRF)
ccdm      I = ADDR(YXSTRTXRF)
      I = LOC(TRSNPSTO)
ccdm      I = ADDR(TRSNPSTO)
C !FM+
C !FM   3-Apr-92 16:26:12 Walter Pin
C !FM    < i/o server initialized in sp0c0init.for >
C !FM
CWP      CALL CAE_INIT_IO_SERVER( INIT_STAT )
C !FM-
CIBMEND
C
C ---    initialize necessary CDB labels
C
         DO I = 1 , MAXSEC
C
            TRIOFLAG(I) = .false.
C
         ENDDO
C
         TREFINISH  = .TRUE.
         TRERROR    = 0
C
         PASS1COMP = .FALSE.
         TRMODE    = INITIALIZE
         TRPROCPT  = PROCBEG
         TRCONTRO = 1
         TRSTOREC  = .false.
         TRPWRSTO  = .false.
C
C --- Initialize recording var
C
         TRWRAPRQ = .TRUE.
         TRWRAPRN = .FALSE.
C
         DO I = 1 , MAXSEC
            TRMAXSPS(I) = 0
         ENDDO
C
C ---    Initialize Snapshot Data
C
         OLDTRQOPT = QIO_IOQD                     ! Old Value of TRQOPT
         TRSNPNUM = 1                             ! Snapshot number
         TRSNPSTO = .false.                       ! Activate Snapshot store
         TRPWRCNT = 0                             ! PWR failure counter
C
CCDM         SCALADD   = YSYSADD
C
         TRRAPNUM  = 0
         RAPNUM    = 1
         TRTIMETO = 0.0
         TRTIMTOG = 0.0
         TRINSTTI = 0.0
         TRRAPSTO  = .FALSE.
         TRRAPREC  = .FALSE.
         TREVREPL  = .FALSE.
         TREVTIM   = 0.0
         TREVMARK  = .FALSE.
         TREVRPAV  = .FALSE.
         TRNEWEV   = .FALSE.
C
CCDM         TRSSSREAV = .FALSE.
CCDM         TRSNAPAV  = .FALSE.
CCDM         TRSSSTAV  = .TRUE.
C
         TRRAPST   = OPEN
         TRRAPTM   = 0
         TRAPSTAR = 1
         TRAPEND   = 0
         TRRAPSEN  = .FALSE.
C
C --- Initialize store/recall var
C
         DO I = 1 , MAXSNP
            TRSNPST(I)  = OPEN
CJF         TRSNPHDG(I) = 0
CJF         TRSNPALT(I) = 0
CJF         TRSNPIAS(I) = 0
         ENDDO
C
         TRSNPNUM     = 1
         TRSNPSTO     = .FALSE.                   ! snapshot take from page
         TRSNPREC     = .FALSE.                   ! snapshot recall from page
         TRSNPDEL     = .FALSE.
         TRRECALL     = .FALSE.
         TRTRANSS   = .FALSE.
C
         DIRECTORY    = .FALSE.
         TRMONUTN     = .FALSE.
C
         TRPROCNU     = 1
         TRPROCAC     = .TRUE.
C
C
C ---    RAP STORAGE SPACE
C
CJF      TRRAPSIZ     =   (   2 * BUFBLKSZ        ! Size of a 10  min RAP
CJF  &                      +     TRSCBSEC(1)
CJF  &                      +     TRSCBSEC(2) )
CJF  &                  * ( 600 / 1.6         )
C
CJF      TR16SIZ      =   (   2 * BUFBLKSZ        ! Size of a 1.6 sec RAP
CJF  &                      +     TRSCBSEC(1)
CJF  &                      +     TRSCBSEC(2) )
C
CJF      TRCONV       = TR16SIZ / 1.6             ! Number of blocks per sec
C
         ASSIGN 900   TO PROGCNTL                 ! Label - Program control s
         ASSIGN 905   TO NEXT                     ! Label - Next process
         ASSIGN 99000 TO END                      ! Label - End of module
         ASSIGN 999   TO PROGEXE                  ! Label - Execute call sect
C
         PASS1        = .FALSE.
         TRINDEX      = NEWREQINDX
         PASS1TIM   = 1200
C
         TRQOPT       = QIO_INIT
C
         GOTO PROGCNTL                            ! Label
C
      ENDIF
C
C
C --- -----------------------------------------------------------------------
C --- Added temp for the cae_io returning argument.
C --- ----------------------------------
C
CIBM
C !FM+
C !FM   3-Apr-92 16:26:59 Walter Pin
C !FM    < Call to local_copy now done by dispatcher. >
C !FM
CWP            CALL CAE_AIO_LOCAL_COPY( COPY_STAT )
C !FM-
CIBMEND
C
C
C
C
C --- -----------------------------------------------------------------------
C --- Wait until first pass is completed
C --- ----------------------------------
C
      IF ( .NOT. PASS1COMP ) THEN
C
         IF ( PASS1TIM .LE. 0 ) THEN
C
            TREXEC     = .TRUE.
C
         ELSE
C
            PASS1TIM = PASS1TIM - 1
            GOTO PROGCNTL                    ! Label
C
         ENDIF
C
      ENDIF
C
C
C
C --- -----------------------------------------------------------------------
C --- Check for a freeze request during playback
C --- ------------------------------------------
C
      IF (      ( TRCONTRO .EQ. 11 )
     -     .OR. ( TRCONTRO .EQ. 12 ) ) TRAPFRZ = TCFTOT
C
C
C
C --- -----------------------------------------------------------------------
C --- Motion freeze release timer
C --- ---------------------------
C
      IF ( TRMOTIMR .GT. 0 ) THEN
C
         TRMOTIMR = TRMOTIMR - 1
C
      ENDIF
C
C
C
C --- -----------------------------------------------------------------------
C --- Compute RAP Active label ( TRAPACTV ) for Throttle Backdrive
C --- ------------------------------------------------------------
C
      TRAPACTV = (      TRRAPREC     ! RAP playback    active
     -             .OR. TRSNPREC     ! SNAPSHOT recall active
     -             .OR. TRSETREC )   ! SETUP    recall active
C
C
C
C
C --- -----------------------------------------------------------------------
C --- IF IN RECALL MODE OR IN PLAYBACK MODE:  BACKDRIVE THE THROTTLE.
C --- -------------------------
C
      IF ( TRTHRBCK ) THEN
C
Crg+ 02-apr-92 "temporarily removed for initial compilation"
Crg        SAENG(1) = .TRUE.
Crg        IF ( ETLA(1) .GT. IAETLA(1) + DEADTHR ) THEN
Crg          SATHCONT(1) = TRTHRFWD
Crg        ELSEIF ( ETLA(1) .LT. IAETLA(1) - DEADTHR ) THEN
Crg          SATHCONT(1) = TRTHRBWD
Crg        ELSE
Crg          SATHCONT(1) = TRTHRRST
Crg          SAENG(1) = .FALSE.
Crg        ENDIF
Crg-
      ENDIF
C
C
C
C --- -----------------------------------------------------------------------
C --- This module is only executed every 100 MS
C --- Execpt when RAP Record or Playback are active
C --- ---------------------------------------------
C
      IF (      ( TRCONTRO .EQ.  5 )
     -     .OR. ( TRCONTRO .EQ. 12 ) ) THEN
C
        GOTO 89
C
      ELSE
C
        TOG = TOG + 1
C
        IF ( TOG .GT. 3 ) THEN
          TOG = 1
        ELSE
       RETURN
        ENDIF
C
      ENDIF
C
      TRINSTTI = TRTIMETO
C
C
      IF (CFRESET) THEN
        CFRESET = .FALSE.
        HCEMODE     = 0          ! ELEVATOR     BACKDRIVE MODE
        HCRMODE     = 0          ! RUDDER       BACKDRIVE MODE
        HCAMODE     = 0          ! AILERON      BACKDRIVE MODE
        HCNMODE     = 0          ! NOSEWHEEL    BACKDRIVE MODE
        HCHMODE     = 0          ! STABILIZER   BACKDRIVE MODE
        HCETMODE    = 0          ! PITCH TRIM   BACKDRIVE MODE
      ENDIF
C
C
C
C --- -----------------------------------------------------------------------
C --- IF IN RECALL MODE OR IN PLAYBACK MODE:  UPDATE THE WATCH DOG TIMER FOR
C --- THE BACKDRIVE THE THROTTLE.
C --- -------------------------
C
      IF ( TRTHRTIMR .GT. 0 ) THEN
        TRTHRTIMR  = TRTHRTIMR - 1
      ENDIF
C
C
C
C --- -----------------------------------------------------------------------
C --- DELETE ALL SNAPSHOT LOGIC
C --- -------------------------
C
      IF ( TRSNPDEL ) THEN
C
         DO  I = 1 , MAXSNP
C
            TRSNPST(I)  = OPEN
C
            TRSNPHDG(I) = 0.00
            TRSNPALT(I) = 0.00
            TRSNPIAS(I) = 0.00
C
         ENDDO
C
C ---
C
         TRSNPAV    = .FALSE.
         LASTSNP    = 0
         LASTSET    = 0
CCDM         TRSNAPAV   = .FALSE.
         SNAPAV     = .FALSE.
         TRSNPLST   = 0
         TRSNPDEL   = .FALSE.
CCDM         TRSSSREAV  = .FALSE.
CCDM         TRSNAPAV   = .FALSE.
CCDM         TRSSSTAV   = .TRUE.
         TRSNPNUM   = 1
C
      ENDIF
C
C
C
C --- -----------------------------------------------------------------------
C --- CHECK IF A RECALL IS FINISHED
C --- -----------------------------
C
      IF (       (        TRRECALL      )
     -     .AND. (    .NOT. TRSLEW      )
     -     .AND. ( TRMOTIMR .LE. 0      )
Crg+ 02-apr-92 "temporarily removed for initial compilation"
Crg     -     .AND. ( ( .NOT.SAENG(1)    )
Crg     -     .OR.    ( TRTHRTIMR .LE. 0 ) )
Crg-
     -     .AND. ( TRPROCNU .GE. 2 ) ) THEN
C
        IF (      (   .NOT. TRPROCAC )
     -       .OR. ( TRCONTRO .EQ. 7 ) ) THEN
C
CCDM          IF ( PTPSNPREC ) THEN             ! Recall from the PTP ?
CCDM            PTPSNPREC  = .FALSE.
CCDM            TRSNPNUM   = OLDSNPNUM
CCDM            QMR_TRSNPNUM=TRSNPNUM!***FPC-QMR***
CCDM          ENDIF
C
          TRSNPREC     = .FALSE.            ! SNP recall
          TRELREC      = .FALSE.            ! SNP recall from EL panel
CCDM          TRUATREC     = .FALSE.            ! UAT recall
          IF ( LASTSNP .LT. MAXSNP ) THEN
            TRSNPNUM     = LASTSNP+1          !  NEXT EMPTY SNP
          ELSE
            TRSNPNUM     = 1                  !  FIRST SNP
          ENDIF
C
          TRSETREC     = .FALSE.            ! STP recall
          TRTHRBCK     = .FALSE.            ! RESET THROTTLE BACKDRIVE
C
          TRPROCNU     = 1
          TRRECALL     = .FALSE.
          TRREADY      = .TRUE.
CCDM          TRSNPRCL     = 0
          TRSETEDT     = .FALSE.
          TRSTREDT     = .FALSE.
          TRRAPREC     = .FALSE.
          TREVREPL     = .FALSE.
          TRCFRLSE     = .TRUE.
          TRNOTRIM     = .FALSE.
          TRSTOREC     = .FALSE.
CCDM1091          TCMSOUND     = .FALSE.
          TCMSOUND     = O_TCMSOUND            ! PREVIOUS TCMSOUND
          TRRAPON      = .FALSE.
          TCFTOT       = .TRUE.
          TRMONOFR    = .FALSE.
          TRTRANSS   = .FALSE.
          TRSPCCOM(10) = .TRUE.             ! FOR RT.FOR MODULE
          TRSPCCOM(8)  = .TRUE.             ! FOR VIFS.FOR MODULE
CCDM          TRSSSREAV    = .TRUE.
CCDM          TRSNAPAV     = .TRUE.
          TROTDTAV     = .FALSE.
C
CJF       TRSNPREC     = .TRUE.             ! for CNIA program
CJF       TRCLBT(31)   = .FALSE.            ! Signal control forces to fade
CJF       TRCLBT(33)   = .TRUE.
CJF       TRSETNUM     = 0
CJF       TRSNPNUM     = 0
CJF       XZTRIM       = OLDXZTRIM
C
C --- Reset backdrive mode during CDB recall
C
          CFRESET = .TRUE.
CCDM2705        HCEMODE     = 0          ! ELEVATOR     BACKDRIVE MODE
CCDM2705        HCRMODE     = 0          ! RUDDER       BACKDRIVE MODE
CCDM2705        HCAMODE     = 0          ! AILERON      BACKDRIVE MODE
CCDM2705        HCNMODE     = 0          ! NOSEWHEEL    BACKDRIVE MODE
CCDM2705        HCHMODE     = 0          ! STABILIZER   BACKDRIVE MODE
        HBDON       = .FALSE.    !              BACKDRIVE ON
        MPLBK       = .false.    ! Motion Playback Request
C
C
CJF          HCATMODE    = 0          ! AILERON TRIM BACKDRIVE MODE
CJF          HCRTMODE    = 0          ! RUDDER TRIM  BACKDRIVE MODE
CJF          HMODE       = 0          ! ANCILLARIES  BACKDRIVE MODE
CCDM             HEMODE(1)   = 0          ! Mode of engines program
CJF          HEMODE(2)   = 0          ! Mode of engines program
CJF          HEMODE(3)   = 0          ! Mode of engines program
CJF          HEMODE(4)   = 0          ! Mode of engines program
          HATGON      = .FALSE.    ! ATG RUNNING FLAG
CJF       TRCLBT(31)  = .FALSE.    ! Signal control forces to fade controls
C
C ---
C
        ELSE IF ( TRINDEX .GT. 9 ) THEN         ! SNP RECALL FOR RAP
C
          TRREADY      = .TRUE.
          TRSTOREC     = .FALSE.
          TRRECALL     = .FALSE.
CCDM1091          TCMSOUND     = .FALSE.
C
        ENDIF
C
      ENDIF
C
C
C
C --- =======================================================================
C --- CHECK FOR A FREEZE RELEASE AFTER RECALL FINISHED
C --- ================================================
C
      IF (       (        TRREADY )
     -     .AND. ( .NOT.    YIFREZ  )
     -     .AND. ( .NOT. TRPROCAC ) ) THEN
C
        TRCFTMR = 20
        TRREADY = .FALSE.
C
      ENDIF
C
C --- SLOWLY RELEASE CONTROL FORCES AFTER RECALL UNFREEZE
C
      IF (       ( TRCFTMR .GE. 1 )
     -     .AND. (       TRCFRLSE ) ) THEN         ! DECREMENT C
C
        TRCFTMR = TRCFTMR - 1
C
        IF ( TRCFTMR .LE. 0 ) THEN
C
          TRCFRLSE  = .FALSE.
          TRMONOFR = .FALSE.
C
        ENDIF
C
      ENDIF
C
C --- MOTION FADE, & TIMER
C
      IF ( TRMONUTN ) THEN
C
        TRMONUT = .TRUE.
C
CLSAK+ : Release MFADE after CDB recalled and wait timer elapsed
C
      ELSEIF (TRMOTIMR.LE.0) THEN
          TRMONUT      = .FALSE.
CLSAK-
C
      ENDIF
C
C
C
C --- -------------------------------
C --- Turn off total freeze light
C --- while in recall mode
C
      IF ( TRRECALL ) THEN
C
        TRRAPON = .TRUE.
        TCFTOT  = .FALSE.
C
      ENDIF
C
C
C
C --- =======================================================================
C --- STORE RECALL CONTROL
C --- =======================================================================
C
      IF ( .NOT. (      TRPROCAC
     -             .OR. PROCACTP
     -             .OR. TRSTOREC    ) ) THEN  ! Nothing act
C
C
C
C ---    ====================================================================
C ---    CHECK FOR A SNAPSHOT STORE
C ---    ====================================================================
C
         IF ( TRSNPSTO ) THEN                     ! Activate Snapshot store
C
C ---
C
            IF (      ( TRSNPNUM .GT. MAXSNP )
     -           .OR. ( TRSNPNUM .LE. 0      ) ) THEN
C
               TRINDEX           = NOTACTINDX
               TRSNPSTO          = .FALSE.
C
            ELSE
C
               LASTSNP           = TRSNPNUM
               TRSNPAV           = .TRUE.
               TRINDEX           = NEWREQINDX
               TRMODE            = SNPMODESTO
               TRPROCAC          = .TRUE.
               TRPROCPT          = PROCBEG
               TRSTOREC          = .TRUE.
               TRSNPST(TRSNPNUM) = ACTIVE
               YIFRZIN           = .TRUE.
               TRSOFFW           = SNPSIZE * ( TRSNPNUM - 1 )
C
               GOTO PROGCNTL                      ! LABEL 900
C
            ENDIF
C
         ENDIF
C
C
C
C ---    ====================================================================
C ---    CHECK FOR A POWER FAILURE SNAPSHOT STORE
C ---    ====================================================================
C
         IF ( .FALSE. ) THEN   !TRPWRCNT .EQ. 9000! Every 5 minutes
C
C ---
C
            IF ( .NOT. TRSTOREC ) THEN            ! STO/REC inactive
C
               TRINDEX  = NEWREQINDX
               TRMODE   = PWRMODESTO
               TRPROCAC = .TRUE.
               TRPROCPT = PROCBEG
               TRSTOREC = .TRUE.
               YIFRZIN  = .TRUE.
               TRBOFFW  = 0
               TRPWRCNT = 0                       ! PWR failure counter
               TRPWRSTO = .true.                  ! PWR failure flag
C
               GOTO PROGCNTL                      ! LABEL 900
C
            ENDIF
C
C ---
C
         ELSE
C
CCDM        TRPWRCNT = TRPWRCNT + 1               ! PWR failure counter
C
         ENDIF
C
C
C
C ---    ====================================================================
C ---    CHECK FOR A SNAPSHOT RECALL
C ---    ====================================================================
C
C ---    Check if snapshot recall request comes from an EL page. If so,
C ---    recall the last snapshot taken (if any)
C
         IF ( TRELREC ) THEN
           IF ( TRSNPAV ) THEN
             TRSNPNUM = LASTSNP
             TRSNPREC = .TRUE.
           ELSE
             TRELREC = .FALSE.
           ENDIF
         ENDIF
C
         IF ( TRSNPREC ) THEN
C
C ---
C
          IF ( TRSNPNUM .EQ. 0 ) THEN
            IF ( LASTSNP .NE. 0 ) THEN
             TRSNPNUM = LASTSNP
            ELSE
             TRSNPNUM = 1
            ENDIF
          ENDIF
C
C ---
C
CCDM          IF (      (TRSNPST(TRSNPNUM) .LT. UNPR_OCC)
CCDM     -        .AND. (TRSNPST(LASTSNP)  .GE. UNPR_OCC) ) THEN
C
CCDM            OLDSNPNUM = TRSNPNUM                               ! Recall from
CCDM            TRSNPNUM  = LASTSNP                                ! the PTP
CCDM            QMR_TRSNPNUM=TRSNPNUM!***FPC-QMR***
CCDM            PTPSNPREC = .TRUE.
C
CCDM          ENDIF
C
C ---
C
          IF (      ( TRSNPNUM .GT. MAXSNP )                    !------------
     -         .OR. ( TRSNPNUM .LE. 0      ) ) THEN             ! Make sure t
C                                                               ! rng is vali
            TRINDEX   = NOTACTINDX
            TRSNPREC  = .FALSE.
            TRSNPNUM  = 1
C
          ELSE IF ( TRSNPST(TRSNPNUM) .GE. UNPR_OCC ) THEN
C
            TRINDEX   = NEWREQINDX
            TRFILNO   = SNP
            TRMODE    = SNPMODEREC
            TRPROCAC  = .TRUE.                                  ! Process act
            TRPROCPT  = PROCBEG
            TRSTOREC  = .TRUE.
            TRRECALL  = .TRUE.
            TRFRZST   = .TRUE.
            TRREADY   = .FALSE.
            TRMONOFR = .TRUE.
            TRBOFFR   = ( TRSNPNUM - 1 ) * SNPSIZE
            TRNOXREF  = .FALSE.
            TRTHRTIMR = 310                   ! THROTTLE WATCH DOG (30sec)
CCDM            TRSSSREAV = .FALSE.
CCDM            TRSNAPAV  = .FALSE.
CJF         JMPNUM    = TRSNPNUM
CJF         TRDCB     = TRFILDCBS(SNP)
C
            GOTO PROGCNTL                       ! LABEL 900
C
          ELSE
C
            TRINDEX  = NOTACTINDX
            TRSNPREC = .FALSE.
C
          ENDIF
C
        ENDIF
C
C
C
         IF ( TRSNPST(TRSNPNUM) .GE. UNPR_OCC ) THEN
CCDM           TCM0SNRA = .TRUE.
CCDM           TCM0SNTK = .FALSE.
         ELSE
CCDM           TCM0SNTK = .TRUE.
CCDM           TCM0SNRA = .FALSE.
         ENDIF
C
CJF      IF ( TRSNPST(1) .GE. UNPR_OCC ) THEN
CJF        TR0SNPRCL(1) = .TRUE.
CJF      ELSE
CJF        TR0SNPRCL(1) = .FALSE.
CJF      ENDIF
C
C
C
C ---    ====================================================================
C ---    CHECK FOR A SETUP RECALL
C ---    ====================================================================
C
         IF ( TRSETREC ) THEN
C
C ---
C
            IF (      ( TRSETNUM .LT. 1      )
     -           .OR. ( TRSETNUM .GT. MAXSET ) ) THEN
C
               TRINDEX   = NOTACTINDX
               TRSETREC  = .FALSE.
               TRSETNUM  = 0
C
            ELSEIF ( TRSETST(TRSETNUM) .GE. UNPR_OCC ) THEN
C
               TRINDEX   = NEWREQINDX
               TRFILNO   = STP
               TRMODE    = SNPMODEREC
               TRPROCAC  = .TRUE.                                  ! Process act
               TRPROCPT  = PROCBEG
               TRSTOREC  = .TRUE.
               TRRECALL  = .TRUE.
               TRFRZST   = .TRUE.
               TRREADY   = .FALSE.
               TRMONOFR = .TRUE.
               TRBOFFR   = ( ( TRSETNUM - 1 ) * SNPSIZE )
               TRNOXREF  = .FALSE.
CJF            TRDCB     = TRFILDCBS(STP)
C
               GOTO PROGCNTL                       ! LABEL 900
C
            ELSE
C
               TRINDEX  = NOTACTINDX
               TRSETREC = .FALSE.
C
            ENDIF
C
         ENDIF
C
C
C
C ---    ====================================================================
C ---    CHECK FOR A UNUSUAL ATTITUDE RECALL
C ---    ====================================================================
C
         IF ( .FALSE. ) THEN        !TRUATREC
C
C ---
C
            IF (      ( TRUATNUM .LT. 1      )
     -           .OR. ( TRUATNUM .GT. MAXUAT ) ) THEN
C
               TRINDEX   = NOTACTINDX
CCDM               TRUATREC  = .FALSE.
               TRUATNUM  = 0
C
            ELSEIF ( TRUATST(TRUATNUM) .GE. UNPR_OCC ) THEN
C
               TRINDEX   = NEWREQINDX
               TRFILNO   = UAT
               TRMODE    = SNPMODEREC
               TRPROCAC  = .TRUE.                                  ! Process act
               TRPROCPT  = PROCBEG
               TRSTOREC  = .TRUE.
               TRRECALL  = .TRUE.
               TRFRZST   = .TRUE.
               TRREADY   = .FALSE.
               TRMONOFR = .TRUE.
               TRBOFFR   = ( ( TRUATNUM - 1 ) * SNPSIZE )
C
               GOTO PROGCNTL                       ! LABEL 900
C
            ELSE
C
               TRINDEX  = NOTACTINDX
CCDM               TRUATREC = .FALSE.
C
            ENDIF
C
         ENDIF
C
C
C
C ---    ====================================================================
C ---    CHECK FOR A POWER FAILURE SNAPSHOT RECALL
C ---    ====================================================================
C
         IF (.FALSE.) THEN        !TCRPWRF
C
C ---
C
            IF ( TRPWRST .GE. UNPR_OCC ) THEN
C
               TRINDEX   = NEWREQINDX
               TRFILNO   = PWR
               TRMODE    = SNPMODEREC
               TRPROCAC  = .TRUE.                                  ! Process act
               TRPROCPT  = PROCBEG
               TRSTOREC  = .TRUE.
               TRRECALL  = .TRUE.
               TRFRZST   = .TRUE.
               TRREADY   = .FALSE.
               TRMONOFR = .TRUE.
               TRBOFFR   = 0
               TRNOXREF  = .FALSE.
C
               GOTO PROGCNTL                       ! LABEL 900
C
            ELSE
C
               TRINDEX  = NOTACTINDX
CCDM               TCRPWRF  = .FALSE.
C
            ENDIF
C
         ENDIF
C
C
C
C ---    ====================================================================
C ---    SETUP EDIT LOGIC
C ---    ====================================================================
C
         IF (       (       TRSTREDT   )
     -        .AND. ( .NOT. TRTRANSS ) ) THEN
C
C
C
C ---       --------------------------------------
C ---       CHECK SNAPSHOT NUMBER
C ---       ---------------------
C
            IF (      ( TRSNPNUM .LT. 1      )
     -           .OR. ( TRSNPNUM .GT. MAXSNP ) ) THEN
C
               DO I = 1 , 34
                   TRMESSAG(I) = ILSNAPSH(I)
               ENDDO
C
               TRSTREDT = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       CHECK SNAPSHOT STATUS
C ---       ---------------------
C
            ELSE IF ( TRSNPST(TRSNPNUM) .LT. UNPR_OCC ) THEN
C
               DO I = 1 , 34
                  TRMESSAG(I) = NONEXIST (I)
               ENDDO
C
               TRSTREDT = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       CHECK SETUP NUMBER
C ---       ------------------
C
            ELSEIF (      ( TRSETNUM .LT. 1      )
     -               .OR. ( TRSETNUM .GT. MAXSET ) ) THEN
C
               DO I = 1 , 34
                  TRMESSAG(I) = ILSETUP(I)
               ENDDO
C
               TRSTREDT = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       CHECK SECRET CODE
C ---       -----------------
C
            ELSEIF ( TRPROTCD .NE. SECRETCO ) THEN
C
               DO I = 1 , 34
                  TRMESSAG(I) = ENTERCOD(I)
               ENDDO
C
               TRSTREDT = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       INITIATE SETUP TRANSFER
C ---       -----------------------
C
            ELSE
C
               DO I = 1 , 34
                  TRMESSAG(I) = BLANKS(I)
               ENDDO
C
               LASTSET    = TRSETNUM
               SETNUM     = TRSETNUM
               TRTRANSS = .TRUE.
               TRINDEX    = NEWREQINDX
               TRMODE     = EDITMODE
               TRPROCAC   = .TRUE.                             ! Process act
               TRPROCPT   = PROCBEG
               TRSETPRO   = .TRUE.
               TRSETST(TRSETNUM) = ACTIVE
C
C ---          SAVE DESCRIPTION
C
               DO I = 1 , 14
                  TRSETDES(I   ,TRSETNUM) = TRSTDIN(I,1)
                  TRSETDES(I+14,TRSETNUM) = TRSTDIN(I,2)
                  TRSTDIN(I,1)            = LBLANCK
                  TRSTDIN(I,2)            = LBLANCK
               ENDDO
C
C ---
C
               TRSRFINU = SNP                     ! Source      file #
               TRDSFINU = STP                     ! Destination file #
C
               TRBOFFR     = ( TRSNPNUM - 1 ) * SNPSIZE
               TRBLKRPT(1) = TRBOFFR + 1
               TRBOFFW     = ( TRSETNUM - 1 ) * SNPSIZE
               TRBLKWPT(1) = TRBOFFW + 1
C
               TRBYXFER    = SNPSIZE * BLKSIZE    ! # of blocks to transfer
C
C ---
C
               GOTO PROGCNTL
C
            ENDIF
C
         ENDIF
C
C
C
C ---    ====================================================================
C ---    DEMONSTRATION EDIT LOGIC
C ---    ====================================================================
C
         IF (       (       .FALSE.   )       !TRDEMEDT
     -        .AND. ( .NOT. TRWRAPRN   )
     -        .AND. ( .NOT. TRTRANSS ) ) THEN
C
C
C
C ---       --------------------------------------
C ---       CHECK START TIME VALUE
C ---       ----------------------
C
            IF (      ( TRTIMETO .LT. 0       )
     -           .OR. ( TRTIMETO .GT. TRRAPTM ) ) THEN
C
               DO I = 1 , 34
                   TRMESSAG(I) = EXTIMTOG(I)
               ENDDO
C
CCDM               TRDEMEDT = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       CHECK DEMO NUMBER
C ---       -----------------
C
            ELSEIF (      ( TRDEMNUM .LT. 1      )
     -               .OR. ( TRDEMNUM .GT. MAXSET ) ) THEN
C
               DO I = 1 , 34
                  TRMESSAG(I) = ILDEMO(I)
               ENDDO
C
CCDM               TRDEMEDT = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       CHECK SECRET CODE
C ---       -----------------
C
            ELSEIF ( TRDEMCOD .NE. SECRETCO ) THEN
C
               DO I = 1 , 34
                  TRMESSAG(I) = ENTERCOD(I)
               ENDDO
C
CCDM               TRDEMEDT = .FALSE.
               TRDEMCOD = 0
C
C
C
C ---       --------------------------------------
C ---       INITIATE DEMO  TRANSFER
C ---       -----------------------
C
            ELSE
C
               DO I = 1 , 34
                  TRMESSAG(I) = BLANKS(I)
               ENDDO
C
               TRTRANSS = .TRUE.
               TRINDEX    = NEWREQINDX
               TRMODE     = EDITMODE
               TRPROCAC   = .TRUE.                             ! Process act
               TRPROCPT   = PROCBEG
               TRSETPRO   = .TRUE.
               TRDEMST(TRDEMNUM) = ACTIVE
               TRDEMTM(TRDEMNUM) = TRRAPTM
C
C ---          SAVE DESCRIPTION
C
               DO I = 1 , 15
C
                  J = ( TRDEMNUM - 1 ) * 30
C
CCDM                  TRDEMDES(I+J   ,1) = TRSTDIN(I,1)
CCDM                  TRDEMDES(I+J+15,1) = TRSTDIN(I,2)
C
                  TRSTDIN(I,1)       = LBLANCK
                  TRSTDIN(I,2)       = LBLANCK
C
               ENDDO
C
C ---
C
               TRSRFINU = RCD                     ! Source      file #
               TRDSFINU = DEM                     ! Destination file #
C
               TRBOFFR     = 0
               TRBLKRPT(1) = TRBOFFR + 1
C
               TRBOFFW     = TRRAPSIZ * ( TRDEMNUM - 1 )
               TRBLKWPT(1) = TRBOFFW + 1
C
CVAX
CVAX           TRBYXFER    =   TR16SIZ * BLKSIZE ! # of bytes to transfer
CVAX &                       * (   JINT(   ( TRRAPTM - 0.1 )
CVAX &                                   / ( 1.6           ) )
CVAX &                           + 1                           )
CVAXEND
C
CIBM
               TRBYXFER    =   TR16SIZ * BLKSIZE ! # of bytes to transfer
     &                       * (   INT(   ( TRRAPTM - 0.1 )
     &                                   / ( 1.6           ) )
     &                           + 1                           )
CIBMEND
C
C ---
C
               GOTO PROGCNTL
C
            ENDIF
C
         ENDIF
C
C
C
C ---    ====================================================================
C ---    UNUSUAL ATTITUDE EDIT LOGIC
C ---    ====================================================================
C
         IF (       (       .FALSE.   )       !TRUSTRED
     -        .AND. ( .NOT. TRTRANSS ) ) THEN
C
C
C
C ---       --------------------------------------
C ---       CHECK SNAPSHOT NUMBER
C ---       ---------------------
C
            IF (      ( TRSNPNUM .LT. 1      )
     -           .OR. ( TRSNPNUM .GT. MAXSNP ) ) THEN
C
               DO I = 1 , 34
                   TRMESSAG(I) = ILSNAPSH(I)
               ENDDO
C
CCDM               TRUSTRED = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       CHECK SNAPSHOT STATUS
C ---       ---------------------
C
            ELSE IF ( TRSNPST(TRSNPNUM) .LT. UNPR_OCC ) THEN
C
               DO I = 1 , 34
                  TRMESSAG(I) = NONEXIST (I)
               ENDDO
C
CCDM               TRUSTRED = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       CHECK UAT NUMBER
C ---       ----------------
C
            ELSEIF (      ( TRUATNUM .LT. 1      )
     -               .OR. ( TRUATNUM .GT. MAXUAT ) ) THEN
C
               DO I = 1 , 34
                  TRMESSAG(I) = ILUAT(I)
               ENDDO
C
CCDM               TRUSTRED = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       CHECK SECRET CODE
C ---       -----------------
C
            ELSEIF ( TRPROTCD .NE. SECRETCO ) THEN
C
               DO I = 1 , 34
                  TRMESSAG(I) = ENTERCOD(I)
               ENDDO
C
CCDM               TRUSTRED = .FALSE.
               TRPROTCD = 0
C
C
C
C ---       --------------------------------------
C ---       INITIATE UAT TRANSFER
C ---       ---------------------
C
            ELSE
C
               DO I = 1 , 34
                  TRMESSAG(I) = BLANKS(I)
               ENDDO
C
               TRTRANSS        = .TRUE.
               TRINDEX           = NEWREQINDX
               TRMODE            = EDITMODE
               TRPROCAC          = .TRUE.         ! Process act
               TRPROCPT          = PROCBEG
               TRSETPRO          = .TRUE.
               TRUATST(TRUATNUM) = ACTIVE
C
C ---
C
               TRSRFINU = SNP                     ! Source      file #
               TRDSFINU = UAT                     ! Destination file #
C
               TRBOFFR     = ( TRSNPNUM - 1 ) * SNPSIZE
               TRBLKRPT(1) = TRBOFFR + 1
               TRBOFFW     = ( TRUATNUM - 1 ) * SNPSIZE
               TRBLKWPT(1) = TRBOFFW + 1
C
               TRBYXFER    = SNPSIZE * BLKSIZE    ! # of blocks to transfer
C
C ---
C
               GOTO PROGCNTL
C
            ENDIF
C
         ENDIF
C
      ENDIF
C
C ---
C
C???  IF ( TRTRANSS ) THEN
C
C???    TRSETNUM = SETNUM
C???    TRSTREDT = .TRUE.
C
C???    TRDEMNUM = SETNUM
C???    TRDEMEDT = .TRUE.
C
C???    TRUSETNU = SETNUM
C???    TRUSTRED = .TRUE.
C
C???  ENDIF
C
C
C
C --- =======================================================================
C --- RAP CONTROL
C --- =======================================================================
C
 89   CONTINUE
C
C ---
C
   90 GOTO
C     ****
C
     -( 100                 ! RAP NOT ACTIVE
     -, 150                 ! STOP RECORDING
     -, 200                 ! START RECORDING
     -, 250                 ! RECORDING NO LONGER ACTIVE
     -, 300                 ! RECORDING IN PROGRESS
     -, 350                 ! SPARE
     -, 400                 ! RESTORATION AFTER REPLAY FINISHED
     -, 450                 ! SNAPSHOT A START OF REPLAY
     -, 500                 ! INITIALIZE REPLAY
     -, 550                 ! PLAYBACK NO LONGER ACTIVE
     -, 600                 ! PLAYBACK ENABLED (INIT. COMP.)
     -, 650                 ! PLAYBACK IN PROGRESS
C
     -      ) TRCONTRO     ! RAP CONTROL WORD
C
C
C
C --- =======================================================================
C --- RAP INACTIVE
C --- ----------------------------------
C --- TRCONTRO = 01
C --- =======================================================================
C
  100 CONTINUE
C
C ---
C
      IF ( .NOT. (      TRSTOREC
     -             .OR. DIRECTORY
     -             .OR. TRPROCAC
     -             .OR. PROCACTP ) ) THEN
C
C
C
C ---    -----------------------------------------
C ---    RECORD REQUEST
C ---    --------------
C
         IF ( TRRAPSTO ) THEN
C
            TRRAPTIM = RAPTMSTD                ! 10 MINUTES RAP
            TREVTIM  = 0.0                     ! reset event time
            TREVRPAV  = .FALSE.
CCDM            TRWRAPRQ   = .TRUE.
CCDM            QMR_TRWRAPRQ=TRWRAPRQ!***FPC-QMR***
            TRMODE      = RAPMODESTO
            TRRAP       = .TRUE.
            TRCONTRO   = 3
            TRPROCNU    = 1
            TRPROCAC    = .TRUE.                  ! Process act
            TRINDEX     = NEWREQINDX
            TRRAPAV     = .TRUE.                  ! A Recording avai. for play
            TRFILNO     = RCD                     ! File number
CJF         TRCLIN(19)  = 0
C
C
C
C ---    -----------------------------------------
C ---    PLAYBACK REQUEST
C ---    ----------------
C
         ELSEIF ( TRRAPREC ) THEN
C
            TRINDEX  = NEWREQINDX
            TRMODE   = RAPMODEREC
CCDM1091            TCMSOUND = .true.
C
C
C ---    -----------------------------------------
C ---    PLAYBACK AT EVENT ?
C ---    ----------------
C

            IF ( TREVREPL ) THEN
              IF ( TREVTIM .LE. 0 ) THEN
                TREVREPL  = .FALSE.
              ELSE
                TRINSTTI  = TRRAPTM - TREVTIM + 10    ! 10 sec before event
                TRTIMETO  = TRINSTTI
              ENDIF
            ENDIF
C
C
C
            TRTIME    = TRINSTTI
C
CVAX
CVAX        TRBOFFR =   TRAPSTAR
CVAX -                + (   JINT(   (   TRRAPTM
CVAX -                                - TRINSTTI )
CVAX -                            * (   TRCONV     )
CVAX -                            / (   TR16SIZ  ) )
CVAX -                    * TR16SIZ                  )
CVAX -                - 1
CVAXEND
C
CIBM
            TRBOFFR =   TRAPSTAR
     -                + (   INT(   (   TRRAPTM
     -                                - TRINSTTI )
     -                            * (   TRCONV     )
     -                            / (   TR16SIZ  ) )
     -                    * TR16SIZ                  )
     -                - 1
CIBMEND
C
            IF ( TRBOFFR .GE. TRRAPSIZ )
     -           TRBOFFR = TRBOFFR - (TRRAPSIZ/TR16SIZ)
     -                               * TR16SIZ
CCDM1091     -           TRBOFFR = TRBOFFR - TRRAPSIZ + 1
C
            TRRAPTIM = TRINSTTI
C
            YIFREZ                 = .TRUE.
            TRREADY              = .FALSE.
            TRMODE               = RAPMODEREC
            TRCONTRO            = 8
            TRRAPEN              = .FALSE.
            TRAPFRZ              = .TRUE.
            TRRAPREP             = TRRAPREC
            TR800ITR             = 8.0
            TRSTOREC             = .TRUE.
            TRPROCAC             =  .TRUE.               ! Process active
            TRPROCNU             = 1
            TRSCRSTO             = .TRUE.
            TRBUFFLG(3)          = .TRUE.    !make sure qio ready see TRQ 14
            IOBASE               = 1
            TRRAPON              = .TRUE.
            TCFTOT               = .FALSE.
            TRTHRTIMR            = 310       ! THROTTLE WATCH DOG (30sec)
CJF         TRDCB                = TRFILDCBS(SCR)
CJF         TRBOFFSRW            = 1
CJF         TRSCRNUM             = RAPSBREC
C
C
C
C ---    -----------------------------------------
C ---    DEMO PLAYBACK REQUEST
C ---    ---------------------
C
         ELSEIF ( .FALSE. ) THEN       !TRDEMPBK
C
C ---
C
            IF (      ( TRDEMNUM .LT. 1      )
     -           .OR. ( TRDEMNUM .GT. MAXDEM ) ) THEN
C
               TRINDEX   = NOTACTINDX
CCDM               TRDEMPBK  = .FALSE.
               TRDEMNUM  = 0
C
            ELSEIF ( TRDEMST(TRDEMNUM) .GE. UNPR_OCC ) THEN
C
               TRINDEX  = NEWREQINDX
               TRMODE   = RAPMODEREC
               TCMSOUND = .true.
C
CJF            IF ( TRINSTTI .GT. TRDEMTM(TRDEMNUM) )
CJF  -              TRINSTTI  =   TRDEMTM(TRDEMNUM)
C
               TRTIME   = TRDEMTM(TRDEMNUM)
               TRTIMETO = TRDEMTM(TRDEMNUM)
C
               TRBOFFR = TRRAPSIZ * ( TRDEMNUM - 1 )
C
CJF               TRBOFFR =   TRAPSTAR
CJF     -                   + (   JINT(   (   TRDEMTM(TRDEMNUM) )
CJF     -                               * (   TRCONV            )
CJF     -                               / (   BUFBLKSZ           ) )
CJF     -                       * BUFBLKSZ                           )
CJF     -                   - 1
C
CJF               IF ( TRBOFFR .GE. RAPDISCSZ )
CJF     -              TRBOFFR = TRBOFFR - RAPDISCSZ
C
CJF            TRRAPTIM = TRINSTTI
C
               TRRAPTIM = TRDEMTM(TRDEMNUM)
C
               YIFREZ                 = .TRUE.
               TRREADY              = .FALSE.
               TRMODE               = RAPMODEREC
               TRCONTRO            = 8
               TRRAPEN              = .FALSE.
               TRAPFRZ              = .TRUE.
               TRDEMREP             = .TRUE.
               TR800ITR             = 8.0
               TRSTOREC             = .TRUE.
               TRPROCAC             =  .TRUE.               ! Process active
               TRPROCNU             = 1
               TRSCRSTO             = .TRUE.
               IOBASE               = 1
               TRRAPON              = .TRUE.
               TCFTOT               = .FALSE.
CJF            TRDCB                = TRFILDCBS(SCR)
CJF            TRBOFFSRW            = 1
CJF            TRSCRNUM             = RAPSBREC
C
            ELSE
C
               TRINDEX  = NOTACTINDX
CCDM               TRDEMPBK = .FALSE.
C
            ENDIF
C
         ENDIF
C
      ENDIF
C
C ---
C
      GOTO PROGCNTL              ! LABEL
C
C
C
C --- =======================================================================
C --- STOP RECORDING
C --- ----------------------------------
C --- TRCONTRO = 02
C --- =======================================================================
C
  150 CONTINUE
C
C ---
C
      IF ( TR800COM ) THEN
C
        IF ( TRQOPT .EQ. QIO_IOQD ) THEN
C
C
CIBM
          IF ( TRBIOFLG(1) .EQ. 1 ) TRBUFFLG(1) = .TRUE.
          IF ( TRBIOFLG(2) .EQ. 1 ) TRBUFFLG(2) = .TRUE.
CIBMEND
          IF (       TRBUFFLG(1)
     -         .AND. TRBUFFLG(2) ) THEN
C

            TRRAPTM           = TRTIME
            IF ( DIRPREV ) THEN
              IF ( .NOT. DIRECTORY ) THEN
CCDM                TRWRAPRQ     = .FALSE.
CCDM                QMR_TRWRAPRQ=TRWRAPRQ!***FPC-QMR***
                TRPROCAC      = .FALSE.                    ! Process active
                DIRPREV = .FALSE.
                TRCONTRO     = 1
                TRRAP         = .FALSE.
                TRMODE        = NOTACTMODE
                IF ( TRRAPST .EQ. ACTIVE ) THEN
                  TRRAPST  = RAPFIN
                  TRRAPSTO = .FALSE.
                ENDIF
                TRRAPTM = TRRAPTM + 0.03333333333
                TRTIMETO = TRRAPTM
C
              ENDIF
            ELSE
              TRRAPTM           = TRTIME
              DIRPREV     = .TRUE.
              DIRECTORY         = .FALSE.
C
C
C
C --- --------------------------------
C --- COMPUTE RAP STARTING BLOCK
C
              IF ( TRTIME .EQ. TRRAPTIM ) THEN
C
                TRAPSTAR = TRBLKWPT( 3 - IOBASE ) + BUFBLKSZ
     -                                            + TRSCBSEC(2)
CCDM1091                TRAPSTAR  = TRBLKWPT( 3 - IOBASE ) + BUFBLKSZ
CCDM1091                IF ( TRAPSTAR .GT. TRRAPSIZ ) TRAPSTAR = 1
C
              ELSE
C
                TRAPSTAR  = 1
C
              ENDIF
C
C ---
C
              TRPROCIN(1) = NEWREQINDX
            ENDIF
          ENDIF
        ENDIF
      ELSE
        TRERROR = -200 - TRCONTRO
      ENDIF
C
C ---
C
      GOTO PROGCNTL                               ! LABEL
C
C
C
C --- =======================================================================
C --- START RECORDING
C --- ----------------------------------
C --- TRCONTRO = 03
C --- =======================================================================
C
  200 CONTINUE
C
C ---
C
      TRBOFFW     = ( RAPNUM - 1 ) * TRRCSIZ
CCDM      TRWRAPRQ   = .FALSE.
CCDM      QMR_TRWRAPRQ=TRWRAPRQ!***FPC-QMR***
      CLOCK       = 0
      TRTIME      = 0.0
      TRRTIME      = 0.0
      TRCONTRO   = 5
      IOBASE      = 1
      P1_STOP     = CONT
      P2_STOP     = CONT
C
      TRBLKWPT(1) = TRBOFFW                          + 1
      TRBLKWPT(2) = TRBOFFW - BUFBLKSZ - TRSCBSEC(2) + 1
C
      TRWRAPRN   = .FALSE.                       ! RAP around flag
      TRWRAPBU    = 1                             ! RAP starting buffer
      TR16TOG     = .FALSE.                       ! 1.6 sec toggle
C
      TRFILADD    = TRFILFCB(RCD)
C
C ---
C
      GOTO PROGCNTL                                    ! LABEL
C
C
C
C --- =======================================================================
C --- RECORD NOT ACTIVE
C --- ----------------------------------
C --- TRCONTRO = 04
C --- =======================================================================
C
  250 CONTINUE
C
C ---
C
      TRCONTRO = 1
      TRINDEX   = NOTACTINDX
C
C ---
C
      GOTO PROGCNTL                                 ! LABEL
C
C
C
C --- =======================================================================
C --- RECORD IN PROGRESS
C --- ----------------------------------
C --- TRCONTRO = 05
C --- =======================================================================
C
  300 CONTINUE
C
C
C
C --- --------------------------------------------
C --- LOOK FOR AN EVENT MARKER
C --- ---------------------------
C
      IF ( TREVMARK ) THEN
        IF ( TRRTIME .LE. 10 ) THEN                 ! AT LEAST 10 SEC
          TREVMARK  = .FALSE.
        ELSE
          TREVMARK  = .FALSE.
          TREVRPAV  = .TRUE.
          TREVTIM   = TRRTIME                       ! SAVE EVENT TIME
          TRNEWEV   = .TRUE.                        ! NEW EVENT MARKER
        ENDIF
      ENDIF
C
C
C
C --- -------------------------------------------------------------
C --- LOOK FOR AN OVERWRITE OF THE EVENT MARKER DURING RAP AROUND
C --- -----------------------------------------------------------
C
      IF ( TRWRAPRN ) THEN
        IF ( TREVTIM .GT. 0 .AND. TRRTIME .GE. TREVTIM ) THEN
          IF ( .NOT.TRNEWEV ) THEN                ! NEW EVENT MARKER ?
            TREVRPAV  = .FALSE.              ! RESET OLD EVENT MARKER
            TREVTIM   = 0.0
          ENDIF
        ENDIF
      ENDIF
C
C
C
C --- --------------------------------------------
C --- LOOK IF AN I/O OF SNAPSHOT STORE IS STANDING BY
C --- ---------------------------
C
      IF ( OLDTRQOPT .NE. QIO_IOQD) THEN
C
        IF ( TRQOPT .NE. QIO_IOQD) THEN
          TEMPTRQ = TRQOPT
        ELSE
          TEMPTRQ = QIO_IOQD
        ENDIF
C
        TRQOPT    = OLDTRQOPT
        OLDTRQOPT = TEMPTRQ
C
      ENDIF
C
C
C
C
C --- --------------------------------------------
C --- LOOK FOR A SNAPSHOT REQUEST
C --- ---------------------------
C
      IF (      TRSNPSTO
     -     .OR. STORECPV ) THEN
C
         IF (       ( .NOT. TRSNPSTO     )
     -        .AND. (       STORECPV ) ) THEN
C
            STORECPV = .FALSE.
            TRPROCNU     = 1
C
         ELSE IF (       (       ( TRSNPNUM .GE. 1      )
     -                     .AND. ( TRSNPNUM .LE. MAXSNP )  )
     -             .AND. ( TRSNPST(TRSNPNUM) .NE. OCCUPIED )
     -             .AND. ( .NOT. STORECPV              ) ) THEN
C
C
            TRPROCNU             = 2
            TRSTOREC             = .TRUE.
            STORECPV         = .TRUE.
            TRSOFFW           = SNPSIZE * ( TRSNPNUM - 1 )
CJF         TRBOFFSRW            = ( TRSNPNUM - 1 ) * SNPSIZE + 1
C
         ENDIF
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- MONITOR THE TOTAL FREEZE FLAG
C --- -----------------------------
C
      IF ( .NOT. YIFREZ  ) THEN
C
         IF ( .NOT. TRIOERR ) THEN
C
            IF ( CLOCK .NE. CLOCKEND ) THEN
C
               TR800COM = .FALSE.
C
            ELSE
C
               TR800COM = .TRUE.
C
C
C
C ---          -----------------------------------
C ---          MONITOR FOR A STOP RECORDING
C ---          ----------------------------
C
               IF (       (      ( .NOT. TRRAPSTO          )
     -                      .OR. (       TRRAPREC          )
     -                      .OR. ( TRTIME .GE. TRRAPTIM    )
     -                            .AND. ( .NOT. TRWRAPRQ   ) )
     -              .AND. (        TR16TOG                   ) ) THEN
C
                  TRRAPSTO  = .FALSE.
                  TRCONTRO = 2
C
               ENDIF
C
               TR16TOG = .NOT. TR16TOG            ! 1.6 sec toggle
C
C ---
C
               IF (.NOT.TRSCBFLG) THEN
C
                 TRIOBASE       = IOBASE
                 IOBASE         = 3 - IOBASE
C
                 B800_PTR       = 1
                 B400_PTR       = 1
                 B200_PTR       = 1
                 B100_PTR       = 1
                 B033_PTR       = 1
                 SCR_PTR        = 1
                 CF_PTR         = 1
C
C
C
C ---          -----------------------------------
C ---          QUEUE CURRENT I/O BUFFER FOR WRITE
C ---          -----------------------------------
C
                 TRBLKWPT(IOBASE)   =   TRBLKWPT(IOBASE)
     &                                + BUFBLKSZ * 2
     &                                + TRSCBSEC(1)
     &                                + TRSCBSEC(2)
C
                 IF ( TRQOPT .NE. QIO_IOQD ) OLDTRQOPT = TRQOPT
C
                 TRQOPT             = QIO_BUFW
C
                 TRBUFCOD(TRIOBASE) = 0
                 TRBUFFLG(TRIOBASE) = .false.
CIBM
                 TRBIOFLG(TRIOBASE) = 0
CIBMEND
               ELSE
                 SCBERRCNT = SCBERRCNT + 1        ! SCB IO error encountered
               ENDIF
C
C ---
C
CCDM               IF ( TRWRAPRN ) THEN
C
CCDM                  TRWRAPBU = TRWRAPBU + 1         ! RAP starting buffer
C
CCDM                  IF ( TRWRAPBU .GT. NUMBUFR ) TRWRAPBU = 1
C
CCDM               ENDIF
C
C
C
CCDM1091C ---          -----------------------------------
CCDM1091C ---          CHECK FOR RAP AROUND
CCDM1091C ---          --------------------
CCDM1091C
CCDM1091               IF (       ( TRRAPSTO                         )
CCDM1091     -              .AND. ( TRBLKWPT(TRIOBASE) .GT. TRRAPSIZ ) ) THEN
C
C
C ---          -----------------------------------
C ---          CHECK FOR RAP AROUND (ALWAYS START AT THE BEGINNING OF
C ---                                RAP FILE WITH TRIOBASE==1       )
C ---          --------------------
C
               IF (       (    TRRAPSTO                    )
     -              .AND. (    TRIOBASE .EQ. 1             )
     -              .AND. ( (  TRBLKWPT(TRIOBASE)
     -                       + 2*BUFBLKSZ + TRSCBSEC(1)
     -                       + TRSCBSEC(2) ) .GT. TRRAPSIZ ) ) THEN
C
                  TRWRAPRN          = .TRUE.     ! RAP around flag
C
                  TRBLKWPT(TRIOBASE) =   TRBOFFW
     &                                 + 1
C
                  TRBLKWPT(IOBASE)   =   TRBOFFW
     &                                 + BUFBLKSZ
     &                                 + TRSCBSEC(TRIOBASE)
     &                                 + 1
C
CCDM                  TRWRAPBU           = 2          ! RAP starting buffer
                  TRTIME             = EVENTOFFTIM
                  TRRTIME  = 0.0                      ! RESET cont time
                  TRNEWEV   = .FALSE.                 ! OLD EVENT MARKER
C
               ENDIF
C
C ---
C
            ENDIF
C
            IF ( TRCONTRO .EQ. 2 ) THEN
C
               IF ( IOBASE .NE. 1 ) THEN
C
                  P1_STOP = STOP
C
               ELSE
C
                  P2_STOP = STOP
C
               ENDIF
C
               TRPROCIN(1) = NEWREQINDX
               TRAPSTOP   = .FALSE.
               TRRAPSTO      = .FALSE.
C
            ELSEIF ( .NOT. TRWRAPRN ) THEN
C
               TRTIME = MIN( ( TRTIME + 0.03333333333 )
     -,                      ( TRRAPTIM            ) )
C
            ENDIF
C
            IF ( TRRAPSTO ) THEN
C
               TRTIMETO = TRTIME                ! for page display
               TRRTIME = TRRTIME + 0.03333333333 ! CONTINUOUS TIME
C
            ENDIF
C
         ENDIF
C
      ENDIF
C
C ---
C
      GOTO PROGCNTL                      ! LABEL
C
C
C
C --- =======================================================================
C --- SPARE
C --- ----------------------------------
C --- TRCONTRO = 06
C --- =======================================================================
C
  350 CONTINUE
C
C ---
C
      TRCONTRO = 1
C
C ---
C
      GOTO PROGCNTL                                 ! LABEL
C
C
C
C --- =======================================================================
C --- PLAYBACK RESTORATION
C --- ----------------------------------
C --- TRCONTRO = 07
C --- =======================================================================
C
  400 CONTINUE
C
C ---
C
      IF ( .NOT. TRRESINI ) THEN
        IF ( .NOT. TRRAPRES ) THEN
          TRCONTRO    = 1
          TRINDEX      = NOTACTINDX
          TRMODE       = NOTACTMODE
          TRRAPON      = .FALSE.
          TRNOTRIM     = .FALSE.
          TRTIMETO   = TRRAPTM
          TRRAPREC     = .FALSE.
CCDM          TRDEMPBK     = .FALSE.                  ! DEMO playback
          TRRAPREP     = .FALSE.
          TRDEMREP     = .FALSE.
          TRRAP        = .FALSE.
C
CJF          HEMODE(1)    = 0
CJF          HEMODE(2)    = 0
CJF          HEMODE(3)    = 0
CJF          HEMODE(4)    = 0
CJF          OLDHEMODE(1) = HEMODE(1)
CJF          OLDHEMODE(2) = HEMODE(2)
CJF          OLDHEMODE(3) = HEMODE(3)
CJF          OLDHEMODE(4) = HEMODE(4)
C
        ELSE
          IF ( TRREADY ) THEN
            TRRAPRES = .FALSE.
          ENDIF
        ENDIF
      ELSE
C
        IF (      TRRAPREC .OR. TRRAPREP ) THEN
CCDM     &       .OR. TRDEMPBK .OR. TRDEMREP ) THEN
C
CJF       TRSCRNUM   = RAPSBREC
          TRIOFLAG(1)    = .FALSE.
          TRSCBFLG = .FALSE.
          TRRECALL      = .TRUE.
          TRSTOREC      = .TRUE.
          TRPROCNU      = 1
          TRBOFFR       = 0
          TRFILNO       = SCR
          TRBUFFLG(3)   = .TRUE.           ! make sure qio ready see TRQ 14
CJF       TRBOFFR       = TRBOFF(TRMAXSEC)
CJF       TRBOFFR       = TRSCRNUM*SNPSIZE+TRBOFF(TRMAXSEC)
          TRPROCIN(1) = NEWREQINDX
          TRPROCIN(2) = NEWREQINDX
          TRRAPRES  = .TRUE.
          TRRESINI     = .FALSE.
          IOBASE        = 1
          TRNOXREF      = .TRUE.
          MPLBK         = .false.                 ! Motion Playback Request
C
          TRFILADD    = TRFILFCB(SCR)
C
        ENDIF
        TRRESINI       = .FALSE.
      ENDIF
C
C ---
C
      GOTO PROGCNTL              ! LABEL
C
C
C
C --- =======================================================================
C --- REPLAY SNAPSHOT
C --- ----------------------------------
C --- TRCONTRO = 08
C --- =======================================================================
C
  450 CONTINUE
C
C ---
C
      IF (       ( .NOT. TRSTOREC )
     -     .AND. ( .NOT. TRREADY  ) ) THEN
C
C
C
C ---    -----------------------------------------
C ---    RECALL INTERFACE ONLY
C ---    ---------------------
C
         TRSCBFLG = .FALSE.
         TRRECALL     = .true.
C
         TRNOXREF     = .true.
         TRSTOREC     = .true.
C
         TRIOBASE     = 1
C
         IF ( TRRAPREC ) THEN
            TRFILNO      = RCD                    ! RAP  playabck
         ELSE
            TRFILNO      = DEM                    ! DEMO playabck
         ENDIF
C
C ---
C
CVAX
CVAX     TRBOFFR      = JMAX0(TRBOFFR,0)
CVAX     TRFILADD     = TRFILFCB(RCD)
CVAXEND
C
CIBM
         TRBOFFR      = MAX0(TRBOFFR,0)
         TRFILADD     = TRFILFCB(RCD)
CIBMEND
C
CSEL
CSEL     TRBOFFR      =  MAX0(TRBOFFR,0)
CSELEND
C
CJF      TRTIME       = TRINSTTI
C
C ---
C
      ELSE IF ( TRREADY ) THEN
C
         TRREADY      = .FALSE.
         TRCONTRO    = 9
         TRINITIA = .TRUE.
         TRPBKSTO     = .FALSE.
C
         TRIOBASE     = 1
         TRQOPT       = QIO_BUFR
C
         TRBUFCOD(TRIOBASE) = 0
         TRBUFFLG(TRIOBASE) = .false.
CIBM
         TRBIOFLG(TRIOBASE) = 0
CIBMEND
C
         TRBLKRPT(1)  = TRBOFFR                          + 1
         TRBLKRPT(2)  = TRBOFFR + BUFBLKSZ + TRSCBSEC(1) + 1
C
CJF      TRBOFFR      = JINT( ( TRRAPTM - TRINSTTI ) *
CJF  -                          TRCONV / WRITELENGTH )
CJF  -                        * WRITELENGTH + 1
C
         IOBASE       = 2
C
      ENDIF
C
C ---
C
      GOTO PROGCNTL                                       !Label
C
C
C
C --- =======================================================================
C --- INITIALIZE REPLAY
C --- ----------------------------------
C --- TRCONTRO = 09
C --- =======================================================================
C
  500 CONTINUE
C
C ---
C
CIBM
      IF ( IOSTATE .EQ. 1 ) THEN
        TRBUFFLG(1) = .FALSE.
      ELSEIF ( TRIOFLG .EQ. 1 .AND. IOSTATE .EQ. 2 ) THEN
        TRBUFFLG(1) = .TRUE.
      ELSEIF ( TRIOFLG .EQ. 1 .AND. IOSTATE .EQ. 3  ) THEN
        SCB2FIN     = .TRUE.
      ENDIF
CIBMEND
C
      IF ( TRBUFFLG(1) ) THEN                     ! WAIT FOR END OF SNAP
C
        TRQOPT = 12   ! READ THE 2nd SECTION OF SCB FROM DISK TO CDB
C
        TRBUFFLG(1) = .FALSE.
CIBM
        TRIOFLG     = 0
CIBMEND
C
      ELSEIF (SCB2FIN) THEN       !WAIT FOR END OF SCB #2
C
        SCB2FIN        = .FALSE.
        TRBUFFLG(1)    = .TRUE.
        TRSCBFLG       = .FALSE.
        TRINTFIR     = .TRUE.
        TRAPFRZ        = .TRUE.
        TRCONTRO      = 11
        CLOCKTRC       = 2
        IOBASE         = 1
C
        TR800ITR       = 24.0
        TR800IT4       = 24
        TR800IT2       = 24
C
        TR400ITR       = 12.0
        TR400IT4       = 12
        TR400IT2       = 12
C
        TR200ITR       = 6.
        TR200IT4       = 6
        TR200IT2       = 6
C
        TR100ITR       = 3.
        TR100IT4       = 3
        TR100IT2       = 3
C
        TR033ITR       = 1.
        TR033IT4       = 1
        TR033IT2       = 1
C
        B033_OFSET     = 0
        B100_OFSET     = 0
        B200_OFSET     = 0
        B400_OFSET     = 0
        B800_OFSET     = 0
        SCR_OFSET      = 0
        CF_OFSET       = 0
C
        TCFTOT         = .TRUE.
        TRMONOFR      = .TRUE.
C
C ---   PUT BACK THE STATE OF SOUND MUTE BEFORE THE PLAYBACK.
C
        TCMSOUND       = O_TCMSOUND
      ENDIF
C
C ---
C
      GOTO PROGCNTL                                    ! LABEL
C
C
C
C --- =======================================================================
C --- PLAYBACK NOT ACTIVE
C --- ----------------------------------
C --- TRCONTRO = 10
C --- =======================================================================
C
  550 CONTINUE
C
C ---
C
      TRCONTRO = 1
      TRINDEX   = NOTACTINDX
C
C ---
C
      GOTO PROGCNTL             ! LABEL
C
C
C
C --- =======================================================================
C --- PLAYBACK ENABLED
C --- ----------------------------------
C --- TRCONTRO = 11
C --- =======================================================================
C
  600 CONTINUE
C
C ---
C
      IF ( .NOT. TRAPFRZ ) THEN         ! WAIT FOR FREEZE RELEASE
        TRRAPEN   = .TRUE.
        TRCONTRO = 12
        MPLBK     = .true.                        ! Motion Playback Request
CCDM1091        TCMSOUND  = .false.
      ENDIF
C
C ---
C
      GOTO PROGCNTL                                   ! Label
C
C
C
C --- =======================================================================
C --- PLAYBACK IN PROGRESS
C --- ----------------------------------
C --- TRCONTRO = 12
C --- =======================================================================
C
 650  CONTINUE
C
C
C
C --- ----------------------------------
C --- COMPUTE THE CLOCK ( TRE )
C
      CLOCKTRE = CLOCKTRC
C
C
C
C --- ----------------------------------
C --- SUSPEND PLAYBACK FOR FREEZE
C
      IF ( TRAPFRZ ) THEN
        TRRAPEN  = .FALSE.
        TRRAPSEN = .FALSE.
CCDM1091        TCMSOUND  = .true.
        GOTO PROGCNTL
      ELSE
        TRRAPEN  = .TRUE.
        TRRAPSEN = .TRUE.
CCDM1091        TCMSOUND  = .false.
      ENDIF
C
C
C
C --- --------------------------------------------
C --- LOOK FOR A STOP SIGN
C --- --------------------
C
      IF ( IOBASE .NE. 1 )  THEN
C
         IF ( P2_STOP .EQ. STOP ) TRPBKSTO = .TRUE.
C
      ELSE
C
         IF ( P1_STOP .EQ. STOP ) TRPBKSTO = .TRUE.
C
      ENDIF
C
C
C
C --- -------------------------------------------
C --- CHECK IF PLAYBACK MUST STOP
C --- ---------------------------
C
      IF (      ( .NOT. TRRAPREC .AND. TRRAPREP )
CCDM     -     .OR. ( .NOT. TRDEMPBK .AND. TRDEMREP )
     -     .OR. ( TRTIME .LE. 1.7               )
     -     .OR. (       ( TRPBKSTO         )
     -            .AND. ( CLOCKTRC .EQ.  2 )    ) ) THEN
C
         TRCONTRO = 7
         TRRESINI = .TRUE.
         TRRAPEN   = .FALSE.
         TRRAPSEN  = .FALSE.
CCDM1091         TCMSOUND  = .true.
         TRTIMETO  = 0.0
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- DO THE ACTUAL PLAYBACK CONTROL
C --- ------------------------------
C
      IF ( TRCONTRO .EQ. 12 ) THEN
C
        IF ( .NOT. TRAPFRZ ) THEN
C
          TRTIME = MAX( MIN( TRTIME - 0.033333333333
     -,                      TRRAPTIM             )
     -,                 0.0                            )
C
CJF       IF ( TRRAPREC ) TRTIMETO = TRTIME
C
          TRTIMETO = TRTIME
C
C
C
C ---     ----------------------------------------
C ---     ISSUE A I/O REQUEST FOR THE NEXT
C ---     INTERFACE BUFFER
C ---     ----------------
C
          IF (( CLOCKTRC .EQ. 4 ).AND..NOT.TRSCBFLG) THEN
C
            TRQOPT              = QIO_BUFR
            TRIOBASE            = 3 - IOBASE
C
            TRBUFCOD(TRIOBASE) = 0
            TRBUFFLG(TRIOBASE) = .false.
CIBM
            TRBIOFLG(TRIOBASE) = 0
CIBMEND
C
C
C
CCDM1091C ---       --------------------------------------
CCDM1091C ---       CHECK FOR RAP AROUND
CCDM1091C ---       --------------------
CCDM1091C
CCDM1091            IF (       ( TRRAPREC                         )
CCDM1091     -           .AND. ( TRBLKRPT(TRIOBASE) .GT. TRRAPSIZ ) ) THEN
CCDM1091C
CCDM1091               TRBLKRPT(TRIOBASE) = 1
CCDM1091               TRBLKRPT(IOBASE)   = 1 - BUFBLKSZ
CCDM1091     -                             - TRSCBSEC(IOBASE)
CCDM1091CCDM               TRBLKRPT(IOBASE)   = TRBOFFW + BUFBLKSZ + 1
CCDM1091CCDM     -                             + TRSCBSEC(TRIOBASE)
CCDM1091C
CCDM1091            ENDIF
C
          ENDIF
C
C
C
C ---     ----------------------------------------
C ---     REINITIALIZE TO PLAYBACK A NEW
C ---     INTERFACE BUFFER
C
          IF ( CLOCKTRC .EQ. 1 ) THEN
C
            TRBLKRPT(IOBASE) =   TRBLKRPT(IOBASE)
     &                         + BUFBLKSZ * 2
     &                         + TRSCBSEC(1)
     &                         + TRSCBSEC(2)
C
C ---          -----------------------------------
C ---          CHECK FOR RAP AROUND
C ---          --------------------
C
               IF (       (    TRRAPREC                       )
     -              .AND. (    IOBASE .EQ. 1                  )
     -              .AND. ( (  TRBLKRPT(IOBASE)
     -                       + 2*BUFBLKSZ + TRSCBSEC(1)
     -                       + TRSCBSEC(2) ) .GT. TRRAPSIZ ) ) THEN
C
                  TRBLKRPT(IOBASE) =   1
C
                  TRBLKRPT(TRIOBASE)   =   1 - BUFBLKSZ - TRSCBSEC(2)
C
               ENDIF
C
            IOBASE         = 3 - IOBASE
C
            TR800ITR       = 24.
            TR800IT4       = 24
            TR800IT2       = 24
C
            TR400ITR       = 12.
            TR400IT4       = 12
            TR400IT2       = 12
C
            TR200ITR       = 6.
            TR200IT4       = 6
            TR200IT2       = 6
C
            TR100ITR       = 3.
            TR100IT4       = 3
            TR100IT2       = 3
C
            TR033ITR       = 1.
            TR033IT4       = 1
            TR033IT2       = 1
C
            B033_OFSET     = 0
            B100_OFSET     = 0
            B200_OFSET     = 0
            B400_OFSET     = 0
            B800_OFSET     = 0
            SCR_OFSET      = 0
            CF_OFSET       = 0
C
          ELSE
C
C                                          BANDS COUNTER
C                                     DECREMENT       - RESET
C
            GOTO ( 700  ! CLOCK  1  -                 - 33,100,200,400,800
     -,            660  ! CLOCK  2  - 100,200,400,800 - 33,
     -,            660  ! CLOCK  3  - 100,200,400,800 - 33,
     -,            670  ! CLOCK  4  -     200,400,800 - 33,100,
     -,            660  ! CLOCK  5  - 100,200,400,800 - 33,
     -,            660  ! CLOCK  6  - 100,200,400,800 - 33,
     -,            680  ! CLOCK  7  -         400,800 - 33,100,200,
     -,            660  ! CLOCK  8  - 100,200,400,800 - 33,
     -,            660  ! CLOCK  9  - 100,200,400,800 - 33,
     -,            670  ! CLOCK 10  -     200,400,800 - 33,100,
     -,            660  ! CLOCK 11  - 100,200,400,800 - 33,
     -,            660  ! CLOCK 12  - 100,200,400,800 - 33,
     -,            690  ! CLOCK 13  -             800 - 33,100,200,400,
     -,            660  ! CLOCK 14  - 100,200,400,800 - 33,
     -,            660  ! CLOCK 15  - 100,200,400,800 - 33,
     -,            670  ! CLOCK 16  -     200,400,800 - 33,100,
     -,            660  ! CLOCK 17  - 100,200,400,800 - 33,
     -,            660  ! CLOCK 18  - 100,200,400,800 - 33,
     -,            680  ! CLOCK 19  -         400,800 - 33,100,200,
     -,            660  ! CLOCK 20  - 100,200,400,800 - 33,
     -,            660  ! CLOCK 21  - 100,200,400,800 - 33,
     -,            670  ! CLOCK 22  -     200,400,800 - 33,100,
     -,            660  ! CLOCK 23  - 100,200,400,800 - 33,
     -,            660  ! CLOCK 24  - 100,200,400,800 - 33,
C
     -                 ) CLOCKTRC    ! PLAYBACK CNTL CLOCK
C
C
C ---  33 MS BAND DECREMENT ALL COUNTERS
C
  660       CONTINUE
C
            TR800ITR      = TR800ITR - 1.
            TR800IT4      = TR800IT4 - 1
            TR800IT2      = TR800IT2 - 1
C
            TR400ITR      = TR400ITR - 1.
            TR400IT4      = TR400IT4 - 1
            TR400IT2      = TR400IT2 - 1
C
            TR200ITR      = TR200ITR - 1.
            TR200IT4      = TR200IT4 - 1
            TR200IT2      = TR200IT2 - 1
C
            TR100ITR      = TR100ITR - 1.
            TR100IT4      = TR100IT4 - 1
            TR100IT2      = TR100IT2 - 1
C
            TR033ITR      = 1.
            TR033IT4      = 1
            TR033IT2      = 1
C
            B033_OFSET = B033_OFSET + B033BYSZ
            CF_OFSET   = CF_OFSET   + CFN
C
            GOTO 700
C
C
C --- 100 MS BAND RESET 100 MS COUNTER DECRIMENT ALL OTHERS
C
  670       CONTINUE
C
            TR800ITR      = TR800ITR - 1.
            TR800IT4      = TR800IT4 - 1
            TR800IT2      = TR800IT2 - 1
C
            TR400ITR      = TR400ITR - 1.
            TR400IT4      = TR400IT4 - 1
            TR400IT2      = TR400IT2 - 1
C
            TR200ITR      = TR200ITR - 1.
            TR200IT4      = TR200IT4 - 1
            TR200IT2      = TR200IT2 - 1
C
            TR100ITR      = 3.
            TR100IT4      = 3
            TR100IT2      = 3
C
            TR033ITR      = 1.
            TR033IT4      = 1
            TR033IT2      = 1
C
            B100_OFSET = B100_OFSET + B100BYSZ
            B033_OFSET = B033_OFSET + B033BYSZ
            SCR_OFSET  = SCR_OFSET  + SCRN
            CF_OFSET   = CF_OFSET   + CFN
C
            GOTO 700
C
C
C --- 200 MS BAND RESET 100 AND 200 MS COUNTERS DECREMENT 400 MS AND 800 MS
C
  680       CONTINUE
C
            TR800ITR      = TR800ITR - 1.
            TR800IT4      = TR800IT4 - 1
            TR800IT2      = TR800IT2 - 1
C
            TR400ITR      = TR400ITR - 1.
            TR400IT4      = TR400IT4 - 1
            TR400IT2      = TR400IT2 - 1
C
            TR200ITR      = 6.
            TR200IT4      = 6
            TR200IT2      = 6
C
            TR100ITR      = 3.
            TR100IT4      = 3
            TR100IT2      = 3
C
            TR033ITR      = 1.
            TR033IT4      = 1
            TR033IT2      = 1
C
            B200_OFSET = B200_OFSET + B200BYSZ
            B100_OFSET = B100_OFSET + B100BYSZ
            B033_OFSET = B033_OFSET + B033BYSZ
            SCR_OFSET  = SCR_OFSET  + SCRN
            CF_OFSET   = CF_OFSET   + CFN
C
            GOTO 700
C
C
C --- 400 MS BAND RESET 100, 200 AND 400 MS COUNTERS DECREMENT 800 MS
C
  690       CONTINUE
C
            TR800ITR      = TR800ITR - 1.
            TR800IT4      = TR800IT4 - 1
            TR800IT2      = TR800IT2 - 1
C
            TR400ITR      = 12.
            TR400IT4      = 12
            TR400IT2      = 12
C
            TR200ITR      = 6.
            TR200IT4      = 6
            TR200IT2      = 6
C
            TR100ITR      = 3.
            TR100IT4      = 3
            TR100IT2      = 3
C
            TR033ITR      = 1.
            TR033IT4      = 1
            TR033IT2      = 1
C
            B400_OFSET = B400_OFSET + B400BYSZ
            B200_OFSET = B200_OFSET + B200BYSZ
            B100_OFSET = B100_OFSET + B100BYSZ
            B033_OFSET = B033_OFSET + B033BYSZ
            SCR_OFSET  = SCR_OFSET  + SCRN
            CF_OFSET   = CF_OFSET   + CFN
C
C ---
C
  700       CONTINUE
C
C ---
C
          ENDIF
C
C
C
C --- ----------------------------------
C --- UPDATE THE CLOCK (TRC) FOR THE
C --- NEXT ITERRATION
C
          CLOCKTRC = CLOCKTRC + 1
C
          IF ( CLOCKTRC .GT. 24 ) CLOCKTRC = 1
C
C ---
C
        ENDIF
      ENDIF
C
C ---
C
      GOTO PROGCNTL                             ! LABEL
C
C
C
C --- =======================================================================
C --- P R O G R A M   C O N T R O L
C --- =======================================================================
C --- L A B E L   9 0 0     " P R O G C N T L "
C --- =======================================================================
C
  900 CONTINUE
C
      PROGEXEFLAG = .FALSE.
      IF (       ( .NOT. PROCACTP )
     -     .AND. (       TRPROCAC    ) ) THEN
C
        DO I = 1 , 3
          TRPROCPC(I)   = 0
          TRPROCIN(I) = NEWREQINDX
          DO J = 0 , NOPST
            TRPROCCO(I,J) = .FALSE.
          ENDDO
        ENDDO
C
      ENDIF
C
      IF ( TRPROCAC ) THEN
C
        PROCACTP = .TRUE.
  905 CONTINUE
C
      IF (TREFINISH)  TRPROCPT = TRPROCPT + 1
C
        IF ( TRPROCPT .LE. TRPROCNU ) THEN
C
          IF ( .NOT. TRPROCCO(TRPROCPT,TRPROCPC(TRPROCPT)) ) THEN
C
            IF ( TRPROCIN(TRPROCPT) .EQ. NEWREQINDX ) THEN
C
              TRPROCCO( TRPROCPT , TRPROCPC(TRPROCPT) ) = .TRUE.
              TRPROCPC(TRPROCPT) = TRPROCPC(TRPROCPT) + 1
C
              IF ((TRPROCPC(TRPROCPT) .GT. 15)) THEN
                 TRPROCPC(TRPROCPT) = 15
                 PROGEXEFLAG = .FALSE.
                 GOTO 907
              ENDIF
C
              IF ( TRPROGCN( TRPROCPT
     -,                        TRPROCPC(TRPROCPT)
     -,                        TRMODE             )
     -              .EQ. PROCCOMPINDX ) THEN
C
                TRPROCCO( TRPROCPT , TRPROCPC(TRPROCPT) ) = .TRUE.
                GOTO NEXT
              ELSE
                TRINDEX = TRPROGCN( TRPROCPT
     -,                               TRPROCPC(TRPROCPT)
     -,                               TRMODE             )
                GOTO PROGEXE
              ENDIF
            ELSE
              TRINDEX = TRPROGCN( TRPROCPT
     -,                             TRPROCPC(TRPROCPT)
     -,                             TRMODE             )
              GOTO PROGEXE
            ENDIF
          ELSE
            GOTO NEXT
          ENDIF
        ELSE
 907   CONTINUE
          TRPROCPT = PROCBEG
          IF ( .NOT. PROGEXEFLAG ) THEN
            TRMODE   = NOTACTMODE
            TRPROCAC = .FALSE.                                  ! Process act
            TRINDEX  = NOTACTINDX
          ENDIF
          GOTO END
        ENDIF
      ELSE
        PROCACTP = .FALSE.
        GOTO END
      ENDIF
C
C
C
C --- =======================================================================
C --- EXECUTE SECTION
C --- =======================================================================
C --- L A B E L   9 9 9     " P R O G E X E "
C --- =======================================================================
C
 999  CONTINUE
C
C ---
C
      IF  ( TRINDEX .NE. NOTACTINDX ) THEN
C
        CALL TREXE                                ! Call execute mod
C            *****
C
      ENDIF
C
C --- Check for a reset
C
 1000 CONTINUE
C
C ---
C
      TRPROCIN(TRPROCPT) = TRINDEX
      IF ( TRPROCIN(TRPROCPT) .EQ. RESETINDX ) THEN
        TRPROCPC(TRPROCPT)   = 0
        TRPROCIN(TRPROCPT) = NEWREQINDX
        DO J = 0 , NOPST
          TRPROCCO(TRPROCPT,J) = .FALSE.
        ENDDO
      ENDIF
C
C ---
C
      GOTO NEXT
C
C
C
C --- =======================================================================
C --- PROGRAM EXIT
C --- =======================================================================
C --- L A B E L   9 9 0 0 0     " E X I T "
C --- =======================================================================
C
C ---
C
99000 CONTINUE
C
C ---
C
      IF ( TRQOPT .NE. QIO_IOQD ) THEN
C
        CALL TRQIO                                ! CALL I/O MODULE
C            *****
C
      ENDIF
C
C
C
C --- =======================================================================
C --- ALOPS SCALIN AND SCALOUT
C --- =======================================================================
C
      IF (      ( TRMODE .EQ. SNPMODEREC )
     -     .OR. ( TRMODE .EQ. SETMODEREC )
     -     .OR. ( TRMODE .EQ. RAPMODEREC )
Crg+ 02-apr-92 "temporarily removed for initial compilation"
Crg     -     .OR. ( SAENG(1)               )
Crg-
     -     ) THEN
C
C ---
C
         DO I = 1 , 10
C
            IF ( SCALOUTF(I) ) THEN
C
               CALL SCALOUT
C                   *******
C
     -            ( SCALSYS(I) )
C
            ENDIF
C
         ENDDO
C
C ---
C
         DO I = 1 , 10
C
            IF ( SCALINF(I) ) THEN
C
               CALL SCALIN
C                   ******
C
     -            ( SCALSYS(I) )
C
            ENDIF
C
         ENDDO
C
C ---
C
      ENDIF
C
C --- -----------------------------------------------------------------------
C
      RETURN
C
C ---
C
      END

