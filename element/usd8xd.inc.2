
C
C  --- SCTBXD.INC
C
C'Description
C
C      This file describe all the possible error code returned by the
C    Directives routines. A returned code of 0 means that there were no error.
C
C      The flag PROC_HOST is also defined. It's used at compile time to
C    determine if the program is running on a host or on a remote. On a remote
C    the variable are READ from the LOCAL CDB but are WRITTEN to the HOST CDB.
C
      LOGICAL*1 PROC_HOST
      PARAMETER (PROC_HOST = .TRUE.)   ! .TRUE. program running on host
C                                      ! .FALSE. program running on remote
C
      LOGICAL*1 PGDT_OFFB
      PARAMETER (PGDT_OFFB = .TRUE.    ! .TRUE. PAGE.DAT with offset block
     .                                 ! .FALSE. PAGE.DAT without offset b
     .           .AND. PROC_HOST )
C
      INTEGER*4
     -          LOCAL_DATA,
     -          HOST_DATA,
     -          OFFSET_EAS,
     -          BASE_EAS,
     -          BYTE_EAS,
     -          TYPE_EAS,
     -          SIZE_EAS,
     -          DT_L1_EAS,
     -          DT_I2_EAS,
     -          DT_I4_EAS,
     -          DT_R4_EAS,
     -          DT_R8_EAS,
     -          SRC_CV_EAS,
     -          LOC_CV_EAS,
     -          SIZE_LOC,
     -          SIZE_HOST
C
      PARAMETER (
     -          LOCAL_DATA = 2,
     -          HOST_DATA  = 1,
     -          BASE_EAS   = 1,
     -          OFFSET_EAS = 2,
     -          BYTE_EAS   = 3,
     -          TYPE_EAS   = 4,
     -          SIZE_EAS   = 4,
     -          DT_L1_EAS  = 1,
     -          DT_I2_EAS  = 4,
     -          DT_I4_EAS  = 5,
     -          DT_R4_EAS  = 7,
     -          DT_R8_EAS  = 8,
     -          SRC_CV_EAS = 1,
     -          LOC_CV_EAS = 2,
     -          SIZE_LOC   = -249,
     -          SIZE_HOST  = 250
     -          )
C
      INTEGER*4
     -          ERR_UNKN
     - ,        ERR_IN
     - ,        ERR_OUT
     - ,        ERR_NUM
     - ,        ERR_LIM
     - ,        ERR_LEN
     - ,        ERR_SGN
     - ,        ERR_WID
     - ,        ERR_CHR
     - ,        ERR_QTY
     - ,        ERR_TYP
     - ,        ERR_COL
C
      PARAMETER (
     -          ERR_UNKN   = 1        ! Unknown DCB type
     - ,        ERR_IN     = 2        ! no IN directive for this DCB type
     - ,        ERR_OUT    = 3        ! no OUT directive for this DCB type
     - ,        ERR_NUM    = 4        ! invalid character in a numeric string
     - ,        ERR_LIM    = 5        ! number out of limit
     - ,        ERR_LEN    = 6        ! invalid number of char in input string
     - ,        ERR_SGN    = 7        ! invalid char for sign
     - ,        ERR_CHR    = 8        ! invalid char in a string
     - ,        ERR_QTY    = 9        ! too many/few expression in a multiple
     - ,        ERR_TYP    = 10       ! Invalid datatype for this DCB
     - ,        ERR_COL    = 11       ! invalid color
     - )
C
C
C
C --- Operation definition, if an operation is added, these array must be
C     changed
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     .         OPER_STRENGTH(0:OPER_NUMBER)! lower number means higher
C                                          ! priority
     .         /21*0,4, 4, 4, 4, 4, 4, 7, 6,
     .            3, 3, 2, 2, 5, 9, 8, 1, 1, 4, 4/
     . ,       OPER_CTN(0:OPER_NUMBER)     ! number of operand for op
C                                          ! (0, 1 or 2)
     .         /21*0,2, 2, 2, 2, 2, 2, 2, 2,
     .            2, 2, 2, 2, 1, 0, 0, 1, 1, 2, 2/
C
C
      CHARACTER STDSTG(2,45)*10        ! True/false string
     .         ,STDERR(11)*10          ! Error message
C
      INTEGER*2
     &          STDSTLEN(45)           ! Display string length
     &         ,STDERRLEN(11)          ! Error message's length
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          STDSTGI1(10,2,45)      ! Equivalenced to STDSTG
     &         ,STDERRI1(10,11)        ! Equivalenced to STDERR
     &         ,STDDATALEN(10)         ! Standard data length
     &          /2,4,4,8,1,0,0,1,4,4/
C
C
      EQUIVALENCE (STDSTG, STDSTGI1)
     &           ,(STDERR, STDERRI1)
C
C'Data_statements
C
      Data STDSTG /'T',          'F',                     ! 1
     2             ' TRUE',      'FALSE',                 ! 2
     3             ' ON',        'OFF',                   ! 3
     4             'YES',        ' NO',                   ! 4
     5             '  UP',       'DOWN',                  ! 5
     6             'READY',      ' FAIL',                 ! 6
     7             '  ACTIVE',   'INACTIVE',              ! 7
     8             ' NORMAL',    'NEUTRAL',               ! 8
     9             'SELECTED',   '        ',              ! 9
     -             'TRUE',       '     ',                 ! 10
     1             '     ',      'FALSE',                 ! 11
     2             'ON',         '  ',                    ! 12
     3             '   ',        'OFF',                   ! 13
     4             'UP',         '  ',                    ! 14
     5             '    ',       'DOWN',                  ! 15
     6             'READY',      '     ',                 ! 16
     7             '    ',       'FAIL',                  ! 17
     8             'ACTIVE',     '      ',                ! 18
     9             '        ',   'INACTIVE',              ! 19
     -             '*',          ' ' ,                    ! 20
     1             ' ',          ' ' ,                    ! 21
     2             'YES',        ' ' ,                    ! 22
     3             'TRUE',       'MAGN',                  ! 23
     4             'CAPT',       'COPI',                  ! 24
     5             'KILLED',     '      ',                ! 25
     6             'OK',         ' X',                    ! 26
     7             '     RADIO', 'INTERPHONE',            ! 27
     8             'RETRACTED',  ' EXTENDED',             ! 28
     9             'TUNED',      '     ',                 ! 29
     -             'NEGATIVE',   'POSITIVE',              ! 30
     1             'HIGH',       ' LOW',                  ! 31
     2             '     YES',   'NEGATIVE',              ! 32
     3             'ABOVE',      'BELOW',                 ! 33
     4             'ABOVE',      '     ',                 ! 34
     5             'BELOW',      '     ',                 ! 35
     6             'GOOD',       ' BAD',                  ! 36
     7             'ILS',        'GCA',                   ! 37
     8             'PT'   ,      'IP'   ,                 ! 38
     9             'AUTO',       ' MAN',                  ! 39
     -             ' ARM',       'SAFE',                  ! 40
     1             'FAILED',     '      ',                ! 41
     2             'TRIP',       'NORM',                  ! 42
     3             ' OPEN',      'CLOSE',                 ! 43
     4             'ENERGIZED',  '   NORMAL',             ! 44
     5             'DE-ENERG''D','    NORMAL'/            ! 45
C
      Data STDSTLEN  / 1, 5, 3, 3, 4,
     &                 5, 8, 7, 8, 4,
     &                 5, 2, 3, 2, 4,
     &                 5, 4, 6, 8, 1,
     &                 1, 3, 4, 4, 6,
     &                 2, 10,9, 5, 8,
     &                 4, 8, 5, 5, 5,
     &                 4, 3, 2, 4, 4,
     &                 6, 4, 5, 9, 10/
C
      Data STDERR / 'UNKNOW DCB'
     &             ,'NO IN DCB'
     &             ,'NO OUT DCB'
     &             ,'??? CHAR'
     &             ,'OVERFLOW'
     &             ,'BAD LENGTH'
     &             ,'BAD SIGN'
     &             ,'BAD CHAR'
     &             ,'# EXPR'
     &             ,'DATATYPE'
     &             ,'BAD COLOR'/
C
      DATA STDERRLEN / 10, 9, 10, 8, 8, 10, 8, 8, 6, 8, 9 /
C
