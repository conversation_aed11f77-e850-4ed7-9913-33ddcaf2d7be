*
**************************************************************************
**                DN1 LOGIC DMC COMMON DATA BASE                        **
**************************************************************************
*
* This CDB represents a memory map of the DN1 LOGIC DMC host
* communication buffer
*
C$0DMCLOG BASE   00
          BOUN 4
*
*         -------------------------
**        DN1 CABINET STATUS BUFFER
*         -------------------------
*
CLSCLON   LOG1 .F.           CONTROL LOADING ON                           TRUE
CLSCLRDY  LOG1 .F.           CONTROL LOADING READY                        TRUE
CLSMNON   LOG1 .F.           MOTION ON                                    TRUE
CLSMNRDY  LOG1 .F.           MOTION READY                                 TRUE
CLSGSON   LOG1 .F.           G-SYSTEM ON                                  TRUE
CLSGSRDY  LOG1 .F.           G-SYSTEM READY                               TRUE
CLSAWUP   LOG1 .F.           ACCESSWAY UP                                 TRUE
CLSAWDN   LOG1 .F.           ACCESSWAY DOWN                               TRUE
CLSGATE   LOG1 .F.           GATE CLOSED                                  TRUE
CLSWARN   LOG1 .F.           ANY SYSTEM WARNING                           TRUE
CLSFAIL   LOG1 .F.           ANY SYSTEM FAILURE                           TRUE
CLSFMFLG  LOG1 .F.           FAILURE MESSAGE FLAG WORD                    TRUE
CLSFMSEQ  INT2 0             FAILURE MESSAGE SEQUENCE NUMBER              I6
CLSFMCODE INT2 0             FAILURE MESSAGE CODE                         I6
CLSFMTEXT BLL1 80            FAILURE MESSAGE TEXT                         I6
          VAL  80*0
CLCABSTATEBLI2 15            CABINET STATES                               I6
          VAL  15*0
CLSSPARE  BLL1 64            EXTRA WORDS FOR NEW VARIABLES                TRUE
          VAL  64*0
*
*         --------------------------
**        DN1 CABINET REQUEST BUFFER
*         --------------------------
*
CLRSPARE  BLL1 64            EXTRA WORDS FOR NEW VARIABLES                TRUE
          VAL  64*0
*
          END
