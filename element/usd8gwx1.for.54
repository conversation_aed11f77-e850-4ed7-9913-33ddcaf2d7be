C'Title             Weather Data Base Management
C'Module_ID         GWX1
C'Entry_point       WX1
C'Application       DASH 8 Weather Radar Simulation
C'Author            <PERSON>'Date              20 Sept 91
C
C'System            Radar
C'Iteration_rate    266 milliseconds
C'Process           CPU0.S00
C
      SUBROUTINE USD8GWX1
C
      IMPLICIT NONE
C'Revision_History
C
C
C  References:
C
C   [1] CAE Software development standard, 18 June 1984
C       CD 130931-01-8-300, Rev. A, CAE
C
C   [2] Fortran-77, release 4.0 reference manual,
C       June 1983, GOULD.
C
C
C'Purpose
C
C       This module controls the I/O between the WXR and COMMON data bases
C       and updates the I/F weather pages. It also repositions the weather
C       front or cells upon request.
C'
C
C'Theory
C
C       This module monitors the I/F weather pages and acts upon any user
C       request. Depending upon the request, this module will read or write
C       to either of the data bases.
C'
C
C'Inputs
C
C       - I/F weather page parameters.
C       - WXR data base buffer.
C       - COMMON data base parameters.
C'
C
C'Outputs
C
C       - I/F weather page updated parameters.
C       - WXR data base updated buffer.
C       - COMMON data base updated parameters.
C'
C
C        ***********************************
C        *                                 *
C        *             VARIABLES           *
C        *                                 *
C        ***********************************
C
C
C'Data_Base_Variables
C
CQ    USD8 XRFTEST*
CB    GLOBAL00:GLOBAL05
C
C     Inputs
C     ------
C
CP    USD8 GIOCODE   ,   !  WXDB I/O error status
CP   -     GIOSTAT   ,   !  WXDB I/O status flag
CP   -     GVCREP    ,   !  Visual cloud flag (Current cloud have change)
CP   -     GVWREP    ,   !  Visual cloud flag (Weater front have change)
CP   -     TACACL    ,   !  I/F active clouds string
CP   -     TACALT    ,   !  I/F cloud altitude
CP   -     TACBEA    ,   !  I/F cloud bearing
CP   -     TACBOT    ,   !  I/F cloud bottom altitude
CP   -     TACLAT    ,   !  I/F cloud latitude
CP   -     TACLNT    ,   !  I/F cloud length
CP   -     TACLON    ,   !  I/F cloud longitude
CP   -     TACNBR    ,   !  I/F cloud number
CP   -     TACORI    ,   !  I/F cloud orientation
CP   -     TACRNG    ,   !  I/F cloud range
CP   -     TACRSP    ,   !  I/F cloud rotational speed
CP   -     TACSPD    ,   !  I/F cloud velocity
CP   -     TACTHK    ,   !  I/F cloud thickness
CP   -     TACTOP    ,   !  I/F cloud top altitude
CP   -     TACTRK    ,   !  I/F cloud track angle
CP   -     TACTYP    ,   !  I/F cloud type
CP   -     TACWDT    ,   !  I/F cloud width
CP   -     TAWDCY    ,   !  I/F weather front decay time
CP   -     TAWFTS    ,   !  I/F weather front store number
CP   -     TAWGRW    ,   !  I/F weather front growth time
CP   -     TAWLAT    ,   !  I/F weather front latitude
CP   -     TAWLON    ,   !  I/F weather front longitude
CP   -     TAWNBA    ,   !  I/F weather front number from page A
CP   -     TAWNBB    ,   !  I/F weather front number from page B
CP   -     TAWORI    ,   !  I/F weather front orientation
CP   -     TAWSUS    ,   !  I/F weather front sustain time
CP   -     TAWTRB    ,   !  I/F weather front turbulence level
CP   -     TCMACT    ,   !  I/F weather front activation flag
CP   -     TCMCAC    ,   !  I/F cloud activation flag
CP   -     TCMCDE    ,   !  I/F cloud deletion flag
CP   -     TCMDEL    ,   !  Remove weather front flag
CP   -     TCMEFX    ,   !  I/F weather front effects flag
CP   -     TCMFST    ,   !  I/F weather front store flag
CP   -     TCMWND    ,   !  I/F weather front wind flag
CP   -     TCMWREP   ,   !  WF reposition flag
CP   -     TAWNMAC   ,   !  WF reposition range
CP   -     TCMSTRM   ,   !  Visual storm activation button
CP   -     YISHIP    ,   !  Configuration control ship mnemonic
C
C     Outputs
C     -------
C
CP   -     GICACT    ,   !  Cloud activation flag
CP   -     GICALT    ,   !  Cloud altitude
CP   -     GICBEA    ,   !  Cloud bearing
CP   -     GICBOT    ,   !  Cloud bottom altitude
CP   -     GICLAT    ,   !  Cloud latitude
CP   -     GICLNT    ,   !  Cloud length
CP   -     GICLON    ,   !  Cloud longitude
CP   -     GICNBR    ,   !  Cloud number
CP   -     GICORI    ,   !  Cloud orientation
CP   -     GICRNG    ,   !  Cloud range
CP   -     GICRTS    ,   !  Cloud rotational speed
CP   -     GICSPD    ,   !  Cloud speed
CP   -     GICTHK    ,   !  Cloud thickness
CP   -     GICTOP    ,   !  Cloud top altitude
CP   -     GICTRK    ,   !  Cloud track angle
CP   -     GICTYP    ,   !  Cloud type
CP   -     GICWDT    ,   !  Cloud width
CP   -     GIOERROR  ,   !  WXDB I/O error flag
CP   -     GIOFILE   ,   !  WXDB I/O access flag
CP   -     GIOREAD   ,   !  WXDB I/O read request flag
CP   -     GIOWFNO   ,   !  WXDB I/O record number
CP   -     GIOWRITE  ,   !  WXDB I/O write request flag
CP   -     GWCACT    ,   !  WXDB cloud activation flag
CP   -     GWCALT    ,   !  WXDB cloud altitude
CP   -     GWCBEA    ,   !  WXDB cloud bearing
CP   -     GWCLAT    ,   !  WXDB cloud latitude
CP   -     GWCLNT    ,   !  WXDB cloud length
CP   -     GWCLON    ,   !  WXDB cloud longitude
CP   -     GWCORI    ,   !  WXDB cloud orientation
CP   -     GWCRNG    ,   !  WXDB cloud range
CP   -     GWCRTS    ,   !  WXDB cloud rotational speed
CP   -     GWCSPD    ,   !  WXDB cloud velocity
CP   -     GWCTHK    ,   !  WXDB cloud thickness
CP   -     GWCTRK    ,   !  WXDB cloud track angle
CP   -     GWCTYP    ,   !  WXDB cloud type
CP   -     GWCWDT    ,   !  WXDB cloud width
CP   -     GWFBLD    ,   !  Weather front buildup factor
CP   -     GWFDCY    ,   !  Weather front decay time
CP   -     GWFFON    ,   !  Weather front ON flag
CP   -     GWFGRW    ,   !  Weather front growth time
CP   -     GWFLAT    ,   !  Weather front latitude
CP   -     GWFLON    ,   !  Weather front longitude
CP   -     GWFNBR    ,   !  Weather front number
CP   -     GWFORI    ,   !  Weather front orientation
CP   -     GWFSUS    ,   !  Weather front sustain time
CP   -     GWFTIM    ,   !  Weather front active time
C
C  OPTION: REL    When repositioning with respect to A/C
C
CP   -     RUPLAT    ,   !  Aircraft latitude
CP   -     RUPLON    ,   !  Aircraft longitude
CP   -     VPSIDG    ,   !  Aircraft heading
C
C  OPTION: RWY    When repositioning with respect to Reference Runway
C
CP   -     RXMISGPX  ,   !  Reference runway touchdown
CP   -     RXMISHDG  ,   !  Reference runway heading
CP   -     RXMISLAT  ,   !  Reference runway latitude
CP   -     RXMISLON  ,   !  Reference runway longitude
CP   -     RXMISRLE  ,   !  Reference runway length
CP   -     RXMISELE      !  Reference runway elevation
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:45:01 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
     &, RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLON(5)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
C$
      REAL*4   
     &  GICBEA         ! CLOUD BEARING                          [Deg]
     &, GICLAT         ! CLOUD LATITUDE                         [Deg]
     &, GICLNT         ! CLOUD LENGTH                           [Nmi]
     &, GICLON         ! CLOUD LONGITUDE                        [Deg]
     &, GICORI         ! CLOUD ORIENTATION                      [Deg]
     &, GICRNG         ! CLOUD RANGE                            [Nmi]
     &, GICRTS         ! CLOUD ROTATIONAL SPEED                 [D/S]
     &, GICSPD         ! CLOUD SPEED                            [Kts]
     &, GICWDT         ! CLOUD WIDTH                            [Nmi]
     &, GWCBEA(20)     ! CLOUD BEARING WRT A/C                  [Deg]
     &, GWCLAT(20)     ! CLOUD LATITUDE                         [Deg]
     &, GWCLNT(20)     ! CLOUD LENGTH                           [Nmi]
     &, GWCLON(20)     ! CLOUD LONGITUDE                        [Deg]
     &, GWCORI(20)     ! CLOUD ORIENTATION                      [Deg]
     &, GWCRNG(20)     ! CLOUD RANGE WRT A/C                    [Nmi]
     &, GWCRTS(20)     ! CLOUD ROTATIONAL SPEED                 [D/S]
     &, GWCSPD(20)     ! CLOUD VELOCITY                         [Kts]
     &, GWCWDT(20)     ! CLOUD WIDTH                            [Nmi]
     &, GWFBLD         ! WEATHER FRONT BUILDUP FACTOR           [Sec]
     &, GWFDCY         ! WEATHER FRONT DECAY TIME               [Sec]
     &, GWFGRW         ! WEATHER FRONT GROWTH TIME              [Sec]
     &, GWFLAT         ! WEATHER FRONT CENTER LATITUDE          [Deg]
     &, GWFLON         ! WEATHER FRONT CENTER LONGITUDE         [Deg]
     &, GWFSUS         ! WEATHER FRONT SUSTAIN TIME             [Sec]
     &, GWFTIM         ! WEATHER FRONT BUILDUP TIME ELAPSED     [Sec]
     &, RXMISHDG(5)    !  7 TRUE HDG/MAG DECLINATION(DEG)      [DEG]
     &, TACBEA         ! WX CELL BEARING (+/-180 )           [Degs ]
     &, TACLAT         ! WX CELL LATITUDE (+/-90 )           [Degs ]
     &, TACLNT         ! WX CELL LENGTH (1-20)               [NMI  ]
     &, TACLON         ! WX CELL LONGITUDE (+/-180 )         [Degs ]
     &, TACORI         ! WX CELL ORIENTATION (+/-180 )       [Degs ]
      REAL*4   
     &  TACRNG         ! WX CELL RANGE (0-99999 )            [NMI  ]
     &, TACRSP         ! WX CELL ROT SPEED (+/-10 )       [Degs/sec]
     &, TACSPD         ! WX CELL SPEED (0-200 )              [Knots]
     &, TACWDT         ! WX CELL WIDTH (0.5-10.0 NMI)        [NMI  ]
     &, TAWDCY         ! WX FRONT DECAY TIME (0-10 )         [Hours]
     &, TAWGRW         ! WX FRONT GROWTH TIME (0-10)         [Hours]
     &, TAWLAT         ! WX FRONT LATITUDE (+/-90)           [Degs ]
     &, TAWLON         ! WX FRONT LONGITUDE (+/-180)         [Degs ]
     &, TAWNMAC        ! WX FRONT REPOS FRONT OF AC          [NMI  ]
     &, TAWORI         ! WX FRONT ORIENTATION (+/-180)       [Degs ]
     &, TAWSUS         ! WX FRONT SUSTAIN TIME (0-100 )      [Hours]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
C$
      INTEGER*4
     &  GICALT         ! CLOUD ALTITUDE ASL                      [Ft]
     &, GICBOT         ! CLOUD BOTTOM ALTITUDE                   [Ft]
     &, GICNBR         ! CLOUD NUMBER                           [N/A]
     &, GICTHK         ! CLOUD THICKNESS                         [Ft]
     &, GICTOP         ! CLOUD TOP ALTITUDE                      [Ft]
     &, GICTRK         ! CLOUD TRACK ANGLE                      [Deg]
     &, GICTYP         ! CLOUD TYPE                             [N/A]
     &, GIOCODE        ! WXDB I/O STATUS CODE                   [N/A]
     &, GIOWFNO        ! WXDB I/O RECORD NUMBER                 [N/A]
     &, GWCALT(20)     ! CLOUD ALTITUDE ABOVE SEA LEVEL          [Ft]
     &, GWCTHK(20)     ! CLOUD THICKNESS                         [Ft]
     &, GWCTRK(20)     ! CLOUD TRACK ANGLE                      [Deg]
     &, GWCTYP(20)     ! CLOUD TYPE                             [N/A]
     &, GWFNBR         ! WEATHER FRONT NUMBER                   [N/A]
     &, GWFORI         ! WEATHER FRONT ORIENTATION              [Deg]
     &, TACALT         ! WX CELL ALTITUDE (0-60000 )         [Feet ]
     &, TACBOT         ! WX CELL BOTTOM ALTITUDE (0-60000)   [Feet ]
     &, TACTHK         ! WX CELL THICKNESS (0-30000 )        [Feet ]
     &, TACTOP         ! WX CELL TOP ALTITUDE (0-90000 )     [Feet ]
     &, YISHIP         ! Ship name
C$
      INTEGER*2
     &  RXMISELE(5)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RXMISGPX(5)    ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RXMISRLE(5)    !  8 RUNWAY LENGTH (FEET)                [FT]
     &, TACNBR         ! WX CELL NUMBER (1-20)
     &, TACTRK         ! WX CELL TRACK ANGLE (0-360 )        [Degs ]
     &, TACTYP         ! WX CELL TYPE (1-6)
     &, TAWFTS         ! WX FRONT NUMBER TO STORE (1-20)
     &, TAWNBA         ! WX FRONT NUMBER PAGE A (0-20)
     &, TAWNBB         ! WX FRONT NUMBER PAGE B (0-20)
     &, TAWTRB         ! WX FRONT TURBULENCE (0-100%)
C$
      LOGICAL*1
     &  GICACT         ! CLOUD ACTIVATION STATUS                [N/A]
     &, GIOERROR       ! WXDB I/O ERROR FLAG                    [N/A]
     &, GIOFILE        ! WXDB OPEN FLAG                         [N/A]
     &, GIOREAD        ! WXDB READ REQUEST FLAG                 [N/A]
     &, GIOSTAT        ! WXDB I/O COMPLETION FLAG               [N/A]
     &, GIOWRITE       ! WXDB WRITE REQUEST FLAG                [N/A]
     &, GVCREP         ! VISUAL CLOUD REPOS FLAG                [N/A]
     &, GVWREP         ! VISUAL FRONT REPOS FLAG                [N/A]
     &, GWCACT(20)     ! CLOUD ACTIVATION STATUS                [N/A]
     &, GWFFON         ! WEATHER FRONT ON FLAG                  [N/A]
     &, TCMACT         ! WX FRONT ACTIVATE
     &, TCMCAC         ! WX CELL ACTIVATE
     &, TCMCDE         ! WX CELL DELETE
     &, TCMDEL         ! WX FRONT DELETION
     &, TCMEFX         ! WX FRONT AUDIO/VISUAL EFFECTS
     &, TCMFST         ! WX FRONT STORE
     &, TCMSTRM        ! STORM
     &, TCMWND         ! WX FRONT WIND EFFECTS
     &, TCMWREP        ! WX FRONT REPOS FLAG
C$
      INTEGER*1
     &  TACACL(60)     ! ACTIVE WX CELL STRING
C$
      LOGICAL*1
     &  DUM0000001(32),DUM0000002(17696),DUM0000003(20696)
     &, DUM0000004(38496),DUM0000005(30),DUM0000006(170)
     &, DUM0000007(229104),DUM0000008(9582),DUM0000009(91)
     &, DUM0000010(2),DUM0000011(1596),DUM0000012(80)
     &, DUM0000013(3),DUM0000014(3),DUM0000015(3),DUM0000016(146)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YISHIP,DUM0000002,VPSIDG,DUM0000003,RUPLAT
     &, RUPLON,DUM0000004,RXMISLAT,RXMISLON,RXMISELE,DUM0000005
     &, RXMISHDG,RXMISRLE,DUM0000006,RXMISGPX,DUM0000007,TCMSTRM
     &, DUM0000008,TCMEFX,TCMWND,TCMACT,TCMDEL,TCMCAC,TCMCDE
     &, TCMFST,TCMWREP,DUM0000009,TAWLAT,TAWLON,TAWORI,TAWGRW
     &, TAWSUS,TAWDCY,TAWTRB,TAWFTS,TAWNBA,TAWNBB,TACACL,TACNBR
     &, TACTYP,TACLNT,TACWDT,TACORI,TACRSP,TACSPD,TACTRK,DUM0000010
     &, TACLAT,TACLON,TACRNG,TACBEA,TACALT,TACTHK,TACBOT,TACTOP
     &, TAWNMAC,DUM0000011,GWFLAT,GWFLON,GWFORI,GWFGRW,GWFSUS
     &, GWFDCY,GWCTYP,GWCLNT,GWCWDT,GWCTHK,GWCORI,GWCRTS,GWCSPD
     &, GWCTRK,GWCLAT,GWCLON,GWCALT,GWCACT,DUM0000012,GWFNBR
     &, GWFFON,DUM0000013,GWFTIM,GWFBLD,GWCBEA,GWCRNG,GICNBR
     &, GICTYP,GICLNT,GICWDT,GICTHK,GICORI,GICRTS,GICSPD,GICTRK
     &, GICLAT,GICLON,GICRNG,GICBEA,GICALT,GICBOT,GICTOP,GICACT
     &, DUM0000014,GIOCODE,GIOFILE,GIOREAD,GIOWRITE,GIOSTAT,GIOERROR
     &, DUM0000015,GIOWFNO,DUM0000016,GVWREP,GVCREP    
C------------------------------------------------------------------------------
C
C'
C
C'IDENT
C
      CHARACTER*55
     &         REV
     &             /'$SOURCE:$'/
C'
C
C'Local_Variables
C
      LOGICAL*1 FIRST      ,  !  First pass flag
     &          FSTORE     ,  !  Store front flag
     &          NEWWF      ,  !  New weather front flag
     &          STORM      ,  !  Visual storm activation status
     &          TABLE(40)  ,  !  Active clouds table
     &          TOLD       ,  !  Take-off/landing fronts flag
     &          UPDATE1    ,  !  WF update flag
     &          UPDATE2    ,  !  WF update flag number 2
     &          LUSAR      ,  !  US Air configuration flag
     &          SPX        ,  !  Redifusion's SPX visual flag
     &          VIT        ,  !  MDAC's VITAL visual flag
     &          HVI        ,  !  Hitachi's HIVIS visual flag
     &          MILES      ,  !  Link Miles visual flag
     &          IBM        ,  !  IBM-based sims configuration flag
     &          VAX        ,  !  VAX-based sims configuration flag
     &          SEL           !  SEL-based sims configuration flag
C
      CHARACTER
     &          TABLES(40)    !
C
      EQUIVALENCE (TABLE(40), TABLES(40))
C
      INTEGER*4 I,J,K,M,N  ,  !  Index counters
     &          MAXCELL    ,  !  Maximum number of clouds
     &          TEMPVIS    ,  !
     &          VISTAB(21,3), !  Visual index table
     &          VISTYP     ,  !  Access WF wrt to table
     &          USD8          !
C
      REAL*4 BEAR          ,  !  Cloud bearing wrt WF center
     &       CHPI          ,  !  090:00:00 deg
     &       CHPIM         ,  !  089:59:59 deg
     &       CMAXANG       ,  !  179:59:59 deg
     &       CMAXLVL       ,  !  Max number of cloud levels
     &       CMINANG       ,  !  000:00:01 deg
     &       COSBEA        ,  !  Delta bearing cosine
     &       COSLAT        ,  !  Delta latitude cosine
     &       COSLAT1       ,  !  WF latitude cosine
     &       COSLAT2       ,  !  Cloud latitude cosine
     &       COSLON        ,  !  Delta longitude cosine
     &       COSRNG        ,  !  Range cosine
     &       CPI           ,  !  180:00:00 deg
     &       CR            ,  !  Earth's radius
     &       C2PI          ,  !  360:00:00 deg
     &       DEG2NMI       ,  !  DEG to NMI conversion factor
     &       DEG2RAD       ,  !  DEG to RAD conversion factor
     &       DELDCY        ,  !  Decay time change
     &       DELGRW        ,  !  Growth time change
     &       DELSUS        ,  !  Sustain time change
     &       DLAT          ,  !  Latitude displacement
     &       DLON          ,  !  Longitude displacement
     &       DLONABS       ,  !  Delta longitude absolute value
     &       DORI          ,  !  Orientation displacement
     &       FT2DEG        ,  !  Feet to Degre conversion factor
     &       ITERATION     ,  !  Iteration rate
     &       LOCDCY        ,  !  Decay time
     &       LOCGRW        ,  !  Growth time
     &       LOCSUS        ,  !  Sustain time
     &       NMI2FT        ,  !  Nautical Mille to Feet conversion factor
     &       NORCL         ,  !  Northern hemisphere cloud status
     &       RAD2DEG       ,  !  RAD to DEG conversion factor
     &       RANGE         ,  !  Cloud range wrt WF center
     &       SINLAT        ,  !  Delta latitude sine
     &       SINLAT1       ,  !  WF latitude sine
     &       SINLAT2       ,  !  Cloud latitude sine
     &       SINLON        ,  !  Delta longitude sine
     &       SINRNG        ,  !  Range sine
     &       TEMP          ,  !  Temporary variable
     &       TEMP1         ,  !  Temporary variable
     &       THETA1        ,  !  Temporary variable
     &       THETA2        ,  !  Temporary variable
     &       WFBEA         ,  !  Weather front repos bearing
     &       WFRNG         ,  !  Weather front repos range
     &       RDST          ,  !  TOLD reposition distance
     &       RHDG             !  TOLD reposition heading
C'
C
C'Constants
C
      PARAMETER( CHPI      = 90.0          , ! 090:00:00 deg
     &           CHPIM     = 89.99972222   , ! 089:59:59 deg
     &           CMAXANG   = 179.99972222  , ! 179:59:59 deg
     &           CMAXLVL   = 42.0          , ! Max number of cloud levels
     &           CMINANG   = 0.0002777778  , ! 000:00:01 deg
C
CIBM+
     &           USD8      = Z'55534438'   , !  US Air
CIBM-
C
CVAX++        ------- VAX Code -------
CVAX      &           USD8      = 'USD8'        , !  US Air
CVAX-            ------------------------
C
CSEL++        ------- SEL Code -------
CSEL      &           USD8      = 'USD8'        , !  US Air
CSEL-            ------------------------
C
     &           CPI       = 180.0         , ! 180:00:00 deg
     &           CR        = 3440.0        , ! Earth's radius
     &           C2PI      = 360.0         , ! 360:00:00 deg
     &           DEG2NMI   = 60.0          , ! DEG to NMI conv factor
     &           DEG2RAD   = 0.01745329252 , ! DEG to RAD conv factor
     &           FT2DEG    = 0.00000274298 , ! Conversion Ft to Deg
     &           ITERATION = 0.266         , ! Iteration rate
     &           MAXCELL   = 20            , ! Max number of clouds
     &           NMI2FT    = 6076.1        , ! Conversion Nmi to Ft
     &           RAD2DEG   = 57.2957795132 ) ! RAD to DEG conv factor
C'
C
C'Data
C
      DATA FIRST   /.TRUE./  ,               ! First pass flag
     &     FSTORE  /.FALSE./ ,               ! Store front flag
     &     M       /6/       ,               ! I/F page update counter
     &     NEWWF   /.TRUE./  ,               ! New weather front flag
     &     TABLES                            ! Active clouds table
     &             /' ','1', ' ','2', ' ','3', ' ','4', ' ','5'  ,
     &              ' ','6', ' ','7', ' ','8', ' ','9', '1','0'  ,
     &              '1','1', '1','2', '1','3', '1','4', '1','5'  ,
     &              '1','6', '1','7', '1','8', '1','9', '2','0'  /,
C
C 2ULD-PLEM Using the old Miles buffer for new CAE visual (31-38)
     &     VISTAB/ 31, 32, 33, 34, 35, 36, 37, 38, 9,10,  ! Link miles buff.
     &            11,12,13,14,15,16,17,18,19,20,21,
     &             6, 7, 8, 9,10, 1, 2, 3, 4, 5,    ! Rediffusion buff.
     &            11,12,13,14,15,16,17,18,19,20,21,
     &            11,12, 1, 2, 3, 4, 5, 6, 7, 8,    ! Vitall buff.
     &             9,10,13,14,15,16,17,18,19,20,21/,
C
     &     TOLD    /.FALSE./ ,               ! Take-off/landing fronts flag
     &     UPDATE1 /.FALSE./ , UPDATE2 /.FALSE./  ! WF update flag
C'
C
      ENTRY WX1
C
C  *************************
C  *                       *
C  *  DATABASE MANAGEMENT  *
C  *                       *
C  *************************
C
C
C -- First pass
C    ==========
C
C=GWX000
C
      IF (FIRST) THEN
         FIRST = .FALSE.
         LUSAR = (YISHIP .EQ.  USD8 )
C
         IBM   = LUSAR
         MILES = LUSAR
         VIT   = .FALSE.
         SPX   = .FALSE.
         VAX   = .FALSE.
         SEL   = .FALSE.
         STORM = .FALSE.
C
         IF (VIT) THEN
            VISTYP = 3
         ELSE IF (MILES) THEN
            VISTYP = 1
         ELSE
            VISTYP = 2
         ENDIF
C
         RETURN
      ENDIF
C
C
C -- Check I/O status flags
C    ======================
C
C=GWX010
C
      IF (SEL) THEN
         GIOERROR = GIOCODE .NE. 0
      ELSE
         GIOERROR = GIOCODE .NE. 1
      ENDIF
      GIOFILE  = (.NOT. GIOSTAT) .OR. GIOERROR
      IF (GIOFILE) RETURN
C
C
C -- Check if new weather system selected
C    ====================================
C
C=GWX020
C
C
      IF (TCMDEL) THEN
         TAWNBA = 0
         TCMDEL = .FALSE.
      ENDIF
C
      IF (TCMSTRM .XOR. STORM) THEN   ! Storm PB pressed
         STORM = TCMSTRM
         IF (STORM) THEN              ! PB active
            TAWNBA = 1
            GWFNBR = 0
         ELSE                         ! PB deactivated
            TAWNBA = 0
         ENDIF
      ENDIF
C
      IF (TAWNBA .NE. GWFNBR) THEN
         GWFNBR = TAWNBA
         TAWNBB = TAWNBA
         NEWWF  = .TRUE.
      ELSE IF (TAWNBB .NE. GWFNBR) THEN
         GWFNBR = TAWNBB
         TAWNBA = TAWNBB
         NEWWF  = .TRUE.
      ENDIF
C
      IF (NEWWF) THEN
C
         NEWWF     = .FALSE.
         TCMACT    = .FALSE.
         GWFTIM    =  0.0
         DO K=1,60
            TACACL(K) = TABLE(1)
         ENDDO
C
         IF (GWFNBR .GT. 0) THEN
            TCMWND  = .FALSE.
            TCMEFX  = .TRUE.
            TAWTRB  = 100
            TAWFTS  = GWFNBR
            TACNBR  = 1
            GICNBR  = 0
            TEMPVIS   = VISTAB(TAWNBA,VISTYP)
            GIOWFNO   = TEMPVIS                 !  Read I/O variables
            GIOSTAT = .FALSE.
            GIOREAD = .TRUE.
C
            IF (STORM .OR. MILES .OR. VIT
     &          .OR. (SPX .AND. (GWFNBR .GT. 3))) THEN
               TCMWREP = .TRUE.
               TAWNMAC = 50.0
            ELSE IF ((GWFNBR .LE. 3) .AND. (SPX)) THEN
               TOLD = .TRUE.
            ENDIF
C
            UPDATE1 = .TRUE.
            RETURN
         ENDIF
C
      ENDIF
C
C
C -- Check if valid weather system selected
C    ======================================
C
C=GWX030
C
      GWFFON = TCMACT .AND. (GWFNBR .GT. 0)
      IF (GWFNBR .EQ. 0) THEN
         TAWLAT    = 0.0
         TAWLON    = 0.0
         TAWORI    = 0.0
         TAWGRW    = 0.0
         TAWSUS    = 0.0
         TAWDCY    = 0.0
         TAWTRB    = 0
         TCMEFX    = .FALSE.
         TCMWND    = .FALSE.
         TCMACT    = .FALSE.
         TACNBR    = 1
         RETURN
      ENDIF
C
C
C -- Check if store weather system
C    =============================
C
C=GWX040
C
      IF (MILES .AND. (GWFNBR .LE. 5)) TCMFST = .FALSE.
      IF (SPX .AND. (GWFNBR .LE. 5) .OR. (GWFNBR .EQ. 21))
     &    TCMFST = .FALSE.
      IF (VIT .AND. (GWFNBR .LE. 2)) TCMFST = .FALSE.
      FSTORE = (.NOT. FSTORE) .AND. TCMFST
      IF (FSTORE) THEN
         TEMPVIS   = VISTAB(TAWNBA,VISTYP)
         GIOWFNO   = TEMPVIS
         GIOSTAT  = .FALSE.
         GIOWRITE = .TRUE.
         GIOFILE  = .TRUE.
         TCMFST   = .FALSE.
         RETURN
      ENDIF
C
C
C  **********************
C  *                    *
C  *  I/F PAGE UPDATES  *
C  *                    *
C  **********************
C
C
C -- Update GSD timers
C    =================
C
C=GWX050
C
      IF (UPDATE1) THEN
         GWFGRW  = 0.0
         GWFSUS  = 36000.0
         GWFDCY  = 0.0
         TAWGRW  = GWFGRW
         TAWSUS  = GWFSUS
         TAWDCY  = GWFDCY
         LOCGRW  = TAWGRW
         LOCSUS  = LOCGRW + TAWSUS
         LOCDCY  = LOCSUS + TAWDCY
      ENDIF
C
      IF (GWFGRW .NE. TAWGRW) THEN
         DELGRW  = TAWGRW - GWFGRW
         LOCGRW  = LOCGRW + DELGRW
         LOCSUS  = LOCSUS + DELGRW
         LOCDCY  = LOCDCY + DELGRW
      ELSE IF (GWFSUS .NE. TAWSUS) THEN
         DELSUS  = TAWSUS - GWFSUS
         LOCSUS  = LOCSUS + DELSUS
         LOCDCY  = LOCDCY + DELSUS
      ELSE IF (GWFDCY .NE. TAWDCY) THEN
         DELDCY  = TAWDCY - GWFDCY
         LOCDCY  = LOCDCY + DELDCY
      ENDIF
C
      IF (GWFTIM .LE. LOCGRW) THEN
         GWFGRW  = LOCGRW - GWFTIM
         TAWGRW  = GWFGRW
         GWFSUS  = LOCSUS - LOCGRW
         TAWSUS  = GWFSUS
         GWFDCY  = LOCDCY - LOCSUS
         TAWDCY  = GWFDCY
         IF (LOCGRW .GT. 0.00) THEN
            GWFBLD = INT((LOCGRW-GWFTIM)*CMAXLVL/LOCGRW)
         ELSE
            GWFBLD = 0
         ENDIF
      ELSE IF (GWFTIM .LE. LOCSUS) THEN
         GWFGRW = 0.0
         TAWGRW = 0.0
         GWFSUS = LOCSUS - GWFTIM
         TAWSUS = GWFSUS
         GWFDCY = LOCDCY - LOCSUS
         TAWDCY = GWFDCY
         GWFBLD = 0
      ELSE IF (GWFTIM .LE. LOCDCY) THEN
         GWFGRW = 0.0
         TAWGRW = 0.0
         GWFSUS = 0.0
         TAWSUS = 0.0
         GWFDCY = LOCDCY - GWFTIM
         TAWDCY = GWFDCY
         IF (LOCDCY .GT. 0.00) THEN
            GWFBLD = INT((GWFTIM-LOCSUS)*CMAXLVL/(LOCDCY-LOCSUS))
         ELSE
            GWFBLD = 0
         ENDIF
      ELSE
         GWFGRW = 0.0
         TAWGRW = 0.0
         GWFSUS = 0.0
         TAWSUS = 0.0
         GWFDCY = 0.0
         TAWDCY = 0.0
         TCMACT = .FALSE.
      ENDIF
C
C
C -- Active weather cells display
C    ============================
C
C=GWX060
C
      IF (UPDATE1) THEN
         J = 1
         DO K=1,MAXCELL
            IF (GWCACT(K)) THEN
               TACACL(J)   = TABLE(2*K-1)
               TACACL(J+1) = TABLE(2*K)
               J = J+3
            ENDIF
         ENDDO
         DO K=J,60
            TACACL(K) = TABLE(1)
         ENDDO
      ENDIF
C
C
C -- Weather system relative position
C    ================================
C
C=GWX070
C
      IF (UPDATE1) THEN
         TAWLAT  = GWFLAT
         TAWLON  = GWFLON
         TAWORI  = GWFORI
C
      ELSE IF (TOLD) THEN
         TOLD    = .FALSE.
         IF (GWFNBR .EQ. 2) THEN
            RDST = 3.0/DEG2NMI
            RHDG = RXMISHDG(3)*DEG2RAD
         ELSE
            RDST = (RXMISRLE(3)-RXMISGPX(3)*****NMI2FT)*FT2DEG
            RHDG = (RXMISHDG(3)-180.0)*DEG2RAD
         ENDIF
         THETA1 = COS(RXMISLAT(3)*DEG2RAD)
         TAWLAT = RXMISLAT(3) + RDST*COS(RHDG)
         TAWLON = RXMISLON(3) + RDST*SIN(RHDG)/THETA1
         GWCALT(1) = RXMISELE(3) + GWCALT(1)
         IF (RXMISHDG(3) .GT. CPI) THEN
            TAWORI = INT(RXMISHDG(3) - C2PI)
         ELSE
            TAWORI = INT(RXMISHDG(3))
         ENDIF
C
      ELSE IF (TCMWREP) THEN
         TCMWREP = .FALSE.
         WFRNG   = TAWNMAC
         WFBEA   = VPSIDG
         TAWORI  = INT(VPSIDG)
         THETA1  = (CHPI - SNGL(RUPLAT))*DEG2RAD
         SINLAT1 = SIN(THETA1)
         COSLAT1 = COS(THETA1)
         COSRNG  = COS(WFRNG*DEG2RAD/DEG2NMI)
         SINRNG  = SIN(WFRNG*DEG2RAD/DEG2NMI)
         COSBEA  = COS(WFBEA*DEG2RAD)
         COSLAT2 = COSRNG*COSLAT1 + SINRNG*SINLAT1*COSBEA
         IF (COSLAT2 .GE. 1.0000) THEN
            TAWLAT = CHPI
            TAWLON = SNGL(RUPLON)
         ELSE IF (COSLAT2 .LE. -1.0000) THEN
            TAWLAT = -CHPI
            TAWLON = SNGL(RUPLON)
         ELSE
            SINLAT2   = SQRT(1.0-COSLAT2**2)
            TAWLAT = CHPI - ACOS(COSLAT2)*RAD2DEG
            IF (SINLAT2 .LE. 1.0E-6) THEN
               TAWLON = SNGL(RUPLON)
            ELSE
               COSLON = (COSRNG*SINLAT1-COSLAT1*SINRNG*COSBEA)/SINLAT2
               IF (COSLON .GT. 1.000) THEN
                  COSLON = 1.000
               ELSE IF (COSLON .LT. -1.000) THEN
                  COSLON = -1.000
               ENDIF
               TEMP   = ACOS(COSLON)*RAD2DEG
               TAWLON = SNGL(RUPLON) + SIGN(TEMP,WFBEA)
            ENDIF
         ENDIF
         IF (STORM) TCMACT = .TRUE.
      ENDIF
C
C
C -- Weather system absolute reposition
C    ==================================
C
C=GWX080
C
C
C    Latitude Reposition
C    ===================
C
C
      IF (TAWLAT .NE. GWFLAT) THEN
         DLAT = TAWLAT - GWFLAT
         GWFLAT = TAWLAT
         UPDATE2 = .TRUE.
         IF (ABS(DLAT) .GE. CMINANG) THEN
C
            DO J=1,MAXCELL
               IF (GWCACT(J)) THEN
C
                  DLON = GWCLON(J) - GWFLON
                  IF (DLON .GT. CPI) THEN
                     DLON = DLON - C2PI
                  ELSE IF (DLON .LE. -CPI) THEN
                     DLON = DLON + C2PI
                  ENDIF
C
                  DLONABS  = ABS(DLON)
                  THETA1   = CHPI - ABS(GWCLAT(J))
C
                  IF (DLONABS .LE. CMINANG) THEN
                     GWCLAT(J) = GWCLAT(J) + DLAT
                  ELSE IF (DLONABS .GE. CMAXANG) THEN
                     GWCLAT(J) = GWCLAT(J) - DLAT
                  ELSE IF (THETA1 .LE. CMINANG) THEN
                     GWCLAT(J) = CHPI - DLAT
                     GWCLON(J) = GWFLON
                  ELSE
                     IF (THETA1 .GE. CHPIM) THEN
                        THETA2 = SIGN (-CHPI,(DLONABS-CHPI))
                     ELSE
                        NORCL    = SIGN(1.0,GWCLAT(J))
                        TEMP     = CHPI * (1.0 - NORCL)
                        TEMP1    = ATAN( TAN(THETA1*DEG2RAD)*
     &                                  COS(DLONABS*DEG2RAD) )*RAD2DEG
                        THETA2   = SIGN(TEMP,TEMP1)+NORCL*TEMP1
C
                     ENDIF
C
                     THETA2 = THETA2 - DLAT
C
                     IF (THETA2 .GT. CPI) THEN
                        THETA2 = THETA2 - C2PI
                     ELSE IF (THETA2 .LE. -CPI) THEN
                        THETA2 = THETA2 + C2PI
                     ENDIF
C
                     TEMP1 = SIN(DLON*DEG2RAD) * COS(GWCLAT(J)*DEG2RAD)
                     TEMP  = COS(THETA2*DEG2RAD) * SQRT(1.0-TEMP1**2)
C
                     GWCLAT(J) = CHPI - ACOS(TEMP)*RAD2DEG
C
                     TEMP1 = TEMP1/COS(GWCLAT(J)*DEG2RAD)
                     DLON  = ASIN(TEMP1)*RAD2DEG
C
                     IF (THETA2 .LT. 0.00) THEN
                        DLON = CPI - DLON
                        IF (DLON .GT. CPI) DLON = DLON - C2PI
                     ENDIF
                     THETA1 = GWFLON - GWCLON(J) - DLON
                     GWCLON(J) = GWFLON + DLON
                     GWCORI(J) = GWCORI(J) + THETA1
C
                  ENDIF
C
                  IF (GWCLAT(J) .GT. CHPI) THEN
                     GWCLAT(J) = CPI - GWCLAT(J)
                     GWCLON(J) = GWCLON(J) + CPI
                     IF (GWCLON(J) .GT. CPI)
     &                    GWCLON(J) = GWCLON(J) - C2PI
                  ELSE IF (GWCLAT(J) .LT. -CHPI) THEN
                     GWCLAT(J) = -CPI - GWCLAT(J)
                     GWCLON(J) = GWCLON(J) - CPI
                     IF (GWCLON(J) .LE. -CPI)
     &                    GWCLON(J) = GWCLON(J) + C2PI
                  ENDIF
               ENDIF
            ENDDO
C
            GVWREP = .TRUE.
C
         ENDIF
C
C
C    Longitude Reposition
C    ====================
C
C
      ELSEIF (TAWLON .NE. GWFLON) THEN
         DLON = TAWLON - GWFLON
         GWFLON = TAWLON
         UPDATE2 = .TRUE.
C
         IF (ABS(DLON) .GE. CMINANG) THEN
            DO J=1,MAXCELL
               IF (GWCACT(J)) THEN
                  GWCLON(J) = GWCLON(J) + DLON
                  IF (GWCLON(J) .GE. CPI) THEN
                     GWCLON(J) = GWCLON(J) - C2PI
                  ELSE IF (GWCLON(J) .LT. -CPI) THEN
                     GWCLON(J) = GWCLON(J) + C2PI
                  ENDIF
               ENDIF
            ENDDO
            GVWREP = .TRUE.
C
         ENDIF
C
C
C    Orientation Reposition
C    ======================
C
C
      ELSEIF (TAWORI .NE. GWFORI) THEN
         DORI = TAWORI - GWFORI
         UPDATE2 = .TRUE.
C
         IF (ABS(DORI) .GT. CMINANG) THEN
C
            THETA1 = (CHPI - GWFLAT) * DEG2RAD
            IF (GWFLAT .GT. CHPIM) THEN
               DO J=1,MAXCELL
                  IF (GWCACT(J))
     &               GWCLON(J) = GWCLON(J) + DORI
               ENDDO
C
            ELSE IF (GWFLAT .LE. -CHPIM) THEN
               DO J=1,MAXCELL
                  IF (GWCACT(J))
     &               GWCLON(J) = GWCLON(J) - DORI
               ENDDO
C
            ELSE
C
               COSLAT1 = COS(THETA1)
               SINLAT1 = SIN(THETA1)
C
               DO J=1,MAXCELL
C
                  IF (GWCACT(J)) THEN
C
                     THETA2 = (CHPI - GWCLAT(J)) * DEG2RAD
                     COSLAT = COS(THETA2)
                     SINLAT = SIN(THETA2)
C
                     DLON = GWCLON(J) - GWFLON
                     IF (DLON .GT. CPI) THEN
                        DLON = DLON - C2PI
                     ELSE IF (DLON .LE. -CPI) THEN
                        DLON = DLON + C2PI
                     ENDIF
C
                     IF (ABS(DLON) .LE. CMINANG) THEN
C
                        DLAT    = ABS(GWFLAT-GWCLAT(J))*DEG2RAD
                        SINRNG  = SIN(DLAT)
                        COSRNG  = COS(DLAT)
                        IF (GWFLAT .GT. GWCLAT(J)) THEN
                           BEAR = CPI
                        ELSE
                           BEAR = 0.0
                        ENDIF
C
                     ELSE IF (ABS(DLON) .GE. CMAXANG) THEN
C
                        DLAT    = (CPI - ABS(GWFLAT+GWCLAT(J)))*DEG2RAD
                        SINRNG  = SIN(DLAT)
                        COSRNG  = COS(DLAT)
                        IF (GWFLAT .GT. -GWCLAT(J)) THEN
                           BEAR = 0.0
                        ELSE
                           BEAR = CPI
                        ENDIF
C
                     ELSE
C
                        COSLON = COS(DLON*DEG2RAD)
C
                        COSRNG = SINLAT1*SINLAT*COSLON +
     &                           COSLAT1*COSLAT
                        TEMP   = SINLAT1*COSLAT -
     &                           SINLAT*COSLAT1*COSLON
C
                        IF (ABS(COSRNG) .GT. 1.00000) THEN
                           SINRNG = 0.0
                           BEAR   = 0.0
                        ELSE
                           SINRNG = SQRT(1.0-COSRNG**2)
                           IF (SINRNG .LE. 1.0E-6) THEN
                              BEAR = 0.0
                           ELSE IF (TEMP/SINRNG .GT. 1.000) THEN
                              BEAR = 0.0
                           ELSE IF (TEMP/SINRNG .LT. -1.000) THEN
                              BEAR = CPI
                           ELSE
                              BEAR = ACOS(TEMP/SINRNG)*RAD2DEG
                           ENDIF
                        ENDIF
                     ENDIF
C
                     IF (DLON .GE. 0.0) THEN
                        BEAR = DORI + BEAR
                     ELSE
                        BEAR = DORI - BEAR
                     ENDIF
C
                     IF (BEAR .GT. CPI) THEN
                        BEAR = BEAR - C2PI
                     ELSE IF (BEAR .LE. -CPI) THEN
                        BEAR = BEAR + C2PI
                     ENDIF
C
                     IF (BEAR .GE. 0.0) THEN
                        NORCL = 1.0
                     ELSE
                        NORCL = -1.0
                     ENDIF
C
                     BEAR = BEAR*DEG2RAD
                     COSLAT = COSRNG*COSLAT1 + COS(BEAR)*SINLAT1*SINRNG
C
                     IF (COSLAT .GT. 1.0000) THEN
                        COSLAT = 1.0000
                        THETA2 = 0.0
                     ELSE IF (COSLAT .LT. -1.0000) THEN
                        COSLAT = -1.0000
                        THETA2 = CPI
                     ELSE
                        THETA2 = ACOS(COSLAT)*RAD2DEG
                     ENDIF
C
                     GWCLAT(J) = CHPI - THETA2
C
                     IF (ABS(GWCLAT(J)) .LT. CHPIM) THEN
                        TEMP  = COSRNG*SINLAT1 -
     &                          COSLAT1*SINRNG*COS(BEAR)
                        TEMP1 = SQRT(1.0-COSLAT**2)
                        IF (TEMP1 .LT. 1.0E-6) THEN
                           DLON = 0.0
                        ELSE IF (TEMP/TEMP1 .GT. 1.0000) THEN
                           DLON = 0.0
                        ELSE IF (TEMP/TEMP1 .LT. -1.0000) THEN
                           DLON = CPI
                        ELSE
                           DLON = ACOS(TEMP/TEMP1)*RAD2DEG
                        ENDIF
                        GWCLON(J) = GWFLON + NORCL*DLON
                     ELSE
                        GWCLON(J) = GWFLON
                     ENDIF
C
                     IF (GWCLON(J) .GT. CPI) THEN
                        GWCLON(J) = GWCLON(J) - C2PI
                     ELSE IF (GWCLON(J) .LE. -CPI) THEN
                        GWCLON(J) = GWCLON(J) + C2PI
                     ENDIF
                     GWCORI(J) = GWCORI(J) + DORI
C
                     IF (GWCORI(J) .GT. CPI) THEN
                        GWCORI(J) = GWCORI(J) - C2PI
                     ELSE IF (GWCORI(J) .LE. -CPI) THEN
                        GWCORI(J) = GWCORI(J) + C2PI
                     ENDIF
                  ENDIF
               ENDDO
            ENDIF
C
            GWFORI = TAWORI
            TAWORI = GWFORI
            GVWREP = .TRUE.
C
         ENDIF
C
      ELSE
         UPDATE1 = UPDATE2
         UPDATE2 = .FALSE.
      ENDIF
C
C
C -- Weather cells range and bearing
C    ===============================
C
C=GWX090
C
      IF (UPDATE1) THEN
         UPDATE1 = .FALSE.
         THETA1  = (CHPI - SNGL(RUPLAT))*DEG2RAD
         SINLAT1 = SIN(THETA1)
         COSLAT1 = COS(THETA1)
         DO J=1,MAXCELL
            IF (GWCACT(J)) THEN
               THETA2    = (CHPI - GWCLAT(J))   *DEG2RAD
               SINLAT2   = SIN(THETA2)
               COSLAT2   = COS(THETA2)
               DLON      = GWCLON(J) - SNGL(RUPLON)
               IF (DLON .GT. CPI) THEN
                  DLON = DLON - C2PI
               ELSE IF (DLON .LE. -CPI) THEN
                  DLON = DLON + C2PI
               ENDIF
               COSLON = COS(DLON*DEG2RAD)
               COSRNG = COSLAT1*COSLAT2
     &                + SINLAT1*SINLAT2*COSLON
               IF (COSRNG .GE. 1.000) THEN
                  GWCRNG(J) = 0.00
                  GWCBEA(J) = 0.00
               ELSE IF (COSRNG .LE. -1.0000) THEN
                  GWCRNG(J) = CR
                  GWCBEA(J) = 0.0
               ELSE
                  GWCRNG(J) = ACOS(COSRNG)*RAD2DEG*DEG2NMI
                  SINRNG    = SQRT(1.00-COSRNG**2)
                  IF (SINRNG .LE. 1.0E-6) THEN
                     GWCBEA(J) = 0.0
                  ELSE
                     COSBEA    = (COSLAT2*SINLAT1 -
     &                            SINLAT2*COSLAT1*COSLON)/SINRNG
                     IF (COSBEA .GE. 1.0000) THEN
                        GWCBEA(J) = 0.0
                     ELSE IF (COSBEA .LE. -1.0000) THEN
                        GWCBEA(J) = CPI
                     ELSE
                        GWCBEA(J) = ACOS(COSBEA)*SIGN(1.0,DLON)
     &                               *RAD2DEG
                     ENDIF
                  ENDIF
               ENDIF
            ENDIF
         ENDDO
      ENDIF
C
C
C -- Weather system activation status
C    ================================
C
C=GWX100
C
C
      GWFFON = TCMACT
C
C
C  *************************
C  *                       *
C  *   SUBBANDED UPDATES   *
C  *                       *
C  *************************
C
C
C -- Weather cells parameter update
C    ==============================
C
C=GWX200
C
      IF (M .LT. 6) THEN
C
         M = M + 1
C
      ELSE
C
         M = 1
         IF (TACNBR .NE. GICNBR) THEN
            I      = TACNBR
            GICNBR = TACNBR
            TACTYP = GWCTYP(I)
            TACLNT = GWCLNT(I)
            TACWDT = GWCWDT(I)
            TACTHK = GWCTHK(I)
            TACORI = GWCORI(I)
            TACRSP = GWCRTS(I)
            TACSPD = GWCSPD(I)
            TACTRK = GWCTRK(I)
            TACLAT = GWCLAT(I)
            TACLON = GWCLON(I)
            TACALT = GWCALT(I)
            TACBOT = GWCALT(I)
            TACTOP = GWCALT(I) + GWCTHK(I)
            TCMCAC = GWCACT(I)
            TACBEA = GWCBEA(I) - VPSIDG
            IF (TACBEA .GT. CPI) THEN
               TACBEA = TACBEA - C2PI
            ELSE IF (TACBEA .LT. -CPI) THEN
               TACBEA = TACBEA + C2PI
            ENDIF
            TACRNG = GWCRNG(I)
            GICTYP = TACTYP
            GICLNT = TACLNT
            GICWDT = TACWDT
            GICTHK = TACTHK
            GICORI = TACORI
            GICRTS = TACRSP
            GICSPD = TACSPD
            GICTRK = TACTRK
            GICLAT = TACLAT
            GICLON = TACLON
            GICALT = TACALT
            GICBOT = TACBOT
            GICTOP = TACTOP
            GICACT = TCMCAC
            GICBEA = TACBEA
            GICRNG = TACRNG
         ENDIF
C
C
C -- Weather cells absolute reposition
C    =================================
C
C=GWX210
C
         IF ((ABS(GICLAT-TACLAT) .GE. 0.01) .OR.
     &       (ABS(GICLON-TACLON) .GE. 0.01)) THEN
C
            GICLAT    = TACLAT
            GWCLAT(I) = GICLAT
            GICLON    = TACLON
            GWCLON(I) = GICLON
            THETA1    = (CHPI - SNGL(RUPLAT))*DEG2RAD
            THETA2    = (CHPI - GWCLAT(I))   *DEG2RAD
            DLON      = GWCLON(I) - SNGL(RUPLON)
            SINLAT1   = SIN(THETA1)
            COSLAT1   = COS(THETA1)
            SINLAT2   = SIN(THETA2)
            COSLAT2   = COS(THETA2)
            IF (DLON .GT. CPI) THEN
               DLON = DLON - C2PI
            ELSE IF (DLON .LE. -CPI) THEN
               DLON = DLON + C2PI
            ENDIF
            COSLON = COS(DLON*DEG2RAD)
            COSRNG    = COSLAT1*COSLAT2 +
     &                  SINLAT1*SINLAT2*COSLON
            IF (COSRNG .GE. 1.000) THEN
               GWCRNG(I) = 0.00
               GWCBEA(I) = 0.00
            ELSE IF (COSRNG .LE. -1.0000) THEN
               GWCRNG(I) = CR
               GWCBEA(I) = 0.0
            ELSE
               GWCRNG(I) = ACOS(COSRNG)*RAD2DEG*DEG2NMI
               SINRNG    = SQRT(1.00-COSRNG**2)
               IF (SINRNG .LE. 1.0E-6) THEN
                  GWCBEA(I) = 0.0
               ELSE
                  COSBEA    = (COSLAT2*SINLAT1 -
     &                         SINLAT2*COSLAT1*COSLON)/SINRNG
                  IF (COSBEA .GE. 1.0000) THEN
                     GWCBEA(I) = 0.0
                  ELSE IF (COSBEA .LE. -1.0000) THEN
                     GWCBEA(I) = CPI
                  ELSE
                     GWCBEA(I) = ACOS(COSBEA) * SIGN(1.0,DLON) * RAD2DEG
                  ENDIF
               ENDIF
            ENDIF
            GICRNG = GWCRNG(I)
            TACRNG = GICRNG
            GICBEA = GWCBEA(I) - VPSIDG
            IF (GICBEA .GT. CPI) THEN
               GICBEA = GICBEA - C2PI
            ELSE IF (GICBEA .LT. -CPI) THEN
               GICBEA = GICBEA + C2PI
            ENDIF
            TACBEA = GICBEA
            GVCREP = .TRUE.
C
C
C -- Weather cells relative reposition
C    =================================
C
C=GWX220
C
         ELSE IF ((ABS(GICRNG-TACRNG) .GE. 0.01) .OR.
     &            (ABS(GICBEA-TACBEA) .GE. 0.01)) THEN
C
            GICRNG    = TACRNG
            GWCRNG(I) = GICRNG
            GICBEA    = TACBEA
            GWCBEA(I) = GICBEA + VPSIDG
            IF (GWCBEA(I) .GT. CPI) THEN
               GWCBEA(I) = GWCBEA(I) - C2PI
            ELSE IF (GWCBEA(I) .LT. -CPI) THEN
               GWCBEA(I) = GWCBEA(I) + C2PI
            ENDIF
            THETA1    = (CHPI - SNGL(RUPLAT))*DEG2RAD
            SINLAT1   = SIN(THETA1)
            COSLAT1   = COS(THETA1)
            COSRNG    = COS(GWCRNG(I)*DEG2RAD/DEG2NMI)
            SINRNG    = SIN(GWCRNG(I)*DEG2RAD/DEG2NMI)
            COSBEA    = COS(GWCBEA(I)*DEG2RAD)
            COSLAT2   = COSRNG*COSLAT1 +
     &                  SINRNG*SINLAT1*COSBEA
C
            IF (COSLAT2 .GE. 1.0000) THEN
               GWCLAT(I) = CHPI
               GWCLON(I) = SNGL(RUPLON)
            ELSE IF (COSLAT2 .LE. -1.0000) THEN
               GWCLAT(I) = -CHPI
               GWCLON(I) = SNGL(RUPLON)
            ELSE
               SINLAT2   = SQRT(1.0-COSLAT2**2)
               GWCLAT(I) = CHPI - ACOS(COSLAT2)*RAD2DEG
               IF (SINLAT2 .LE. 1.0E-6) THEN
                  GWCLON(I) = SNGL(RUPLON)
               ELSE
                  COSLON = (COSRNG*SINLAT1 - COSLAT1*SINRNG*COSBEA)
     &                     /SINLAT2
                  IF (COSLON .GT. 1.0000) THEN
                     COSLON = 1.0000
                  ELSE IF (COSLON .LT. -1.0000) THEN
                     COSLON = -1.0000
                  ENDIF
                  TEMP   = ACOS(COSLON)*RAD2DEG
                  GWCLON(I) = SNGL(RUPLON) + SIGN(TEMP,GWCBEA(I))
               ENDIF
            ENDIF
            GICLAT    = GWCLAT(I)
            TACLAT    = GICLAT
            GICLON    = GWCLON(I)
            TACLON    = GICLON
            GVCREP    = .TRUE.
C
         ELSE
C
            GICRNG    = GWCRNG(I)
            TACRNG    = GICRNG
            GICBEA = GWCBEA(I) - VPSIDG
            IF (GICBEA .GT. CPI) THEN
               GICBEA = GICBEA - C2PI
            ELSE IF (GICBEA .LT. -CPI) THEN
               GICBEA = GICBEA + C2PI
            ENDIF
            TACBEA    = GICBEA
            GICLAT    = GWCLAT(I)
            TACLAT    = GICLAT
            GICLON    = GWCLON(I)
            TACLON    = GICLON
C
         ENDIF
C
C
C -- Weather cells static parameters
C    ===============================
C
C=GWX230
C
         IF (TACTYP .NE. GICTYP) THEN
            GICTYP    = TACTYP
            GWCTYP(I) = GICTYP
         ELSE IF (TACLNT .NE. GICLNT) THEN
            GICLNT    = TACLNT
            GWCLNT(I) = GICLNT
         ELSE IF (TACWDT .NE. GICWDT) THEN
            GICWDT    = TACWDT
            GWCWDT(I) = GICWDT
         ELSE IF (TACALT .NE. GICALT) THEN
            GICALT    = TACALT
            GICBOT    = GICALT
            GICTOP    = GICALT + GICTHK
            TACBOT    = GICBOT
            TACTOP    = GICTOP
            GWCALT(I) = GICALT
         ELSE IF (TACTHK .NE. GICTHK) THEN
            IF (TACTHK .LT. 1000.0) TACTHK=1000.0
            GICTHK    = TACTHK
            GICTOP    = GICALT + GICTHK
            TACTOP    = GICTOP
            GWCTHK(I) = GICTHK
         ELSE IF (TACBOT .NE. GICBOT) THEN
            GICTHK    = GICTOP - TACBOT
            IF (GICTHK .LT. 1000.0) THEN
               GICTHK = 1000.0
               TACBOT = GICTOP - GICTHK
            ENDIF
            GICBOT    = TACBOT
            GICALT    = GICBOT
            TACALT    = GICALT
            TACTHK    = GICTHK
            GWCALT(I) = GICALT
            GWCTHK(I) = GICTHK
         ELSE IF (TACTOP .NE. GICTOP) THEN
            GICTHK    = TACTOP - GICBOT
            IF (GICTHK .LT. 1000.0) THEN
               GICTHK = 1000.0
               TACTOP = GICBOT + GICTHK
            ENDIF
            GICTOP    = TACTOP
            TACTHK    = GICTHK
            GWCTHK(I) = GICTHK
         ELSE IF (TACRSP .NE. GICRTS) THEN
            GICRTS    = TACRSP
            GWCRTS(I) = GICRTS
         ELSE IF (TACSPD .NE. GICSPD) THEN
            GICSPD    = TACSPD
            GWCSPD(I) = GICSPD
         ELSE IF (TACTRK .NE. GICTRK) THEN
            GICTRK    = TACTRK
            GWCTRK(I) = GICTRK
         ELSE IF ( (TCMCAC .AND. .NOT. GICACT) .OR.
     &             (TCMCDE .AND. GICACT) ) THEN
            GICACT    = TCMCAC
            GWCACT(I) = GICACT
            GVCREP = .TRUE.
            TCMCAC = .FALSE.
            TCMCDE = .FALSE.
            J = 1
            DO K=1,MAXCELL
               IF (GWCACT(K)) THEN
                  TACACL(J)   = TABLE(2*K-1)
                  TACACL(J+1) = TABLE(2*K)
                  J = J+3
               ENDIF
            ENDDO
            DO K=J,60
               TACACL(K) = TABLE(1)
            ENDDO
         ELSE IF ((TCMCAC .AND. GICACT) .OR.
     &           (TCMCDE .AND. .NOT. GICACT)) THEN
            TCMCAC = .FALSE.
            TCMCDE = .FALSE.
         ENDIF
C
      ENDIF
C
C
C -- Weather cells dynamic parameters
C    ================================
C
C=GWX240
C
      IF (TACORI .NE. GICORI) THEN
         GICORI    = TACORI
         GWCORI(I) = GICORI
      ELSE
         IF (GWCORI(I) .GT. CPI) THEN
            GWCORI(I) = GWCORI(I) - C2PI
         ELSE IF (GWCORI(I) .LT. -CPI) THEN
            GWCORI(I) = GWCORI(I) + C2PI
         ENDIF
         GICORI = GWCORI(I)
         TACORI = GICORI
      ENDIF
C
C
C -- Weather system activation timer
C    ===============================
C
C=GWX250
C
      IF (TCMACT) THEN
         GWFTIM  = GWFTIM + ITERATION
      ENDIF
C
C
      RETURN
      END
