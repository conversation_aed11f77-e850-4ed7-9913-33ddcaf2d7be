C'TitleVOR,ILS and DME RECEIVERS
C'Module_IDUSD8RB
C'DocumentationT.B.D
C'CustomerUSAIR
C'ApplicationSimulation of VOR, ILS and DME receivers
C'Author<PERSON><PERSON><PERSON>
C'DateDecember 1991
C
C'SystemRadio Aids
C'Iteration_rate133 msec
C'ProcessSynchronous process
C
C'Compilation_directives
C
C
      SUBROUTINE USD8RB
C     -----------------
C
C
C'Revision_History
C
C  usd8rb.for.7  6Nov2018 17:18 usd8 Tom
C       < working on UNS in -100 config >
C
C  usd8rb.for.6 19Mar1996 09:55 usd8 DES
C       < S81-1-114 Modification >
C
C  usd8rb.for.5 31Jan1996 04:11 usd8 Tom
C       < COA S81-1-104 RNAV/VOR ident fix >
C
C  usd8rb.for.4 29Sep1993 00:46 usd8 Tom
C       < COA S81-1-038 & 039 to fix SPR9030 declination problem. >
C
C  usd8rb.for.3 14Aug1993 03:30 usd8 S.GOULD
C       < ADDITIONAL CHANGES FROM PETER YEE FOR ILS PROBLEM >
C
C  usd8rb.for.2 13Aug1993 04:36 usd8 S.GOULD
C       < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C
C     usd8rb.for.1 17Jul1992 14:38 usd8 M.WARD
C     < REPLACED LABEL RBILSVAR BY RBILSHDG AS PER JOHN D. >
C
C'
C
C
C     SUBROUTINES: FIND_STN
C     -----------  ILS_APR
C     RNGBRG
C     RSIGSTR
C
C
      IMPLICIT NONE
C
C
C     EXTERNAL LABELS
C     ---------------
C
C     MISCELLANEOUS
C     -------------
CP    USD8 VH,VHS,YLGAUSN,VXCG,VZCG,YITAIL,
CP    RXACCESS,RTAVAR,RUPLAT,RUPLON,RBFRZ,
CP    RHVATN,RVKILFLG,SLRNAVSL,UBX017A,UBZ017A0,
C
C     VOR/ILS
C     -------
CP    BILJ01(2),TF34031(2),TF34041(2),TF34071(2),TF34121,TF34131,
CP    TV34141(2),TV34151(2),IDRBILS(2),ICVCSC(2),ICVCSS(2),
CP    RBVRCC,RHVFT,RHVFREQ,RXCIVOR,RXCFVOR,RXCRVOR,
CP    RXB,RXCIILS,RXCFILS,RXB2,RXCRILS,RBILSIDE,RBILSLAT,
CP    RBILSLON,RBILSREC,RBILSRAN,RBILSVAR,RBILSTYP,RBFVOS,RBDLO,
CP    RBVTF,RBDGS,RBIFRV,RBIFBV,RBIFIV,RBVFW,RBVNCD,RBILSELE,
CP    RBIFNAV,RBFVOI,RBILSLOB,RBILSGPB,RBFVILC(3),RBCGLOCX,
CP    RBCGGPX,RBCGGPY,RBILSSTY,RBFLO,RBFGS,RBSVOR,RBNVOR,RBBVOR,
CP    RFKFRF05(4),RFKREP05(2),RFKCHM05(2),RFKRTY05(2),RFKDOT05(2),
CP    RFKPAU05(2),RFKTST05(2),RFKIDC05(2),RFKTSL05(2),RFKASS05(2),
C
C     DME
C     ---
CP    BIAA05(2),TF34081(2),RHDFREQ,
CP    RXCRDME,RXCIDME,RXCFDME,RXB5,RBDMETYP,RBDMELAT,RBDMELON,
CP    RBDMEREC,RBDMERAN,RBDMEELE,RBDMEIDE,RBDMESTY,RBDMEDME,
CP    RBRRT,RBTTGO,
CP    RHDMODE,RBGOD,RBDFW,RBIFBD,RBFDME,REDMEIDE,REDMESTY,RBDMEDID,
CP    RBRDME,RBDNCD,RBTDME,RBIFRD,RBDMEM,RDX201A(2),RBFDMEC(2),
CP    RFKFRF0A(4),RFKCHM0A(2),RFKRTY0A(2),RFKDOT0A(2),RFKPAU0A(2),
CP    RFKREP0A(2),RFKTST0A(2),RFKIDC0A(2),RFKTSL0A(2),RFKASS0A(2)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 13-Nov-2018 20:18:58 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RBDMELAT(15)   !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RBDMELON(15)   !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RBILSLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RBILSLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  ICVCSC(2)      ! CAPT HSI COS OF COURSE                CI005
     &, ICVCSS(2)      ! CAPT HSI SIN OF COURSE                CI004
     &, RBBVOR(3)      ! VOR BEARING W.R.T. A/C MAG HDG        [DEG]
     &, RBCGGPX(3)     ! A/C C/G TO GP ANTENNA (X-AXIS)         [FT]
     &, RBCGGPY(3)     ! A/C C/G TO GP ANTENNA (Y-AXIS)         [FT]
     &, RBCGLOCX(3)    ! A/C C/G TO LOC ANTENNA (X-AXIS)        [FT]
     &, RBDGS(3)       ! ILS G/S DEV'N TO INSTS +VE= DOWN     [DOTS]
     &, RBDLO(3)       ! LOC DEV'N TO INSTS +VE= RIGHT        [DOTS]
     &, RBIFBD(3)      ! BEARING  OF A/C TO TUNED DME          [DEG]
     &, RBIFBV(2)      ! BEARING  OF A/C TO TUNED VOR          [DEG]
     &, RBIFNAV(3)     ! NAV STN TUNED FREQ  MHZ               [MHZ]
     &, RBIFRD(3)      ! DISTANCE OF A/C TO TUNED DME           [NM]
     &, RBIFRV(2)      ! DISTANCE OF A/C TO TUNED VOR           [NM]
     &, RBILSVAR(3)    !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RBNVOR(3)      ! VOR NOISE LEVEL
     &, RBRDME(5,3)    ! DME RANGE                              [NM]
     &, RBRRT(5,3)     ! DME RANGE RATE                         [NM]
     &, RBSVOR(3)      ! VOR SIGNAL STRENGTH
     &, RBTTGO(5,3)    ! DME TIME-TO-GO                         [NM]
     &, RBVRCC(3)      ! VOR COURSE SELECTED                   [DEG]
     &, RBVTF(3)       ! VOR TO/FROM FLAG   +VE=TO
     &, RTAVAR         ! MAGNETIC VARIATION                    [DEG]
     &, TV34141(2)     ! LOC DEVIATION OFFSET (10 UNITS = 1 1
     &, TV34151(2)     ! GS DEVIATION OFFSET (10 UNITS = 1 1
     &, UBX017A        ! DME DISTANCE (NM)             R2
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VXCG           ! CENTRE OF GRAVITY                       [in]
     &, VZCG           ! Z-COORD OF C.G.                         [in]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      INTEGER*4
     &  RBDMEDID(15)   ! 27 DME IDENT
     &, RBDMEIDE(15)   ! 42 STATION IDENT (ASCII)
     &, RBDMEREC(15)   !    RZ RECORD NUMBER
     &, RBDMETYP(15)   ! 41 STATION TYPE (ASCII)
     &, RBILSIDE(3)    ! 42 STATION IDENT (ASCII)
     &, RBILSREC(3)    !    RZ RECORD NUMBER
     &, RBILSTYP(3)    ! 41 STATION TYPE (ASCII)
     &, RBTDME(3)      ! DME STATION (FREQ) TUNED
     &, RDX201A(2)     ! LDME                        10 LDME         2
     &, REDMEIDE(3)    ! DME IDENT (ASCII) FOR KEYER
     &, RHDFREQ(5,3)   ! DME SELECTED FREQ                [MHZ*1000]
     &, RHDMODE(5,3)   ! DME FMC DIRECTED FREQUENCY MODE
     &, RHVFREQ(3)     ! VOR CP SELECTED FREQ             [MHZ*1000]
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXCFDME(150)   ! FREQ (OR CHN) OF IN-RANGE DME,TAC
     &, RXCFILS(50)    ! FREQ OF IN-RANGE ILS             [MHZ*1000]
     &, RXCFVOR(150)   ! FREQ OF IN-RANGE VOR             [MHZ*1000]
     &, RXCIDME(150)   ! RZ INDEX NUMBER OF IN-RANGE DME,TAC
     &, RXCIILS(50)    ! RZ INDEX NUMBER OF IN-RANGE ILS
     &, RXCIVOR(150)   ! RZ INDEX NUMBER OF IN-RANGE VOR
     &, RXCRDME(150)   ! RZ RECORD OF IN-RANGE DME,TAC
     &, RXCRILS(50)    ! RZ RECORD OF IN-RANGE ILS
     &, RXCRVOR(150)   ! RZ RECORD OF IN-RANGE VOR
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  RBDMEDME(15)   ! 44 DME BIAS                         [NM*10]
     &, RBDMEELE(15)   !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RBDMERAN(15)   ! 32 STATION POWER RANGE (NM)            [NM]
     &, RBILSELE(3)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RBILSGPB(3)    ! 54 G/P SEMI BEAMWIDTH             [DEG*100]
     &, RBILSLOB(3)    ! 55 LOCALIZER SEMI BEAMWIDTH       [DEG*100]
     &, RBILSRAN(3)    ! 32 STATION POWER RANGE (NM)            [NM]
     &, RXB            ! ACTUAL NO. OF IN-RANGE VOR
     &, RXB2           ! ACTUAL NO. OF IN-RANGE ILS
     &, RXB5           ! ACTUAL NO. OF IN-RANGE DME,TAC
C$
      LOGICAL*1
     &  BIAA05(2)      ! DME 1                       34 PDAL   DI1958
     &, BILJ01(2)      ! VOR 1                      *34 PDLES  DI2008
     &, IDRBILS(2)     ! NAV 1 ILS ENERGIZE                    DI0601
     &, RBDFW(5,3)     ! DME FAILURE WARNING
     &, RBDMEM(5,3)    ! DME MEMORY ANNUNCIATOR
     &, RBDNCD(5,3)    ! DME NCD
     &, RBFDME(5,3)    ! DME VALIDITY
     &, RBFDMEC(2)     ! DME 1        CONTINUOUS TONE FLAGS
     &, RBFGS(3)       ! G/S SUPER FLAG TO INSTS
     &, RBFLO(3)       ! LOC SUPER FLAG TO INSTS
     &, RBFRZ          ! RB FREEZE
     &, RBFVILC(3)     ! VOR/ILS      CONTINUOUS TONE FLAGS
     &, RBFVOI(3)      ! RECEIVER POWERED AND ILS MODE SELECTED
     &, RBFVOS(3)      ! A/C OVER TUNED VOR STN
     &, RBGOD(3)       ! DME AUDIO
     &, RBVFW(3)       ! VOR RECEIVER FAILURE WARNING
     &, RBVNCD(3)      ! VOR RECEIVER NCD
     &, RHVATN(3)      ! VOR AUTOTUNE FLAG
     &, RHVFT(3)       ! VOR CP FUNC. TEST
     &, RVKILFLG       ! CHANGE KILL SUMMARY
     &, SLRNAVSL(2)    ! RNAV no.1 sel out
     &, TF34031(2)     ! ILS LOC FAIL 1
     &, TF34041(2)     ! ILS GS FAIL 1
     &, TF34071(2)     ! VOR FAIL 1
     &, TF34081(2)     ! DME FAIL 1
     &, TF34121        ! LOC BEAM DEVIATION
     &, TF34131        ! GS BEAM DEVIATION
     &, UBZ017A0       ! DME FLAG
C$
      INTEGER*1
     &  RBDMESTY(15)   ! 38 SUBTYPE NUMBER
     &, RBIFIV(4,2)    ! IDENT OF TUNED VOR
     &, RBILSSTY(3)    ! 38 SUBTYPE NUMBER
     &, REDMESTY(3)    ! DME KEYER SUBTYPE
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(1196),DUM0000003(10384)
     &, DUM0000004(1042),DUM0000005(664),DUM0000006(379)
     &, DUM0000007(4225),DUM0000008(40),DUM0000009(1956)
     &, DUM0000010(92),DUM0000011(12206),DUM0000012(6136)
     &, DUM0000013(116),DUM0000014(4),DUM0000015(515)
     &, DUM0000016(624),DUM0000017(6),DUM0000018(60)
     &, DUM0000019(9),DUM0000020(6),DUM0000021(72)
     &, DUM0000022(216),DUM0000023(988),DUM0000024(210)
     &, DUM0000025(120),DUM0000026(45),DUM0000027(30)
     &, DUM0000028(30),DUM0000029(692),DUM0000030(2196)
     &, DUM0000031(36),DUM0000032(15),DUM0000033(16)
     &, DUM0000034(96),DUM0000035(15),DUM0000036(21)
     &, DUM0000037(3),DUM0000038(12),DUM0000039(63)
     &, DUM0000040(15),DUM0000041(30),DUM0000042(3)
     &, DUM0000043(67),DUM0000044(30),DUM0000045(3)
     &, DUM0000046(253),DUM0000047(60),DUM0000048(185)
     &, DUM0000049(458),DUM0000050(24),DUM0000051(8)
     &, DUM0000052(44),DUM0000053(1634),DUM0000054(1)
     &, DUM0000055(30),DUM0000056(4),DUM0000057(2284)
     &, DUM0000058(460),DUM0000059(2880),DUM0000060(460)
     &, DUM0000061(2880),DUM0000062(460),DUM0000063(255624)
     &, DUM0000064(518),DUM0000065(1728),DUM0000066(6563)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,YLGAUSN,DUM0000003,ICVCSC
     &, ICVCSS,DUM0000004,IDRBILS,DUM0000005,BILJ01,DUM0000006
     &, BIAA05,DUM0000007,VHS,DUM0000008,VH,DUM0000009,VXCG,DUM0000010
     &, VZCG,DUM0000011,SLRNAVSL,DUM0000012,RUPLAT,RUPLON,DUM0000013
     &, RBTDME,DUM0000014,RBFVILC,RBFDMEC,DUM0000015,RTAVAR,DUM0000016
     &, RBILSLAT,RBILSLON,RBILSELE,DUM0000017,RBILSVAR,DUM0000018
     &, RBILSRAN,DUM0000019,RBILSSTY,DUM0000020,RBILSTYP,RBILSIDE
     &, DUM0000021,RBILSGPB,RBILSLOB,DUM0000022,RBILSREC,DUM0000023
     &, RBDMELAT,RBDMELON,RBDMEELE,DUM0000024,RBDMEDID,DUM0000025
     &, RBDMERAN,DUM0000026,RBDMESTY,DUM0000027,RBDMETYP,RBDMEIDE
     &, DUM0000028,RBDMEDME,DUM0000029,RBDMEREC,DUM0000030,RBBVOR
     &, DUM0000031,RBVTF,RBVRCC,RBSVOR,RBNVOR,DUM0000032,RBFVOS
     &, RBVFW,RBVNCD,DUM0000033,RBDLO,RBDGS,DUM0000034,RBCGGPY
     &, RBCGGPX,RBCGLOCX,DUM0000035,RBFLO,RBFGS,DUM0000036,RBFVOI
     &, DUM0000037,RBRDME,DUM0000038,RBRRT,RBTTGO,REDMEIDE,DUM0000039
     &, RBFDME,RBDFW,RBDNCD,DUM0000040,RBDMEM,DUM0000041,REDMESTY
     &, DUM0000042,RBGOD,DUM0000043,RHVFREQ,DUM0000044,RHVFT
     &, DUM0000045,RHVATN,DUM0000046,RHDFREQ,DUM0000047,RHDMODE
     &, DUM0000048,RBFRZ,DUM0000049,RBIFRV,RBIFBV,DUM0000050
     &, RBIFRD,RBIFBD,DUM0000051,RBIFIV,DUM0000052,RBIFNAV,DUM0000053
     &, RVKILFLG,DUM0000054,RXACCESS,DUM0000055,RXB,RXB2,DUM0000056
     &, RXB5,DUM0000057,RXCFVOR,RXCFILS,DUM0000058,RXCFDME,DUM0000059
     &, RXCRVOR,RXCRILS,DUM0000060,RXCRDME,DUM0000061,RXCIVOR
     &, RXCIILS,DUM0000062,RXCIDME,DUM0000063,TV34151,TV34141
     &, DUM0000064,TF34081,TF34131,TF34041,TF34031,TF34121,TF34071
     &, DUM0000065,UBX017A,UBZ017A0,DUM0000066,RDX201A   
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFKASS05(2)    ! KEYER ASSOCIATION POSSIBILITY #5      MO6854
     &, RFKASS0A(2)    ! KEYER ASSOCIATION POSSIBILITY #10     MO6859
     &, RFKCHM05(2)    ! KEYER CHANGE MATRIX #5                MO6804
     &, RFKCHM0A(2)    ! KEYER CHANGE MATRIX #10               MO6809
     &, RFKDOT05(2)    ! KEYER DOT DURATION #5                 MO6818
     &, RFKDOT0A(2)    ! KEYER DOT DURATION #10                MO681D
     &, RFKFRF05(4)    ! KEYER TONE FREQ. FRACTIONNAL #5       MO68D1
     &, RFKFRF0A(4)    ! KEYER TONE FREQ. FRACTIONNAL #10      MO68DB
     &, RFKIDC05(004,2)! KEYER ID CHARACTER CH#5               MO6888
     &, RFKIDC0A(004,2)! KEYER ID CHARACTER CH#10              MO689C
     &, RFKPAU05(2)    ! KEYER PAUSE DURATION #5               MO682C
     &, RFKPAU0A(2)    ! KEYER PAUSE DURATION #10              MO6831
     &, RFKREP05(2)    ! KEYER REPETITION PERIOD #5            MO6840
     &, RFKREP0A(2)    ! KEYER REPETITION PERIOD #10           MO6845
     &, RFKRTY05(2)    ! KEYER RECEIVER TYPE #5                MO6868
     &, RFKRTY0A(2)    ! KEYER RECEIVER TYPE #10               MO686D
     &, RFKTSL05(2)    ! KEYER TONE SIGNAL LEVEL #5            MO68F4
     &, RFKTSL0A(2)    ! KEYER TONE SIGNAL LEVEL #10           MO68F9
     &, RFKTST05(2)    ! KEYER TONE STATE #5                   MO6908
     &, RFKTST0A(2)    ! KEYER TONE STATE #10                  MO690D
C$
      LOGICAL*1
     &  DUM0100001(3776),DUM0100002(24),DUM0100003(432)
     &, DUM0100004(12),DUM0100005(756),DUM0100006(6)
     &, DUM0100007(26),DUM0100008(6),DUM0100009(26)
     &, DUM0100010(6),DUM0100011(26),DUM0100012(6)
     &, DUM0100013(26),DUM0100014(6),DUM0100015(26)
     &, DUM0100016(6),DUM0100017(26),DUM0100018(6)
     &, DUM0100019(26),DUM0100020(6)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFKIDC05,DUM0100002,RFKIDC0A,DUM0100003,RFKFRF05
     &, DUM0100004,RFKFRF0A,DUM0100005,RFKCHM05,DUM0100006,RFKCHM0A
     &, DUM0100007,RFKDOT05,DUM0100008,RFKDOT0A,DUM0100009,RFKPAU05
     &, DUM0100010,RFKPAU0A,DUM0100011,RFKREP05,DUM0100012,RFKREP0A
     &, DUM0100013,RFKASS05,DUM0100014,RFKASS0A,DUM0100015,RFKRTY05
     &, DUM0100016,RFKRTY0A,DUM0100017,RFKTSL05,DUM0100018,RFKTSL0A
     &, DUM0100019,RFKTST05,DUM0100020,RFKTST0A  
C------------------------------------------------------------------------------
C
C
C     INTERNAL LABELS
C     ---------------
C
C     MISCELLANEOUS
C     -------------
C
      REAL*4
     &     SP1R                 ! Scratch pad
C
      INTEGER*4
     &     I,J,JJ,K(3),L,LL(2),M ! Counters
     &     , SCALSYS/'14'X/     ! Scaling system id code
     &     , PREFRE(2)
C
      INTEGER*2
     &     TYPE                 ! Rcvr type no
C
      LOGICAL*1
     &     NAVCHNG(2)           ! NAV Frequency change
C
C     VOR/ILS RCVR
C     ------------
      REAL*4
     &     BRG(2)               ! Bearing a/c to stn (wrt true north)
     &     , COSLATFG(2)        ! Cos lat front gp ant
     &     , COSLATVL(2)        ! Cos stn lat
     &     , DEVN(2)            ! VOR deviation
     &     , GSDEV(2)           ! GS devn - temporary
     &     , GSERR(2)           ! GS interf. malf. error amount
     &     , GSINCR(2)          ! GS interf. malf. increment
     &     , GSLIMIT            ! GS interf. malf. vbl limit
     &     , GSRATE(2)          ! GS interf. malf. vbl rate
     &     , GSTIME             ! GS interf. malf. vbl duration
     &     , GSTMR(2)           ! GS interf. malf. timer
     &     , ILSTTMR(2)         ! ILS test timer
     &     , JITTER(2)          ! BRG pointer jitter
     &     , LOCERR(2)          ! LOC interf. malf. error amount
     &     , LOCINCR(2)         ! LOC interf. malf. increment
     &     , LOCLIMIT           ! LOC interf. malf. vbl limit
     &     , LOCRATE(2)         ! LOC interf. malf. vbl rate
     &     , LOCTIME            ! LOC interf. malf. vbl duration
     &     , LOCTMR(2)          ! LOC interf. malf. timer
     &     , MAGBRG(2)          ! MAG bearing
     &     , MODF(2)            ! Modification factor to signal strength
     &     , NNOISE(2)          ! Noise level
     &     , NSIG(2)            ! RF signal strength
     &     , RAD                ! A/C height (nm)
     &     , RAN(2)             ! Dist a/c to stn
     &     , RANDOM(2)          ! Random number
     &     , RATE(2)            ! Rate of change of brg
     &     , VORBRG(2)          ! Actual vor bearing
     &     , VORSLEW            ! VOR bearing pointer slew rate  (21 deg/s)
C
      INTEGER*4
     &     FREQ(2)              ! Selected nav frequency
     &     , GSTYP              ! G/S killed mask
     &     , IDE                ! Ident
     &     , IFIN(3)            ! Ident of tuned nav station
     &     , IKILTYP(3)         ! ILS kill mask
     &     , INTREC(2)          ! Interfering stn index no
     &     , LOCTYP             ! LOC killed mask
     &     , PREILIDE(3)        ! Previous ident for vor/ils
     &     , STNREC(2)          ! Stn index no
     &     , VKILTYP(3)         ! Nav kill mask
     &     , VORTONE(2)         ! VOR tone
     &     , VORTYP             ! NAV killed mask
C
      INTEGER*2
     &     PROLOCVL(2)          ! Probable location of current stn in VOR/ILS ta
C
      INTEGER*1
     &     IDEQ(4)              ! Equivalent array of ide
     &     , ILSSTY(3)          ! Station type
C
      LOGICAL*1
     &     FTFILS(2)            ! First time flag for ilsapr
     &     , FTFVIL(2)          ! First time flag for rngbrg
     &     , GSINIT(2)          !
     &     , GSVALID(2)         ! GS receiver validity
     &     , LOCFAIL(2)         !
     &     , LOCINIT(2)         !
     &     , LOCVALID(2)        ! LOC receiver validity
     &     , NAVPWR(2)          ! Nav receiver powered
     &     , PARK(2)            ! Park vor pointer
     &     , PRETEST(2)         ! Previous value of rbilft
     &     , PREVTEST(2)        !
     &     , VCONEFLG(2)        ! A/C in cone of confusion
     &     , VORTEST(2)         !
     &     , SLILSDNR(2)        ! ************ FOR NOW *********
     &     , SLILSUPL(2)        ! ************ FOR NOW *********
C
C     DME
C     ----
      REAL*4
     &     COSLATDM(15)         ! DME transmitter cos lat
     &     , DBRG(15)           ! BRG to dme station
     &     , DELAY(15)          ! Lock on delay timer
     &     , DME1               !
     &     , DMERATE(15)        ! DME distance rate of change
     &     , DMETTMR(3)         ! DME test timer
     &     , DNOISE(15)         ! DME noise level
     &     , DRAN(15)           ! Range to dme station
     &     , DSIG(15)           ! DME sig str
     &     , INTERVAL(15)       ! Memory interval for averaging
     &     , MAXRAN             ! DME rcvr max range
     &     , MEMTMR(15)         ! Memory timer
     &     , OLDRAN(15)         ! Previous value of range
     &     , SLRAN(15)          ! DME computed slant range
     &     , TEMPRAN(15)        !
     &     , VELOCITY(15)       ! Memory velocity (nm/iter)
     &     , YITIM              ! Iteration rate
C
      INTEGER*4
     &     DEC(6)               !
     &     , DINTREC(15)        ! Interfering stn index no
     &     , DKILTYP(15)        ! DME kill mask
     &     , DMETONE(2)         ! DME tone
     &     , DMETYP             ! DME killed mask
     &     , DSTNREC(15)        ! STN index no
     &     , FREQD(15)          ! DME frequency
     &     , HEX(6)             !
     &     , PREDMIDE(3)        ! Previous ident for dme
     &     , RANGE(2)           !
     &     , RRDAT(2)           !
     &     , TIMER(3)           ! Maximum duration of dme memory mode
C
      INTEGER*2
     &     PREVIND(15)          ! Last times dme index
     &     , PROLOCDM(15)       ! Probable location of stn in tune
     &     , TYP5               ! DME type for FIND_STN
C
      INTEGER*1
     &     DMESTY(15)           ! Station type
C
      LOGICAL*1
     &     DMECHNG(2)           ! DME Frequency change
     &     , DMEFT(3)           ! DME functional test
     &     , DMEFW(3)           ! DME failure warning
     &     , DMENCD(3)          ! DME NCD
     &     , DMEPWR(3)          ! DME power status
     &     , FTFDME(15)         ! First-time flag for RNGBRG
     &     , IDLETST1(2)        ! *********** FOR NOW ************
     &     , IDLPDM1T(2)        ! *********** FOR NOW ************
C
      EQUIVALENCE (IDEQ,IDE)
      EQUIVALENCE (RBIFIV,IFIN)
      EQUIVALENCE (RFKFRF05(1),VORTONE(1))
      EQUIVALENCE (RBILSSTY,ILSSTY)
      EQUIVALENCE (RFKFRF0A(1),DMETONE(1))
      EQUIVALENCE (RBDMESTY,DMESTY)
C
      COMMON/DISPCOM/YITIM
C
      PARAMETER (MAXRAN=320.)
C
      DATA TIMER/10,10,10/,
     -     DEC/1,10,100,1000,10000,100000/,
     -     DMETYP/6/,
     -     GSTYP/8/,
     -     HEX/X'100',X'1000',X'10000',X'100000',
     -     X'1000000',X'10000000'/,
     -     LOCTYP/16/,
     -     VORTYP/1/,
     -     TYP5/5/
C
C
C     -- Module entry point
C
      Entry RBNAV
C
      IF (RBFRZ) RETURN
C
      CALL SCALIN( SCALSYS )
C
C     FIRST PASS SET UP
C
      IF (VORSLEW.EQ.0.0) THEN
        VORSLEW = YITIM * 25.0
      ENDIF
C
C
C     The VOR/ILS Navigation Set: KING KNR-634
C     --------------------------
C
C     Description: VOR Mode: The VOR/ILS rceivers may be tuned from the NAV
C     -----------            control panels. VOR bearing information is displaye
C     on the RMI's and course deviation information is
C     displayed on the HSI's.
C     ILS Mode: ILS indications are displayed on the HSI's and ADI's.
C     The bearing pointers are parked at 3 o'clock when an
C     ILS frequency is selected or the signal is lost in
C     VOR mode.
C
C
      DO 490 I = 1,2
C
C     CHECK IF ILS OR VOR FREQ, TYPE=1 FOR VOR, TYPE=2 FOR ILS
C
        IF (IDRBILS(I).AND..NOT.RHVATN(I)) THEN
          TYPE = 2
        ELSE
          TYPE = 1
        ENDIF
C
C     DETERMINE VHF NAV PWR STATUS
C
        NAVPWR(I) = BILJ01(I)
C
C     SET RECEIVER POWERED AND ILS MODE SELECTED FLAG FOR A/P
C
        RBFVOI(I) = NAVPWR(I) .AND. IDRBILS(I)
C
C     DECODE THE SELECTED COURSE
C
        IF (ICVCSS(I).NE.0.AND.ICVCSC(I).NE.0)
     &       RBVRCC(I) = (ATAN2(ICVCSS(I),ICVCSC(I))*1/0.0174533)-60.
        IF (RBVRCC(I).GT.180.) RBVRCC(I)=RBVRCC(I)-360
        IF (RBVRCC(I).LT.-180.) RBVRCC(I)=RBVRCC(I)+360
C
C     DECODE THE SELECTED FREQUENCY
C
C !FM+
C !FM  13-Aug-93 04:26:41 S.GOULD
C !FM    < ADDED CHANGES FROM PETER YEE SENT TO CORRECT USAIR SPR 9019 >
C !FM
        IF (RHVFREQ(I) .NE. PREFRE(I)) THEN
          NAVCHNG(I) = .TRUE.
          PREFRE(I)  = RHVFREQ(I)
          FREQ(I) = RHVFREQ(I)
        ENDIF
C
        IF (TYPE.EQ.1) THEN
C
C     RECEIVER IS IN VOR MODE
C
          PARK(I) = .FALSE.
          RBDGS(I) = 0.
          RBFGS(I) = .FALSE.
C
C     NO POWER OR FAIL, SET DEV'S, TO/FROM, SIG & NOISE=0, FLAGS INVALID
C
          IF (.NOT.NAVPWR(I).OR.TF34071(I)) THEN
            RBVTF(I) = 0.
            RBDLO(I) = 0.
            RBVFW(I) = .TRUE.
            NSIG(I) = 0.
            NNOISE(I) = 0.
            RBFLO(I) = .FALSE.
            RBIFNAV(I) = 0.
            NAVCHNG(I) = .TRUE.
          ELSE
C
C     RECEIVER IS POWERED
C
            RBVFW(I) = .FALSE.
C
C     OUTPUT VOR FREQ TO I/F
C
            RBIFNAV(I) = RHVFREQ(I)*(1/1000.)
C
C     DETERMINE WHETHER A STATION IS IN RANGE
C
            IF (NAVCHNG(I) .OR. RVKILFLG) THEN
              CALL FIND_STN (TYPE,FREQ(I),RXB,RXCFVOR,RXCIVOR,RXCRVOR,
     &             PROLOCVL(I),STNREC(I),INTREC(I),VKILTYP(I))
              NAVCHNG(I) = .FALSE.
            ENDIF
C !FM-
C
C     A STATION MAY BE IN RANGE
C
            IF (STNREC(I).NE.0.AND.(IAND(VKILTYP(I),VORTYP).EQ.0))THEN
C
C     REQUEST DATA
C
              IF (STNREC(I).NE.RBILSREC(I)) THEN
                IF (RXACCESS(1,I).EQ.0) RXACCESS(1,I)=STNREC(I)
                FTFVIL(I) = .TRUE.
                NSIG(I) = 0.
                NNOISE(I) = 0.
              ELSE
C
C     COMPLETE DATA IS AVAILABLE DETERMINE THE RANGE & BRG
C
                IF (COSLATVL(I) .EQ. 0) FTFVIL(I)= .TRUE.
                CALL RNGBRG (RBILSLAT(I),RBILSLON(I),RUPLAT,RUPLON,
     &               COSLATVL(I),FTFVIL(I),RAN(I),BRG(I))
C
C     DETERMINE THE SIGNAL STRENGTH
C
                CALL RSIGSTR(1,RBILSRAN(I),RAN(I),RBILSELE(I),NSIG(I),
     &               NNOISE(I))
              ENDIF
            ELSE
C
C     THERE IS NO STATION TUNED
C
              NSIG(I) = 0.
              NNOISE(I) = 1.0
            ENDIF
C
C     CHECK IF SIGNAL IS LOW
C
            IF (NSIG(I).NE.0.0) THEN
C
C     180
C     CHECK IF TEST MODE SELECTED
C
              IF (RHVFT(I)) THEN
C
C     THERE IS A TEST IN PROGRESS
C
                VORTEST(I) = .TRUE.
C
C     SET CONTINUOUS TONE & PUT BRG =180
C
                RBFVILC(I) = .TRUE.
                NSIG(I) = 1.0
                NNOISE(I) = 0.
                MAGBRG(I) = 180.
C
              ELSE
C
C     NO TEST IS IN PROGRESS
C
C     210
                VORTEST(I) = .FALSE.
                RBFVILC(I) = .FALSE.
C
C     IF STN IS A 'VOT' SET MAG BRG = 180 DEGS
C
                IF (RBILSTYP(I).EQ.X'564F5420') THEN
                  MAGBRG(I) = 180.
                ELSE
C
C     DETERMINE EFFECT OF CONE OF CONFUSION ON SIG STR & MAG BRG :
C     IF THE VERTICAL ANGLE IS GREATER THAN 55 DEGS, THE SIG AND NOISE ARE
C     MODIFIED BY A FACTOR (RANGE/RADIUS) AND A RANDOM NUMBER IS ADDED TO THE
C     BEARING.
C     IF THE VERTICAL ANGLE IS GREATER THAN 70 DEGS, THE CONE FLAG IS SET
C     AND THE BEARING IS FROZEN.
C
C     ANGLE > 55 DEGS ?
C
                  IF (VH.GT.8677.57*RAN(I)) THEN
C
                    VCONEFLG(I) = .FALSE.
                    RBFVOS(I) = .TRUE.
                    RAD = VH * 0.0001152
                    IF (RAD.GT.0.) MODF(I)=RAN(I)/RAD
                    NSIG(I) = NSIG(I) * MODF(I)
                    IF (NSIG(I).LT.0.01) NSIG(I)=0.01
                    NNOISE(I) = 1. - NSIG(I)
                    IF (ABS(YLGAUSN(I)).GT.0.3) RANDOM(I)=YLGAUSN(I)
                    JITTER(I) = (VH/(6076.1*RAN(I)+100.0)-1.0)
     &                   *RANDOM(I)*5.0
C
C     ANGLE > 70 DEGS ?
C
                    IF(VH.GT.16693.95*RAN(I)) THEN
                      VCONEFLG(I) = .TRUE.
                      JITTER(I) = 0.
                    END IF
C
                  ELSE
C
                    RBFVOS(I) = .FALSE.
                    VCONEFLG(I) = .FALSE.
                    IF (NNOISE(I).LT.0.85.OR.NNOISE(I).GE.1.) THEN
                      JITTER(I) = 0.
                    ELSE
                      JITTER(I) = YLGAUSN(I) * 40. * (NNOISE(I)-0.85)
                    ENDIF
C
                  ENDIF
C
C     DETERMINE NEW MAG BRG
C
                  MAGBRG(I) = BRG(I) + JITTER(I) - RBILSVAR(I)
                  IF (MAGBRG(I).GT.180.) THEN
                    MAGBRG(I) = MAGBRG(I) - 360.
                  ELSE IF (MAGBRG(I).LT.-180.) THEN
                    MAGBRG(I) = MAGBRG(I) + 360.
                  ENDIF
C
                ENDIF
              ENDIF
            ENDIF
C
            IF (NSIG(I).GT.0..AND..NOT.VCONEFLG(I)) THEN
C
C     DETERMINE COURSE DEV'N & TO/FROM FLAG
C
              RBFLO(I) = .TRUE.
              DEVN(I) = VORBRG(I) - RBVRCC(I)
              IF (DEVN(I).GT.180.) THEN
                DEVN(I) = DEVN(I) - 360.
              ELSE IF (DEVN(I).LT.-180.) THEN
                DEVN(I) = DEVN(I) + 360.
              ENDIF
              IF (ABS(DEVN(I)).LT.90.) THEN
                RBVTF(I) = 1.0
              ELSE
                RBVTF(I) = -1.0
                DEVN(I) = -DEVN(I) + 180.
                IF (DEVN(I).GT.180.) THEN
                  DEVN(I) = DEVN(I) - 360.
                ELSE IF (DEVN(I).LT.-180.) THEN
                  DEVN(I) = DEVN(I) + 360.
                ENDIF
              ENDIF
              IF (DEVN(I).GE.20.) THEN
                RBDLO(I) = 4.
              ELSE IF (DEVN(I).LE.-20.) THEN
                RBDLO(I) = -4.
              ELSE
                RBDLO(I) = 0.2 * DEVN(I)
              ENDIF
            ELSE IF (NSIG(I) .LE. 0) THEN
              RBFLO(I) = .FALSE.
              RBDLO(I) = 0.0
              RBVTF(I) = 0.0
            ENDIF
C
          ENDIF
C
C     ARINC 429 OUTPUTS
C
C     NCD CONDITION:
C
          RBVNCD(I) = .NOT.(RBFLO(I).OR.RBVFW(I))
C
        ELSE
C
C     RECEIVER IS IN ILS MODE
C
C     PARK VOR POINTERS AT 3'OCLOCK POSITION
C
          PARK(I) = .TRUE.
C
C     TO/FROM POINTER OUT OF VIEW
C
          RBVTF(I) = 0.
C
C     RESET OVER STATION FLAG
C
          RBFVOS(I) = .FALSE.
C
          IF (NAVPWR(I)) THEN
C
C     CHECK FOR TEST
C
            IF ((SLILSDNR(I).OR.SLILSUPL(I).OR.IDLETST1(I))) THEN
C
              IF (.NOT.PRETEST(I)) THEN
                PRETEST(I) = .TRUE.
                ILSTTMR(I) = 0.
              ELSE
                ILSTTMR(I) = ILSTTMR(I) + YITIM
              ENDIF
C
              IF (ILSTTMR(I).LT.3.) THEN
C
                RBFLO(I) = .FALSE.
                RBFGS(I) = .FALSE.
C
              ELSE IF (ILSTTMR(I).LT.20.) THEN
C
                RBFLO(I) = .TRUE.
                RBFGS(I) = .TRUE.
                IF (SLILSDNR(I)) THEN
                  DEVN(I) = 1.
                  GSDEV(I) = 1.
                ELSE
                  DEVN(I) = -1.
                  GSDEV(I) = -1.
                ENDIF
C
              ELSE
C
                RBFLO(I) = .FALSE.
                RBFGS(I) = .FALSE.
C
              ENDIF
C
            ELSE
C
              PRETEST(I) = .FALSE.
C
C     DETERMINE WHETHER AN ILS STATION IS IN RANGE
C
C !FM+
C !FM  14-Aug-93 03:29:44 S.GOULD
C !FM    < ADDITIONAL CHANGES FROM PETER YEE FOR ILS PROBLEM >
C !FM
              IF (NAVCHNG(I) .OR. RVKILFLG) THEN
                CALL FIND_STN (TYPE,FREQ(I),RXB2,RXCFILS,RXCIILS,
     &             RXCRILS,PROLOCVL(I),STNREC(I),INTREC(I),IKILTYP(I))
                NAVCHNG(I) = .FALSE.
              ENDIF
C !FM-
C
C     REQUEST DATA
C
              IF ((STNREC(I).NE.RBILSREC(I)).AND.(STNREC(I).NE.0)) THEN
                IF (RXACCESS(1,I).EQ.0) RXACCESS(1,I)=STNREC(I)
                NSIG(I) = 0.
                NNOISE(I) = 0.
                FTFVIL(I) = .TRUE.
                FTFILS(I) = .TRUE.
              ELSE
C
C     THERE IS NO STATION TUNED
C
                IF (STNREC(I).EQ.0) THEN
                  NSIG(I) = 0.
                  NNOISE(I) = 1.0
                  RBFLO(I) = .FALSE.
                  RBFGS(I) = .FALSE.
                ELSE
C
C     SET UP ANTENNA PARAMETERS REFERRED TO C/G
C
C     ********* ESTIMATE - TO BE REPLACED BY ACTUAL NUMBERS **********
C
                  RBCGLOCX(1) = (VXCG-30)/12
                  RBCGGPY(1) = (100-VZCG)/12
                  RBCGGPX(1) = (VXCG-30)/12
C
C     DETERMINE THE ILS DEVIATIONS
C
                  CALL ILS_APR(I,RBCGLOCX(1),RBCGGPX(1),RBCGGPY(1),
     &                 DEVN(I),GSDEV(I),RAN(I),BRG(I),
     &                 LOCVALID(I),GSVALID(I),FTFILS(I),COSLATFG(I),
     &                 COSLATVL(I))
C
C     OBTAIN SIGNAL STRENGTH
C
                  CALL RSIGSTR (2,RBILSRAN(I),RAN(I),RBILSELE(I),
     &                 NSIG(I),NNOISE(I))
C
                ENDIF
C
C     DETERMINE TRANSMITTED LOC & GS VALIDITY
C
C     OUTPUT THE GLIDESLOPE VALIDITY
C
                RBFGS(I) = (NSIG(I).GT.0.0)
     &               .AND.GSVALID(I).AND.(IAND(IKILTYP(I),GSTYP).EQ.0)
     &               .AND..NOT.TF34041(I)
C
C     MODIFY SIGNAL STRENGTH FOR LOCALIZER
C
                IF(TF34031(I))THEN
                  NSIG(I) = 0.0
                  NNOISE(I) = 0.0
                ELSEIF(IAND(IKILTYP(I),(LOCTYP)).NE.0)THEN
                  NSIG(I) = 0.0
                  NNOISE(I) = 1.0
                ENDIF
C
C     OUTPUT LOCALIZER VALIDITY
C
                RBFLO(I) = (NSIG(I).GT.0.0) .AND. LOCVALID(I)
     &               .AND..NOT. LOCFAIL(I)
C
              ENDIF
            ENDIF
C
          ELSE
C
C
C     ILS IS NOT POWERED
C
            NSIG(I) = 0.
            NNOISE(I) = 0.
            RBFLO(I) = .FALSE.
            RBFGS(I) = .FALSE.
            PRETEST(I) = .FALSE.
            RBIFNAV(I) = 0.
C !FM+
C !FM  13-Aug-93 04:26:41 S.GOULD
C !FM    < ADDED CHANGES FROM PETER YEE SENT TO CORRECT USAIR SPR 9019 >
C !FM
            NAVCHNG(I) = .TRUE.
C !FM-
C
          ENDIF
C
C     OUTPUT LOCALIZER DEVIATION IN DOTS
C
          IF (.NOT.RBFLO(I)) THEN
            RBDLO(I) = 0.0
          ELSE
C
C     LOC BEAM DEVIATION MALFUNCTION
C     FREQUENCY=3 CYCLES/MIN, MAX LIMIT=0.4 DOTS, MAX DURATION=4 MIN.
C     3 CYCLE/MIN=0.05 CYCLE/SEC: (0.05 CYCLE/SEC)*(1.6 DOTS/CYCLE)=0.08 DOTS/SE
C
            IF (TF34121.AND.(RBIFRV(I).LT.30.)) THEN
              LOCLIMIT = 0.4
              LOCTIME = 240.
              IF (LOCTMR(I).LT.LOCTIME) THEN
                LOCTMR(I) = LOCTMR(I) + YITIM
                IF (LOCINIT(I)) THEN
                  LOCINIT(I) = .FALSE.
                  LOCRATE(I) = 0.08 * YITIM
                  LOCINCR(I) = 0.08 * YITIM
                ENDIF
                IF (LOCERR(I).GE.LOCLIMIT) THEN
                  LOCINCR(I) = -LOCRATE(I)
                ELSEIF (LOCERR(I).LE.-LOCLIMIT) THEN
                  LOCINCR(I) = +LOCRATE(I)
                ENDIF
                LOCERR(I) = LOCERR(I) + LOCINCR(I)
              ELSE
                LOCINIT(I) = .TRUE.
                LOCERR(I) = 0.0
              ENDIF
            ELSE
              LOCINIT(I) = .TRUE.
              LOCTMR(I) = 0.0
              LOCERR(I) = 0.0
            ENDIF
C
C     LOC DEVIATION OFFSET MALFUNCTION (10 UNITS = 1 DOT)
C
            RBDLO(I) = DEVN(I) + (TV34141(I)*0.1) + LOCERR(I)
C
            IF(RBDLO(I).GT.4) THEN
              RBDLO(I) = 4
            ELSE IF(RBDLO(I).LT.-4) THEN
              RBDLO(I) = -4
            END IF
c
          ENDIF
C
C     OUTPUT G/S DEVIATION IN DOTS
C
          IF (.NOT.RBFGS(I)) THEN
            RBDGS(I) = 0.0
          ELSE
C
C     GS BEAM DEVIATION MALFUNCTION
C     FREQUENCY=3 CYCLES/MIN, MAX LIMIT=0.4 DOTS, MAX DURATION=4 MIN.
C     3 CYCL/MIN=0.05 CYCLES/SEC: (0.05 CYCLES/SEC)*(1.6 DOTS/CYCLE)=0.08 DOTS/S
C
            IF (TF34131.AND.(RBIFRV(I).LT.30.)) THEN
              GSLIMIT = 0.4
              GSTIME = 240.
              IF (GSTMR(I).LT.GSTIME) THEN
                GSTMR(I) = GSTMR(I) + YITIM
                IF (GSINIT(I)) THEN
                  GSINIT(I) = .FALSE.
                  GSRATE(I) = 0.08 * YITIM
                  GSINCR(I) = 0.08 * YITIM
                ENDIF
                IF (GSERR(I).GE.GSLIMIT) THEN
                  GSINCR(I) = -GSRATE(I)
                ELSEIF (GSERR(I).LE.-GSLIMIT) THEN
                  GSINCR(I) = +GSRATE(I)
                ENDIF
                GSERR(I) = GSERR(I) + GSINCR(I)
              ELSE
                GSINIT(I) = .TRUE.
                GSERR(I) = 0.0
              ENDIF
            ELSE
              GSINIT(I) = .TRUE.
              GSTMR(I) = 0.0
              GSERR(I) = 0.0
            ENDIF
C
C     GS DEVIATION OFFSET MALFUNCTION (10 UNITS = 1 DOT)
C
            RBDGS(I) = GSDEV(I) + (TV34151(I)*0.1) + GSERR(I)
C
          ENDIF
C
          IF(RBDGS(I).GT.4) THEN
            RBDGS(I) = 4
          ELSE IF(RBDGS(I).LT.-4) THEN
            RBDGS(I) = -4
          ENDIF
C
        ENDIF
C
C     END OF ILS SECTION
C
C     VOR POINTERS
C
        IF (NAVPWR(I)) THEN
C
          IF (PARK(I)) MAGBRG(I) = 90.
C
C     LIMIT THE BEARING RATE TO 25 DEGS/SEC
C
          RATE(I) = MAGBRG(I) - VORBRG(I)
          IF (RATE(I).GT.180.) THEN
            RATE(I) = RATE(I) - 360.
          ELSE IF (RATE(I).LT.-180.) THEN
            RATE(I) = RATE(I) + 360.
          ENDIF
          IF (RATE(I).GT.VORSLEW) THEN
            RATE(I) = VORSLEW
          ELSE IF (RATE(I).LT.-VORSLEW) THEN
            RATE(I) = -VORSLEW
          ENDIF
C
C     VOR BEARING UPDATED
C
          VORBRG(I) = VORBRG(I) + RATE(I)
          IF (VORBRG(I).GT.180.) THEN
            VORBRG(I) = VORBRG(I) - 360.
          ELSE IF (VORBRG(I).LT.-180.) THEN
            VORBRG(I) = VORBRG(I) + 360.
          ENDIF
          RBBVOR(I) = VORBRG(I)
          IF (RBBVOR(I).GT.180.) THEN
            RBBVOR(I) = RBBVOR(I) - 360.
          ELSE IF (RBBVOR(I).LT.-180.) THEN
            RBBVOR(I) = RBBVOR(I) + 360.
          ENDIF
        ENDIF
C
C     OUTPUT TO I/F
C
        IF (NSIG(I).EQ.0.0) THEN
          RBIFRV(I) = 0
          RBIFBV(I) = 0
          IFIN(I) = X'20202020'
        ELSE
          RBIFRV(I) = RAN(I)
          BRG(I) = BRG(I) - RTAVAR
          IF (BRG(I).LT.0.) BRG(I)=BRG(I)+360
          RBIFBV(I) = BRG(I)
          IFIN(I) = RBILSIDE(I)
        ENDIF
C
C     AUDIO OUTPUT
C
C COA S81-1-104 RNAV/VOR ident fix.
C
C        IF (I.EQ.1.AND.RHVATN(I)) THEN
C          RBSVOR(I) = 0.0
C          RBNVOR(I) = 0.0
C        ELSE
          RBSVOR(I) = NSIG(I)
          RBNVOR(I) = NNOISE(I)
C        ENDIF
C
C end of COA S81-1-104 for RB module
C
 490  CONTINUE
C
C     Output the VOR/ILS ident to the digital keyer
C     ---------------------------------------------
C
C     If station has changed, toggle the Change Flag,  output the ident
C     as four 16 bit words & update status, etc
C
      DO I=1,2
        IF(RBILSIDE(I).NE.PREILIDE(I)) THEN
C
C     Output the ident
          IDE = RBILSIDE(I)
C     left justify ident for digital keyer
C
          JJ = 0
          DO J=1,4
            IF((IDEQ(J).NE.X'20') .OR. (JJ.NE.0))THEN
              JJ = JJ + 1
              RFKIDC05(JJ,I) = IDEQ(J)
            ELSE
              RFKIDC05(5-J,I) = X'0020'
            ENDIF
          ENDDO
C
C     Receiver Type
          RFKRTY05(I) = 2
C
C     Keyer Dot Duration
          RFKDOT05(I) = 100
C
C     Pause Duration
          RFKPAU05(I) = 0
C
C     Set tone frequency
C     2100.512 IS THE DMC FREQ CORRECTION FACTOR
C
          VORTONE(I) = -1020.*2100.512
C
C     Set Repetition period
          RFKREP05(I) = 10000
C
C     Set Associated status
          IF(ILSSTY(I).EQ.2) THEN
            RFKASS05(I) = 255
          ELSE
            RFKASS05(I) = 0
          ENDIF
C
C     Toggle the change flag
          IF(RFKCHM05(I).LT.255) THEN
            RFKCHM05(I) = RFKCHM05(I) + 1
          ELSE
            RFKCHM05(I) = 0
          ENDIF
        ENDIF
C
C     Save the stn ident
        PREILIDE(I) = RBILSIDE(I)
C
C     Set continuous tone status
C
        IF(RBFVILC(I))THEN
          RFKTST05(I) = 2
        ELSE
          RFKTST05(I) = 0
        ENDIF
C
C
C     Set signal levels
        RFKTSL05(I) = RBSVOR(I) * 32767
C
      ENDDO
C
C
C     DME TRANSPONDER: KING KDM706A (PN: 006-1066-23)
C     ---------------
C
C     A DME TRANSPONDER INTERROGATES A DME OR TACAN STATION & COMPUTES THE
C     SLANT RANGE TO THE STATION. IF THE RECEIVER LOSES LOCK, MEMORY MODE
C     IS ENTERED & THE RANGE IS UPDATED FOR APPROXIMATELY 10 SECS USING
C     VELOCITY MEMORY. RANGE OUTPUTS AND FLAGS ARE FED TO DUAL DISPLAY
C     INDICATORS WHICH DISPLAY BLANKS IF THE  DME IS NOT VALID AND DASHES
C     IF NO COMPUTED DATA. DME-1 IS TIED TO NAV PANEL #1 AND DME-2 IS TIED
C     TO NAV PANEL #2.
C
      DME1 = YITIM
C
      DO 700 I=1,2
C
 
C     MODE 1 IS ALWAYS ENABLED (VHF NAV FREQ)
C
        RHDMODE(1,I) = 1
C
C
C     INIT 429 FLAGS
C
        DMEFW(I) = .FALSE.
        DMENCD(I) = .FALSE.
        DMEFT(I) = .FALSE.
        RBDMEM(1,I) = .FALSE.
C
C     DETERMINE THE POWER STATUS OF DME TRANSPONDERS
C
        DMEPWR(I) = BIAA05(I) .AND. .NOT.TF34081(I)
C
C     TRANSPONDER NOT POWERED
C
        IF (.NOT.DMEPWR(I)) THEN
C
C     SET FLAGS & O/P'S  =  0 & INHIBIT MEMORY, SLANT RANGE  =  0, RESET TEST
C     TIMER AND INHIBIT CONTINUOUS TONE IF PREVIOUSLY ON TEST
C
          RBFDME(1,I) = .FALSE.
          PREVIND(I) = 0
          RBTDME(I) = 0
          SLRAN(I) = 0.
          DSIG(I) = 0.
          DNOISE(I) = 0.
          MEMTMR(I) = TIMER(I)
          DELAY(I) = 2.0
          VELOCITY(I) = 0.
          DMEFW(I) = .TRUE.
C !FM+
C !FM  13-Aug-93 04:26:41 S.GOULD
C !FM    < ADDED CHANGES FROM PETER YEE SENT TO CORRECT USAIR SPR 9019 >
C !FM
          DMECHNG(I) = .TRUE.
C
        ELSE
C
C     DME POWERED
C
          IF (RHDFREQ(1,I) .NE. FREQD(I)) THEN
            DMECHNG(I) = .TRUE.
            FREQD(I) = RHDFREQ(1,I)
            RBTDME(I) = FREQD(I)
          ENDIF
C
C     DETERMINE WHETHER A STATION IS IN RANGE
C
          IF (DMECHNG(I) .OR. RVKILFLG) THEN
            CALL FIND_STN (TYP5,FREQD(I),RXB5,RXCFDME,RXCIDME,RXCRDME,
     &         PROLOCDM(I),DSTNREC(I),DINTREC(I),DKILTYP(I))
            DMECHNG(I) = .FALSE.
          ENDIF
C !FM-
C
C     NO STATION IS IN RANGE OR STATION KILLED
C
          IF(IAND(DKILTYP(I),DMETYP).NE.0.OR.DSTNREC(I).EQ.0) THEN
            DSIG(I) = 0.
            DNOISE(I) = 0.1
            MEMTMR(I) = TIMER(I)
C
          ELSE
C
C     SELECTED STATION FOUND IN THE FREQ TABLE REQUEST DATA IF NECESSARY
C
            IF (DSTNREC(I).NE.RBDMEREC(I)) THEN
C
              MEMTMR(I) = TIMER(I)
              IF (RXACCESS(4,I).EQ.0) THEN
                RXACCESS(4,I) = DSTNREC(I)
              ENDIF
              DSIG(I) = 0.
              DNOISE(I) = 0.1
              FTFDME(I) = .TRUE.
C
            ELSE
C
C     COMPLETE DATA IS AVAILABLE DETERMINE THE RANGE
C560
C     MOVE TX CO-ORDINATES TO LOC TX IF IDME IS TUNED
C
              IF (COSLATDM(I) .EQ. 0) FTFDME(I)= .TRUE.
              CALL RNGBRG (RBDMELAT(I),RBDMELON(I),RUPLAT,
     &             RUPLON,COSLATDM(I),FTFDME(I),DRAN(I),DBRG(I))
C
C     DETERMINE THE SIGNAL STRENGTH
C
              CALL RSIGSTR (5,RBDMERAN(I),DRAN(I),RBDMEELE(I),DSIG(I),
     &             DNOISE(I))
            ENDIF
          ENDIF
C
C     620
C     CHECK FOR TEST
C
          IF (IDLPDM1T(I).AND..NOT.PREVTEST(I)) DMETTMR(I)=0.
          PREVTEST(I) = IDLPDM1T(I)
          IF (DMETTMR(I).LT.15.4) DMETTMR(I)=DMETTMR(I)+DME1
          IF (DMETTMR(I).LT.2.0) THEN
C
C     BLANK TEST IN PROGRESS: FORCE DELAY ON TERMINATION
C     RESET VELOCITY MEMORY, SET DME FLAG INVALID
C     SET ZERO SLANT RANGE.
C
            PREVIND(I) = 0
            VELOCITY(I) = 0.
            RBFDME(1,I) = .FALSE.
            SLRAN(I) = 0.
            RBRDME(1,I) = 0.
C
          ELSE IF (DMETTMR(I).LT.4.0) THEN
C
C     DASH TEST IN PROGRESS: SET DME FLAG VALID
C     DISPLAY DASHES AS NCD IS SET
C
            RBFDME(1,I) = .TRUE.
            RBDNCD(1,I) = .TRUE.
C
          ELSE IF (DMETTMR(I).LT.15.4) THEN
C
C     ZEROES TEST IN PROGRESS: TEST AUDIO (NO AUDIO TEST ON THIS RCVR)
C     RESET VEL. MEM. STORED RANGE AND DISPLAY ZEROES
C
            DSIG(I) = 1.0
            DNOISE(I) = 0.
            RBDNCD(1,I) = .FALSE.
            OLDRAN(I) = 0.
          ENDIF
C
          IF (DMETTMR(I).GE.15.4.AND..NOT.IDLPDM1T(I)) THEN
C
C     TEST IS FINISHED OR NOT IN TEST
C
C     RECEIVER CAN PROCESS SIGNALS ; SET DME FLAG VALID
C
            RBFDME(1,I) = .TRUE.
C
            IF (PREVIND(I).NE.DSTNREC(I)) THEN
              PREVIND(I) = DSTNREC(I)
C
C     LOCK-ON DELAY OBTAIN A RANDOM DELAY
C
              DELAY(I) = 5.0 * ABS(YLGAUSN(I))
              IF (DELAY(I).GT.2.5) THEN
                DELAY(I) = 2.5
              ELSE IF (DELAY(I).LT.1.0) THEN
                DELAY(I) = 1.0
              ENDIF
C
            ENDIF
C
C     SET NCD CONDITION IF DELAY ACTIVE
C
            IF (DELAY(I).GE.0) DELAY(I) = DELAY(I)-DME1
C
C     LOCK-ON DELAY EXPIRED, ANALYZE REC'D SIGNAL
C     IF NO SIGNAL REC'D ENTER VEL. MEM. MODE
C
C     RESET MEMORY TIMER & UPDATE VELOCITY MEMORY EACH 1.5 SECONDS
C
            IF (SLRAN(I).LT.MAXRAN .AND. DSIG(I).GT.0.
     &           .AND. DELAY(I).LT.0) THEN
C
              SLRAN(I) = SQRT(DRAN(I)**2+
     &             2.7086E-8*(VHS-RBDMEELE(I))**2)
C
C     DME TRANSMITTER BIAS
C
              SLRAN(I) = SLRAN(I) - RBDMEDME(I)*0.1
C
              MEMTMR(I) = 0.0
              IF (INTERVAL(I).GT.1.5) THEN
                VELOCITY(I) = (SLRAN(I)-OLDRAN(I))*DME1/INTERVAL(I)
                RBRRT(1,I) = ABS(VELOCITY(L)*(3600.))/DME1
                IF(RBRRT(1,I).NE.0.)THEN
                  RBTTGO(1,I)=SLRAN(L)/RBRRT(1,I)
                ENDIF
                INTERVAL(I) = DME1
                OLDRAN(I) = SLRAN(I)
              ELSE
                INTERVAL(I) = INTERVAL(I) + DME1
              ENDIF
            ELSE
C
C     SIGNAL IS INSUFFICIENT (MEMORY MODE)
C
              IF (MEMTMR(I).LT.TIMER(I)) THEN
C
                MEMTMR(I) = MEMTMR(I) + DME1
C
C     USE THE VELOCITY MEMORY TO UPDATE THE RANGE
C
                SLRAN(I) = SLRAN(I) + VELOCITY(I)
                RBDMEM(1,I) = .TRUE.
C
              ELSE
C
C     MEMORY TIME HAS EXPIRED INDICATE DASHES
C
                MEMTMR(I) = TIMER(I)
                DMENCD(I) = .TRUE.
              ENDIF
            ENDIF
C
C     LIMIT DME RATE TO 10 MILES/SEC
C
            DMERATE(I) = SLRAN(I) - RBRDME(1,I)
            IF (ABS(DMERATE(I)).GE.1.0) DMENCD(I)=.TRUE.
C
            SP1R = DME1 * 10.0
            IF (DMERATE(I).GT.SP1R) THEN
              DMERATE(I) = SP1R
            ELSE IF (DMERATE(I).LT.-SP1R) THEN
              DMERATE(I) = -SP1R
            ENDIF
C
C     UPDATE RANGE
C
            TEMPRAN(I) = TEMPRAN(I) + DMERATE(I)
            IF (TEMPRAN(I) .LT. 0.0) THEN
              RBRDME(1,I) = MAXRAN + TEMPRAN(I)
            ELSE
              RBRDME(1,I) = TEMPRAN(I)
            ENDIF
          ENDIF
        ENDIF
C
C     ARINC 429 OUTPUTS
C
        RBDFW(1,I) = DMEFW(I)
        RBDNCD(1,I) = .NOT.RBDFW(1,I) .AND. DMENCD(I)
C
C
C     KEYER DATA
C
        REDMEIDE(I) = RBDMEDID(I)
        REDMESTY(I) = RBDMESTY(I)
        LL(I) = I
C
C     O/P RANGE AND BEARING TO I/F
C
        IF (DSIG(I).EQ.0.) THEN
          RBIFRD(I) = 0
          RBIFBD(I) = 0
        ELSE
          RBIFRD(I) = DRAN(I)*10.
          IF (DBRG(I).LT.0.) THEN
            RBIFBD(I) = 3600. + DBRG(I)*10.
          ELSE
            RBIFBD(I) = DBRG(I) * 10.
          ENDIF
        ENDIF
C
C     OUTPUT THE AUDIO CONTROL GATE
C
        RBGOD(I) = DSIG(I).GT.0.
C
C
C     HSI DISTANCE DISPLAY SERIAL DATA FORMAT
C     ---------------------------------------
C
C     (NOTE: IPASSALS ARE BEING USED)
C
        RANGE(I) = X'81'
        IF((I.EQ.1).AND.SLRNAVSL(1)) THEN
          RRDAT(I) = UBX017A * 1000
        ELSE
          RRDAT(I) = RBRDME(1,I) * 1000
        ENDIF
        DO M=1,6
          RANGE(I) = RANGE(I) + HEX(M)*MOD((RRDAT(I)/DEC(M)),10)
        ENDDO
 
        IF(SLRNAVSL(1)) THEN
          IF (.NOT.UBZ017A0) RANGE(I)=RANGE(I)+X'80000000'
        ELSE
          IF (RBDNCD(1,I)) RANGE(I)=RANGE(I)+X'80000000'
          IF (RBDFW(1,I))  RANGE(I)=RANGE(I)+X'C0000000'
        ENDIF
        RDX201A(I) = RANGE(I)
C
 700  CONTINUE
C
CD    Output to the digital keyer
C     ---------------------------
C
C     If station has changed, toggle the Change Flag,  output the ident
C     as four 16 bit words & update status, etc
C
      DO I=1,2
        IF(RBDMEIDE(LL(I)).NE.PREDMIDE(I)) THEN
C
C     Output the ident
          IDE = RBDMEDID(LL(I))
C     left justify ident for digital keyer
C
          JJ = 0
          DO J=1,4
            IF((IDEQ(J).NE.X'20') .OR. (JJ.NE.0))THEN
              JJ = JJ + 1
              RFKIDC0A(JJ,I) = IDEQ(J)
            ELSE
              RFKIDC0A(5-J,I) = X'0020'
            ENDIF
          ENDDO
C
C     Receiver Type
          RFKRTY0A(I) = 4
C
C     Keyer Dot Duration
          RFKDOT0A(I) = 100
C
C     Pause Duration After first character
          IF(RFKIDC0A(1,I).EQ.X'0049')THEN
            RFKPAU0A(I) = 1000
          ELSE
            RFKPAU0A(I) = 0
          ENDIF
C
C     Set tone
C     2100.512 is the DMC freq correction factor
C
          DMETONE(I) = -1350.*2100.512
C
C     Set repetition period
          RFKREP0A(I) = 40000
C
C     Set associated status
          IF(DMESTY(LL(I)).EQ.2) THEN
            RFKASS0A(I) = 255
          ELSE
            RFKASS0A(I) = 0
          ENDIF
C
C     Toggle the change flag
C
          IF(RFKCHM0A(I).LT.255) THEN
            RFKCHM0A(I) = RFKCHM0A(I) + 1
          ELSE
            RFKCHM0A(I) = 0
          ENDIF
C
C
        ENDIF
C
C     Save the stn index
C
        PREDMIDE(I) = RBDMEIDE(LL(I))
C
C
C
        IF ( RBGOD(I) )THEN
C
C     Set signal levels
C
          RFKTSL0A(I) = DSIG(LL(I)) * 32767
        ELSE
          RFKTSL0A(I) = 0
        ENDIF
C
C     Set continuous tone status
C
        IF(RBFDMEC(I))THEN
          RFKTST0A(I) = 2
        ELSE
          RFKTST0A(I) = 0
        ENDIF
C
      ENDDO
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01449 Output to the digital keyer
C
