/* $ScmHeader: 999602266v73759v6Cvu999999978&8|@ $*/
/* $Id: cae.h,v 1.1 2000/05/10 22:23:57 avnhw(MASTER_VERSION|ANY_DB) Exp $*/
/*  cae.h - CAE standard types, constant, ... */

#if !defined( CAE_HEADER )

#define CAE_HEADER 1

/* CDB type */
typedef  signed char            sint1;
typedef  char                    int1;
typedef  short int               int2;
typedef  long                    int4;
typedef  unsigned char           log1;
typedef  unsigned char          uint1;
typedef  unsigned short int     uint2;
typedef  unsigned int           uint4;
typedef  float                   real;

typedef sint1 SINT1;
typedef  int1  INT1 ;
typedef  int2  INT2 ;
typedef  int4  INT4 ;
typedef  log1  LOG1 ; 
typedef uint1 UINT1 ;
typedef uint2 UINT2 ;
typedef uint4 UINT4 ;
typedef  real  REAL ;

typedef  LOG1  DISC[20] ;

typedef unsigned char boolean;

/* language constant */

#define  false         0
#define  true          1

#define  FALSE         0
#define  TRUE          1

#endif
