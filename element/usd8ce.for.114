C*********************************************************************
C
C'Title                ELEVATOR CONTROL SYSTEM
C'Module_ID            USD8CE
C'Entry_point          CELEVATOR
C'Documentation
C'Customer             US Air
C'Application          Simulation of DHC8-100 elevator system
C'Author               <PERSON>'Date                 July 1991
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       33 msec
C'Process              Synchronous process
C
C***********************************************************************
C
C'Revision_history
C
C  usd8ce.for.55  8Oct2019 20:49 usd8 Tom
C       < adjusted ce_aprate a little more >
C
C  usd8ce.for.54 20Mar2019 21:20 usd8 Tom
C       < Adjusted ce_aprate to stop or reduce porpoising >
C
C  usd8ce.for.53 16Feb1993 13:59 usd8 SBW
C       < added CETRIM calc for 300 and cleaned up doc >
C
C  usd8ce.for.52 18Dec1992 04:08 usd8 sbw
C       < added engine speed as transfer for friction >
C
C  usd8ce.for.51 17Dec1992 08:57 SBW
C       < usd8 comp helv for non linearities in CELV >
C
C  usd8ce.for.50 17Dec1992 06:03 usd8 sbw
C       < modified spos to celv gearing >
C
C  usd8ce.for.48 29Jul1992 17:19 usd8 paul va
C       < add gains and limit to adjust mass unbalance >
C
C  usd8ce.for.46 12Jul1992 18:42 usd8 sbw
C       < disabled force calc if cl off >
C
C  usd8ce.for.45 10Jul1992 00:24 usd8 sbw
C       < added prop vibration on column >
C
C  usd8ce.for.40 10Jun1992 02:02 usd8 sbw
C       < added cetrimdg label >
C
C  usd8ce.for.36  9Jun1992 02:37 usd8 SBW
C       < ADDED MASSUNBALANCE TERMS >
C
C  usd8ce.for.28  4Apr1992 11:39 usd8 sbw
C       < smoothed elevator ap ip >
C
C  usd8ce.for.19 18Mar1992 00:09 usd8 sbw
C       < made both elevs backdrive in mode 1 >
C
C  usd8ce.for.18 11Mar1992 18:44 usd8 sbw
C       < added variable rate on trim  >
C
C File: /cae1/ship/usd8ce.for.2
C       Modified by: sbw
C       Tue Dec  3 11:55:07 1991
C       < from upstairs >
C
C File: /cae1/ship/usd8ce.for.19
C       Modified by: SBW
C       Wed Nov 27 15:54:14 1991
C       < INSTALLED ON SITE >
C
C
C'References
C
C  1) DASH 8 Series 100 Maintenance Manual ATA 27.
C
C  2) DASH 8 Series 100 Operating Data.
C
C  3) DASH 8 Series 300 Maintenance Manual ATA 27.
C
C  4) DASH 8 Series 300 Operating Data.
C
C,
C
C
C    ===================
      SUBROUTINE USD8CE
C    ===================
C
C
      IMPLICIT NONE
C
      INCLUDE 'disp.com'
C
C     ================
C      XREF VARIABLES
C     ================
C
C  CIRCUIT BREAKERS
C
CPI    USD8  BILH07,
C
C  INTERFACE
C
CPI  I  IDCEFNU , IDCEFND , IDCECNU , IDCECND , IDCETARM, IDCESTSW,
CPI  I  IACETPOS, IDCEDISC,
C
C  MALFUNCTIONS
C
CPI  T  TF27151, TF27031, TF27032, TF27041, TF27042, TF27141,
CPI  T  TF27221, TF27222, TF71301, TF71302,
C
C  FLIGHT BACKDRIVE
C
CPI  H  HCEMODE, HCETMODE , HCOL , HELV, HCETRIM ,YITAIL , HCENOFRI ,
C
C  FLIGHT
C
CPI  C  VDYNPR    ,VM ,VUGD ,VALPHA    ,IACGLOCK  , VAXB , VAZB, ENP,
CPI  C  VFLAPS    ,
C
C  AUTOPILOT
C
CPI  C  SPELEV    , SLSRVENG , STRMDNC, STRMUPC , STRIMENG ,
C
C  C30 TRANSFERS IN
C
CPI  C  CIECFPOS, CIECSPOS, CIEFSPOS, CIESPR5, CIESPR6, CIECAFOR,
CPI  C  CIEFAFOR ,CIESPR2, CIESPR7, CIETSPOS, CIETFPOS, CIECDPOS,
CPI  C  CLSCLON,
C
C  SYSTEM
C
CPI  C  MTHPUT,
C
C  INTERNAL
C
CP   C  CETND, CETNU, CEFORCE,CEATGON,CETRIMF,CECOLUMN,CEOFST,
C
C  OUTPUTS
C
CPO  C  CELVL, CELVR, CEHMELV , CETRIM , CETRIMDG ,
C
C  C30 TRANSFERS OUT
C
CPO  C  CE$CBON,CE$CBPOS, CE$FBON,CE$FBPOS,CE$AZB, CE$AXB,
CPO  C  CE$TBON,CE$TBPOS,CE$TCLUT,
CPO  C  CE$TAPNU, CE$TAPND, CE$MALF  ,CE$APENG ,CE$APCH  ,
CPO  C  CE$YTAIL, CE$DYNPR, CE$MACH  ,
CPO  C  CE$ALPHA, CE$GLOCK, CE$SPELV,
CPO  C  CE$NOFRI, CE$TUNE, CE$ISPR, CE$SPR0, CE$SPR1,CE$SPR2,
C
C  INTERFACE OUT
C
CPO  C  CE$RCPI, CE$LCPI
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  8-Oct-2019 20:50:06
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  CECOLUMN       ! COLUMN POSITION (WRT VERTICAL)
     &, CEFORCE        ! COLUMN FORCE
     &, CEOFST         ! COLUMN OFFSET FOR OVP
     &, CETRIMF        ! ELEVATOR TRIM WHEEL FORCE
     &, CIECAFOR       ! CAPT COLUMN ACTUAL FORCE               [LBS]
     &, CIECDPOS       ! CAPT COLUMN DEMANDED POSITION          [DEG]
     &, CIECFPOS       ! CAPT COLUMN FOKKER POSITION            [DEG]
     &, CIECSPOS       ! LEFT ELEVATOR SURFACE POSITION         [DEG]
     &, CIEFAFOR       ! F/O COLUMN ACTUAL FORCE                [LBS]
     &, CIEFSPOS       ! RIGHT ELEVATOR SURFACE POSITION        [DEG]
     &, CIESPR2        ! PITCH C30 SPARE 2
     &, CIESPR5        ! PITCH C30 SPARE 5
     &, CIESPR6        ! PITCH C30 SPARE 6
     &, CIESPR7        ! PITCH C30 SPARE 7
     &, CIETFPOS       ! PITCH TRIM WHEEL FOKKER POSITION       [DEG]
     &, CIETSPOS       ! PITCH TRIM TAB SURFACE POSITION        [DEG]
     &, ENP(2)         ! PHYSICAL PROPELLER SPEED                 [%]
     &, HCETRIM        ! ELEVATOR TRIM BACKDRIVE COMMAND
     &, HCOL           ! MODE 2 COLUMN        COMMAND (+AFT)    [deg]
     &, HELV           ! MODE 1 ELEVATOR      COMMAND (+TED)    [deg]
     &, IACETPOS       ! ELEVATOR TRIM POSITION                AI016
     &, IACGLOCK       ! CONTROL LOCK LEVER POSITION           AI069
     &, SPELEV         ! elevator servo command                 [deg]
     &, STRMDNC        ! elevator trim tab nose down cmd        [sec]
     &, STRMUPC        ! elevator trim tab nose up cmd          [sec]
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VAXB           ! BODY AXES TOTAL X ACC.             [ft/s**2]
     &, VAZB           ! BODY AXES TOTAL Z ACC.             [ft/s**2]
     &, VDYNPR         ! DYNAMIC PRESSURE                 [lbs/ft**2]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VM             ! MACH NUMBER
      REAL*4
     &  VUGD           ! BODY AXES VAXB + GRAVITY           [ft/s**2]
C$
      INTEGER*4
     &  HCEMODE        ! ELEVATOR   BACKDRIVE MODE
     &, HCETMODE       ! ELEVATOR TRIM BACKDRIVE MODE
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  BILH07         ! ELEV TRIM STBY              27 PDLES  DI206D
     &, CEATGON        ! PITCH CTS TEST ON
     &, CETND          ! PITCH TRIM AND CMD
     &, CETNU          ! PITCH TRIM ANU CMD
     &, CLSCLON        ! CONTROL LOADING ON
     &, HCENOFRI       ! ELEVATOR  FRICTION INHIBIT
     &, IDCECND        ! CAPT STBY ELEV TRIM NOSE DOWN         DI0352
     &, IDCECNU        ! CAPT STBY ELEV TRIM NOSE UP           DI0351
     &, IDCEDISC       ! PITCH DISCONNECT                      DI0640
     &, IDCEFND        ! F/O  STBY ELEV TRIM NOSE DOWN         DI034D
     &, IDCEFNU        ! F/O  STBY ELEV TRIM NOSE UP           DI034C
     &, IDCESTSW       ! STANDBY ELEV TRIM SW                  DI0011
     &, IDCETARM       ! STANDBY ELEVATOR TRIM ARMING SW       DI0350
     &, MTHPUT         ! THROUGHPUT DELAY MODE FLAG
     &, SLSRVENG(2)    ! servo engage flag (pitch & roll)
     &, STRIMENG       ! automatic pitch trim engage flag
     &, TF27031        ! ELEVATOR CONTROL JAM LEFT
     &, TF27032        ! ELEVATOR CONTROL JAM RIGHT
     &, TF27041        ! ELEVATOR SURFACE JAM LEFT
     &, TF27042        ! ELEVATOR SURFACE JAM RIGHT
     &, TF27141        ! PITCH TRIM FAIL
     &, TF27151        ! STANDBY PITCH TRIM FAIL
     &, TF27221        ! PITCH TRIM RWY NU
     &, TF27222        ! PITCH TRIM RWY ND
     &, TF71301        ! PROPELLER VIBRATION LEFT
     &, TF71302        ! PROPELLER VIBRATION RIGHT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  CE$ALPHA       ! WING ANGLE OF ATTACK                   [DEG]
     &, CE$AXB         ! X-AXES A/C ACCELERATION         [FT/SEC/SEC]
     &, CE$AZB         ! Z-AXES A/C ACCELERATION         [FT/SEC/SEC]
     &, CE$CBPOS       ! CAPT ELEVATOR BACKDRIVE COMMAND        [DEG]
     &, CE$DYNPR       ! DYNAMIC PRESSURE                       [PSI]
     &, CE$FBPOS       ! F/O ELEVATOR BACKDRIVE COMMAND         [DEG]
     &, CE$GLOCK       ! CONTROL LOCK LEVER POSITION
     &, CE$LCPI        ! LEFT ELEVATOR SURFACE POSITION        SO031
     &, CE$MACH        ! A/C MACH NUMBER
     &, CE$RCPI        ! RIGHT ELEVATOR SURFACE POSITION       SO030
     &, CE$SPELV       ! A/P ELEVATOR COMMAND                   [DEG]
     &, CE$SPR0        ! PITCH SPARE 0
     &, CE$SPR1        ! PITCH SPARE 1
     &, CE$SPR2        ! PITCH SPARE 2
     &, CE$TBPOS       ! PITCH TRIM BACKDRIVE COMMAND           [DEG]
     &, CEHMELV        ! ELEV. HINGE MOMENT - A/P
     &, CELVL          ! LEFT ELEVATOR POSITION
     &, CELVR          ! RIGHT ELEVATOR POSITION
     &, CETRIM         ! Elevator Trim Tab position (NRC)
     &, CETRIMDG       ! ELEVATOR TRIM WHEEL POSITION (DEG)
C$
      INTEGER*4
     &  CE$APCH        ! NUMBER OF AUTOPILOT CHANNELS ENGAGED
     &, CE$APENG       ! ELEVATOR AUTOPILOT SERVO ENGAGD
     &, CE$CBON        ! CAPT ELEVATOR BACKDRIVE MODE
     &, CE$FBON        ! F/O ELEVATOR BACKDRIVE MODE
     &, CE$ISPR        ! ELEVATOR TRANSFER SPARE - INTEGER
     &, CE$MALF        ! ELEVATOR MALFUNCTIONS
     &, CE$TBON        ! PITCH TRIM BACKDRIVE MODE
     &, CE$TUNE        ! ELEVATOR DYNAMIC TUNING FLAG
     &, CE$YTAIL       ! ELEVATOR TAIL NUMBER CONFIGUARTION OPTION
C$
      LOGICAL*4
     &  CE$NOFRI       ! ELEVATOR FRICTION INHIBIT
     &, CE$TAPND       ! STBY OR A/P PITCH TRIM AND CMD
     &, CE$TAPNU       ! STBY OR A/P PITCH TRIM ANU CMD
     &, CE$TCLUT       ! A/P PITCH TRIM CLUTCH ENGAGED
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(5576),DUM0000003(5948)
     &, DUM0000004(999),DUM0000005(1045),DUM0000006(689)
     &, DUM0000007(2382),DUM0000008(104),DUM0000009(24)
     &, DUM0000010(416),DUM0000011(36),DUM0000012(20)
     &, DUM0000013(4504),DUM0000014(28),DUM0000015(148)
     &, DUM0000016(52),DUM0000017(8),DUM0000018(8)
     &, DUM0000019(3487),DUM0000020(8),DUM0000021(8)
     &, DUM0000022(4),DUM0000023(16),DUM0000024(4)
     &, DUM0000025(12),DUM0000026(16),DUM0000027(8)
     &, DUM0000028(428),DUM0000029(4),DUM0000030(8)
     &, DUM0000031(4),DUM0000032(4),DUM0000033(4),DUM0000034(4)
     &, DUM0000035(384),DUM0000036(4727),DUM0000037(1)
     &, DUM0000038(874),DUM0000039(868),DUM0000040(1204)
     &, DUM0000041(4),DUM0000042(50),DUM0000043(64425)
     &, DUM0000044(220798),DUM0000045(249)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,CE$RCPI,CE$LCPI,DUM0000003
     &, IACETPOS,IACGLOCK,DUM0000004,IDCESTSW,IDCEFNU,IDCEFND
     &, IDCETARM,IDCECNU,IDCECND,IDCEDISC,DUM0000005,BILH07,DUM0000006
     &, MTHPUT,DUM0000007,VFLAPS,DUM0000008,VAZB,DUM0000009,VALPHA
     &, DUM0000010,VAXB,VUGD,DUM0000011,VM,DUM0000012,VDYNPR
     &, DUM0000013,HCEMODE,DUM0000014,HELV,DUM0000015,HCOL,DUM0000016
     &, HCETMODE,DUM0000017,HCETRIM,DUM0000018,HCENOFRI,DUM0000019
     &, CIECSPOS,DUM0000020,CIEFSPOS,DUM0000021,CIECDPOS,DUM0000022
     &, CIECFPOS,CIECAFOR,DUM0000023,CIEFAFOR,DUM0000024,CIETSPOS
     &, DUM0000025,CIETFPOS,DUM0000026,CIESPR2,DUM0000027,CIESPR5
     &, CIESPR6,CIESPR7,DUM0000028,CE$MALF,CE$APENG,CE$APCH,CE$TUNE
     &, CE$YTAIL,CE$CBON,CE$FBON,CE$TBON,CE$NOFRI,DUM0000029
     &, CE$TAPND,CE$TAPNU,CE$TCLUT,DUM0000030,CE$ISPR,CE$ALPHA
     &, CE$MACH,CE$DYNPR,CE$AXB,CE$AZB,CE$GLOCK,DUM0000031,CE$SPELV
     &, DUM0000032,CE$CBPOS,DUM0000033,CE$FBPOS,DUM0000034,CE$TBPOS
     &, CE$SPR0,CE$SPR1,CE$SPR2,DUM0000035,CLSCLON,DUM0000036
     &, CELVL,CELVR,CEHMELV,CEFORCE,CETRIM,CETND,CETNU,CEATGON
     &, DUM0000037,CETRIMDG,CETRIMF,CECOLUMN,CEOFST,DUM0000038
     &, SLSRVENG,DUM0000039,SPELEV,DUM0000040,STRMDNC,DUM0000041
     &, STRMUPC,DUM0000042,STRIMENG,DUM0000043,ENP,DUM0000044
     &, TF27151,TF27031,TF27032,TF27041,TF27042,TF27141,TF27221
     &, TF27222,DUM0000045,TF71301,TF71302
C------------------------------------------------------------------------------
C
C       ---------------
C       Local Variables
C       ---------------
C
C
      REAL*4
     - CE_TAPL/10.0/,			! Pitch trim clutch force breakout
     - CE_TAPR/1.0/,                 ! Pitch trim nominal rate
     - CE_ANLM/-30.0/,			! Elev aft neg limit
     - CE_APLM/20.0/,			! Elev aft pos limit
     - CE_FNLM/-8.0/,			! Elev aft neg limit
     - CE_FPLM/11.0/,			! Elev aft pos limit
     - CE_TANLM/-30.0/,		! Pitch trim  aft neg limit
     - CE_TAPLM/30.0/, 		! Pitch trim  aft pos limit
C     - ce_aprate/1./ ,           ! Rate gain on AP
     - ce_aprate/.74/,          ! reduced to prevent porpoising
     - ce_qtpfad /.1/,            ! Throughput fade in for dynpr
     - ce_grav /-32.0/            ! gravity
      REAL*4     ce_spelev(4)            ! averaging
      REAL*4     ce_speleva              ! averaging
      real*4     ce_axblim/16/           ! mass unbalance x acc limit
      real*4     ce_ugdg/1./             ! mass unbalance x acc gain
      real*4     ce_axbg/.0/             ! mass unbalance x acc gain

      Logical  CE_FIRST/.true./

      INTEGER*4  BIT1,BIT2,BIT3,BIT4,BIT5,
     +           BIT6,BIT7,BIT8,BIT9,BIT10,
     +           BIT11,BIT12,BIT13,BIT14,BIT15,
     +           BIT16,BIT17,BIT18,BIT19,BIT20,
     +           BIT21,BIT22,BIT23,BIT24,BIT25,
     +           BIT26,BIT27,BIT28,BIT29,BIT30,
     +           BIT31,BIT32, ce_malf


      INTEGER*4  ISPELEV                 ! Loop counter index

      PARAMETER  (BIT1  = '00000001'X, BIT17 = '00010000'X,
     +           BIT2  = '00000002'X, BIT18 = '00020000'X,
     +           BIT3  = '00000004'X, BIT19 = '00040000'X,
     +           BIT4  = '00000008'X, BIT20 = '00080000'X,
     +           BIT5  = '00000010'X, BIT21 = '00100000'X,
     +           BIT6  = '00000020'X, BIT22 = '00200000'X,
     +           BIT7  = '00000040'X, BIT23 = '00400000'X,
     +           BIT8  = '00000080'X, BIT24 = '00800000'X,
     +           BIT9  = '00000100'X, BIT25 = '01000000'X,
     +           BIT10 = '00000200'X, BIT26 = '02000000'X,
     +           BIT11 = '00000400'X, BIT27 = '04000000'X,
     +           BIT12 = '00000800'X, BIT28 = '08000000'X,
     +           BIT13 = '00001000'X, BIT29 = '10000000'X,
     +           BIT14 = '00002000'X, BIT30 = '20000000'X,
     +           BIT15 = '00004000'X, BIT31 = '40000000'X,
     +           BIT16 = '00008000'X, BIT32 = '80000000'X)
C
C
C     ---------------
      ENTRY CELEVATR
C     ---------------
C
C  -------------------------------
CD CE010 FIRST PASS INITIALIZATION
C  -------------------------------
C
      IF (ce_first) THEN
        ce_first = .FALSE.
        CE$TUNE = .FALSE.
      ENDIF
C
C  ----------------------------
CD CE020 ATG BACKDRIVE ELEVATOR
C  ----------------------------
C
C  Backdrive modes:  0 - backdrive off
C                    1 - backdrive to commanded surface position
C                    2 - backdrive to commanded control position
C
      IF (HCEMODE.EQ.1) THEN
        CE$CBON = 1
        CE$FBON = 1

        IF (YITAIL.EQ.226) THEN
          CE$FBPOS = HELV
        ELSE
          IF (HELV.GT.10) THEN    ! NON LINEARITIES IN 300
            CE$FBPOS = (HELV - 10.)*(1./10.) * (12.) + 10.
          ELSEIF (HELV .LT.-16) THEN    ! NON LINEARITIES IN 300
            CE$FBPOS = (HELV + 16.)*(1./13.) * (14.) - 15.
          ELSEIF (HELV.LT.0.) THEN    ! NON LINEARITIES IN 300
            CE$FBPOS = HELV*(1./16.) * (15.)
          ELSE
            CE$FBPOS = HELV
          ENDIF
        ENDIF
        CE$CBPOS = CE$FBPOS

        IF (CE$CBPOS.GT.ce_aplm) CE$CBPOS =  ce_aplm
        IF (CE$CBPOS.LT.ce_anlm) CE$CBPOS =  ce_anlm
      ELSE IF (HCEMODE.EQ.2) THEN
        CE$CBON = 2
        CE$CBPOS = HCOL
        IF (CE$CBPOS.GT. ce_fplm) CE$CBPOS =  ce_fplm
        IF (CE$CBPOS.LT. ce_fnlm) CE$CBPOS =  ce_fnlm
      ELSE
        CE$FBON = 0
        CE$CBON = 0
      ENDIF
C
C  ------------------------------
CD CE030 ATG BACKDRIVE PITCH TRIM
C  ------------------------------
C
C  Backdrive modes :  0 - backdrive off
C                     1 - backdrive to commanded surface position
C
      IF ((HCETMODE.EQ.1).OR.(HCETMODE.EQ.3).OR.(HCETMODE.EQ.4))  THEN
        CE$TBON = 1
        CE$TBPOS = HCETRIM
        IF (CE$TBPOS.GT.ce_taplm) CE$TBPOS =  ce_taplm
        IF (CE$TBPOS.LT.ce_tanlm) CE$TBPOS =  ce_tanlm
      ELSEIF (HCETMODE.EQ.2) THEN
        CE$TBON = 2
        CE$TBPOS = HCETRIM
        IF (CE$TBPOS.GT.ce_taplm) CE$TBPOS =  ce_taplm
        IF (CE$TBPOS.LT.ce_tanlm) CE$TBPOS =  ce_tanlm
      ELSE
        CE$TBON = 0
      ENDIF
C
C  -----------------
CD CE050 PITCH TRIM
C  -----------------
C
C  Pitch Trim Switch Commands
C  --------------------------
C
      IF (BILH07.AND.IDCETARM.AND..NOT.TF27151) THEN      ! Trim circuit power
        CE$SPR0 = 1.                                 ! use nominal trim rate
        CE$TCLUT = .TRUE.
        CETNU = (IDCECNU.OR.IDCEFNU.OR.TF27221) .AND..NOT.
     $          (IDCECND.OR.IDCEFND)
        CETND = (IDCECND.OR.IDCEFND.OR.TF27222) .AND..NOT.
     $          (IDCECNU.OR.IDCEFNU)
C
C  Pitch Trim A/P Commands
C  --------------------------
C
      ELSEIF (STRIMENG) THEN
        CE$TCLUT = .TRUE.
        CE$SPR0 = (STRMUPC + STRMDNC)*ce_aprate    ! this is gain on rate
        IF (TF27221.OR.TF27222) THEN
          CE$SPR0 = 1.                              ! use nominal trim rate
          CETNU = (STRMUPC.NE.0).OR.TF27221
          CETND = (STRMDNC.NE.0).OR.TF27222
        ELSE
          CETNU = (STRMUPC.NE.0)
          CETND = (STRMDNC.NE.0)
        ENDIF
      ELSEIF (.NOT.CEATGON) THEN
        CE$SPR0 = 0.                                 ! use no trim rate
        CE$TCLUT = .FALSE.
        CETND = .FALSE.
        CETNU = .FALSE.
      ENDIF
      CEHMELV = CIESPR2

C  -------------------------------------
CD CE090 FORCE AND POSITION CALCULATION
C  -------------------------------------

      IF (CLSCLON) THEN
        IF (HCEMODE.NE.0) THEN
          CEFORCE = -CIESPR5*(1/2.48)            ! 1/COL LENGTH
        ELSE
          CEFORCE = CIECAFOR + CIEFAFOR
        ENDIF

        CECOLUMN = CIECDPOS + CEOFST
      ENDIF

C  ---------------------------
CD CE100 C30 - LABELS XFER
C  ---------------------------
C
CR CAE Calculation
CC
CC The information required by the C30 code are assigned into the transfer
CC labels. They are sent to the C30 card during the beginning of each
CC host iteration.
CC
      ce_malf = 0
C
C Elevator malfunction
C
      IF (TF27031) ce_malf = IOR(ce_malf,BIT1)      ! Left fwd jam
      IF (TF27032) ce_malf = IOR(ce_malf,BIT2)      ! Right fwd jam
      IF (TF27041) ce_malf = IOR(ce_malf,BIT3)      ! left surface jam
      IF (TF27042) ce_malf = IOR(ce_malf,BIT4)      ! right surface jam
C
C Pitch trim malfunctions
C
      IF (TF27141) ce_malf = IOR(ce_malf,BIT5)      ! Pitch trim cable break
C
C Prop vibration malfunctions
C
      IF (TF71301.AND.ENP(1).GT.50.) ce_malf=IOR(ce_malf,BIT6) !left prop vibr
      IF (TF71302.AND.ENP(2).GT.50.) ce_malf=IOR(ce_malf,BIT7) !right prop vibr
C
      CE$TAPNU = CETNU
      CE$TAPND = CETND

C Set if Pitch disconnect

      IF (.NOT.CEATGON) THEN
        CE$ISPR = 0
        IF (.NOT.IDCEDISC) CE$ISPR = IOR(CE$ISPR,BIT1)
      ENDIF

      CE$MALF  = ce_malf                                   !
      CE$APENG = SLSRVENG(1).OR.SLSRVENG(2)                ! Autopilot ai
      CE$APCH  = 0                                         ! Number of ai
      CE$YTAIL = YITAIL                                    ! Ship configur

C
C Dynamic pressure calculation:
C
C Dynmic Pressure is artificially inserted
C to assist in control centering in through put delay mode.

      IF (MTHPUT) THEN
        IF (CE$DYNPR.LT.50.) CE$DYNPR = CE$DYNPR + ce_qtpfad
      ELSE
        CE$DYNPR = VDYNPR
      ENDIF
C
C Limit flight acceleration inputs
C
      CE$AXB   = VAXB * ce_axbg + VUGD * ce_ugdg
      IF (CE$AXB.GT.ce_axblim)  CE$AXB =  ce_axblim
      IF (CE$AXB.LT.-ce_axblim) CE$AXB = -ce_axblim

      CE$AZB   = VAZB - ce_grav
      IF (CE$AZB.LT.-16) CE$AZB = -16
      IF (CE$AZB.GT. 16) CE$AZB =  16

      CE$MACH  = VM                                        ! Mach number
      CE$ALPHA = VALPHA                                    ! Angle of atta
      IF (.NOT.CEATGON) CE$GLOCK = IACGLOCK                ! Gust lock pos

C
C  Elevator surface command from auto pilot is smoothed as to
C  Compensate for the difference iteration rates for AP and CF
C
      ISPELEV = ISPELEV + 1
      IF (ISPELEV .GT. 4) ISPELEV = 1
      ce_spelev(ISPELEV) = SPELEV
      ce_speleva = (ce_spelev(1) + ce_spelev(2) + ce_spelev(3)
     &            + ce_spelev(4) )*.25

      CE$SPELV = ce_speleva

      CE$NOFRI = HCENOFRI   ! friction off request

      CE$SPR1  = ENP(1)+ENP(2)   ! Engine speed (used for friction calc)
      CE$SPR2 = VFLAPS            ! flap postion

C  -----------------------
CD CE110 ELEVATOR SURFACES
C  -----------------------

      IF (YITAIL.EQ.226) THEN
        CELVL  = CIECSPOS
        CELVR  = CIEFSPOS
      ELSE
        IF (CIECSPOS.GT.10) THEN    ! NON LINEARITIES IN 300
          CELVL  = (CIECSPOS - 10.)*(1./12.) * (10.) + 10.
        ELSEIF (CIECSPOS.LT.-15) THEN    ! NON LINEARITIES IN 300
          CELVL  = (CIECSPOS + 15.)*(1./14.) * (13.) - 16.
        ELSEIF (CIECSPOS.LT.0.) THEN    ! NON LINEARITIES IN 300
          CELVL  = (CIECSPOS)*(1./15.) * (16.)
        ELSE
          CELVL  = CIECSPOS
        ENDIF

        IF (CIEFSPOS.GT.10) THEN    ! NON LINEARITIES IN 300
          CELVR  = (CIEFSPOS - 10.)*(1./12.) * (10.) + 10.
        ELSEIF (CIEFSPOS.LT.-15) THEN    ! NON LINEARITIES IN 300
          CELVR  = (CIEFSPOS + 15.)*(1./14.) * (13.) - 16.
        ELSEIF (CIECSPOS.LT.0.) THEN    ! NON LINEARITIES IN 300
          CELVR  = (CIEFSPOS)*(1./15.) * (16.)
        ELSE
          CELVR  = CIEFSPOS
        ENDIF
      ENDIF

CFM+
      IF (YITAIL.EQ.226) THEN
        CETRIM = (CIETSPOS-3.5)
      ELSE
        CETRIM = (-(CIETSPOS)-24)
      ENDIF
CFM-
CFM      CETRIM = (CIETSPOS-3.5)
      CETRIMDG = CIETFPOS * 360
      CETRIMF  = -CIESPR7

      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00379 CE010 FIRST PASS INITIALIZATION
C$ 00388 CE020 ATG BACKDRIVE ELEVATOR
C$ 00427 CE030 ATG BACKDRIVE PITCH TRIM
C$ 00448 CE050 PITCH TRIM
C$ 00485 CE090 FORCE AND POSITION CALCULATION
C$ 00499 CE100 C30 - LABELS XFER
C$ 00585 CE110 ELEVATOR SURFACES
