#!  /bin/csh -f
#!  $Revision: COM_ENT - Enter or Extract a command file V1.1 (MT) May-91$
#!
#! &
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ! ("$argv[2]" == "EXTRACT" || "$argv[2]" == "ENTER") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
if ("$argv[2]" == "ENTER") then
  set FSE_FILE="`revl '-$FSE_FILE'`"
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_FILE."
      reverr $stat
    endif
    exit
  endif
#
  set FSE_INFO="`fmtime $FSE_FILE | cut -c1-17`"
  if ("$FSE_INFO" == "") then
    echo "%FSE-E-INFOFAILED, File information is not available ($FSE_FILE)"
  else
    echo "0CCTPU $FSE_FILE,$FSE_FILE,COM_ENT.COM,,,Last modified on $FSE_INFO" >$argv[4]
  endif
endif
#
if ("$argv[2]" == "EXTRACT") then
  echo "0ECTPU $FSE_FILE" >$argv[4]
endif
#
exit
