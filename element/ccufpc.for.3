C     *************************************************************
      SUBROUTINE SEND_ERROR_MESSAGE(SLOT_LOCATION,system,test_type,
     +                              dmcptr)
C     *************************************************************
      IMPLICIT NONE

      INCLUDE 'ccu_diag.inc' ! NOFPC
      INCLUDE 'ccu_comm.inc' ! NOFPC
      integer*2 dmcptr

      if (system.eq.'A') then
         IF (YERLREAD) THEN
            IF (YERLSCNT.LT.64) THEN
               YERLSCNT = YERLSCNT + 1

               YERLSBUF((YERLSCNT-1)*6+1) = '4000'X
               YERLSBUF((YERLSCNT-1)*6+2) = SLOT_LOCATION
               YERLSBUF((YERLSCNT-1)*6+3) = test_type
               YERLSBUF((YERLSCNT-1)*6+4) = dmcptr
               YERLSBUF((YERLSCNT-1)*6+5) = 0
               YERLSBUF((YERLSCNT-1)*6+6) = 0
               YERLPROC = .TRUE.
            ENDIF
         ENDIF
      else

         IF (YERREAD2) THEN
            IF (YERSCNT2.LT.64) THEN
               YERSCNT2 = YERSCNT2 + 1

               YERSBUF2((YERSCNT2-1)*6+1) = '4000'X
               YERSBUF2((YERSCNT2-1)*6+2) = SLOT_LOCATION
               YERSBUF2((YERSCNT2-1)*6+3) = test_type
               YERSBUF2((YERSCNT2-1)*6+4) = dmcptr
               YERSBUF2((YERSCNT2-1)*6+5) = 0
               YERSBUF2((YERSCNT2-1)*6+6) = 0
               YERPROC2 = .TRUE.
            ENDIF
         ENDIF

      endif

      RETURN
      END


C     ******************************************
      SUBROUTINE UPDATE_CDB_BUFFER (test_type,
     +                              ERROR_COUNT,
     +                              SLOT_LOCATION,
     +                              DMC_NUMBER,
     +                              INTERFACE_ASSIGNMENT,
     +                              CABINET,
     +                              CBUS_ADDRESS,
     +                              REAL_DIFF,
     +                              INT_DIFF,
     +                              system)
C     ******************************************
      IMPLICIT NONE

      INCLUDE 'ccu_diag.inc' ! NOFPC
      INCLUDE 'ccu_para.inc' ! NOFPC
      INCLUDE 'ccu_comm.inc' ! NOFPC

      CHARACTER*40 CCUDBC
      INTEGER*2 CCUDB2(20)
      INTEGER*4 CCUDB4(10)
      REAL*4    CCUDBR(10)
      INTEGER*4 ERROR_BUFFER(10)

      EQUIVALENCE (CCUDBC,CCUDB2,CCUDB4,CCUDBR,ERROR_BUFFER)

      CCUDBC(1:16)  = CABINET(1:16)
      ccudbc(16:16) = system
      CCUDBC(17:22) = INTERFACE_ASSIGNMENT(1:6)
      CCUDB2(19)    = DMC_NUMBER
      CCUDB2(13)    = CBUS_ADDRESS

      IF (TEST_TYPE.EQ.DIP_TEST.OR.
     +   TEST_TYPE.EQ.DOP_TEST) THEN
         CCUDB2(17)= INT_DIFF
      ELSE
         CCUDBR(8) = REAL_DIFF
      ENDIF

      CCUDB2(18) = TEST_TYPE
      CCUDB2(14) = ERROR_COUNT

      DO I=1,10
         SLOTBUF(I,SLOT_LOCATION) = ERROR_BUFFER(I)
      ENDDO

      RETURN
      END

C     *****************************************************
      SUBROUTINE UPDATE_STATS(      ERROR_COUNT,             ! return
     +                              SLOT_LOCATION,           ! return
     +                              INTERFACE_ASSIGNMENT,    ! provide
     +                              system,                  ! provide
     +                              STATUS)                  ! return
C     *****************************************************
      IMPLICIT NONE

      INCLUDE 'ccu_diag.inc' ! NOFPC
      INCLUDE 'ccu_para.inc' ! NOFPC
      INCLUDE 'ccu_comm.inc' ! NOFPC

      integer*2 max_ccu_err
      parameter (max_ccu_err = '7FFF'X)


      INTEGER*2 DUMMY2(2)
      INTEGER*4 DUMMY
      EQUIVALENCE (DUMMY2,DUMMY)

      INTEGER*4 TEMP(2)
      CHARACTER*8 TEMP_NAME
      EQUIVALENCE (TEMP_NAME,TEMP)

      integer*4 tempsys
      character*4 temp_sys
      character*1 systemp
      equivalence(tempsys,temp_sys)

      DO I=1, YCCUSCNT

         TEMP( 1 ) = SLOTBUF( 5,I ) ! ---> temp_name
         TEMP( 2 ) = SLOTBUF( 6,I ) ! --|

         DUMMY = SLOTBUF(7,I)    ! --|
         ERROR_COUNT = DUMMY2(2)  ! ---> error_count

         tempsys = slotbuf(4,I)
         systemp = temp_sys(4:4)

         IF (TEMP_NAME(1:6) .EQ. INTERFACE_ASSIGNMENT(1:6).and.
     +       systemp.eq.system) THEN
            IF (ERROR_COUNT.LT.max_ccu_err) THEN
               ERROR_COUNT = ERROR_COUNT + 1
            ENDIF
            SLOT_LOCATION = I ! YCCUSBUF slot to display
            GOTO 540
         ENDIF
      ENDDO
C
C  Add a new slot
C
      IF ((YCCUSCNT+1) .GT. MAXSLOT) THEN
         YCCUOFLW = .TRUE.
         YCCUSCNT = 0        ! Reset CCU error data base
         yccupcnt = 0
      ENDIF

      YCCUSCNT = YCCUSCNT + 1
      SLOT_LOCATION = YCCUSCNT
      ERROR_COUNT = 1

540   CONTINUE

      STATUS = NO_LOG

      IF (YCCUPCNT.GE.0.AND.YCCUSCNT.GT.0) THEN
         IF (4*ERROR_COUNT .GE. YCCUPCNT) THEN
            STATUS = LOG
         ENDIF
      ENDIF

      RETURN
      END



C     *****************************************************
      subroutine power_test(dmc_number,status,power_status,system)
C     *****************************************************
      implicit none

      INCLUDE 'ccu_diag.inc' ! NOFPC
      include 'ccu_para.inc' ! NOFPC
      include 'ccu_comm.inc' ! NOFPC
      include 'ccu_dgn.inc'  ! NOFPC

      integer*2 power_mask /'0E00'X/, powres,powdmc
      integer*2 i0800 /'0800'X/
      integer*2 i0400 /'0400'X/
      integer*2 i0200 /'0200'X/
      integer*2 NiF7FF /'F7FF'X/
      integer*2 NiFBFF /'FBFF'X/
      integer*2 NiFDFF /'FDFF'X/
      integer*2 i7FFF /'7FFF'X/ ! mask out dmc
      integer*4 time

      power_status(plus_fifteen) = .false.
      power_status(minus_fifteen) = .false.
      power_status(plus_twenty_four) = .false.

      test_type = pow_test
      aip_t_number = 0

      call prepare_comm_buff1(dmc_number,
     +                        test_type,
     +                        aip_t_number,
     +                        comm_buff1)

      yccubdat(1) = comm_buff1
      yccubdat(2) = 0

      yccubcom = .false.

      if (system.eq.'A') then
         yccubres(1) = 0
      else
         yccubres(1) = 1
      endif

      yccubres(3) = 0
      yccufail(2) = .false.

      yccubreq = .true.
      call wait_req_complete(status)

      if (yccubcom.and..not.yccufail(2)) then
         do i=1,6
            results(i) = yccubres(i)
         enddo
         powdmc = iand(i7FFF,results(2))
         if (powdmc.eq.dmc_number) then
            powres = iand(results(3),power_mask)

C           The next part of code is to reset the errors of power if
C           the dmc power voltage is not present
            if (iand(powres,i0800).ne.0.and.no_power(powdmc,3)
     +                                       .eq.1)then
               powres = iand(powres,niF7FF)
            endif
            if (iand(powres,i0400).ne.0.and.no_power(powdmc,1)
     +                                       .eq.1)then
               powres = iand(powres,niFBFF)
            endif
            if (iand(powres,i0200).ne.0.and.no_power(powdmc,2)
     +                                       .eq.1)then
               powres = iand(powres,niFDFF)
            endif

            if (powres.eq.'0000'X) then
               status = success
            else
               status = failure
               if (iand(powres,i0800).ne.0) then
                  power_status(plus_twenty_four) = .true.
               endif
               if (iand(powres,i0400).ne.0) then
                  power_status(plus_fifteen) = .true.
               endif
               if (iand(powres,i0200).ne.0) then
                  power_status(minus_fifteen) = .true.
               endif
            endif
         else
            status = no_response ! wrong dmc replied
         endif
      elseif (yccufail(2).and.yccubcom) then
         status = off_line
      else
         status = no_response
      endif

      yccubcom = .true.
      yccubreq = .false.

      return
      end

C     ******************
      subroutine HISTORY
C     ******************
      IMPLICIT NONE
      include 'ccu_diag.inc' ! NOFPC
      include 'ccu_para.inc' ! NOFPC


      integer*4 time
      CHARACTER*80 FILENAME, FILE2
      CHARACTER*80 COMMAND
      character*132 message(1100)
      CHARACTER*160 OUT_MESSAGE
      INTEGER*4 UNIT, UNIT2
      character*20 header
      integer hex_check

      INTEGER*4 IOERR
      INTEGER*4 STRING_LENGTH
      INTEGER*2 RECORD,FIRSTREC,LASTREC,pointer
      INTEGER*2 ENTRIES /0/ ,RECSTRT,RECEND
      CHARACTER*5 RECNUM
      INTEGER*2 CON_TO_INT

      CHARACTER*1 ESC
      integer*2 errnumtotest(6)

      character*4 app
      integer*2 mask /'FF00'X/ ! app:indicator
      integer*2 iapp1,iapp2,severity,val1,val2
      equivalence (iapp1,app(1:2))
      equivalence (iapp2,app(3:4))

      LOGICAL*1 PRINTER /.FALSE./, search_flag,
     +          log_file_opened /.false./
      INTEGER*2 MAXREC
      LOGICAL*1 PRINTER_OK /.false./, sim_start, sim_msg

      CHARACTER*80 BLANK

      logical*1 display_commands
      common /display/ display_commands

      integer*2 s1,s2,s3,f1,f2,f3
      integer*2 saveofmt
      integer*4 I
      character*80 rvlstr /'$Revision: ccufpc V2.2 May 21, 92 $'/

C     2.1A changed lp to print

      BLANK='                                        '//
     +      '                                        '

      ESC=CHAR(27)
      FILENAME = 'dmclogfl.log'
      CALL GET_FILE_NAME(FILENAME)
      FILE2 = 'dmcprint.log'
      CALL GET_FILE_NAME(FILE2)

      UNIT = 10
      UNIT2 = 11

      display_commands = .true.

      call disp_pri(printer)

110   continue


      WRITE(6,'(1X,A)') ESC//'[6;1H'//BLANK
      WRITE (6,'(1X,A)') ESC//'[6;1HHISTORY> '

      if (display_commands) then
         display_commands = .false.

         WRITE(6,'(1X,A)') esc//'[24;1H'//ESC//'[7m'
         write(6,'(1X,A)') esc//'[24;1H'//
     +   'Enter: '//
     +   'Display   Exit   Help   Load   Restart   Search   Set     '
     +   //'               '//esc//'[10;1H'//esc//'[0m'

      endif

      WRITE(6,'(1X,A,$)') ESC//'[6;10H'

      read(5,'(A80)',err=999,end=999) command

      call parse_command(command,s1,f1,s2,f2,s3,f3)

      if ((command(s1:s1).eq.'X').or.
     +        (command(s1:s1+2).eq.'EXI').or.
     +        (command(s1:s1+2).eq.'QUI')) then

         goto 999

      elseif (command(s1:s1+2).eq.'ERR') then
         errnumtotest(1) = 0
         errnumtotest(2) = 0
         errnumtotest(3) = 0
         errnumtotest(4) = 0
         errnumtotest(5) = 0
         errnumtotest(6) = 0
         severity = 0
         val1 = 0
         val2 = 0
         app = ' '

         if (s2.ne.80) then
            read (command(s2:s2+3),'(Z4.4)',err=999,end=999)
     +      errnumtotest(1)
            errnumtotest(2) = 0
            errnumtotest(3) = 0
            errnumtotest(4) = 0
            errnumtotest(5) = 0
            errnumtotest(6) = 0
         else
            errnumtotest(1) = 0
            errnumtotest(2) = 0
            errnumtotest(3) = 0
            errnumtotest(4) = 0
            errnumtotest(5) = 0
            errnumtotest(6) = 0
         endif
         if (s3.ne.80) then
C           put in the error_code
            read (command(s3:s3+3),'(Z4.4)',err=999,end=999)
     +      errnumtotest(2)

C           put in the XX_YY value
            read (command(s3+9:s3+12),'(Z4.4)',err=999,end=999)
     +      errnumtotest(3)

C           put in the application val1
            read (command(s3+5:s3+8),'(A)',err=999,end=999)
     +      app
C           put in the application val2
            read (command(s3+14:s3+14),'(I1.1)',err=999,end=999)
     +      severity
            val1 = iapp1
            val2 = iapp2
            val2 = iand(val2,mask)
            val2 = val2+severity ! place indicator
            errnumtotest(4)= val1
            errnumtotest(5)= val2

C           put in the dev specific value
            read (command(s3+16:s3+19),'(Z4.4)',err=999,end=999)
     +      errnumtotest(6)

         else
           errnumtotest(2) = 0
           errnumtotest(3) = 0
           errnumtotest(4) = 0
           errnumtotest(5) = 0
           errnumtotest(6) = 0
         endif

         if (yerlread.and.yerlscnt.lt.64) then
            yerlscnt = yerlscnt + 1
            yerlproc = .true.
            yerlsbuf((yerlscnt-1)*6+1) = errnumtotest(1)
            yerlsbuf((yerlscnt-1)*6+2) = errnumtotest(2)
            yerlsbuf((yerlscnt-1)*6+3) = errnumtotest(3)
            yerlsbuf((yerlscnt-1)*6+4) = errnumtotest(4)
            yerlsbuf((yerlscnt-1)*6+5) = errnumtotest(5)
            yerlsbuf((yerlscnt-1)*6+6) = errnumtotest(6)
         endif

      elseif (command(s1:s1+2).eq.'LOA') then

         call clear_scroll_display

         if (log_file_opened) then
            CALL CLOSE_DISK_FILE(UNIT)
            log_file_opened = .false.
         endif

         CALL OPEN_DISK_FILE(UNIT,FILENAME,IOERR)

         IF (IOERR.EQ.-1) THEN
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A,A)')
     +               'Unable to open log file: ',
     +               FILENAME(1:STRING_LENGTH(FILENAME))
            GOTO 110
         ENDIF

         READ(UNIT,REC=1,IOSTAT=IOERR,err=120) MESSAGE(1)(1:20)
         READ(MESSAGE(1)(1:5),'(I5)',IOSTAT=IOERR,ERR=120) FIRSTREC
         READ(MESSAGE(1)(6:10),'(I5)',IOSTAT=IOERR,ERR=120) LASTREC


CNEW
         IF (FIRSTREC.EQ.1.AND.LASTREC.EQ.2) THEN
         elseif (firstrec.gt.lastrec) then
            READ(UNIT,REC=1001,IOSTAT=IOERR,err=888) MESSAGE(1)
            if (message(1)(1:4).eq.'This') then
               goto 888
            endif
         else
            READ(UNIT,REC=lastrec,IOSTAT=IOERR,err=888) MESSAGE(1)
         endif

         goto 889

888      continue ! OLD

         if (firstrec.lt.lastrec) then
            lastrec = lastrec - 1
         endif
         maxrec = 999

         goto 890

889      continue  ! NEW
         maxrec = 1001

890      continue


         IF (FIRSTREC.LE.0.OR.FIRSTREC.GT.MAXREC.OR.
     +      LASTREC.LE.0.OR.LASTREC.GT.MAXREC) THEN

            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +         'Log file is corrupted.'
            GOTO 110
         ENDIF

         IF (FIRSTREC.EQ.1.AND.LASTREC.EQ.2) THEN
            ENTRIES = 0
         ELSEIF (LASTREC.GE.FIRSTREC) THEN
               ENTRIES = LASTREC - FIRSTREC +1
          ELSE
               ENTRIES = LASTREC-FIRSTREC+MAXREC
         ENDIF

         IF (ENTRIES.EQ.1) THEN

            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +         'There is 1 error message '//
     +         'in the log file.'

            RECSTRT = 1
            RECEND = ENTRIES

         elseif (entries.eq.0) then

            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +         'There are no error messages '//
     +         'in the log file.'


         ELSEIF (ENTRIES.GE.2) THEN
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            out_message =
     +         'There are '//RECNUM(ENTRIES)//
     +         ' error messages in the log file.'

            WRITE(6,'(1X,A)') out_message(1:80)
         ENDIF

         do i=2,entries+1
            READ(UNIT,REC=i,IOSTAT=IOERR,err=120) MESSAGE(i)
         enddo

         log_file_opened = .true.
         goto 130

120      continue

         WRITE(6,'(1X,A)') ESC//'[22;1H'
         WRITE(6,'(1X,A)')
     +      'Error occurred while reading file'

         CALL CLOSE_DISK_FILE(UNIT)

130      continue

      elseif (command(s1:s1+2).eq.'RES') then
         saveofmt = yerlofmt
         yerlofmt = 7 ! stop error logger

         time = 3
         call sleeper(time)
         call clear_scroll_display

         if (log_file_opened) then
            CALL CLOSE_DISK_FILE(UNIT)
            log_file_opened = .false.
         endif

         CALL OPEN_DISK_FILE(UNIT,FILENAME,IOERR)

         IF (IOERR.EQ.-1) THEN
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A,A)')
     +               'Unable to open log file: ',
     +               FILENAME(1:STRING_LENGTH(FILENAME))
            yerlofmt = saveofmt
            GOTO 110
         ENDIF

Cmikep

         out_message =
     +      ' This is an empty, but reserved record location.'
         do i=2,1001
            WRITE(UNIT,REC=i,ERR=55,IOSTAT=IOERR)
     +      out_MESSAGE(1:132)
         enddo

         firstrec =1
         lastrec = 2
         HEADER=RECNUM(FIRSTREC)//RECNUM(LASTREC)
         WRITE(UNIT,REC=1,ERR=55,IOSTAT=IOERR) header(1:20)
         WRITE(UNIT,REC=1,ERR=55,IOSTAT=IOERR) header(1:20)

         call close_disk_file(unit)

         WRITE(6,'(1X,A)') ESC//'[22;1H'
         WRITE(6,'(1X,A)')
     +      'File has been restarted.'
         yerlofmt = saveofmt
         if (s2.ne.80) then
            if (s2.eq.f2) then
               i = hex_check(command(s2:f2),1)
               if (i.eq.success) then
                  read(command(s2:f2),'(Z1)') saveofmt
                  yerlofmt = saveofmt
               endif
            endif
         endif
         goto 110

55       continue

         WRITE(6,'(1X,A)') ESC//'[22;1H'
         WRITE(6,'(1X,A)')
     +      'File error, cannot access log file'

         time = 2
         call sleeper(time)
         yerlofmt = saveofmt

      elseif (command(s1:s1+2).eq.'DIS') then

         call clear_scroll_display

         IF (.NOT.LOG_FILE_OPENED) THEN
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +         'Log file has not been loaded, type LOAD'
            goto 110
         elseif (entries.eq.0) then
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +         'There are no error messages '//
     +         'in the log file.'
            goto 110
         endif

         if (command(s2:s2+2).eq.'ALL') then
            RECSTRT = 1
            RECEND = ENTRIES

         elseif (command(s2:s2+2).eq.'SIM') then
            RECSTRT = 1
            RECEND = ENTRIES
            sim_start = .true.
         elseif (s2.eq.80) then

            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +      'Must specifiy "start_entry" and "end_entry",'//
     +      '   EX. DISPLAY  2  10'

            WRITE(6,'(1X,A)')
     +      '               ALL, or SIMULATION'
            GOTO 110

         else

            recstrt = con_to_int(command,s2,f2)
            if (s3.eq.80) then
               recend = recstrt
            else
               recend  = con_to_int(command,s3,f3)
            endif

         endif

         IF (.NOT.(RECSTRT.LE.ENTRIES.AND.RECSTRT.GT.0.AND.
     +             RECEND .LE.ENTRIES.AND.RECEND .GT.0.AND.
     +             RECSTRT.LE.RECEND)) THEN

            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +         'Entries are incorrect!'
            GOTO 110
         ENDIF

         search_flag = .false.

         call find_and_display(firstrec,
     +                         lastrec,
     +                         recstrt,
     +                         recend,
     +                         printer,
     +                         printer_ok,
     +                         sim_start,
     +                         sim_msg,
     +                         search_flag,
     +                         s2,
     +                         f2,
     +                         command,
     +                         unit2,
     +                         file2,
     +                         maxrec,
     +                         message)


      elseif (command(s1:s1+2).eq.'SET') then

         call clear_scroll_display

         if (command(s2:s2+2).eq.'PRI') then
            printer = .true.

            call disp_pri(printer)
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)') 'Output will be directed to PRINTER'

         elseif (command(s2:s2+2).eq.'SCR') then
            printer = .false.

            call disp_pri(printer)
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)') 'Output will be directed to SCREEN'

         elseif (command(s2:s2+2).eq.'LOG') then

            if (s3.ne.80) then
               if (s3.eq.f3) then
                  i = hex_check(command(s3:f3),1)
                  if (i.eq.success) then
                     read(command(s3:f3),'(Z1)') saveofmt
                     yerlofmt = saveofmt

                     WRITE(6,'(1X,A)') ESC//'[22;1H'
                     WRITE(6,'(1X,A)') 'YERLOFMT label is changed'
                  else

                     WRITE(6,'(1X,A)') ESC//'[22;1H'
                     WRITE(6,'(1X,A)')
     +               'Error, value must be a digit. EX. 2'
                  endif
               else
                  WRITE(6,'(1X,A)') ESC//'[22;1H'
                  WRITE(6,'(1X,A)')
     +            'Error, value must be a digit. EX. 2'
               endif
            else

               WRITE(6,'(1X,A)') ESC//'[22;1H'
               WRITE(6,'(1X,A)')
     +         'Must specify new value. See LIS LOG command. EX. 2'
            endif
         else

            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +      'Must specify PRINTER, SCREEN, or LOG_FORMAT'

         endif
      elseif (command(s1:s1+2).eq.'SEA') then

         call clear_scroll_display
         if (s2.eq.80.or.f2.eq.80) then

            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)') 'Must specify "string" in quotes'
            goto 110
         endif

         search_flag = .true.
         RECSTRT = 1
         RECEND = ENTRIES

         call find_and_display(firstrec,
     +                         lastrec,
     +                         recstrt,
     +                         recend,
     +                         printer,
     +                         printer_ok,
     +                         sim_start,
     +                         sim_msg,
     +                         search_flag,
     +                         s2,
     +                         f2,
     +                         command,
     +                         unit2,
     +                         file2,
     +                         maxrec,
     +                         message)

         search_flag = .false.

      elseif (command(s1:s1+2).eq.'HEL') then

         call clear_scroll_display
         call rloghelp

         call disp_pri(printer)

      else
         WRITE(6,'(1X,A)') ESC//'[22;1H'
         WRITE(6,'(1X,A,A)') 'Illegal command: ',command(s1:f1)
      endif

      goto 110

999   continue

      if (log_file_opened) then
         CALL CLOSE_DISK_FILE(UNIT)
         log_file_opened = .false.
      endif

      write(6,'(1X,A)') ESC//'[23;71H          '
      call put_system
      return
C     ***
      end
C     ***


C     ************************************
      subroutine wait_req_complete(status)
C     ***********************************
      implicit none

      include 'ccu_diag.inc' !NOFPC
      include 'ccu_comm.inc' !NOFPC
      include 'ccu_para.inc' !NOFPC

      integer*2 timer

      timer = 5

      do while (timer.gt.0)
         if (yccubcom) goto 10
         timer = timer - 1
         call sleeper(1) ! wait 1 second
      enddo

10    continue

      if (yccubcom) then
         status = success
      else
         status = failure
      endif

      return
      end

C     ****************************************
      subroutine do_dgn_test(IND2,IND3,system)
C     ****************************************
      implicit none
      include 'ccu_diag.inc'! NOFPC
      include 'ccu_dgn.inc' ! NOFPC

      integer*2 IND1,IND2,IND3
      character*1 system

      yccudata(1) = reqform(IND2,IND3,1)
      yccudata(2) = reqform(IND2,IND3,2)

C      write(6,'(1X,2(A,Z4.4))')
C     +     ' DATA1:',yccudata(1),' DATA2:',yccudata(2)

      yccucomp = .false.

      if (system.eq.'A') then
         yccureq = .true.
      else
         yccureq1 = .true.
      endif

      return
      end

C     **************************
      subroutine pass_dgn_tobg()
C     **************************
      include 'ccu_diag.inc' ! NOFPC
      include 'ccu_dgn.inc'  ! NOFPC

      do i=1,10
         slotbuf(i,50) = test4_res(i)
      enddo

      yccubdat(1) = dgn_ptr ! the number of i*2 words

      yccubres(3) = time1
      yccubres(4) = time2

      yccubcom = .true. ! tell bg

      return
      end

C     **********************************
      subroutine log_dgn_results(system)
C     **********************************
      implicit none
      include 'ccu_diag.inc' ! NOFPC
      include 'ccu_dgn.inc'  ! NOFPC
      integer*2 i,iclk
      character*1 system
      logical*1 proc,proc2

      if (.not.yerlread.and.system.eq.'A') goto 20
      if (.not.yerread2.and.system.eq.'B') goto 20
      proc = .false.
      proc2 = .false.

      do i=1, dgn_ptr

         iclk = 0 ! assume ok

         if (test_results(i).ne.0.or.(dgnl_clk(dgn_test).and.
     +       i.eq.1)) then
            if (dgnl_clk(dgn_test).and.i.eq.1) then
               if (time1.eq.time2) then
                  iclk = -2 ! stopped
               else
                  goto 10
               endif
            endif
            if (system.eq.'A') then
               if (yerlscnt.lt.64) then
                  yerlscnt = yerlscnt + 1

                  yerlsbuf((yerlscnt-1)*6+1) = '4800'X
                  yerlsbuf((yerlscnt-1)*6+2) = dgn_test
                  yerlsbuf((yerlscnt-1)*6+3) = test_results(i) ! err_code
                  yerlsbuf((yerlscnt-1)*6+4) = test_results(i+10) ! slot
                  yerlsbuf((yerlscnt-1)*6+5) = iclk
                  proc = .true.
               endif
            else
               if (yerscnt2.lt.64) then
                  yerscnt2 = yerscnt2 + 1

                  yersbuf2((yerscnt2-1)*6+1) = '4800'X
                  yersbuf2((yerscnt2-1)*6+2) = dgn_test
                  yersbuf2((yerscnt2-1)*6+3) = test_results(i) ! err_code
                  yersbuf2((yerscnt2-1)*6+4) = test_results(i+10) ! slot
                  yersbuf2((yerscnt2-1)*6+5) = iclk
                  proc2 = .true.
               endif
            endif
         endif
10       continue
      enddo

      if (proc) then
         yerlproc = .true.
      endif
      if (proc2) then
         yerproc2 = .true.
      endif

20    continue
      return
      end

C     ***************************
      subroutine ccugetcdb(cdb_addr)
C     ***************************
      implicit none
      include 'ccu_diag.inc' ! NOFPC
      integer cdb_addr(20), loc
      cdb_addr(1) = loc(yxstrtxrf)
      cdb_addr(2) = loc(yxstrtxrf1)
      return
      end

