/******************************************************************************
C
C'Title                Yaw Card Slow Access Data File
C'Module_ID            usd8cydata.c
C'Entry_points         N/A
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Yaw control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.2
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
*/
 
#include  "cf_def.h"
 
 
/*
C -----------------------------------------------------------------------------
CD CYDATA010 YAW CONTROLS CALIBRATION PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the servocal
CC routine to hold the actuator units to pilot units relationship data.
CC The maximum number of calibration breakpoints is limited to 11 by
CC the MAX_CAL symbol which is defined in the <cf_def.h> include file.
*/
 
/*
C ------------------------------------------------------------
CD CYDATA020 -  Rudder calibration parameters
C ------------------------------------------------------------
*/
 
int
CR_CAL_FUNC = -1,   /*  Rudder CALIBRATION FUNCTION INDEX */
CRCALCHG    = -1,   /*  Rudder CALIBRATION CHANGE FLAG    */
CRCALCNT    = 11;   /*  Rudder CALIBRATION BRKPOINT COUNT */
 
float
CRCALAPOS[MAX_CAL] =   /*  Rudder ACTUATOR POS BRKPOINTS  */
{-2.0, -1.5, -1., -0.5, -0.25, 0.0, 0.25, 0.5, 1.0, 1.5 , 2.0},
 
CRCALPPOS[MAX_CAL] =   /*  Rudder PILOT POS BRKPOINTS     */
{-200.0, -150.,-100.0, -50.0, -25.0, 0.0, 25.0, 50.0, 100.0, 150.0, 200.0},
 
CRCALGEAR[MAX_CAL] =   /*  Rudder FORCE GEARING BRKPOINTS */
{0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20},
 
CRCALFRIC[MAX_CAL] =   /*  Rudder MECHANICAL FRIC BRKPNTS */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.},
 
CRCALFORC[MAX_CAL] =   /*  Rudder FORCE OFFSET BRKPOINTS  */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.};
 
 
/*
C -----------------------------------------------------------------------------
CD CYDATA030 YAW CARD FEELSPRING PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the feelspring
CC initialization routine and the feelspring interpolation macro.
CC The maximum number of feelspring breakpoints is limited to 17 by the
CC MAX_FEEL symbol and the maximum number of curves is limited to 6 by
CC the MAX_CURVE symbol which are both defined in the <cf_def.h> include file.
*/
 
/*
C -----------------------------------------------------------
CD CYDATA040 -  Rudder feelspring parameters
C -----------------------------------------------------------
*/
 
int
CRFEEL_FUNC = -1,       /* Feelspring function return number         */
CRFEELERR   =  0,       /* Feelspring error return status            */
CRFEELAFT   =  1,       /* Aft units flag (0 = convert to fwd units) */
CRFEELBCN   =  3,       /* Feelspring breakpoints number             */
CRFEELCCN   =  1,       /* Feelspring curves number                  */
CRFEELCHG[MAX_CURVE] =  /* Feelspring change flag for each curve     */
{1,1,1,1,1,1};
 
float
CRVARI = 0.,            /* Feelspring curve selection variable       */
CRFEELCRV[MAX_CURVE] =  /* Feelspring curve selection breakpoints    */
{0.0,50.0,100.0},
 
CRFEELNNL = 0,         /* Feelspring negative notch level           */
CRFEELNPL = 0,         /* Feelspring positive notch level           */
CRFEELXMN = 0.,         /* Feelspring minimum breakpoint position    */
CRFEELXMX = 0.,         /* Feelspring maximum breakpoint position    */
CRFEELPOS[MAX_FEEL] =   /* Feelspring position breakpoints           */
{-150.0, 0.0, 150.0},
 
CRFEELSFO = 0.,                   /* Feelspring force output         */
CRFEELFOR[MAX_CURVE][MAX_FEEL] =  /* Feelspring force breakpoints    */
{0.0, 0.0, 0.0},
 
CRFEELSFR = 0.,                   /* Feelspring friction output      */
CRFEELFRI[MAX_CURVE][MAX_FEEL] =  /* Feelspring friction breakpoints */
{0.0, 0.0, 0.0};
 
 
/*
C$
C$--- Section Summary
C$
C$ 00041 CYDATA010 YAW CONTROLS CALIBRATION PARAMETERS                         
C$ 00052 CYDATA020 -  Rudder calibration parameters                            
C$ 00080 CYDATA030 YAW CARD FEELSPRING PARAMETERS                              
C$ 00092 CYDATA040 -  Rudder feelspring parameters                             
*/
