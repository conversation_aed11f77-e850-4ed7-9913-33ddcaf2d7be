*                 
*                 
*  USAir Dash 8
*                                   
*  DMC-BASED INTERFACE ADDRESS FILE 
*  ( #2 for Digital Audio)               
*
*************************************************************************
*                                                                       *
*                                                                       *
*                                                                       *
*                                                                       *
*                         DMC-BASED INTERFACE                           *
*                                                                       *
*                   AUDIO  ADDRESS  FILE  SECTION                       *
*                                                                       *
*                              VER : 1.0AV      (VAX)                   *
*                                                                       *
*                                                                       *
*       NOTE :  IF SOMEBODY NEEDS TO MODIFY THE AUDIO ADDRESS FILE      *
*               PLEASE CALL THE AUDIO GROUP             THANK-YOU       *
*                                                                       *
*                                                                       *
*************************************************************************
*
* 1 = DMC NUMBER      2 = START ADDRESS      3 = CHANNEL DEFINITION
* 4 = OFFSET ADDRESS  5 = START ASSIGNATION  6 = CARD TYPE
* 7 = DIAGNOSTIC      8 = CONFIGURATION      9 = COMMENTS
*
*
* 1    2   3  4         5         6       7     8                 9
* V    V   V  V         V         V       V     V                 V
*0C/A7154/0:6/4       MO98AA     XXX     OFF   CON ='0014      !EXAMPLE
*************************************************************************
*
CHASSIS=F2AB11                                               !DAS CHASSIS
BACKPLANE=C-BUS
*
******* SLOT # 03 = SPC1
SLOT=03
0C/85000/0:11        MO1000     XXX     OFF    CON ='0017'     ! DOP
SLOT=03
0C/85018/0:47        MI100C     XXX     OFF                    ! DIP
SLOT=03
0C/85600             MO1300     XXX     OFF                    ! LOW CONTROL
SLOT=03
0C/85602             MI1301     XXX     OFF                    ! LOW STATUS
SLOT=03
0C/85604             MO1302     XXX     OFF                    ! LOW POINTER
SLOT=03
0C/85800             MO1400     XXX     OFF                    ! 2ND CONTROL
SLOT=03
0C/85802             MI1401     XXX     OFF                    ! 2ND STATUS
SLOT=03
0C/85804/0:1         MO1402     XXX     OFF                    ! 2ND POINTER
*
SLOT=03
0C/85DFC             MO16FE     XXX     OFF                    ! HIGH CONTROL
SLOT=03
0C/85DFE             MI16FF     XXX     OFF                    ! HIGH STATUS
SLOT=03
0C/85E16             MI170B     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 04 = SPC2
SLOT=04
0C/87000/0:11        MO1800     XXX     OFF    CON ='0017'     ! DOP
SLOT=04
0C/87018/0:47        MI180C     XXX     OFF                    ! DIP
SLOT=04
0C/87600             MO1B00     XXX     OFF                    ! LOW CONTROL
SLOT=04
0C/87602             MI1B01     XXX     OFF                    ! LOW STATUS
SLOT=04
0C/87604             MO1B02     XXX     OFF                    ! LOW POINTER
SLOT=04
0C/87800             MO1C00     XXX     OFF                    ! 2ND CONTROL
SLOT=04
0C/87802             MI1C01     XXX     OFF                    ! 2ND STATUS
SLOT=04
0C/87804/0:1         MO1C02     XXX     OFF                    ! 2ND POINTER
*
SLOT=04
0C/87DFC             MO1EFE     XXX     OFF                    ! HIGH CONTROL
SLOT=04
0C/87DFE             MI1EFF     XXX     OFF                    ! HIGH STATUS
SLOT=04
0C/87E16             MI1F0B     XXX     OFF                    ! FORGND COUNTER
*
*
******** SLOT # 05 = SPC3
**SLOT=05
**0C/89000/0:11        MO2000     XXX     OFF    CON ='0017'     ! DOP
**SLOT=05
**0C/89018/0:47        MI200C     XXX     OFF                    ! DIP
**SLOT=05
**0C/89600             MO2300     XXX     OFF                    ! LOW CONTROL
**SLOT=05
**0C/89602             MI2301     XXX     OFF                    ! LOW STATUS
**SLOT=05
**0C/89604             MO2302     XXX     OFF                    ! LOW POINTER
**SLOT=05
**0C/89800             MO2400     XXX     OFF                    ! 2ND CONTROL
**SLOT=05
**0C/89802             MI2401     XXX     OFF                    ! 2ND STATUS
**SLOT=05
**0C/89804/0:1         MO2402     XXX     OFF                    ! 2ND POINTER
***
**SLOT=05
**0C/89DFC             MO26FE     XXX     OFF                    ! HIGH CONTROL
**SLOT=05
**0C/89DFE             MI26FF     XXX     OFF                    ! HIGH STATUS
**SLOT=05
**0C/89E16             MI270B     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 06 = DV PLAY
SLOT=06
0C/8B000/0:9         MO2800     XXX     OFF    CON ='0015'     ! ACTIVE PLAY
SLOT=06
0C/8B014/0:9         MI280A     XXX     OFF                    ! ACKNOLEDGE PLAY
SLOT=06
0C/8B028             MI2814     XXX     OFF                    ! SYNC. PLAY
SLOT=06
0C/8B02A/0:9         MO2815     XXX     OFF                    ! VOLUME PLAY
SLOT=06
0C/8B03E             MO281F     XXX     OFF                    ! 2ND CONTROL
SLOT=06
0C/8B040             MI2820     XXX     OFF                    ! 2ND STATUS
SLOT=06
0C/8B042/0:1         MO2821     XXX     OFF                    ! 2ND POINTER
*
SLOT=06
0C/(1)8B06C/0:739    MO2836     XXX     OFF                    ! PLAY FRAME 1
SLOT=06
0C/(1)8B634/0:739    MO2B1A     XXX     OFF                    ! PLAY FRAME 2
*
SLOT=06
0C/8BDFC             MO2EFE     XXX     OFF                    ! HIGH CONTROL
SLOT=06
0C/8BDFE             MI2EFF     XXX     OFF                    ! HIGH STATUS
SLOT=06
0C/8BE16             MI2F0B     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 07 = FILTER
SLOT=07
0C/8D600             MO3300     XXX     OFF    CON ='0015'     ! LOW CONTROL
SLOT=07
0C/8D602             MI3301     XXX     OFF                    ! LOW STATUS
SLOT=07
0C/8D604             MO3302     XXX     OFF                    ! LOW POINTER
*
SLOT=07
0C/8DDFC             MO36FE     XXX     OFF                    ! HIGH CONTROL
SLOT=07
0C/8DDFE             MI36FF     XXX     OFF                    ! HIGH STATUS
SLOT=07
0C/8DE16             MI370B     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 08 = VAE1
**SLOT=08
**0C/8F000             MO3800     XXX     OFF    CON ='0015'     ! LOW CONTROL
**SLOT=08
**0C/8F002             MI3801     XXX     OFF                    ! LOW STATUS
**SLOT=08
**0C/8F004             MO3802     XXX     OFF                    ! LOW POINTER
***
**SLOT=08
**0C/8F006             MI3803     XXX     OFF                    ! PITCH DETECT
**SLOT=08
**0C/8F008/0:10        MO3804     XXX     OFF                    ! VAE PARAMETERS
***
**SLOT=08
**0C/8FDFC             MO3EFE     XXX     OFF                    ! HIGH CONTROL
**SLOT=08
**0C/8FDFE             MI3EFF     XXX     OFF                    ! HIGH STATUS
**SLOT=08
**0C/8FE16             MI3F0B     XXX     OFF                    ! FORGND COUNTER
***
***
********* SLOT # 09 = VAE2
**SLOT=09
**0C/91000             MO4000     XXX     OFF    CON ='0015'     ! LOW CONTROL
**SLOT=09
**0C/91002             MI4001     XXX     OFF                    ! LOW STATUS
**SLOT=09
**0C/91004             MO4002     XXX     OFF                    ! LOW POINTER
***
**SLOT=09
**0C/91006             MI4003     XXX     OFF                    ! PITCH DETECT
**SLOT=09
**0C/91008/0:10        MO4004     XXX     OFF                    ! VAE PARAMETERS
***
**SLOT=09
**0C/91DFC             MO46FE     XXX     OFF                    ! HIGH CONTROL
**SLOT=09
**0C/91DFE             MI46FF     XXX     OFF                    ! HIGH STATUS
**SLOT=09
**0C/91E16             MI470B     XXX     OFF                    ! FORGND COUNTER
***
***
********* SLOT # 10 = VAE3
**SLOT=10
**0C/93000             MO4800     XXX     OFF    CON ='0015'     ! LOW CONTROL
**SLOT=10
**0C/93002             MI4801     XXX     OFF                    ! LOW STATUS
**SLOT=10
**0C/93004             MO4802     XXX     OFF                    ! LOW POINTER
***
**SLOT=10
**0C/93006             MI4803     XXX     OFF                    ! PITCH DETECT
**SLOT=10
**0C/93008/0:10        MO4804     XXX     OFF                    ! VAE PARAMETERS
***
**SLOT=10
**0C/93DFC             MO4EFE     XXX     OFF                    ! HIGH CONTROL
**SLOT=10
**0C/93DFE             MI4EFF     XXX     OFF                    ! HIGH STATUS
**SLOT=10
**0C/93E16             MI4F0B     XXX     OFF                    ! FORGND COUNTER
***
***
********* SLOT # 11 = VAE4
**SLOT=11
**0C/95000             MO5000     XXX     OFF    CON ='0015'     ! LOW CONTROL
**SLOT=11
**0C/95002             MI5001     XXX     OFF                    ! LOW STATUS
**SLOT=11
**0C/95004             MO5002     XXX     OFF                    ! LOW POINTER
***
**SLOT=11
**0C/95006             MI5003     XXX     OFF                    ! PITCH DETECT
**SLOT=11
**0C/95008/0:10        MO5004     XXX     OFF                    ! VAE PARAMETERS
***
**SLOT=11
**0C/95DFC             MO56FE     XXX     OFF                    ! HIGH CONTROL
**SLOT=11
**0C/95DFE             MI56FF     XXX     OFF                    ! HIGH STATUS
**SLOT=11
**0C/95E16             MI570B     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 12 = NOISE
SLOT=12
0C/97002/0:3         MO5801     XXX     OFF    CON ='0015'     ! CONTROL + POINTER
SLOT=12
0C/97010/0:4         MO5805     XXX     OFF                    ! NOISE #1
SLOT=12
0C/9701A/0:4         MO580A     XXX     OFF                    ! NOISE #2
SLOT=12
0C/97024/0:1         MO580F     XXX     OFF                    ! IGNITION DELAY
SLOT=12
0C/97028/0:1         MO5811     XXX     OFF                    ! IGNITION
SLOT=12
0C/9702C/0:4         MO5813     XXX     OFF                    ! STATIC DISCHARGE
SLOT=12
0C/97036             MO5818     XXX     OFF                    ! WHITE NOISE
SLOT=12
0C/9700C/0:1         MI5801     XXX     OFF                    ! STATUS REGISTERS
*
SLOT=12
0C/97DFC             MO5800     XXX     OFF                    ! HIGH CONTROL
SLOT=12
0C/97DFE             MI5800     XXX     OFF                    ! HIGH STATUS
SLOT=12
0C/97E16             MI5803     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 13 = VOCAL WARN.
**SLOT=13
**0C/99040             MO0000     XXX     OFF    CON ='0015'
*
*
******* SLOT # 14 = KEYER
SLOT=14                                                        ! 19 => 14
0C/9B000/0:14        MO6800     XXX     OFF    CON ='0014'     ! KEYER CHANGE
SLOT=14
0C/9B028/0:14        MO6814     XXX     OFF                    ! DOT DURATION
SLOT=14
0C/9B050/0:14        MO6828     XXX     OFF                    ! PAUSE DURATION
SLOT=14
0C/9B078/0:14        MO683C     XXX     OFF                    ! REP. PERIOD
SLOT=14
0C/9B0A0/0:14        MO6850     XXX     OFF                    ! ASSOC. STATUS
SLOT=14
0C/9B0C8/0:14        MO6864     XXX     OFF                    ! RECEIVER TYPE
*
SLOT=14
0C/(1)9B0F0/0:3      MO6878     XXX     OFF                    ! ID #1
SLOT=14
0C/(1)9B0F8/0:3      MO687C     XXX     OFF                    ! ID #2
SLOT=14
0C/(1)9B100/0:3      MO6880     XXX     OFF                    ! ID #3
SLOT=14
0C/(1)9B108/0:3      MO6884     XXX     OFF                    ! ID #4
SLOT=14
0C/(1)9B110/0:3      MO6888     XXX     OFF                    ! ID #5
SLOT=14
0C/(1)9B118/0:3      MO688C     XXX     OFF                    ! ID #6
SLOT=14
0C/(1)9B120/0:3      MO6890     XXX     OFF                    ! ID #7
SLOT=14
0C/(1)9B128/0:3      MO6894     XXX     OFF                    ! ID #8
SLOT=14
0C/(1)9B130/0:3      MO6898     XXX     OFF                    ! ID #9
SLOT=14
0C/(1)9B138/0:3      MO689C     XXX     OFF                    ! ID #10
SLOT=14
0C/(1)9B140/0:3      MO68A0     XXX     OFF                    ! ID #11
SLOT=14
0C/(1)9B148/0:3      MO68A4     XXX     OFF                    ! ID #12
SLOT=14
0C/(1)9B150/0:3      MO68A8     XXX     OFF                    ! ID #13
SLOT=14
0C/(1)9B158/0:3      MO68AC     XXX     OFF                    ! ID #14
SLOT=14
0C/(1)9B160/0:3      MO68B0     XXX     OFF                    ! ID #15
*SLOT=14
*0C/(1)9B168/0:3      MO68B4     XXX     OFF                    ! ID #16
*SLOT=14
*0C/(1)9B170/0:3      MO68B8     XXX     OFF                    ! ID #17
*SLOT=14
*0C/(1)9B178/0:3      MO68BC     XXX     OFF                    ! ID #18
*SLOT=14
*0C/(1)9B180/0:3      MO68C0     XXX     OFF                    ! ID #19
*SLOT=14
*0C/(1)9B188/0:3      MO68C4     XXX     OFF                    ! ID #20
*
SLOT=14                                                        ! 39 =>29
0C/9B190/0:29        MO68C8     XXX     OFF                    ! KEYER TONE FREQ
SLOT=14
0C/9B1E0/0:14        MO68F0     XXX     OFF                    ! KEYER LEVEL
SLOT=14
0C/9B208/0:14        MO6904     XXX     OFF                    ! KEYER STATE
SLOT=14
0C/9B230/0:3         MO6918     XXX     OFF                    ! BFO FREQ.
SLOT=14
0C/9B238/0:1         MO691C     XXX     OFF                    ! BFO LEVEL
SLOT=14
0C/9B23C/0:14        MI691E     XXX     OFF                    ! ID STATUS
SLOT=14
0C/9B264/0:14        MI6932     XXX     OFF                    ! LEVEL STATUS
*
*SLOT=14
*0C/9B28C/0:9         MO6946     XXX     OFF                    ! CHANGE MORSE
SLOT=14
0C/9B2A0/0:14        MI6950     XXX     OFF                    ! REMAINING TIME
SLOT=14
0C/9B2C8             MO6964     XXX     OFF                    ! START/STOP PAUSE
SLOT=14
0C/9B2CA             MO6965     XXX     OFF                    ! REP. ERROR FACTOR
*
SLOT=14
0C/9BDFC             MO6EFE     XXX     OFF                    ! HIGH CONTROL
SLOT=14
0C/9BDFE             MI6EFF     XXX     OFF                    ! HIGH STATUS
SLOT=14
0C/9BE16             MI6F0B     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 15 = SIGNAL
SLOT=15
0C/9D000/0:5         MO7000     XXX     OFF    CON ='0014'     ! FREQUENCY 1
SLOT=15
0C/9D01A/0:5         MO700B     XXX     OFF                    ! FREQUENCY 2
SLOT=15
0C/9D034/0:5         MO7016     XXX     OFF                    ! FREQUENCY 3
SLOT=15
0C/9D00C             MO7006     XXX     OFF                    ! AMPLITUDE 1
SLOT=15
0C/9D010/0:2         MO7007     XXX     OFF                    !
SLOT=15
0C/9D018             MO700A     XXX     OFF                    ! SND DURATION 1
SLOT=15
0C/9D026             MO7011     XXX     OFF                    ! AMPLITUDE 2
SLOT=15
0C/9D02A/0:2         MO7012     XXX     OFF                    !
SLOT=15
0C/9D032             MO7015     XXX     OFF                    ! SND DURATION 2
SLOT=15
0C/9D040             MO701C     XXX     OFF                    ! AMPLITUDE 3
SLOT=15
0C/9D044/0:2         MO701D     XXX     OFF                    !
SLOT=15
0C/9D04C             MO7020     XXX     OFF                    ! SND DURATION 3
SLOT=15
0C/9D04E/0:7         MO7021     XXX     OFF                    ! SND PATTERN
SLOT=15
0C/9D05E/0:1         MO7060     XXX     OFF                    ! COUNTER + POI
SLOT=15
0C/9D062/0:6         MO7029     XXX     OFF                    ! COMMAND REGIS
SLOT=15
0C/9D070             MO7083     XXX     OFF                    ! COMMAND #8
SLOT=15
0C/9D072/0:6         MO7030     XXX     OFF                    ! POINTER REGIS
SLOT=15
0C/9D080             MO7084     XXX     OFF                    ! PN #8
SLOT=15
0C/9D0A2/0:6         MO7037     XXX     OFF                    ! OUTPUT AMPLIT
SLOT=15
0C/9D0B0             MO7085     XXX     OFF                    ! OUTPUT AMPLIT
SLOT=15
0C/9D082/0:6         MI7000     XXX     OFF                    ! STATUS REGIST
SLOT=15
0C/9D090             MI7018     XXX     OFF                    ! STATUS REGIST
SLOT=15
0C/9D092/0:6         MI7007     XXX     OFF                    ! GENERATOR FLAG
SLOT=15
0C/9D0A0             MI7019     XXX     OFF                    ! GENERATOR FLAG
SLOT=15
0C/9D0B2/0:5         MO703E     XXX     OFF                    ! AMPSHA FREQ.
SLOT=15
0C/9D0BE             MO7044     XXX     OFF                    ! AMPSHA AMPL.
SLOT=15
0C/9D0C2/0:2         MO7045     XXX     OFF                    ! AMP.
SLOT=15
0C/9D0CA/0:6         MO7048     XXX     OFF                    ! AMPSHA SND DAT
SLOT=15
0C/9D0D8/0:7         MO704F     XXX     OFF                    ! AMPSHA SND PAT
SLOT=15
0C/9D0E8/0:1         MO7062     XXX     OFF                    ! AMPSHA SND PAT
SLOT=15
0C/9D0EC/0:2         MO7057     XXX     OFF                    ! AMPSHA COMMAND
SLOT=15
0C/9D0F2             MO7086     XXX     OFF                    ! AMPSHA COMMAND
SLOT=15
0C/9D0F4/0:2         MO705A     XXX     OFF                    ! AMPSHA POINTER
SLOT=15
0C/9D0FA             MO7087     XXX     OFF                    ! AMPSHA POINTER
SLOT=15
0C/9D10C/0:2         MO705D     XXX     OFF                    ! AMPSHA OUTPUT
SLOT=15
0C/9D112             MO7088     XXX     OFF                    ! AMPSHA OUTPUT 
SLOT=15
0C/9D0FC/0:2         MI700E     XXX     OFF                    ! AMPSHA STATUS
SLOT=15
0C/9D102             MI701A     XXX     OFF                    ! AMPSHA STATUS
SLOT=15
0C/9D104/0:2         MI7011     XXX     OFF                    ! AMPSHA FLAG
SLOT=15
0C/9D10A             MI701B     XXX     OFF                    ! AMPSHA FLAG
SLOT=15
0C/9D114/0:9         MO7064     XXX     OFF                    ! SND DATA
SLOT=15
0C/9D128/0:4         MO706E     XXX     OFF                    ! SND DATA
SLOT=15
0C/9D132/0:9         MO7073     XXX     OFF                    ! SND DATA
SLOT=15
0C/9D146/0:1         MO707D     XXX     OFF                    ! SND DATA
SLOT=15
0C/9D14A/0:1         MO707F     XXX     OFF                    ! SND DATA
SLOT=15
0C/9D156/0:1         MO7081     XXX     OFF                    ! SND DATA
SLOT=15
0C/9D14E/0:1         MI7014     XXX     OFF                    ! SND DATA
SLOT=15
0C/9D152/0:1         MI7016     XXX     OFF                    ! SND DATA
SLOT=15
0C/9DA00/0:1         MO7500     XXX     OFF                    ! DMC RATE
*
SLOT=15
0C/9DDFC             MO7089     XXX     OFF                    ! HIGH CONTROL
SLOT=15
0C/9DDFE             MI701C     XXX     OFF                    ! HIGH STATUS
SLOT=15
0C/9DE16             MI701D     XXX     OFF                    ! COUNTER
*
*
******* SLOT # 16 = GENERATOR MIXER # 1
SLOT=16
0C/9F000/0:31        MO7800     XXX     OFF    CON ='0015'     ! CH#1 INPUT VOL
SLOT=16
0C/9F040/0:31        MO7820     XXX     OFF                    ! CH#2 INPUT VOL
SLOT=16
0C/9F080/0:31        MO7840     XXX     OFF                    ! CH#3 INPUT VOL
SLOT=16
0C/9F0C0/0:31        MO7860     XXX     OFF                    ! CH#4 INPUT VOL
SLOT=16
0C/9F100/0:31        MO7880     XXX     OFF                    ! CH#5 INPUT VOL
SLOT=16
0C/9F140/0:4         MO78A0     XXX     OFF                    ! FILTER VOL. MA
SLOT=16
0C/9F14A/0:4         MO78A5     XXX     OFF                    ! UNFILT. VOL. M
SLOT=16
0C/9F154/0:2         MO78AA     XXX     OFF                    ! DIRECT VOLUME
SLOT=16
0C/9F4A0             MO78B1     XXX     OFF                    ! LOW CONTROL
SLOT=16
0C/9F4A2             MI7800     XXX     OFF                    ! LOW STATUS
SLOT=16
0C/9F4A4             MO78B2     XXX     OFF                    ! LOW POINTER
*
SLOT=16
0C/9FDFC             MO78B0     XXX     OFF                    ! HIGH CONTROL
SLOT=16
0C/9FDFE             MI7801     XXX     OFF                    ! HIGH STATUS
SLOT=16
0C/9FE16             MI7802     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 17 = GENERATOR MIXER # 2
SLOT=17
0C/A1000/0:31        MO8000     XXX     OFF    CON ='0015'     ! CH#1 INPUT VOL
*SLOT=17
*0C/A1040/0:31        MO8020     XXX     OFF                   ! CH#2 INPUT VOL
*SLOT=17
*0C/A1080/0:31        MO8040     XXX     OFF                   ! CH#3 INPUT VOL
*SLOT=17
*0C/A10C0/0:31        MO8060     XXX     OFF                   ! CH#4 INPUT VOL
*SLOT=17
*0C/A1100/0:31        MO8080     XXX     OFF                   ! CH#5 INPUT VOL
SLOT=17
0C/A1140/0:4         MO80A0     XXX     OFF                    ! FILTER VOL. MA
SLOT=17
0C/A114A/0:4         MO80A5     XXX     OFF                    ! UNFILT. VOL. M
SLOT=17
0C/A1154/0:2         MO80AA     XXX     OFF                    ! DIRECT VOLUME
SLOT=17
0C/A14A0             MO80B1     XXX     OFF                    ! LOW CONTROL
SLOT=17
0C/A14A2             MI8000     XXX     OFF                    ! LOW STATUS
SLOT=17
0C/A14A4             MO80B2     XXX     OFF                    ! LOW POINTER
*
SLOT=17
0C/A1DFC             MO80B0     XXX     OFF                    ! HIGH CONTROL
SLOT=17
0C/A1DFE             MI8001     XXX     OFF                    ! HIGH STATUS
SLOT=17
0C/A1E16             MI8002     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 18 = GENERATOR MIXER # 3
**SLOT=18
**0C/A3000/0:31        MO8800     XXX     OFF    CON ='0015'     ! CH#1 INPUT V
**SLOT=18
**0C/A3040/0:31        MO8820     XXX     OFF                    ! CH#2 INPUT V
**SLOT=18
**0C/A3080/0:31        MO8840     XXX     OFF                    ! CH#3 INPUT V
**SLOT=18
**0C/A30C0/0:31        MO8860     XXX     OFF                    ! CH#4 INPUT V
**SLOT=18
**0C/A3100/0:31        MO8880     XXX     OFF                    ! CH#5 INPUT V
**SLOT=18
**0C/A3140/0:4         MO88A0     XXX     OFF                    ! FILTER VOL. 
**SLOT=18
**0C/A314A/0:4         MO88A5     XXX     OFF                    ! UNFILT. VOL.
**SLOT=18
**0C/A3154/0:2         MO88AA     XXX     OFF                    ! DIRECT VOLUM
**SLOT=18
**0C/A34A0             MO88B1     XXX     OFF                    ! LOW CONTROL
**SLOT=18
**0C/A34A2             MI8801     XXX     OFF                    ! LOW STATUS
**SLOT=18
**0C/A34A4             MO88B2     XXX     OFF                    ! LOW POINTER
***
**SLOT=18
**0C/A3DFC             MO88B0     XXX     OFF                    ! HIGH CONTROL
**SLOT=18
**0C/A3DFE             MI8800     XXX     OFF                    ! HIGH STATUS
**SLOT=18
**0C/A3E16             MI8802     XXX     OFF                    ! FORGND COUNT
*
*
******* SLOT # 19 = VOICE MIXING 1
SLOT=19
0C/A5000/0:23        MO9000     XXX     OFF    CON ='0015'     ! CH#1 INPUT VOL
SLOT=19
0C/A5048/0:9         MO9024     XXX     OFF                    ! CH#1 INPUT VOL
SLOT=19
0C/A5060/0:23        MO9030     XXX     OFF                    ! CH#2 INPUT VOL
SLOT=19
0C/A50A8/0:9         MO9054     XXX     OFF                    ! CH#2 INPUT VOL
SLOT=19
0C/A50C0/0:23        MO9060     XXX     OFF                    ! CH#3 INPUT VOL
SLOT=19
0C/A5108/0:9         MO9084     XXX     OFF                    ! CH#3 INPUT VOL
SLOT=19
0C/A5120/0:23        MO9090     XXX     OFF                    ! CH#4 INPUT VOL
SLOT=19
0C/A5168/0:9         MO90B4     XXX     OFF                    ! CH#4 INPUT VOL
SLOT=19
0C/A5180/0:23        MO90C0     XXX     OFF                    ! CH#5 INPUT VOL
SLOT=19
0C/A51C8/0:9         MO90E4     XXX     OFF                    ! CH#5 INPUT VOL
SLOT=19
0C/A51E0/0:23        MO90F0     XXX     OFF                    ! CH#6 INPUT VOL
SLOT=19
0C/A5228/0:9         MO9114     XXX     OFF                    ! CH#6 INPUT VOL
SLOT=19
0C/A5240/0:23        MO9120     XXX     OFF                    ! CH#7 INPUT VOL
SLOT=19
0C/A5288/0:9         MO9144     XXX     OFF                    ! CH#7 INPUT VOL
SLOT=19
0C/A52A0/0:23        MO9150     XXX     OFF                    ! CH#8 INPUT VOL
SLOT=19
0C/A52E8/0:9         MO9174     XXX     OFF                    ! CH#8 INPUT VOL
SLOT=19
0C/A5300/0:7         MO9180     XXX     OFF                    ! MASTER VOLUME
SLOT=19
0C/A5310/0:6         MO9188     XXX     OFF                    ! DIRECT VOLUME
SLOT=19
0C/A54A0             MO918F     XXX     OFF                    ! LOW CONTROL
SLOT=19
0C/A54A2             MI9000     XXX     OFF                    ! LOW STATUS
SLOT=19
0C/A54A4             MO9190     XXX     OFF                    ! LOW POINTER
*
SLOT=19
0C/A5DFC             MO9191     XXX     OFF                    ! HIGH CONTROL
SLOT=19
0C/A5DFE             MI9001     XXX     OFF                    ! HIGH STATUS
SLOT=19
0C/A5E16             MI9002     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 20 = VOICE MIXING 2
SLOT=20
0C/A7000/0:23        MO9800     XXX     OFF    CON ='0015'     ! CH#1 INPUT VOL
SLOT=20
0C/A7048/0:9         MO9824     XXX     OFF                    ! CH#1 INPUT VOL
SLOT=20
0C/A7060/0:23        MO9830     XXX     OFF                    ! CH#2 INPUT VOL
SLOT=20
0C/A70A8/0:9         MO9854     XXX     OFF                    ! CH#2 INPUT VOL
SLOT=20
0C/A70C0/0:23        MO9860     XXX     OFF                    ! CH#3 INPUT VOL
SLOT=20
0C/A7108/0:9         MO9884     XXX     OFF                    ! CH#3 INPUT VOL
*SLOT=20
*0C/A7120/0:23        MO9890     XXX     OFF                    ! CH#4 INPUT VO
*SLOT=20
*0C/A7168/0:9         MO98B4     XXX     OFF                    ! CH#4 INPUT VO
*SLOT=20
*0C/A7180/0:23        MO98C0     XXX     OFF                    ! CH#5 INPUT VO
*SLOT=20
*0C/A71C8/0:9         MO98E4     XXX     OFF                    ! CH#5 INPUT VO
*SLOT=20
*0C/A71E0/0:23        MO98F0     XXX     OFF                    ! CH#6 INPUT VO
*SLOT=20
*0C/A7228/0:9         MO9914     XXX     OFF                    ! CH#6 INPUT VOL.
*SLOT=20
*0C/A7240/0:23        MO9920     XXX     OFF                    ! CH#7 INPUT VO
*SLOT=20
*0C/A7288/0:9         MO9944     XXX     OFF                    ! CH#7 INPUT VO
*SLOT=20
*0C/A72A0/0:23        MO9950     XXX     OFF                    ! CH#8 INPUT VO
*SLOT=20
*0C/A72E8/0:9         MO9974     XXX     OFF                    ! CH#8 INPUT VO
SLOT=20
0C/A7300/0:7         MO9980     XXX     OFF                    ! MASTER VOLUME
SLOT=20
0C/A7310/0:6         MO9988     XXX     OFF                    ! DIRECT VOLUME
SLOT=20
0C/A74A0             MO998F     XXX     OFF                    ! LOW CONTROL
SLOT=20
0C/A74A2             MI9800     XXX     OFF                    ! LOW STATUS
SLOT=20
0C/A74A4             MO9990     XXX     OFF                    ! LOW POINTER
*
SLOT=20
0C/A7DFC             MO9991     XXX     OFF                    ! HIGH CONTROL
SLOT=20
0C/A7DFE             MI9801     XXX     OFF                    ! HIGH STATUS
SLOT=20
0C/A7E16             MI9802     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 21 = DIST MIXING 1 (REPLACED BY VOIMIX)
SLOT=21
0C/A9000/0:18        MOA000     XXX     OFF    CON ='0015'     ! CH#1 INPUT VOL
SLOT=21
0C/A903C/0:9         MOA01E     XXX     OFF                    ! CH#1 INPUT VOL
SLOT=21
0C/A9060/0:18        MOA030     XXX     OFF                    ! CH#2 INPUT VOL
SLOT=21
0C/A909C/0:9         MOA04E     XXX     OFF                    ! CH#2 INPUT VOL
SLOT=21
0C/A90C0/0:18        MOA060     XXX     OFF                    ! CH#3 INPUT VOL
SLOT=21
0C/A90FC/0:9         MOA07E     XXX     OFF                    ! CH#3 INPUT VOL
SLOT=21
0C/A9120/0:18        MOA090     XXX     OFF                    ! CH#4 INPUT VOL
SLOT=21
0C/A915C/0:9         MOA0AE     XXX     OFF                    ! CH#4 INPUT VOL
SLOT=21
0C/A9180/0:18        MOA0C0     XXX     OFF                    ! CH#5 INPUT VOL
SLOT=21
0C/A91BC/0:9         MOA0DE     XXX     OFF                    ! CH#5 INPUT VOL
SLOT=21
0C/A91E0/0:18        MOA0F0     XXX     OFF                    ! CH#6 INPUT VOL
SLOT=21
0C/A921C/0:9         MOA10E     XXX     OFF                    ! CH#6 INPUT VOL
SLOT=21
0C/A9240/0:18        MOA120     XXX     OFF                    ! CH#7 INPUT VOL
SLOT=21
0C/A927C/0:9         MOA13E     XXX     OFF                    ! CH#7 INPUT VOL
SLOT=21
0C/A92A0/0:18        MOA150     XXX     OFF                    ! CH#8 INPUT VOL
SLOT=21
0C/A92DC/0:9         MOA16E     XXX     OFF                    ! CH#8 INPUT VOL
SLOT=21
0C/A9300/0:7         MOA180     XXX     OFF                    ! MASTER VOLUME
SLOT=21
0C/A9310/0:6         MOA188     XXX     OFF                    ! DIRECT VOLUME
SLOT=21
0C/A94A0             MOA18F     XXX     OFF                    ! LOW CONTROL
SLOT=21
0C/A94A2             MIA000     XXX     OFF                    ! LOW STATUS
SLOT=21
0C/A94A4             MOA190     XXX     OFF                    ! LOW POINTER
*
SLOT=21
0C/A9DFC             MOA191     XXX     OFF                    ! HIGH CONTROL
SLOT=21
0C/A9DFE             MIA001     XXX     OFF                    ! HIGH STATUS
SLOT=21
0C/A9E16             MIA002     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 22 = DIST MIXING 2 (REPLACED BY VOIMIX)
SLOT=22
0C/AB000/0:18        MOA800     XXX     OFF    CON ='0015'     ! CH#1 INPUT VOL
SLOT=22
0C/AB03C/0:9         MOA81E     XXX     OFF                    ! CH#1 INPUT VOL
SLOT=22
0C/AB060/0:18        MOA830     XXX     OFF                    ! CH#2 INPUT VOL
SLOT=22
0C/AB09C/0:9         MOA84E     XXX     OFF                    ! CH#2 INPUT VOL
SLOT=22
0C/AB0C0/0:18        MOA860     XXX     OFF                    ! CH#3 INPUT VOL
SLOT=22
0C/AB0FC/0:9         MOA87E     XXX     OFF                    ! CH#3 INPUT VOL
SLOT=22
0C/AB120/0:18        MOA890     XXX     OFF                    ! CH#4 INPUT VOL
SLOT=22
0C/AB15C/0:9         MOA8AE     XXX     OFF                    ! CH#4 INPUT VOL
SLOT=22
0C/AB180/0:18        MOA8C0     XXX     OFF                    ! CH#5 INPUT VOL
SLOT=22
0C/AB1BC/0:9         MOA8DE     XXX     OFF                    ! CH#5 INPUT VOL
SLOT=22
0C/AB1E0/0:18        MOA8F0     XXX     OFF                    ! CH#6 INPUT VOL
SLOT=22
0C/AB21C/0:9         MOA90E     XXX     OFF                    ! CH#6 INPUT VOL
SLOT=22
0C/AB240/0:18        MOA920     XXX     OFF                    ! CH#7 INPUT VOL
SLOT=22
0C/AB27C/0:9         MOA93E     XXX     OFF                    ! CH#7 INPUT VOL
SLOT=22
0C/AB2A0/0:18        MOA950     XXX     OFF                    ! CH#8 INPUT VOL
SLOT=22
0C/AB2DC/0:9         MOA96E     XXX     OFF                    ! CH#8 INPUT VOL
SLOT=22
0C/AB300/0:7         MOA980     XXX     OFF                    ! MASTER VOLUME
SLOT=22
0C/AB310/0:6         MOA988     XXX     OFF                    ! DIRECT VOLUME
SLOT=22
0C/AB4A0             MOA98F     XXX     OFF                    ! LOW CONTROL
SLOT=22
0C/AB4A2             MIA800     XXX     OFF                    ! LOW STATUS
SLOT=22
0C/AB4A4             MOA990     XXX     OFF                    ! LOW POINTER
*
SLOT=22
0C/ABDFC             MOA991     XXX     OFF                    ! HIGH CONTROL
SLOT=22
0C/ABDFE             MIA801     XXX     OFF                    ! HIGH STATUS
SLOT=22
0C/ABE16             MIA802     XXX     OFF                    ! FORGND COUNTER
*
*
******* SLOT # 23 = DV RECORD
SLOT=23
0C/AD000/0:9         MOB000     XXX     OFF    CON ='0015'     ! ACTIVE RECO
SLOT=23
0C/AD014/0:9         MOB00A     XXX     OFF                    ! ACKNOL. RECO
SLOT=23
0C/AD028/0:9         MOB014     XXX     OFF                    ! VOLUME RECO
SLOT=23
0C/AD03C             MIB01E     XXX     OFF                    ! ACK. ERR.
*
SLOT=23
0C/(1)AD042/0:739    MIB021     XXX     OFF                    ! RECO FRAME 1
*SLOT=23
*0C/(1)AD60A/0:739    MIB305     XXX     OFF                    ! RECO FRAME 2
*
SLOT=23
0C/ADBE6             MOB5F3     XXX     OFF                    ! THRESHOLD
*
SLOT=23
0C/ADDFC             MOB6FE     XXX     OFF                    ! HIGH CONTROL
SLOT=23
0C/ADDFE             MIB6FF     XXX     OFF                    ! HIGH STATUS
SLOT=23
0C/ADE16             MIB70B     XXX     OFF                    ! FORGND COUNTER
*
*
/SOUND_AUX_DATA
*
*
//DATA=0:811D                               !Chassis interrupt timer
//DATA=1:1E00                               !status display
//DATA=2:0003                               !0=SOUND 1=AUDIO 2=HOST SYNC
//DATA=3:0000                               !Not used
*
*
*//FUNCTION=0                                !DAC RESET
*
//FUNCTION=1                                !DSG/DSP RESET
*
  000E                                      !NUMBER OF SLOT
*
  0004                                      !NUMBER OF TRANSFER
*                Offset
  1F80                                      !TASK NUMBER
  1F82                                      !NUM_SS
  1F84                                      !FORMOD
  1F86                                      !REFMOD
*                Segment
  8400                                      !SLOT XA03   SPC #1
  8600                                      !SLOT XA04   SPC #2
**8800                                      !SLOT XA05   SPC #3
  8A00                                      !SLOT XA06   DSP DVPLAY
  8C00                                      !SLOT XA07   DSP FILTER
**8E00                                      !SLOT XA08   DSP VAE1
**9000                                      !SLOT XA09   DSP VAE2
**9200                                      !SLOT XA10   DSP VAE3
**9400                                      !SLOT XA11   DSP VAE4
  9600                                      !SLOT XA12   DSP NOISE
**9800                                      !SLOT XA13   DSP VOCAL
  9A00                                      !SLOT XA14   DSG KEYER
  9C00                                      !SLOT XA15   DSG SIGNAL
  9E00                                      !SLOT XA16   DSP GENMIX 1
  A000                                      !SLOT XA17   DSP GENMIX 2
**A200                                      !SLOT XA18   DSP GENMIX 3
  A400                                      !SLOT XA19   DSP VOICE MIX 1
  A600                                      !SLOT XA20   DSP VOICE MIX 2
  A800                                      !SLOT XA21   DSP DISTMIX 1
  AA00                                      !SLOT XA22   DSP DISTMIX 2
  AC00                                      !SLOT XA23   DSP DVRECO
*                Data
  0031 0100 0001 0002                       !SPC1 (4)
  0041 0D00 0001 0002                       !SPC2 (4)
**0051 1900 0001 0002                       !SPC3 (4)
  0060 2500 0001 0001                       !DSP DVPLAY HOST SYNC (4)
  007B 0009 0001 0002                       !DSP FILTER
**0082 0000 0001 0002                       !DSP VAE 1
**0092 0000 0001 0002                       !DSP VAE 2
**00A2 0000 0001 0002                       !DSP VAE 3
**00B2 0000 0001 0002                       !DSP VAE 4
  00C3 0010 0001 0002                       !DSP NOISE
**00D6 0000 0001 0002                       !DSP VOCAL
  00E5 0000 0001 0002                       !DSG KEYER (16*3)
  00F4 0022 0001 0002                       !DSG SIGNAL
  0108 000A 0001 0002                       !DSP GEN MIX 1
  0118 000A 0001 0002                       !DSP GEN MIX 2
**0128 000A 0001 0002                       !DSP GEN MIX 3
  0137 0010 0001 0002                       !DSP VOICE MIX 1
  0147 0010 0001 0002                       !DSP VOICE MIX 2
  0157 0010 0001 0002                       !DSP DISTMIX 1
  0167 0010 0001 0002                       !DSP DISTMIX 2
  017A 0000 0001 0001                       !DSP DVRECO HOST SYNC (4)
*
//FUNCTION=2                                !DSG/DSP RUN
*
  000E                                      !NUMBER OF SLOT
*
  0002                                      !NUMBER OF TRANSFER
*                Offset
  1C80                                      !CMDBUF     WRITE  <---
  1CC0                                      !RSPBUF     READ   <---
*                Segment
  8400                                      !SLOT XA03   SPC #1
  8600                                      !SLOT XA04   SPC #2
**8800                                      !SLOT XA05   SPC #3
  8A00                                      !SLOT XA06   DSP DVPLAY
  8C00                                      !SLOT XA07   DSP FILTER
**8E00                                      !SLOT XA08   DSP VAE1
**9000                                      !SLOT XA09   DSP VAE2
**9200                                      !SLOT XA10   DSP VAE3
**9400                                      !SLOT XA11   DSP VAE4
  9600                                      !SLOT XA12   DSP NOISE
**9800                                      !SLOT XA13   DSP VOCAL
  9A00                                      !SLOT XA14   DSG KEYER
  9C00                                      !SLOT XA15   DSG SIGNAL
  9E00                                      !SLOT XA16   DSP GENMIX 1
  A000                                      !SLOT XA17   DSP GENMIX 2
**A200                                      !SLOT XA18   DSP GENMIX 3
  A400                                      !SLOT XA19   DSP VOICE MIX 1
  A600                                      !SLOT XA20   DSP VOICE MIX 2
  A800                                      !SLOT XA21   DSP DISTMIX 1
  AA00                                      !SLOT XA22   DSP DISTMIX 2
  AC00                                      !SLOT XA23   DSP DVRECO
*                Data
  000E 0000                                 !SPC1 (4)
  000E 0000                                 !SPC2 (4)
**000E 0000                                 !SPC3 (4)
  000E 0000                                 !DSP DVPLAY HOST SYNC (4)
  000E 0000                                 !DSP FILTER
**000E 0000                                 !DSP VAE 1
**000E 0000                                 !DSP VAE 2
**000E 0000                                 !DSP VAE 3
**000E 0000                                 !DSP VAE 4
  000E 0000                                 !DSP NOISE
**000E 0000                                 !DSP VOCAL
  000E 0000                                 !DSG KEYER (16*3)
  000E 0000                                 !DSG SIGNAL
  000E 0000                                 !DSP GEN MIX 1
  000E 0000                                 !DSP GEN MIX 2
**000E 0000                                 !DSP GEN MIX 3
  000E 0000                                 !DSP VOICE MIX 1
  000E 0000                                 !DSP VOICE MIX 2
  000E 0000                                 !DSP DISTMIX 1
  000E 0000                                 !DSP DISTMIX 2
  000E 0000                                 !DSP DVRECO HOST SYNC (4)
*
//FUNCTION=3                                !DSG/DSP REFRESH
*
  000E                                      !NUMBER OF SLOT
*
  0001                                      !NUMBER OF TRANSFER
*                Offset
  1D80                                      !REFHOS
*                Segment
  8400                                      !SLOT XA03   SPC #1
  8600                                      !SLOT XA04   SPC #2
**8800                                      !SLOT XA05   SPC #3
  8A00                                      !SLOT XA06   DSP DVPLAY
  8C00                                      !SLOT XA07   DSP FILTER
**8E00                                      !SLOT XA08   DSP VAE1
**9000                                      !SLOT XA09   DSP VAE2
**9200                                      !SLOT XA10   DSP VAE3
**9400                                      !SLOT XA11   DSP VAE4
  9600                                      !SLOT XA12   DSP NOISE
**9800                                      !SLOT XA13   DSP VOCAL
  9A00                                      !SLOT XA14   DSG KEYER
  9C00                                      !SLOT XA15   DSG SIGNAL
  9E00                                      !SLOT XA16   DSP GENMIX 1
  A000                                      !SLOT XA17   DSP GENMIX 2
**A200                                      !SLOT XA18   DSP GENMIX 3
  A400                                      !SLOT XA19   DSP VOICE MIX 1
  A600                                      !SLOT XA20   DSP VOICE MIX 2
  A800                                      !SLOT XA21   DSP DISTMIX 1
  AA00                                      !SLOT XA22   DSP DISTMIX 2
  AC00                                      !SLOT XA23   DSP DVRECO
*                Data
  A000                                      !SPC1 (4)
  A000                                      !SPC2 (4)
**A000                                      !SPC3 (4)
  A000                                      !DSP DVPLAY HOST SYNC (4)
  A000                                      !DSP FILTER
**A000                                      !DSP VAE 1
**A000                                      !DSP VAE 2
**A000                                      !DSP VAE 3
**A000                                      !DSP VAE 4
  A000                                      !DSP NOISE
**A000                                      !DSP VOCAL
  A000                                      !DSG KEYER (16*3)
  A000                                      !DSG SIGNAL
  A000                                      !DSP GEN MIX 1
  A000                                      !DSP GEN MIX 2
**A000                                      !DSP GEN MIX 3
  A000                                      !DSP VOICE MIX 1
  A000                                      !DSP VOICE MIX 2
  A000                                      !DSP DISTMIX 1
  A000                                      !DSP DISTMIX 2
  A000                                      !DSP DVRECO HOST SYNC (4)
*
*
*//FUNCTION=4                                !DAC RESET/ENABLE  WATCH-DOG
*
*//FUNCTION=5                                !DAC DISABLE  WATCH-DOG
*
*//FUNCTION=6                                !DAC KILL OUTPUT
*
//FUNCTION=7                                !RUN TEST
*
  000E                                      !NUMBER OF SLOT
*
  0001                                      !NUMBER OF TRANSFER
*                Offset
  1E2A                                      !RUNTEST REGISTER
*                Segment
  8400                                      !SLOT XA03   SPC #1
  8600                                      !SLOT XA04   SPC #2
**8800                                      !SLOT XA05   SPC #3
  8A00                                      !SLOT XA06   DSP DVPLAY
  8C00                                      !SLOT XA07   DSP FILTER
**8E00                                      !SLOT XA08   DSP VAE1
**9000                                      !SLOT XA09   DSP VAE2
**9200                                      !SLOT XA10   DSP VAE3
**9400                                      !SLOT XA11   DSP VAE4
  9600                                      !SLOT XA12   DSP NOISE
**9800                                      !SLOT XA13   DSP VOCAL
  9A00                                      !SLOT XA14   DSG KEYER
  9C00                                      !SLOT XA15   DSG SIGNAL
  9E00                                      !SLOT XA16   DSP GENMIX 1
  A000                                      !SLOT XA17   DSP GENMIX 2
**A200                                      !SLOT XA18   DSP GENMIX 3
  A400                                      !SLOT XA19   DSP VOICE MIX 1
  A600                                      !SLOT XA20   DSP VOICE MIX 2
  A800                                      !SLOT XA21   DSP DISTMIX 1
  AA00                                      !SLOT XA22   DSP DISTMIX 2
  AC00                                      !SLOT XA23   DSP DVRECO
*                Data
  7000                                      !SPC1 (4)
  7000                                      !SPC2 (4)
**7000                                      !SPC3 (4)
  7000                                      !DSP DVPLAY HOST SYNC (4)
  7000                                      !DSP FILTER
**7000                                      !DSP VAE 1
**7000                                      !DSP VAE 2
**7000                                      !DSP VAE 3
**7000                                      !DSP VAE 4
  7000                                      !DSP NOISE
**7000                                      !DSP VOCAL
  7000                                      !DSG KEYER (16*3)
  7000                                      !DSG SIGNAL
  7000                                      !DSP GEN MIX 1
  7000                                      !DSP GEN MIX 2
**7000                                      !DSP GEN MIX 3
  7000                                      !DSP VOICE MIX 1
  7000                                      !DSP VOICE MIX 2
  7000                                      !DSP DISTMIX 1
  7000                                      !DSP DISTMIX 2
  7000                                      !DSP DVRECO HOST SYNC (4)
*
//FUNCTION=8                                !STOP TEST
*
  000E                                      !NUMBER OF SLOT
*
  0001                                      !NUMBER OF TRANSFER
*                Offset
  1E2A                                      !RUNTEST REGISTER
*                Segment
  8400                                      !SLOT XA03   SPC #1
  8600                                      !SLOT XA04   SPC #2
**8800                                      !SLOT XA05   SPC #3
  8A00                                      !SLOT XA06   DSP DVPLAY
  8C00                                      !SLOT XA07   DSP FILTER
**8E00                                      !SLOT XA08   DSP VAE1
**9000                                      !SLOT XA09   DSP VAE2
**9200                                      !SLOT XA10   DSP VAE3
**9400                                      !SLOT XA11   DSP VAE4
  9600                                      !SLOT XA12   DSP NOISE
**9800                                      !SLOT XA13   DSP VOCAL
  9A00                                      !SLOT XA14   DSG KEYER
  9C00                                      !SLOT XA15   DSG SIGNAL
  9E00                                      !SLOT XA16   DSP GENMIX 1
  A000                                      !SLOT XA17   DSP GENMIX 2
**A200                                      !SLOT XA18   DSP GENMIX 3
  A400                                      !SLOT XA19   DSP VOICE MIX 1
  A600                                      !SLOT XA20   DSP VOICE MIX 2
  A800                                      !SLOT XA21   DSP DISTMIX 1
  AA00                                      !SLOT XA22   DSP DISTMIX 2
  AC00                                      !SLOT XA23   DSP DVRECO
*                Data
  0000                                      !SPC1 (4)
  0000                                      !SPC2 (4)
**0000                                      !SPC3 (4)
  0000                                      !DSP DVPLAY HOST SYNC (4)
  0000                                      !DSP FILTER
**0000                                      !DSP VAE 1
**0000                                      !DSP VAE 2
**0000                                      !DSP VAE 3
**0000                                      !DSP VAE 4
  0000                                      !DSP NOISE
**0000                                      !DSP VOCAL
  0000                                      !DSG KEYER (16*3)
  0000                                      !DSG SIGNAL
  0000                                      !DSP GEN MIX 1
  0000                                      !DSP GEN MIX 2
**0000                                      !DSP GEN MIX 3
  0000                                      !DSP VOICE MIX 1
  0000                                      !DSP VOICE MIX 2
  0000                                      !DSP DISTMIX 1
  0000                                      !DSP DISTMIX 2
  0000                                      !DSP DVRECO HOST SYNC (4)
*
*
//FUNCTION=9                                ! SPC MUTE
*
  0002                                      !NUMBER OF SLOT
*
  0001                                      !NUMBER OF TRANSFERT
*                Offset
  1E2A                                      !CHANNEL SELECT
*                Segment
  8400                                      !SLOT XA03   SPC #1
  8600                                      !SLOT XA04   SPC #2
**8800                                      !SLOT XA05   SPC #3
*                Data
  6000                                      !SPC1 (4)
  6000                                      !SPC2 (4)
**6000                                      !SPC3 (4)
*
*//FUNCTION=10                              !Not used
*//FUNCTION=11                              !Not used
*
*END OF SOUND_AUX_DATA
*
