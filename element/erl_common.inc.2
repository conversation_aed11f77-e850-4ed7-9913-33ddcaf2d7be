C     character*80 rvlstr1
C     . /'$Revision: erl_common.inc V1.0 (RC) June-92$'/
C
C
C'Revision_History
C
C  erl_common.inc.6  7Apr1992 14:12 s742 remic  
C       < renam public_type for public_reset >
C
C  erl_common.inc.5  2Apr1992 16:15 S742 remic  
C       < initialise the dbug 9001 9002 >
C
C  erl_common.inc.4  1Apr1992 10:01 S742 remic  
C       < added the variable public1_type >
C
C  erl_common.inc.3 16Mar1992 08:56 S742 remic  
C       < add the variable public2_type >
C
C  erl_common.inc.2 10Mar1992 15:44 S742 REMIC  
C       < Remove the variable visual_type >
C
C  erl_common.inc.1 17Feb1992 11:00 md11 REMIC  
C       < include the variable visual_type  >


      integer*2 dmcmax
      parameter (dmcmax = 20)
      integer*2 chanmax
      parameter (chanmax = 16)
      integer*4 dispmax
      parameter (dispmax = 2)
      integer*2 arimax
      parameter (arimax = 4)


      INTEGER*2 DOP_TEST,DIP_TEST,AOP_TEST,SOP_TEST,AIP_TEST,
     +          POW_TEST,SO2_TEST
 
      PARAMETER (DOP_TEST = 0,
     +           DIP_TEST = 1,
     +           AOP_TEST = 2,
     +           SOP_TEST = 3,
     +           AIP_TEST = 4,
     +           POW_TEST = 6,
     +           SO2_TEST = 7)
 
      INTEGER*2 INTERFACE_TYPE,ARINC_TYPE,ITER_TYPE,
     +          OTHER_TYPE,SWITCH_TYPE,CCU_TYPE,SPECIAL_TYPE,
     +          ARINC_WARNING,CHASSIS_TYPE, 
     +          intell_type,public_reset,public2_type,public1_type
 
      PARAMETER (INTERFACE_TYPE = 1,
     +           ARINC_TYPE     = 2,
     +           ITER_TYPE      = 3,
     +           OTHER_TYPE     = 4,
     +           SWITCH_TYPE    = 5,
     +           CCU_TYPE       = 6,
     +           ARINC_WARNING  = 7,
     +           CHASSIS_TYPE   = 8,
     +           SPECIAL_TYPE   = 9,
     +           intell_type    =12,
     +           public_reset   =15,
     +           public1_type   =16,
     +           public2_type   =17)
 
 
      INTEGER*2 DMC_ARINC_MASK     /'7C00'X/
      INTEGER*2 SLAVE_ID_MASK      /'03E0'X/
      INTEGER*2 SLOT_MASK          /'3E00'X/
      INTEGER*2 PAGE_MASK          /'0780'X/
      INTEGER*2 SEG_MASK           /'7800'X/
      INTEGER*2 DMC_STANDARD_MASK  /'007F'X/
      INTEGER*2 MASK_00FF          /'00FF'X/
      INTEGER*2 MASK_FF00          /'FF00'X/
      INTEGER*4 MASK4 /'0000FFFF'X/
 
      INTEGER*2 DEBUG_CODE(40)
 
      CHARACTER*80 FILENAME /'dmclogfl.log'/
 
C      DATA DEBUG_CODE/ '8101'X,'8102'X,'8002'X,
C     .                 '8001'X,'7002'X,'7102'X,
C     .                 '6000'X,'6001'X,'6002'X,
C     .                 '6003'X,'6004'X,'6005'X,
C     .                 '6006'X,'6007'X,'6008'X,
C     .                 '6009'X,'600A'X,'600B'X,
C     .                 '600C'X,'5FFF'X,'5FFE'X,
C     .                 '0000'X,'5FFC'X,'5FFB'X,   ! 5FFD
C     .                 '5FFA'X,'5FF9'X,'5FF8'X,
C     .                 '5FF7'X,'5FF6'X,'5FF5'X,
C     .                 '4000'X,'8101'X,'8102'X,
C     .                 '7202'X,'5FF2'X,'4200'X,
C     .                 '4202'X,'4303'X,'4400'X,
C     .                 '4304'X /
C 
      DATA DEBUG_CODE/ '9002'X,'9001'X,'9001'X,
     .                 '9001'X,'9002'X,'9001'X,
     .                 '9001'X,'9001'X,'9002'X,
     .                 '9001'X,'9002'X,'9001'X,
     .                 '9002'X,'9001'X,'9001'X,
     .                 '9001'X,'9002'X,'9001'X,
     .                 '9001'X,'9001'X,'9002'X,
     .                 '9001'X,'9001'X,'9001'X,  
     .                 '9002'X,'9001'X,'9001'X,
     .                 '9001'X,'9002'X,'9001'X,
     .                 '9002'X,'9001'X,'9001'X,
     .                 '9001'X,'9002'X,'9001'X,
     .                 '9002'X,'9001'X,'9001'X,
     .                 '9001'X /

Cspecial

      integer*2 t_slots(6), machine, errCODEnew

      character*256 temp_message

      INTEGER*4 LOG_ITER /0/
      INTEGER*4 MAX_LOG_ITER 
      PARAMETER (MAX_LOG_ITER = '7FFFFFFF'X)

Cspecial

Cfilter

      logical*1 off_line(4,0:64)
      logical*1 off_flag(4),off_force
      integer*4 off_time(4,64),off_diff
      integer*2 off_s4(4,64)
      integer*2 count2,count3(4),x_slots(64),x2_slots(4,2,64*25)
      integer*2 count4(4),x3_slots(4,4,200),tocount,okcount
Cfilter



C     Status results -------

      INTEGER*2 DISP_STAT(dispmax,17,2) ! CPU/INDEX/SELECT
      integer*4 disp_cons(dispmax,17,2) ! cpu/index/select [iter,cons]
      INTEGER*2 AR_C_STAT(dispmax,7,0:arimax,0:25,0:chanmax,2)
C                                     ! CPU/INDEX/DMC/SLAVE/CHAN/SELECT
      INTEGER*2 AR_S_STAT(dispmax,4,0:arimax,0:25,2) 
C                                     ! CPU/INDEX/DMC/SLAVE/SELECT
      INTEGER*2 AR_D_STAT(dispmax,1,0:arimax,2) ! CPU/INDEX/DMC/SELECT
      INTEGER*2 AR_STAT(dispmax,2,2)              ! CPU/INDEX/SELECT
      INTEGER*2 DMCIO_STAT(dispmax,4,0:dmcmax,2)  ! CPU/INDEX/DMC/SELECT
      INTEGER*2 CBUS_STAT(dispmax,4,0:dmcmax,25,2) ! CPU/INDEX/DMC/SLOT/SELECT
CM      INTEGER*2 CBUSM_STAT(4,4,64,2)        ! CPU/INDEX/DMC/SELECT
      integer*2 DMC_STAT(dispmax,2,0:dmcmax,2)! CPU/INDEX/DMC/SELECT
      integer*2 indicator                   ! 1=(I) 2=(E) 3=(W)

      integer*2 ar_cache(dispmax,arimax)

      COMMON /STATS/ DISP_STAT,
     +               AR_C_STAT,
     +               AR_S_STAT,
     +               AR_D_STAT,
     +               AR_STAT,
     +               DMCIO_STAT,
     +               CBUS_STAT,
CM     +               CBUSM_STAT,
     +               DMC_STAT,
     +               disp_cons,
     +               indicator,
     +               ar_cache

      integer*2 memok(200,4),memto(200,4),okptr,toptr
      common /stat2/ memok,memto,okptr,toptr


C     End Status results -------

      integer*2 no_inp_wait(0:dispmax)
      common /stat3/ no_inp_wait
 
