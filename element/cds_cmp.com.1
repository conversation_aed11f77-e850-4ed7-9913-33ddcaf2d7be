#!  /bin/csh -f
#   $Revision: CDS_CMP - Invoke the CDBS utility V1.2 (DF) Jan-92$
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#
#   Version 1.2: <PERSON><PERSON>  (27-Feb-92)
#      - for new spare labels approach
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set FSE_UNIK="`pid`.tmp.1"
set FSE_TEMP=$SIMEX_DIR/work/cdst_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/cdsl_$FSE_UNIK
set FSE_MAKE=$SIMEX_DIR/work/cdsm_$FSE_UNIK
#
echo '@$.'          >$FSE_TEMP
echo '&$.XSL'      >>$FSE_TEMP
echo '&$?.XSL'     >>$FSE_TEMP
echo '@CDB_SPARE.' >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
unalias cdbs
cdbs 
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
foreach FSE_LINE ("`grep ' Generated  ' $FSE_MAKE`")
  set FSE_FILE="`echo '$FSE_LINE' | cut -c13-`"
  set FSE_TEST="`norev $FSE_FILE`"
  set FSE_TEST="$FSE_TEST:t"
#
  set FSE_WORK="$FSE_TEST:r"
  if ("$FSE_TEST:e" == "xss") then
    set FSE_SAVE="`revl '-$SIMEX_DIR/enter/$FSE_WORK.xeq' +`"
    if ( -e "$FSE_SAVE") rm $FSE_SAVE
    setenv fse_source "$FSE_MAKE"
    setenv fse_target "$FSE_SAVE"
    setenv fse_action "X"
    fse_compile
    rm $FSE_MAKE
    exit
  endif
#
end
rm $FSE_MAKE
exit
