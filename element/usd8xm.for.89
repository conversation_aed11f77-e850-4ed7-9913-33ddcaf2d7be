C'Title            Malfunction Monitor Program
C'Module_ID        XMALMON
C'Entry_point      XMALF
C'Documentation    TBD
C'Customer         USAIR DASH-8
C'Application      Monitors malfunctions
C'Authors          Nam Tran
C'Date             01/06/91
C
C'System           I/F
C'Iteration_rate   133 msec
C'Process          Asynchronous
C'
C
C'Revision_history
C
C  usd8xm.for.14 12Jul1992 16:31 usd8 Baon
C       < Put in fixes for lesson plan malfunctions & CDB label preselect >
C
C  usd8xm.for.13  9Mar1992 11:54 usd8 baon
C       < Var malf's labels (TV labels) now contain the actual value of
C         malfunctions and not the percentage value >
C
C File: /cae/if/mmut/usd8xm.for.12
C       Modified by: Baon
C       Sat Nov  9 18:57:34 1991
C       < Incorporate malfunction collection logic for USAIR DASH-8 >
C
C File: /cae1/ship/usd8xm.for.4
C       Modified by: baon
C       Sun Nov  3 19:51:22 1991
C       < equivalenced TAMF0NUM to XMLFNUM temporarily >
C
C File: /cae/if/mmut/usd8xm.for.2
C       Modified by: Baon
C       Tue Oct 29 17:48:41 1991
C       < Declared TAMF0NUM and XMLFPSLT locally temporarily >
C
C File: /cae1/ship/aw37xm.for.60
C       Modified by: nt
C       Wed Oct  2 18:21:24 1991
C       < added logic to display values of var malf on fail summary page >
C
C File: /cae1/ship/aw37xm.for.55
C       Modified by: NT
C       Thu Sep 26 11:34:10 1991
C       < added logic to display preselect criteria on fail summary page
C         see lmlfpcr >
C
C'
C'References
C       1. Malfunction Monitor Documentation
C
      SUBROUTINE XMALMON
C
      IMPLICIT NONE
C
C'Purpose
C       The purpose of this program is to monitor all Malfunctions.
C       If a malfunction is active or preselected, its description
C       is read from the ____xm.dat, stored in the CDB XMLFDES
C       and displayed on the fail summary page at a later time.
C'
C
C'Method
C       The I/O operations are done using the BUFFER I/O
C       routines which perform the I/O asynchronously.
C       The description can then be inserted into the CDB.
C'
C
C'Input
C       ACTIVE MALFs
C       TAMALF      : number of active malfs detected from CRT
C                     malf pages by xi module on SGI or from EL
C                     malf pages by xd module on HOST
C       TAMLFTBL(#) : offsets of active malfs
C       TAMFTYPE(#) : data type of active malfs see mmut.f
C
C       PRESELECT MALFS
C       XSOFFST(#)  : offsets of preselect malfs from preselect module
C
C       TCRMALF     : clear all malfs
C
C       The descriptions are read from the Label offset/data
C       xxxxXM.DAT file.
C'
C
C'Output
C
C       XMLFDES(#,#) : malf display buffers
C       TAMF0NUM     : number of active/preslect malfs in the buffer
C       XMLFOVRF     : true when the buffer is full
C'
C
C'CDB_labels
C
C
CP    USD8    YXSTRTXRF,
CP   -        TAMFTYPE , TAMALF    , TAMLFTBL ,
CP   -        TAMF0NUM,
CP   -        XMLFNUM  ,
CP   -        TCR0MALF , TCRMALF   ,
CP   -        TCR0MFCL , TCRMFCOL  ,
CP   -        TFSTART  , TFEND     ,
CP   -        TVSTART  , TVEND     ,
CP   -        BP0      , BP9999    ,
CP   -        XMLFOFAV , XMLFDES   , XMALFOF  , XMLFOVRF ,
CP   -        XMLFPSLT ,
CP   -        XMLFINFO  ,
CP   -        XSOFFST  , XSARM     , XSCRITVAL,
CP   -        XSINDEX  , XSPRUPD   , XSNXTOFF ,
CP   -        TXSPRUPD,
CP   -        XMCOL(*),  XMTBSTAT  , XMMS(*)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:20:01 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  TVEND          ! Last Variable Malfunctions
     &, TVSTART        ! First Variable Malfunctions
C$
      INTEGER*4
     &  TAMLFTBL(50)   ! MALFUNCTION OFFSET
     &, XMCOLIDX(50)   ! INDEX OF EACH COLLECT ENTRY IN TABLE
     &, XMCOLNXT       ! NEXT AVAIL. LOCATON IN THE COLLECT TABLE
C$
      INTEGER*2
     &  TAMALF         ! NUMBER OF MALFUNCTIONS
     &, TAMF0NUM       ! # OF MALFS TO BE USED AS DARK LABEL
     &, TAMFTYPE(50)   ! MALFUNCTION TYPE
     &, XMTBSTAT(50)   ! MALF. SUMMARY TABLE STATUS
C$
      LOGICAL*1
     &  BP0            ! FIRST BP - DUMMY             0 PAON   DODUMY
     &, BP9999         ! SPARE                       88 PAON   DODUMY
     &, TCR0MALF       ! ALL MALFUNCTION
     &, TCR0MFCL       ! CLEAR ALL COLLECTED MALF AVAIL.
     &, TCRMALF        ! ALL MALFUNCTION
     &, TCRMFCOL       ! CLEAR ALL COLLECTED MALFUNCTION
     &, TFEND          ! Last Discrete Malfunctions
     &, TFSTART        ! First Discrete Malfunctions
     &, XMALFOF(50)    ! RESET MALFS ON SUMMARY PG
     &, XMCOLLEC(50)   ! COLLECT ENTRY FLAG
     &, XMLFDES(70,50) ! MALFUNCTION DESCRIPTION
     &, XMLFINFO(25,50)! MALFUNCTION SUMMARY INFORMATION
     &, XMLFOFAV(50)   ! MALFUNCTION RESET AVAILABLE
     &, XMLFOVRF       ! MALF. OVERFLOW FLAG
     &, XMLFPSLT(50)   ! PRESELECT ENTRY FLAG
     &, XMMS0MOR       ! MALF. SUMMARY MORE AVAILABLE
     &, XMMS3COL(50)   ! MALF. SUMMARY ENTRIES 3RD COLOR
     &, XMMSACT(100)   ! MALF. SUMMARY ACTIVE COLOR
     &, XMMSAV(50)     ! MALF. SUMMARY AVAIL. FOR ENTRY
     &, XMMSCOL(100)   ! MALF. SUMMARY COLLECT COLOR
     &, XMMSDES(70,50) ! MALF. SUMMARY DESCRIPTION
     &, XMMSINFO(25,50)! MALFUNCTION SUMMARY PRES/COLLECT DESC
     &, XMMSMORE       ! MALFUNCTION SUMMARY MORE FLAG
     &, XMMSPRES(100)  ! MALF. SUMMARY PRESELECT COLOR
     &, XMMSSEL(50)    ! MALF. SUMMARY ENTRY SELECTION
     &, XMMSTIT        ! MALF TITLE SELECT
     &, YXSTRTXRF      ! Start of CDB
C$
      LOGICAL*1
     &  DUM0000001(10063),DUM0000002(499),DUM0000003(99668)
     &, DUM0000004(7),DUM0000005(1606),DUM0000006(1)
     &, DUM0000007(1),DUM0000008(2),DUM0000009(183824)
     &, DUM0000010(11),DUM0000011(75),DUM0000012(10)
     &, DUM0000013(10100),DUM0000014(2624),DUM0000015(276)
     &, DUM0000016(482)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,DUM0000001,BP0,DUM0000002,BP9999,DUM0000003
     &, XMALFOF,XMLFOFAV,XMLFDES,DUM0000004,XMLFINFO,DUM0000005
     &, XMLFOVRF,XMMSTIT,DUM0000006,XMMSMORE,XMMS0MOR,XMMSDES
     &, XMMSINFO,XMMS3COL,XMMSAV,XMMSACT,XMMSPRES,XMMSCOL,XMMSSEL
     &, DUM0000007,XMTBSTAT,XMLFPSLT,XMCOLLEC,DUM0000008,XMCOLIDX
     &, XMCOLNXT,DUM0000009,TCRMALF,DUM0000010,TCRMFCOL,DUM0000011
     &, TCR0MALF,DUM0000012,TCR0MFCL,DUM0000013,TAMLFTBL,TAMALF
     &, TAMFTYPE,TAMF0NUM,DUM0000014,TVSTART,DUM0000015,TVEND
     &, TFSTART,DUM0000016,TFEND     
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      REAL*4   
     &  XMCOLVAL(2)    ! VARIABLE MALFUNCTION VALUES TO COLLECT
     &, XMCOLVTB(50)   ! VALUE TABLE OF COLLECTED MALFUNCTIONS
     &, XSCRITVAL(50,20)
C$                     ! VALUE TABLE FOR PRESELECT CRITERIA
C$
      INTEGER*4
     &  XMCOLOFF(50)   ! COLLECTED MALFUNCTION OFFSET
     &, XMCOLTBL(4,2)  ! OFFSET OF COLLECT MALF FROM PG INPUT
     &, XSINDEX(50)    ! OFFSET LOCATION OF EACH PRESELECT TABLE
     &, XSNXTOFF       ! NEXT AVAILABLE LOCATON IN THE PRE' TABLE
     &, XSOFFST(50)    ! CONTAINS THE OFFSET OF THE ARMES MALF'
C$
      INTEGER*2
     &  XMCOLNUM       ! NUMBER OF COLLECTED MALFUNCTIONS
     &, XMCOLTYP(50)   ! COLLECTED MALFUNCTION TYPE
     &, XMLFNUM        ! NUMBER OF MALFUNCTIONS (EQUIV. TO TAMF0NUM)
C$
      LOGICAL*1
     &  TXSPRUPD(6)    ! UPDATE PRESELECT FLAG
     &, XMCOLL(200)    ! MALF. COLLECTED COLOR ON PAGE
     &, XMCOLSEL(2)    ! MALFUNCTION COLLECTION SELECTED FLAG
     &, XMCOLUPD(2)    ! MALF COLLECTION PAGE UPDATE FLAG
     &, XSARM(50)      ! ARMED MALFUNCTION FLAGS
     &, XSPRUPD(2)     ! UPDATE PRESELECT FLAG
C$
      LOGICAL*1
     &  DUM0200001(15285),DUM0200002(9),DUM0200003(86)
     &, DUM0200004(4252),DUM0200005(12200),DUM0200006(102)
     &, DUM0200007(2)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,TXSPRUPD,DUM0200002,XSPRUPD,DUM0200003,XSOFFST
     &, DUM0200004,XSINDEX,XSNXTOFF,XSCRITVAL,DUM0200005,XSARM
     &, DUM0200006,XMCOLL,XMCOLOFF,XMCOLTYP,XMCOLNUM,DUM0200007
     &, XMCOLVAL,XMCOLVTB,XMCOLSEL,XMCOLUPD,XMCOLTBL,XMLFNUM   
C------------------------------------------------------------------------------
 
C
C
C
C
C'
C
      INCLUDE 'stdpres.inc'  !NOFPC include file for preselect logic
C'USD8+
      INCLUDE 'stdcol.inc'   !NOFPC
C'USD8-
C
C     ship-dependent parameters: zmaxsum, zxsdmax, zmlfdsz, zpslcvsz
C                                zmxpcrv
C
      INTEGER*4 ZMAXSUM	     ! # entries available in summary		
     .,         ZRECLEN      ! record length of malf descriptions in xm.dat
     .,         ZSLOTLEN     ! length of each summary line (& system text)
     .,         ZXSDMAX      ! max # of preselect malfs see XSOFFST
     .,         ZMAXOVRF     ! size of overflow table
     .,         ZMLFDSZ      ! size of malf descriptions XMLFDES
     .,         ZPSLCVSZ     ! size of preselect criteria values XMLFINFO
     .,         ZMXPCRV      ! tot number of preselect criteria
     .,         ZSLEEP       !
     .,         ZIOWAIT      ! waiting for I/O to complete
     .,         ZIODONE      ! I/O done - should be processed
C
      PARAMETER ( ZMAXSUM  = 15                  ! 15 displayed malfs
     .,           ZRECLEN  = 62                  ! see mmut.for
     .,           ZSLOTLEN = 62                  ! see xmlfdes & malf page
     .,           ZXSDMAX  = 50                  ! see XSOFFST
     .,           ZMAXOVRF = 40                  ! 40 malfs see tech spec
     .,           ZMLFDSZ  = 70                  ! see XMLFDES
     .,           ZPSLCVSZ = 25                  ! see XMLFINFO
     .,           ZMXPCRV  = 5                   ! ft, kt, sec & others
     .,           ZSLEEP   = '81111111'X
     .,           ZIOWAIT  = 1
     .,           ZIODONE  = 2
     .          )
C
      CHARACTER PCRUNIT(ZMXPCRV)*3           ! preselect criteria unit
      DATA      PCRUNIT / 'FT '              ! 1st
     .,                   'KT '              ! 2nd
     .,                   'SEC'              ! 3rd
     .,                   'PR '              ! other
     .,                   'PR '              ! other
     .                  /
C
C
C     Local Equivalences
C
      LOGICAL*1    XR(0:0)                   ! Redefines CDB
      REAL*4       XRREAL4(0:0)              !
      EQUIVALENCE (XR(0),      YXSTRTXRF)    ! malf labels in base 0
      EQUIVALENCE (XRREAL4(0), YXSTRTXRF)    ! malf labels in base 0
C                                            ! SHIP-DEPENDENT !!!
C --- Malfunction summary page display variables
C
      CHARACTER*1 PMLFMSG(ZMAXSUM)*(ZMLFDSZ) ! descr display see xmlfdes
     .,           PMLFPCR(ZMAXSUM)*(ZPSLCVSZ)! preselect criteria see xmlfinfo
C
      EQUIVALENCE (PMLFMSG(1)(1:1), XMLFDES) ! equiv to cdb
      EQUIVALENCE (PMLFPCR(1)(1:1), XMLFINFO)! equiv to cdb
C
C'Local_variables
C
      INTEGER*4 TVSTOFF                       ! Start of TV's
     .,         TVENDOFF                      ! End of TV's
     .,         TFSTOFF                       ! Start of TF's
     .,         TFENDOFF                      ! End of TF's
     .,         BPSTOFF                       ! Start of BP's
     .,         BPENDOFF                      ! End of BP's
     .,         TOT_TVS                       ! Total number of TV's
     .,         TOT_TFS                       ! Total number of TF's
     .,         TOT_BPS                       ! Total number of BP's
     .,         STARTXREF                     ! Start of CDB
     .,         XDI2ASZ/5/                    ! String size for XDITOA
     .,         XDI2AI4V                      ! I4VALUE FOR XDITOA xd module
     .,         ADDR                          ! /cae/lib/libcae.a
     .,         INT                           ! real to integer function
C
     .,         OVRFLOW(ZMAXOVRF)             ! Overflow buffer offset
     .,         OVRFTYP(ZMAXOVRF)             !     "      "    type
C
     .,         MLRCODE(ZMAXSUM)              ! I/O return code table
     .,         OLDMLRC(ZMAXSUM)              ! Old mlrcode
     .,         MLRDSTAT(ZMAXSUM)             ! malf read status
     .,         MLTYPE(ZMAXSUM)               ! malfunction data type
     .,         MLOFFSET(ZMAXSUM)             ! malf offset
C
     .,         RETRN                         ! *return address*
     .,         XROFF                         ! offset for search
     .,         IOFF                          ! offset
     .,         NEWOFF                        !
     .,         NEWREC                        ! record number (offset)
     .,         PRSLINDEX                     ! preselect i for xsoffst
     .,         PRSLOFFSET                    ! preslt offset from mloffset
     .,         PRES_INDEX                    ! preselect index for xsindex
     .,         NEXT_PRES                     ! next of pres_index
C
      INTEGER SLOTN,FSLOTN, I, J, K, NB, KKK  ! loop controls
C
      INTEGER*2 NEWSLOT                       !
     .,         NEWTYPE                       ! insert info data type
     .,         DATATYPE                      ! temporary for data type
C    .,         NAMALFS                       ! count active/preselect malfs
     .,         DTSIZES(7)                    ! data type sizes
     .,         PSLOT                         ! page table slot index
     .,         IREAD/1/                      ! Read pointer for overfl
     .,         IWRITE/1/                     ! Write pointer for overf
     .,         OLD_SUMM_CNT/0/               ! previous # summary entries
     .,         SUMM_CNT/0/                   ! current # summary entries
     .,         LOOP_CNT/0/                   ! Loop counter for shift up
     .,         NUM_COLLECT/0/                ! Local # of collected malfs
C
      LOGICAL*1
     .          MLEMPTY(ZMAXSUM)       ! Slot empty table
     .,         XRESET(ZMAXSUM)        ! Malfunction reset table
     .,         IRWDIFF/.FALSE./       !
     .,         FPASS/.TRUE./          ! first pass flag
     .,         FPASSIO/.TRUE./        ! I/O first pass
     .,         CLEAR_LAST/.FALSE./    ! clear last table entry
     .,         MLFPUPD                ! update malf page table
     .,         MLFFMOT                ! malf found in mloffset table
     .,         XDI2ADLZ/.FALSE./      ! display leading zero XDITOA xd module
     .,         XDI2AERR               ! error rtn flag XDI2A
C
      CHARACTER*1
     .          MLRDBUF(ZMAXSUM)*(ZRECLEN)  ! I/O Read buffer see mmut.f
     .,         LMLFMSG(ZMAXSUM)*(ZSLOTLEN) ! description  display
     .,         LMLFPCR(ZMAXSUM)*(ZPSLCVSZ) ! preselect criteria values
     .,         TEMPS*(12)                   ! temp string
C
C -- I/O labels see caelib on ibm aix systems release 10 volm 1 chap 19
C
      INTEGER*4 OP_STATUS                         ! open status call rtn code
     .,         OP_IO_STATUS                      ! open i/o status  rtn code
     .,         OP_FLAG                           ! open file flag
     .,         FD                                !
     .,         RD_STATUS                         ! read status call rtn code
     .,         RD_IO_STATUS                      ! read i/o status  rtn code
     .,         RD_POSITION                       ! read position
     .,         IO_OK                             ! io success
     .,         IO_ERR                            ! Error see stioc.inc
     .,         IO_WAIT                           ! Wait
     .,         CAE_TRNL                          ! Lognam function
C
C'USD8       CHARACTER*30 FNAME                         ! filename
       CHARACTER*50 FNAME                         ! filename
C
C'USD8+ -------------------------------------------------------------------
C
      REAL*4
     .          R4DCB(0:100)
      INTEGER*4
     .          COLINDEX              ! collect index for XMCOLOFF
     .,         COLOFFSET             ! collect offset from MLOFFSET
     .,         ZCOLMAX               ! max # of collect malfs see XMCOLOFF
     .,         CRIT_START            ! Criteria start
     .,         CRIT_END              ! Criteria end
     .,         STV_START             ! Set Value start
     .,         STV_END               ! Set Value end
     .,         OFF_START             ! Offset block start
     .,         OFF_END               ! Offset block end
     .,         OFF_SIZE              ! Offset block size
     .,         DCB_PTR               ! DCB Pointer
     .,         OFF_PTR               ! Offset block Pointer
     .,         I4DCB(0:100)          ! Equivalence to DCB
     .,         OFF_BLK(4,MAX_CRIT+1,2)
     .,         OFF_BLOCK(4,-249:250,2)
     .,         BASE_EAS              ! Base parameter to offset block
     .,         OFFSET_EAS            ! Offset parameter to offset block
     .,         BYTE_EAS              ! Bytes parameter to offset block
     .,         TYPE_EAS              ! Type parameter to offset block
     .,         HOST_DATA             ! Host data flag
     .,         LOCAL_DATA            ! Local data flag
     .,         ERROR_CODE            ! Error code
C
      INTEGER*2
     .          DCB(0:200)            ! DCB Variable
     .,         MAX_COL/50/
     .,         OFF_TBL(MAX_CRIT+1*16)
      LOGICAL*1
     .          COL_ACT/.FALSE./         ! Collect --> Active state
     .,         HOST_UPD                 ! Host update flag
     .,         PERCENT_FLAG/.FALSE./    ! Percent display flag
C
      BYTE
     .          VAL_TABLE(100)
C
      EQUIVALENCE  ( I4DCB  ,  DCB    )
     .,            ( R4DCB  ,  DCB    )
     .,            ( OFF_TBL, OFF_BLK )
C
      PARAMETER   (
     .             BASE_EAS    =  1
     .,            OFFSET_EAS  =  2
     .,            BYTE_EAS    =  3
     .,            TYPE_EAS    =  4
     .,            HOST_DATA   =  1
     .,            LOCAL_DATA  =  2
     .,            ZCOLMAX  = 50                  ! see XMCOLOFF
     .            )
C
C'USD8- --------------------------------------------------------------------
C                                                 ! see stioc.inc
       PARAMETER (OP_FLAG   =  5)                 ! read only on an old file
       PARAMETER (IO_OK     =  1)                 ! code for successful IO
       PARAMETER (IO_ERR    =  9)                 ! code for error IO
       PARAMETER (IO_WAIT   =  0)                 ! code for wait IO
C
C     Local Data
C
      DATA DTSIZES/ 1,  4,  4,  8,  1,  1, 16/
C
      ENTRY XMALF
C
C -- First Pass Code: state 1 init & open ____xm.dat
C                     state 2 io wait
C
      IF (FPASS) THEN
        IF (FPASSIO) THEN	
          STARTXREF = ADDR(YXSTRTXRF)
          TVSTOFF   = ADDR(TVSTART) - STARTXREF
          TVENDOFF  = ADDR(TVEND)   - STARTXREF
          TFSTOFF   = ADDR(TFSTART) - STARTXREF
          TFENDOFF  = ADDR(TFEND)   - STARTXREF
          BPSTOFF   = ADDR(BP0)     - STARTXREF
          BPENDOFF  = ADDR(BP9999)  - STARTXREF
          TOT_TFS   =  TFENDOFF - TFSTOFF      + 1 ! tot number of TFs
          TOT_TVS   = (TVENDOFF - TVSTOFF)/4   + 1 ! tot number of TVs
          TOT_BPS   =  BPENDOFF - BPSTOFF      + 1 ! tot number of BPs
C
          DO SLOTN = 1, ZMAXSUM
            MLEMPTY(SLOTN)  = .TRUE.               ! empty slot
            XMLFPSLT(SLOTN) = .FALSE.              ! not preselect
            MLOFFSET(SLOTN) = -1                   ! don't care
            MLRDSTAT(SLOTN) = ZSLEEP               ! do nothing
            OLDMLRC(SLOTN) = 0                     ! previous values MLRDCODE
            XRESET(SLOTN)   = .FALSE.              ! do nothing
            LMLFMSG(SLOTN) = ' '                   ! clear malf desc
            LMLFPCR(SLOTN) = ' '                   ! clear preselect criteria
C'USD8+
            XMCOLLEC(SLOTN) = .FALSE.              ! not collect
            XMMSACT(SLOTN)  = .FALSE.
            XMMSPRES(SLOTN) = .FALSE.
            XMMSCOL(SLOTN)  = .FALSE.
            XMMSAV(SLOTN)   = .FALSE.
C'USD8-
          ENDDO
          MLFPUPD = .FALSE.                        ! malf page update flag
          I = CAE_TRNL('MMUT_DAT', J, FNAME, 0)    ! 0 = global
          OP_STATUS    = 0                         ! init for cae_io_open
          OP_IO_STATUS = IO_WAIT                   ! init for cae_io_open
          CALL CAE_IO_OPEN (
     .                      OP_STATUS
     .,                     OP_IO_STATUS
     .,                     FD
     .,                    %VAL(ZRECLEN)          ! recoed size see mmut.f
     .,                     FNAME
     .,                    %VAL(OP_FLAG)
     .                     )
          FPASSIO = .FALSE.
          RETURN
C
        ELSE
C
C -- If could not open file give message
C
          IF (OP_IO_STATUS .NE. IO_WAIT) THEN    ! check open call func status
            IF (OP_IO_STATUS .EQ. IO_ERR) THEN   ! check open call f retn code
              LMLFMSG(1) = 'Can''t open...'//FNAME(1:40)
            ELSE
              FPASS = .FALSE.
            ENDIF
          ELSE
            RETURN                               ! I/O not been called yet
          ENDIF
        ENDIF
C
        RETURN
      ENDIF  ! End of IF (FPASS)
C
C -- Wait until data ready (SGI to HOST).
C    note: HOST labels are reset to 0 when malf process finished
C
      IF (TAMALF .GT. 0) THEN       ! from SGI xi.f (CRT) or HOST xd.f (EL)
         IF (TAMLFTBL(TAMALF) .EQ. 0 .OR.
     .       TAMFTYPE(TAMALF) .EQ. 0      ) THEN
         RETURN ! transferring malf labels from sgi to host in progress
         ENDIF
      ENDIF
C
C --  Special logic to delete preselect via CLEAR BUTTON
C
      IF (TCRMALF) THEN
        DO I = 1, ZMAXSUM
          XMALFOF(I) = .TRUE.  ! for preselect
        ENDDO
      ENDIF
C
      IF (TCRMFCOL) THEN
        DO I = 1, ZMAXSUM
          IF (XMCOLLEC(I)) XMALFOF(I) = .TRUE.  ! For collect
        ENDDO
      ENDIF
C
C -- Update MLRDSTAT (io read return code) when cae_io_read completed
C
C !FM      DO I = 1, ZMAXSUM
C !FM         IF (MLRCODE(I).EQ.IO_OK .AND. OLDMLRC(I).NE.IO_OK) THEN
C !FM            MLRDSTAT(I) = ZIODONE       ! cae_io_read finished
C !FM         ENDIF
C !FM         OLDMLRC(I) = MLRCODE(I)        ! previous values
C !FM      ENDDO
C !FM+
      DO I = 1, ZMAXSUM
         IF (MLRCODE(I).EQ.IO_OK) THEN
            MLRDSTAT(I) = ZIODONE       ! cae_io_read finished
            MLRCODE(I)  = 0
         ENDIF
      ENDDO
C !FM-
C
C -- Check for input from page (XMALFOF) & set appropriate reset flag
C
       DO PSLOT=1,ZMAXSUM
          XRESET(PSLOT) = XMALFOF(PSLOT)
       ENDDO
C
C -- If something is in the overflow buffer then no need to process anyt
C    just copy from CDB to local buffer
C
      IF (TAMALF .NE. 0) THEN           ! malf detected
        IF (IREAD .NE. IWRITE)THEN      ! check for overflow
          IRWDIFF = .TRUE.              ! overflow occurs
          NEWSLOT = 0                   ! go in no more room logic
        ENDIF
C
C  -- Something to be processed
C
        DO I = 1,TAMALF
C
          IF (.NOT. IRWDIFF) THEN   ! check for overflow
             IF (TAMLFTBL(I) .NE. 0 .AND. TAMFTYPE(I) .NE. 0) THEN ! input ok
                NEWOFF = TAMLFTBL(I)
                NEWTYPE = TAMFTYPE(I)
                ASSIGN 90 TO RETRN
                GO TO 1                  ! insert an active malf function
             ELSE
                NEWSLOT = 1              ! skip no more room logic
             ENDIF
          ENDIF
          IRWDIFF = .FALSE.
C
C -- No more room on the fail summary page. CRT display buffer full
C
 90       CONTINUE
C
          IF (NEWSLOT .LE. 0) THEN ! no slot found for malf -> overflow
 91         CONTINUE
            DO K = I,TAMALF        ! copy the remainders in overflow table
              IF (TAMLFTBL(K) .NE. 0 .AND. TAMFTYPE(K) .NE. 0) THEN ! inp ok
                MLFFMOT = .FALSE.
                J = 1
                DO WHILE ( .NOT. MLFFMOT .AND. J .LE. ZMAXSUM)
                  MLFFMOT = ( MLOFFSET(J) .EQ. TAMLFTBL(K) )
                  J = J + 1
                ENDDO
                IF (.NOT. MLFFMOT) THEN        ! malf not in overflow table
                  OVRFLOW(IREAD) = TAMLFTBL(K) ! save offset/datatype in
                  OVRFTYP(IREAD) = TAMFTYPE(K) ! overflow table
                  IF (IREAD .LT. ZMAXOVRF) THEN
                     IREAD = IREAD + 1
                  ELSE
                     IREAD = 1
                  ENDIF
                ENDIF
              ENDIF
              TAMLFTBL(K) = 0               ! reset host malf labels
              TAMFTYPE(K) = 0
            ENDDO
            GO TO 92                        ! end of malf process
          ENDIF
          TAMLFTBL(I) = 0                   ! reset host malf labels
          TAMFTYPE(I) = 0
        ENDDO     ! End of DO I = 1, TAMALF
      ENDIF       ! End of IF (TAMALF .NE. 0)
C
C -- Scan XMCOLOFF collect table to see if any collect should also be
C    in the summary
C
C'USD8+
      IF (TAMF0NUM .LT. ZMAXSUM) THEN  ! room available
C
        DO I = 1, ZCOLMAX              ! scan collect table
          NEWSLOT = 0                  ! reset newslot
          IF (XMCOLOFF(I) .NE. 0) THEN ! collect encountered
            NEWOFF = XMCOLOFF(I)       ! get collect offset
            ASSIGN 99 TO RETRN
            GOTO 12                    ! insert a collect malf
          ENDIF
 99       CONTINUE
        ENDDO
      ENDIF
C'USD8-
C
C -- Scan XSOFFST preselect table to see if any preselect should also be
C    in the summary
C
      IF (TAMF0NUM .LT. ZMAXSUM) THEN  ! room available
C
        DO I = 1, ZXSDMAX              ! scan preselect table
          NEWSLOT = 0                  ! reset newslot
          IF (XSARM(I)) THEN           ! preselect encountered
            NEWOFF = XSOFFST(I)        ! get preselect offset
            ASSIGN 98 TO RETRN
            GOTO 10                    ! insert a preselect malf
          ENDIF
 98   CONTINUE
C
C --- extract preselect criteria for preselect malf
C     xscritval(preselect #, criteria)
C
          IF (NEWSLOT .GE. 1 .AND. NEWSLOT .LE. ZMAXSUM) THEN ! psl in buf
               K=1                                            ! lmlfpcr index
               J=1
               DO WHILE (J .LE. ZMXPCRV .AND.                 ! criteria
     .                   K .LE. 16                )           ! 25-6 - 3
                  IF (XSCRITVAL(I,J) .NE. NULL_CRIT) THEN     ! criteria val
                     XDI2AI4V = INT(XSCRITVAL(I,J))           ! int to ascii
                     CALL XDITOA(                             ! xd subroutine
     .                     TEMPS(1:XDI2ASZ)                   ! return string
     .,                    XDI2ASZ                            ! string size
     .,                    XDI2AI4V                           ! int val
     .,                    XDI2ADLZ                           ! true=leading 0
     .,                    XDI2AERR                           ! error flag
     .                     )
                     KKK = 1  ! remove leading blanks
                     DO WHILE (TEMPS(KKK:KKK) .EQ. ' ')
                       KKK = KKK + 1
                     ENDDO
                     LMLFPCR(NEWSLOT)(K:K+(XDI2ASZ-KKK)) =
     .                TEMPS(KKK:XDI2ASZ)
                     K = K + (XDI2ASZ-KKK+1)                    ! adjust index
                     LMLFPCR(NEWSLOT)(K:K+2) = PCRUNIT(J)(1:3)  ! unit
                     K = K + 3
                  ENDIF ! criteria value
                  J = J + 1                ! next criteria
                ENDDO ! criteria
          ENDIF ! newslot
        ENDDO ! scan preselect table
      ENDIF ! room available
C
C -- Insert a malfunction description when I/O is done     ZIODONE
C    Delete malfunctions in summary which have gone *off*  ZSLEEP
C
 92   CONTINUE
      TAMALF = 0
C
      DO 200 SLOTN = 1, ZMAXSUM
        IF (.NOT. MLEMPTY(SLOTN)) THEN            ! not empty
          IF (MLRDSTAT(SLOTN) .EQ. ZIODONE) THEN  ! check for io read status
            MLRDSTAT(SLOTN) = ZSLEEP              ! done
C !FM            IF (MLRCODE(SLOTN) .NE. IO_OK) THEN
C !FM              LMLFMSG(SLOTN) = 'Read Error.'
C !FM            ELSE
C !FM              LMLFMSG(SLOTN)   = ' '              ! get malf descriptions
C !FM              LMLFMSG(SLOTN)(1:ZSLOTLEN)   = MLRDBUF(SLOTN)(1:ZSLOTLEN)
C !FM            ENDIF
C !FM+
            LMLFMSG(SLOTN)  = ' '                 ! get malf descriptions
            LMLFMSG(SLOTN)(1:ZSLOTLEN) = MLRDBUF(SLOTN)(1:ZSLOTLEN)
C !FM-
C
            MLFPUPD = .TRUE.    ! update display on page
C
C -- A new active malfunction
C
            TAMF0NUM = TAMF0NUM + 1
            IF (XMCOLLEC(SLOTN)) NUM_COLLECT = NUM_COLLECT + 1
C
          ELSE IF (MLRDSTAT(SLOTN) .EQ. ZSLEEP) THEN
            DATATYPE = MLTYPE(SLOTN)
            XROFF = MLOFFSET(SLOTN)
            IOFF = XROFF - 1                     ! relative offset
C
C -- Check for malf line reset from fail summary page
C
            IF (XRESET(SLOTN)) THEN              ! delete a malf
              IF (XMLFPSLT(SLOTN)) THEN          ! preselect malf
                PRSLINDEX  = 1                                 ! search for
                PRSLOFFSET = MLOFFSET(SLOTN)                   ! preselect
                DO WHILE ( PRSLINDEX .LE. ZXSDMAX .AND.        ! index in
     .                     PRSLOFFSET .NE. XSOFFST(PRSLINDEX)) ! XS* table
                  PRSLINDEX = PRSLINDEX + 1
                ENDDO
                IF (PRSLINDEX .LE. ZXSDMAX) THEN ! preselect index found
                  PRES_INDEX         = XSINDEX(PRSLINDEX)
                  XSINDEX(PRSLINDEX) = 0
                  NEXT_PRES          = PRES_INDEX+XSTABLE(PRES_INDEX)
C
C ---          Perform compress on XSTABLE for the deleted preselect item
C
                  DO I = NEXT_PRES, XSNXTOFF - 1
                    DO J = 1, MAX_PRES
                      IF (XSINDEX(J).EQ.I) THEN
                        XSINDEX(J) = PRES_INDEX
                      ENDIF
                    ENDDO
                    XSTABLE(PRES_INDEX) = XSTABLE(I)
                    PRES_INDEX          = PRES_INDEX + 1
                  ENDDO
C
                  XSNXTOFF          = PRES_INDEX
                  XSTABLE(XSNXTOFF) = -1
C
C ---             Reset preselect variables
C
                  DO I=1,MAX_CRIT
                    XSCRITVAL(PRSLINDEX,I) = NULL_CRIT
                  ENDDO
                  XSARM(PRSLINDEX)   = .FALSE.
                  XSOFFST(PRSLINDEX) = 0        ! remove preselect offset
                  XSPRUPD(1)         = .TRUE.   ! update preselect descriptns
                  TXSPRUPD(1)        = .TRUE.   ! for upper/lower CRT
                  XSPRUPD(2)         = .TRUE.
                  TXSPRUPD(2)        = .TRUE.
                ENDIF ! preselect index found
C'USD8+
              ELSE IF (XMCOLLEC(SLOTN)) THEN    ! remove corresponding
C                                               ! entry from collect table
                COLINDEX    = 1
                COLOFFSET   = MLOFFSET(SLOTN)
                DO WHILE ( COLINDEX .LE. ZCOLMAX .AND.
     .                     COLOFFSET .NE. XMCOLOFF(COLINDEX))
                  COLINDEX = COLINDEX + 1
                ENDDO
C
                IF (COLINDEX .LE. ZCOLMAX) THEN   ! collect offset found
                  PRES_INDEX         = XMCOLIDX(COLINDEX)
                  XMCOLIDX(COLINDEX) = 0
                  NEXT_PRES          = PRES_INDEX+XMCOLDCB(PRES_INDEX)
C
C ---          Perform compress on XSTABLE for the deleted preselect item
C
                  DO I = NEXT_PRES, XMCOLNXT - 1
                    DO J = 1, MAX_COL
                      IF (XMCOLIDX(J).EQ.I) THEN
                        XMCOLIDX(J) = PRES_INDEX
                      ENDIF
                    ENDDO
                    XMCOLDCB(PRES_INDEX) = XMCOLDCB(I)
                    PRES_INDEX           = PRES_INDEX + 1
                  ENDDO
C
                  XMCOLNXT           = PRES_INDEX
                  XMCOLDCB(XMCOLNXT) = -1
C
C ---             Reset collect variables
C
                  XMCOLUPD(1)         = .TRUE.   ! update collect descriptns
                  XMCOLOFF(COLINDEX)  = 0        ! remove collect offset
                  XMCOLVTB(COLINDEX)  = 0.0
                  XMCOLTYP(COLINDEX)  = 0.0
C
                ENDIF ! collect index found
C'USD8-
 
              ELSE                              ! active malf
                 DO NB = 1, DTSIZES(DATATYPE)   ! Set active malf off
                  XR(IOFF+NB) = '0'X
                ENDDO
              ENDIF
C
C'USD8+
            ELSE IF (XMMSSEL(SLOTN)) THEN
              IF (XMLFPSLT(SLOTN)) THEN
                PRSLINDEX  = 1                                 ! search for
                PRSLOFFSET = MLOFFSET(SLOTN)                   ! preselect
                DO WHILE ( PRSLINDEX .LE. ZXSDMAX .AND.        ! index in
     .                     PRSLOFFSET .NE. XSOFFST(PRSLINDEX)) ! XS* table
                  PRSLINDEX = PRSLINDEX + 1
                ENDDO
                IF (PRSLINDEX .LE. ZXSDMAX) THEN ! preselect index found
C
C ---           Set up DCB for directives
C
                  STV_START  = XSINDEX(PRSLINDEX) + 4
                  STV_END    = XSTABLE(STV_START) + STV_START - 1
                  CRIT_START = STV_END + 1
                  CRIT_END   = XSTABLE(CRIT_START)  + CRIT_START - 1
                  OFF_SIZE   = XSTABLE(CRIT_END + 1)
C
C ---             Set up offset block
C
                  OFF_START = CRIT_END + 1
                  OFF_END   = OFF_START + OFF_SIZE - 1
                  OFF_PTR   = 0
                  DO J = OFF_START+1, OFF_END
                    OFF_PTR = OFF_PTR + 1
                    OFF_TBL(OFF_PTR) = XSTABLE(J)
                  ENDDO
C
                  DCB_PTR = 0
                  DO J = STV_START, STV_END
                    DCB(DCB_PTR) = XSTABLE(J)
                    DCB_PTR = DCB_PTR + 1
                  ENDDO
C
C ---             Set up offset block for directives
C
                  J = 1
                  DO WHILE (OFF_BLK(OFFSET_EAS,J,HOST_DATA).NE.0)
                    OFF_BLOCK(BASE_EAS,J,HOST_DATA) =
     >                        OFF_BLK(BASE_EAS,J,HOST_DATA)
                    OFF_BLOCK(OFFSET_EAS,J,HOST_DATA) =
     >                        OFF_BLK(OFFSET_EAS,J,HOST_DATA)
                    OFF_BLOCK(BYTE_EAS,J,HOST_DATA) =
     >                        OFF_BLK(BYTE_EAS,J,HOST_DATA)
                    OFF_BLOCK(TYPE_EAS,J,HOST_DATA) =
     >                        OFF_BLK(TYPE_EAS,J,HOST_DATA)
                    J = J + 1
                  ENDDO
C
C
C ---             Call directive to set value into the CDB
C
                  CALL XDSETIN ( DCB, I4DCB, R4DCB, ERROR_CODE,
     >                           OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C
                  PRES_INDEX         = XSINDEX(PRSLINDEX)
                  XSINDEX(PRSLINDEX) = 0
                  NEXT_PRES          = PRES_INDEX+XSTABLE(PRES_INDEX)
C
C ---          Perform compress on XSTABLE for the deleted preselect item
C
                  DO I = NEXT_PRES, XSNXTOFF - 1
                    DO J = 1, MAX_PRES
                      IF (XSINDEX(J).EQ.I) THEN
                        XSINDEX(J) = PRES_INDEX
                      ENDIF
                    ENDDO
                    XSTABLE(PRES_INDEX) = XSTABLE(I)
                    PRES_INDEX          = PRES_INDEX + 1
                  ENDDO
C
                  XSNXTOFF          = PRES_INDEX
                  XSTABLE(XSNXTOFF) = -1
C
C ---             Reset preselect variables
C
                  DO I=1,MAX_CRIT
                    XSCRITVAL(PRSLINDEX,I) = NULL_CRIT
                  ENDDO
                  XSOFFST(PRSLINDEX) = 0        ! remove preselect offset
                  XSARM(PRSLINDEX)   = .FALSE.
                  XSPRUPD(1)         = .TRUE.   ! update preselect descriptns
                  TXSPRUPD(1)        = .TRUE.   ! for upper/lower CRT
                  XSPRUPD(2)         = .TRUE.
                  TXSPRUPD(2)        = .TRUE.
                ENDIF ! preselect index found
C
              ELSE IF (XMCOLLEC(SLOTN)) THEN
                COLINDEX    = 1
                COLOFFSET   = MLOFFSET(SLOTN)
                DO WHILE ( COLINDEX .LE. ZCOLMAX .AND.
     .                     COLOFFSET .NE. XMCOLOFF(COLINDEX))
                  COLINDEX = COLINDEX + 1
                ENDDO
C
                IF (COLINDEX .LE. ZCOLMAX) THEN   ! collect offset found
C
C ---           Set up DCB for directives
C
                  OFF_SIZE   = 8
                  STV_START  = XMCOLIDX(COLINDEX)
                  STV_END    = XMCOLDCB(STV_START) + STV_START - 1
C
C ---             Set up offset block
C
                  OFF_START = STV_END + 1
                  OFF_END   = OFF_START + OFF_SIZE - 1
                  OFF_PTR   = 0
                  DO J = OFF_START, OFF_END
                    OFF_PTR = OFF_PTR + 1
                    OFF_TBL(OFF_PTR) = XMCOLDCB(J)
                  ENDDO
C
                  DCB_PTR = 0
                  DO J = STV_START, STV_END
                    DCB(DCB_PTR) = XMCOLDCB(J)
                    DCB_PTR = DCB_PTR + 1
                  ENDDO
C
C ---             Set up offset block for directives
C
                  OFF_BLOCK(BASE_EAS,1,HOST_DATA) =
     >                        OFF_BLK(BASE_EAS,1,HOST_DATA)
                  OFF_BLOCK(OFFSET_EAS,1,HOST_DATA) =
     >                        OFF_BLK(OFFSET_EAS,1,HOST_DATA)
                  OFF_BLOCK(BYTE_EAS,1,HOST_DATA) =
     >                        OFF_BLK(BYTE_EAS,1,HOST_DATA)
                  OFF_BLOCK(TYPE_EAS,1,HOST_DATA) =
     >                        OFF_BLK(TYPE_EAS,1,HOST_DATA)
C
C ---             Call directive to set value into the CDB
C
                  CALL XDSETIN ( DCB, I4DCB, R4DCB, ERROR_CODE,
     >                           OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C
                ENDIF ! collect index found
              ELSE
                DO NB = 1, DTSIZES(DATATYPE)   ! Set active malf off
                  XR(IOFF+NB) = '0'X
                ENDDO
              ENDIF
C'USD8-
C
            ENDIF
C
C -- Check that entries in active slots are still active
C
            IF (XMLFPSLT(SLOTN)) THEN            ! preselect malf
              PRSLINDEX    = 1
              PRSLOFFSET   = MLOFFSET(SLOTN)
              DO WHILE ( PRSLINDEX .LE. ZXSDMAX .AND.
     .                   PRSLOFFSET .NE. XSOFFST(PRSLINDEX))
                PRSLINDEX = PRSLINDEX + 1
              ENDDO
C
              IF (PRSLINDEX .LE. ZXSDMAX) THEN   ! preselect offset found
                  GOTO 110                       ! preselect still active
              ELSE                               ! preselect offset not found
                IF (.NOT. XRESET(SLOTN)) THEN    ! not reset from fail summ pg
C
C !FM+ --- I/F bug number 217
C
                  IF (DATATYPE .GE. 1) THEN
C !FM-
                    DO NB = 1, DTSIZES(DATATYPE)
C'USD8                IF (XR(IOFF+NB) .NEQV. '00'X) THEN ! preselect cond met
                      IF (XR(IOFF+NB) .NE. '00'X) THEN ! preselect cond met
                        XMLFPSLT(SLOTN) = .FALSE.      ! preselect -> active
                        LMLFPCR(SLOTN)  = ' '          ! no psl criteria
C'USD8+
                        XMMSACT(SLOTN) = .TRUE.
                        XMMSPRES(SLOTN) = .FALSE.
                        XMMSCOL(SLOTN) = .FALSE.
C'USD8-
C
C --- get value of var malf
C
                        IF (MLTYPE(SLOTN) .EQ. 3) THEN ! check for var malf
C'USD8                          XDI2AI4V = INT(100*XRREAL4((IOFF+1)/4))
C'USD8                          IF (XDI2AI4V .LT. 0) THEN
C'USD8                            XDI2AI4V = -XDI2AI4V
C'USD8                            LMLFPCR(SLOTN)(21:21) = '-'
C'USD8                          ENDIF
C'USD8+
                          XDI2AI4V = INT(XRREAL4((IOFF+1)/4)) ! get var mal
C
                          IF ( XDI2AI4V .NE. 0 ) THEN
                            PERCENT_FLAG = .FALSE.
                          ELSE
                            XDI2AI4V = INT(100*XRREAL4((IOFF+1)/4))
                            PERCENT_FLAG = .TRUE.
                          ENDIF
C
                          IF (XDI2AI4V .LT. 0) THEN
                            XDI2AI4V = -XDI2AI4V
                            LMLFPCR(SLOTN)(1:1) = '-'
                          ENDIF
C
                          IF (PERCENT_FLAG) THEN
                            XDI2ASZ = 3
                          ELSE
                            XDI2ASZ = 4
                          ENDIF
C
                          XDI2AERR = .FALSE.
                          CALL XDITOA(
     .                                TEMPS(1:XDI2ASZ)
     .,                               XDI2ASZ
     .,                               XDI2AI4V
     .,                               XDI2ADLZ
     .,                               XDI2AERR
     .                               )
                          IF ( XDI2AERR ) THEN
                            LMLFPCR(SLOTN)(1:13) = 'Err in XDITOA'
                          ELSE
                            KKK = 1    ! removed leading blanks
                            DO WHILE (TEMPS(KKK:KKK) .EQ. ' ')
                              KKK = KKK + 1
                            ENDDO
C
                            IF (PERCENT_FLAG) THEN
                              LMLFPCR(SLOTN)(2:2+(XDI2ASZ-KKK)+1) =
     .                        TEMPS(         KKK:XDI2ASZ)      //'%'
                            ELSE
                              LMLFPCR(SLOTN)(2:2+(XDI2ASZ-KKK)) =
     .                        TEMPS(         KKK:XDI2ASZ)
                            ENDIF
                          ENDIF
C'USD8-
                          XDI2ASZ = 5  ! default value
                        ENDIF ! check for var malf
C
                        GOTO 110                       ! still active
                      ENDIF
                    ENDDO
C
C !FM+ --- I/F bug number 217
C
                  ENDIF
C !FM-
                ENDIF                 ! otherwise, preselect is reset
              ENDIF                   ! from failsummary / malfunction page
C
C'USD8+
            ELSE IF (XMCOLLEC(SLOTN)) THEN
              COLINDEX    = 1
              COLOFFSET   = MLOFFSET(SLOTN)
              DO WHILE ( COLINDEX .LE. ZCOLMAX .AND.
     .                   COLOFFSET .NE. XMCOLOFF(COLINDEX))
                COLINDEX = COLINDEX + 1
              ENDDO
C
              IF (COLINDEX .LE. ZCOLMAX) THEN   ! collect offset found
                COL_ACT = .FALSE.
                DO NB = 1, DTSIZES(DATATYPE)
                  IF (XR(IOFF+NB) .NE. '00'X) COL_ACT = .TRUE.  ! active
                ENDDO
                IF (.NOT. COL_ACT) THEN
                  LMLFPCR(SLOTN) = ' '
                  LMLFPCR(SLOTN)(1:9) = 'COLLECTED'
                  XMMSAV(SLOTN)  = .FALSE.
                  XMMSACT(SLOTN) = .FALSE.
                  XMMSPRES(SLOTN) = .FALSE.
                  XMMSCOL(SLOTN) = .TRUE.
                ELSE
                  LMLFPCR(SLOTN) = ' '
                  LMLFPCR(SLOTN)(1:9) = 'COLLECTED'
 
C
C --- get value of var malf
C
                  IF (MLTYPE(SLOTN) .EQ. 3) THEN ! check for var malf
                    XDI2AI4V = INT(XRREAL4((IOFF+1)/4)) ! get var mal
C
                    IF ( XDI2AI4V .NE. 0 ) THEN
                      PERCENT_FLAG = .FALSE.
                    ELSE
                      XDI2AI4V = INT(100*XRREAL4((IOFF+1)/4))
                      PERCENT_FLAG = .TRUE.
                    ENDIF
C
                    IF (XDI2AI4V .LT. 0) THEN
                      XDI2AI4V = -XDI2AI4V
                      LMLFPCR(SLOTN)(11:11) = '-'
                    ENDIF
C
                    IF (PERCENT_FLAG) THEN
                      XDI2ASZ = 3
                    ELSE
                      XDI2ASZ = 4
                    ENDIF
C
                    XDI2AERR = .FALSE.
                    CALL XDITOA (
     .                           TEMPS(1:XDI2ASZ)
     .,                          XDI2ASZ
     .,                          XDI2AI4V
     .,                          XDI2ADLZ
     .,                          XDI2AERR
     .                          )
                    IF ( XDI2AERR ) THEN
                      LMLFPCR(SLOTN)(1:13) = 'Err in XDITOA'
                    ELSE
                      KKK = 1    ! removed leading blanks
                      DO WHILE (TEMPS(KKK:KKK) .EQ. ' ')
                        KKK = KKK + 1
                      ENDDO
C
                      IF (PERCENT_FLAG) THEN
                        LMLFPCR(SLOTN)(12:12+(XDI2ASZ-KKK)+1) =
     .                  TEMPS(         KKK:XDI2ASZ)      //'%'
                      ELSE
                        LMLFPCR(SLOTN)(12:12+(XDI2ASZ-KKK)) =
     .                  TEMPS(         KKK:XDI2ASZ)
                      ENDIF
                    ENDIF
                    XDI2ASZ = 5  ! default value
                  ENDIF ! check for var malf
                  XMMSAV(SLOTN)  = .TRUE.
                  XMMSACT(SLOTN) = .TRUE.
                  XMMSPRES(SLOTN) = .FALSE.
                  XMMSCOL(SLOTN) = .FALSE.
                ENDIF
                GOTO 110
              ENDIF
C'USD8-
C
            ELSE                      ! active malf
              DO NB = 1, DTSIZES(DATATYPE)
C'USD8          IF (XR(IOFF+NB) .NEQV. '00'X) GOTO 110  ! still active
                IF (XR(IOFF+NB) .NE. '00'X) GOTO 110  ! still active
              ENDDO
            ENDIF
C
C -- Lost an active malfunction
C
            TAMF0NUM = TAMF0NUM - 1
C
            MLFPUPD = .TRUE.               ! update display on page
C
            LMLFMSG(SLOTN) = ' '           ! clear
            MLEMPTY(SLOTN) = .TRUE.        ! empty slot
            XMLFPSLT(SLOTN) = .FALSE.      ! not preselect
C'USD8+
            IF (XMCOLLEC(SLOTN)) THEN
              DO NB = 1, DTSIZES(DATATYPE) ! Set active malf off
                XR(IOFF+NB) = '0'X
              ENDDO
              XMCOLLEC(SLOTN) = .FALSE.    ! not collect
              NUM_COLLECT = NUM_COLLECT - 1
            ENDIF
            XMMSACT(SLOTN)  = .FALSE.
            XMMSPRES(SLOTN) = .FALSE.
            XMMSCOL(SLOTN)  = .FALSE.
            XMMSAV(SLOTN)   = .FALSE.
C'USD8+
            LMLFPCR(SLOTN) = ' '           ! no preselect criteria
            MLOFFSET(SLOTN) = -1           ! don't care
 94         IF (IREAD .NE. IWRITE) THEN    ! check for overflow
                NEWOFF = OVRFLOW(IWRITE)   ! overflow detected get offset &
                NEWTYPE= OVRFTYP(IWRITE)   ! data type from overflow table
                IF (IWRITE .LT. ZMAXOVRF) THEN
                  IWRITE = IWRITE + 1
                ELSE
                  IWRITE = 1
                ENDIF
                ASSIGN 95 TO RETRN
                GOTO 1                         ! insert an active malf
 95             CONTINUE
                IF (NEWSLOT .EQ. 999) GOTO 94  ! recheck for overflow
              ENDIF
110         CONTINUE
          ENDIF     ! End of IF (MLRDSTAT(SLOTN) .EQ. ZIODONE)... ELSE IF...
        ENDIF     ! End of IF (.NOT. MLEMPTY(SLOTN))
        XRESET(SLOTN) = .FALSE.
        XMMSSEL(SLOTN) = .FALSE.
200   CONTINUE
C
C
C -- Calculate total active malfunctions currently displayed
C
      OLD_SUMM_CNT = SUMM_CNT
      SUMM_CNT = TAMF0NUM
C
C -- If number of summary entries has decreased, set flag so that
C    tables will be shifted up
C
      IF ( SUMM_CNT.GT.0.AND.SUMM_CNT.LT.OLD_SUMM_CNT ) THEN  ! shift up
C
C -- Make sure no slots have I/O pending.  If so, don't do the
C    shift on this pass.
C
         DO SLOTN=1,ZMAXSUM
            IF (MLRDSTAT(SLOTN) .NE. ZSLEEP) GOTO 2000
         ENDDO
C
         LOOP_CNT = OLD_SUMM_CNT - SUMM_CNT
         DO I=1,LOOP_CNT
C
C -- Loop through all table entries
C
           DO SLOTN = 1, ZMAXSUM-1
C
C -- If this slot is empty, fill it with contents of next slot
C
             IF (MLEMPTY(SLOTN)) THEN
C
C -- Push up all tables indexed by 1's
C
               MLEMPTY(SLOTN)   = MLEMPTY(SLOTN+1)
               XMLFPSLT(SLOTN)  = XMLFPSLT(SLOTN+1)
C'USD8+
               XMCOLLEC(SLOTN) =  XMCOLLEC(SLOTN+1)
               XMMSACT(SLOTN)  =  XMMSACT(SLOTN+1)
               XMMSPRES(SLOTN) =  XMMSPRES(SLOTN+1)
               XMMSCOL(SLOTN)  =  XMMSCOL(SLOTN+1)
               XMMSAV(SLOTN)   =  XMMSAV(SLOTN+1)
C'USD8-
               LMLFPCR(SLOTN)   = ' '
               LMLFPCR(SLOTN)   = LMLFPCR(SLOTN+1)
               MLOFFSET(SLOTN)  = MLOFFSET(SLOTN+1)
               MLRDSTAT(SLOTN)  = MLRDSTAT(SLOTN+1)
               MLTYPE(SLOTN)    = MLTYPE(SLOTN+1)
               LMLFMSG(SLOTN)   = ' '
               LMLFMSG(SLOTN)   = LMLFMSG(SLOTN+1)
C
               MLEMPTY(SLOTN+1) = .TRUE.         ! The next slot is now 'EMPTY'
               CLEAR_LAST = .TRUE.               ! clear the last table entry
C
             ENDIF  ! End of IF (MLEMPTY(SLOTN))
           ENDDO  ! End of DO SLOTN= 1,ZMAXSUM
C
C -- If required, set bottom slot emtpy
C
           IF (CLEAR_LAST) THEN
             SLOTN = ZMAXSUM
             MLEMPTY(SLOTN)  = .TRUE.
             XMLFPSLT(SLOTN) = .FALSE.
C'USD8+
             XMCOLLEC(SLOTN) = .FALSE.
             XMMSACT(SLOTN)  = .FALSE.
             XMMSPRES(SLOTN) = .FALSE.
             XMMSCOL(SLOTN)  = .FALSE.
             XMMSAV(SLOTN)   = .FALSE.
C'USD8-
             LMLFPCR(SLOTN)  = ' '
             MLOFFSET(SLOTN) = -1
             MLRDSTAT(SLOTN) = ZSLEEP
             MLTYPE(SLOTN)   = 0
             LMLFMSG(SLOTN)  = ' '
             CLEAR_LAST = .FALSE.
           ENDIF
C
         ENDDO
C
         MLFPUPD = .TRUE.                        ! update the displayed page
C
      ENDIF  ! End of shift up logic
C
2000  CONTINUE
C
      TCR0MALF = (SUMM_CNT .GE. 1)             ! tcrmalf dark concept
      TCR0MFCL = (NUM_COLLECT .GE. 1)          ! tcrmfcol dark concept
      XMLFOVRF = (IREAD .NE. IWRITE)           ! table overflow
      MLFPUPD = .TRUE.                         ! update CDB display buffer
C
C
C
C -- Set appropriate reset flags
C
990   CONTINUE
C
      DO PSLOT=1,ZMAXSUM
        XMALFOF(PSLOT) = XRESET(PSLOT)
      ENDDO
C
C
C -- If malfunction page update required, copy from local tables to
C    CDB tables for display on page
C
      IF (MLFPUPD) THEN
C
C -- Copy description fields & Set up reset available flag
C
        DO PSLOT = 1, ZMAXSUM
          PMLFMSG(PSLOT)(1:ZSLOTLEN) = LMLFMSG(PSLOT)(1:ZSLOTLEN)
          PMLFPCR(PSLOT)(1:ZPSLCVSZ) = LMLFPCR(PSLOT)(1:ZPSLCVSZ)
          XMLFOFAV(PSLOT) = ( .NOT.(MLEMPTY(PSLOT)) )
        ENDDO
C
C -- If summary page displayed, set malf page update flag
C
        MLFPUPD = .FALSE.
      ENDIF     ! End of IF (MLFPUPD)
C
      RETURN
C
C
C -- INSERT A MALFUNCTION DESCRIPTION *SUBROUTINE*
C    for active malfunctions
C    Actually only starts the I/O going
C
1     CONTINUE
      NEWSLOT = 0
      FSLOTN = ZMAXSUM
2     CONTINUE
      IF (MLOFFSET(FSLOTN) .EQ. NEWOFF) THEN
         NEWSLOT = 999                              ! skip iread logic
         GOTO RETRN
      ENDIF
      IF (MLEMPTY(FSLOTN)) NEWSLOT = FSLOTN
      FSLOTN = FSLOTN - 1
      IF (FSLOTN .GE. 1) GO TO 2
      IF (NEWSLOT .GT. 0) THEN                      ! there is room
        LMLFPCR(NEWSLOT) = ' '
        IF (NEWTYPE .EQ. 1) THEN
          NEWREC = (NEWOFF - TFSTOFF)               ! discrete malf see mmut.f
        ELSE IF (NEWTYPE .EQ. 2) THEN
          NEWREC = TOT_TFS+TOT_TVS+(NEWOFF-BPSTOFF) ! crk breaker see mmut.f
          NEWTYPE = 1
        ELSE
          NEWREC = TOT_TFS+(NEWOFF-TVSTOFF)/4       ! variable malf see mmut.f
C'USD8    XDI2AI4V = INT(100*XRREAL4(NEWOFF/4))     ! int to ascii (malf val)
C'USD8    IF (XDI2AI4V .LT. 0) THEN
C'USD8      XDI2AI4V = -XDI2AI4V
C'USD8      LMLFPCR(SLOTN)(21:21) = '-'
C'USD8    ENDIF
C'USD8+
          XDI2AI4V = INT(XRREAL4(NEWOFF/4)) ! int to ascii (malf val)
C
          IF ( XDI2AI4V .NE. 0 ) THEN
            PERCENT_FLAG = .FALSE.
          ELSE
            XDI2AI4V = INT(100*XRREAL4(NEWOFF/4))
            PERCENT_FLAG = .TRUE.
          ENDIF
C
          IF (XDI2AI4V .LT. 0) THEN
            XDI2AI4V = -XDI2AI4V
            LMLFPCR(NEWSLOT)(1:1) = '-'     ! negative sign
          ENDIF
C
          IF (PERCENT_FLAG) THEN
            XDI2ASZ = 3                     ! 3 digits 1:100
          ELSE
            XDI2ASZ = 4                     ! 4 digits max 9999
          ENDIF
C
          XDI2AERR = .FALSE.
          CALL XDITOA (
     .                 TEMPS(1:XDI2ASZ)     ! return string
     .,                XDI2ASZ              ! string size
     .,                XDI2AI4V             ! int val
     .,                XDI2ADLZ             ! true=leading 0
     .,                XDI2AERR             ! error flag
     .                )
C
          IF ( XDI2AERR ) THEN
            LMLFPCR(SLOTN)(1:13) = 'Err in XDITOA'
          ELSE
            KKK = 1    ! removed leading blanks
            DO WHILE (TEMPS(KKK:KKK) .EQ. ' ')
              KKK = KKK + 1
            ENDDO
C
            IF (PERCENT_FLAG) THEN
              LMLFPCR(NEWSLOT)(2:2+(XDI2ASZ-KKK)+1) =
     .        TEMPS(         KKK:XDI2ASZ)      //'%'
            ELSE
              LMLFPCR(NEWSLOT)(2:2+(XDI2ASZ-KKK)) =
     .        TEMPS(         KKK:XDI2ASZ)
            ENDIF
          ENDIF
C'USD8-
          XDI2ASZ = 5                              ! default value
        ENDIF
C
        MLOFFSET(NEWSLOT) = NEWOFF
        MLEMPTY(NEWSLOT) = .FALSE.
        XMLFPSLT(NEWSLOT) = .FALSE.
C'USD8+
        XMCOLLEC(NEWSLOT) = .FALSE.
        XMMSACT(NEWSLOT)  = .TRUE.
        XMMSPRES(NEWSLOT) = .FALSE.
        XMMSCOL(NEWSLOT)  = .FALSE.
        XMMSAV(NEWSLOT)   = .FALSE.
C'USD8-
        MLTYPE(NEWSLOT) = NEWTYPE
        MLRDSTAT(NEWSLOT) = ZIOWAIT
        MLRCODE(NEWSLOT) = 0
        OLDMLRC(NEWSLOT) = 0
        RD_POSITION = 0
        CALL CAE_IO_READ ( RD_STATUS
     .,                    MLRCODE(NEWSLOT)   ! READ_IO_STATUS
     .,                   %VAL(FD)
     .,                   %VAL(ZRECLEN)       ! record size see mmut.f
     .,                    MLRDBUF(NEWSLOT)
     .,                    NEWREC
     .,                    ZRECLEN            ! read amount in byte see mmut.f
     .,                    RD_POSITION
     .                   )
C
      ELSE                        ! no more room so remove preselect
        KKK = 1                   ! malf if there is any
4       IF (XMLFPSLT(KKK)) THEN   ! preselect exists
          MLOFFSET(KKK) = -1
          MLEMPTY(KKK) = .TRUE.
          XMLFPSLT(KKK)=.FALSE.
          LMLFPCR(KKK) = ' '
          LMLFMSG(KKK) = ' '
          TAMF0NUM = TAMF0NUM - 1
          GO TO 1
        ELSE
          KKK = KKK+2                    ! try next slot
          IF(KKK .LE. ZMAXSUM) GO TO 4
        ENDIF
      ENDIF ! End of IF (NEWSLOT .GT. 0)
C
      GOTO RETRN
C
C
C -- INSERT A PRESELECT MALFUNCTION DESCRIPTION *SUBROUTINE*
C    for preselect malfunctions
C    Actually only starts the I/O going
C
 10   CONTINUE
      NEWSLOT = 0
      FSLOTN = TAMF0NUM + 1                        !
 11   CONTINUE
      IF (MLOFFSET(FSLOTN) .EQ. NEWOFF) THEN
         NEWSLOT = 998                              ! skip iread logic
         GOTO RETRN
      ENDIF
      IF (MLEMPTY(FSLOTN)) NEWSLOT = FSLOTN
      FSLOTN = FSLOTN - 1
      IF (FSLOTN .GE. 1) GO TO 11
      IF (NEWSLOT .GT. 0) THEN                      ! there is room
        IF ( (NEWOFF.GE.TFSTOFF) .AND. (NEWOFF.LE.TFENDOFF) ) THEN
          NEWTYPE = 1                               ! boolean
          NEWREC = (NEWOFF - TFSTOFF)               ! discrete malf see mmut.f
        ELSE IF ((NEWOFF.GE.BPSTOFF) .AND. (NEWOFF.LE.BPENDOFF)) THEN
          NEWTYPE = 1                               ! boolean
          NEWREC = TOT_TFS+TOT_TVS+(NEWOFF-BPSTOFF) ! crk breaker see mmut.f
        ELSE IF ((NEWOFF.GE.TVSTOFF) .AND. (NEWOFF.LE.TVENDOFF)) THEN
          NEWTYPE = 3
          NEWREC = TOT_TFS+(NEWOFF-TVSTOFF)/4       ! variable malf see mmut.f
        ELSE
C
C !FM+ --- I/F bug number 217
C
          NEWTYPE = -1
          MLOFFSET(NEWSLOT) = NEWOFF
          MLEMPTY(NEWSLOT)  = .FALSE.
          XMLFPSLT(NEWSLOT) = .TRUE.
C'USD8+
          XMCOLLEC(NEWSLOT) = .FALSE.
          XMMSACT(NEWSLOT)  = .FALSE.
          XMMSPRES(NEWSLOT) = .TRUE.
          XMMSCOL(NEWSLOT)  = .FALSE.
          XMMSAV(NEWSLOT)   = .FALSE.
C'USD8-
          MLTYPE(NEWSLOT)   = NEWTYPE
          MLRDSTAT(NEWSLOT) = ZIODONE
          MLRDBUF(NEWSLOT)  = ' '
          MLRDBUF(NEWSLOT)(1:9) = 'CDB LABEL'
C !FM-
          GOTO RETRN
        ENDIF
C
        MLOFFSET(NEWSLOT) = NEWOFF
        MLEMPTY(NEWSLOT) = .FALSE.
        XMLFPSLT(NEWSLOT) = .TRUE.
C'USD8+
        XMCOLLEC(NEWSLOT) = .FALSE.
        XMMSACT(NEWSLOT)  = .FALSE.
        XMMSPRES(NEWSLOT) = .TRUE.
        XMMSCOL(NEWSLOT)  = .FALSE.
        XMMSAV(NEWSLOT)   = .FALSE.
C'USD8-
        MLTYPE(NEWSLOT) = NEWTYPE
        MLRDSTAT(NEWSLOT) = ZIOWAIT
        MLRCODE(NEWSLOT) = 0
        OLDMLRC(NEWSLOT) = 0
        RD_POSITION = 0
        CALL CAE_IO_READ ( RD_STATUS
     .,                    MLRCODE(NEWSLOT)  ! READ_IO_STATUS
     .,                   %VAL(FD)
     .,                   %VAL(ZRECLEN)      ! record size see mmut.f
     .,                    MLRDBUF(NEWSLOT)
     .,                    NEWREC
     .,                    ZRECLEN           ! read amount in byte see mmut.f
     .,                    RD_POSITION
     .                   )
C
      ENDIF ! End of IF (NEWSLOT .GT. 0)
C
      GOTO RETRN
C
C'USD8+ --------------------------------------------------------------
C
C -- INSERT A COLLECT MALFUNCTION DESCRIPTION *SUBROUTINE*
C    for collect malfunctions
C    Actually only starts the I/O going
C
 12   CONTINUE
      NEWSLOT = 0
      FSLOTN = TAMF0NUM + 1                        !
 13   CONTINUE
      IF (MLOFFSET(FSLOTN) .EQ. NEWOFF) THEN
         NEWSLOT = 997                              ! skip iread logic
         GOTO RETRN
      ENDIF
      IF (MLEMPTY(FSLOTN)) NEWSLOT = FSLOTN
      FSLOTN = FSLOTN - 1
      IF (FSLOTN .GE. 1) GO TO 13
      IF (NEWSLOT .GT. 0) THEN                      ! there is room
        IF ( (NEWOFF.GE.TFSTOFF) .AND. (NEWOFF.LE.TFENDOFF) ) THEN
          NEWTYPE = 1                               ! boolean
          NEWREC = (NEWOFF - TFSTOFF)               ! discrete malf see mmut.f
        ELSE IF ((NEWOFF.GE.BPSTOFF) .AND. (NEWOFF.LE.BPENDOFF)) THEN
          NEWTYPE = 1                               ! boolean
          NEWREC = TOT_TFS+TOT_TVS+(NEWOFF-BPSTOFF) ! crk breaker see mmut.f
        ELSE IF ((NEWOFF.GE.TVSTOFF) .AND. (NEWOFF.LE.TVENDOFF)) THEN
          NEWTYPE = 3
          NEWREC = TOT_TFS+(NEWOFF-TVSTOFF)/4       ! variable malf see mmut.f
        ELSE
          NEWSLOT = 997
          GOTO RETRN
        ENDIF
C
        MLOFFSET(NEWSLOT) = NEWOFF
        MLEMPTY(NEWSLOT) = .FALSE.
        XMLFPSLT(NEWSLOT) = .FALSE.
        XMCOLLEC(NEWSLOT) = .TRUE.
        XMMSACT(NEWSLOT)  = .FALSE.
        XMMSPRES(NEWSLOT) = .FALSE.
        XMMSCOL(NEWSLOT)  = .TRUE.
        XMMSAV(NEWSLOT)   = .FALSE.
        MLTYPE(NEWSLOT) = NEWTYPE
        MLRDSTAT(NEWSLOT) = ZIOWAIT
        MLRCODE(NEWSLOT) = 0
        OLDMLRC(NEWSLOT) = 0
        RD_POSITION = 0
        CALL CAE_IO_READ ( RD_STATUS
     .,                    MLRCODE(NEWSLOT)  ! READ_IO_STATUS
     .,                   %VAL(FD)
     .,                   %VAL(ZRECLEN)      ! record size see mmut.f
     .,                    MLRDBUF(NEWSLOT)
     .,                    NEWREC
     .,                    ZRECLEN           ! read amount in byte see mmut.f
     .,                    RD_POSITION
     .                   )
C
      ENDIF ! End of IF (NEWSLOT .GT. 0)
C
      GOTO RETRN
C
C'USD8---------------------------------------------------------------------
C
      END
