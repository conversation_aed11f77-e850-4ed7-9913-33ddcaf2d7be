C'Title             Weather Data Processing
C'Module_ID         GWX3
C'Entry_point       WX3
C'Application       Dash 8 Weather Radar Simulation
C'Author            <PERSON>'Date              20 Sept 91
C
C'System            Radar
C'Itrn_rate         266 milliseconds
C'Process           CPU0.S00
C
C
      SUBROUTINE USD8GWX3
C
C'Revision_History
C
C
      IMPLICIT NONE
C
C
C'Purpose
C
C       This module computes the cloud position according to the
C       instructor station data.
C'
C
C'Theory
C
C       By knowing the A/C position and the cloud position, this module
C       can update the microprocessor buffer with data which is used by
<PERSON>       the micro code to display the cells on the indicators. This module
C       is subdivided into six sections:
C
C               - Weather Front Processing
C               - Cell Processing
C               - Turbulence
C               - Special Effects
C               - Tilt Effect
C               - Microprocessor Buffer Update
C'
C
C'Inputs
C
C       - Weather front and cell parameters
C       - Aircraft parameters
C       - Weather radar system parameters
C'
C
C'Outputs
C
C       - Microprocessor buffer:
C              (1)  Cell type
C              (2)  Cell scaled length
C              (3)  Cell scaled width
C              (4)  Cell orientation wrt to own bearing
C              (5)  Cell range
C              (6)  Cell bearing wrt A/C
C              (7)  Cell levels due to tilt for pilot and copilot
C       - Flight turbulence effect
C       - Visual and sound effects
C'
C
C        ***************************
C        *                         *
C        *        VARIABLES        *
C        *                         *
C        ***************************
C
C
C
C'Data_Base_Variables
C
CQ    USD8 XRFTEST*
C
CE    INTEGER*2 MICROA(8,20)            ,  !  Download buffer
CE    EQUIVALENCE(MICROA(1,1) , GYTYP1A),
C
CE    INTEGER*2 MICROB(8,20)            ,  !  Download buffer
CE    EQUIVALENCE(MICROB(1,1) , GYTYP1B)
C
C     MICROA(1,n) = GYTYPnA | MICROB(1,n) = GYTYPnB
C     MICROA(2,n) = GYLNTnA | MICROB(2,n) = GYLNTnB
C     MICROA(3,n) = GYWDTnA | MICROB(3,n) = GYWDTnB
C     MICROA(4,n) = GYORInA | MICROB(4,n) = GYORInB
C     MICROA(5,n) = GYRNGnA | MICROB(5,n) = GYRNGnB
C     MICROA(6,n) = GYBEAnA | MICROB(6,n) = GYBEAnB
C     MICROA(7,n) = GYLVLnA | MICROB(7,n) = GYLVLnB
C     MICROA(8,n) = GYLVRnA | MICROB(8,n) = GYLVRnB
C
C     Inputs
C     ------
C
CP    USD8 GIOFILE          , !  WXDB I/O access flag
CP   -     GMTILTL          , !  Antenna tilt angle left
CP   -     GMTILTR          , !  Antenna tilt angle right
CP   -     GMPWR            , !  Vertical profile enable
CP   -     GWCACT           , !  WXDB cloud activation flag
CP   -     GWCALT           , !  WXDB cloud altitude
CP   -     GWCLNT           , !  WXDB cloud length
CP   -     GWCRTS           , !  WXDB cloud rotational speed
CP   -     GWCSPD           , !  WXDB cloud velocity
CP   -     GWCTHK           , !  WXDB cloud thickness
CP   -     GWCTRK           , !  WXDB cloud track angle
CP   -     GWCTYP           , !  WXDB cloud type
CP   -     GWCWDT           , !  WXDB cloud width
CP   -     GYFBINA          , !  Cloud density value
CP   -     GY25SRLA         , !  325C25 selected range
CP   -     RUCOSLAT         , !  A/C latitude cosine
CP   -     RUPLAT           , !  A/C latitude
CP   -     RUPLON           , !  A/C longitude
CP   -     TACNBR           , !  I/F cloud number
CP   -     TASWDIR          , !  Surface wind direction
CP   -     TASWSPD          , !  Surface wind speed
CP   -     TAWDIR           , !  Wind direction
CP   -     TAWSPD           , !  Wind speed
CP   -     TAWTRB           , !  I/F weather front turbulence level
CP   -     TCMEFX           , !  I/F weather front effects flag
CP   -     TCMWND           , !  I/F weather front wind flag
CP   -     VHS              , !  A/C altitude
CP   -     VPSIDG           , !  A/C corrected heading
CP   -     YISHIP           , !  Ship ID
C
C     Outputs
C     -------
C
CP   -     GALAT            , !  A/C latitude
CP   -     GALON            , !  A/C longitude
CP   -     GOCTALT          , !  Visual cloud altitude
CP   -     GOCTBOT          , !  Visual cloud deck bottom
CP   -     GOCTDLA          , !  Visual cloud delta latitude
CP   -     GOCTDLO          , !  Visual cloud delta longitude
CP   -     GOCTLAT          , !  Visual cloud latitude
CP   -     GOCTLON          , !  Visual cloud longitude
CP   -     GOCTORI          , !  Visual cloud orientation
CP   -     GOCTTOP          , !  Visual cloud deck top
CP   -     GOCTTYP          , !  Visual cloud type
CP   -     GOLIGHT          , !  Lightning flag
CP   -     GORAIN           , !  Rain level
CP   -     GOSOUND          , !  Thunder flag
CP   -     GOTURB           , !  Turbulence level
CP   -     GOVISIB          , !  Visibility level
CP   -     GWCBEA           , !  WXDB cloud bearing
CP   -     GWCLAT           , !  WXDB cloud latitude
CP   -     GWCLON           , !  WXDB cloud longitude
CP   -     GWCORI           , !  WXDB cloud orientation
CP   -     GWCRNG           , !  WXDB cloud range
CP   -     GWFBLD           , !  Weather front buildup factor
CP   -     GWFFON           , !  Weather front ON flag
CP   -     GWFLAT           , !  Weather front latitude
CP   -     GWFLON           , !  Weather front longitude
CP   -     GWFNBR           , !  Weather front number
CP   -     GWFORI           , !  Weather front orientation
CP   -     GYTYP1A          , !  Microprocessor download buffer start
CP   -     GYTYP1B          , !  Microprocessor download buffer start
CP   -     TAWLAT           , !  I/F weather front latitude
CP   -     TAWLON             !  I/F weather front longitude
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:45:43 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  GALAT          ! AIRCRAFT LATITUDE                      [Deg]
     &, GALON          ! AIRCRAFT LONGITUDE                     [Deg]
     &, GOCTALT        ! VISUAL CLOUD ALTITUDE                   [Ft]
     &, GOCTBOT        ! VISUAL CLOUD DECK BOTTOM                [Ft]
     &, GOCTDLA        ! VISUAL CLOUD DELTA LAT                 [Deg]
     &, GOCTDLO        ! VISUAL CLOUD DELTA LON                 [Deg]
     &, GOCTLAT        ! VISUAL CLOUD LATITUDE                  [Deg]
     &, GOCTLON        ! VISUAL CLOUD LONGITUDE                 [Deg]
     &, GOCTORI        ! VISUAL CLOUD ORIENTATION               [Deg]
     &, GOCTTOP        ! VISUAL CLOUD DECK TOP                   [Ft]
     &, GORAIN         ! RAIN SOUND LEVEL                       [N/A]
     &, GOVISIB        ! VISUAL VISIBILITY FACTOR               [N/A]
     &, GWCBEA(20)     ! CLOUD BEARING WRT A/C                  [Deg]
     &, GWCLAT(20)     ! CLOUD LATITUDE                         [Deg]
     &, GWCLNT(20)     ! CLOUD LENGTH                           [Nmi]
     &, GWCLON(20)     ! CLOUD LONGITUDE                        [Deg]
     &, GWCORI(20)     ! CLOUD ORIENTATION                      [Deg]
     &, GWCRNG(20)     ! CLOUD RANGE WRT A/C                    [Nmi]
     &, GWCRTS(20)     ! CLOUD ROTATIONAL SPEED                 [D/S]
     &, GWCSPD(20)     ! CLOUD VELOCITY                         [Kts]
     &, GWCWDT(20)     ! CLOUD WIDTH                            [Nmi]
     &, GWFBLD         ! WEATHER FRONT BUILDUP FACTOR           [Sec]
     &, GWFLAT         ! WEATHER FRONT CENTER LATITUDE          [Deg]
     &, GWFLON         ! WEATHER FRONT CENTER LONGITUDE         [Deg]
     &, RUCOSLAT       ! COS A/C LAT
     &, TASWDIR        ! SEA LEVEL WIND DIRECTION
     &, TASWSPD        ! SEA LEVEL WIND SPEED
     &, TAWDIR(5)      ! WIND DIRECTION AT FLIGHT LEVEL      [Degs ]
     &, TAWLAT         ! WX FRONT LATITUDE (+/-90)           [Degs ]
     &, TAWLON         ! WX FRONT LONGITUDE (+/-180)         [Degs ]
     &, TAWSPD(5)      ! WIND SPEED AT FLIGHT LEVEL          [Knots]
      REAL*4   
     &  VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
C$
      INTEGER*4
     &  GMTILTL        ! ANTENNA TILT ANGLE                     [Deg]
     &, GMTILTR        ! ANTENNA TILT ANGLE                     [Deg]
     &, GOCTTYP        ! VISUAL CLOUD TYPE                      [N/A]
     &, GOTURB         ! FLIGHT TURBULENCE ON A/C               [N/A]
     &, GWCALT(20)     ! CLOUD ALTITUDE ABOVE SEA LEVEL          [Ft]
     &, GWCTHK(20)     ! CLOUD THICKNESS                         [Ft]
     &, GWCTRK(20)     ! CLOUD TRACK ANGLE                      [Deg]
     &, GWCTYP(20)     ! CLOUD TYPE                             [N/A]
     &, GWFNBR         ! WEATHER FRONT NUMBER                   [N/A]
     &, GWFORI         ! WEATHER FRONT ORIENTATION              [Deg]
     &, YISHIP         ! Ship name
C$
      INTEGER*2
     &  GY25SRLA       ! 320C25 SELECTED RANGE           [N/A] MOC0AF
     &, GYFBINA(8)     ! RANGE BIN, SYNC, SPARES         [N/A] MIC000
     &, GYTYP1A        ! CLOUD TYPE                      [N/A] MOC000
     &, GYTYP1B        ! CLOUD TYPE                      [N/A] MOC100
     &, TACNBR         ! WX CELL NUMBER (1-20)
     &, TAWTRB         ! WX FRONT TURBULENCE (0-100%)
C$
      LOGICAL*1
     &  GIOFILE        ! WXDB OPEN FLAG                         [N/A]
     &, GMPWR          ! INDICATOR POWER                        [N/A]
     &, GOLIGHT        ! VISUAL LIGHTNING FLAG                  [N/A]
     &, GOSOUND        ! THUNDER SOUND FLAG                     [N/A]
     &, GWCACT(20)     ! CLOUD ACTIVATION STATUS                [N/A]
     &, GWFFON         ! WEATHER FRONT ON FLAG                  [N/A]
     &, TCMEFX         ! WX FRONT AUDIO/VISUAL EFFECTS
     &, TCMWND         ! WX FRONT WIND EFFECTS
C$
      LOGICAL*1
     &  DUM0000001(32),DUM0000002(5992),DUM0000003(348)
     &, DUM0000004(112),DUM0000005(5898),DUM0000006(5324)
     &, DUM0000007(248),DUM0000008(20444),DUM0000009(275072)
     &, DUM0000010(208),DUM0000011(2181),DUM0000012(97)
     &, DUM0000013(16),DUM0000014(66),DUM0000015(1658)
     &, DUM0000016(12),DUM0000017(80),DUM0000018(7)
     &, DUM0000019(72),DUM0000020(11),DUM0000021(3)
     &, DUM0000022(80),DUM0000023(3),DUM0000024(18)
     &, DUM0000025(1)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YISHIP,DUM0000002,GYTYP1A,DUM0000003,GY25SRLA
     &, DUM0000004,GYTYP1B,DUM0000005,GYFBINA,DUM0000006,VPSIDG
     &, DUM0000007,VHS,DUM0000008,RUPLAT,RUPLON,RUCOSLAT,DUM0000009
     &, TAWSPD,TAWDIR,DUM0000010,TASWDIR,TASWSPD,DUM0000011,TCMEFX
     &, TCMWND,DUM0000012,TAWLAT,TAWLON,DUM0000013,TAWTRB,DUM0000014
     &, TACNBR,DUM0000015,GWFLAT,GWFLON,GWFORI,DUM0000016,GWCTYP
     &, GWCLNT,GWCWDT,GWCTHK,GWCORI,GWCRTS,GWCSPD,GWCTRK,GWCLAT
     &, GWCLON,GWCALT,GWCACT,DUM0000017,GWFNBR,GWFFON,DUM0000018
     &, GWFBLD,GWCBEA,GWCRNG,DUM0000019,GIOFILE,DUM0000020,GOLIGHT
     &, DUM0000021,GOVISIB,GOCTLAT,GOCTLON,GOCTALT,GOCTORI,GOCTTYP
     &, GOCTDLA,GOCTDLO,GOCTTOP,GOCTBOT,DUM0000022,GOSOUND,DUM0000023
     &, GORAIN,GOTURB,GALAT,GALON,DUM0000024,GMPWR,DUM0000025
     &, GMTILTL,GMTILTR   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*2
     &  MICROA(8,20)     
     &, MICROB(8,20)     
C$
      EQUIVALENCE
     &  (MICROA(1,1),GYTYP1A),(MICROB(1,1),GYTYP1B)                     
C------------------------------------------------------------------------------
C
C'
C
C'IDENT
C
      CHARACTER*55
     &          REV
     &              /
     -  '$Source: usd8gwx3.for.4  7Jan1992 08:01 usd8 correct$'/
C'
C
C'Local_Variables
C
      LOGICAL*1 FIRST     , !  First pass flag
     &          LIGHT     , !  Lightning flag
     &          BOARD1    , !  Identification of board
     &          BOARD2    , !  Identification of boards
     &          NEWWF     , !  New weather front flag
     &          SPX       , !  Redifusion's SPX visual flag
     &          MILES     , !  Redifusion's SPX visual flag
     &          VIT       , !  MDAC's VITAL visual flag
     &          HVI       , !  Hitachi's HIVIS visual flag
     &          LUSAR       !  Swissair configuration flag
C
      REAL*4 BEAROC1      , !  Previous cell bearing
     &       BEAROC2      , !  Previous cell bearing
     &       CELLLVL(20)  , !  Cell slice index
     &       CELLVIS(20)  , !  Cell visibility
     &       CELLBEA(20)  , !  Cell bearing
     &       CELLSIZE(20) , !  Cell size
     &       CHPI         , !  090:00:00 deg
     &       CHPIM        , !  089:59:59 deg
     &       CMAXVIS      , !  Maximum scaled cell visibility
     &       CMAXMIC      , !  Maximum scaled cell range
     &       CNMIFT       , !  NMI to FT conversion factor
     &       COSBEA       , !  Delta bearing cosine
     &       COSLAT1      , !  Aircraft latitude cosine
     &       COSLAT2      , !  Cloud latitude cosine
     &       COSLON       , !  Delta longitude cosine
     &       COSRNG       , !  Range cosine
     &       CPI          , !  180:00:00 deg
     &       CR           , !  Earth's radius
     &       CROCVIS      , !  Visibility rate limit
     &       C2PI         , !  360:00:00 deg
     &       DEG2NMI      , !  DEG to NMI conversion factor
     &       DEG2RAD      , !  DEG to RAD conversion factor
     &       DELVIS       , !  Delta visibility
     &       DELNLAT      , !  Delat new latitude
     &       DELNLON      , !  Delat new longitude
     &       DISABLE      , !  Cell disable value
     &       DLON         , !  Longitude displacement
     &       FSIZE        , !  Cloud turbulence range
     &       ITER         , !  Angular velocity factor
     &       ITERATION    , !  Iteration rate
     &       LOCALT         !  Visual cloud altitude (average)
C
      REAL*4 MAXLVL(6)    , !  Maximum number of cloud levels
     &       MINVIS       , !  Minimum visibility level
     &       NEWBEA       , !  Updated bearing
     &       NEWRNG       , !  Updated range
     &       PREVBEA      , !  Previous bearing
     &       PREVRNG      , !  Previous range
     &       RAD2DEG      , !  RAD to DEG conversion factor
     &       R1VIS(20)    , !  Full visibility range
     &       R2VIS(20)    , !  Zero visibility range
     &       R3VIS(20)    , !  Full rain sound range
     &       SANGLE       , !  Scaling factor for angles
     &       SDIST        , !  Scaling factor for distances
     &       SINLAT1      , !  Aircraft latitude sine
     &       SINLAT2      , !  Cloud latitude sine
     &       SINRNG       , !  Range sine
     &       STURB        , !  Turbulence scaling factor
     &       TANTLTL      , !  Antenna tilt tangent left
     &       TANTLTR      , !  Antenna tilt tangent right
     &       TEMP         , !  Temporary variable
     &       THETA        , !  Temporary variable
     &       THETA1       , !  Temporary variable
     &       THETA2       , !  Temporary variable
     &       TURB         , !  Cloud turbulence
     &       VISIB        , !  Cloud visibility
     &       BEA, RNG     , !  Tempo range & bearing variable
     &       WCASE(20)    , !  Cloud angular velocity update east
     &       WCASN(20)    , !  Cloud angular velocity update north
     &       WCROCBEA(20) , !  Cloud bearing rate of change
     &       WCROCRNG(20) , !  Cloud range rate of change
     &       WCTRK        , !  Cloud track angle
     &       WFASE        , !  WF angular velocity update east
     &       WFASN        , !  WF angular velocity update north
     &       WFSPD        , !  Weather front speed
     &       WFTRK        , !  Weather front track angle
     &       RAINSND(20)  , !
     &       RAINLVL(6)   , !
     &       MAXRAIN        !
C
      INTEGER*4
     &          ACTCNT    , !  Active cloud count
     &          INDEX(20) , !  Visual cloud index
     &          HEAVY     , !  Heavy turbulence
     &          TBLVL     , !  Turbulence on Level
     &          LEVEL     , !  Cell slice index
     &          OLDORI    , !  Last orientation
     &          OLDNBR    , !  Last wf number
     &          MAXCELL   , !  Maximum number of clouds
     &          VISWDT    , !  Visual width angle
     &          USD8      , !  Ship identifier
     &          I , J , K , !  Counters
     &          L , M , N   !  Counters
C'
C
C'Constants
C
      PARAMETER(CHPI       = 90.0        , ! 090:00:00 deg
     &          CHPIM      = 89.983333333, ! 089:59:59 deg
     &          CMAXVIS    = 1.0         , ! Maximum scaled cell visibility
     &          CMAXMIC     = 32767.0    , ! Maximum scaled cell range
     &          CNMIFT     = 59.335      , ! NMI to FT conversion factor
CIBM+
     &           USD8      = Z'55534438'   , !  American Airlines
CIBM-
C
CVAX++        ------- VAX Code -------
CVAX      &           USD8      = 'USD8'        , !
CVAX-            ------------------------
C
CSEL++        ------- SEL Code -------
CSEL      &           USD8      = 'USD8'        , !
CSEL-            ------------------------
C
     &          CPI        = 180.0       , ! 180:00:00 deg
     &          CR         = 3440.0      , ! Earth's radius
     &          CROCVIS    = 0.2         , ! Visibility rate limit
     &          C2PI       = 360.0       , ! 360:00:00 deg
     &          DEG2NMI    = 60.0        , ! DEG to NMI conversion factor
     &          DEG2RAD    = 0.017453293 , ! DEG to RAD conversion factor
     &          DISABLE    = -1.0        , ! Disable cell value
     &          ITERATION  = 0.266       , ! Iteration rate
     &          MAXCELL    = 20          , ! Maximum number of clouds
     &          RAD2DEG    = 57.29577951 , ! RAD to DEG conversion factor
     &          SANGLE     = 16384.0/90.0, ! Scaling factor for angles
     &          SDIST      = 51.2        , ! Scaling factor for distances
     &          STURB      = 16.0        , ! Turbulence scaling factor
     &          VISWDT     = 150         ) ! Visual width angle
C'
C
C'Data
C
      DATA FIRST  /.TRUE./  ,               ! First pass flag
     &     INDEX  /20*0/,                   ! Visual cloud index
     &     L      /0/       ,               ! Process cloud index
     &     MAXLVL /10,20,20,30,36,42/ ,     ! Max number of cloud levels
     &     RAINLVL/0.4,0.6,0.8,1.0,1.0,1.0/
C'
C
      ENTRY WX3
C
C
C -- First pass
C    ==========
C
C=GWX000
C
      IF (FIRST) THEN
         FIRST  = .FALSE.
         LUSAR  = (YISHIP .EQ. USD8)
C
         BOARD1 = LUSAR
         BOARD2 = .FALSE.
         MILES  = LUSAR
         SPX    = .FALSE.
         HVI    = .FALSE.
         VIT    = .FALSE.
C
         DO I = 1, MAXCELL
            WCASE(I) = 0.0
            WCASN(I) = 0.0
         ENDDO
         VISIB   = 0.00
         DELNLAT = 0.0
         DELNLON = 0.0
      ENDIF
C
C
C -- Check if system available
C    =========================
C
C=GWX010
C
      IF (GIOFILE) RETURN
C
      IF (.NOT. GWFFON) THEN
         NEWWF    = .TRUE.
         DO I=1,MAXCELL
            MICROA(5,I) = DISABLE
         ENDDO
         GOTURB   = 0
         GORAIN   = 0.0
         GOVISIB  = CMAXVIS
         GOSOUND  = .FALSE.
         GOLIGHT  = .FALSE.
         GOCTTYP  = 0
         GOCTDLA  = 0
         GOCTDLO  = 0
         RETURN
      ENDIF
C
C
C -- A/C latitude and longitude
C    ==========================
C
C=GWX020
C
      GALAT = SNGL(RUPLAT)
      GALON = SNGL(RUPLON)
C
C
C -- Antenna tilt functions
C    ======================
C
C=GWX030
C
      TANTLTL = TAN(GMTILTL*0.25*DEG2RAD)
      TANTLTR = TAN(GMTILTR*0.25*DEG2RAD)
C
C
C  *********************************
C  *                               *
C  *  WEATHER FRONT REPOSITIONING  *
C  *                               *
C  *********************************
C
C
C -- Process weather front parameters?
C    =================================
C
C=GWX100
C
      IF (L .GE. MAXCELL) THEN
C
         L = 1
C
C
C -- Update weather front parameters
C    ===============================
C
C=GWX110
C
         IF (TCMWND) THEN
C
            WFSPD = TAWSPD(3)
            WFTRK = TAWDIR(3) - CPI
            IF (WFTRK .LE. -CPI) THEN
               WFTRK = WFTRK + C2PI
            ELSE IF (WFTRK .GT. CPI) THEN
               WFTRK = WFTRK - C2PI
            ENDIF
C
            IF (WFSPD .GT. 0.001) THEN
C
               ITER = ITERATION * (1/216000.0) * MAXCELL
               TEMP  = WFSPD * ITER
C
               IF (ABS(GWFLAT) .LE. CHPIM) THEN
                  WFASE = TEMP * SIN(WFTRK * DEG2RAD)
     &                    / COS(GWFLAT * DEG2RAD)
               ENDIF
               WFASN = TEMP * COS(WFTRK*DEG2RAD)
C
               GWFLAT = GWFLAT + WFASN
               GWFLON = GWFLON + WFASE
               TAWLAT = GWFLAT
               TAWLON = GWFLON
C
            ENDIF
C
         ELSE
C
            WFSPD = 0.0
            WFTRK = 0.0
            WFASE = 0.0
            WFASN = 0.0
C
         ENDIF
C
C
C -- Update weather cells velocities
C    ===============================
C
C=GWX120
C
         DO I = 1, MAXCELL
            IF (GWCACT(I)) THEN
               WCTRK = GWCTRK(I)
               IF (WCTRK .LE. -CPI) THEN
                  WCTRK = WCTRK + C2PI
               ELSE IF (WCTRK .GT. CPI) THEN
                  WCTRK = WCTRK - C2PI
               ENDIF
               IF (GWCSPD(I) .GT. 0.001) THEN
                  ITER = ITERATION * (1/216000.0) * MAXCELL
                  TEMP = GWCSPD(I) * ITER
                  IF (ABS(GWCLAT(I)) .LE. CHPIM) THEN
                     WCASE(I) = TEMP * SIN(WCTRK*DEG2RAD)
     &                          / COS(GWCLAT(I)*DEG2RAD)
                  ENDIF
                  WCASN(I) = TEMP * COS(WCTRK*DEG2RAD)
               ELSE
                  WCASE(I) = 0.0
                  WCASN(I) = 0.0
               ENDIF
               WCASE(I) = WCASE(I) + WFASE
               WCASN(I) = WCASN(I) + WFASN
            ENDIF
         ENDDO
C
      ELSE
C
         L = L + 1
C
      ENDIF
C
C
C  **********************
C  *                    *
C  *  CLOUD PROCESSING  *
C  *                    *
C  **********************
C
C
C -- Check if cloud inhibited
C    ========================
C
C=GWX200
C
      IF (.NOT. GWCACT(L)) THEN
C
         CELLVIS(L)  = 1.000
         WCROCRNG(L) = 0.0
         WCROCBEA(L) = 0.0
C
      ELSE
C
C
C -- Cloud latitude and longitude
C    ============================
C
C=GWX210
C
         GWCLAT(L) = GWCLAT(L) + WCASN(L)
         GWCLON(L) = GWCLON(L) + WCASE(L)
C
C
C -- Cloud range and bearing
C    =======================
C
C=GWX220
C
         PREVRNG  = GWCRNG(L)
         PREVBEA  = GWCBEA(L)
         THETA1   = (CHPI - GALAT) * DEG2RAD
         THETA2   = (CHPI - GWCLAT(L)) * DEG2RAD
         SINLAT1  = SIN(THETA1)
         COSLAT1  = COS(THETA1)
         SINLAT2  = SIN(THETA2)
         COSLAT2  = COS(THETA2)
         DLON     = GWCLON(L) - GALON
         IF (DLON .GT. CPI) THEN
            DLON = DLON - C2PI
         ELSE IF (DLON .LE. -CPI) THEN
            DLON = DLON + C2PI
         ENDIF
         COSLON = COS(DLON * DEG2RAD)
         COSRNG = COSLAT1 * COSLAT2 + SINLAT1 * SINLAT2 * COSLON
         IF (COSRNG .GE. 1.000) THEN
            GWCRNG(L) = 0.000
            GWCBEA(L) = 0.000
         ELSE IF (COSRNG .LE. -1.0000) THEN
            GWCRNG(L) = CR
            GWCBEA(L) = 0.000
         ELSE
            GWCRNG(L) = ACOS(COSRNG) * RAD2DEG * DEG2NMI
            SINRNG    = SQRT(1.00 - COSRNG**2)
            IF (SINRNG .LE. 1.0E-6) THEN
               GWCBEA(L) = 0.000
            ELSE
               COSBEA = (COSLAT2 * SINLAT1 - SINLAT2 * COSLAT1 * COSLON)
     &                  / SINRNG
               IF (COSBEA .GE. 1.000) THEN
                  GWCBEA(L) = 0.000
               ELSE IF (COSBEA .LE. -1.0000) THEN
                  GWCBEA(L) = CPI
               ELSE
                  GWCBEA(L) = ACOS(COSBEA) * SIGN(1.0,DLON) * RAD2DEG
               ENDIF
            ENDIF
         ENDIF
C
C
C -- Cloud range and bearing rates of change
C    =======================================
C
C=GWX230
C
         WCROCRNG(L) = (GWCRNG(L) - PREVRNG) / MAXCELL
         BEAROC1 = GWCBEA(L)
         BEAROC2 = PREVBEA
         IF (ABS(BEAROC1-BEAROC2) .GT. 180.0) THEN
            IF (BEAROC1 .LT. 0.0) THEN
               BEAROC1 = BEAROC1 + 360.0
            ELSEIF (BEAROC2 .LT. 0.0) THEN
               BEAROC2 = BEAROC2 + 360.0
            ENDIF
         ENDIF
         WCROCBEA(L) = (BEAROC1 - BEAROC2) / MAXCELL
C
C
C  *****************************
C  *                           *
C  *  CELL EFFECTS PARAMETER   *
C  *                           *
C  *****************************
C
C
C=GWX300
C
C
         IF (GWCRNG(L) .GT. 0.01) THEN
            CELLSIZE(L) = SQRT(GWCWDT(L)**2 + GWCLNT(L)**2)
     &                    /GWCRNG(L)
         ELSE
            CELLSIZE(L) = 9999
         ENDIF
C
         CELLBEA(L) = GWCBEA(L) - VPSIDG
         IF (CELLBEA(L) .GT. CPI) THEN
            CELLBEA(L) = CELLBEA(L) - C2PI
         ELSE IF (CELLBEA(L) .LE. -CPI) THEN
            CELLBEA(L) = CELLBEA(L) + C2PI
         ENDIF
C
         CELLLVL(L) = (VHS - GWCALT(L) - 0.5 * GWCTHK(L)) * 2.0
     &                * MAXLVL(GWCTYP(L)) / GWCTHK(L)
C
         THETA = (GWCORI(L) + GWCBEA(L) - VPSIDG) * DEG2RAD
         FSIZE = 0.5 / SQRT( (COS(THETA) / GWCLNT(L))**2
     &                     + (SIN(THETA) / GWCWDT(L))**2 )
C
         R1VIS(L)   = GWCRNG(L) - 2*FSIZE
         R2VIS(L)   = GWCRNG(L) - FSIZE
         R3VIS(L)   = GWCRNG(L) - 0.5*FSIZE
         TEMP = ABS(CELLLVL(L)/MAXLVL(GWCTYP(L)))
C
         IF (ABS(CELLLVL(L)) .GT. MAXLVL(GWCTYP(L))) THEN
            CELLVIS(L) = 1.000
         ELSEIF (R2VIS(L) .LE. 1.0E-4) THEN
            CELLVIS(L) = TEMP
         ELSEIF (R1VIS(L) .LE. 0.00) THEN
            CELLVIS(L) = R2VIS(L)/FSIZE + TEMP
         ELSE
            CELLVIS(L) = 1.000
         ENDIF
         IF (CELLVIS(L) .GT. 1.000) CELLVIS(L) = 1.000
C
         RAINSND(L) = 0
         TEMP = CELLLVL(L)/MAXLVL(GWCTYP(L)) - 1
         IF (TEMP .LT. -1.0) TEMP = -1
         IF (TEMP .LE. 0.0) THEN
            IF (R3VIS(L) .LE. 0.0) THEN
               RAINSND(L) = - TEMP
            ELSE IF (R2VIS(L) .LT. 0.0) THEN
               RAINSND(L) = TEMP * R2VIS(L)/ (R3VIS(L)-R2VIS(L))
            ENDIF
         ENDIF
         RAINSND(L) = RAINLVL(GWCTYP(L)) * RAINSND(L)
C
      ENDIF
C
C
C  ****************************
C  *                          *
C  *  OUTPUT SPECIAL EFFECTS  *
C  *                          *
C  ****************************
C
C
C -- Visibility Effects
C    ==================
C
C=GWX400
C
      MINVIS = 1.0
      MAXRAIN = 0.0
      DO I = 1, MAXCELL
         IF (GWCACT(I)) THEN
            IF (CELLVIS(I) .LT. MINVIS) MINVIS = CELLVIS(I)
            IF (RAINSND(I) .GT. MAXRAIN) MAXRAIN = RAINSND(I)
         ENDIF
      ENDDO
      DELVIS = MINVIS - GOVISIB
      IF (ABS(DELVIS) .GT. CROCVIS) DELVIS = SIGN(CROCVIS,DELVIS)
      IF (VISIB+DELVIS .GT. CMAXVIS) THEN
         VISIB = CMAXVIS
      ELSE IF (VISIB+DELVIS .LT. 0.00) THEN
         VISIB = 0.00
      ELSE
         VISIB = VISIB + DELVIS
      ENDIF
C
C
C -- Turbulence effects
C    ==================
C
C=GWX410
C
      IF (GYFBINA(1) .GE. 128) THEN
         HEAVY = 1
      ELSE
         HEAVY = 0
      ENDIF
      TBLVL = GYFBINA(1)
      TBLVL = IAND(TBLVL,63)
      IF (MAXRAIN .GT. 0.0) THEN
         TURB = HEAVY*60.0 + TBLVL + MAXRAIN*20
         IF (TURB .GT. 100) TURB = 100
      ELSE
         TURB = 0
      ENDIF
      TURB = TURB*TAWTRB/100.0
C
      IF (TAWTRB .GT. 0) THEN
         IF (TURB .GT. GOTURB) THEN
            GOTURB = GOTURB + 1
         ELSE IF (TURB .LT. GOTURB) THEN
            GOTURB = GOTURB - 1
         ENDIF
      ELSE
         GOTURB = 0
      ENDIF
C
C
C -- Audio effects
C    =============
C
C=GWX420
C
      IF (TCMEFX) THEN
C
         GOVISIB = VISIB
         GORAIN  = MAXRAIN
         IF (MAXRAIN .GE. 0.1) THEN
            GOLIGHT = .TRUE.
            GOSOUND = .TRUE.
         ELSE
            GOLIGHT = .FALSE.
            GOSOUND = .FALSE.
         ENDIF
C
      ELSE
C
         GOVISIB = CMAXVIS          ! set visibility to maximum
         GORAIN  = 0.0              ! set rain sound to minimum
         GOLIGHT = .FALSE.          ! set lightning to false
         GOSOUND = .FALSE.          ! set thunder sound to false
C
      ENDIF
C
C
C -- Visual model parameters
C    =======================
C
C=GWX430
C
C
      IF (TCMEFX) THEN
C
         IF (MILES .AND. (GWFNBR .LE. 8)) THEN
C
C PLEM Adapting the Miles code to work with CAE Visual
C PLEM            IF (GWFNBR .EQ. 1) THEN
C PLEM               GOCTTYP = 3
C PLEM            ELSE IF (GWFNBR .EQ. 2) THEN
C PLEM               GOCTTYP = 5
C PLEM            ELSE IF (GWFNBR .EQ. 3) THEN
C PLEM               GOCTTYP = 7
C PLEM            ELSE IF (GOCTTYP .EQ. 4) THEN
C PLEM               GOCTTYP = 9
C PLEM            ELSE IF (GOCTTYP .EQ. 5) THEN
C PLEM               GOCTTYP = 13
C PLEM            ELSE
C PLEM               GOCTTYP = 0
C PLEM               GOCTDLA = 0
C PLEM               GOCTDLO = 0
C PLEM            ENDIF
C PLEM            GOCTLAT = GWFLAT
C PLEM            GOCTLON = GWFLON
C PLEM            GOCTORI = GWFORI
C PLEM            IF (OLDNBR .NE. GWFNBR) THEN
C PLEM               OLDNBR = GWFNBR
C PLEM               LOCALT = 0
C PLEM               ACTCNT = 0
C PLEM               DO I=1,MAXCELL
C PLEM                  IF (GWCACT(I)) THEN
C PLEM                     LOCALT = (GWCALT(I) + 0.5*GWCTHK(I)) + LOCALT
C PLEM                     ACTCNT = ACTCNT + 1
C PLEM                  ENDIF
C PLEM               ENDDO
C PLEM               IF (LOCALT .GT. 0 ) THEN
C PLEM                  GOCTALT = LOCALT / ACTCNT
C PLEM                  IF (GOCTALT .LT. 1500) GOCTALT = 1500
C PLEM               ENDIF
C PLEM            ENDIF
C PLEM            GOCTTOP = GOCTALT
C PLEM            GOCTBOT = 1500
C PLEM            GOCTDLA = WFASN/160.0
C PLEM            GOCTDLO = WFASE/160.0
C PLEM	
C PLEM-2ULD Added Code for CAE Visual
 
            GOCTTYP = GWFNBR
C
            GOCTLAT = GWFLAT
            IF (GOCTLAT .LE. -CPI) THEN
              GOCTLAT = GOCTLAT + C2PI
            ELSE IF (GOCTLAT .GT. CPI) THEN
              GOCTLAT = GOCTLAT - C2PI
            ENDIF
C
            GOCTLON = GWFLON
            IF (GOCTLON .LE. -CPI) THEN
               GOCTLON = GOCTLON + C2PI
            ELSE IF (GOCTLON .GT. CPI) THEN
                GOCTLON = GOCTLON - C2PI
            ENDIF
C
            IF (MILES) THEN
              GOCTORI = GWCORI(1) + 90
              IF (GOCTORI .GT. CPI) THEN
                GOCTORI = GOCTORI - C2PI
              ELSEIF (GOCTORI .LE. -CPI) THEN
                GOCTORI = GOCTORI + C2PI
              ENDIF
            ELSE
              GOCTORI = GWCORI(1)
            ENDIF
C
            GOCTALT = 5000
C PLEM-2ULD End			
         ENDIF
C
      ELSE
         GOCTTYP = 0
         GOCTDLA = 0
         GOCTDLO = 0
      ENDIF
C
C  ****************************
C  *                          *
C  *  CELL PARAMETERS UPDATE  *
C  *                          *
C  ****************************
C
C
C=GWX500
C
      DO I = 1, MAXCELL
C
         IF (.NOT. GWCACT(I)) THEN
            MICROA(5,I) = DISABLE
         ELSE
C
            GWCORI(I) = GWCORI(I) + GWCRTS(I) * ITERATION
            IF (I .EQ. L) THEN
               NEWRNG = (PREVRNG + WCROCRNG(I))*SDIST*2.0000
               NEWBEA = (PREVBEA + WCROCBEA(I))*SANGLE
            ELSE
               IF (NEWWF) THEN
                  WCROCRNG(I) = 0.0
                  WCROCBEA(I) = 0.0
                  NEWRNG = GWCRNG(I)*SDIST*2.0000
                  NEWBEA = GWCBEA(I)*SANGLE
               ELSE
                  NEWRNG = MICROA(5,I) + WCROCRNG(I)*SDIST*2.0000
                  NEWBEA = MICROA(6,I) + WCROCBEA(I)*SANGLE
               ENDIF
            ENDIF
            IF (NEWRNG .LT. 0.0) THEN
               NEWRNG = 0.0
            ELSE IF ((GWCRNG(I)*SDIST*2.00) .GT. CMAXMIC) THEN
               NEWRNG = DISABLE
            ENDIF
            MICROA(5,I) = NEWRNG
            MICROA(6,I) = MIN(NEWBEA,CMAXMIC)
C
C
C -- Antenna boresight intercept
C    ===========================
C
C=GWX510
C
            TEMP  = 2.0*MAXLVL(GWCTYP(I))*NEWRNG*CNMIFT/GWCTHK(I)
            IF (GMPWR) THEN
               MICROA(7,I) = CELLLVL(I)
               MICROA(8,I) = TEMP * 2
            ELSE
               LEVEL = ABS(TEMP*TANTLTL + CELLLVL(I)) + GWFBLD
               IF (LEVEL .GE. 63) THEN
                  MICROA(7,I) = 63
               ELSE
                  MICROA(7,I) = LEVEL
               ENDIF
               LEVEL = ABS(TEMP*TANTLTR + CELLLVL(I)) + GWFBLD
               IF (LEVEL .GE. 63) THEN
                  MICROA(8,I) = 63
               ELSE
                  MICROA(8,I) = LEVEL
               ENDIF
            ENDIF
C
         ENDIF
C
      ENDDO
      NEWWF = .FALSE.
C
C
C -- Update micro download buffer
C    ============================
C
C=GWX600
C
      DO I = 1, MAXCELL
         IF (GWCACT(I)) THEN
            MICROA(1,I) = GWCTYP(I)
            MICROA(2,I) = GWCLNT(I) * SDIST
            MICROA(3,I) = GWCWDT(I) * SDIST * 2.000
            MICROA(4,I) = MIN(GWCORI(I) * SANGLE, CMAXMIC)
         ENDIF
         IF (BOARD2) THEN
            MICROB(1,I) = MICROA(1,I)
            MICROB(2,I) = MICROA(2,I)
            MICROB(3,I) = MICROA(3,I)
            MICROB(4,I) = MICROA(4,I)
            MICROB(5,I) = MICROA(5,I)
            MICROB(6,I) = MICROA(6,I)
            MICROB(7,I) = MICROA(7,I)
            MICROB(8,I) = MICROA(8,I)
         ENDIF
      ENDDO
C
C
      RETURN
      END
