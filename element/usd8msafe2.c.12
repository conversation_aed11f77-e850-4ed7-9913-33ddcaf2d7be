/*****************************************************************************

  'Title                SAFETY 2 PROGRAM
  'Module_ID            MSAFETY2.C
  'Entry_point          msafety2()
  'Documentation
  'Customer             QANTAS
  'Application          Detection of non-critical motion safeties
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       ? msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history
18 JUN 92 AK   Increased m_adiolim from 5 to 10
17 JUN 92 Norm Used "not responding" and "not installed" failure messages
               to differentiate between these two diff failures
15 jun 92 AK   Added new adio freeze code 
12 may 92 Norm ADDED WASHOUT ON FRICTION
06 apr 92 Norm  Corrected bug in ADIO freeze message logging.
28 mar 92 Mct   Added m_fricilim declaration to permit compilation
27 feb 92 Norm  Moved checksum detection MOTION.C. Action still taken here.
16 feb 92 norm  checksum was using movee[5] iso movee[4]
14 FEB 92 NORM  Logic communication failure selects level FREEZE, then STANDBY
07 feb 92 NORM	Split msafe in two.
		Enabled adio frozen failure.
05 feb 92 norm 	Checksum: granular buffet amplitudes are multiplied by 100.

   NORM B 30 oct Rewrite including all safeties, using macro format.
                 Split into safety1 and safety2
  'References
*/

#include "mot_define.h"
#include "mot_ext.h"
#include <math.h>
#include <adio.h>

/*
C
C      LOCAL VARIABLES
C
*/
static float 	/*
		* 	ADIO frozen
		*/
		m_pxa1inp=0.,		/* previous J1XAINP */
		m_pxa2inp=0.,		/* previous J2XAINP */
		m_pxa3inp=0.,		/* previous J3XAINP */
		m_pxa4inp=0.,		/* previous J4XAINP */
		m_pxa5inp=0.,		/* previous J5XAINP */
		m_pxa6inp=0.,		/* previous J6XAINP */
		m_pfa1inp=0.,		/* previous J1FAINP */
		m_pfa3inp=0.,		/* previous J1FAINP */
		m_pfa5inp=0.,		/* previous J1FAINP */
		m_piac1=0.,		/* previous J1IAC */
		m_piac2=0.,		/* previous J2IAC */
		m_piac3=0.,		/* previous J3IAC */
		m_chklim =0.1;           /* checksum error limit */
static int    	/*
		* 	ADIO frozen failure
		*/
		m_paip0,		/* previous ADIO_AIP5[0] */
		m_paip1,		/* previous ADIO_AIP5[1] */
		m_paip2,		/* previous ADIO_AIP5[2] */
		m_adio1frz,		/* adio frozen timer [iter] */
		m_adio2frz,		/* adio frozen timer [iter] */
		m_adio3frz,		/* adio frozen timer [iter] */
		m_adio4frz,		/* adio frozen timer [iter] */
		m_adio5frz,		/* adio frozen timer [iter] */
                m_adiolim = 10;		/* adio frozen time limit [iter] */
float  m_friclim = 10., /* ic threshold to disable fric. comp. */
       FRICO[6]={0.,0.,0.,0.,0.,0.},  /* RAW FRICTION */
       FRICP[6]={0.,0.,0.,0.,0.,0.},  /* PREVIOUS RAW FRICTION */
       FRICWASH=0.95;                  /* FRICTION WASHOUT CONSTANT */
static float 	sp0,sp1,sp2,sp3,sp4,sp5,
		m_logtim = 0.;		/* logic communication fail timer */
 
static int    	m2_first=1,
		m_reset = 0,
		m_nooptim = 0,		/* option not sent timer */
		/*
		* 	Failure disable timers
		*/
	        m_resetwait,	    /* timer */
		m_resetim= 5,       /* delay before call macro, after reset*/

		m_enveltim = 0,
		m_frictim = 0,
		m_j1biastim = 0,
		m_j2biastim = 0,
		m_j3biastim = 0,
		m_j4biastim = 0,
		m_j5biastim = 0,
		m_j6biastim = 0,
		m_pmnon = 0,
		mcsaf2 = 0,
		m_ptoggle = 0,	/* previous value of logic toggle */
		m_forcount[6]= {0,0,0,0,0,0},	/* force out of range counter */
                m_frctime[6]={0,0,0,0,0,0}, /* friction timer */
		m_uptim = 0.0,	/* motion up time out timer */
		m_dwtim = 0.0; /* motion down time out timer */

void msafety2()
{


mcsaf2++;

/*
*	-------------------------------------------------
**	first pass init
*	-------------------------------------------------
*/
if(m2_first)
{
	/*
	*	FRICTION FORCES LAGs [sec]
	*/
	FACLAG = 2.0;
	HYDFORLAG = 2.0;

	m2_first = FALSE;
	return;
}

/*
*  	----------------------------------------
* 	GLOBAL RESET ENABLE
* 	-----------------------------------------
*	for ABORT and STANDBY levels,
* 	wait untill AT REST and DEPRESURIZED  before resetting
*/

if(  (MFAILEVEL<=STANDBY) && /*!L2M_MNATREST&&*/ MOT_PRES  )
{
 	MRSTENABLE = FALSE;
}
else
{
	MRSTENABLE = TRUE;
}

/* ----------------------------------------
CD MS000 GLOBAL RESET
C ----------------------------------------*/

if (MFRESET)
{
	if(MRSTENABLE)
	{
		MSMAXRST = TRUE;
		MSFAILWRST = TRUE;
	}
	MFRESET = FALSE;
}

/* ----------------------------------------
CD MS000 RESET ALL SIX JACKS MAXIMUM VALUES
C ----------------------------------------*/

if (MSMAXRST)
{
	JL2MTIMMAX = 0.;
	J1MSMAXRESET = TRUE;
	J2MSMAXRESET = TRUE;
	J3MSMAXRESET = TRUE;
	J4MSMAXRESET = TRUE;
	J5MSMAXRESET = TRUE;
	J6MSMAXRESET = TRUE;
        MSMAXRST = FALSE;
}

/* --------------------------------
CD MS000 RESET ALL SIX JACKS SAFETY
C --------------------------------*/

if (MSFAILWRST)
{
	MFAILEVEL = NOFAIL;			/* NO motion failure */
	J1MSFWRESET = TRUE;
	J2MSFWRESET = TRUE;
	J3MSFWRESET = TRUE;
	J4MSFWRESET = TRUE;
	J5MSFWRESET = TRUE;
	J6MSFWRESET = TRUE;
        MF_OVERRUN = FALSE;
	MOVERRUN = FALSE;
	MF_INVSW = FALSE;
        MF_HCHKSUM = FALSE;
	MOT_FREEZEREQ = FALSE;
        MF_L2MCOMM = FALSE;
	MF_ADIO1 = FALSE;
	MF_ADIO2 = FALSE;
	MF_ADIO3 = FALSE;
	MF_ADIO4 = FALSE;
	MF_ADIO5 = FALSE;
	MF_FADIO1 = FALSE;
	MF_FADIO2 = FALSE;
	MF_FADIO3 = FALSE;
	MF_FADIO4 = FALSE;
	MF_FADIO5 = FALSE;
	/*
	* 	reset timer to delay call to macro and allow safety1 to
	*	run. ( detection of BU not connected, etc
	*/

	m_resetwait = m_resetim;

        MSFAILWRST = FALSE;
}

/*
*	----------------------------------
**	Disable flags
*	For 15 min only
*	----------------------------------
*
* 	ENVELOPPE FLAG
*	note: more position and velocity error are allowed when ENVEL set
*/
if (  ( ENVEL ) && (m_enveltim<27000) )
{
	m_enveltim++;
	J1TRAVLDIS = TRUE;
	J2TRAVLDIS = TRUE;
	J3TRAVLDIS = TRUE;
	J4TRAVLDIS = TRUE;
	J5TRAVLDIS = TRUE;
	J6TRAVLDIS = TRUE;
        FRICTION = TRUE;
}
else if ( m_enveltim != 0 )
{
	ENVEL = FALSE;
	J1TRAVLDIS = FALSE;
	J2TRAVLDIS = FALSE;
	J3TRAVLDIS = FALSE;
	J4TRAVLDIS = FALSE;
	J5TRAVLDIS = FALSE;
	J6TRAVLDIS = FALSE;
        FRICTION = FALSE;
	m_enveltim = 0;
}

if (  ( J1BIAS ) && (m_j1biastim<27000) )
{
	m_j1biastim++;
	J1TRAVLDIS = TRUE;
	J1POSERDIS = TRUE;
	J1VELERDIS = TRUE;
	J1CURERDIS = TRUE;
	J2BIAS = FALSE;
	J3BIAS = FALSE;
	J4BIAS = FALSE;
	J5BIAS = FALSE;
	J6BIAS = FALSE;
        FRICTION = TRUE;   /* NO FRICTION FAILURE */
}
else if ( m_j1biastim != 0)
{
	J1BIAS = FALSE;
	J1TRAVLDIS = FALSE;
	J1POSERDIS = FALSE;
	J1VELERDIS = FALSE;
	J1CURERDIS = FALSE;
        FRICTION = FALSE;
	m_j1biastim = 0;
}
if (  ( J2BIAS ) && (m_j2biastim<27000) )
{
	m_j2biastim++;
	J2TRAVLDIS = TRUE;
	J2POSERDIS = TRUE;
	J2VELERDIS = TRUE;
	J2CURERDIS = TRUE;
	J3BIAS = FALSE;
	J4BIAS = FALSE;
	J5BIAS = FALSE;
	J6BIAS = FALSE;
        FRICTION = TRUE;
}
else if ( m_j2biastim != 0)
{
	J2BIAS = FALSE;
	J2TRAVLDIS = FALSE;
	J2POSERDIS = FALSE;
	J2VELERDIS = FALSE;
	J2CURERDIS = FALSE;
        FRICTION = FALSE;
	m_j2biastim = 0;
}
if (  ( J3BIAS ) && (m_j3biastim<27000) )
{
	m_j3biastim++;
	J3TRAVLDIS = TRUE;
	J3POSERDIS = TRUE;
	J3VELERDIS = TRUE;
	J3CURERDIS = TRUE;
	J4BIAS = FALSE;
	J5BIAS = FALSE;
	J6BIAS = FALSE;
        FRICTION = TRUE;
}
else if ( m_j3biastim != 0)
{
	J3BIAS = FALSE;
	J3TRAVLDIS = FALSE;
	J3POSERDIS = FALSE;
	J3VELERDIS = FALSE;
	J3CURERDIS = FALSE;
        FRICTION = FALSE;
	m_j3biastim = 0;
}
if (  ( J4BIAS ) && (m_j4biastim<27000) )
{
	m_j4biastim++;
	J4TRAVLDIS = TRUE;
	J4POSERDIS = TRUE;
	J4VELERDIS = TRUE;
	J4CURERDIS = TRUE;
	J5BIAS = FALSE;
	J6BIAS = FALSE;
        FRICTION = TRUE;
}
else if ( m_j4biastim != 0)
{
	J4BIAS = FALSE;
	J4TRAVLDIS = FALSE;
	J4POSERDIS = FALSE;
	J4VELERDIS = FALSE;
	J4CURERDIS = FALSE;
        FRICTION = FALSE;
	m_j4biastim = 0;
}
if (  ( J5BIAS ) && (m_j5biastim<27000) )
{
	m_j5biastim++;
	J5TRAVLDIS = TRUE;
	J5POSERDIS = TRUE;
	J5VELERDIS = TRUE;
	J5CURERDIS = TRUE;
	J6BIAS = FALSE;
        FRICTION = TRUE;
}
else if ( m_j5biastim != 0)
{
	J5BIAS = FALSE;
	J5TRAVLDIS = FALSE;
	J5POSERDIS = FALSE;
	J5VELERDIS = FALSE;
	J5CURERDIS = FALSE;
        FRICTION = FALSE;
	m_j5biastim = 0;
}
if (  ( J6BIAS ) && (m_j6biastim<27000) )
{
	m_j6biastim++;
	J6TRAVLDIS = TRUE;
	J6POSERDIS = TRUE;
	J6VELERDIS = TRUE;
	J6CURERDIS = TRUE;
        FRICTION = TRUE;
}
else if ( m_j6biastim != 0)
{
	J6BIAS = FALSE;
	J6TRAVLDIS = FALSE;
	J6POSERDIS = FALSE;
	J6VELERDIS = FALSE;
	J6CURERDIS = FALSE;
        FRICTION = FALSE;
	m_j6biastim = 0;
}



/*
*	-----------------------------------------------------------------
**	Host commands checksum failure action. NOTE: detected in motion.c
*	-----------------------------------------------------------------
*/
 
if( MF_HCHKSUM )
{
	MFAILEVEL = min(MFAILEVEL,OFFREQ);
}


/*
*	----------------------------------
**	LOGIC to MOTION communication fail
*	----------------------------------
*/

if ( LOGIC_REQUEST.toggle == m_ptoggle )
{
	m_logtim += YITIM;
}
else
{
	m_logtim = 0.;
}

m_ptoggle = LOGIC_REQUEST.toggle;

/*
* 	Note : Logic will reset failure if communication resumes
*	Otherwise, freeze motion. 5 sec later, set standby level.
*/
if ( m_logtim > JL2MCOMTIM )
{
 	MF_L2MCOMM = TRUE;
        MFAILEVEL = min (MFAILEVEL ,FREEZE );
}
if ( m_logtim > 5. )
{
        MFAILEVEL = min (MFAILEVEL ,STANDBY);
}

JL2MTIMMAX = max ( JL2MTIMMAX, m_logtim );

/*
*	----------------------------------
**	LOGIC fails to send motion options
*	----------------------------------
*	motion can not be ready until options are sent
*	if not sent on time, abort.
*/

if ( !MOPTION )
{
	MFAILEVEL = min(MFAILEVEL,OFFREQ);
	m_nooptim++;
}
else if ( ( MFAILEVEL == OFFREQ )&& (m_nooptim!=0 ) )
{
 	MFAILEVEL = NOFAIL;
	m_nooptim = 0;
}

if ( m_nooptim > 300 )
{
	MF_NOOPT = TRUE;
	MFAILEVEL = min(MFAILEVEL,ABORT);
}

/*
*	------------------
**	motion UP time out
*	------------------
*/


/******************************************
*sp0 = 500 * JMRMPTIMOUT;
*
*if ( ( L2M_MNONREQ) && (!M2L_MNON) )
*{
*	m_uptim++;
*
*	m_dwtim = 0.0;
*
*	if ( m_uptim >=  sp0 )
*	{
*		m_uptim = sp0;
*		MFAILEVEL = min(MFAILEVEL,OFFREQ);
*		JMUPTIMOUT = TRUE;
*	}
*
*}
*
*****************************************/

/*
*	--------------------
**	motion DOWN time out
*	--------------------
*/

/*++++++++++++++++++++++++++++++++++++++++++++++++++++=====
*else if ( (!L2M_MNONREQ) && (!MOTATREST) )
*++++++++++++++++++++++++++++++++++++===*/

/******************************************
*
*
*else if ( (!L2M_MNONREQ) && (J1XAC > -0.98 ) )
*{
*	m_dwtim++;
*
*	m_uptim = 0.0;
*
*	if ( m_dwtim >=  sp0 )
*	{
*		m_dwtim = sp0;
*		MFAILEVEL = min(MFAILEVEL,STANDBY);
*		JMDWTIMOUT = TRUE;
*	}
*
*}
*else
*{
*	m_uptim = 0.0;
*	m_dwtim = 0.0;
*}
******************************************/
/*
*
*	-------------------
**	MOTION ADIO FROZEN 
*	-------------------
*       Detected only if card is installed
*/

if ( (J1XAINP==m_pxa1inp)&&(J2XAINP==m_pxa2inp)&&(J1FAINP==m_pfa1inp) )
{
	m_adio1frz=m_adio1frz+1;
}
else
{
	m_adio1frz = 0;
}
if ( ( m_adio1frz > m_adiolim)&&( !MF_ADIO1 ) )
{
	/*
	*	If adio is frozen, call adio_check once to reset it
	*	Just resetting the cabinet should be enough to clear fault.
	*/
	if ( !MF_FADIO1 )IO_FSTATUS1 = adio_check(ADIO_SLOT1);

 	MF_FADIO1 = TRUE;
	MFAILEVEL = min (MFAILEVEL , ABORT );
}

if ( (J3XAINP==m_pxa3inp)&&(J4XAINP==m_pxa4inp)&&(J3FAINP==m_pfa3inp) )
{
	m_adio2frz=m_adio2frz+1;
}
else
{
	m_adio2frz = 0;
}
if ( ( m_adio2frz > m_adiolim)&&( !MF_ADIO2 ) )
{
	/*
	*	If adio is frozen, call adio_check once to reset it
	*	Just resetting the cabinet should be enough to clear fault.
	*/
	if ( !MF_FADIO2 )IO_FSTATUS2 = adio_check(ADIO_SLOT2);

 	MF_FADIO2 = TRUE;
	MFAILEVEL = min (MFAILEVEL , ABORT );
}



if ( (J5XAINP==m_pxa5inp)&&(J6XAINP==m_pxa6inp)&&(J5FAINP==m_pfa5inp) )
{
	m_adio3frz=m_adio3frz+1;
}
else
{
	m_adio3frz = 0;
}
if ( ( m_adio3frz > m_adiolim)&&( !MF_ADIO3 ) )
{
	/*
	*	If adio is frozen, call adio_check once to reset it
	*	Just resetting the cabinet should be enough to clear fault.
	*/
	if ( !MF_FADIO3 )IO_FSTATUS3 = adio_check(ADIO_SLOT3);

 	MF_FADIO3 = TRUE;
	MFAILEVEL = min (MFAILEVEL , ABORT );
}

if ( (J1IAC==m_piac1)&&(J2IAC==m_piac2)&&(J3IAC==m_piac3) )
{
	m_adio4frz=m_adio4frz+1;
}
else
{
	m_adio4frz = 0;
}
if ( ( m_adio4frz > m_adiolim)&&( !MF_ADIO4 ) )
{
	/*
	*	If adio is frozen, call adio_check once to reset it
	*	Just resetting the cabinet should be enough to clear fault.
	*/
	if ( !MF_FADIO4 )IO_FSTATUS4 = adio_check(ADIO_SLOT4);

	MF_FADIO4 = TRUE;
        MFAILEVEL = min (MFAILEVEL , WARNING );
}



if ( (ADIO_AIP5[0]==m_paip0)&&(ADIO_AIP5[1]==m_paip1)
		&&(ADIO_AIP5[2]==m_paip2) )
{
	m_adio5frz=m_adio5frz+1;
}
else
{
	m_adio5frz = 0;
}
if ( ( m_adio5frz > m_adiolim)&&( !MF_ADIO5 ) )
{
	/*
	*	If adio is frozen, call adio_check once to reset it
	*	Just resetting the cabinet should be enough to clear fault.
	*/
	if ( !MF_FADIO5 )IO_FSTATUS5 = adio_check(ADIO_SLOT5);

	MF_FADIO5 = TRUE;
	MFAILEVEL = min (MFAILEVEL , WARNING );
}

m_pxa1inp = J1XAINP;
m_pxa2inp = J2XAINP;
m_pxa3inp = J3XAINP;
m_pxa4inp = J4XAINP;
m_pxa5inp = J5XAINP;
m_pxa6inp = J6XAINP;
m_pfa1inp = J1FAINP;
m_pfa3inp = J3FAINP;
m_pfa5inp = J5FAINP;
m_piac1   = J1IAC;
m_piac2   = J2IAC;
m_piac3   = J3IAC;
m_paip0   = ADIO_AIP5[0];
m_paip1   = ADIO_AIP5[1];
m_paip2   = ADIO_AIP5[2];
 
/*
*	-----------------------------------------------------------------
**	MOTION ADIO not responding NOTE: same code repeated in msafety1.c
*	-----------------------------------------------------------------
*/

if ( IO_STATUS1 != 1 )
{
 	MF_ADIO1 = TRUE;
        MFAILEVEL = min (MFAILEVEL , ABORT );
}
if ( IO_STATUS2 != 1 )
{
 	MF_ADIO2 = TRUE;
        MFAILEVEL = min (MFAILEVEL , ABORT );
}
if ( IO_STATUS3 != 1 )
{
 	MF_ADIO3 = TRUE;
        MFAILEVEL = min (MFAILEVEL , ABORT );
}
if ( IO_STATUS4 != 1 )
{
 	MF_ADIO4 = TRUE;
        MFAILEVEL = min (MFAILEVEL , WARNING );
}
if ( IO_STATUS5 != 1 )
{
 	MF_ADIO5 = TRUE;
        MFAILEVEL = min (MFAILEVEL , WARNING );
}
/*
*  	Decrement wait timer. Call MACRO after waiting
*/

m_resetwait = max (m_resetwait-1,0);

/*
*	-------------------------------
CD	Call msafety2 macro for jack #1
*	-------------------------------
*/
if ( !MF_ADIO1 )
{

#undef CHAN
#define CHAN JACK1

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe2.mac"
}
/*
*	-------------------------------
CD	Call msafety2 macro for jack #2
*	-------------------------------
*/
if ( !MF_ADIO1 )
{

#undef CHAN
#define CHAN JACK2

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe2.mac"
}
/*
*	-------------------------------
CD	Call msafety2 macro for jack #3
*	-------------------------------
*/

if ( !MF_ADIO2 )
{

#undef CHAN
#define CHAN JACK3

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe2.mac"
}
/*
*	-------------------------------
CD	Call msafety2 macro for jack #4
*	-------------------------------
*/

if ( !MF_ADIO2 )
{

#undef CHAN
#define CHAN JACK4

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe2.mac"
}
/*
*	-------------------------------
CD	Call msafety2 macro for jack #5
*	-------------------------------
*/

if ( !MF_ADIO3 )
{

#undef CHAN
#define CHAN JACK5

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe2.mac"
}
/*
*	-------------------------------
CD	Call msafety2 macro for jack #6
*	-------------------------------
*/

if ( !MF_ADIO3 )
{

#undef CHAN
#define CHAN JACK6

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe2.mac"
}



/*
*	------------------------------
**	update previous motion on flag
*       ------------------------------
*/

m_pmnon = M2L_MNON;

/*
C END OF FILE MSAFETY2.C
*/

}    			/* end of file */


