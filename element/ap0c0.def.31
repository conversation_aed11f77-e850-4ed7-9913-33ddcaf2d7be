;	+===============================================+
;	+	Dispatcher Table AP0C0.DEF		+
;	+	Simulator: US-AIR Dash-8                +
;	+===============================================+
;
;         This module contains the entry addresses and
;         scheduling parameters for the programs in the
;         ASYNChronous bands.
; 
;         WARNING : All module entry names must in lower case.
;
;****************************************************
;*                                                  *
;*  Users must not modify any sections without      *
;*  consulting department 73			    *
;*                                                  *
;****************************************************
;
; Band structure:
;
;                               1_1
;           2_1                 2_2                  2_3
;      3_1       3_2        3_3      3_4       3_5         3_6 
;  4_1   4_2   4_3  4_4   4_5 4_6  4_7 4_8   4_9 4_10   4_11 4_12    
;
crit_table:
end_crittable:
;
sync_table:
          leg  1_1 2_1 3_1 4_1 
          leg  1_1 2_2 3_3 4_5 
          leg  1_1 2_3 3_5 4_9 
          leg  1_1 2_1 3_2 4_3 
          leg  1_1 2_2 3_4 4_7 
          leg  1_1 2_3 3_6 4_11 
          leg  1_1 2_1 3_1 4_2
          leg  1_1 2_2 3_3 4_6 
          leg  1_1 2_3 3_5 4_10 
          leg  1_1 2_1 3_2 4_4 
          leg  1_1 2_2 3_4 4_8 
          leg  1_1 2_3 3_6 4_12 
end_synctable:
;
;*****************************************
;       User update section              *
;*****************************************
;
; BAND 1_1 50 ms
;
band1_1:
     aprog call_local_copy.u    ; OSU : special routine for OPEN/READ buffer
     aprog host_ovp.u           ; OVP
     aprog xvoice.f             ; IF : Digital voice instr cntl
     aprog x9play.f             ; IF : D.V. msg play
     aprog x9da.u               ; IF : Digital Voice disk access
     aprog refrwy.u             ; Radio Aids : Reference runway 
     aprog visalign.f           ; VIS: Visual Alignment
     aprog tpxi.u               ; IF : EL panel input
     aprog tpxo.u               ; IF : EL panel outpout
     aprog xsca.u               ; IF : Preselect (Criteria Analysis)
;
; BAND 2_1 150 ms
;
band2_1:
      aprog rxrzr.u              ; Radio Aids : TMA pages
      aprog rxaccs.u             ; Radio Aids : Data file scan 
      aprog rgalmana.f           ; 2U0C GPS almanac retrieve module
;
; BAND 2_2 150 ms
;
band2_2:
     aprog xsrt.u               ; IF : Preselect 
     aprog rxrzx.u              ; Radio Aids : Data file 
;
; BAND 2_3 150 ms
;
band2_3:
     aprog rxopen.u             ; Radio Aids : Data file open 
     aprog rxscan.u             ; Radio Aids : Data file scan 
;
; BAND 3_1 300 ms
;
band3_1:
     aprog tcnia.u              ; IF : Control Not In Agrement
     aprog xrgrdtrf.f           ; IF : Ground Traffic
;
; BAND 3_2 300 ms
;
band3_2:
     aprog vturcof.u            ; Flight:  turbulence coeff calculations
;
; BAND 3_3 300 ms
;
band3_3:
     aprog rvkill.u             ; Radio Aids : Station kill
;
; BAND 3_4 300 ms
;
band3_4:
     aprog tlessex.u            ; IF : Lesson plan
     aprog rvapr.u              ; Radio Aids : Station kill
;
; BAND 3_5 300 ms
;
band3_5:
     aprog isp.u                ; Morning readiness isp module
     aprog wx4.f                ; WXR : asynchronus module
;
; BAND 3_6 300 ms
;
band3_6:
     aprog xmalf.u              ; IF : Malfunction summary
     aprog dn1_error.u          ; 
;
; BAND 4_1 600 ms
;
band4_1:
;
; BAND 4_2 600 ms
;
band4_2:
;
; BAND 4_3 600 ms
;
band4_3:
;
; BAND 4_4 600 ms
;
band4_4:
;
; BAND 4_5 600 ms
;
band4_5:
;
; BAND 4_6 600 ms
;
band4_6:
;
; BAND 4_7 600 ms
;
band4_7:
;
; BAND 4_8 600 ms
;
band4_8:
;
; BAND 4_9 600 ms
;
band4_9:
;
; BAND 4_10 600 ms
;
band4_10:
;
; BAND 4_11 600 ms
;
band4_11:
;
; BAND 4_12 600 ms
;
band4_12:

end:
