#!  /bin/csh -f
#!  $Revision: C30FGEN_BLD - Build a C30 C FGEN file, V1.5 (KU) OCT-92$
#!
#! &
#! @
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
#
source `logicals -t cae_dfc_uproc`/std_log.com
#
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
#
set FSE_LINE="`sed -n 2p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  goto FSE_BUILD
endif
#
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
set tmp_name=`norev $FSE_FILE`
set FSE_NAME=$tmp_name:t
set FSE_NAME=$FSE_NAME:r
set FSE_VERS=.$FSE_FILE:e
set FSE_DATA=$FSE_FILE
#
if ("$FSE_DATA" == "") then
  echo "%FSE-E-NOSRC, No source file in the list."
  goto FSE_BUILD
endif
#
set FSE_LINE="`sed -n 1p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  goto FSE_BUILD
endif
#
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
set tmp_name=`norev $FSE_FILE`
set FSE_NAME=$tmp_name:t
set FSE_NAME=$FSE_NAME:r
set FSE_C=$SIMEX_WORK/$FSE_NAME.c$FSE_VERS
#
c30fgen $FSE_DATA $FSE_C
set stat=$status
if ($stat != 0) then
  if (-e "$FSE_C") rm $FSE_C
  goto FSE_BUILD
endif
if (! -e "$FSE_C") goto FSE_BUILD
#
set FSE_INFO="`fmtime $FSE_C | cut -c1-17`"
if ("$FSE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_C)"
else
  echo "0MRBSC $FSE_C,,,C30FGEN_BLD.COM,,Produced by C30FGEN on $FSE_INFO" >$argv[4]
endif
#
FSE_BUILD:
  exit
