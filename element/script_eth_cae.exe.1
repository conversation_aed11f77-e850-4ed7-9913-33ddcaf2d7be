#! /bin/csh -f
#  $Revision: script_eth_cae  V1.3 29-Mar-92 (MT)$
#
#  This script is used to configure CAE Real Time Ethernet Drivers
#
#  V1.0 Original
#
#  V1.1 25-Nov-1991
#    Corrected spelling - CONFIGURATING - CONFIGURING
#
#  V1.2 04-Feb-1992 
#    Corrected spelling and implement revision for ident 
#
#  V1.3 29-Mar-1992
#    Added redirections in /dev/null of many unused output messages
#
echo "*** REMOVING PREVIOUS INSTALLATION OF IBM ETHERNET DRIVER"
echo " "
set BIN="`/cae/logicals -t CAE_CAELIB_PATH`"
$BIN/conf_eth_cae -u
unlink /dev/cae_ent0
unlink /dev/cae_ent1
unlink /dev/cae_ent2
unlink /dev/cae_ent3
unlink /dev/cae_ent4
unlink /dev/cae_ent5
unlink /dev/cae_ent6
unlink /dev/cae_ent7
echo "*** CONFIGURING IBM ETHERNET DRIVER to get ETHERNET ATTRIBUTES"
echo
cfgmgr >&/dev/null
echo "*** GENERATING ETHERNET ATTRIBUTES IN FILE:"
echo "        '$BIN/ent_database'"
echo
rm $BIN/ent_database >&/dev/null
$BIN/gen_ent_attr
echo "*** INSTALLING CAE ETHERNET DRIVER" 
echo
$BIN/conf_eth_cae 
#
exit
