C
C ---  TPXO.INC  file
C
C ---  Include file used by the Touch Panel output foreground software
C
C
C --------------------------------
C ---   Processing parameter   ---
C --------------------------------
C
      INTEGER*4
     .            NUMPAGPP, NUMDEVPP, NUMDCBPP, LOAD_DEL
C
      PARAMETER (
     .            NUMPAGPP    = 10   !      ! # of page process per pass
     .,           NUMDEVPP    = 1    !      ! # of Devices process pes pass
     .,           NUMDCBPP    = 8    !      ! # of DCBs process pes pass
     .,           LOAD_DEL    = 2    ! 160  ! Delay before initial process
     .          )
C
C -----------------------------
C ---   Display Parameter   ---
C -----------------------------
C
      INTEGER*4
     .            SCR_1_OFF, SCR_2_OFF
     .,           COL_BLOF,  STA_BLOF, HIG_BLOF
     .,           TCH_YP_O,  TCH_XP_O, TCH_YE_O, TCH_XE_O
C
      PARAMETER (
     .            SCR_1_OFF = 0             ! Screen axis 1 offset
     .,           SCR_2_OFF = 0             ! Screen axis 2 offset
     .,           COL_BLOF  = 0             ! Color Block offset
     .,           STA_BLOF  = 5             ! State Block offset
     .,           HIG_BLOF  = 3             ! High Light Block offset
     .,           TCH_YP_O  = -1            ! Touch Real Y Position Offset
     .,           TCH_XP_O  = -3            ! Touch Real X Position Offset
     .,           TCH_YE_O  = -3            ! Touch Real Y End Offset
     .,           TCH_XE_O  = -1            ! Touch Real X End Offset
     .          )
C
C -----------------------------------------------
C ---   Buffer size declaration description   ---
C -----------------------------------------------
C
      INTEGER*4
     .            OSTRINGS
C
      PARAMETER (
     .            OSTRINGS = 30             ! Output String length
     .          )
C
C ----------------------------------------------
C --- First Pass State Sequence declarations ---
C ----------------------------------------------
C
C +----------------+-------------------------------------------------------+
C | Initial Reset  |   *         *    *    *    *    *    *    *           |
C | Total Reset    |   *    *    *    *    *    *    *    *    *           |
C | Devices Reset  |             *    *                   *         *      |
C | File Update    |        *              *    *    *         *           |
C +----------------+-------------------------------------------------------+
C | Labels         |  1000 1100 1200 1300 1400 1500 1600 1700 1800 1900    |
C +----------------+-------------------------------------------------------+
C | States Name        :    :    :    :    :    :    :    :    :    :      |
C |   Variables Init ..:    :    :    :    :    :    :    :    :    :      |
C |   Close Files ..........:    :    :    :    :    :    :    :    :      |
C |   Assign Serial Port ........:    :    :    :    :    :    :    :      |
C |   Init Devices ...................:    :    :    :    :    :    :      |
C |   Open Files ..........................:    :    :    :    :    :      |
C |   Read Files Header ........................:    :    :    :    :      |
C |   Read Files Directory ..........................:    :    :    :      |
C |   Send Initial Display ...............................:    :    :      |
C |   Complete First Pass .....................................:    :      |
C |   Complete Reset ...............................................:      |
C |                                                                        |
C +------------------------------------------------------------------------+
C
C --- Fisrt Pass State Declarations
C
      INTEGER*2   FPST_IVS, FPST_CFL, FPST_ASP, FPST_IDV, FPST_OFL
     .,           FPST_RFH, FPST_RFD, FPST_SID, FPST_CFP, FPST_CDR
C
      PARAMETER (
     .            FPST_IVS   =  1      ! Initial Variable Setting
     .,           FPST_CFL   =  2      ! Close Files
     .,           FPST_ASP   =  3      ! Assign Serial Port
     .,           FPST_IDV   =  4      ! Assign Serial Port
     .,           FPST_OFL   =  5      ! Open Files
     .,           FPST_RFH   =  6      ! Read Files Header
     .,           FPST_RFD   =  7      ! Read Files Directory
     .,           FPST_SID   =  8      ! Send Initial Display
     .,           FPST_CFP   =  9      ! Complete First Pass
     .,           FPST_CDR   = 10      ! Complete First Pass Device reset
     .          )
C
C --- Fisrt Pass Mode Declarations
C
      INTEGER*2   FPMD_IRS,  FPMD_TRS,  FPMD_DRS,  FPMD_FUD
C
      PARAMETER (
     .            FPMD_IRS   = 1         ! Initial Reset
     .,           FPMD_TRS   = 2         ! Total Reset
     .,           FPMD_DRS   = 3         ! Device Reset
     .,           FPMD_FUD   = 4         ! Files Update
     .          )
C
C --- First pass Sequence Declaration
C
      INTEGER*2   FPSEQUEN(9,4)          ! (sequence_step,sequence_mode)
C
      DATA        FPSEQUEN /
C
     .            FPST_IVS, FPST_ASP, FPST_IDV, FPST_OFL, FPST_RFH   ! Initial
     .,           FPST_RFD, FPST_SID, FPST_CFP, 0
C
     .,           FPST_IVS, FPST_CFL, FPST_ASP, FPST_IDV, FPST_OFL   ! Total
     .,           FPST_RFH, FPST_RFD, FPST_SID, FPST_CFP
C
     .,           FPST_ASP, FPST_IDV, FPST_SID, FPST_CDR, 0          ! Device
     .,           0,        0,        0,        0
C
     .,           FPST_CFL, FPST_OFL, FPST_RFH, FPST_RFD, FPST_CFP   ! File
     .,           0,        0,        0,        0    /
C
C ----------------------------------------------------
C --- Page Processing states sequence declarations ---
C ----------------------------------------------------
C
C Page Processing sequence
C           +-------------------------------------------------------------+
C           | Read Page Header                                            |
C           | |    Read Page data                                         |
C           | |    |    Process Page data                                 |
C           | |    |    |    Send Page data                               |
C           | |    |    |    |    Read DCB                                |
C           | |    |    |    |    |    Activate Display List              |
C +------+  | |    |    |    |    |    |    Complete Page processing      |
C | Page |  | |    |    |    |    |    |    |    Complete Cache processing|
C | Type |  | v    v    v    v    v    v    v    v    Iddle state         |
C +------+--+-------------------------------------------------------------+
C | Normal  | *    *    *    *    *         *         *                   |
C | Cache   |                *                   *    *                   |
C | Resident|                          *         *    *                   |
C +---------+-------------------------------------------------------------+
C | Label   | 2100 2200 2300 2400 2500 2600 2700 2800 2990                |
C +---------+-------------------------------------------------------------+
C
C --- Page Processing states declarations
C
      INTEGER*2   PPST_RPH, PPST_RPD, PPST_RCI, PPST_PPD
     .,           PPST_SPD, PPST_RDC, PPST_CPG, PPST_CPP
     .,           PPST_CCP, PPST_CHP, PPST_IDL
C
      PARAMETER (
     .            PPST_RPH   = 1         ! Read Page Header
     .,           PPST_RPD   = 2         ! Read Page data
     .,           PPST_PPD   = 3         ! Process Page data
     .,           PPST_SPD   = 4         ! Send Page data
     .,           PPST_RDC   = 5         ! Read DCB
     .,           PPST_CPG   = 6         ! Activate Display List
     .,           PPST_CPP   = 7         ! Complete Page processing
     .,           PPST_CHP   = 8         ! Complete Cache Page processing
     .,           PPST_IDL   = 9         ! Iddle state
     .          )
C
C --- Page Processing mode declarations
C
      INTEGER*2   PPMD_ELP,  PPMD_ALO,  PPMD_RES
C
      PARAMETER (
     .            PPMD_ELP   = 1         ! Process a Normal EL Panel Page
     .,           PPMD_ALO   = 2         ! Process an allready loaded Page
     .,           PPMD_RES   = 3         ! Process a display list Page
     .          )
C
C --- Page Processing sequence declarations
C
      INTEGER*2   PPSEQUEN(7,3)          ! (sequence_step,sequence_mode)
C
      DATA        PPSEQUEN  /
     .            PPST_RPH, PPST_RPD, PPST_PPD, PPST_SPD
     .,           PPST_RDC, PPST_CPP, PPST_IDL
C
     .,           PPST_SPD, PPST_CHP, PPST_IDL, 0
     .,           0,        0,        0
C
     .,           PPST_CPG, PPST_CHP, PPST_IDL, 0
     .,           0,        0,        0                  /
C
C ------------------
C --- CDB Labels ---
C ------------------
C
CSGI
CSGI  QSGI   YXSTRTXRF, TP(*), YXENDXRF                                   \CP
CSGIEND
CVAX
CVAX  SCTB   YXSTRTXRF, TP(*)                                             \CP
CVAXEND
CIBM
CP    USD8 YXSTRTXRF, TP(*)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:04:47 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  TPACTDEV       ! ACTIVE DEVICE
     &, TPDEVRES       ! RESET ONE EL PANEL DEVICE
     &, TPERROR        ! EL PANEL ERROR
     &, TPXIDCB(600)   ! DCB BLOCK BUFFER FOR SLEW PURPOSE
     &, TPXIIDBP(3)    ! CURRENT INPUT DCB BLOCK POINTER
     &, TPXIIDEV       ! LAST EL PANEL WHERE AN INPUT OCCUR
     &, TPXIILIN       ! INPUT LINE NUMBER OF THE LAST INPUT DCB
     &, TPXOILIN(3)    ! CURRENT TPXO INPUT LINE UPDATED
     &, TPXOODBP(3)    ! CURRENT OUTPUT DCB BLOCK POINTER
     &, TPXOODCB(3)    ! CURRENT TPXO OUTPUT DCB
     &, TPXOODEV       ! CURRENT OUTPUT DEVICE
     &, TPXOPAGE(3)    ! EL PANEL CURRENT PAGE
     &, TPXOREQP       ! CURRENT DEVICE REQUESTED PAGE
C$
      LOGICAL*1
     &  TPDEVDIS(3)    ! DISABLE EACH EL PANEL DEVICE
     &, TPDISAB        ! DISABLE EL PANEL MODULES
     &, TPDUMMY        ! EL PANEL DUMMY (FOR BLANK BOX)
     &, TPFILUPD       ! EL PANEL FILE UPDATE
     &, TPRESET        ! RESET ALL EL PANEL DEVICES
     &, TPXICHAR       ! CHARACTER INPUTTED FOR STRING EDIT
     &, TPXIEDAT(32,3) ! EDIT STRING FOR ACTUAL VALUE
     &, TPXIEDCM(32,3) ! EDIT STRING FOR COMMENT DISPLAY
     &, TPXIEDEC(32,3) ! EDIT STRING FOR ECHO DISPLAY
     &, TPXIEDER(3)    ! EDIT ERROR
     &, TPXIFAST       ! FAST UPDATE OF THE LAST DCB INPUTED
     &, TPXINSER(3)    ! INSERT MODE
     &, YXSTRTXRF      ! Start of CDB
C$
      LOGICAL*1
     &  DUM0000001(106831),DUM0000002(3),DUM0000003(2)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,DUM0000001,TPDISAB,TPDEVDIS,TPRESET,DUM0000002
     &, TPDEVRES,TPFILUPD,TPDUMMY,DUM0000003,TPERROR,TPACTDEV
     &, TPXOPAGE,TPXOREQP,TPXOODEV,TPXOODCB,TPXOILIN,TPXOODBP
     &, TPXICHAR,TPXINSER,TPXIIDEV,TPXIILIN,TPXIIDBP,TPXIFAST
     &, TPXIEDAT,TPXIEDCM,TPXIEDEC,TPXIEDER,TPXIDCB   
C------------------------------------------------------------------------------
CIBMEND
C
C
C
C --------------------
C --- Local Labels ---
C --------------------
C
C --- General Local Labels
C
      INTEGER*4
     .            I,   J,   K,   L       ! Loops index
     .,           ID,  JD,  KD,  LD      ! Loops data
C
      INTEGER*2
     .            I2ID, I2JD, I2KD, I2LD ! I*2 Loop data
C
C --- Program Control Local Label
C
      LOGICAL*1
     .            FIRSTPAS               ! FIRST PASs flag
     .,           DEVDISAB(MNDEV)        ! Disable Flag for every unit
     .,           DEVRESET(MNDEV)        ! Reset Flag for every unit
     .,           FILUPDIP               ! FILe UPDate In Progress
C
C --- Program States Local Labels
C
      INTEGER*2
     .            FPSSTATE               ! First Pass State pointer.
     .,           FPSMODE                ! First Pass State Mode indicator
     .,           FPSSTEP                ! First Pass State step pointer.
     .,           FPSRESCT               ! First PAss Reset Time Counter
     .,           LOADCNTR               ! Delay counter before initial load
C
     .,           PGPSTATE(NPAR)         ! Page processing states pointer.
     .,           PGPRMODE(NPAR)         ! Page processing mode indicator
     .,           PGPRSTEP(NPAR)         ! Page processing mode indicator
C
C --- Page.dat File name labels
C
      CHARACTER*1
CVAX
CVAX .            PGDTNAMV*5             ! PaGe.DaT name version number.
CVAXEND
CIBM
     .            PGDTNAMV*256           ! PaGe.DaT name version number.
CIBMEND
     .,           PGDTFLNM*256           ! PaGe.DaT file name
C
C --- Page buffer control labels
C
      LOGICAL*1
     .            PARUNREM(NPAR)         ! Page ARea is UN REMovable
     .,           PARRESID(NPAR,INDEV)   ! Page ARea is RESIDent
C
      INTEGER*4
     .            PARORDLD(NPAR)         ! Page ARea ORDer of LoaD
C
      INTEGER*2
     .            PARNBUSE(NPAR)         ! Page ARea NumBer of USE
     .,           DEVRESPC(INDEV)        ! Number of Resident Page Loaded
C
C --- Page buffers
C
     .,           PAGESERC(MAX_DCB*4,NPAR)     ! Starting Ending Row Col.
C
     .,           PAGENVPO(2,NPAR)             ! Page Non-volatile position
     .,           PAGENVIN(2,NPAR)             ! Page Non-volatile increment
     .,           PAGENVCS(2,NPAR)             ! Page Non-volatile Char size
     .,           PAGECOBL(4,MAX_DCB,NPAR)     ! Page Color block pos. in pix
     .,           PAGETOZN(4,MAX_DCB,NPAR)     ! Page Touch zone pos. in pix
     .,           PAGESTZN(4,MAX_DCB,NPAR)     ! Page State zone pos. in pix
C
C --- Page control labels
C
     .,           PREVPAGE(INDEV)        ! Local previous page
     .,           PGNUMDFP(INDEV)        ! Page number of the device first pg
     .,           LDV                    ! Local DeVice index, for pg process
     .,           PPAR                   ! Process Page ARea index
     .,           PAGENVNC               ! Page Non-volatile number of colum
C
      INTEGER*4
     .            PARORDER               ! Page ARea ORDER to be loaded
     .,           PARUNUSE               ! Page ARea un-used
     .,           PARLOADE               ! Page ARea allready loaded
     .,           PAROLDES               ! Page ARea oldest
     .,           PAROLDAG               ! Page ARea oldest age
C
C --- Page construction local varialbles
C
      INTEGER*2
     .            SBI                    ! Serial i/o Buffer Index
     .,           FBI                    ! File Buffer Index
     .,           CHRWIDTH               ! Width of the character of the page
     .,           CHRHEIGH               ! Height of the character of the page
     .,           PAGEXORG               ! Page X_coord origin
     .,           PAGEYORG               ! Page Y_coord origin
     .,           PAGEXPOS               ! Page X_coord position
     .,           PAGEYPOS               ! Page Y_coord position
     .,           PAGEXEND               ! Page X_coord ending position
     .,           PAGEYEND               ! Page Y_coord ending position
     .,           PAGE1POS               ! Page 1_coord position
     .,           PAGE2POS               ! Page 2_coord position
     .,           LINESTRT               ! index of the beginning of a line
C
      INTEGER*4
     .            ODBP                   ! Output DCB Block Pointer
     .,           SDBP                   ! Local Sub-DCB pointer
     .,           LDBP                   ! Local DCB Block Pointer
C
C --- Device Control Local label for volatile update
C
      INTEGER*4
     .            ODEV                   ! Output DEVice index
     .,           OPAR                   ! Local Page Area index
     .,           ILIN                   ! Input Line number
     .,           ODCB                   ! Output DCB INDeX
     .,           BFCI(INDEV)            ! Buffer Chunc index
C
C --- Directive Call Extra Parameters
C
      INTEGER*4
     .            DIRECERR                  ! Directive error code
     .,           OFF_BLOCK(2,1)            ! Offset Block
C
      LOGICAL*1
     .            DIRERESU                  ! Directive return status
     .,           HOST_UPD                  ! Update the host ?
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     .            VAL_TABLE
C
C --- Page volatile update variables
C
      LOGICAL*1
     .            L1TMPVAL                  ! Temporary Value
     .,           L1PRVVAL                  ! Previous Value
     .,           L1ACTVAL                  ! Actual Value
     .,           CHARPRES                  ! Character present on the line
C
C --- Color Emulation Label
C
      LOGICAL*1
     .            INACTCOL                  ! Inactive color highlight
     .,           ACTIVCOL                  ! Active color highlight
C
      LOGICAL*1
     .            UN, HI
C
      PARAMETER (
     .            UN = .FALSE.              ! UnHighlighted
     .,           HI = .TRUE.               ! Highlighted
     .           )
C
      LOGICAL*1
     .            HIGHCOLO(0:255)           ! Highlighted Color
C
C --- Page volatile update Local flags
C
      LOGICAL*1
     .            FLIPADCB                  ! Flipable DCB
     .,           MULVLDCB                  ! Multiple value DCB
     .,           PRVDRKST                  ! previous dark concept value
     .,           ACTDRKST                  ! Actual dark concept value
     .,           PRVHIGST                  ! Previous hightlight status
     .,           ACTHIGST                  ! Actual hightlight status
     .,           ACTCOLCD                  ! Actual color condition value
C
     .,           COLZONER                  ! Color zone erasing request
     .,           BUTCTNDW                  ! Button contour drawing request
     .,           NONVOLDW                  ! Non-volatile drawing request
     .,           VOLATIDW                  ! Volatile drawing request
     .,           VOLAHIGH                  ! highlight volatile drawing
     .,           STACOLTO                  ! State color toggle request
     .,           COLORSTA                  ! Color Status
C
      CHARACTER
     .            OSTRINGC*(OSTRINGS)       ! Output string
C
      INTEGER*1
     .            OSTRINGB(OSTRINGS)        ! Output string
     .,           I1TMPVAL(8)               ! Temporary Value
     .,           I1ACTVAL(8)               ! Actual Value
C
      INTEGER*2
     .            I2OFFSET(2)               ! Local offset
     .,           I2TMPVAL(4)               ! Temporary Value
     .,           I2PRVVAL                  ! Previous Value
     .,           I2ACTVAL                  ! Actual Value
     .,           XPOS(8)                   ! X starting position for box draw
     .,           YPOS(8)                   ! X starting position for box draw
     .,           XEND(8)                   ! X ending position for box draw
     .,           YEND(8)                   ! X ending position for box draw
     .,           DCBSIZE                   ! Local DCB size
     .,           DCBTYPE                   ! Local DCB type
     .,           DATATYPE                  ! Local DCB data type
     .,           COLORNUM                  ! Color Number
C
      INTEGER*4
     .            I4OFFSET                  ! Local offset
     .,           I4TMPVAL                  ! Temporary Value
     .,           I4PRVVAL                  ! Previous Value
     .,           I4ACTVAL                  ! Actual Value
     .,           OSTRSIZE                  ! Output string size
C
      REAL*4
     .            R4TMPVAL                  ! Temporary Value
     .,           R4PRVVAL                  ! Previous Value
     .,           R4ACTVAL                  ! Actual Value
C
      REAL*8
     .            R8TMPVAL                  ! Temporary Value
     .,           R8PRVVAL                  ! Previous Value
     .,           R8ACTVAL                  ! Actual Value
C
C --- Cursor positionning
C
      LOGICAL*1
     .            PREVCURF(INDEV)           ! Previous cursor enable flag
C
      INTEGER*2
     .            ECHOAROW(NPAR)            ! Echo area Row
     .,           ECHOACOL(NPAR)            ! Echo area Colum
C
      EQUIVALENCE
     .           (R8TMPVAL, L1TMPVAL)
     .,          (R8TMPVAL, I1TMPVAL)
     .,          (R8TMPVAL, I2TMPVAL)
     .,          (R8TMPVAL, I4TMPVAL)
     .,          (R8TMPVAL, R4TMPVAL)
C
     .,          (R8PRVVAL, L1PRVVAL)
     .,          (R8PRVVAL, I2PRVVAL)
     .,          (R8PRVVAL, I4PRVVAL)
     .,          (R8PRVVAL, R4PRVVAL)
C
     .,          (R8ACTVAL, L1ACTVAL)
     .,          (R8ACTVAL, I1ACTVAL)
     .,          (R8ACTVAL, I2ACTVAL)
     .,          (R8ACTVAL, I4ACTVAL)
     .,          (R8ACTVAL, R4ACTVAL)
C
     .,          (I4OFFSET, I2OFFSET)
C
     .,          (OSTRINGB, OSTRINGC)
C
C ----------------------
C --- Data Statement ---
C ----------------------
C
      DATA
     .            FIRSTPAS     /.TRUE./
     .,           FPSSTATE     /FPST_IVS/
     .,           FPSMODE      /FPMD_IRS/
     .,           PGDTFLNM     /'/cae/pg/page.dat'/
     .,           HIGHCOLO     /
     .            UN                        ! Transparent    (Always .False.)
     .,           HI                        ! Red
     .,           HI                        ! Green
     .,           HI                        ! Yellow
     .,           UN                        ! Blue
     .,           HI                        ! Magenta
     .,           UN                        ! Cyan
     .,           UN                        ! White
     .,           UN                        ! Gray
     .,           UN                        ! Light Gray
     .,           HI                        ! Orange
     .,           UN                        ! Pink
     .,           UN                        ! Black
     .,           UN                        ! Light Green
     .,           UN                        ! Brown
     .,           HI                        ! Amber
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN
     .,           UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN,UN /
C
C ---------------------------------------------
C ---                                       ---
C ---------------------------------------------
C
