C     character*80 rvlstr1
C     . /'$Revision: ccu_comm.inc V1.0 (RC) June-92$'/
C
C
      LOGICAL*1 FIRST_PASS,
     +          BACKGROUND_REQ,
     +          RETRY_TEST,
     +          power_status(4),
     +          load_pass

      INTEGER*2 WAIT_COUNT,
     +          STATUS,
     +          <PERSON>NT<PERSON><PERSON>,
     +          RESULTS(6),
     +          RETRY,
     +          START_POINTER,
     +          POINTER,
     +          TEST_TYPE,
     +          DMC_NUMBER,
     +          CBUS_ADDRESS,
     +          SLOT_NUMBER,
     +          AIP_T_NUMBER,
     +          COMM_BUFF1,
     +          ERROR_COUNT,
     +          INT_DIFF,
     +          SLOT_LOCATION

      CHARACTER*8  INTERFACE_ASSIGNMENT
      CHARACTER*16 CABINET
      character*1 system
            
      REAL*4 REAL_DIFF,
     +       real_results(2)


      INTEGER*4 LOC,
     +          I,
     +          MOD


