/*****************************************************************************

  'Title                MAIN MOTION TEST PROGRAM
  'Module_ID            MTP
  'Entry_point          MTP
  'Documentation
  'Customer             QANTAS
  'Application          Set up of MT and MTANALYSE program for tuning
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history
05 mar 92 Norm   Using box #1 accel signal for full platform FOR NOW
11 nov 91 NORM B New scheme for motion test.
12 june 91 Norm  Hide mask forgain/phase not being tuned
14 june 91 norm  Message buffers
03 july 91 Norm  Combining all motion test programs into one program: MTEST

  'References

*/


#include "mot_define.h"
#include "mot_ext.h"
#include <math.h>

extern int MTMESSAGE[5][60];
extern float MTFRQTABLE[50];


void mtp()
{

/*
C
C      LOCAL VARIABLES
C
*/

static int 	m_pttest=0,	/* previous MTTEST_KEY     */
		mtp_first = 1;	/* sign of wave        */

static float	*m_addr;	/* address of selected tuning gain */

/*
*	------------------------------------------------------------------
CD	Get address of selected tuning gain in auto mtp and set MTAUTOTUNE_KEY
*	------------------------------------------------------------------
*
*	C30 address is 4 times less than Host address
*
*/

m_addr = (float *)(MTADDR >> 2);

if ( ( m_addr == &J1KV )||( m_addr == &J2KV )||( m_addr == &J3KV )||
     ( m_addr == &J4KV )||( m_addr == &J5KV )||( m_addr == &J6KV )   )
{
	MTAUTOTUNE_KEY = T_KV;
}
else if ( ( m_addr == &J1KL )||( m_addr == &J2KL )||( m_addr == &J3KL )||
          ( m_addr == &J4KL )||( m_addr == &J5KL )||( m_addr == &J6KL )  )
{
	MTAUTOTUNE_KEY = T_KL;
}
else if ( ( m_addr == &J1KBET )||( m_addr == &J2KBET )||( m_addr == &J3KBET )||
          ( m_addr == &J4KBET )||( m_addr == &J5KBET )||( m_addr == &J6KBET ) )
{
	MTAUTOTUNE_KEY = T_KBET;
}
else if ( ( m_addr == &J1KMID )||( m_addr == &J2KMID )||( m_addr == &J3KMID )||
          ( m_addr == &J4KMID )||( m_addr == &J5KMID )||( m_addr == &J6KMID )  )
{
	MTAUTOTUNE_KEY = T_KMID;
}
else if ( ( m_addr == &J1KCO )||( m_addr == &J2KCO )||( m_addr == &J3KCO )||
     ( m_addr == &J4KCO )||( m_addr == &J5KCO )||( m_addr == &J6KCO )   )
{
	MTAUTOTUNE_KEY = T_KCO;
}
/*
* 	address is set to zero if page not active
*/
else if ( m_addr == 0 )
{
	MTAUTOTUNE_KEY = 0;
}
/*
* 	if address is invalid, default to KV
       TO BE REMOVED WHEN CURSOR MOVEMENT WILL BE LIMITED
*/
else
{
	MTAUTOTUNE_KEY = T_KV;
}

/*
*	------------------------------------------------------------------
CD	write DFC selected test into C30 variable
*	------------------------------------------------------------------
*/
if(MTMTFAAKEY != 0 )
{
	/*
	* 	Change test only if not driving
	*/
	if (MTSTATUS != DRIVE )
	{
		MTTEST_KEY = MTMTFAAKEY;
	}
	else
	{
		MTMTFAAKEY = MTTEST_KEY ;
	}
}
else if(MTMORNKEY != 0 )
{
	/*
	* 	Change test only if not driving
	*/
	if (MTSTATUS != DRIVE )
	{
		MTTEST_KEY = MTMORNKEY;
	}
	else
	{
		MTMORNKEY = MTTEST_KEY ;
	}
}
else if (MTTUNE_KEY != 0 )
{
	MTTEST_KEY = MTTUNE_KEY;
}
else if (MTAUTOTUNE_KEY != 0 )
{
	MTTEST_KEY = MTAUTOTUNE_KEY;
}
/*
* 	AUTOMATED SCALING TESTS
*
*	Should be forced to 0 when quiting the page **GEOFFREY**
*/
else if ( MTSCALEKEY != 0 )
{
	MTTEST_KEY = MTSCALEKEY;
}
else
{
	MTTEST_KEY = 0;
}


/*------------------------------------------------------
CD MP000 PROCESS TEST KEY AND SETUP MOTION TEST PROGRAMS
--------------------------------------------------------
*
*	NOTE: MTRECORD.C and MOPLOT.FOR ( host s/w )
*	      set up some of the parameters for plots
*
*/

if( MTTEST_KEY!=m_pttest )

/* do setup if change was made or test restarted */
{

	m_pttest = MTTEST_KEY;

	switch(MTTEST_KEY)
	{
	/*
	* 	Turn around bump plot
	*/
	case(TABUMP):
           /* driver */
	   MTAMPSEL  = ACCEL;            /* */
           MTAMPACC = 0.1;		/* drive at .2 g's peak */
	   MTFRQTABLE[0] = 0.5;	  	/* drive at 0.5 Hertz in table mode */
	   MTLENTABLE = 1;		/* frq table of 1 element */
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTANALYS = NOANAL;
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = NONE;
	   MTRSLTMODE = GRAPH;
  	   MRRESET = TRUE;      	/* reset record buffer and program */
           MRNCURVE =1;             	/* just one curve*/
	   MTNPOINT = 600;         	/* size of the curve [point] */
	   MRSAMFRQ = 6;		/* sampling rate, subband of sys_freq,	*/

	   strpack(&MTMESSAGE[MTMSPOINT][0]," now testing TURN AROUND BUMP, at 0.5 Hz .2g's");
	   MTMSPOINT++;
	   if( MTMSPOINT==5)MTMSPOINT=0;

	  break;


	/*
	* 	leg balance plot
	*/
	case(LEGBAL):
           /* driver */
	   MTAMPSEL  = ACCEL;            /* */
           MTAMPACC = 0.1;		/* drive at .2 g's peak */
	   MTFRQTABLE[0] = 0.5;	  	/* drive at 0.5 Hertz in table mode */
	   MTLENTABLE = 1;		/* frq table of 1 element */
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTANALYS = NOANAL;
		/* recording */
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = NONE;
	   MTRSLTMODE = GRAPH;
	   MRRESET = TRUE;      	/* reset record buffer and program */
	   MRNCURVE =7;             	/* number of curve*/
	   MTNPOINT = 450;         	/* size of the curve [point] */
	   MRSAMFRQ = 6;		/* sampling rate, subband of sys_freq,	*/

	   strpack(&MTMESSAGE[MTMSPOINT][0]," now testing LEG BALANCE, at 0.5 Hz and .2g's");
	   MTMSPOINT++;
	   if( MTMSPOINT==5)MTMSPOINT=0;

	  break;

	/*
	* 	Frequency response plot
	*/
	case(FRQRESP):
           /* driver */
	   MTAMPSEL  = ACCEL;            /* select acceleration */
           MTAMPACC = .2;	         /* drive at .2 g peak*/
	   MTAUTOFREQ=TRUE;            /* get auto frq table setup */
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTANALYS = FRA;
	   MTEPS = .005;
           MTANALRST = TRUE; 		/* reset analysis  */
	   MTOUT_KEY = KAA;
	   MTIN_KEY = KAP;
	   MTOUTGAIN = 1.0 ;
	   MTPHASEOFS = 0.0;
		/* test and result modes */
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = SINGLE;
	   MTRSLTMODE = BODEPLOT;
	   MRRESET = TRUE;      	/* reset record buffer and program */
 	   MRNCURVE =2;                 /* number of curve*/
	   MTNPOINT = 49;       /* size of the curve [point] */
	   MRSAMFRQ = 1;		/* sampling rate, subband of sys_freq,	*/

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing FREQUENCY RESPONSE IN HEAVE, ");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;
	strpack(&MTMESSAGE[MTMSPOINT][0],"at frequencies ranging from 0.1Hz to 15Hz and .2g's");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	   break;

	/*
	* 	position frequency response plot for jack MTJACK
	*/
	case(POSFRQRESP):
           /* driver */
	   MTAMPSEL  = ACCEL;           /* select acceleration */
  	   MTAMPACC = .2;	        /* drive at .2 g peak*/
	   MTAUTOFRSM = TRUE;           /* get SMALL auto frq table setup */
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTANALYS = FRA;
	   MTEPS = .005;
           MTANALRST = TRUE; 		/* reset analysis  */
	   MTOUT_KEY = KXAC;
	   MTIN_KEY = KXC;
	   MTOUTGAIN = 1.0 ;
	   MTPHASEOFS = 0.0;
		/* test and result modes */
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = SINGLE;
	   MTRSLTMODE = BODEPLOT;
	   MRRESET = TRUE;      	/* reset record buffer and program */
	   MRNCURVE =2;                 /* number of curve*/
	   MTNPOINT = 28;               /* size of the curve [point] WAS 49 */
	   MRSAMFRQ = 1;		/* sampling rate, subband of sys_freq,	*/

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing POSITION FREQUENCY RESPONSE ");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;
	strpack(&MTMESSAGE[MTMSPOINT][0],"at frequencies ranging from 0.1Hz to 10Hz and .2g's");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	   break;

	/*
	*  	AUSTRIALIAN CAA TESTS FOR QANTAS
	*
	* 	Required signals will be mapped out using ADIO#2 and ADIO#3
	*	to be plotted on a strip chart recorder.
	* 	MAP program will be flagged to map proper signals.
	*/

	/*
	* 	POSITION LINEARITY #1 : check jack response to FIRST frequency
	*/
	case(POSLINEAR1):
           /* driver */
	   MTAMPSEL  = DISPL ;
  	   MTAMPDISP = .3;
	   MTFREQREQ = 0.1;
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   /* analyser */
	   MTANALYS = NOANAL;
	   /* test and result modes */
	   MTDRIVMODE = CONTINU;
	   MTCHANANAL = NOANAL;
	   MTRSLTMODE = MAPOUT;       /* patch panel output */
	   /* mapping signals */

	   MAP_KEY1 = KXAC;
	   MAPGAIN1 = 1.0;
	   MAP_KEY2 = KXAC;
	   MAPGAIN2 = 1.0;
	   MAP_KEY3 = KXAC;
	   MAPGAIN3 = 1.0;
	   MAP_KEY4 = KXAC;
	   MAPGAIN4 = 1.0;
	   MAP_KEY5 = KXAC;
	   MAPGAIN5 = 1.0;
	   MAP_KEY6 = KXAC;
	   MAPGAIN6 = 1.0;
	   MAP_KEY7 = KXC;
	   MAPGAIN7 = 1.0;

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing POSITION LINEARITY at 0.1 Hz");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	   break;

	/*
	* 	POSITION LINEARITY #2 : check jack response to SECOND frequency
	*/
	case(POSLINEAR2):
           /* driver */
	   MTAMPSEL  = DISPL ;
  	   MTAMPDISP = .3;
	   MTFREQREQ = 3.0;
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   /* analyser */
	   MTANALYS = NOANAL;
	   /* test and result modes */
	   MTDRIVMODE = CONTINU;
	   MTCHANANAL = NOANAL;
	   MTRSLTMODE = MAPOUT;       /* patch panel output */
	   /* mapping signals */

	   MAP_KEY1 = KXAC;
	   MAPGAIN1 = 1.0;
	   MAP_KEY2 = KXAC;
	   MAPGAIN2 = 1.0;
	   MAP_KEY3 = KXAC;
	   MAPGAIN3 = 1.0;
	   MAP_KEY4 = KXAC;
	   MAPGAIN4 = 1.0;
	   MAP_KEY5 = KXAC;
	   MAPGAIN5 = 1.0;
	   MAP_KEY6 = KXAC;
	   MAPGAIN6 = 1.0;
	   MAP_KEY7 = KXC;
	   MAPGAIN7 = 1.0;

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing POSITION LINEARITY at 3.0 Hz");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	   break;


	/*
	* 	ACCELERATION PERFORMANCE #1 : check motion acceleration
	*				      AT 10 HZ
	*				      Have to achieve +-0.5 g
	*********** NOTE : DISABLED NOW THAT TEST WAS RUN FOR CAA ************
	* amp was 0.7
	*/
	case(ACCELPERF1):
           /* driver */
/*******   MTAMPSEL  = ACCEL;
  	   MTAMPACC = 0.0;
           MTNOLIMIT = TRUE;
	   MTFREQREQ = 10.0;
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
****/
	   /* analyser */
	   MTANALYS = NOANAL;
	   /* test and result modes */
/**********
	   MTDRIVMODE = CONTINU;
	   MTCHANANAL = NOANAL;
   	   MTRSLTMODE = MAPOUT; **/
	   /* patch panel output */
	   /* mapping signals */
/************
   	   MAP_KEY1 = KAA;
	   MAPGAIN1 = 1.0;
	   MAP_KEY2 = KAP;
	   MAPGAIN2 = 1.0;
	   MAP_KEY3 = FALSE;
	   MAPGAIN3 = 1.0;
	   MAP_KEY4 = FALSE;
	   MAPGAIN4 = 1.0;
	   MAP_KEY5 = FALSE;
	   MAPGAIN5 = 1.0;
	   MAP_KEY6 = FALSE;
	   MAPGAIN6 = 1.0;
	   MAP_KEY7 = FALSE;
	   MAPGAIN7 = 1.0;

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing ACCELERATION PERFORMANCE at 5.0 Hz");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;
***********/
	   break;

	/*
	* 	ACCELERATION PERFORMANCE #2 : check motion acceleration
	*				      at 30 Hz
	*********** NOTE : DISABLED NOW THAT TEST WAS RUN FOR CAA ************
	** amp WAS 0.15
	*/
	case(ACCELPERF2):
           /* driver */
/****	   MTAMPSEL  = ACCEL;
  	   MTAMPACC = 0.0;
	   MTFREQREQ = 30.0;
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTANALYS = NOANAL;
	   MTDRIVMODE = CONTINU;
	   MTCHANANAL = NOANAL;
	   MTRSLTMODE = MAPOUT;
	   MAP_KEY1 = KAA;
	   MAPGAIN1 = 1.0;
	   MAP_KEY2 = KAP;
	   MAPGAIN2 = 1.0;
	   MAP_KEY3 = FALSE;
	   MAPGAIN3 = 1.0;
	   MAP_KEY4 = FALSE;
	   MAPGAIN4 = 1.0;
	   MAP_KEY5 = FALSE;
	   MAPGAIN5 = 1.0;
	   MAP_KEY6 = FALSE;
	   MAPGAIN6 = 1.0;
	   MAP_KEY7 = FALSE;
	   MAPGAIN7 = 1.0;

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing ACCELERATION PERFORMANCE at 30.0 Hz");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;
**********/
	   break;

	/*
	* 	MORNING READINESS 1 : velocity vs current graph for jack MTJACK
	*/
	case(MORN1):
           /* driver */
	   MTAMPSEL  = ACCEL;            /* */
           MTAMPACC = 0.2;		/* drive at .2 g's peak */
	   MTFRQTABLE[0] = 0.125;	/* drive at 0.125 Hertz in table mode */
	   MTLENTABLE = 1;		/* frq table of 1 element */
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTANALYS = NOANAL;
		/* test and result modes */
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = NOANAL;
	   MTRSLTMODE = GRAPH;
	   MRRESET = TRUE;      	/* reset record buffer and program */
	   MTJACK = JACK1;
	   MRNCURVE =1;                 /* number of curve*/
	   MTNPOINT = 400;		/* size of the curve [point] */
	   MRSAMFRQ = 10;		/* sampling rate, subband of sys_freq,*/

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing MORNING READINESS 1, at 0.125 Hz and .2g's");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	  break;

	/*
	* 	MORNING READINESS 2 : Velocity error RMS value
	*/
	case(MORN2):
           /* driver */
	   MTAMPSEL  = ACCEL;            /* */
           MTAMPACC = 0.2;		/* drive at .2 g's peak was .2 */
	   MTFRQTABLE[0] = 0.1;	  	/* drive at 0.1 Hertz in table mode */
	   MTLENTABLE = 1;		/* frq table of 1 element */
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTANALYS = RMS;
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = ALL;
	   MTRSLTMODE = REPORT;
	   MTREPORTNUM = 1;            /* one set of results */
	   MRRESET = TRUE;      	/* reset record buffer and program */
	   MTIN_KEY=KVE;	/* check velocity error RMS value */

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing MORNING READINESS 2, at 0.1 Hz and .2g's");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	  break;

	/*
	* 	MORNING READINESS 3 : velocity gain and phase
	*/
	case(MORN3):
           /* driver */
	   MTAMPSEL  = ACCEL;            /* */
           MTAMPACC = 0.2;		/* drive at .2 g's peak */
	   MTFRQTABLE[0] = 1.0;	  	/* drive at 1.0 Hertz in table mode */
	   MTLENTABLE = 1;		/* frq table of 1 element */
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = ALL;
	   MTRSLTMODE = REPORT;
	   MTREPORTNUM = 1;            /* one result */
	   MTANALYS = FRA;
	   MTEPS = .001;
           MTANALRST = TRUE; 		/* reset analysis  */
	   MTIN_KEY = KVR;		/* check velocity gain and phase */
	   MTOUT_KEY = KVAC;
	   MTOUTGAIN = 1.0 ;
	   MTPHASEOFS = 0.0;

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing MORNING READINESS 3, at 1.0 Hz and .2g's");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	  break;

	/*
	* 	MORNING READINESS 4 : Friction test (results shown on the screen)
	*/
	case(MORN4):
           /* driver */
	   MTAMPSEL  = ACCEL;            /* */
           MTAMPACC = 0.2;		/* drive at .2 g's peak was .2*/
	   MTFRQTABLE[0] = 0.1;	  	/* drive at 0.1 Hertz in table mode */
	   MTLENTABLE = 1;		/* frq table of 1 element */
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = ALL;
	   MTRSLTMODE = REPORT;
	   MTREPORTNUM = 1;            /* one set of results */
	   MTANALYS = RMS;
	   MTIN_KEY=KFRIC;	       /* check velocity error RMS value */

	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing FRICTION, at 0.1 Hz and .2g's");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	  break;

	/*
	* 	MORNING READINESS 5 : position gain and phase at 0.5 and 2.5 Hz
	*/
/*
	case(MORN5):
	   MTAMPSEL  = ACCEL;
           MTAMPACC = 0.2;
	   MTFRQTABLE[0]  = 0.5;
	   MTFRQTABLE[1]  = 2.5;
	   MTLENTABLE = 2;
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = ALL;
	   MTRSLTMODE = REPORT;
	   MTREPORTNUM = 2;
	   MTANALYS = FRA;
	   MTEPS = .001;
           MTANALRST = TRUE;
	   MTIN_KEY = KXC;
	   MTOUT_KEY = KXAC;
	   MTOUTGAIN = 1.0 ;
	   MTPHASEOFS = 0.0;
	strpack(&MTMESSAGE[MTMSPOINT][0]," now testing MORNING READINESS 4 ");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;
*/

	  break;


	case(T_KV):
           /* driver */
	   MTAMPSEL  = ACCEL;            /* */
           MTAMPACC = .2;		/* drive at .2 g's peak */
	   MTFREQREQ = 1.0;	  	/* drive at 1.0 Hz in continuous mode*/
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	   /* analyser */
	   MTDRIVMODE = CONTINU;
	   MTCHANANAL = ALL;
	   MTRSLTMODE = SCROLL_UP;
	   MTANALYS = FRA;
	   MTEPS = .001;
           MTANALRST = TRUE;
	   MTOUT_KEY = KVAC;
	   MTIN_KEY = KVR;
	   MTOUTGAIN = 1.0 ;
	   MTPHASEOFS = 12.0;
	   /* Results */
	   MTGAINHIDE = 1.0;
	   MTPHASHIDE = 0.0;

	strpack(&MTMESSAGE[MTMSPOINT][0]," now tuning KV, at 1.0 Hz .2g's");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	   break;

	case(T_KL):
		/* driver */
	   MTAMPSEL  = ACCEL;            /* select displacement */
           MTAMPACC = 0.4;		/* drive at .2 g's peak */
	   MTFREQREQ = 1.0;	  	/* drive at 1.0 Hz in continuous mode*/
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = TRUE;
	        /* analyser */
	   MTDRIVMODE = CONTINU;
	   MTCHANANAL = ALL;
	   MTRSLTMODE = SCROLL_UP;
	   MTANALYS = FRA;
	   MTEPS = .001;
           MTANALRST = TRUE;
	   MTOUT_KEY = KXAC;
	   MTIN_KEY = KXC;
	   MTOUTGAIN = 2.0 ;
	   MTPHASEOFS = 90.0;
	   /* Results */
	   MTGAINHIDE = 0.0;
	   MTPHASHIDE = 1.0;

	strpack(&MTMESSAGE[MTMSPOINT][0]," now tuning KL, at 1.0 Hz .2g's acceleration command DISABLED");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	   break;

	case(T_KBET):
		/* driver */
	   MTAMPSEL  = ACCEL;            /* select displacement */
           MTAMPACC = 0.4;		/* drive at .2 g's peak */
	   MTFREQREQ = 1.0;	  	/* drive at 1.0 Hz in continuous mode*/
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = TRUE;
	        /* analyser */
	   MTDRIVMODE = CONTINU;
	   MTCHANANAL = ALL;
	   MTRSLTMODE = SCROLL_UP;
	   MTANALYS = FRA;
	   MTEPS = .001;
           MTANALRST = TRUE;
	   MTOUT_KEY = KXAC;
	   MTIN_KEY = KXC;
	   MTOUTGAIN = 2.0 ;
	   MTPHASEOFS = 90.0;
	   /* Results */
	   MTGAINHIDE = 1.0;
	   MTPHASHIDE = 0.0;


	strpack(&MTMESSAGE[MTMSPOINT][0]," now tuning KBET, at 1.0 Hz .2g's acceleration command DISABLED");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	   break;

	case(T_KMID):
		/* driver */
	   MTAMPSEL  = ACCEL;            /* select displacement */
           MTAMPACC = 0.2;		/* drive at .2 g's peak */
	   MTFREQREQ = 1.0;	  	/* drive at 1.0 Hz in continuous mode*/
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTDRIVMODE = CONTINU;
	   MTCHANANAL = ALL;
	   MTRSLTMODE = SCROLL_UP;
	   MTANALYS = FRA;
	   MTEPS = .001;
           MTANALRST = TRUE;
	   MTOUT_KEY = KVAC;
	   MTIN_KEY = KVC;
	   MTOUTGAIN = 1.0 ;
	   MTPHASEOFS = 20.0;
	   /* Results */
	   MTGAINHIDE = 1.0;
	   MTPHASHIDE = 0.0;

	strpack(&MTMESSAGE[MTMSPOINT][0]," now tuning KMID, at 1.0 Hz .2g's");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	   break;

	case(T_KCO):
		/* driver */
	   MTAMPSEL  = ACCEL;            /* select acceleration */
           MTAMPACC = .2;		/* drive at .2 g peak*/
           MTFRQTABLE[0]=7.;
           MTFRQTABLE[1]=7.5;
           MTFRQTABLE[2]=8.;
           MTFRQTABLE[3]=8.5;
           MTFRQTABLE[4]=9.;
           MTFRQTABLE[5]=9.5;
           MTFRQTABLE[6]=10.0;
           MTFRQTABLE[7]=10.5;
	   MTLENTABLE = 8;		 /* frq table of 8 elements */
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = ALL;
	   MTRSLTMODE = SCROLL_MAX;
	   MTANALYS = FRA;
	   MTEPS = .005;
           MTANALRST = TRUE;
	   MTOUT_KEY = KAA;
	   MTIN_KEY = KAP;
	   MTOUTGAIN = 1.0 ;
	   MTPHASEOFS = 0.0;
	   /* Results */
	   MTGAINHIDE = 1.0;
	   MTPHASHIDE = 0.0;

	strpack(&MTMESSAGE[MTMSPOINT][0]," now tuning KCO, at frequencies from	7.0 Hz to 10.5 Hz, .2g's");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;

	   break;

/*
* 	AUTO-SCALE
*/
	/*
	* 	System Warmup
	*/
	case(WARM_UP):
           /* driver */
	   MTAMPSEL  = ACCEL;
           MTAMPACC = 0.2;
	   MTFREQREQ = 0.1;	  	/* drive at 1.0 Hz in continuous mode*/
	   MTWAVEREQ = SINE;
	   MTAXISREQ = AXISHEAV;
	   MTNOPOS = FALSE;
	   MTNOACC = FALSE;
	        /* analyser */
	   MTANALYS = NOANAL;
	   MTDRIVMODE = CONTINU;
	   MTCHANANAL = NONE;

	   strpack(&MTMESSAGE[MTMSPOINT][0]," now warming up the motion system");
	   MTMSPOINT++;
	   if( MTMSPOINT==5)MTMSPOINT=0;

	  break;


	default:
	   MTNOLIMIT = FALSE;
	   MTOUTGAIN = 1.0 ;
	   MTPHASEOFS = 0.0;
	   /*
	   *	Results
	   */
	   MTGAINHIDE = 1.0;
	   MTPHASHIDE = 1.0;
           /*
	   *    Reset of mapping utility
	   */
	   MTRSLTMODE = SCROLL_UP; /* unselect patch panel output */
   	   MAP_KEY1 = FALSE;
	   MAPGAIN1 = 1.0;
	   MAP_KEY2 = FALSE;
	   MAPGAIN2 = 1.0;
	   MAP_KEY3 = FALSE;
	   MAPGAIN3 = 1.0;
	   MAP_KEY4 = FALSE;
	   MAPGAIN4 = 1.0;
	   MAP_KEY5 = FALSE;
	   MAPGAIN5 = 1.0;
	   MAP_KEY6 = FALSE;
	   MAPGAIN6 = 1.0;
	   MAP_KEY7 = FALSE;
	   MAPGAIN7 = 1.0;

	  break;

	}				/* end of switch(MTTEST_KEY) */

}					/*  end of if( MTTEST_KEY!=m_pttest) */


}  /* end of file MTP */
/*****************************************************************************

  'Title                MOTION TEST DRIVE PROGRAM - LOGIC PART
  'Module_ID            MTLOGIC
  'Entry_point          MTLOGIC
  'Documentation
  'Customer             QANTAS
  'Application          Tuning and testing of motion system
  'Author               NORM BLUTEAU
  'Date                 DEC 1990

  'System               MOTION
  'Iteration_rate       0.033 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history
12 FEB 91  New scheme for motion test programs
19 DEC 90  Created MTLOGIC running in 30 Hz band

  'References

*/
static int      i,
		m_linaxis = 0,  /* linear axis flag */
		m_lfirst = 1,   /* first pass flag */
		m_pdrive;	/* previous MTDRIVE */

static float   	m_sp0,
	       	m_sp1,
		m_washlim=.25,	/* washout limit */
		/*
		*	Amplitude limiting
		*/
	        m_omega,		/* 2*pi*freq */
		m_invomega,             /* 1/omega */
		m_poslimit,		/* position limit inch */
		m_vellimit=15.0,	/* velocity limit inch/sec */
		m_acclimit=143.0,	/* acceleration limit inch/sec2 */
		m_anglimit=30.,
		m_angvlim=15.,
		m_angalim=120.,
		m_limit1,
		m_limit2,
		m_ampdisp,      /* previous MTAMPDISP */
		m_ampacc,	/* previous MTAMPACC */
		m_ampdispd,	/* demanded disp amplitude from user */
		m_ampaccd,	/* demanded acc amplitude from user */


	       	m_ampfad = 2.0,	 	/* rate of change of amplitude [inch/sec]*/
		m_twopi = 6.283184,	/* 2*pi */
               	m_freqfad = 0.4;  	/* rate of change of frequency [Hz/sec]*/


extern int MTMESSAGE[5][60];
extern float MTFRQTABLE[50];

void mtlogic()
{
/*
* 	First pass
*/


if ( m_lfirst)
{
	MTFREQREQ = 1.0;		/* so m_limit1 is not zero */
	m_poslimit = MKJSCALE * 0.85;
        m_lfirst = FALSE;
}

/*--------------------------------
CD ACCELEROMETER LOGIC
---------------------------------*/

if ( MTACCEL == NOBOX )
{
	JXACCOFS1 = 0.0;			/* accel box #1 x offset */
	JXACCOFS2 = 0.0;			/* accel box #2 x offset */
	JYACCOFS1 = 0.0;			/* accel box #1 y offset */
	JYACCOFS2 = 0.0;			/* accel box #2 y offset */
	JZACCOFS1 = 0.0;			/* accel box #1 z offset */
	JZACCOFS2 = 0.0;			/* accel box #2 z offset */
	JZACCOFS3 = 0.0;			/* accel box #3 z offset */
	JXACCGAIN1 = 0.0;		/* accel box #1 x gain */
	JXACCGAIN2 = 0.0;		/* accel box #2 x gain */
	JYACCGAIN1 = 0.0;		/* accel box #1 y gain */
	JYACCGAIN2 = 0.0;		/* accel box #2 y gain */
	JZACCGAIN1 = 0.0;		/* accel box #1 z gain */
	JZACCGAIN2 = 0.0;		/* accel box #2 z gain */
	JZACCGAIN3 = 0.0;		/* accel box #3 z gain */
}
else if ( MTACCEL == SINGLE_BOX_1 )
{
	JXACCOFS1 = 0.0;			/* accel box #1 x offset */
	JXACCOFS2 = 0.0;			/* accel box #2 x offset */
	JYACCOFS2 = 0.0;			/* accel box #2 y offset */
	JZACCOFS2 = 0.0;			/* accel box #2 z offset */
	JZACCOFS3 = 0.0;			/* accel box #3 z offset */
	JXACCGAIN1 = 0.0;		/* accel box #1 x gain */
	JXACCGAIN2 = 0.0;		/* accel box #2 x gain */
	JYACCGAIN2 = 0.0;		/* accel box #2 y gain */
	JZACCGAIN2 = 0.0;		/* accel box #2 z gain */
	JZACCGAIN3 = 0.0;		/* accel box #3 z gain */
}
else if ( MTACCEL == SINGLE_BOX_2 )
{
	JXACCOFS1 = 0.0;			/* accel box #1 x offset */
	JYACCOFS1 = 0.0;			/* accel box #1 y offset */
	JZACCOFS1 = 0.0;			/* accel box #1 z offset */
	JXACCOFS2 = 0.0;			/* accel box #2 x offset */
	JZACCOFS3 = 0.0;			/* accel box #3 z offset */
	JXACCGAIN1 = 0.0;		/* accel box #1 x gain */
	JYACCGAIN1 = 0.0;		/* accel box #1 y gain */
	JZACCGAIN1 = 0.0;		/* accel box #1 z gain */
	JXACCGAIN2 = 0.0;		/* accel box #2 x gain */
	JZACCGAIN3 = 0.0;		/* accel box #3 z gain */
}

/*--------------------------------
CD MT ON REQUEST FROM HOST PROGRAM
---------------------------------*/

if( (MTONREQ)&&(MTSTATUS==OFF) )
{
	MTSTATUS = STOP;     /* ON but stopped */
}
else if ( !MTONREQ )
{
	MTSTATUS = OFF;
	return;              /* do not execute if utility not ON */
}


/*-----------------------------
CD MTOOO DFC PAGE DRIVE REQUEST
-----------------------------*/
if ( ( MTDRIVE ) && ( !m_pdrive )  )
{
	MTDRIVEREQ = TRUE; /* drive is requested */
/**
	strpack(&MTMESSAGE[MTMSPOINT][0],"** DRIVING MOTION **");
	MTMSPOINT++;
	if( MTMSPOINT==5)MTMSPOINT=0;
**/
}

if ( ( !MTDRIVE ) && ( m_pdrive )  ) MTSTOPREQ = TRUE;  /* stop is requested */

m_pdrive = MTDRIVE;

/*---------------
CD MT DRIVE LOGIC
----------------*/

if ( !M2L_MNON )MTSTOPREQ = TRUE;     /* STOP generator if motion goes OFF */

if( MTDRIVEREQ&&(MTSTATUS==STOP) )
{
	MTSTATUS = DRIVE;
	MTDRIVE  = TRUE;
	m_pdrive = TRUE;
}

MTDRIVEREQ = FALSE;

if(MTSTOPREQ)
{
	if ( !MTRESTART )
	{
/**
		strpack(&MTMESSAGE[MTMSPOINT][0],"MOTION STOPPED");
		MTMSPOINT++;
		if( MTMSPOINT==5)MTMSPOINT=0;
**/
	}

	MTSTATUS = WASHOUT;
	MTDRIVE = FALSE;
	m_pdrive = FALSE;

	if (  ( abs(LONG_MTPOS)<m_washlim) &&     /* wait for washout to be over */
	      ( abs(LAT_MTPOS)<m_washlim) &&
	      ( abs(HEAV_MTPOS)<m_washlim) &&
   	      ( abs(PICH_MTPOS)<m_washlim) &&
	      ( abs(ROLL_MTPOS)<m_washlim) &&
	      ( abs(YAW_MTPOS)<m_washlim)  &&
	      ( abs(J1MTJP)<m_washlim) &&
	      ( abs(J2MTJP)<m_washlim) &&
	      ( abs(J3MTJP)<m_washlim) &&
	      ( abs(J4MTJP)<m_washlim) &&
	      ( abs(J5MTJP)<m_washlim) &&
	      ( abs(J6MTJP)<m_washlim)       )
	{
	     MTSTATUS = STOP;
             MTSTOPREQ = FALSE;
        }
}


/*--------------------
WAVE AND AXIS CHANGE
--------------------*/

if( MTSTATUS==STOP)    		/* wait until motion is washed out */
{
	MTWAVE = MTWAVEREQ;
	MTAXIS = MTAXISREQ;

	if ( MTRESTART )        /* if motion was driving, restart it */
	{
		MTDRIVEREQ=TRUE;
		MTRESTART = FALSE;
	}
}
/*
*	change in requested wave or axis while driven :
*	stop motion for a moment.
*	It will restart itself after change has been processed
*/
else if (   ( (MTAXISREQ!=MTAXIS)||(MTWAVEREQ!=MTWAVE) )&&
	        (MTSTATUS==DRIVE)&&(MTSTOPREQ==FALSE) )
{
	MTSTOPREQ = TRUE;
	MTRESTART = TRUE;
}

/*--------------------------------------------------------------------
*	Special logic for manual page
*	LIMIT MTCHANANAL AND MTRSLMODE
*       Set up Bodeplot parameters if selected from manual
---------------------------------------------------------------------*/

if ( MTMANUALON )
{
	if ( MTCHANANAL>=SINGLE)     MTCHANANAL = SINGLE;
	if ( MTRSLTMODE<=SCROLL_UP ) MTRSLTMODE = SCROLL_UP;
	if ( MTRSLTMODE==SCROLL_MAX) MTRSLTMODE = SCROLL_UP;
	if ( MTRSLTMODE>=BODEPLOT  ) MTRSLTMODE = BODEPLOT;
	MTOUTGAIN = 1.0;
	MTPHASEOFS = 0.0;
	MTGAINHIDE = 1.0;
	MTPHASHIDE = 1.0;
	if ( MTRSLTMODE==BODEPLOT )
	{
	   MTANALYS = FRA;
	   MTDRIVMODE = TABLE;
	   MTCHANANAL = SINGLE;
 	   MRNCURVE =2;                 /* number of curve*/
	   MTNPOINT = MTLENTABLE;	/* size of the curve [point] */
	   MRSAMFRQ = 1;		/* sampling rate, subband of sys_freq,	*/
	}
}
/*
* 	Reset tune key. Do not allow disabling the limits on MT generators
*	set-up, unless on manual page on running accel performance tests
*/
else
{
	MTTUNE_KEY = 0;
	if ( MTTEST_KEY != ACCELPERF1 ) MTNOLIMIT = FALSE;
}

/*-----------------------------------------------------------------------------
*	Do not allow selection for type of analysis if MTCHANANAL set to NONE
------------------------------------------------------------------------------*/
if ( MTCHANANAL==NONE)
{
	MTANALYS = NOANAL;
}

/*--------------------------
*	Rotational axis flag
----------------------------*/
if ( (MTAXIS==AXISROLL)||(MTAXIS==AXISPICH)||(MTAXIS==AXISYAW) )
{
	m_linaxis = FALSE;
}
else
{
	m_linaxis = TRUE;
}
/* ------------------------------------------------------
CD MT000 AMPLITUDE SELECTION LOGIC AND AMPLITUDE LIMITING
C  -----------------------------------------------------*/

/*
* 	Limit frequency
*/
	MTFREQ = limit ( MTFREQ, .01, 50. ) ;

/*
*	ACCEL SELECTION MODE not allow in triangle and square modes
*/
if (  (MTWAVE==TRIANGLE)||(MTWAVE==SQUARE) )
{
 	MTAMPSEL=DISPL;
}

/* 	-------------------------------
*	use DISPLACEMENT  as INPUT ampl
*	-------------------------------*/
if (MTAMPSEL==DISPL)
{
       	if ( !MTNOLIMIT)
	{
		/*
		* 	Amplitude limited by max displacement
		*/
		if ( m_linaxis)
		{
			MTAMPDISP = limit ( MTAMPDISP , 0.0, m_poslimit );
	        }
		else
		{
			MTAMPDISP = limit ( MTAMPDISP , 0.0, m_anglimit );
		}
	}
	if (MTWAVE==SINE)
	{
		m_omega = m_twopi*MTFREQ;
		m_invomega = 1. / max(.01,m_omega );

		/*
		*	IN TABLE MODES, check for change in input displacement
		* 	if so, memorize last entry as demanded displacement
		*	and keep using it for the entire frq table
		*	Otherwise, accept new amplitude.
		*/
   		if( (abs(MTAMPDISP-m_ampdisp)>.1) && (MTDRIVMODE==TABLE) )
		{
			m_ampdispd = MTAMPDISP;
		}
		else if (MTDRIVMODE==CONTINU)
		{
			m_ampdispd = MTAMPDISP;
		}

	       	if ( !MTNOLIMIT)
		{
			/*
			* 	Amplitude limited by max velocity
			*/
			if ( m_linaxis)
			{
			      m_limit1 = min(m_ampdispd*m_omega, m_vellimit)*m_invomega;
	                }
			else
			{
			      m_limit1 = min(m_ampdispd*m_omega, m_angvlim)*m_invomega;
			}
			/*
			* 	Amplitude limited by max acceleration
			*/
			if ( m_linaxis)
			{
				m_limit2 = min(m_ampdispd*m_omega*m_omega,m_acclimit)
				    *m_invomega*m_invomega;
			}
			else
			{
				m_limit2 = min(m_ampdispd*m_omega*m_omega,m_angalim)
				    *m_invomega*m_invomega;
			}
			/*
			*	limit position command
			*/
	                MTAMPDISP = min ( m_ampdispd , m_limit1 );

	   		MTAMPDISP = min ( MTAMPDISP , m_limit2 );
		}

		/*
		*	update value of previous MTAMPDISP
		*/
		m_ampdisp = MTAMPDISP ;

 		/*
		* force value of MTAMPACC [g's] or [rad/sec**2]
		*/
		if ( m_linaxis)
		{
			MTAMPACC=MTAMP*(TWOPI*MTFREQ)*(TWOPI*MTFREQ)*GINCHINV;
		}
		else
		{
			MTAMPACC=MTAMP*(TWOPI*MTFREQ)*(TWOPI*MTFREQ);
		}
	}

	/*
	*	NOT in sine wave mode : allow no entry for acceleration amplitude
	*/
	else
	{
		MTAMPACC = 0.0;

	}	/* end of if (MTWAVE==SINE) */

	/*
	*	Set MTAMPREQ
	*/
     	if ( m_linaxis)
	{
	        MTAMPREQ = MTAMPDISP;
	}
	else
	{
	        MTAMPREQ = MTAMPDISP;
        }
}		/* end of if (MTAMPSEL==DISPL) */

/*-----------------------------------------------------------
*	use ACCELERATION as INPUT ampl ( with sinewave only )
------------------------------------------------------------*/

if (MTAMPSEL==ACCEL)
{
       	if ( !MTNOLIMIT)
	{
		/*
		* 	Acceleration limited to 0.5 g's or 120 deg/s**2
		*/
		if ( m_linaxis)
		{
			MTAMPACC = limit ( MTAMPACC, 0.0, .5 );
		}
		else
		{
			MTAMPACC = limit ( MTAMPACC, 0.0, 120. );
		}
	}
	if (MTWAVE==SINE)
	{
		m_omega = m_twopi*MTFREQ;
		m_invomega = 1. / max(.01,m_omega );
		/*
		*	IN TABLE MODES, check for change in input acceleration
		* 	if so, memorize last entry as demanded acceleration
		*	and keep using it for the entire frq table
		*/
   		if( (abs(MTAMPACC-m_ampacc)>.001) && (MTDRIVMODE==TABLE) )
		{
			m_ampaccd = MTAMPACC;
		}
		else if (MTDRIVMODE==CONTINU)
		{
			m_ampaccd = MTAMPACC;
		}

	       	if ( !MTNOLIMIT)
		{
	 		/*
			* 	Acceleration limited by max velocity
			*/
			if ( m_linaxis)
			{
				m_limit1 = min(m_ampaccd*386.4*m_invomega, m_vellimit)
                                                      *m_omega/386.4 ;
                	}
			else
			{
				m_limit1 = min(m_ampaccd*386.4*m_invomega, m_angvlim)
                                                      *m_omega/386.4 ;
			}

			/*
			* 	Acceleration limited by max displacement
			*/
			if ( m_linaxis)
			{
				m_limit2=min(m_ampaccd*386.4*m_invomega*m_invomega,m_poslimit)
					*m_omega*m_omega/386.4 ;
			}
			else
			{
				m_limit2=min(m_ampaccd*386.4*m_invomega*m_invomega,m_anglimit)
					*m_omega*m_omega/386.4 ;
			}

			/*
			*	limit acceleration command
			*/
	      		MTAMPACC = min ( m_ampaccd, m_limit1 );

  	 		MTAMPACC = min ( MTAMPACC , m_limit2 );
		}

		/*
		*	update value of previous MTAMPACC
		*/
		m_ampacc = MTAMPACC ;

		/*
		*	Compute requested amplitude from commanded acceleration
		* 	Force value of MTAMPDISP based on actual amplitude
		*/

		if ( m_linaxis)
		{
			MTAMPREQ=MTAMPACC*GINCH/((TWOPI*MTFREQ)*(TWOPI*MTFREQ));

			MTAMPDISP = min (MTAMP, m_poslimit);
		}
        	else
		{
			MTAMPREQ = MTAMPACC/( (TWOPI*MTFREQ)*(TWOPI*MTFREQ) );

			MTAMPDISP = min (MTAMP, m_anglimit);
		}

	}	/* 	if (MTWAVE==SINE) */

}	/* end of if (MTAMPSEL==ACCEL) */

/* ---------------------------
CD MT000 CLEAR FREQUENCY TABLE
C  --------------------------*/

if(MTCLTABREQ)
{
	for (i = 0; i < 50; i++)
	{
		MTFRQTABLE[i] = 0.0;
	}

	MTLENTABLE = 0;

	MTCLTABREQ = FALSE;
}

/* ----------------------------------------------
CD MT000 FREQUENCY TABLE AUTO SET-UP: LARGE TABLE
C  ----------------------------------------------*/

if(MTAUTOFREQ)
{
	MTFRQTABLE[0]=0.1;                 /* table from .1 to 15 Hz */

	for (i = 1; i <= 17; i++)MTFRQTABLE[i]=MTFRQTABLE[i-1]+0.05;

        MTFRQTABLE[18]=1.0;

	for (i = 19; i <= 48; i++)MTFRQTABLE[i]=MTFRQTABLE[i-1]+0.5;

	MTLENTABLE = 49;

	MTAUTOFREQ = FALSE;
}

/* ----------------------------------------------
CD MT000 FREQUENCY TABLE AUTO SET-UP: SMALL TABLE
C  ----------------------------------------------*/

if(MTAUTOFRSM)
{
	MTFRQTABLE[0]=0.1;                 /* table from .1 to 5 Hz */

	for (i = 1; i <= 17; i++)MTFRQTABLE[i]=MTFRQTABLE[i-1]+0.05;

        MTFRQTABLE[18]=1.0;

	for (i = 19; i <= 28; i++)MTFRQTABLE[i]=MTFRQTABLE[i-1]+0.5;

	MTLENTABLE = 29;

	MTAUTOFRSM = FALSE;
}

/* -------------------------------
CD MT000 FREQUENCY TABLE SELECTION
C  ------------------------------*/
/*
In single frequency mode (MTDRIVMODE==CONTINUous), motion drives endlessly
unless stopped by user. Analysis may or may not be enabled.
User writes directly into MTFREQREQ from MTP page.

In FREQUENCY TABLE MODE (MTDRIVMODE==TABLE), each frequency from the table is
used in turn. Frequency change is triggered by the analysis program after new
result, or by the plot routine, etc.
The currently active frequency from the table is deposited into MTFREQREQ
and is shown on the MTP page. When all the freq are used, motion is stopped .
*/

/*
*	FREQUENCY TABLE MODE
*/

if ( MTDRIVMODE == TABLE )
{
	if( MTFRQCHANGE )  /* result generated for this frq */
	{
		if( MTFRQPOINT==(MTLENTABLE-1)  )/* end of table stop */
		{
			MTSTOPREQ = TRUE;
			MTFRQPOINT=0;            /* restart at begin of table */
		}
		else
		{
			MTFRQPOINT++;            /* get next frq from table */
		}

		MTFRQCHANGE = FALSE;
	}
	MTFREQREQ = MTFRQTABLE[MTFRQPOINT];

}/* end of if ( MTDRIVMODE==TABLE )

/*
* -----------------------------------------------------
* Frequency notch filter for visual resonance protection
* -----------------------------------------------------
*/
if ( ( MTFREQREQ > MVRESNF1 )&&( MTFREQREQ < MVRESNF2 ) )
{
        MTFREQREQ = MVRESNF1;
}

/*----------------------------
AMPLITUDE AND FREQUENCY CHANGE
----------------------------*/
/*
*	ramp to requested values
*/
if(MTSTATUS==DRIVE)
{
 	m_sp0 = m_ampfad*YITIM;
	MTAMP  += limit(MTAMPREQ-MTAMP,-m_sp0,m_sp0);

	m_sp1 = m_freqfad*YITIM;
	MTFREQ += limit(MTFREQREQ-MTFREQ,-m_sp1,m_sp1);
}
/*
*	allow immediat change in amp and freq
*/
else if (MTSTATUS<DRIVE)
{
	    MTAMP  = MTAMPREQ ;
	    MTFREQ = MTFREQREQ ;
	    MTFRQPOINT = 0;		/* reset freq table pointer */
}

/*--------------------
WAVE AND AXIS CHANGE
--------------------*/

if( MTSTATUS==STOP)    		/* wait until motion is washed out */
{
	MTWAVE = MTWAVEREQ;
	MTAXIS = MTAXISREQ;

	if ( MTRESTART )        /* if motion was driving, restart it */
	{
		MTDRIVEREQ=TRUE;
		MTRESTART = FALSE;
	}
}
/*
*	change in requested wave or axis while driven :
*	stop motion for a moment.
*	It will restart itself after change has been processed
*/
else if (   ( (MTAXISREQ!=MTAXIS)||(MTWAVEREQ!=MTWAVE) )&&
	        (MTSTATUS==DRIVE)&&(MTSTOPREQ==FALSE) )
{
	MTSTOPREQ = TRUE;
	MTRESTART = TRUE;
}

}		/* end of file MTLOGIC */
/*****************************************************************************

  'Title                MOTION TEST DRIVE PROGRAM
  'Module_ID            MT
  'Entry_point          MT
  'Documentation
  'Customer             QANTAS
  'Application          Tuning and testing of motion system
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous proces
*****************************************************************************

  'Revision_history

12 feb 91  New scheme for motion test programs
25 nov     Reset logic brought over from MTANAL to be more general and to
           apply to all concerned programs.

  'References

*/

void mtanalyse();
void mtrecord();

void mt()
{

/*
C
C      LOCAL VARIABLES
C
*/

static int      i,
		m_analsub = 0,	/* analysis pgm subbanding counter */
		m_sub = 1,	/* subbanding factor */
	        m_rstim =500,	/* time delay before reset is activated */
	        m_rstimer = 0,  /* reset timer  */
        	m_analrst = 0,  /* internal reset flag */
		m_pstatus = 0,		/* previous MTSTATUS */
		m_pdrive = 0,		/* previous MTDRIVE */
		m_panalys = 0,		/* previous MTANALYS */
		m_pin_key,              /* previous iter MTIN_KEY */
		m_pout_key,             /* previous iter MTOUT_KEY */
		m_pautotune,		/* previous MTAUTOTUNE_KEY */
		m_sign;		 	/* sign of wave */

static float   	m_sp0,
	       	m_sp1,
		m_fadein=0,       	/* fadein factor when start driving */
		m_fadestep = .0005,	/* fade in step */
		m_wait=0.,
	    	m_waitlim=3.0,		/* waiting time before start recording*/
	       	m_pfreq,		/* local previous frequency */
	       	m_pamp,			/* local previous amp  */
	       	m_pampmin=.01,		/* local previous amp  */
	       	m_kwash = .998,          /* washout constant */
	       	m_slope,  	 	/* slope of triangle wave */
		m_maxper = 10,           /* maximum num of period before reset*/
	       	m_washmin = .001,  	/* min motion displacement for washout */
		m_vellag = .040;        /* velocity lag gain: .002/TAU */


/*
  	FULL PLATFORM OR SINGLE ACCELEROMETER BOX

	GEOMETRY FACTOR
*/

if ( MTACCEL == NOBOX )
{
	JXACCEL = 0.;
	JYACCEL = 0.;
	JZACCEL = 0.;
}
else if ( MTACCEL == SINGLE_BOX_1 )
{
	JXACCEL = JXACC1 ;
	JYACCEL = JYACC1 ;
	JZACCEL = JZACC1 ;
}
else if ( MTACCEL == SINGLE_BOX_2 )
{
	JXACCEL = JXACC2 ;
	JYACCEL = JYACC2 ;
	JZACCEL = JZACC2 ;
}
else if ( MTACCEL == FULL_PLAT )
{
	JXACCEL = JXACC1 ;   /********temporary ********/
	JYACCEL = JYACC1 ;
	JZACCEL = JZACC1 ;
}

/*-------------------------------------------------------------
CD PROGRAM SKIP IF NOT GETTING THE ON REQUEST FROM HOST PROGRAM
--------------------------------------------------------------*/

if (	MTSTATUS == OFF ) return;
 
             /* do not execute if utility not ON */



/*-----------------------
TIME BASE FOR GENERATOR
-----------------------*/

	MTTIME += YITIM;
	if (MTTIME>1000000.)MTTIME=0.0;

	MTTHETA += TWOPI*MTFREQ*YITIM;
	if (MTTHETA>6283.)MTTHETA = 0.0;


/*--------------
WAVE GENERATOR
--------------*/

	if( MTSTATUS == DRIVE)
	{
		switch(MTWAVE)
		{
		   case(SINE):

	             MTGEN = MTAMP * sin(MTTHETA);
		     break;

		   case(TRIANGLE):
		     m_slope = 4*MTFREQ*MTAMP;
		     MTGEN +=  m_sign*m_slope*YITIM;
		     if (abs(MTGEN)>MTAMP)m_sign = - m_sign;
		     break;

		   case(SQUARE):
                     if(MTTIME> 1/(MTFREQ+.001) )
		     {
		        MTTIME = 0.0;
      	                m_sign = -m_sign;
      	                MTGEN = m_sign * MTAMP;
		     }
		     break;

	        }             /* end of switch(MTWAVE)        */

		/*
		* 	Fade in the generator signal as the motion
		*	goes into DRIVE mode
		*/

		m_fadein = min ( m_fadein + m_fadestep , 1.0 );

		MTGEN *= m_fadein;


	}       /* end of if(MTSTATUS==DRIVE)   */


	else                   /* reset generators when not driven */
	{
	  m_fadein = 0.0;
	  MTTIME     = 0.0;
	  MTTHETA    = 0.0;
          MTGEN      = 0.0;
	  m_sign     = 1;

	}	/* end of if( MTSTATUS == DRIVE) */



/*--------------
AXIS SELECTION
--------------*/

	if(MTSTATUS==DRIVE)
	{
	   	switch(MTAXIS)
	  	{
		    case(AXISLONG):
	 	      LONG_MTPOS = MTGEN;
		      break;

		     case(AXISLAT):
	 	       LAT_MTPOS = MTGEN;
		       break;

		     case(AXISHEAV):
	 	       HEAV_MTPOS = MTGEN;
		     break;

		     case(AXISPICH):
	 	       PICH_MTPOS = MTGEN;
		     break;

		     case(AXISROLL):
	 	       ROLL_MTPOS = MTGEN;
		     break;

		     case(AXISYAW):
	 	       YAW_MTPOS = MTGEN;
		     break;

		     case(AXISJ1):
	 	        J1MTJP = MTGEN;
		     break;

		     case(AXISJ2):
	 	        J2MTJP = MTGEN;
		     break;

		     case(AXISJ3):
	 	        J3MTJP = MTGEN;
		     break;

		     case(AXISJ4):
	 	        J4MTJP = MTGEN;
		     break;

		     case(AXISJ5):
	 	        J5MTJP = MTGEN;
		     break;

		     case(AXISJ6):
	 	        J6MTJP = MTGEN;
		     break;


	  } 	   	    /* end of switch(MTAXIS) */


	}                   /* end of if mtstatus=drive */

/*-------------------------------
* 	WASHOUT MOTION TO NEUTRAL
*------------------------------*/
/* washout commanded positions to zero */
	else
	{
		LONG_MTPOS *= m_kwash;
		LAT_MTPOS  *= m_kwash;
		HEAV_MTPOS *= m_kwash;
		PICH_MTPOS *= m_kwash;
		ROLL_MTPOS *= m_kwash;
		YAW_MTPOS  *= m_kwash;

		J1MTJP *= m_kwash;
		J2MTJP *= m_kwash;
		J3MTJP *= m_kwash;
		J4MTJP *= m_kwash;
		J5MTJP *= m_kwash;
		J6MTJP *= m_kwash;

		if( abs(LONG_MTPOS) < m_washmin )LONG_MTPOS = 0.0;
		if( abs(LAT_MTPOS)  < m_washmin )LAT_MTPOS = 0.0;
		if( abs(HEAV_MTPOS) < m_washmin )HEAV_MTPOS = 0.0;
		if( abs(PICH_MTPOS) < m_washmin )PICH_MTPOS = 0.0;
		if( abs(ROLL_MTPOS) < m_washmin )ROLL_MTPOS = 0.0;
		if( abs(YAW_MTPOS)  < m_washmin )YAW_MTPOS = 0.0;

		if( abs(J1MTJP) < m_washmin )J1MTJP = 0.0;
		if( abs(J2MTJP) < m_washmin )J2MTJP = 0.0;
		if( abs(J3MTJP) < m_washmin )J3MTJP = 0.0;
		if( abs(J4MTJP) < m_washmin )J4MTJP = 0.0;
		if( abs(J5MTJP) < m_washmin )J5MTJP = 0.0;
		if( abs(J6MTJP) < m_washmin )J6MTJP = 0.0;

	}	/*   end of if(MTSTATUS==DRIVE) */

/*	-----------------------------------------
CD      LAG velocity signal for motion test plots
*	-----------------------------------------
*/

	J1VACL = J1VACL + m_vellag * (J1VAC - J1VACL);
	J2VACL = J2VACL + m_vellag * (J2VAC - J2VACL);
	J3VACL = J3VACL + m_vellag * (J3VAC - J3VACL);
	J4VACL = J4VACL + m_vellag * (J4VAC - J4VACL);
	J5VACL = J5VACL + m_vellag * (J5VAC - J5VACL);
	J6VACL = J6VACL + m_vellag * (J6VAC - J6VACL);


/* -----------------
CD RESET LOGIC
C -----------------
C
C
C	This part erases the scroll up results display and resets MTRECORD
*/
if ( (    MTIN_KEY != m_pin_key )||(MTOUT_KEY != m_pout_key ) ||
     (    MTAUTOTUNE_KEY != m_pautotune                     ) ||
     (   (MTSTATUS == DRIVE) && (m_pstatus  != DRIVE)       ) ||
     (   (abs(MTAMP-m_pamp)>m_pampmin)&&(MTDRIVMODE==CONTINU))||
     (   (MTFREQ != m_pfreq )&&(MTDRIVMODE==CONTINU)        ) ||
     (    MTANALYS  != m_panalys                            )       )
{

	MRRESET = TRUE;

/* 	reset C30-HOST buffers if test has changed or motion was stopped */

	J1MTRPOINT = 0;  	/* next new result should be at start of array */
	J2MTRPOINT = 0;
	J3MTRPOINT = 0;
	J4MTRPOINT = 0;
	J5MTRPOINT = 0;
	J6MTRPOINT = 0;
	JXMTRPOINT = 0;

	J1NUMRSULT = 0;
	J2NUMRSULT = 0;
	J3NUMRSULT = 0;
	J4NUMRSULT = 0;
	J5NUMRSULT = 0;
	J6NUMRSULT = 0;
	JXNUMRSULT = 0;

	for (i = 0; i < 10; i++)
	{
		J1MTRSFLAG[i] = FALSE;
		J2MTRSFLAG[i] = FALSE;
		J3MTRSFLAG[i] = FALSE;
		J4MTRSFLAG[i] = FALSE;
		J5MTRSFLAG[i] = FALSE;
		J6MTRSFLAG[i] = FALSE;
		JXMTRSFLAG[i] = FALSE;
	}
}


m_pstatus = MTSTATUS;

/*
* 	This part RESETS ANALYSIS if any change occurs
* 	to drive parameters ( freq, amp )or selected test.
*       Will also reset analysis if frequency change was requested
*       but has not yet resulted in a frequency change.
*	Resets if drive signal is being faded at start.
*       Also, reset if analysis times out.
*/
if (  ( MTFRQCHANGE )          ||
      ( MTFREQ != m_pfreq )    || ( abs(MTAMP-m_pamp)>m_pampmin ) ||
      ( MTSTATUS != DRIVE )    || (MTANALYS  != m_panalys  ) ||
      ( MTIN_KEY != m_pin_key) || (MTOUT_KEY != m_pout_key ) ||
      ( MTAUTOTUNE_KEY != m_pautotune)      ||
      ( m_fadein != 1.0              )      ||
      (	J1MTELTIME>m_maxper*MTPERIOD[JACK1])||
      ( J2MTELTIME>m_maxper*MTPERIOD[JACK2])||
      (	J3MTELTIME>m_maxper*MTPERIOD[JACK3])||
      ( J4MTELTIME>m_maxper*MTPERIOD[JACK4])||
      (	J5MTELTIME>m_maxper*MTPERIOD[JACK5])||
      ( J6MTELTIME>m_maxper*MTPERIOD[JACK6])||
      ( JXMTELTIME>m_maxper*MTPERIOD[JACKX])  	  )
{
        m_analrst = TRUE;

	m_pfreq = MTFREQ;
	m_pamp  = MTAMP ;
	m_panalys = MTANALYS;
	m_pin_key = MTIN_KEY;
	m_pout_key = MTOUT_KEY;
        m_pautotune = MTAUTOTUNE_KEY;
}
/*
* 	Reset for a few iterations to allow motion to stabilize
*/
if ( m_analrst )
{
	m_rstimer++;
 	MTANALRST = TRUE;
}
if ( m_rstimer > m_rstim )
{
        m_rstimer = 0;
        m_analrst = FALSE;
}

/*--------------------------------------
CALL TO MTANALYSE OR MTRECORD IF ENABLED
----------------------------------------*/

if ( MTSTATUS==DRIVE)
{
	if( (MTANALYS==FRA)||(MTANALYS==RMS) )
	{
		/*
 		* 	subbanding analysis
		*/
		m_analsub = m_analsub + 1;
		if ( m_analsub >= m_sub )
		{
			m_analsub = 0;
			mtanalyse();
		}
	}
	/*
	* 	Wait for motion to reach steady state for
	* 	tests that plot only
	*/
        if(MTRSLTMODE==GRAPH)
        {
		m_wait += YITIM;
		if ( m_wait > m_waitlim) mtrecord();
        }
	/*
	* 	Start recording right away for Bode plot
	*/
        else if (MTRSLTMODE==BODEPLOT)
	{
		mtrecord();
	}
}				/* end of if ( MTSTATUS==DRIVE) */

/*
* 	Reset wait counter for call to record
*/
else
{
	m_wait = 0.0;
        if(MTRSLTMODE==SCROLL_MAX)MTGAINMAX=-100.0;
}				/* end of if ( MTSTATUS==DRIVE) */


/*------------
C END OF FILE
-------------*/

}    			/* end of file  MT */


/*****************************************************************************

  'Title                MOTION TEST ANALYSIS PROGRAM for 6 jacks simultaneously
  'Module_ID            MTANALYSE
  'Entry_point          MTANALYSE
  'Documentation
  'Customer             QANTAS
  'Application          Analysis of motion system response
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history
   15 oct   six jack version using macro
   25 nov   reset logic now in MT and more global
   29 nov   subanding macro
   12 feb   Nex scheme for motion test: MTANAL calls MTFRA and MTRMS

  'References


*/

extern float mtselect(int,int);

/*
C
C      LOCAL VARIABLES
C
*/

static int 	m_firstavg[7],	  	/* first pass flag for average gain comp. */
		m_subcount = 0,         /* subbanding control */
		m_finish[7]={0,0,0,0,0,0,0},
					/* test finished for that jack */
		m_frqchange[7]={0,0,0,0,0,0,0},
					/* frq change request for that jack*/
		m_numavg[7];		/* number of gain used to compute average */

static float    m_subbfactor = 1.,	/* subbanding time factor  was 1.*/
                m_miniter=3.,		/* min number of periods before anal*/
		m_wt[7],		/* product of w and iteration time */
		m_xsin[7],		/* sin of m_wt */
		m_xcos[7],		/* cos of m_wt */
		m_isq[7],	        /* square of input signal */
		m_avgainsq[7],		/* average gain square */
                m_tani[7],		/* tan of m_xi , m_yi  */
		m_tano[7],		/* tan of m_xo , m_yo */
		m_dt[7],		/* omega * delta time */
		m_xi[7],		/*  */
		m_yi[7],		/* */
		m_xo[7],		/* */
		m_yo[7],		/* */
		m_freq[7],		/* local drive frequency */
		m_inputmax[7],		/* MTRMS maximum value of input signal*/
		m_inputmin[7],		/* MTRMS minimum value of input signal*/
		m_sumavg[7];		/* MTRMS sum of gain to compute average */

void mtanalyse()
{


/* ----------------------------------
CD MA010  RESET ALL SIX JACK ANALYSIS
C  ---------------------------------*/


if (MTANALRST)
{
	J1MTRESET = TRUE;
	J2MTRESET = TRUE;
	J3MTRESET = TRUE;
	J4MTRESET = TRUE;
	J5MTRESET = TRUE;
	J6MTRESET = TRUE;
	JXMTRESET = TRUE;
        MTANALRST = FALSE;
}

/*
*	----------------------------------------------------------------------
*	SINGLE CHANNEL : call FRA or RMS macro for single channel being tested
*	----------------------------------------------------------------------
*/
if ( MTCHANANAL == SINGLE )
{
	#undef CHAN
	#define CHAN JACKX

#include "mot_analyse.h"

	if ( MTANALYS==FRA )
	{
#include "mot_fra.mac"
	}
	else if ( MTANALYS==RMS )
	{
#include "mot_rms.mac"
	}

/*	-----------------------------------------------------------------
*	In SINGLE channel mode, as soon as single channel test converges,
*	ask for a freq change.
*	-----------------------------------------------------------------
*/

	if ( m_frqchange[JACKX] )
	{
 		MTFRQCHANGE = TRUE;
		m_frqchange[JACKX] = FALSE;
	}

/*
*	-----------------------------------------------------------------
*	In SINGLE channel mode, as soon as single channel test is over,
*       signal DFC utility to display results
*	-----------------------------------------------------------------
*/
	if ( m_finish[JACKX])
	{
		MTRSFINISH = TRUE;
	}

}	/* end of if ( MTCHANANAL==SINGLE) */

/*
*	-----------------------------------------------------------------------------
*	ALL CHANNEL ANALYSIS : call FRA or RMS macro for ALL jacks
*	-----------------------------------------------------------------------------
*/

if ( MTCHANANAL==ALL)
{
/*
*	---------------------------------
*	call FRA or RMS macro for jack #1
*	---------------------------------
*/
	#undef CHAN
	#define CHAN JACK1

#include "mot_analyse.h"
	if ( MTANALYS==FRA )
	{
#include "mot_fra.mac"
	}
	else if ( MTANALYS==RMS )
	{
#include "mot_rms.mac"
	}

/*
*	---------------------------------
*	call FRA or RMS macro for jack #2
*	---------------------------------
*/
	#undef CHAN
	#define CHAN JACK2

#include "mot_analyse.h"
	if ( MTANALYS==FRA )
	{
#include "mot_fra.mac"
	}
	else if ( MTANALYS==RMS )
	{
#include "mot_rms.mac"
	}

/*
*	---------------------------------
*	call FRA or RMS macro for jack #3
*	---------------------------------
*/
	#undef CHAN
	#define CHAN JACK3

#include "mot_analyse.h"
	if ( MTANALYS==FRA )
	{
#include "mot_fra.mac"
	}
	else if ( MTANALYS==RMS )
	{
#include "mot_rms.mac"
	}

/*
*	---------------------------------
*	call FRA or RMS macro for jack #4
*	---------------------------------
*/
	#undef CHAN
	#define CHAN JACK4

#include "mot_analyse.h"
	if ( MTANALYS==FRA )
	{
#include "mot_fra.mac"
	}
	else if ( MTANALYS==RMS )
	{
#include "mot_rms.mac"
	}
/*
*	---------------------------------
*	call FRA or RMS macro for jack #5
*	---------------------------------
*/
	#undef CHAN
	#define CHAN JACK5

#include "mot_analyse.h"
	if ( MTANALYS==FRA )
	{
#include "mot_fra.mac"
	}
	else if ( MTANALYS==RMS )
	{
#include "mot_rms.mac"
	}

/*
*	---------------------------------
*	call FRA or RMS macro for jack #6
*	---------------------------------
*/
	#undef CHAN
        #define CHAN JACK6

#include "mot_analyse.h"
	if ( MTANALYS==FRA )
	{
#include "mot_fra.mac"
	}
	else if ( MTANALYS==RMS )
	{
#include "mot_rms.mac"
	}


/*	---------------------------------------------------------------
*	In ALL channel mode and frequency table mode, wait until test
*	converges for all channels before asking for a freq change.
*	---------------------------------------------------------------
*/
	if ( ( MTDRIVMODE==TABLE )&&( MTCHANANAL==ALL)&&
	     m_frqchange[0]&&m_frqchange[1]&&m_frqchange[2]&&
     	     m_frqchange[3]&&m_frqchange[4]&&m_frqchange[5]    )
	{
 		MTFRQCHANGE = TRUE;
		m_frqchange[0] = FALSE;
		m_frqchange[1] = FALSE;
		m_frqchange[2] = FALSE;
		m_frqchange[3] = FALSE;
		m_frqchange[4] = FALSE;
		m_frqchange[5] = FALSE;
	}


/*	---------------------------------------------------------------
	In ALL channel mode,, wait until test over for all jacks before
*       signaling  DFC utility to display results.
*	---------------------------------------------------------------
*/
	if( m_finish[0]&&m_finish[1]&&m_finish[2]&&
            m_finish[3]&&m_finish[4]&&m_finish[5]     )
	{
		MTRSFINISH = TRUE;
	}


}	/* end of if ( MTDRIVMODE == TABLE) */

/*
C END OF FILE
*/

}    			/* end of file MTANALYSE */


/*****************************************************************************

  'Title                MOTION TEST RECORD PROGRAM
  'Module_ID            MTRECORD
  'Entry_point          mtrecord()
  'Documentation
  'Customer             QANTAS
  'Application          recording of motion signal for testing
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history

  'References

*/


extern float MTBUFFERX[600];	/*labels in MOTDATA.C, and not in MOT_EXT.H */
extern float MTBUFFERY[4096];
extern float MRPLOTTIME;


static int      m_plotfirst;

static float  	m_meter = 0.025,
		m_phlimneg = -179.,
		m_phlimpos = 90.,
		m_sp0;


void mtrecord()
{

/*  --------------------------------
CD  MR000 RESET
C   -------------------------------*/

if ( MRRESET)
{
	MRCOUNT = 0;			/* point counter */
	MRPLOTTIME = 0.0;
	MRSAMSB = 0;
	MRRESET = FALSE;
}

/*  ---------------------------------------------------------
CD  MR000 FILL DATA BUFFER WITH SUBBANDED SAMPLING OF SIGNALS
C   --------------------------------------------------------*/


if (MRCOUNT<MTNPOINT )			/* loop for all the points */
{
	MRSAMSB++;

	if( MRSAMSB == MRSAMFRQ)	/* sampling subanding control */
	{
		MRSAMSB = 0;

		if (MTTEST_KEY==TABUMP)
		{
			if(SITE<=HTF)
			{
				MTBUFFERY[MRCOUNT] = J1XAC ;   /* HTF:POSITION*/
			}
			else
			{
				MTBUFFERY[MRCOUNT] = JZACCEL ;     /* accel signal */
			}

			MTBUFFERX[MRCOUNT]= MRPLOTTIME;    /* time base */

			MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
                }

		else if (MTTEST_KEY==LEGBAL)
		{
			MTBUFFERY[ MRCOUNT ]            = J1VACL*m_meter;

			MTBUFFERY[ MRCOUNT+MTNPOINT ]   = J2VACL*m_meter;

			MTBUFFERY[ MRCOUNT+2*MTNPOINT ] = J3VACL*m_meter;

			MTBUFFERY[ MRCOUNT+3*MTNPOINT ] = J4VACL*m_meter;

			MTBUFFERY[ MRCOUNT+4*MTNPOINT ] = J5VACL*m_meter;

			MTBUFFERY[ MRCOUNT+5*MTNPOINT ] = J6VACL*m_meter;

			MTBUFFERY[ MRCOUNT+6*MTNPOINT ] = J1XCF*m_meter;

			MTBUFFERX[MRCOUNT]=MRPLOTTIME;           /* time base */

			MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
		}

		else if (MTTEST_KEY==FRQRESP)
		{
			if (MRNEWRESULT)	/* if new result produced */
			{
				MTBUFFERY[ MRCOUNT ]  = JXMTFGAINDB;

   				m_sp0 =  - JXMTPHASE;
				m_sp0 = max ( m_sp0 , m_phlimneg);
				if ( m_sp0 > m_phlimpos ) m_sp0 = m_phlimneg;

				MTBUFFERY[ MRCOUNT+MTNPOINT ]   =  m_sp0;

				MTBUFFERY[ MRCOUNT+2*MTNPOINT ] = MTAMPACC;

			  	MTBUFFERX[MRCOUNT] = MTFREQ;    /* drive frq */

				MRNEWRESULT = FALSE;

	                        MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
			}
		}

		else if (MTTEST_KEY==POSFRQRESP)
		{
			  if (MRNEWRESULT)	/* if new result produced */
			  {
				MTBUFFERY[ MRCOUNT ] = JXMTFGAINDB;

   				m_sp0 =  - JXMTPHASE;
				m_sp0 = max ( m_sp0 , m_phlimneg);
				if ( m_sp0 > m_phlimpos ) m_sp0 = m_phlimneg;

				MTBUFFERY[ MRCOUNT+MTNPOINT ]   =  m_sp0;

				MTBUFFERY[ MRCOUNT+2*MTNPOINT ] = MTAMPACC;

			  	MTBUFFERX[MRCOUNT]=MTFREQ;    /* drive frq */

				MRNEWRESULT = FALSE;

			        MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
			  }
		}

		else if (MTTEST_KEY==MORN1)
		{
		/*
		*	valve current vs jack vel
		*/
			if(MTJACK==JACK1)
			{
				MTBUFFERY[MRCOUNT] = J1IC;
			     	MTBUFFERX[MRCOUNT] = J1VACL*m_meter;
			  	MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
			}

        		if(MTJACK==JACK2)
			{
			      	MTBUFFERY[MRCOUNT] = J2IC;
			     	MTBUFFERX[MRCOUNT] = J2VACL*m_meter;
			  	MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
			}

        		if(MTJACK==JACK3)
			{
			      	MTBUFFERY[MRCOUNT] = J3IC;
			     	MTBUFFERX[MRCOUNT] = J3VACL*m_meter;
			  	MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
			}

        		if(MTJACK==JACK4)
			{
			      	MTBUFFERY[MRCOUNT] = J4IC;
			     	MTBUFFERX[MRCOUNT] = J4VACL*m_meter;
			  	MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
			}

        		if(MTJACK==JACK5)
			{
			      	MTBUFFERY[MRCOUNT] = J5IC;
			     	MTBUFFERX[MRCOUNT] = J5VACL*m_meter;
			  	MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
			}

        		if(MTJACK==JACK6)
			{
			      	MTBUFFERY[MRCOUNT] = J6IC;
			     	MTBUFFERX[MRCOUNT] = J6VACL*m_meter;
				MRCOUNT = MIN(MRCOUNT+1,MTNPOINT);
			}
		}			/* end of else if (MTTEST_KEY==MORN1)*/

	}                               /* end of if(MRSAMSB==MRSAMFRQ) */

	m_plotfirst = TRUE;		/* reset flag for call to PLOT ROUTINE */

}					/* end of if(MRCOUNT<MTNPOINT) */



/* -------------------------------------------------------------------------
CD MR000  RECORDING OF MTNPOINT POINTS IS OVER:
C         STOP MOTION AND SEND DATA TO MTPLOT ( in host computer )
C  -------------------------------------------------------------------------*/

if (MRCOUNT>=MTNPOINT )		/* curves completely recorded */
{
	/*
	* 	Set frq change flag to stop motion
	*	since table has been used up
	*/

	if ( (MTDRIVMODE == TABLE)&&(MTRSLTMODE==GRAPH) ) MTFRQCHANGE = TRUE;

	if(m_plotfirst)
	{
		m_plotfirst = FALSE;

		/*
		*	Call to plot package
		*/

		if ( (MTMANUALON)&&(MTRSLTMODE==BODEPLOT) )
		{
			MTPLOTKEY = FRQRESP;
		}
		else
		{
			MTPLOTKEY = MTTEST_KEY;  /* NOTEmanual MTP page should set MTPLOTKEY */
		   	MTPLOTREQ = TRUE;        /* when it is running a freq resp */
		}
	}
}

/*  ---------------
CD  MR000 TIME BASE
C   --------------*/

MRPLOTTIME += YITIM;

/*------------
C END OF FILE
-------------*/

}    			/* end of file MTRECORD */
/*****************************************************************************

  'Title                MOTION TEST SELECT PROGRAM
  'Module_ID            MTSELECT
  'Entry_point          MTSELECT
  'Documentation
  'Customer             QANTAS
  'Application          selection of the signals used for motion tuning
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history
   15 oct	6 jack version. 2 parameter: key and jack number

  'References

*/


int m_key,m_jack;

float mtselect(m_key,m_jack)
{
static float    m_signal,
		m_APp,
		m_APpp;

m_signal = 0.0;

if(m_key==KAA)              /* accelerometers */
{
	   switch(MTAXIS)
	   {
		case(AXISLONG):
           	    m_signal = JXACCEL;
	            break;
		case(AXISLAT):
           	    m_signal = JYACCEL;
	            break;
		case(AXISHEAV):
           	    m_signal = JZACCEL;
	            break;
		case(AXISPICH):
           	    m_signal = JQACCEL;
	            break;
		case(AXISROLL):
           	    m_signal = JPACCEL;
	            break;
		case(AXISYAW):
           	    m_signal = JRACCEL;
	            break;
	   }
}                   /* end of if(m_key==KAA) */

else if (m_key==KAP) 		/* commanded platform acceleration */
{
	switch(MTAXIS)
	   {
		case(AXISLONG):
                    m_signal = LONG_MPOSDD;
 	            break;
 		case(AXISLAT):
                    m_signal = LAT_MPOSDD;
 	            break;
		case(AXISHEAV):
                    m_signal = HEAV_MPOSDD;
 	            break;
		case(AXISPICH):
                    m_signal = (PICH_MTPOS - 2.*m_APp + m_APpp);
           	    m_APpp = m_APp ; 	/* update previous iteration values*/
           	    m_APp = PICH_MTPOS;
 	            break;
		case(AXISROLL):
                    m_signal = (ROLL_MTPOS - 2.*m_APp + m_APpp);
           	    m_APpp = m_APp ; 	/* update previous iteration values*/
           	    m_APp = ROLL_MTPOS;
 	            break;
		case(AXISYAW):
                    m_signal = (YAW_MTPOS - 2.*m_APp + m_APpp);
           	    m_APpp = m_APp ; 	/* update previous iteration values*/
           	    m_APp = YAW_MTPOS;
 	            break;

	   }

}		/* end of if(m_key==KAC) */

else if (m_key==KAC) 		/* commanded jack acceleration */
{
	switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1AC;
	            break;
		case(JACK2):
           	    m_signal = J2AC;
	            break;
		case(JACK3):
           	    m_signal = J3AC;
	            break;
		case(JACK4):
           	    m_signal = J4AC;
	            break;
		case(JACK5):
           	    m_signal = J5AC;
	            break;
		case(JACK6):
           	    m_signal = J6AC;
	            break;
	   }

}		/* end of if(m_key==KAC) */


else if (m_key==KVAC)	 /* actual velocity */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1VAC;
	            break;
		case(JACK2):
           	    m_signal = J2VAC;
	            break;
		case(JACK3):
           	    m_signal = J3VAC;
	            break;
		case(JACK4):
           	    m_signal = J4VAC;
	            break;
		case(JACK5):
           	    m_signal = J5VAC;
	            break;
		case(JACK6):
           	    m_signal = J6VAC;
	            break;
	   }
} 		/* end of if (m_key==KVAC) */


else if (m_key==KVR) 		/* requested velocity */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1VR;
	            break;
		case(JACK2):
           	    m_signal = J2VR;
	            break;
		case(JACK3):
           	    m_signal = J3VR;
	            break;
		case(JACK4):
           	    m_signal = J4VR;
	            break;
		case(JACK5):
           	    m_signal = J5VR;
	            break;
		case(JACK6):
           	    m_signal = J6VR;
	            break;
	   }
}		/* end of if (m_key==KVR) */

else if (m_key==KVC) 		/* commanded velocity */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1VC;
	            break;
		case(JACK2):
           	    m_signal = J2VC;
	            break;
		case(JACK3):
           	    m_signal = J3VC;
	            break;
		case(JACK4):
           	    m_signal = J4VC;
	            break;
		case(JACK5):
           	    m_signal = J5VC;
	            break;
		case(JACK6):
           	    m_signal = J6VC;
	            break;
	   }
}		/* end of if (m_key==KVR) */

else if (m_key==KVE) 		/* velocity error */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1VE;
	            break;
		case(JACK2):
           	    m_signal = J2VE;
	            break;
		case(JACK3):
           	    m_signal = J3VE;
	            break;
		case(JACK4):
           	    m_signal = J4VE;
	            break;
		case(JACK5):
           	    m_signal = J5VE;
	            break;
		case(JACK6):
           	    m_signal = J6VE;
	            break;
	   }
}		/* end of if (m_key==KVE) */


else if (m_key==KAD) 		/* demanded acceleleration */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1AD;
	            break;
		case(JACK2):
           	    m_signal = J2AD;
	            break;
		case(JACK3):
           	    m_signal = J3AD;
	            break;
		case(JACK4):
           	    m_signal = J4AD;
	            break;
		case(JACK5):
           	    m_signal = J5AD;
	            break;
		case(JACK6):
           	    m_signal = J6AD;
	            break;
	   }
}		/* end of if (m_key==KAD) */


else if (m_key==KXAC) 		/* actual position */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1XAC;
	            break;
		case(JACK2):
           	    m_signal = J2XAC;
	            break;
		case(JACK3):
           	    m_signal = J3XAC;
	            break;
		case(JACK4):
           	    m_signal = J4XAC;
	            break;
		case(JACK5):
           	    m_signal = J5XAC;
	            break;
		case(JACK6):
           	    m_signal = J6XAC;
	            break;
	   }
}		/* end of else if (m_key==KXAC) */

else if (m_key==KXC) 		/* commanded position */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1XC;
	            break;
		case(JACK2):
           	    m_signal = J2XC;
	            break;
		case(JACK3):
           	    m_signal = J3XC;
	            break;
		case(JACK4):
           	    m_signal = J4XC;
	            break;
		case(JACK5):
           	    m_signal = J5XC;
	            break;
		case(JACK6):
           	    m_signal = J6XC;
	            break;
	   }
}		/* end of else if (m_key==KAC)*/


else if (m_key==KIAC) 		/* actual current */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1IAC;
	            break;
		case(JACK2):
           	    m_signal = J2IAC;
	            break;
		case(JACK3):
           	    m_signal = J3IAC;
	            break;
		case(JACK4):
           	    m_signal = J4IAC;
	            break;
		case(JACK5):
           	    m_signal = J5IAC;
	            break;
		case(JACK6):
           	    m_signal = J6IAC;
	            break;
	   }
}		/* else if (m_key==KIAC)  */


else if (m_key==KIC) 		/* commanded current */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1IC;
	            break;
		case(JACK2):
           	    m_signal = J2IC;
	            break;
		case(JACK3):
           	    m_signal = J3IC;
	            break;
		case(JACK4):
           	    m_signal = J4IC;
	            break;
		case(JACK5):
           	    m_signal = J5IC;
	            break;
		case(JACK6):
           	    m_signal = J6IC;
	            break;
	   }
}		/* end of else if (m_key==KIC) */


else if (m_key==KFRIC) 		/* friction force */
{
	   switch(m_jack)
	   {
		case(JACK1):
           	    m_signal = J1FRIC;
	            break;
		case(JACK2):
           	    m_signal = J2FRIC;
	            break;
		case(JACK3):
           	    m_signal = J3FRIC;
	            break;
		case(JACK4):
           	    m_signal = J4FRIC;
	            break;
		case(JACK5):
           	    m_signal = J5FRIC;
	            break;
		case(JACK6):
           	    m_signal = J6FRIC;
	            break;
	   }
}		/* end of else if (m_key==KFRIC) */





return(m_signal);


}        /* END OF FILE MTSELECT */


/*****************************************************************************

  'Title                SIMULATOR PLATFORM POSITION ANALYSIS
  'Module_ID            MTSIMAVG
  'Entry_point          MTSimAvg ()
  'Documentation
  'Customer             QANTAS
  'Application          performs software noise reduction on the 6 sampled
			position transducer signals
  'Author               MARC KYRIACO
  'Date                 JUN 1991

  'System               MOTION
  'Iteration_rate       2000 Hz
  'Process              Synchronous process

*****************************************************************************/


/***********************/
/* SUBROUTINE MTSIMAVG */
/***********************/

void MTSimAvg ()

{

/* velocity and acceleration limits at the system frequency (2000 Hz) */

static float JMAXACC = 1.449e-4;     /* [IN / ITERATION^2] = 1.5 g   */
static float JMAXVEL = 0.0175;       /*  [IN / ITERATION]  = 35 in/s */

static float JMAXPACC;
static float JMAXNACC;    /*  +/- copies of 2 above  */
static float JMAXPVEL;
static float JMAXNVEL;

/* local variables */
/* =============== */

/* general */

static int INIT_PASS = TRUE;
static int SUBINIT_PASS = TRUE;

/* stage 0:  pre-limit averaging down to 2000/S0_SUBBAND Hz */

static int   S0_SUBBAND = 1;
static float S0_INUMPOS;        /* inverse of S0_SUBBAND */

static int   S0_NUMPOS;       /* subbanding counter */

static float S0_SUMX1;
static float S0_SUMX2;
static float S0_SUMX3;      /* position summations */
static float S0_SUMX4;
static float S0_SUMX5;
static float S0_SUMX6;

static float S0_X1;
static float S0_X2;
static float S0_X3;        /* averaged position */
static float S0_X4;
static float S0_X5;
static float S0_X6;

/* stage 1:  velocity limiting */

static float S1_PX1;
static float S1_PX2;
static float S1_PX3;     /* previous position */
static float S1_PX4;
static float S1_PX5;
static float S1_PX6;

static float S1_UV1;
static float S1_UV2;
static float S1_UV3;     /* unlimited velocity */
static float S1_UV4;
static float S1_UV5;
static float S1_UV6;

static float S1_V1;
static float S1_V2;
static float S1_V3;      /* limited velocity */
static float S1_V4;
static float S1_V5;
static float S1_V6;

/* stage 2:  acceleration limiting */

static float S2_PV1;
static float S2_PV2;
static float S2_PV3;     /* previous velocity */
static float S2_PV4;
static float S2_PV5;
static float S2_PV6;

static float S2_UA1;
static float S2_UA2;
static float S2_UA3;     /* unlimited acceleration */
static float S2_UA4;
static float S2_UA5;
static float S2_UA6;

static float S2_A1;
static float S2_A2;
static float S2_A3;      /* limited acceleration */
static float S2_A4;
static float S2_A5;
static float S2_A6;

/* stage 3:  integrate to get velocity and position from acceleration */

/* stage 4:  copy to output variables */

static float OUTPOS1 = 0.;
static float OUTPOS2 = 0.;
static float OUTPOS3 = 0.;
static float OUTPOS4 = 0.;
static float OUTPOS5 = 0.;
static float OUTPOS6 = 0.;

static float OUTVEL1 = 0.;
static float OUTVEL2 = 0.;
static float OUTVEL3 = 0.;
static float OUTVEL4 = 0.;
static float OUTVEL5 = 0.;
static float OUTVEL6 = 0.;

/******************/
/* INITIALIZATION */
/******************/

if (INIT_PASS) {

   JMAXPVEL = JMAXVEL * S0_SUBBAND;
   JMAXPACC = JMAXACC * S0_SUBBAND;
   JMAXNVEL = -1. * JMAXPVEL;
   JMAXNACC = -1. * JMAXPACC;

   S0_INUMPOS = 1. / S0_SUBBAND;
   S0_NUMPOS = 0;

   S0_SUMX1 = 0.;
   S0_SUMX2 = 0.;
   S0_SUMX3 = 0.;
   S0_SUMX4 = 0.;
   S0_SUMX5 = 0.;
   S0_SUMX6 = 0.;

   INIT_PASS = FALSE;

} /* if */

/*********************************/
/* STAGE 0:  PRE-LIMIT AVERAGING */
/*********************************/

S0_NUMPOS += 1;
S0_SUMX1 += J1XAC;
S0_SUMX2 += J2XAC;
S0_SUMX3 += J3XAC;
S0_SUMX4 += J4XAC;
S0_SUMX5 += J5XAC;
S0_SUMX6 += J6XAC;

if (S0_NUMPOS == S0_SUBBAND) {              /* start of stage 0 subbanding */

   S0_X1 = S0_SUMX1 * S0_INUMPOS;
   S0_X2 = S0_SUMX2 * S0_INUMPOS;
   S0_X3 = S0_SUMX3 * S0_INUMPOS;
   S0_X4 = S0_SUMX4 * S0_INUMPOS;
   S0_X5 = S0_SUMX5 * S0_INUMPOS;
   S0_X6 = S0_SUMX6 * S0_INUMPOS;

   S0_NUMPOS = 0;
   S0_SUMX1 = 0.;
   S0_SUMX2 = 0.;
   S0_SUMX3 = 0.;
   S0_SUMX4 = 0.;
   S0_SUMX5 = 0.;
   S0_SUMX6 = 0.;

/*******************************/
/* STAGE 1:  VELOCITY LIMITING */
/*******************************/

   if (SUBINIT_PASS) {

      S1_PX1 = S0_X1;
      S1_PX2 = S0_X2;
      S1_PX3 = S0_X3;
      S1_PX4 = S0_X4;
      S1_PX5 = S0_X5;
      S1_PX6 = S0_X6;

   } /* if */

   S1_UV1 = S0_X1 - S1_PX1;
   S1_UV2 = S0_X2 - S1_PX2;
   S1_UV3 = S0_X3 - S1_PX3;    /* calculate unlimited velocity */
   S1_UV4 = S0_X4 - S1_PX4;
   S1_UV5 = S0_X5 - S1_PX5;
   S1_UV6 = S0_X6 - S1_PX6;

   if (S1_UV1 > JMAXPVEL)                    /* limit jack 1 velocity */
      S1_V1 = JMAXPVEL;
   else
      if (S1_UV1 < JMAXNVEL)
         S1_V1 = JMAXNVEL;
      else
         S1_V1 = S1_UV1;

   if (S1_UV2 > JMAXPVEL)                    /* limit jack 2 velocity */
      S1_V2 = JMAXPVEL;
   else
      if (S1_UV2 < JMAXNVEL)
         S1_V2 = JMAXNVEL;
      else
         S1_V2 = S1_UV2;

   if (S1_UV3 > JMAXPVEL)                    /* limit jack 3 velocity */
      S1_V3 = JMAXPVEL;
   else
      if (S1_UV3 < JMAXNVEL)
         S1_V3 = JMAXNVEL;
      else
         S1_V3 = S1_UV3;

   if (S1_UV4 > JMAXPVEL)                    /* limit jack 4 velocity */
      S1_V4 = JMAXPVEL;
   else
      if (S1_UV4 < JMAXNVEL)
         S1_V4 = JMAXNVEL;
      else
         S1_V4 = S1_UV4;

   if (S1_UV5 > JMAXPVEL)                    /* limit jack 5 velocity */
      S1_V5 = JMAXPVEL;
   else
      if (S1_UV5 < JMAXNVEL)
         S1_V5 = JMAXNVEL;
      else
         S1_V5 = S1_UV5;

   if (S1_UV6 > JMAXPVEL)                    /* limit jack 6 velocity */
      S1_V6 = JMAXPVEL;
   else
      if (S1_UV6 < JMAXNVEL)
         S1_V6 = JMAXNVEL;
      else
         S1_V6 = S1_UV6;

/***********************************/
/* STAGE 2:  ACCELERATION LIMITING */
/***********************************/

   if (SUBINIT_PASS) {

      SUBINIT_PASS = FALSE;

      S2_PV1 = S1_V1;
      S2_PV2 = S1_V2;
      S2_PV3 = S1_V3;
      S2_PV4 = S1_V4;
      S2_PV5 = S1_V5;
      S2_PV6 = S1_V6;

   } /* if */

   S2_UA1 = S1_V1 - S2_PV1;
   S2_UA2 = S1_V2 - S2_PV2;
   S2_UA3 = S1_V3 - S2_PV3;    /* calculate unlimited acceleration */
   S2_UA4 = S1_V4 - S2_PV4;
   S2_UA5 = S1_V5 - S2_PV5;
   S2_UA6 = S1_V6 - S2_PV6;

   if (S2_UA1 > JMAXPACC)                    /* limit jack 1 acceleration */
      S2_A1 = JMAXPACC;
   else
      if (S2_UA1 < JMAXNACC)
         S2_A1 = JMAXNACC;
      else
         S2_A1 = S2_UA1;

   if (S2_UA2 > JMAXPACC)                    /* limit jack 2 acceleration */
      S2_A2 = JMAXPACC;
   else
      if (S2_UA2 < JMAXNACC)
         S2_A2 = JMAXNACC;
      else
         S2_A2 = S2_UA2;

   if (S2_UA3 > JMAXPACC)                    /* limit jack 3 acceleration */
      S2_A3 = JMAXPACC;
   else
      if (S2_UA3 < JMAXNACC)
         S2_A3 = JMAXNACC;
      else
         S2_A3 = S2_UA3;

   if (S2_UA4 > JMAXPACC)                    /* limit jack 4 acceleration */
      S2_A4 = JMAXPACC;
   else
      if (S2_UA4 < JMAXNACC)
         S2_A4 = JMAXNACC;
      else
         S2_A4 = S2_UA4;

   if (S2_UA5 > JMAXPACC)                    /* limit jack 5 acceleration */
      S2_A5 = JMAXPACC;
   else
      if (S2_UA5 < JMAXNACC)
         S2_A5 = JMAXNACC;
      else
         S2_A5 = S2_UA5;

   if (S2_UA6 > JMAXPACC)                    /* limit jack 6 acceleration */
      S2_A6 = JMAXPACC;
   else
      if (S2_UA6 < JMAXNACC)
         S2_A6 = JMAXNACC;
      else
         S2_A6 = S2_UA6;

/************************************/
/* STAGE 3:  INTEGRATE A TO GET V,X */
/************************************/

   S2_PV1 += S2_A1;
   S2_PV2 += S2_A2;
   S2_PV3 += S2_A3;
   S2_PV4 += S2_A4;
   S2_PV5 += S2_A5;
   S2_PV6 += S2_A6;

   S1_PX1 += S2_PV1;
   S1_PX2 += S2_PV2;
   S1_PX3 += S2_PV3;
   S1_PX4 += S2_PV4;
   S1_PX5 += S2_PV5;
   S1_PX6 += S2_PV6;

/**************************************/
/* STAGE 4:  COPY TO OUTPUT VARIABLES */
/**************************************/
/*
   OUTPOS1 = S1_PX1;
   OUTPOS2 = S1_PX2;
   OUTPOS3 = S1_PX3;
   OUTPOS4 = S1_PX4;
   OUTPOS5 = S1_PX5;
   OUTPOS6 = S1_PX6;

   OUTVEL1 = S2_PV1;
   OUTVEL2 = S2_PV2;
   OUTVEL3 = S2_PV3;
   OUTVEL4 = S2_PV4;
   OUTVEL5 = S2_PV5;
   OUTVEL6 = S2_PV6;
*/
   J1VAC = S2_PV1 * 2000. * S0_INUMPOS;
   J2VAC = S2_PV2 * 2000. * S0_INUMPOS;
   J3VAC = S2_PV3 * 2000. * S0_INUMPOS;
   J4VAC = S2_PV4 * 2000. * S0_INUMPOS;
   J5VAC = S2_PV5 * 2000. * S0_INUMPOS;
   J6VAC = S2_PV6 * 2000. * S0_INUMPOS;

/*****************************/
/* END OF STAGE 0 SUBBANDING */
/*****************************/

} /* if */

/*********************/
/* END OF SUBROUTINE */
/*********************/

return;

} /* MTSimAvg */
