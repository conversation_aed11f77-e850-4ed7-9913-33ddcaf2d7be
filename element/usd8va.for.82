C'Title              Atmosphere Program
C'Module_ID          VA
C'Entry_point        VAIR
C'Documentation      TBD
C'Application        Compute ambient temperature and wind vectors
C'Author             Department 24, Flight
C'Date               May 1, 1991
C
C'System             FLIGHT
C'Iteration rate     66 msec
C'Process            Synchronous process
C
      SUBROUTINE USD8VA
C     =================
C
      IMPLICIT NONE
C
C
C'Revision_history
C
C  usd8va.for.14 27Mar1997 00:47 usd8 TOM
C       < COA S81-1-129 raised altitude level of taflvl(3) >
C
C  usd8va.for.13 13Sep1996 02:05 usd8 tom
C       < COA To fix the temp/altimeter mixing problem >
C
C  usd8va.for.12 14Sep1992 13:32 usd8 TOM
C       < CHANGED SIGN VA230 AND VA426 per Paul VE FAX 8/9/92 >
C
C  usd8va.for.11 27Aug1992 00:57 usd8 PVE
C       < CORRECT SIGN ON VERTICAL WIND SHEAR >
C
C  usd8va.for.10 26Aug1992 23:20 usd8 pve
C       < make plotting labels vnshrkts and veshrkts in knots wheras
C         vvshrkts is in ft/sec >
C
C  usd8va.for.9 26Aug1992 22:56 usd8 PAUL VA
C       < ADD LABELS TO PLOT OUT WINDSHEARS. >
C
C  usd8va.for.8  5Jul1992 15:44 usd8 PLam
C       < Corrected equation number VA380 to VA297 >
C
C  usd8va.for.7 20Jun1992 10:54 usd8 M.WARD
C       < SEE FIELD MARKERS, WAS GOING INTO INFINITE LOOP ON REF RWY
C         CHANGE >
C
C  usd8va.for.6  5May1992 08:34 usd8 PVE
C       < WINDSHEAR TUNING HARDCODED PLUS FIX ERRORS IN ALTSET LOGIC >
C
C  usd8va.for.5 16Apr1992 05:27 usd8 pve
C       < lower default intermediate levels >
C
C  usd8va.for.4 16Apr1992 00:08 usd8 PVE
C       < RESET TAWSGAIN TO 1 If I/F sets it to zero >
C
C  usd8va.for.3 11Apr1992 08:23 usd8 PVE
C       < INITIALIZE AND INCREASE WINDSHEAR  >
C
C  usd8va.for.2  9Mar1992 22:02 usd8 pve
C       < turn off automatic wind decay at low altitudes >
C
C  usd8va.for.1 20Dec1991 16:12 usd8 PAULV
C       < ADD IDENT LABEL AND UNCOMMENT CALLS TO VRAE AND VMB >
C
C   #(002) 15-Oct-91 GORDON C
C         Changing DA81 to USD8
C
C'
C'References :
C
C  [1]  FAA Windshear Training Aid, February 1987
C'
C'Purpose :
C
C       This module models temperature and wind variations in the
C       atmosphere, including windshears and microbursts, and
C       downbursts, but not including turbulence.
C'
C'Include_files :
C
      INCLUDE 'disp.com'
C'
C'Subroutines_called :
C
C     VMB                   ! Microburst Module
C     VRAE                  ! RAE Bedford Microburst Module
C
C'Ident :
C
       CHARACTER*55   REV /
     &  '$Source: usd8va.for.14 27Mar1997 00:47 usd8 TOM    $'/
C
CQ    USD8  XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,XRFTEST5,XRFTEST6
C
C     Inputs
C
CP    USD8
CPI  H  HATGON,    HEWIND,    HMAN,      HNWIND,    HVWIND,    HWDIR,
CPI  H  HWDIROFF,
CPI  R  RTAVAR,    RUCOSLAT,  RUFLT,     RUPLAT,    RUPLON,    RXMISELE,
CPI  R  RXMISGPX,  RXMISHDG,  RXMISIDX,  RXMISLAT,  RXMISLON,  RXMISRLE,
CPI  T  TABSTDUR,  TABSTINT,  TAGUST,    TAMBGAIN,  TAMBPROF,  TAMBXOFF,
CPI  T  TAMBYOFF,  TAWSGAIN,  TAWSPROF,  TCFFLPOS,  TCFTOT,    TCMBURST,
CPI  T  TCMSHEAR,
CPI  V  VALLZONE,  VBOG,      VFESHR,    VFNSHR,    VFRZVFG,   VFUWSHR,
CPI  V  VFVSHR,    VFVWSHR,   VFWWSHR,   VH,        VHS,       VLXB,
CPI  V  VLXB0,     VLYB,      VLYB0,     VLZB,      VLZB0,     VMXB,
CPI  V  VMXB0,     VMYB,      VMYB0,     VMZB,      VMZB0,     VNXB,
CPI  V  VNXB0,     VNYB,      VNYB0,     VNZB,      VNZB0,     VTHETADG,
CPI  V  VVE,       VVR,
CPI  Y  YLGAUSN,
C
C  OUTPUTS
C
CPO  T  TAAWDIR,   TAAWSPD,   TAFLVL,    TAMBLAT,   TAMBLON,   TAOAT,
CPO  T  TAQFE,     TAQNH,     TATEMP,    TAWDFLVL,  TAWDIR,    TAWNBA,
CPO  T  TAWSFLVL,  TAWSPD,    TCMAVQNH,  TCMAVTMP,  TCMCHATM,  TCMDBURS,
CPO  T  TCRENVI,
CPO  V  VA,        VAI,       VAINIT,    VAIRSET,   VCLASS2,   VCSRW,
CPO  V  VCSWDIR,   VEMB,      VESHR,     VHP,       VKWSHR,    VNMB,
CPO  V  VNSHR,     VOAWDIR,   VOAWSPD,   VOFLVL,    VOQNH,     VOTEMP,
CPO  V  VOWDFLVL,  VOWDIR,    VOWSFLVL,  VOWSPD,    VPSIW,     VRAE,
CPO  V  VSHLTURB,  VSNRW,     VSNWDIR,   VTEMP,     VTEMPK,    VTOTWDIR,
CPO  V  VTOTWIND,  VUG,       VUW,       VUWSHR,    VVDB,      VVEW,
CPO  V  VVG,       VVMB,      VVNS,      VVSHR,     VVW,       VVWD,
CPO  V  VVWEW,     VVWNS,     VVWSHR,    VWG,       VWGUST,    VWIND,
CPO  V  VNSHRKTS,  VESHRKTS,  VVSHRKTS,
CPO  V  VWW,       VWWSHR,    VXD,       VXREF,     VYD,       VZONE2
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:00:41 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
     &, RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLON(5)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
C$
      REAL*4   
     &  HEWIND         ! EAST WIND COMPONENT                    [kts]
     &, HNWIND         ! NORTH WIND COMPONENT                   [kts]
     &, HVWIND         ! VERTICAL WIND COMPONENT                [kts]
     &, HWDIR          ! WIND DIRECTION FOR ATG                 [deg]
     &, HWDIROFF       ! ATG WIND DIRECTION OFFSET              [deg]
     &, RTAVAR         ! MAGNETIC VARIATION                    [DEG]
     &, RUCOSLAT       ! COS A/C LAT
     &, RXMISHDG(5)    !  7 TRUE HDG/MAG DECLINATION(DEG)      [DEG]
     &, TABSTDUR       ! DOWNBURST DURATION                  [Secs ]
     &, TABSTINT       ! DOWNBURST INTENSITY (0-100%)
     &, TAGUST         ! WIND GUST INTENSITY
     &, TAMBGAIN       ! MICROBURST INTENSITY (0-5)
     &, TAMBXOFF       ! MICROBURST X POS OFFSET             [KM   ]
     &, TAMBYOFF       ! MICROBURST Y POS OFFSET             [KM   ]
     &, TAWSGAIN       ! WINDSHEAR INTENSITY
     &, VFESHR         ! EAST-WEST WIND COMPONENT FOR WINDSHEAR
     &, VFNSHR         ! NORTH-SOUTH WIND COMPONENT FOR WINDSHEAR
     &, VFUWSHR        ! FAA X-BODY WINDSHEAR COMPONENT
     &, VFVSHR         ! VERTICAL COMPONENT FOR WINDSHEAR
     &, VFVWSHR        ! FAA Y-BODY WINDSHEAR COMPONENT
     &, VFWWSHR        ! FAA Z-BODY WINDSHEAR COMPONENT
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VLXB           ! DC A/C X WITH EARTH X AXIS
     &, VLXB0          ! VLXB AT VPSI=0
     &, VLYB           ! DC A/C Y WITH EARTH X AXIS
     &, VLYB0          ! VLYB AT VPSI=0
     &, VLZB           ! DC A/C Z WITH EARTH X AXIS
     &, VLZB0          ! VLZB AT VPSI=0
     &, VMXB           ! DC A/C X WITH EARTH Y AXIS
     &, VMXB0          ! VMXB AT VPSI=0
      REAL*4   
     &  VMYB           ! DC A/C Y WITH EARTH Y AXIS
     &, VMYB0          ! VMYB AT VPSI=0
     &, VMZB           ! DC A/C Z WITH EARTH Y AXIS
     &, VMZB0          ! VMZB AT VPSI=0
     &, VNXB           ! DC A/C X WITH EARTH Z AXIS
     &, VNXB0          ! VNXB AT VPSI=0
     &, VNYB           ! DC A/C Y WITH EARTH Z AXIS
     &, VNYB0          ! VNYB AT VPSI=0
     &, VNZB           ! DC A/C Z WITH EARTH Z AXIS
     &, VNZB0          ! VNZB AT VPSI=0
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, VVR            ! ROTATION SPEED                         [kts]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      INTEGER*4
     &  RXMISIDX(5)    ! 31 STATION INDEX NUMBER
C$
      INTEGER*2
     &  RXMISELE(5)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RXMISGPX(5)    ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RXMISRLE(5)    !  8 RUNWAY LENGTH (FEET)                [FT]
     &, TAMBPROF       ! MICROBURST PROFILE (0-6)
     &, TAWSPROF       ! WINDSHEAR PROFILE (0-12)
C$
      LOGICAL*1
     &  HATGON         ! ATG RUNNING FLAG
     &, HMAN           ! MANUAL ATG TEST FLAG
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFTOT         ! FREEZE/TOTAL
     &, TCMBURST       ! MICROBURST ACTIVATE
     &, TCMSHEAR       ! WINDSHEAR ACTIVATE SELECTED PROF
     &, VALLZONE       ! SET ALL BITS FOR ALL FLIGHT FGEN DATA
     &, VBOG           ! ON GROUND FLAG
     &, VFRZVFG        ! FREEZE FUNCTION GENERATION
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  TAAWDIR        ! WIND DIRECTION AT AIRCRAFT          [Degs ]
     &, TAAWSPD        ! WIND SPEED AT AIRCRAFT              [Knots]
     &, TAFLVL(5)      ! FLIGHT LEVEL FOR TEMPERATURE        [Feet ]
     &, TAMBLAT        ! MICROBURST LATITUDE                 [Degs ]
     &, TAMBLON        ! MICROBURST LONGITUDE                [Degs ]
     &, TAOAT          ! TEMPERATURE AT A/C                  [DegsC]
     &, TAQFE          ! AIRFIELD PRESSURE                   [Inchs]
     &, TAQNH          ! SEA LEVEL BARO PRESSURE             [Inchs]
     &, TATEMP(5)      ! TEMPERATURE AT FLIGHT LEVEL         [DegsC]
     &, TAWDFLVL(5)    ! FLIGHT LEVEL FOR WIND DIRECTION     [Feet ]
     &, TAWDIR(5)      ! WIND DIRECTION AT FLIGHT LEVEL      [Degs ]
     &, TAWSFLVL(5)    ! FLIGHT LEVEL FOR WIND SPEED         [Feet ]
     &, TAWSPD(5)      ! WIND SPEED AT FLIGHT LEVEL          [Knots]
     &, VA             ! SPEED OF SOUND IN AIR                 [ft/s]
     &, VAI            ! INVERSE OF SPEED OF SOUND AT A/C      [s/ft]
     &, VCSRW          ! COS OF RWY TRUE HEADING
     &, VCSWDIR        ! COSINE OF WIND DIRECTION AT A/C
     &, VEMB           ! Y-WIND VELOCITY DUE MICROBURST        [ft/s]
     &, VESHR          ! EAST/WEST COMPONENT OF WIND SHEAR      [kts]
     &, VESHRKTS       ! E/W WINDSHEAR COMPONENT                [kts]
     &, VHP            ! ALTITUDE CORRECTION TO VPI              [ft]
     &, VKWSHR         ! WIND FACTOR FOR FAA WIND SHEARS
     &, VNMB           ! X-WIND VELOCITY DUE MICROBURST        [ft/s]
     &, VNSHR          ! NORTH/SOUTH COMPONENT OF WIND SHEAR    [kts]
     &, VNSHRKTS       ! N/S WINDSHEAR COMPONENT                [kts]
     &, VOAWDIR        ! PREV. WIND DIRECTION AT A/C            [deg]
     &, VOAWSPD        ! PREV. WIND SPEED AT A/C                [kts]
     &, VOFLVL(5)      ! PREVIOUS VALUE OF TAFLVL
     &, VOQNH          ! PREV. S.L. BAROMETRIC PRESSURE       [in HG]
     &, VOTEMP(5)      ! PREVIOUS VALUE OF TATEMP
     &, VOWDFLVL(5)    ! PREVIOUS VALUE OF TAWDFLVL
      REAL*4   
     &  VOWDIR(5)      ! PREVIOUS VALUE OF TAWDIR
     &, VOWSFLVL(5)    ! PREVIOUS VALUE OF TAWSFLVL
     &, VOWSPD(5)      ! PREVIOUS VALUE OF TAWSPD
     &, VPSIW          ! WIND DIRECTION AT A/C DEG TRUE NORTH
     &, VSHLTURB       ! RAE BEDFORD TURBULENCE
     &, VSNRW          ! SIN OF RWY TRUE HEADING
     &, VSNWDIR        ! SINE OF WIND DIRECTION AT A/C
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VTEMPK         ! AMBIENT TEMPERATURE AT A/C           [deg K]
     &, VTOTWDIR       ! TOTAL WIND DIR AT A/C                  [deg]
     &, VTOTWIND       ! TOTAL WIND SPD AT A/C                 [ft/s]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VUW            ! X WIND VELOCITY (BODY AXES)           [ft/s]
     &, VUWSHR         ! FAA WINDSHEAR COMPONENT X-BODY AXIS   [ft/s]
     &, VVDB           ! DOWNBURST VERT. SPEED                 [ft/s]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VVMB           ! Z-WIND VELOCITY DUE MICROBURST        [ft/s]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
     &, VVSHR          ! VERTICAL COMPONENT OF WIND SHEAR       [kts]
     &, VVSHRKTS       ! VERTICAL WINDSHEAR COMPONENT           [kts]
     &, VVW            ! Y WIND VELOCITY (BODY AXES)           [ft/s]
     &, VVWD           ! VERTICAL WIND VELOCITY                [ft/s]
     &, VVWEW          ! EAST/WEST WIND VELOCITY               [ft/s]
     &, VVWNS          ! NORTH/SOUTH WIND VELOCITY             [ft/s]
     &, VVWSHR         ! FAA WINDSHEAR COMPONENT Y-BODY AXIS   [ft/s]
     &, VWG            ! BODY AXES Z VEL. WRT GROUND           [ft/s]
     &, VWGUST         ! CHANGE IN WIND SPEED DUE TO GUST       [kts]
     &, VWIND          ! WIND SPEED AT A/C                     [ft/s]
     &, VWW            ! Z WIND VELOCITY (BODY AXES)           [ft/s]
     &, VWWSHR         ! FAA WINDSHEAR COMPONENT Z-BODY AXIS   [ft/s]
      REAL*4   
     &  VXD            ! X DIST. TO TOUCHDOWN AREA ON R/W        [ft]
     &, VXREF          ! FAA WINDSHEAR REFERENCE GROUND DISTANCE [FT]
     &, VYD            ! Y DIST. TO TOUCHDOWN AREA ON R/W        [ft]
C$
      INTEGER*2
     &  TAWNBA         ! WX FRONT NUMBER PAGE A (0-20)
     &, VCLASS2        ! CLASS FOR FUNCTION GENERATION
     &, VZONE2         ! ZONE FOR FUNCTION GENERATION
C$
      LOGICAL*1
     &  TCMAVQNH       ! QNH ASSOCIATED BOOLEAN
     &, TCMAVTMP       ! TEMP ASSOCIATED BOOLEAN
     &, TCMCHATM       ! CHANGE IN TEMP/WIND PROFILE
     &, TCMDBURS       ! DOWNBURST ACTIVATE
     &, TCRENVI        ! ENVIRONMENT STANDARD RESET
     &, VAINIT         ! REINITIALIZE VA MODULE
     &, VAIRSET        ! ACTIVATE GROUND SPEED ADJUSTMENT LOGIC
     &, VRAE           ! USE RAE BEDFORD MICROBURST AND TURBULENCE
C$
      LOGICAL*1
     &  DUM0000001(1236),DUM0000002(15052),DUM0000003(6)
     &, DUM0000004(62),DUM0000005(2),DUM0000006(13)
     &, DUM0000007(17),DUM0000008(395),DUM0000009(136)
     &, DUM0000010(300),DUM0000011(44),DUM0000012(376)
     &, DUM0000013(48),DUM0000014(152),DUM0000015(40)
     &, DUM0000016(740),DUM0000017(8),DUM0000018(36)
     &, DUM0000019(12),DUM0000020(4),DUM0000021(4)
     &, DUM0000022(56),DUM0000023(396),DUM0000024(148)
     &, DUM0000025(44),DUM0000026(407),DUM0000027(1377)
     &, DUM0000028(6),DUM0000029(255),DUM0000030(316)
     &, DUM0000031(484),DUM0000032(15776),DUM0000033(44)
     &, DUM0000034(603),DUM0000035(37840),DUM0000036(30)
     &, DUM0000037(50),DUM0000038(100),DUM0000039(228810)
     &, DUM0000040(54),DUM0000041(157),DUM0000042(8)
     &, DUM0000043(6730),DUM0000044(402),DUM0000045(2)
     &, DUM0000046(6),DUM0000047(28),DUM0000048(2428)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLGAUSN,DUM0000002,VZONE2,DUM0000003,VCLASS2
     &, DUM0000004,VRAE,VALLZONE,DUM0000005,VBOG,DUM0000006,VFRZVFG
     &, DUM0000007,VAINIT,DUM0000008,VWG,DUM0000009,VVG,DUM0000010
     &, VUG,DUM0000011,VVE,DUM0000012,VTHETADG,DUM0000013,VLXB0
     &, VLXB,VMXB0,VMXB,VNXB0,VNXB,VLYB0,VLYB,VMYB0,VMYB,VNYB0
     &, VNYB,VLZB0,VLZB,VMZB0,VMZB,VNZB0,VNZB,VVNS,VVEW,DUM0000014
     &, VHP,VHS,DUM0000015,VH,DUM0000016,VOTEMP,VOWDFLVL,VOWDIR
     &, VOWSPD,VOFLVL,VOWSFLVL,VOQNH,DUM0000017,VOAWSPD,VOAWDIR
     &, DUM0000018,VTEMP,VTEMPK,VNSHR,VESHR,VVSHR,VUWSHR,VVWSHR
     &, VWWSHR,VKWSHR,VNSHRKTS,VESHRKTS,VVSHRKTS,DUM0000019,VXD
     &, VYD,DUM0000020,VXREF,VPSIW,VCSWDIR,VSNWDIR,VWIND,DUM0000021
     &, VTOTWIND,VTOTWDIR,VFNSHR,VFESHR,VFVSHR,VFUWSHR,VFVWSHR
     &, VFWWSHR,VEMB,VNMB,VVMB,DUM0000022,VVWNS,VVWEW,VVWD,VUW
     &, VVW,VWW,VVDB,DUM0000023,VWGUST,DUM0000024,VSHLTURB,DUM0000025
     &, VVR,DUM0000026,VAIRSET,DUM0000027,HATGON,DUM0000028,HMAN
     &, DUM0000029,VCSRW,VSNRW,DUM0000030,HWDIR,HEWIND,HNWIND
     &, HVWIND,HWDIROFF,DUM0000031,VA,VAI,DUM0000032,RUPLAT,RUPLON
     &, RUCOSLAT,DUM0000033,RUFLT,DUM0000034,RTAVAR,DUM0000035
     &, RXMISLAT,RXMISLON,RXMISELE,DUM0000036,RXMISHDG,RXMISRLE
     &, DUM0000037,RXMISIDX,DUM0000038,RXMISGPX,DUM0000039,TCFTOT
     &, TCFFLPOS,DUM0000040,TCRENVI,DUM0000041,TCMSHEAR,TCMDBURS
     &, TCMBURST,DUM0000042,TCMCHATM,DUM0000043,TCMAVQNH,TCMAVTMP
     &, DUM0000044,TAFLVL,TAWDFLVL,TAWSFLVL,TATEMP,TAWSPD,TAWDIR
     &, TAAWSPD,TAAWDIR,DUM0000045,TAMBPROF,TAWSPROF,DUM0000046
     &, TABSTDUR,TABSTINT,TAWSGAIN,TAGUST,TAQNH,TAQFE,TAOAT,DUM0000047
     &, TAMBXOFF,TAMBYOFF,TAMBLAT,TAMBLON,TAMBGAIN,DUM0000048
     &, TAWNBA    
C------------------------------------------------------------------------------
CSDD+
C'
C'Local_Variables :
C
      REAL*4 PRDTODG, PDGTORD, PDEGTOFT, PKTSTOFS, PGAMMA, PR, PI, PI2
C
      PARAMETER
     &(         PRDTODG    = 57.29577951  ! Radians to degress
     &,         PDGTORD    = 0.017453293  ! Degrees to radians
     &,         PDEGTOFT   = 364566.0     ! Degrees to feet (lat/lon calc.)
     &,         PKTSTOFS   = 1.68705      ! Knots to feet/sec
     &,         PGAMMA     = 1.4          ! Cp/Cv for air
     &,         PR         = 3092.14      ! Ideal gas constant for air
     &,         PI         = 3.141592654  ! Pi
     &,         PI2        = 6.283185308  ! 2 * pi
     &)
C
      REAL*4    PDQNH, PDFADE, PDTEMP, PDPRES
      INTEGER*4 FGENFILE/2/             ! FGEN file identification
      INTEGER*4 PATLEV, PWSLEV, PWDLEV
C
      PARAMETER
     &(       PDQNH   = 0.05  ! Delta QNH per second (millibar)
     &,       PDFADE  = 0.2   ! Delta wind per second
     &,       PDTEMP  = 5.0   ! Delta temperature per second
     &,       PDPRES  = 30.   ! Delta pressure per second
     &,       PATLEV  = 5     ! Number of Levels in Temperature Profile
     &,       PWSLEV  = 5     ! Number of Levels in Wind Speed Profile
     &,       PWDLEV  = 5     ! Number of Levels in Wind Direction Profile
     &)
C
      REAL*4
     &        LBURSTD            ! Downburst timer (sec)
     &,       LDELPSI            ! Difference between LPSID and rwy heading
     &,       LDLAT              ! Difference in A/C lat. and rwy end lat.
     &,       LDLON              ! Difference in A/C long. and rwy end long.
     &,       LDQNH              ! Max change in QNH per iter.
     &,       LDPRES             ! Max change in pressure per iter.
     &,       LDTEMP             ! Max change in temperature per iter.
     &,       LDFADE             ! Max change in wind fade factor per iter.
     &,       LEWIND             ! ATG east wind input
     &,       LFADE1             ! Fade factor for down burst
     &,       LGUSTINT           ! Wind gust intensity
     &,       LGUSTMAX/25./      ! Maximum wind gust variation          (ft/sec)
     &,       LKGUST/0.045/      ! Gain factor for gust
     &,       LNWIND             ! ATG north wind input
     &,       LOFFSET            ! Offset for windshear from threshold
     &,       LNEWGUST           ! New wind gust velocity               (ft/sec)
     &,       LNEWPSIG           ! New wind gust direction              (deg)
     &,       LOQFE              ! Previous value of TAQFE
     &,       LOWSGAIN           ! Previous value of TAWSGAIN
     &,       LOMBGAIN           ! Previous value of TAMBGAIN
     &,       LPGUST             ! Prev. wind gust intensity
     &,       LPSID              ! Heading of line from A/C to rwy end
     &,       LPSIG              ! Wind heading variation due to gust   (deg)
     &,       LPSIGMAX/15.0/     ! Max. heading variation due to gust   (deg)
     &,       LPSIWRD            ! Wind heading (rad)
     &,       LWFSHR/1.45/       ! Wind factor for FAA shears
     &,       LRUNLEN            ! Runway length                        (NM)
     &,       LRWYDIST           ! Distance from A/C to rwy end
     &,       LRWYHDG            ! Reference runway heading
     &,       LRWYL              ! Rwy length from G/P pos. to end
     &,       LSHREW             ! Windshear and microburst EW (ft/sec)
     &,       LSHRNS             ! Windshear and microburst NS (ft/sec)
     &,       LSHRV              ! Windshear and microburst V  (ft/sec)
     &,       LSP0               ! Scratch Pad
     &,       LTEMP              ! Unfiltered temp at A/C (deg C)
     &,       LTEMPK             ! Unfiltered temp at A/C (deg K)
     &,       LVWIND             ! ATG vertical wind input
     &,       LAATFLVL(5)        ! FLIGHT temperature profile level (ft)
     &,       LAWSFLVL(5)        ! FLIGHT wind speed profile level (ft)
     &,       LAWDFLVL(5)        ! FLIGHT wind direction profile level (ft)
     &,       LVPRESS            ! FLIGHT pressure at aircraft
     &,       LPRESS             ! FLIGHT pressure at aircraft (unfaded)
     &,       LVHH               ! FLIGHT local pressure altitude
     &,       LAPRESS(5)         ! FLIGHT pressure profile value (deg C)
     &,       LATEMPK(5)         ! FLIGHT temperature profile value (deg K)
     &,       LATEMP(5)          ! FLIGHT temperature profile value (deg C)
     &,       LAWSPD(5)          ! FLIGHT wind speed profile value (kts)
     &,       LAWDIR(5)          ! FLIGHT wind direction profile value (deg)
     &,       LATSLP(5)          ! FLIGHT temperature profile slope
     &,       LWSSLP(5)          ! FLIGHT wind speed profile slope
     &,       LWDSLP(5)          ! FLIGHT wind direction profile slope
     &,       LATINT(5)          ! FLIGHT temperature profile intercept
     &,       LWSINT(5)          ! FLIGHT wind speed profile intercept
     &,       LWDINT(5)          ! FLIGHT wind direction profile intercept
     &,       LVWNS              ! Unfaded value of north-south wind (ft/s)
     &,       LVWEW              ! Unfaded value of east-west wind (ft/s)
     &,       LVWD               ! Unfaded value of vertical wind (ft/s)
     &,       LVWNSD             ! Step in north-south wind (ft/s)
     &,       LVWEWD             ! Step in east-west wind (ft/s)
     &,       LVWDD              ! Step in vertical wind (ft/s)
     &,       LFADE              ! Fade factor for wind changes
     &,       LNORTH             ! north-south wind in feet/sec
     &,       LEAST              ! east-west wind in feet/sec
     &,       LDELTA             ! difference in aircraft rwy & test rwy
     &,       LHS                ! Limited value of VHS
     &,       LSP1,LSP2          ! scratch pads for atg wind backdrve
     &,       LUW                ! old value of vuw
     &,       LVW                ! old value of vvw
     &,       LWW                ! old value of vww
     &,       VWEIGHT            ! Weight fuction generation subbanding
     &,       LTQFE              ! TAQFE variable
     &,       LHPP               ! Past value of VHP
C
      INTEGER*4
     &        LZONE1(6)          ! Lookup table for windshear zones
     &,       LZONE2(6)          ! Lookup table for windshear zones
     &,       LRWYIND/-1/        ! Previous value of RXMISIND
     &,       I                  ! Do loop index
     &,       J                  ! Do loop index
     &,       K                  ! Do loop index
     &,       IGCNT              ! Gust iteration counter
     &,       IGFREQ/150/        ! Frequency at which gust is recalculated
     &,       IN(3) / 1, 2, 2 /  ! Map array for profile logic
     &,       ID /1/             ! Current wind direction segment
     &,       IT /1/             ! Current temperature segment
     &,       IS /1/             ! Current wind speed segment
     &,       LOMBPROF           ! Old microburst profile
     &,       LOWSPROF           ! Old windshear profile
     &,       LLCOU              ! Subbanding counter for microburst
C
      LOGICAL*1
     &        LWCHANGE           ! Change in wind conditions flag
     &,       LCMBURST           ! Previous value of TCMBURST flag
     &,       LBURST             ! Downburst activation flag
     &,       LBURSTFP/.TRUE./   ! Downburst first pass flag
     &,       LFPASS  /.TRUE./   ! First pass initialisation flag
     &,       LGUST              ! Wind gust activation flag
     &,       LGUSTFP/.TRUE./    ! Wind gust first pass flag
     &,       LATOFF             ! Offset temperature profile flag
     &,       LWSOFF             ! Offset wind speed profile flag
     &,       LWDOFF             ! Offset wind direction profile flag
     &,       LATELE             ! Elevation to change temp profile flag
     &,       LWSELE             ! Elevation to change wind spd profile flag
     &,       LWDELE             ! Elevation to change wind dir profile flag
     &,       LATFLG             ! First temperature level updated flag
     &,       LWSFLG             ! First wind speed level updated flag
     &,       LWDFLG             ! First wind direction level updated flag
     &,       LCRENVIP           ! Previous value of TCRENVI
     &,       LTRIGGER           ! Trigger for FAA windshears
     &,       LPTRIGGR           ! Previous value of LTRIGGER
     &,       LFLAG              ! Flag to calculate FAA shear components
     &,       LWNOEXP/.TRUE./    ! Boundary layer exponential decay flag
     &,       LALTSET            ! Change in pressure altitude flag
     &,       LTSTEP             ! Step change in temperature conditions flag
     &,       LQSTEP             ! Step change in pressure conditions flag
C
      DATA
     &     LZONE1/1,2,4,8,16,32/
     &,    LZONE2/1,1,1,2,4,8/
C
C
      ENTRY VAIR
C
C
CD VA010 First Pass Initialization
CR       CAE Calculations,[1] pg. 3.3-51,52,53,54
C
CC    On the first pass of the module, the atmosphere is reset to
CC    standard conditions and the rates used to slew ambient
CC    parameters are calculated as a function of iteration rate.
CC    FAA windshear windfactor is set to 1.45 based on a maximum take-off
CC    weight of 240,000 lbs and thrust at V2 of 26,500 per engine according
CC    to reference [1].
C
      IF (LFPASS .OR. VAINIT) THEN
        FGENFILE = 2
        TCRENVI = .TRUE.
        VA = 1000.
C
        VKWSHR  = 1.7   ! FAA WINDSHEAR TRAINING AID PG 3.3-52
C
        LAATFLVL(1) = -1000.0
        LAWDFLVL(1) = -1000.0
        LAWSFLVL(1) = -1000.0
        LAATFLVL(PATLEV) = 100000.0
        LAWSFLVL(PWSLEV) = 100000.0
        LAWDFLVL(PWDLEV) = 100000.0
C
        LATOFF    = .FALSE.
        LWSOFF    = .FALSE.
        LWDOFF    = .FALSE.
        LATELE    = .FALSE.
        LWSELE    = .TRUE.
        LWDELE    = .TRUE.
C
        LDQNH     =  PDQNH  * YITIM
        LDTEMP    =  PDTEMP * YITIM
        LDPRES    =  PDPRES * YITIM
        LDFADE    =  PDFADE * YITIM
        LFPASS    = .FALSE.
C
      ENDIF
C
CD VA020 Reset to Standard Atmosphere
CR       CAE Calculations
C
CC    If a request for a standard atmosphere is made, then the wind
CC    profile is set to zero, and the barometric and temperature
CC    profiles are set to ISA values.
C
      IF (TCRENVI) THEN
C
        TAWSGAIN = 1.0
        TAFLVL(1) = 0.0
        TAFLVL(2) = RXMISELE(3)
C
C COA S81-1-129 changing field temp at SKBO causes sim to "bomb"
C
        TAFLVL(3) = 9000.                   ! old value was 8000. -JWM-
        TAFLVL(4) = 16000.
        TAFLVL(5) = 36089.2388
C
        DO I=1,PATLEV
          TATEMP(I) = 15.0 - 0.00198127 * TAFLVL(I)
          TAWSPD(I) = 0.0
          TAWDIR(I) = 0.0
          TAWSFLVL(I) = TAFLVL(I)
          TAWDFLVL(I) = TAFLVL(I)
        ENDDO
C
        DO I=1,PATLEV
          VOTEMP(I) = TATEMP(I)
          VOWSPD(I) = TAWSPD(I)
          VOWDIR(I) = TAWDIR(I)
          VOFLVL(I) = TAFLVL(I)
          VOWSFLVL(I) = TAWSFLVL(I)
          VOWDFLVL(I) = TAWDFLVL(I)
        ENDDO
C
        TAOAT   = VTEMP
        TAAWSPD = VOAWSPD
        TAAWDIR = VOAWDIR
C
        TCMCHATM = .TRUE.
        TAQNH    =  29.9212598
        TAWNBA   = 0
C        LWNOEXP  = .FALSE.
C
C        TCRENVI = .FALSE.
      ENDIF
C
CD VA030 Effect of Change of Temperature at A/C
CR       CAE Calculations
C
CC    If the temperature at the aircraft is changed, the entire
CC    temperature profile is shifted by the same amount.
C
      IF (TAOAT .NE. VTEMP) THEN
        LSP0 = TAOAT - VTEMP
        DO I = 2,PATLEV
          TATEMP(I) = TATEMP(I) + LSP0
          VOTEMP(I) = TATEMP(I)
        ENDDO
        TCMCHATM = .TRUE.
      ENDIF
C
CD VA040 Effect of Change of Wind Speed at A/C
CR       CAE Calculations
C
CC    If the wind speed at the aircraft is changed, the entire
CC    wind speed profile is shifted by the same amount.
C
      IF (TAAWSPD .NE. VOAWSPD) THEN
        LSP0 = TAAWSPD - VOAWSPD
        DO I = 2,PWSLEV
          TAWSPD(I) = TAWSPD(I) + LSP0
          VOWSPD(I) = TAWSPD(I)
        ENDDO
        TCMCHATM = .TRUE.
      ENDIF
C
CD VA050 Effect of Change of Wind Direction at A/C
CR       CAE Calculations
C
CC    If the wind direction at the aircraft is changed, the entire
CC    wind direction profile is shifted by the same amount.
C
      IF (TAAWDIR .NE. VOAWDIR) THEN
        LSP0 = TAAWDIR - VOAWDIR
        DO I = 2,PWDLEV
          TAWDIR(I) = TAWDIR(I) + LSP0
          DO WHILE (TAWDIR(I) .LE. -180.0)
            TAWDIR(I) = TAWDIR(I) + 360.0
          ENDDO
          DO WHILE (TAWDIR(I) .GT. 180.0)
            TAWDIR(I) = TAWDIR(I) - 360.0
          ENDDO
          VOWDIR(I) = TAWDIR(I)
        ENDDO
        TCMCHATM = .TRUE.
      ENDIF
C
      LSP0 = VPSIW - RTAVAR
      DO WHILE (LSP0 .LE. -180.)
         LSP0 = LSP0 + 360.
      ENDDO
      DO WHILE (LSP0 .GT. 180.)
         LSP0 = LSP0 - 360.
      ENDDO
      TAAWDIR = LSP0
      VOAWDIR = TAAWDIR
C
CD VA060 Change in Atmosphere Conditions
CR       CAE Calculations
C
CC       If a change in the I/F ambient profiles has occurred,
CC       enter logic to update FLIGHT ambient profile arrays.
C
      IF (TCMCHATM) THEN
C
CD VA070 Change in I/F Temperature Levels
CR       CAE Calculations
C
CC      If a change in the I/F temperature levels has occurred,
CC      a check is performed to ensure that the levels are in
CC      correct order.  If the airfield elevation has changed,
CC      the entire profile will be shifted by the same amount
CC      if required.
C
C        DO I=1,PATLEV                       ! ADDED IF I/F DOES NOT
C          TAWSFLVL(I) = TAFLVL(I)           ! UPDATE TAWSFLVL AND
C          TAWDFLVL(I) = TAFLVL(I)           ! TAWDFLVL
C        ENDDO
C
        DO I=2, PATLEV
          IF (TAFLVL(I) .NE. VOFLVL(I)) THEN
            IF (((TAFLVL(I) - TAFLVL(I-1)) .LT. 1000.0)
     &         .AND. (I .GT. 2)) THEN
              TAFLVL(I) = TAFLVL(I-1) + 1000.0
            ENDIF
            IF (I .LT. PATLEV) VOFLVL(I+1) = TAFLVL(I+1) - 0.1
            IF ((I .EQ. 2) .AND. LATELE) THEN
              LSP0 = TAFLVL(2) - VOFLVL(2)
              DO J = 3, PATLEV-1
                TAFLVL(J) = TAFLVL(J) + LSP0
              ENDDO
              LATEMP(1) = TATEMP(2) + (TATEMP(3) - TATEMP(2))
     &                                * (LAATFLVL(1) - TAFLVL(2))
     &                                / (TAFLVL(3) - TAFLVL(2))
              LATFLG = .TRUE.
            ELSE
CMW              K=1
CMW              DO WHILE (TAFLVL(2) .GT. LAATFLVL(K + 1))
CMW                K = K + 1
CMW              ENDDO
CMW              DO WHILE (TAFLVL(2) .LT. LAATFLVL(IT))
CMW                K = K - 1
CMW             ENDDO
C !FM+
C !FM  20-Jun-92 10:53:53 M.WARD
C !FM    < THIS WAS GOING INTO INFINITE LOOPS WHEN REP RWY CHANGED >
C !FM
              IF (TAFLVL(2) .LT. LAATFLVL(1)) THEN
                K=1
              ELSEIF (TAFLVL(2) .GE. LAATFLVL(5)) THEN
                K=5
              ELSE
                DO WHILE (TAFLVL(2) .GT. LAATFLVL(K+1))
                  K=K+1
                ENDDO
                DO WHILE (TAFLVL(2) .LT. LAATFLVL(K))
                  K=K-1
                ENDDO
              ENDIF
C !FM-
C
             TATEMP(2) = LATINT(K) + LATSLP(K) * TAFLVL(2)
            ENDIF
            VOFLVL(I) = TAFLVL(I)
          ENDIF
        ENDDO
C
CD VA080 Change in I/F Temperatures
CR       CAE Calculations
C
CC       If the sea level or airfield temperature has changed,
CC       then the first temperature level is updated.  If the
CC       first intermediate level temperature has changed, then
CC       the first temperature level is updated to keep either
CC       the sea level or airfield temperature constant. If re-
CC       quired, the entire profile above the changed level will
CC       be offset by the delta change in temperature.
C
        DO I=1,PATLEV
          IF (TATEMP(I) .NE. VOTEMP(I)) THEN
            IF (LATOFF .AND. (I .LE. (PATLEV-1))) THEN
              LSP0 = TATEMP(I) - VOTEMP(I)
              DO J=I+1,PATLEV
                TATEMP(J) = TATEMP(J) + LSP0
                VOTEMP(J) = TATEMP(J)
              ENDDO
            ENDIF
            VOTEMP(I)   = TATEMP(I)
            IF (I .LE. 3) THEN
              LATEMP(1) = TATEMP(IN(I)) + (TATEMP(3) - TATEMP(IN(I)))
     &                                * (LAATFLVL(1) - TAFLVL(IN(I)))
     &                                / (TAFLVL(3) - TAFLVL(IN(I)))
              LATFLG = .TRUE.
            ENDIF
          ENDIF
        ENDDO
C
CD VA090 Change in I/F Wind Speed Levels
CR       CAE Calculations
C
CC      If a change in the I/F wind speed levels has occurred,
CC      a check is performed to ensure the levels are in correct
CC      order.  If the airfield elevation has changed, the sea
CC      level wind speed will be calculated to keep the air-
CC      field wind speed constant if required.
C
        DO I=2, PWSLEV
          IF (TAWSFLVL(I) .NE. VOWSFLVL(I)) THEN
            IF (((TAWSFLVL(I) - TAWSFLVL(I-1)) .LT. 1000.0)
     &         .AND. (I .GT. 2)) THEN
              TAWSFLVL(I) = TAWSFLVL(I-1) + 1000.
            ENDIF
            IF (I .LT. PWSLEV) VOWSFLVL(I+1) = TAWSFLVL(I+1) - 0.1
            IF ((I .EQ. 2) .AND. LWSELE) THEN
              LSP0 = TAWSFLVL(2) - VOWSFLVL(2)
              DO J = 3, PWSLEV-1
                TAWSFLVL(J) = TAWSFLVL(J) + LSP0
              ENDDO
              LAWSPD(1) = TAWSPD(2) + (TAWSPD(3) - TAWSPD(2))
     &                                * (LAWSFLVL(1) - TAWSFLVL(2))
     &                                / (TAWSFLVL(3) - TAWSFLVL(2))
              LWSFLG = .TRUE.
            ENDIF
            VOWSFLVL(I) = TAWSFLVL(I)
          ENDIF
        ENDDO
C
CD VA100 Change in I/F Wind Speeds
CR       CAE Calculations
C
CC       If the sea level or airfield wind speed has changed,
CC       then the first wind speed level is updated.  If the
CC       first intermediate level wind speed has changed, then
CC       the first wind speed level is updated to keep either
CC       the sea level or airfield wind speed constant. If re-
CC       quired, the entire profile above the changed level will
CC       be offset by the delta change in wind speed.
C
        DO I=1,PWSLEV
          IF (TAWSPD(I) .NE. VOWSPD(I)) THEN
            IF (LWSOFF .AND. (I .LE. (PWSLEV-1))) THEN
              LSP0 = TAWSPD(I) - VOWSPD(I)
              DO J=I+1,PWSLEV
                TAWSPD(J) = TAWSPD(J) + LSP0
                VOWSPD(J) = TAWSPD(J)
              ENDDO
            ENDIF
            VOWSPD(I)   = TAWSPD(I)
            IF (I .LE. 3) THEN
              LAWSPD(1) = TAWSPD(IN(I)) + (TAWSPD(3) - TAWSPD(IN(I)))
     &                                * (LAWSFLVL(1) - TAWSFLVL(IN(I)))
     &                                / (TAWSFLVL(3) - TAWSFLVL(IN(I)))
              LWSFLG = .TRUE.
            ENDIF
          ENDIF
        ENDDO
C
CD VA110 Change in I/F Wind Direction Levels
CR       CAE Calculations
C
CC      If a change in the I/F wind direction levels has occurred,
CC      a check is performed to ensure the levels are in correct
CC      order.  If the airfield elevation has changed, the sea
CC      level wind direction will be calculated to keep the air-
CC      field temperature constant if required.
C
        DO I=2, PWDLEV
          IF (TAWDFLVL(I) .NE. VOWDFLVL(I)) THEN
            IF (((TAWDFLVL(I) - TAWDFLVL(I-1)) .LT. 1000.0)
     &         .AND. (I .GT. 2)) THEN
              TAWDFLVL(I) = TAWDFLVL(I-1) + 1000.
            ENDIF
            IF (I .LT. PWDLEV) VOWDFLVL(I+1) = TAWDFLVL(I+1) - 0.1
            IF ((I .EQ. 2) .AND. LWDELE) THEN
              LSP0 = TAWDFLVL(2) - VOWDFLVL(2)
              DO J = 3, PWDLEV-1
                TAWDFLVL(J) = TAWDFLVL(J) + LSP0
              ENDDO
              LAWDIR(1) = TAWDIR(2) + (TAWDIR(3) - TAWDIR(2))
     &                                * (LAWDFLVL(1) - TAWDFLVL(2))
     &                                / (TAWDFLVL(3) - TAWDFLVL(2))
              LWDFLG = .TRUE.
            ENDIF
            VOWDFLVL(I) = TAWDFLVL(I)
          ENDIF
        ENDDO
C
CD VA120 Change in I/F Wind Directions
CR       CAE Calculations
C
CC       If the sea level or airfield wind direction has changed,
CC       then the first wind direction level is updated.  If the
CC       first intermediate level wind direction has changed, then
CC       the first wind direction level is updated to keep either
CC       the sea level or airfield wind direction constant. If re-
CC       quired, the entire profile above the changed level will
CC       be offset by the delta change in wind direction.
C
        DO I=1,PWDLEV
          IF (TAWDIR(I) .NE. VOWDIR(I)) THEN
            IF (LWDOFF .AND. (I .LE. (PWDLEV-1))) THEN
              LSP0 = TAWDIR(I) - VOWDIR(I)
              DO J=I+1,PWDLEV
                TAWDIR(J) = TAWDIR(J) + LSP0
                DO WHILE (TAWDIR(J) .LE. -180.0)
                  TAWDIR(J) = TAWDIR(J) + 360.0
                ENDDO
                DO WHILE (TAWDIR(J) .GT. 180.0)
                  TAWDIR(J) = TAWDIR(J) - 360.0
                ENDDO
                VOWDIR(J) = TAWDIR(J)
              ENDDO
            ENDIF
            DO WHILE (TAWDIR(I) .LE. -180.0)
              TAWDIR(I) = TAWDIR(I) + 360.0
            ENDDO
            DO WHILE (TAWDIR(I) .GT. 180.0)
              TAWDIR(I) = TAWDIR(I) - 360.0
            ENDDO
            VOWDIR(I)   = TAWDIR(I)
            IF (I .LE. 3) THEN
              LAWDIR(1) = TAWDIR(IN(I)) + (TAWDIR(3) - TAWDIR(IN(I)))
     &                                * (LAWDFLVL(1) - TAWDFLVL(IN(I)))
     &                                / (TAWDFLVL(3) - TAWDFLVL(IN(I)))
              LWDFLG = .TRUE.
            ENDIF
          ENDIF
        ENDDO
C
CD VA130 Update Temperature Profile
CR       CAE Calculations
C
CC       The temperature altitude breakpoints are updated based
CC       on the I/F values.  The profile slopes and intercepts
CC       are also re-calculated.
C
        IF (.NOT. LATFLG) THEN
          LATEMP(1) = TATEMP(2) + (TATEMP(3) - TATEMP(2))
     &                          * (LAATFLVL(1) - TAFLVL(2))
     &                          / (TAFLVL(3) - TAFLVL(2))
        ENDIF
        DO I = 2, (PATLEV - 1)
          LAATFLVL(I) = TAFLVL(I+1)
          LATEMP(I)   = TATEMP(I+1)
        ENDDO
        LATEMP(PATLEV) = TATEMP(PATLEV)
C
        DO I = 1,(PATLEV - 1)
          LATEMPK(I) = LATEMP(I) + 273.16
          LATSLP(I) = (LATEMP(I+1) - LATEMP(I))
     &                    / (LAATFLVL(I+1) - LAATFLVL(I))
          LATINT(I) = LATEMP(I) - LATSLP(I) * LAATFLVL(I)
        ENDDO
        TCMAVQNH = .TRUE.
C
CD VA140 Update Wind Speed Profile
CR       CAE Calculations
C
CC       The wind speed altitude breakpoints are updated based
CC       on the I/F values.  The profile slopes and intercepts
CC       are also re-calculated.
C
        IF (.NOT. LWSFLG) THEN
          LAWSPD(1) = TAWSPD(2) + (TAWSPD(3) - TAWSPD(2))
     &                          * (LAWSFLVL(1) - TAWSFLVL(2))
     &                          / (TAWSFLVL(3) - TAWSFLVL(2))
        ENDIF
        DO I = 2, (PWSLEV-1)
          LAWSFLVL(I) = TAWSFLVL(I+1)
          LAWSPD(I)   = TAWSPD(I+1)
        ENDDO
        LAWSPD(PWSLEV) = TAWSPD(PWSLEV)
C
        DO I = 1,(PWSLEV-1)
          LWSSLP(I) = (LAWSPD(I+1) - LAWSPD(I))
     &                    / (LAWSFLVL(I+1) - LAWSFLVL(I))
          LWSINT(I) = LAWSPD(I) - LWSSLP(I) * LAWSFLVL(I)
        ENDDO
C
CD VA150 Update Wind Direction Profile
CR       CAE Calculations
C
CC       The wind direction altitude breakpoints are updated based
CC       on the I/F values.  The profile slopes and intercepts
CC       are also re-calculated.
C
        IF (.NOT. LWDFLG) THEN
          LAWDIR(1) = TAWDIR(2) + (TAWDIR(3) - TAWDIR(2))
     &                          * (LAWDFLVL(1) - TAWDFLVL(2))
     &                          / (TAWDFLVL(3) - TAWDFLVL(2))
        ENDIF
        DO I = 2, (PWDLEV-1)
          LAWDFLVL(I) = TAWDFLVL(I+1)
          LAWDIR(I)   = TAWDIR(I+1)
        ENDDO
        LAWDIR(PWDLEV) = TAWDIR(PWDLEV)
C
        DO I = 1,(PWDLEV-1)
          LWDSLP(I) = (LAWDIR(I+1) - LAWDIR(I))
     &                    / (LAWDFLVL(I+1) - LAWDFLVL(I))
          LWDINT(I) = LAWDIR(I) - LWDSLP(I) * LAWDFLVL(I)
        ENDDO
C
CD VA160 Update Sea Level and Airfield Parameters
CR       CAE Calculations
C
CC       The sea level and airfield parameters are recalculated
CC       to take into account any change in the overall profiles.
CC       Previous values are also updated.
C
        DO I = 1,2
          TATEMP(I)   = LATINT(1) + LATSLP(1) * TAFLVL(I)
          TAWSPD(I)   = LWSINT(1) + LWSSLP(1) * TAFLVL(I)
          TAWDIR(I)   = LWDINT(1) + LWDSLP(1) * TAFLVL(I)
          DO WHILE (TAWDIR(I) .LE. -180.0)
            TAWDIR(I) = TAWDIR(I) + 360.0
          ENDDO
          DO WHILE (TAWDIR(I) .GT. 180.0)
            TAWDIR(I) = TAWDIR(I) - 360.0
          ENDDO
          VOTEMP(I)   = TATEMP(I)
          VOWSPD(I)   = TAWSPD(I)
          VOWDIR(I)   = TAWDIR(I)
          VOFLVL(I) = TAFLVL(I)
          VOWSFLVL(I) = TAFLVL(I)       ! TAWSFLVL(I)
          VOWDFLVL(I) = TAFLVL(I)       ! TAWDFLVL(I)
          LATFLG      = .FALSE.
          LWSFLG      = .FALSE.
          LWDFLG      = .FALSE.
        ENDDO
C
        VOQNH       = TAQNH - LDQNH
        LTSTEP      = .TRUE.
        LWCHANGE    = .TRUE.
        TCMCHATM    = .FALSE.
      ENDIF
C
CD VA170 Effect of Change in Reference Runway
CR       CAE Calculations
C
CC    If the reference runway has changed, all airfield
CC    parameters are updated so that ambient profiles are
CC    not affected by the change. The runway parameters
CC    used for calculating the A/C position wrt the runway
CC    threshold and the sine and cosine of the new runway
CC    heading are also recalculated.
C
      IF (RXMISIDX(3) .NE. LRWYIND) THEN
C
        LRWYL   = RXMISRLE(3) - RXMISGPX(3)
C
        IF (RXMISHDG(3) .GT. 180.) THEN
          LRWYHDG = (RXMISHDG(3) - 360.0) * PDGTORD
        ELSE
          LRWYHDG = RXMISHDG(3) * PDGTORD
        ENDIF
C
        IF (ABS(LRWYHDG) .LE. 1E-20) THEN
          LRWYHDG = 0.0
          VCSRW  = 1.0
          VSNRW  = 0.0
        ELSE
          VCSRW  = COS(LRWYHDG)
          VSNRW  = SIN(LRWYHDG)
        ENDIF
C
        TAFLVL(2) = RXMISELE(3)
        TAWDFLVL(2) = RXMISELE(3)
        TAWSFLVL(2) = RXMISELE(3)
        TCMCHATM = .TRUE.
C
        LRWYIND   = RXMISIDX(3)
      ENDIF
C
CD VA180  Change in Field Barometric Pressure
CR        CAE Calculations
C
CC    If the field barometric pressure is changed, update the
CC    sea level barometric pressure.
C
      IF (TAQFE .NE. LOQFE) THEN
        TCMAVQNH = .TRUE.
 
C coa to fix temp/altimeter problem
c        IF (ABS(LATSLP(1)) .GT. 0.00001) THEN
c          TAQNH = TAQFE  * ((TATEMP(2)+273.16)/(TATEMP(1)+273.16))
c     &                  **(.01041332/LATSLP(1))
c        ELSE
c          TAQNH = TAQFE  * EXP((.01041332/(TATEMP(2)+273.16))*
c     &                  RXMISELE(3))
        IF (RXMISELE(3) .EQ. 0.0) THEN
          TAQNH = TAQFE
        ELSE
          TAQNH = TAQFE * (288.16/ (273.16 - .00198127 * RXMISELE(3)))
     &                ** (-32.174 / (-.00198127 * (1.8 * 1716.)))
        ENDIF
        LOQFE = TAQFE
      ENDIF
C
CD VA190  Sea Level Barometric Pressure (Hg)
CR        CAE Calculations
C
CC    Except for on-ground and ATG conditions, an instructor change
CC    in barometric pressure is filtered to avoid discontinuities.
CC    The pressure altitude correction and airfield barometric
CC    pressure are re-calculated based on new sea level barometric
CC    pressure.
C
      IF (TAQNH .NE. VOQNH) THEN
        TCMAVQNH = .TRUE.
        LSP0 = TAQNH - VOQNH
        IF ((ABS(LSP0) .LE. LDQNH) .OR. HATGON) THEN
          VOQNH = TAQNH
        ELSE
          IF (LSP0 .GT. 0.0) THEN
            VOQNH = VOQNH + LDQNH
          ELSE
            VOQNH = VOQNH - LDQNH
          ENDIF
        ENDIF
      ENDIF
      IF (TCMAVQNH) THEN
C
C COA TO FIX TEMP/ALTIMETER PROBLEM
C        IF (ABS(LATSLP(1)) .GT. 0.00001) THEN
C          LAPRESS(1) = VOQNH * ((TATEMP(1) + 273.16)/LATEMPK(1))
C     &               **(.01041332/LATSLP(1))
C        ELSE
C          LAPRESS(1) = VOQNH * EXP((.01041332/LATEMPK(1))*LAATFLVL(1))
        IF (RXMISELE(3) .EQ. 0.0) THEN
          TAQFE = VOQNH
          LSP0  = VOQNH
        ELSE
          TAQFE = VOQNH * ((288.16 -.00198127 * RXMISELE(3)) / 288.16)
     &                ** (-32.174 / (-.00198127 * (1.8 * 1716.)))
          LSP0 = TAQFE * ((TATEMP(1) + 273.16) /
     &                    (TATEMP(2) + 273.16))
     &                ** (-32.174 / (LATSLP(1) * (1.8 * 1716.)))
        ENDIF
        IF (ABS(LATSLP(1)) .GT. 0.00001) THEN
          LAPRESS(1) = LSP0 * ((TATEMP(1) + 273.16)/LATEMPK(1))
     &                **(.01041332/LATSLP(1))
        ELSE
          LAPRESS(1) = LSP0 * EXP((.01041332/LATEMPK(1))*LAATFLVL(1))
        ENDIF
C
        DO I = 2,(PATLEV-1)
          IF (ABS(LATSLP(I-1)) .GT. 0.00001) THEN
            LAPRESS(I) = LAPRESS(I-1) * (LATEMPK(I)/LATEMPK(I-1))
     &                  **(-.01041332/LATSLP(I-1))
          ELSE
            LAPRESS(I) = LAPRESS(I-1) * EXP((-.01041332/LATEMPK(I))*
     &                  (LAATFLVL(I)-LAATFLVL(I-1)))
          ENDIF
        ENDDO
C
C COA TO FIX TEMP/ALTIMETER PROBLEMS
C
C        I = 1
C        DO WHILE (RXMISELE(3) .GT. LAATFLVL(I+1))
C          I=1+1
C        ENDDO
C        IF (ABS(LATSLP(I)) .GT. 0.00001) THEN
C          TAQFE = LAPRESS(I) * ((TATEMP(2)+273.16)/LATEMPK(I))
C     &                  **(-.01041332/LATSLP(I))
C        ELSE
C          TAQFE = LAPRESS(I) * EXP((-.01041332/(TATEMP(2)+273.16))*
C     &                  (RXMISELE(3)-LAATFLVL(I)))
C        ENDIF
C
        LOQFE = TAQFE
        LQSTEP   = .TRUE.
        TCMAVQNH = .FALSE.
      ENDIF
C
CD VA200 Distances to reference runway G/P position
CR       CAE Calculations
C
CC    The position is calculated as a function
CC    of the lat/long of the runway end, the lat/long of
CC    the aircraft, the runway heading, and the runway length.
C
C
C     A/C latitude and longitude wrt runway end (deg)
C
      LDLAT  = RUPLAT - RXMISLAT(3)
      LDLON  = (RUPLON - RXMISLON(3)) * RUCOSLAT
C
C     Distance to end of reference runway (ft)
C
      LRWYDIST = SQRT (LDLAT*LDLAT + LDLON*LDLON) * PDEGTOFT
C
C     Heading of A/C to rwy end wrt to runway heading (rad)
C
      IF (ABS(LDLAT) .GT. 1.0E-20) THEN
        LPSID = ATAN2(LDLON,LDLAT)
      ELSEIF (LDLON .EQ. 0.0) THEN
        LPSID = LRWYHDG
      ELSEIF (LDLON .LT. 0.0) THEN
        LPSID = - (PI/2.)
      ELSE
        LPSID = (PI/2.)
      ENDIF
C
      LDELPSI = (LPSID - LRWYHDG)
      IF (LDELPSI .GT.  PI) LDELPSI = LDELPSI - PI2
      IF (LDELPSI .LT. -PI) LDELPSI = LDELPSI + PI2
      IF (ABS(LDELPSI) .LT. 1E-20) LDELPSI = 0.0
C
C     Distances to runway G/P
C
      VXD = LRWYDIST * COS(LDELPSI) + LRWYL
      VYD = LRWYDIST * SIN(LDELPSI)
C
C
CD VA210 Check for Change in Windshear Type
CR       CAE Calculations
C
CC    If a windshear has been selected or deselected, then
CC    activate logic to prevent discontinuites in the A/C's
CC    airspeed.
C
      IF (TAWSPROF .NE. LOWSPROF) THEN
        LWCHANGE = .TRUE.
        LOWSPROF = TAWSPROF
      ENDIF
C
CD VA220  Natural windshear selected
C         --------------------------
CC If one of the six windshear profiles based on accident records
CC has been selected then fucntion generation is called to interpolate
CC the along runway, across runway and vertical wind magnitudes. The
CC six profiles are zoned so that only one can be active at a time.
C
      IF (TCMSHEAR .AND. TAWSPROF.GT.0) THEN
        IF (TAWSPROF .GT. 6) THEN
          VZONE2 = LZONE1(TAWSPROF-6)
          VCLASS2 = 1
          IF (.NOT. VFRZVFG) THEN
            CALL FLIGHTFG(FGENFILE)
          ENDIF
C
C -- Windshear components
C
          IF (TAWSGAIN .LT. .0001)TAWSGAIN=1.0
          VNSHR = VFNSHR * TAWSGAIN
          VESHR = VFESHR * TAWSGAIN
          VVSHR = VFVSHR * TAWSGAIN
          VUWSHR = 0.0
          VVWSHR = 0.0
          VWWSHR = 0.0
C
CD VA230  FAA windshear selected
CR        [1]  3.3-33
C         ----------------------
CC An FAA training windshear has been selected. Since windshear
CC activation is based on various triggers, the trigger conditions
CC are checked for the active windshear. If the windshear becomes
CC triggered, the reference ground distance is calculated and function
CC generation is called to interpolate the headwind, crosswind and
CC vertical components of wind. These components are then rotated
CC through the euler pitch and roll angles to provide body axis
CC components of wind. The windshears are automatically deactivated
CC if either flight or total freeze comes on or the aircraft is
CC repositioned.
C
        ELSEIF (TAWSPROF .LE. 6) THEN
C
C -- Triggers to activate windshears
C
          IF (TAWSPROF .EQ. 1) THEN                       ! Wind #1
            IF (VVE .GT. VVR-8.) LTRIGGER = .TRUE.        ! Prior to Vr
            LWFSHR = VKWSHR - .1
          ELSEIF (TAWSPROF .EQ. 2) THEN                   ! Wind #1
            IF (VTHETADG .GT. 3.) LTRIGGER = .TRUE.       ! Rotation
          ELSEIF (TAWSPROF .EQ. 3) THEN                   ! Wind #1
            IF (VH .LT. 300.) LTRIGGER = .TRUE.           ! Approach
            LWFSHR = VKWSHR + .2 ! TUNED WITH CUSTOMER
          ELSEIF (TAWSPROF .EQ. 4) THEN                  ! Wind #2
            IF (VH .GT. 200.) LTRIGGER = .TRUE.           ! Takeoff
            LWFSHR = VKWSHR - .315
          ELSEIF (TAWSPROF .EQ. 5) THEN                  ! Wind #3, tak
            IF (VH .GT. 100.) LTRIGGER = .TRUE.           ! Takeoff
            LWFSHR = VKWSHR - .6 ! TUNED WITH CUSTOMER
          ELSEIF (TAWSPROF .EQ. 6) THEN                  ! Wind #4, app
            IF (VH .LT. 1200.) LTRIGGER = .TRUE.          ! Approach
            LWFSHR = VKWSHR
          ENDIF
C
C -- Condition to calculate windshear components and reference
C    ground distance
C
          IF (LTRIGGER .AND. .NOT.LPTRIGGR) LFLAG = .TRUE.
          IF (RUFLT .OR. TCFFLPOS .OR. TCFTOT) LFLAG = .FALSE.
          LPTRIGGR = LTRIGGER
C
C -- Windshear components
C
          IF (LFLAG) THEN
            VXREF  = VXREF + YITIM*VUG
            VZONE2 = LZONE2(TAWSPROF)
            VCLASS2 = 2
            IF (.NOT. VFRZVFG) THEN
              CALL FLIGHTFG(FGENFILE)
            ENDIF
            IF (TAWSGAIN .LT. .0001)TAWSGAIN=1.0
            LSP0 = TAWSGAIN
            LSP1 = LSP0 * VFUWSHR
            LSP2 = LSP0 * VFVWSHR
            VUWSHR = (LSP1*VLXB0 + LSP2*VMXB0 + VFWWSHR*VNXB0) * LWFSHR
            VVWSHR = (LSP2*VMYB0 + VFWWSHR*VNYB0) * LWFSHR
            VWWSHR = (LSP1*VLZB0 + LSP2*VMZB0 + VFWWSHR*VNZB0) * LWFSHR
          ELSE
            LTRIGGER = .FALSE.
            LPTRIGGR = .FALSE.
            VXREF  = 0.0
            VUWSHR = 0.0
            VVWSHR = 0.0
            VWWSHR = 0.0
          ENDIF
          VNSHR = 0.0
          VESHR = 0.0
          VVSHR = 0.0
        ENDIF
C
CD VA240  Windshear not selected
C         ----------------------
CC If neither form of windshear or microburst is active, all resulting
CC wind components are zeroed.
C
      ELSE
        LFLAG = .FALSE.
        LTRIGGER = .FALSE.
        LPTRIGGR = .FALSE.
        VXREF = 0.0
        VESHR = 0.0
        VNSHR = 0.0
        VVSHR = 0.0
        VUWSHR = 0.0
        VVWSHR = 0.0
        VWWSHR = 0.0
      ENDIF
C
CD VA260 Temperature at Aircraft (deg C)
CR       CAE Calculations
C
CC    The temperature at the aircraft is calculated based
CC    on the aircraft's altitude segment.  The parameter
CC    is filtered to prevent motion bumps caused by dis-
CC    continuities.
C
      IF (VHS .LT. -1000) THEN
        LHS = -1000.0
      ELSEIF (VHS .GT. 100000.0) THEN
        LHS = 100000.0
      ELSE
        LHS = VHS
      ENDIF
C
      DO WHILE (LHS .GT. LAATFLVL(IT + 1))
        IT = IT + 1
      ENDDO
      DO WHILE (LHS .LT. LAATFLVL(IT))
        IT = IT - 1
      ENDDO
C
      LTEMP = LATINT(IT) + LATSLP(IT) * LHS
      LTEMPK = LTEMP + 273.16
C
      IF (LTSTEP) THEN
        LALTSET = .TRUE.
        LSP0 = LTEMP - VTEMP
        IF ((ABS(LSP0) .LT. LDTEMP) .OR. HATGON) THEN
          VTEMP  = LTEMP
          LTSTEP = .FALSE.
        ELSE
          IF (LSP0 .GT. 0.0) THEN
            VTEMP = VTEMP + LDTEMP
            TCMAVTMP = .TRUE.
          ELSE
            VTEMP = VTEMP - LDTEMP
            TCMAVTMP = .TRUE.
          ENDIF
        ENDIF
      ELSE
        VTEMP = LTEMP
      ENDIF
C
CD VA270 Wind Speed at Aircraft (Kts)
CR       CAE Calculations
C
CC    The wind speed at the aircraft is calculated based
CC    on the aircraft's altitude segment.  If the wind
CC    exponential decay is active, then the wind speed is
CC    reduced at altitudes below 30 feet above ground.
C
      DO WHILE (LHS .GT. LAWSFLVL(IS + 1))
        IS = IS + 1
      ENDDO
      DO WHILE (LHS .LT. LAWSFLVL(IS))
        IS = IS - 1
      ENDDO
C
      VOAWSPD = LWSINT(IS) + LWSSLP(IS) * LHS
C
      IF (VH .LT. 30.0) THEN
         IF (.NOT. LWNOEXP) THEN
C
C            VWIND = VOAWSPD * (1.0 - EXP(-0.1 * VH))* PKTSTOFS
C
             IF (VH .LT. 10) THEN
               VWIND = VOAWSPD * VH * .067 * PKTSTOFS
             ELSE
               VWIND = VOAWSPD * (.505 + VH * .0165) * PKTSTOFS
             ENDIF
         ELSE
            VWIND = VOAWSPD * PKTSTOFS
         ENDIF
      ELSE
         VWIND = VOAWSPD * PKTSTOFS
      ENDIF
C
      TAAWSPD = VOAWSPD
C
CD VA280 Wind Direction at Aircraft (Deg)
CR       CAE Calculations
C
CC    The wind direction at the aircraft is calculated based
CC    on the aircraft's altitude segment.  The wind direction
CC    is limited so that it is between 0 and 360 degrees.
C
      DO WHILE (LHS .GT. LAWDFLVL(ID + 1))
        ID = ID + 1
      ENDDO
      DO WHILE (LHS .LT. LAWDFLVL(ID))
        ID = ID - 1
      ENDDO
C
      VPSIW = LWDINT(ID) + LWDSLP(ID) * LHS + RTAVAR
C
      IF (VPSIW .LE. -180.0) THEN
        VPSIW  = VPSIW + 360.0
      ELSE IF (VPSIW .GT. 180.0) THEN
        VPSIW  = VPSIW - 360.0
      ENDIF
C
CD VA290 Temperature at Aircraft (degrees K)
CR       CAE Calculations
C
CC    The temperature at the aircraft is converted from degrees
CC    Celcius to degrees Kelvin.  The I/F label used to display
CC    temperature at the aircraft is also updated.
C
      VTEMPK   = VTEMP + 273.16
      TAOAT    = VTEMP
C
CD VA295 Pressure altitude at Aircraft
CR       D613N109 page 55
C
CC    The pressure is calculated at the aircraft in inches mercury.
CC    The difference between true altitude and pressure altitude is
CC    calculated.
C
      IF (ABS(LATSLP(IT)) .GT. 0.00001) THEN
        LPRESS = LAPRESS(IT) * (LTEMPK/LATEMPK(IT))
     &                  **(-.01041332/LATSLP(IT))
      ELSE
        LPRESS = LAPRESS(IT) * EXP(-.01041332/LTEMPK *
     &                  (VHS -LAATFLVL(IT)))
      ENDIF
C
      IF (LQSTEP) THEN
        LALTSET = .TRUE.
        LSP0 = LPRESS - LVPRESS
        IF ((ABS(LSP0) .LT. LDPRES) .OR. HATGON) THEN
          LVPRESS = LPRESS
        ELSE
          IF (LSP0 .GT. 0.0) THEN
            LVPRESS = LVPRESS + LDPRES
          ELSE
            LVPRESS = LVPRESS - LDPRES
          ENDIF
        ENDIF
      ELSE
        LVPRESS = LPRESS
      ENDIF
C
      IF ( LVPRESS .GT. 6.68323876) THEN
        LVHH = 145442.156 * (1. - (LVPRESS*(1./29.9212598))**.190263105)
      ELSE
        LVHH = 36089.2388 + 20805.8257 * LOG(6.68323876/LVPRESS)
      ENDIF
C
      VHP = LVHH - VHS
C
CD VA297 Adjustment of Height Above Sea Level
CR       N/A
C
CC If the A/C is above 5000 ft above sea level and a change in
CC temperature or pressure has occured, then the height above
CC sea level is adjusted to keep the A/C at the same pressure
CC altitude.
C
      IF (LALTSET.AND.(.NOT.HATGON)) THEN
        IF ((VHS .GT. 5000.0) .AND. (VH .GT. 100.0)) THEN
          VHS = VHS - (VHP - LHPP)
        ENDIF
        LALTSET = .FALSE.
      ENDIF
      LHPP = VHP
C
CD VA300 Speed of Sound (sec/ft)
CR       CAE Calculations
C
CC    Calculate the speed of sound as a function
CC    of outside air temperature.
C
C      VA  = SQRT(PGAMMA*PR*VTEMPK)
C
      VA = (VA + VTEMPK * PGAMMA * PR * VAI) * .5
      VAI = 1. / VA
C
CD VA0310  Wind Gust Model
CR         CAE Calculations
C
CC Activation of the gust model simply feeds in a random wind
CC speed and direction variation into the overall wind.
C
      IF ((TAGUST.GT.0.) .OR. LGUST) THEN
C
C ----- Wind gust initialization condition
C
          IF (TAGUST.LE.0.)THEN
            IGCNT    = 0                 ! Gust iteration counter
            LGUSTINT = 0.                ! Wind gust intensity
          ELSEIF ((.NOT. LGUST) .OR. (TAGUST .NE. LPGUST)) THEN
            LGUST  = .TRUE.              ! Wind gust activation flag
            IGCNT    = 0                 ! Gust iteration counter
            LGUSTINT = TAGUST * 0.04     ! Wind gust intensity
            LPGUST   = TAGUST            ! Previous wind gust intensity
          ENDIF
C
C ----- New velocity, direction & duration of wind gust
C
          IF (IGCNT .LE. 0) THEN
            LSP0 = YLGAUSN(4)
            IF (LSP0 .LT. -1.)THEN
              LSP0 = -1.
            ELSEIF (LSP0 .GT. 1.)THEN
              LSP0 = 1.
            ENDIF
            LNEWGUST = LSP0 * ( LGUSTMAX * LGUSTINT )
C
            IF (VH .LT. 30.) THEN
C
C               LNEWGUST = LNEWGUST * (1. - EXP(-0.1 * VH))
C
               IF ( VH .LT. 10.) THEN
                 LNEWGUST = LNEWGUST * VH * .067
               ELSE
                 LNEWGUST = LNEWGUST * (.505 + VH * .0165)
               ENDIF
            ENDIF
C
            LSP0 = YLGAUSN(5)
            IF (LSP0 .LT. -1.)THEN
              LSP0 = -1.
            ELSEIF (LSP0 .GT. 1.)THEN
              LSP0 = 1.
            ENDIF
            LNEWPSIG   = LSP0 * LPSIGMAX * LGUSTINT
C
            LSP0 = ABS(YLGAUSN(6))
            IF (LSP0.LT.0.1) LSP0=0.1
            IGCNT = LSP0 * IGFREQ
          ENDIF
C
C ----- Fade in wind gust parameters
C
          IF (LGUST) THEN
            VWGUST = VWGUST + (LNEWGUST - VWGUST) * LKGUST
            LPSIG  = LPSIG  + (LNEWPSIG - LPSIG)  * LKGUST
          ENDIF
C
C ----- Stop wind gust computation
C
          IF (( TAGUST .LE.0.) .AND. (VWGUST .LE. 0.5)) THEN
            VWGUST   = 0.0
            LPSIG    = 0.0
            LGUST  = .FALSE.
          ENDIF
C
C ----- Gust iteration counter
C
          IGCNT = IGCNT - 1
      ENDIF
C
CD VA320 Check for Change in Microburst Type
CR       CAE Calculations
C
CC    If a microburst has been selected or deselected, then
CC    activate logic to prevent discontinuites in the A/C's
CC    airspeed.
C
      IF (TAMBPROF .NE. LOMBPROF) THEN
        IF (TCMBURST) LWCHANGE = .TRUE.
        LOMBPROF = TAMBPROF
      ENDIF
      IF (TCMBURST .XOR. LCMBURST) THEN
        IF (TAMBPROF.GT.0) LWCHANGE = .TRUE.
        LCMBURST= TCMBURST
      ENDIF
C
CD VA330 Check for Microburst Being Active
CR       CAE Calculations
C
CC    If no microburst is active, the microburst outputs are reset
CC    to zero. Otherwise, call the MICROBURST subroutine
C
      IF ((TAMBPROF .EQ. 0) .OR. (.NOT. TCMBURST))THEN
        VNMB     = 0.0
        VEMB     = 0.0
        VVMB     = 0.0
      ELSE
C
C   MICROBURST LAT,LON
C
        LLCOU = LLCOU + 1
        IF (LLCOU .GE. 30) THEN
          LLCOU = 0
          LRUNLEN = (RXMISRLE(3)-RXMISGPX(3))/6076.115
          TAMBLAT = RXMISLAT(3) + ((-TAMBXOFF - LRUNLEN)*VCSRW
     &             - TAMBYOFF * VSNRW) * (1/60.)
C
          LSP1 = COS(TAMBLAT*PDGTORD)
          IF (LSP1 .LT. .01 ) LSP1 = .01
          TAMBLON = RXMISLON(3) + ((-TAMBXOFF - LRUNLEN)*VSNRW
     &             + TAMBYOFF * VCSRW)*(1/60.)/LSP1
C
        ENDIF
C
        IF (TAMBPROF .EQ. 6) THEN
          VSHLTURB = 1.
          VRAE = .TRUE.
        ELSE
          VRAE = .FALSE.
          VSHLTURB = 0.
        ENDIF
        IF (VRAE) THEN
          CALL RAE
        ELSE
          CALL MICROB
        ENDIF
C
CD VA340 Microburst Intensity Factor
CR       CAE Calculations
C
CC    The microburst components are scaled by an I/F selectable
CC    intensity factor.  The factor is filtered to prevent
CC    motion bumps caused by discontinuities.
C
        IF (TAMBGAIN .NE. LOMBGAIN) THEN
          LWCHANGE = .TRUE.
          LOMBGAIN = TAMBGAIN
        ENDIF
      ENDIF
C
CD VA350 Downbursts
CR       CAE Calculations
C
CC    A downburst is a strong downdraft of wind for a limited
CC    duration.
C
      IF (TCMDBURS) THEN
        IF (LBURSTFP) THEN
          LBURST   = .TRUE.
          LBURSTD  = 0.0
          LBURSTFP = .FALSE.
        ENDIF
        IF (LBURSTD .LT. (TABSTDUR * 2.0)) THEN
          LBURSTD = LBURSTD + YITIM
          IF ((TABSTINT * 0.5 - VVDB) .LT. LFADE1) THEN
            VVDB = VVDB + LFADE1
          ELSE
            VVDB = TABSTINT * 0.5
          ENDIF
        ELSE
          TCMDBURS = .FALSE.
        ENDIF
      ELSE
        IF (LBURST) THEN
          LBURSTFP = .TRUE.
          LBURSTD  = 0.0
          IF (VVDB .GT. 0.0) THEN
            VVDB = VVDB - LFADE1
          ELSE
            VVDB   = 0.0
            LBURST = .FALSE.
          ENDIF
        ENDIF
      ENDIF
C
CD VA360 Wind and Wind Gust Parameter Summation
CR       CAE Calculations
C
      VWIND    = VWIND + VWGUST
      LPSIWRD  = (VPSIW + LPSIG) * PDGTORD
C
C -- Sine and cosine of wind direction
C
      VCSWDIR = COS(LPSIWRD)
      VSNWDIR = SIN(LPSIWRD)
C
CD VA370 Microburst and Windshear Vector Summation
CR       CAE Calculations
C
      IF ( VRAE) THEN
        LSHRNS   = VNSHR
        LSHREW   = VESHR
        LSHRV    = VVSHR
      ELSE
        LSHRNS   = VNSHR + VNMB
        LSHREW   = VESHR + VEMB
        LSHRV    = VVSHR + VVMB
      ENDIF
C
CD VA380 ATG Wind Inputs
CR       CAE Calculations
C
CC    The ATG wind inputs are resolved into north-south
CC    and east-west components.  The wind vector can be given as
CC    a magnitude and direction (HNWIND and HWDIR), or as two
CC    perpendicular vectors (HNWIND and HEWIND) with HWDIR
CC    used as an offset.
C
C     Sign convention:
C             HNWIND - pos from north
C                    - scaler when HEWIND not used, HWDIR
C                      takes care of dirction
C             HEWIND - pos from east
C             VWNS  - pos from north
C             VWEW  - pos from east
C             HWDIR  - angle from true north to HNWIND
C                    - used as offset wind HEWIND is used
C
      IF (HATGON .OR. HMAN) THEN
        LSP0  = (HWDIR + HWDIROFF) * PDGTORD
        LSP1  = COS(LSP0)
        LSP2  = SIN(LSP0)
        LNWIND = (HNWIND * LSP1 - HEWIND * LSP2) * PKTSTOFS
        LEWIND = (HNWIND * LSP2 + HEWIND * LSP1) * PKTSTOFS
        LVWIND = HVWIND * PKTSTOFS
      ELSE
        LNWIND = 0.
        LEWIND = 0.
        LVWIND = 0.
      ENDIF
C
CD VA390 Wind Speeds w.r.t. Ground
CR       CAE Calculations
C
CC    The total north-south, east-west, and vertical wind
CC    components are calculated as a function of the steady
CC    wind, the windshear and microburst components, and
CC    the ATG winds.
CC    RAE Bedford Microbursts are computed in earth axis system
CC    and thus are summed here.
C
      IF (VRAE )THEN
        LVWNS = VWIND*VCSWDIR + LSHRNS*VCSRW - LSHREW*VSNRW + LNWIND
     &          - VNMB
        LVWEW = VWIND*VSNWDIR + LSHREW*VCSRW + LSHRNS*VSNRW + LEWIND
     &          - VEMB
        LVWD  = LSHRV - VVDB + LVWIND - VVMB
      ELSE
        LVWNS = VWIND*VCSWDIR + LSHRNS*VCSRW - LSHREW*VSNRW + LNWIND
        LVWEW = VWIND*VSNWDIR + LSHREW*VCSRW + LSHRNS*VSNRW + LEWIND
        LVWD  = LSHRV - VVDB + LVWIND
      ENDIF
C
CD VA400 Wind Input Fading
CR       CAE Calculations
C
CC       If a change in wind conditions has occured, then
CC       the steps in the north-south, east-west, and vertical
CC       winds are faded in over a fixed time interval.
C
      IF (LWCHANGE) THEN
        LVWNSD   = LVWNS - VVWNS
        LVWEWD   = LVWEW - VVWEW
        LVWDD    = LVWD  - VVWD
        LFADE    = 1.0
        LWCHANGE = .FALSE.
      ENDIF
C
      IF (LFADE .GT. 0.0) THEN
        IF (HATGON) THEN
          LFADE = 0.0
        ELSE
          LFADE = LFADE - LDFADE
          VAIRSET = .TRUE.
        ENDIF
        VVWNS  = LVWNS - LVWNSD * LFADE
        VVWEW  = LVWEW - LVWEWD * LFADE
        VVWD   = LVWD  - LVWDD  * LFADE
      ELSE
        VVWNS = LVWNS
        VVWEW = LVWEW
        VVWD  = LVWD
      ENDIF
C
CD VA410 Wind Parameters in X-Y Ground Axis Plan.
CR       CAE Calculations
C
CC    The magnitude and direction of the total horizontal
CC    wind are calculated as a function of the north-south
CC    and east-west components.
C
      VTOTWIND = SQRT ( VVWNS * VVWNS + VVWEW * VVWEW )
      IF (VTOTWIND .NE. 0) THEN
        VTOTWDIR = ATAN2 ( VVWEW , VVWNS) * PRDTODG
      ELSE
        VTOTWDIR = 0.0
      ENDIF
C
CD VA420 U,V,W Components (Body Axis)
CR       CAE Calculations
C
CC Wind velocities are transformed into the body axes system
CC and added to the windshear component (for FAA shears) to
CC yield the total body axis wind component.
C
      LUW      = VUW
      LVW      = VVW
      LWW      = VWW
C
      VUW      = VLXB * VVWNS + VMXB * VVWEW + VNXB * VVWD + VUWSHR
      VVW      = VLYB * VVWNS + VMYB * VVWEW + VNYB * VVWD + VVWSHR
      VWW      = VLZB * VVWNS + VMZB * VVWEW + VNZB * VVWD + VWWSHR
C
CD VA0425  Any Changes in Wind Conditions
CR         CAE Calculations
C
      IF (VAIRSET) THEN
        IF (.NOT.VBOG .AND. .NOT. HATGON) THEN
          VUG      = VUG + LUW - VUW
          VVG      = VVG + LVW - VVW
          VWG      = VWG + LWW - VWW
          VVEW     = VMXB * VUG + VMYB * VVG + VMZB * VWG
          VVNS     = VLXB * VUG + VLYB * VVG + VLZB * VWG
        ENDIF
        VAIRSET = .FALSE.
      ENDIF
C
CD VA4260 Output for plotting windspeeds
C
       VNSHRKTS = (VLXB * VUW + VLYB * VVW + VLZB * VWW)/PKTSTOFS
       VESHRKTS = (VMXB * VUW + VMYB * VVW + VMZB * VWW)/PKTSTOFS
       VVSHRKTS = (VNXB * VUW + VNYB * VVW + VNZB * VWW)
C
CD VA430 Reset module initialization flag
C
      IF (VAINIT) VAINIT = .FALSE.
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00506 VA010 First Pass Initialization
C$ 00545 VA020 Reset to Standard Atmosphere
C$ 00593 VA030 Effect of Change of Temperature at A/C
C$ 00608 VA040 Effect of Change of Wind Speed at A/C
C$ 00623 VA050 Effect of Change of Wind Direction at A/C
C$ 00654 VA060 Change in Atmosphere Conditions
C$ 00662 VA070 Change in I/F Temperature Levels
C$ 00724 VA080 Change in I/F Temperatures
C$ 00754 VA090 Change in I/F Wind Speed Levels
C$ 00784 VA100 Change in I/F Wind Speeds
C$ 00814 VA110 Change in I/F Wind Direction Levels
C$ 00844 VA120 Change in I/F Wind Directions
C$ 00886 VA130 Update Temperature Profile
C$ 00912 VA140 Update Wind Speed Profile
C$ 00936 VA150 Update Wind Direction Profile
C$ 00960 VA160 Update Sea Level and Airfield Parameters
C$ 00994 VA170 Effect of Change in Reference Runway
C$ 01031 VA180  Change in Field Barometric Pressure
C$ 01056 VA190  Sea Level Barometric Pressure (Hg)
C$ 01132 VA200 Distances to reference runway G/P position
C$ 01172 VA210 Check for Change in Windshear Type
C$ 01184 VA220  Natural windshear selected
C$ 01209 VA230  FAA windshear selected
C$ 01282 VA240  Windshear not selected
C$ 01300 VA260 Temperature at Aircraft (deg C)
C$ 01345 VA270 Wind Speed at Aircraft (Kts)
C$ 01381 VA280 Wind Direction at Aircraft (Deg)
C$ 01403 VA290 Temperature at Aircraft (degrees K)
C$ 01413 VA295 Pressure altitude at Aircraft
C$ 01452 VA297 Adjustment of Height Above Sea Level
C$ 01468 VA300 Speed of Sound (sec/ft)
C$ 01479 VA0310  Wind Gust Model
C$ 01554 VA320 Check for Change in Microburst Type
C$ 01570 VA330 Check for Microburst Being Active
C$ 01611 VA340 Microburst Intensity Factor
C$ 01624 VA350 Downbursts
C$ 01659 VA360 Wind and Wind Gust Parameter Summation
C$ 01670 VA370 Microburst and Windshear Vector Summation
C$ 01683 VA380 ATG Wind Inputs
C$ 01715 VA390 Wind Speeds w.r.t. Ground
C$ 01737 VA400 Wind Input Fading
C$ 01768 VA410 Wind Parameters in X-Y Ground Axis Plan.
C$ 01782 VA420 U,V,W Components (Body Axis)
C$ 01797 VA0425  Any Changes in Wind Conditions
C$ 01811 VA4260 Output for plotting windspeeds
C$ 01817 VA430 Reset module initialization flag
