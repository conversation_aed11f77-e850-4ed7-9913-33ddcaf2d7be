/*
*
*                 Common Data Base MOTION
*
*'Revision_History
* 30 May 93 Jean  Set JSTNDBYVEL to 1.5(was -10.) and IAOFS to 0.(was -300.)
* 15 nov 92 <PERSON>    removed visual resonant filter - not needed on usd8
* 11 may 92 Norm Tuned mksofton for softer stops. NOTE: m_jlag should be .005.
* 09 may 92 Norm   Increased current error threshold
* 13 apr 92  Norm  Tuned visual filter  ****** should be in fortran CDB ***
* 13 MAR 92  MCT   ADDED ,'S AFTER MOGTHE,PHI,PSI... TO COMPILE
* 11 MAR 92  NORM  ADDED NUMEROUS LABELS FOR INIT OF C30:
*                  MB<PERSON><PERSON> MBXFRDIS MOBGCKS MOBDCKS MOXFADE MONLSCL  
*                  MOPHCR MOGBUNT MOVISF1 MOVISF2  MOXWEI1  MOXWEI2  
*                  MOXWEI3 MOYWEI1 MOYWEI2 MOYWEI3 MOZWEI1 MOZWEI2  
*                  MOZWEI3 MOGXA MOGXG MOGYA MOGYG MOGZA MOGZG    
*                  MOWHPHI MOWHTHE MOWHPSI MOGPHI MOGTHE MOGPSI  
*                  MOXPH MOZPH MOXP MOZP MOLPXMAX MOLPXMIN 
*		   ***** TO BE USED WITH MINI REV 2 *****
*		   ***** TO BE USED WITH DFX REV 7 *****
*
*	05 mar 92 Norm Changed labels MOFDSTRA/D to MOFDSTA/D
*		   	to match new host cdb names.
*
*	28 feb 92 Norm Added labels MBAMP1F-5F, MBAML1F-5F for
*	               checksum error protection. 
*		       Added MOBGCKS, MOBDCKS for buffet checksum
*
*	26 feb 92 NORM Added PICH_POS for c/l mass umbalance
*
*	07 feb 92 NORM  Removed spare I/F labels to allow compilation of motion
*		        ( there are too many labels for compilation on IBM )
*			Added TANTHES to XRF for MOTION to use it.
*	05 feb 92 NORM  MJFAD changed from 1.0 to .2 so washout not felt.
*			
*
*/
/*
C'Revision_History
*/
#include "mot_define.h"
/*
*    ----------
**   Dispatcher
*    ----------
*/
float
YITIM        = 0.0005,		/* BAND TIME [sec] set by dispatcher every band */
YIFREQ     = 2000.0,          /* FAST BAND FREQUENCY [hz] */
MAXT_30 = 0.0,	       		/* 30 hz band maximum time */
MAXT_500 = 0.0,			/* 500 hz band maximum time */
MAXT_2000 = 0.0,			/* 2000 hz band maximum time */
MTIMER = 0.0,                             	/* MOTION FREEZE TIMER */
MKWASH = .9997;		        /* washout factor: 6 sec time constant*/
int
MOVERRUN = 0,			/* SYSTEM OVERRUN FLAG */
MOUTSTATE = 1,			/* motion output state */
WASHEDOUT = FALSE,		/* motion washout completed flag */
MWASH=FALSE,	        	/* MOTION WASHOUT REQUEST FLAG */
MOT_FREEZEREQ=0;			/* MOTION FREEZE REQUEST */
/*
*    -----------------------------------
**   Simulator configuration AND options
*    -----------------------------------
*/

int
MOPTION=0,			/* options received flag */
SITE=3,				/* site: 1:CTS TEST 2:HTF TEST 3:QANTAS SITE */
MABORTMAN=0,			/* abort manifold available on this motion */
MOTYPE=2,                       /* motion type 1=3oo2=5oo3=55o4=75o5=6oo6=8oo*/
MPRESTYPE=0,			/* pressure transducer types P300=1,P600=2 */
MTACCEL=0,			/* accelerometer package */
MTLIMITED=0,			/* freq limited for protection*/
MVALVETYPE=0,                   /* motion valve type: 1=MOOG, 0=PEGASUS */
MFORCESCAL=0,			/* force transducer scaling: F11250=0,F20000=1*/
MPOSSCAL=0;			/* position transducer scaling: P9=0,F10=1*/
float
CAP_AREA = 0.0,			/* motion piston cap end area [inch2] */
ROD_AREA = 0.0;			/* motion piston rod end area [inch2] */
/*
* 	VISUAL SYSTEM RESONANCE FILTER CUTOFF FREQUENCIES
*/
float MVRESNF1 = 0.;  /* visual resonance: notch filter freq 1 was 12*/
float MVRESNF2 = 0.;  /* visual resonance: notch filter freq 2 was 13*/
/*
*         --------------
**        INPUTS OUTPUTS
*         --------------
*/
int
ADIO_AIP1[8] = {0,0,0,0,0,0,0,0},	/* ADIO #1 AIPS XA2*/
ADIO_DIP1    = 0,			/* ADIO #1 DIP XA2*/
ADIO_AIP2[8] = {0,0,0,0,0,0,0,0},	/* ADIO #2 AIPS XA3*/
ADIO_DIP2    = 0,			/* ADIO #2 DIP XA3*/
ADIO_AIP3[8] = {0,0,0,0,0,0,0,0},	/* ADIO #3 AIPS XA4*/
ADIO_DIP3    = 0,			/* ADIO #3 DIP XA4*/
ADIO_AIP4[8] = {0,0,0,0,0,0,0,0},	/* ADIO #4 AIPS XA5*/
ADIO_DIP4    = 0,			/* ADIO #4 DIP XA5*/
ADIO_AIP5[8] = {0,0,0,0,0,0,0,0},	/* ADIO #5 AIPS XA6*/
ADIO_DIP5    = 0,			/* ADIO #5 DIP XA6*/

ADIO_AOP1[8] = {0,0,0,0,0,0,0,0},	/* ADIO #1 AOPS XA2*/
ADIO_DOP1    = 0,			/* ADIO #1 DOP XA2*/
ADIO_AOP2[8] = {0,0,0,0,0,0,0,0},	/* ADIO #2 AOPS XA3*/
ADIO_DOP2    = 0,			/* ADIO #2 DOP XA3*/
ADIO_AOP3[8] = {0,0,0,0,0,0,0,0},	/* ADIO #3 AOPS XA4*/
ADIO_DOP3    = 0,			/* ADIO #3 DOP XA4*/
ADIO_AOP4[8] = {0,0,0,0,0,0,0,0},	/* ADIO #3 AOPS XA5*/
ADIO_DOP4    = 0,			/* ADIO #4 DOP XA5*/
ADIO_AOP5[8] = {0,0,0,0,0,0,0,0},	/* ADIO #3 AOPS XA6*/
ADIO_DOP5    = 0,			/* ADIO #5 DOP XA6*/

IO_STATUS1 = FALSE,			/* MOTION ADIO STATUS , XA2 */
IO_STATUS2 = FALSE,			/* MOTION ADIO STATUS , XA3 */
IO_STATUS3 = FALSE,			/* MOTION ADIO STATUS , XA4 */
IO_STATUS4 = FALSE,			/* MOTION ADIO STATUS , XA5 */
IO_STATUS5 = FALSE,			/* MOTION ADIO STATUS , XA6 */

IO_FSTATUS1 = FALSE,			/* MOTION ADIO FREEZE STATUS , XA2 */
IO_FSTATUS2 = FALSE,			/* MOTION ADIO FREEZE STATUS , XA3 */
IO_FSTATUS3 = FALSE,			/* MOTION ADIO FREEZE STATUS , XA4 */
IO_FSTATUS4 = FALSE,			/* MOTION ADIO FREEZE STATUS , XA5 */
IO_FSTATUS5 = FALSE,			/* MOTION ADIO FREEZE STATUS , XA6 */

READ_ADIO1 = FALSE,                     /* motion adio read enable, XA2 */
READ_ADIO2 = FALSE,                     /* motion adio read enable, XA3 */
READ_ADIO3 = FALSE,                     /* motion adio read enable, XA4 */
READ_ADIO4 = FALSE,                     /* motion adio read enable, XA5 */
READ_ADIO5 = FALSE,                     /* motion adio read enable, XA6 */

WRITE_ADIO1 = FALSE,			/* motion adio write enable XA2*/
WRITE_ADIO2 = FALSE,			/* motion adio write enable XA3*/
WRITE_ADIO3 = FALSE,			/* motion adio write enable XA4*/
WRITE_ADIO4 = FALSE,			/* motion adio write enable XA5*/
WRITE_ADIO5 = FALSE,			/* motion adio write enable XA6*/

MABORTDOP= FALSE,			/* abort manifold dop */
MPOWEROFFDOP= FALSE;			/* cabinet power off dop */
/*
*         -----------------------
**        Motion servo controller
*         -----------------------
*/
/* &BEGIN_SERVO& */             /* begin defines for macro */
int
HOSTUPDT= FALSE;                       /*  motion command update flag */
/*
*           --------------------
**          acceleration command
*           --------------------
*/
float
J1XC   = 0.0,                          /* input jack pos command */
J2XC   = 0.0,                          /* input jack pos command */
J3XC   = 0.0,                          /* input jack pos command */
J4XC   = 0.0,                          /* input jack pos command */
J5XC   = 0.0,                          /* input jack pos command */
J6XC   = 0.0,                          /* input jack pos command */
J1XCD   = 0.0,                         /* pos interpolator rate */
J2XCD   = 0.0,                         /* pos interpolator rate */
J3XCD   = 0.0,                         /* pos interpolator rate */
J4XCD   = 0.0,                         /* pos interpolator rate */
J5XCD   = 0.0,                         /* pos interpolator rate */
J6XCD   = 0.0,                         /* pos interpolator rate */
J1XCF   = 0.0,                         /* interpolated position */
J2XCF   = 0.0,                         /* interpolated position */
J3XCF   = 0.0,                         /* interpolated position */
J4XCF   = 0.0,                         /* interpolated position */
J5XCF   = 0.0,                         /* interpolated position */
J6XCF   = 0.0,                         /* interpolated position */
J1XAINP   = 0.,                /* jack position transducer input [IN*/
J2XAINP   = 0.,                /* jack position transducer input [IN]*/
J3XAINP   = 0.,                /* jack position transducer input [IN]*/
J4XAINP   = 0.,                /* jack position transducer input [IN]*/
J5XAINP   = 0.,                /* jack position transducer input IN]*/
J6XAINP   = 0.,                /* jack position transducer input [IN]*/
J1XAC   = 0.0,                         /* actual position scaled */
J2XAC   = 0.0,                         /* actual position scaled */
J3XAC   = 0.0,                         /* actual position scaled */
J4XAC   = 0.0,                         /* actual position scaled */
J5XAC   = 0.0,                         /* actual position scaled */
J6XAC   = 0.0,                         /* actual position scaled */
J1XAOFS   = 0.0,                       /* position transducer offsets */
J2XAOFS   = 0.0,                       /* position transducer offsets */
J3XAOFS   = 0.0,                       /* position transducer offsets */
J4XAOFS   = 0.0,                       /* position transducer offsets */
J5XAOFS   = 0.0,                       /* position transducer offsets */
J6XAOFS   = 0.0,                       /* position transducer offsets */
J1AC   = 0.0,                          /* input jack acc command */
J2AC   = 0.0,                          /* input jack acc command */
J3AC   = 0.0,                          /* input jack acc command */
J4AC   = 0.0,                          /* input jack acc command */
J5AC   = 0.0,                          /* input jack acc command */
J6AC   = 0.0,                          /* input jack acc command */
J1ACD   = 0.0,                         /* acc interpolator rate */
J2ACD   = 0.0,                         /* acc interpolator rate */
J3ACD   = 0.0,                         /* acc interpolator rate */
J4ACD   = 0.0,                         /* acc interpolator rate */
J5ACD   = 0.0,                         /* acc interpolator rate */
J6ACD   = 0.0,                         /* acc interpolator rate */
J1ACF   = 0.0,                         /* interpolated acceleration */
J2ACF   = 0.0,                         /* interpolated acceleration */
J3ACF   = 0.0,                         /* interpolated acceleration */
J4ACF   = 0.0,                         /* interpolated acceleration */
J5ACF   = 0.0,                         /* interpolated acceleration */
J6ACF   = 0.0,                         /* interpolated acceleration */
J1AD   = 0.0,                          /* demanded acceleration rate */
J2AD   = 0.0,                          /* demanded acceleration rate  */
J3AD   = 0.0,                          /* demanded acceleration rate  */
J4AD   = 0.0,                          /* demanded acceleration rate  */
J5AD   = 0.0,                          /* demanded acceleration rate  */
J6AD   = 0.0,                          /* demanded acceleration rate  */
J1VCD   = 0.0,                          /* interpolator rate */
J2VCD   = 0.0,                          /* interpolator rate */
J3VCD   = 0.0,                          /* interpolator rate */
J4VCD   = 0.0,                          /* interpolator rate */
J5VCD   = 0.0,                          /* interpolator rate */
J6VCD   = 0.0,                          /* interpolator rate */
J1VCF   = 0.0,                          /* interpolated velocity */
J2VCF   = 0.0,                          /* interpolated velocity */
J3VCF   = 0.0,                          /* interpolated velocity */
J4VCF   = 0.0,                          /* interpolated velocity */
J5VCF   = 0.0,                          /* interpolated velocity */
J6VCF   = 0.0,                          /* interpolated velocity */
J1VC   = 0.0,                           /* demanded velocity */
J2VC   = 0.0,                           /* demanded velocity */
J3VC   = 0.0,                           /* demanded velocity */
J4VC   = 0.0,                           /* demanded velocity */
J5VC   = 0.0,                           /* demanded velocity */
J6VC   = 0.0,                           /* demanded velocity */
J1VCW0 = 0.0,                           /* demanded velocity washout */
VCLIM = 10000.,                         /* demanded velocity limit*/
J1VR   = 0.0,                           /* controller requested velocity */
J2VR   = 0.0,                           /* controller requested velocity */
J3VR   = 0.0,                           /* controller requested velocity */
J4VR   = 0.0,                           /* controller requested velocity */
J5VR   = 0.0,                           /* controller requested velocity */
J6VR   = 0.0,                           /* controller requested velocity */
VRLIM = 100.,                           /* controller requested velocity limit*/
J1AVXAC   = 0.0,                        /* average position */
J2AVXAC   = 0.0,                        /* average position */
J3AVXAC   = 0.0,                        /* average position */
J4AVXAC   = 0.0,                        /* average position */
J5AVXAC   = 0.0,                        /* average position */
J6AVXAC   = 0.0,                        /* average position */
J1SUMXAC  = 0.0,                        /* sum of all positions */
J2SUMXAC  = 0.0,                        /* sum of all positions */
J3SUMXAC  = 0.0,                        /* sum of all positions */
J4SUMXAC  = 0.0,                        /* sum of all positions */
J5SUMXAC  = 0.0,                        /* sum of all positions */
J6SUMXAC  = 0.0,                        /* sum of all positions */
VELAVCOUNT  = 0.0;                      /* number of iteration used for vel average*/
int
VELINPROG = 0,				/* velocity computing in progress flag */
VELFRQ = 20;				/* velocity subband factor (100hz) */
float
J1VAC   = 0.0,                          /* actual velocity */
J2VAC   = 0.0,                          /* actual velocity */
J3VAC   = 0.0,                          /* actual velocity */
J4VAC   = 0.0,                          /* actual velocity */
J5VAC   = 0.0,                          /* actual velocity */
J6VAC   = 0.0,                          /* actual velocity */
J1VACL   = 0.0,                         /* actual velocity lagged*/
J2VACL   = 0.0,                         /* actual velocity lagged*/
J3VACL   = 0.0,                         /* actual velocity lagged*/
J4VACL   = 0.0,                         /* actual velocity lagged*/
J5VACL   = 0.0,                         /* actual velocity lagged*/
J6VACL   = 0.0,                         /* actual velocity lagged*/
J1VE   = 0.0,                           /* velocity error */
J2VE   = 0.0,                           /* velocity error */
J3VE   = 0.0,                           /* velocity error */
J4VE   = 0.0,                           /* velocity error */
J5VE   = 0.0,                           /* velocity error */
J6VE   = 0.0,                           /* velocity error */
J1FAINP   = 0.0,                        /* jack force transducer input */
J2FAINP   = 0.0,                        /* jack force transducer input */
J3FAINP   = 0.0,                        /* jack force transducer input */
J4FAINP   = 0.0,                        /* jack force transducer input */
J5FAINP   = 0.0,                        /* jack force transducer input */
J6FAINP   = 0.0,                        /* jack force transducer input */
J1FAF   = 0.0,                          /* CO compensation term */
J2FAF   = 0.0,                          /* CO compensation term */
J3FAF   = 0.0,                          /* CO compensation term */
J4FAF   = 0.0,                          /* CO compensation term */
J5FAF   = 0.0,                          /* CO compensation term */
J6FAF   = 0.0,                          /* CO compensation term */
J1IC   = 0.0,                           /* commanded current */
J2IC   = 0.0,                           /* commanded current */
J3IC   = 0.0,                           /* commanded current */
J4IC   = 0.0,                           /* commanded current */
J5IC   = 0.0,                           /* commanded current */
J6IC   = 0.0,                           /* commanded current */
ICLIM = 65.,                      	/* commanded current limit [mAmp] */
J1ICOFS   = 5.0,                        /* commanded current offset */
J2ICOFS   = 5.0,                        /* commanded current offset */
J3ICOFS   = 5.0,                        /* commanded current offset */
J4ICOFS   = 5.0,                        /* commanded current offset */
J5ICOFS   = 5.0,                        /* commanded current offset */
J6ICOFS   = 5.0,                        /* commanded current offset */
IAOFS     = 0.0;                        /* actual current offset */
/*
*	Buffer unit signals
*/
int
J1BUDOP=4 ,                          /* */
J2BUDOP=4,                          /* */
J3BUDOP=4,                          /* */
J4BUDOP=4,                          /* */
J5BUDOP=4,                          /* */
J6BUDOP=4,                          /* */
J1BUDIP=4,                          /* */
J2BUDIP=4,                          /* */
J3BUDIP=4,                          /* */
J4BUDIP=4,                          /* */
J5BUDIP=4,                          /* */
J6BUDIP=4;                          /* */
/*
* 	Controller tuning gains
*/
float
J1KLS  = 0.,                     /* KL scaled */
J2KLS  = 0.,                     /* KL scaled */
J3KLS  = 0.,                     /* KL scaled */
J4KLS  = 0.,                     /* KL scaled */
J5KLS  = 0.,                     /* KL scaled */
J6KLS  = 0.,                     /* KL scaled */
J1KBETS   = .0,                  /* KBET scaled */
J2KBETS   = .0,                  /* KBET scaled*/
J3KBETS   = .0,                  /* KBET scaled*/
J4KBETS   = .0,                  /* KBET scaled*/
J5KBETS   = .0,                  /* KBET scaled*/
J6KBETS   = .0,                  /* KBET scaled*/
J1KMIDS   = .0,                  /* KBET scaled */
J2KMIDS   = .0,                  /*  KBET scaled*/
J3KMIDS   = .0,                  /* KBET scaled */
J4KMIDS   = .0,                  /* KBET scaled */
J5KMIDS   = .0,                  /* KBET scaled */
J6KMIDS   = .0,                  /* KBET scaled */
J1KVS   = 0.,                    /* KV scaled */
J2KVS   = 0.,                    /* KV scaled  */
J3KVS   = 0.,                    /* KV scaled  */
J4KVS   = 0.,                    /* KV scaled  */
J5KVS   = 0.,                    /* KV scaled  */
J6KVS   = 0.,                    /*  KV scaled  */
J1KCOS   = 0.0,                	/* KCO SCALED */
J2KCOS   = 0.0,                	/* KCO SCALED */
J3KCOS   = 0.0,                	/* KCO SCALED */
J4KCOS   = 0.0,                	/* KCO SCALED */
J5KCOS   = 0.0,                	/* KCO SCALED */
J6KCOS   = 0.0,                	/* KCO SCALED */
J1KCOGS   = 0.0,                 /* KCOG scaled */
J2KCOGS   = 0.0,                 /* KCOG scaled */
J3KCOGS   = 0.0,                 /* KCOG scaled */
J4KCOGS   = 0.0,                 /* KCOG scaled */
J5KCOGS   = 0.0,                 /* KCOG scaled */
J6KCOGS   = 0.0,                 /* KCOG scaled */
J1KCOWS   = .0,                  /* KCOW SCALED */
J2KCOWS   = .0,                  /* KCOW SCALED */
J3KCOWS   = .0,                  /* KCOW SCALED */
J4KCOWS   = .0,                  /* KCOW SCALED */
J5KCOWS   = .0,                  /* KCOW SCALED */
J6KCOWS   = .0,                  /* KCOW SCALED */
J1KCOWDEN   = 0.0,               /* CO compensation denominator */
J2KCOWDEN   = 0.0,               /* CO compensation denominator */
J3KCOWDEN   = 0.0,               /* CO compensation denominator */
J4KCOWDEN   = 0.0,               /* CO compensation denominator */
J5KCOWDEN   = 0.0,               /* CO compensation denominator */
J6KCOWDEN   = 0.0,               /* CO compensation denominator */
/*
* 	Motion commands
*/
J1MVC   = 0.0,                     /* FINAL JACK VELOCITY */
J2MVC   = 0.0,                     /* FINAL JACK  VELOCITY */
J3MVC   = 0.0,                     /* FINAL JACK VELOCITY */
J4MVC   = 0.0,                     /* FINAL JACK VELOCITY */
J5MVC   = 0.0,                     /* FINAL JACK VELOCITY */
J6MVC   = 0.0,                     /* FINAL JACK  VELOCITY */
J1MAC   = 0.0,                     /* FINAL JACK ACCEL */
J2MAC   = 0.0,                     /* FINAL JACK ACCEL */
J3MAC   = 0.0,                     /* FINAL JACK ACCEL */
J4MAC   = 0.0,                     /* FINAL JACK ACCEL */
J5MAC   = 0.0,                     /* FINAL JACK ACCEL */
J6MAC   = 0.0,                     /* FINAL JACK ACCEL */
J1MACL   = 0.0,                    /* FINAL JACK ACCEL LAGGED */
J2MACL   = 0.0,                    /* FINAL JACK ACCEL LAGGED */
J3MACL   = 0.0,                    /* FINAL JACK ACCEL LAGGED */
J4MACL   = 0.0,                    /* FINAL JACK ACCEL LAGGED */
J5MACL   = 0.0,                    /* FINAL JACK ACCEL LAGGED */
J6MACL   = 0.0,                    /* FINAL JACK ACCEL LAGGED */
J1MJXCLP = 0.0  ,                      /* PREVIOUS MJXCL */
J2MJXCLP = 0.0    ,                    /* PREVIOUS MJXCL */
J3MJXCLP = 0.0    ,                    /* PREVIOUS MJXCL */
J4MJXCLP = 0.0    ,                    /* PREVIOUS MJXCL */
J5MJXCLP = 0.0    ,                    /* PREVIOUS MJXCL */
J6MJXCLP = 0.0    ,                    /* PREVIOUS MJXCL */
J1MJXCLPP   = 0.0,                     /* PREVIOUS MJXCLP */
J2MJXCLPP   = 0.0,                     /* PREVIOUS MJXCLP */
J3MJXCLPP   = 0.0,                     /* PREVIOUS MJXCLP */
J4MJXCLPP   = 0.0,                     /* PREVIOUS MJXCLP */
J5MJXCLPP   = 0.0,                     /* PREVIOUS MJXCLP */
J6MJXCLPP   = 0.0,                     /* PREVIOUS MJXCLP */
MKACC = 0.0,                           /* ACCEL SCALING FACTOR */
MKPLTACC = 0.0,                        /* ACCEL SCALING FACTOR */
MKVEL = 0.0,                           /* VEL SCALING FACTOR */
MACCLAG = .9,                          /* ACCEL LAG CONSTANT */
J1XE   = 0.0,                          /* position error */
J2XE   = 0.0,                          /* position error */
J3XE   = 0.0,                          /* position error */
J4XE   = 0.0,                          /* position error */
J5XE   = 0.0,                          /* position error */
J6XE   = 0.0,                          /* position error */
J1FAC   = 0.0,                         /* actual force scaled */
J2FAC   = 0.0,                         /* actual force scaled */
J3FAC   = 0.0,                         /* actual force scaled */
J4FAC   = 0.0,                         /* actual force scaled */
J5FAC   = 0.0,                         /* actual force scaled */
J6FAC   = 0.0,                         /* actual force scaled */
J1FAOFS   = 0.0,                       /* force transducer (friction) offset */
J2FAOFS   = 0.0,                       /* force transducer (friction) offset */
J3FAOFS   = 0.0,                       /* force transducer (friction) offset */
J4FAOFS   = 0.0,                       /* force transducer (friction) offset */
J5FAOFS   = 0.0,                       /* force transducer (friction) offset */
J6FAOFS   = 0.0,                       /* force transducer (friction) offset */
J1IAC   = 0.0,                          /* actual current */
J2IAC   = 0.0,                          /* actual current */
J3IAC   = 0.0,                          /* actual current */
J4IAC   = 0.0,                          /* actual current */
J5IAC   = 0.0,                          /* actual current */
J6IAC   = 0.0,                          /* actual current */
J1IE = 0.0,				/* current error */
J2IE = 0.0,				/* current error */
J3IE = 0.0,				/* current error */
J4IE = 0.0,				/* current error */
J5IE = 0.0,				/* current error */
J6IE = 0.0;				/* current error */
/*
* 	Experimental force filter variables
*/
/*
*float MKCOTEST = 0.0;
*float MKCOW    = 0.0;
*float MKCOA = 0.;
*float MKCOB = 0.;
*float MKCOE = 0.;
*float MKCODAMP = 0.0;
*float MKCOWAVE = 0.0;
*float MKCOAMP = 0.0;
*float MKCOFREQ = 0.0;
*int   MKCOLOC = 0;
*/
/*
*  tuning constant scaling and offset
*/
float
KL_SC = -.00185 ,               /* scaling for position loop phase was -.05*/
KBET_SC = .0035,                /* scaling for position loop gain */
KMID_SC = .0033,                /* scaling for midrange velocity */
KV_SC = .01445,        		/* scaling for overall velocity was .18*/
KCO_SC = -.00392,               /* scaling for CO compensation gain was -.00037*/
KCOG_SC = .0001,                /* scaling for CO compensation gain */
KCOW_SC = 1.0,                  /* scaling for CO compensation phase */
KL_OFS = .35185  ,              /* scaling for position loop phase was 9.5*/
KBET_OFS = -0.7 ,               /* scaling for position loop gain */
KMID_OFS = 0.0 ,                /* scaling for midrange velocity */
KV_OFS = 0.0 ,                  /* scaling for overall velocity */
KCO_OFS = 0.4579,		/* scaling for CO compensation gain was .0674*/
KCOG_OFS = 0.0 ,                /* scaling for CO compensation gain */
KCOW_OFS = 0.0 ,                /* scaling for CO compensation phase */
J1KL   = 116.,                  /* position loop phase adjustment */
J2KL   = 100.,                  /* position loop phase adjustment */
J3KL   = 96.,                  /* position loop phase adjustment */
J4KL   = 108.6,                  /* position loop phase adjustment */
J5KL   = 102.7,                  /* position loop phase adjustment */
J6KL   = 114.3,                  /* position loop phase adjustment */
J1KBET   = 102.0,                  /* position loop gain adjustment */
J2KBET   = 88.,                  /* position loop gain adjustment */
J3KBET   = 86.,                  /* position loop gain adjustment */
J4KBET   = 99.,                  /* position loop gain adjustment */
J5KBET   = 96.,                  /* position loop gain adjustment */
J6KBET   = 103.,                  /* position loop gain adjustment */
J1KMID   = 98.6,                  /* midrange velocity adjustment */
J2KMID   = 101.7,                 /* midrange velocity adjustment */
J3KMID   = 102.7,                  /* midrange velocity adjustment */
J4KMID   = 98.8,                  /* midrange velocity adjustment */
J5KMID   = 102.0,                  /* midrange velocity adjustment */
J6KMID   = 96.3,                  /* midrange velocity adjustment */
J1KV   = 102.7,                    /* overall velocity adjustment */
J2KV   = 122.6,                    /* overall velocity adjustment */
J3KV   = 114.9,                    /* overall velocity adjustment */
J4KV   = 107.2,                    /* overall velocity adjustment */
J5KV   = 110.4,                    /* overall velocity adjustment */
J6KV   = 110.3,                   /* overall velocity adjustment */
J1KCO   = 90.,                /* CO compensation gain adjustment .038*/
J2KCO   = 90.,                /* CO compensation gain adjustment */
J3KCO   = 90.,                /* CO compensation gain adjustment */
J4KCO   = 90.,                /* CO compensation gain adjustment */
J5KCO   = 90.,                /* CO compensation gain adjustment */
J6KCO   = 90.,                /* CO compensation gain adjustment */
J1KCOG   = 100.,                 /* CO compensation gain adjustment */
J2KCOG   = 100.,                 /* CO compensation gain adjustment */
J3KCOG   = 100.,                 /* CO compensation gain adjustment */
J4KCOG   = 100.,                 /* CO compensation gain adjustment */
J5KCOG   = 100.,                 /* CO compensation gain adjustment */
J6KCOG   = 100.,                 /* CO compensation gain adjustment */
J1KCOW   = 5.0,                  /* CO compensation phase adjustment */
J2KCOW   = 5.0,                  /* CO compensation phase adjustment */
J3KCOW   = 5.0,                  /* CO compensation phase adjustment */
J4KCOW   = 5.0,                  /* CO compensation phase adjustment */
J5KCOW   = 5.0,                  /* CO compensation phase adjustment */
J6KCOW   = 5.0;                  /* CO compensation phase adjustment */
/* &END_SERVO& */             /* ends defines for macro */

/*
*    -------------
**   Motion Status
*    -------------
*/
int
MOT_ATREST= FALSE,			/* motion at rest flag */
MOT_UP = FALSE,				/* motion was UP flag for leanout */
MOT_PRES= FALSE;			/* motion system pressurized flag */
float
MXFADE=1.0;			/* motion fade in factor */
/*
*
**  motion buffets
*
*/
int NUM_IN_PKS;
int NUM_OUT_PKS;
/*
*
** motion commands fade filters
*
*/
float
MLAG5= 0.002,                /* EFFECTIVE LAG CONSTANT [SEC] */
MLAG5MAX= 5.0,                  /* LAG CONSTANT [SEC]  WAS 2.0 */
MLAG5INC = .0001,                /* LAG CONSTANT RATE OF CHANGE  WAS .0004*/
MPOTATTLAG = 1.0,		  /* MT panel attitude pot lag */
MPOTACTLAG = 1.0;		  /* MT panel actuator pots lag */
/*
*
**  motion commands
*
*/
int
MOUPDT = FALSE;				/* synch flag for MO predictor */
float
LONG_MPPOSL   = 0.0,                      /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
LAT_MPPOSL   = 0.0,                       /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
HEAV_MPPOSL   = 0.0,                      /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
PICH_MPPOSL   = 0.0,                      /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
ROLL_MPPOSL   = 0.0,                      /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
YAW_MPPOSL   = 0.0,                       /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
LONG_MTPOS   = 0.0,                       /* MT PGM PLATFORM COMMANDS */
LAT_MTPOS   = 0.0,                        /* MT PGM PLATFORM COMMANDS */
HEAV_MTPOS   = 0.0,                       /* MT PGM PLATFORM COMMANDS */
PICH_MTPOS   = 0.0,                       /* MT PGM PLATFORM COMMANDS */
ROLL_MTPOS   = 0.0,                       /* MT PGM PLATFORM COMMANDS */
YAW_MTPOS   = 0.0,                        /* MT PGM PLATFORM COMMANDS */
LONG_MOPOS   = 0.0,                       /* MO PGM PLATFORM COMMANDS */
LAT_MOPOS   = 0.0,                        /* MO PGM PLATFORM COMMANDS */
HEAV_MOPOS   = 0.0,                       /* MO PGM PLATFORM COMMANDS */
PICH_MOPOS   = 0.0,                       /* MO PGM PLATFORM COMMANDS */
ROLL_MOPOS   = 0.0,                       /* MO PGM PLATFORM COMMANDS */
YAW_MOPOS   = 0.0,                        /* MO PGM PLATFORM COMMANDS */
LONG_MOPOSD   = 0.0,                       /* MO PGM PLATFORM COMMANDS PRED RATE*/
LAT_MOPOSD   = 0.0,                        /* MO PGM PLATFORM COMMANDS PRED RATE*/
HEAV_MOPOSD   = 0.0,                       /* MO PGM PLATFORM COMMANDS PRED RATE*/
PICH_MOPOSD   = 0.0,                       /* MO PGM PLATFORM COMMANDS PRED RATE*/
ROLL_MOPOSD   = 0.0,                       /* MO PGM PLATFORM COMMANDS PRED RATE*/
YAW_MOPOSD   = 0.0,                        /* MO PGM PLATFORM COMMANDS PRED RATE*/
LONG_MOPOSF   = 0.0,                       /* MO PGM PLATFORM COMMANDS PREDICTED*/
LAT_MOPOSF   = 0.0,                        /* MO PGM PLATFORM COMMANDS PREDICTED*/
HEAV_MOPOSF   = 0.0,                       /* MO PGM PLATFORM COMMANDS PREDICTED*/
PICH_MOPOSF   = 0.0,                       /* MO PGM PLATFORM COMMANDS PREDICTED*/
ROLL_MOPOSF   = 0.0,                       /* MO PGM PLATFORM COMMANDS PREDICTED*/
YAW_MOPOSF   = 0.0,                        /* MO PGM PLATFORM COMMANDS PREDICTED*/
KPME         = .001,			   /* MO COMMAND PREDICTOR ERROR GAIN */
LONG_MBPOS   = 0.0,                       /* BUFFET PGM PLATFORM COMMANDS */
LAT_MBPOS   = 0.0,                        /* BUFFET PGM PLATFORM COMMANDS */
HEAV_MBPOS   = 0.0,                       /* BUFFET PGM PLATFORM COMMANDS */
PICH_MBPOS   = 0.0,                       /* BUFFET PGM PLATFORM COMMANDS */
ROLL_MBPOS   = 0.0,                       /* BUFFET PGM PLATFORM COMMANDS */
YAW_MBPOS   = 0.0,                        /* BUFFET PGM PLATFORM COMMANDS */
MBAMP1F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBAMP2F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBAMP3F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBAMP4F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBAMP5F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBAML1F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBAML2F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBAML3F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBAML4F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBAML5F = 0.0,				  /* DISCRETE BUFFET FILTERED AMP */
MBFRE1F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
MBFRE2F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
MBFRE3F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
MBFRE4F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
MBFRE5F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
MBFRL1F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
MBFRL2F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
MBFRL3F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
MBFRL4F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
MBFRL5F = 0.0,				  /* DISCRETE BUFFET FILTERED FREQ */
LONG_MPOSDD   = 0.0,                      /* EFFEC PLATF COMMANDS DOUBLE DERIV */
LAT_MPOSDD   = 0.0,                       /* EFFEC PLATF COMMANDS DOUBLE DERIV */
HEAV_MPOSDD   = 0.0,                      /* EFFEC PLATF COMMANDS DOUBLE DERIV */
PICH_POS = 0.0,                           /* PLAT PITCH FOR C/L MASS UMBAL */
LONG_MPOS   = 0.0,                        /* EFFEC PLATFORM COMMANDS */
LAT_MPOS   = 0.0,                         /* EFFECTIVE PLATFORM COMMANDS */
HEAV_MPOS   = 0.0,                        /* EFFECTIVE PLATFORM COMMANDS */
PICH_MPOS   = 0.0,                        /* EFFECTIVE PLATFORM COMMANDS */
ROLL_MPOS   = 0.0,                        /* EFFECTIVE PLATFORM COMMANDS */
YAW_MPOS   = 0.0,                         /* EFFECTIVE PLATFORM COMMANDS */
LONG_BIAS   = 0.0,                        /* PLATFORM COMMANDED BIAS */
LAT_BIAS   = 0.0,                         /* PLATFORM COMMANDED BIAS */
HEAV_BIAS   = 0.0,                        /* PLATFORM COMMANDED BIAS */
PICH_BIAS   = 0.0,                        /* PLATFORM COMMANDED BIAS */
ROLL_BIAS   = 0.0,                        /* PLATFORM COMMANDED BIAS */
YAW_BIAS   = 0.0,                         /* PLATFORM COMMANDED BIAS */
J1MOJP   = 0.0,                           /* JACK EXTENSIONS FROM T MATRIX  */
J2MOJP   = 0.0,                           /* JACK EXTENSIONS FROM T MATRIX  */
J3MOJP   = 0.0,                           /* JACK EXTENSIONS FROM T MATRIX  */
J4MOJP   = 0.0,                           /* JACK EXTENSIONS FROM T MATRIX  */
J5MOJP   = 0.0,                           /* JACK EXTENSIONS FROM T MATRIX  */
J6MOJP   = 0.0,                        /* JACK EXTENSIONS FROM T MATRIX  */
J1MTJP   = 0.0,                        /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
J2MTJP   = 0.0,                        /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
J3MTJP   = 0.0,                        /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
J4MTJP   = 0.0,                        /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
J5MTJP   = 0.0,                        /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
J6MTJP   = 0.0,                        /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
J1MJPI   = 0.0,                        /* INTEGRATED SOFTSTOP JACK EXTENSION */
J2MJPI   = 0.0,                        /* INTEGRATED SOFTSTOP JACK EXTENSION */
J3MJPI   = 0.0,                        /* INTEGRATED SOFTSTOP JACK EXTENSION */
J4MJPI   = 0.0,                        /* INTEGRATED SOFTSTOP JACK EXTENSION */
J5MJPI   = 0.0,                        /* INTEGRATED SOFTSTOP JACK EXTENSION */
J6MJPI   = 0.0;                        /* INTEGRATED SOFTSTOP JACK EXTENSION */
/*
*
** TRANSFORMATION MATRIX
*
*/
float
TT11 = 1., 		/* T MATRIX TERMS  */
TT12 = 0.,
TT13 = 0.,
TT21 = 0.,
TT22 = 1.,
TT23 = 0.,
TT31 = 0.,
TT32 = 0.,
TT33 = 1.,
TANTHES = 0.,           /* TANGENT OF PITCH ANGLE */
SINPHIS = 0.0,		/* SINE OF ROLL ANG */
SINTHES = 0.0,          /* SINE OF PITCH ANG */
SINPSIS = 0.0,          /* SINE OF YAW ANG */
COSPHIS = 0.0,          /* COS OF ROLL ANG */
COSTHES = 0.0,          /* COS OF PITCH ANG */
COSPSIS = 0.0,          /* COS OF YAW ANG */
MDIRCS[6][6]            /* DIRECTION COSINE */
;
/*
*
**    BEARING COORDINATES
*
*/
float
J1MXO   = 0.0,                         /* MOTION BASE X COORDINATE */
J2MXO   = 0.0,                         /* MOTION BASE X COORDINATE */
J3MXO   = 0.0,                         /* MOTION BASE X COORDINATE */
J4MXO   = 0.0,                         /* MOTION BASE X COORDINATE */
J5MXO   = 0.0,                         /* MOTION BASE X COORDINATE */
J6MXO   = 0.0,                         /* MOTION BASE X COORDINATE */
J1MYO   = 0.0,                         /* MOTION BASE Y COORDINATE */
J2MYO   = 0.0,                         /* MOTION BASE Y COORDINATE */
J3MYO   = 0.0,                         /* MOTION BASE Y COORDINATE */
J4MYO   = 0.0,                         /* MOTION BASE Y COORDINATE */
J5MYO   = 0.0,                         /* MOTION BASE Y COORDINATE */
J6MYO   = 0.0,                         /* MOTION BASE Y COORDINATE */
MZO = 0.0,                             /* MOTION BASE Z COORDINATE */
J1MXP   = 0.0,                         /* MOTION PLATFORM X COORDINATE */
J2MXP   = 0.0,                         /* MOTION PLATFORM X COORDINATE */
J3MXP   = 0.0,                         /* MOTION PLATFORM X COORDINATE */
J4MXP   = 0.0,                         /* MOTION PLATFORM X COORDINATE */
J5MXP   = 0.0,                         /* MOTION PLATFORM X COORDINATE */
J6MXP   = 0.0,                         /* MOTION PLATFORM X COORDINATE */
J1MYP   = 0.0,                         /* MOTION PLATFORM Y COORDINATE */
J2MYP   = 0.0,                         /* MOTION PLATFORM Y COORDINATE */
J3MYP   = 0.0,                         /* MOTION PLATFORM Y COORDINATE */
J4MYP   = 0.0,                         /* MOTION PLATFORM Y COORDINATE */
J5MYP   = 0.0,                         /* MOTION PLATFORM Y COORDINATE */
J6MYP   = 0.0,                         /* MOTION PLATFORM Y COORDINATE */
MZP = 0.0,                             /* MOTION PLATFORM Z COORDINATE */
MKJSCALE=0.0,                   	/* JACK LENGHT [in] */
MKINVSCALE=0.0,				/* INVERSE OF JACK LENGHT [1/in] */
MKJN=0.0;           	        	/* JACK EXTENSION AT NEUTRAL [in] */
/*
*
**  JACK EXTENSIONS
*
*/
float
J1MJX   = 0.0,                         /* JACK X LENGTH */
J2MJX   = 0.0,                         /* JACK X LENGTH */
J3MJX   = 0.0,                         /* JACK X LENGTH */
J4MJX   = 0.0,                         /* JACK X LENGTH */
J5MJX   = 0.0,                         /* JACK X LENGTH */
J6MJX   = 0.0,                         /* JACK X LENGTH */
J1MJY   = 0.0,                         /* JACK Y LENGTH */
J2MJY   = 0.0,                         /* JACK Y LENGTH */
J3MJY   = 0.0,                         /* JACK Y LENGTH */
J4MJY   = 0.0,                         /* JACK Y LENGTH */
J5MJY   = 0.0,                         /* JACK Y LENGTH */
J6MJY   = 0.0,                         /* JACK Y LENGTH */
J1MJZ   = 0.0,                          /* JACK Z LENGTH */
J2MJZ   = 0.0,                          /* JACK Z LENGTH */
J3MJZ   = 0.0,                          /* JACK Z LENGTH */
J4MJZ   = 0.0,                          /* JACK Z LENGTH */
J5MJZ   = 0.0,                          /* JACK Z LENGTH */
J6MJZ   = 0.0;                          /* JACK Z LENGTH */
/*
*
**   MOTION LIMITS
*
*/
float
GLM[3]  = {0.,0.,0.};                        /* EXTENSION LIMITS */
/*
*
** SOFTSTOP1
*
*/
/* &BEGIN_SOFT1& */             /* begin defines for macro */
float
J1JACKV   = 0.0,                      /* JACK VELOCITY NOT LIMITED */
J2JACKV   = 0.0,                      /* JACK VELOCITY NOT LIMITED */
J3JACKV   = 0.0,                      /* JACK VELOCITY NOT LIMITED */
J4JACKV   = 0.0,                      /* JACK VELOCITY NOT LIMITED */
J5JACKV   = 0.0,                      /* JACK VELOCITY NOT LIMITED */
J6JACKV   = 0.0,                      /* JACK VELOCITY NOT LIMITED */
J1MMJP   = 0.0,                        /* JACK EXTENSION FROM S/W */
J2MMJP   = 0.0,                        /* JACK EXTENSION FROM S/W */
J3MMJP   = 0.0,                        /* JACK EXTENSION FROM S/W */
J4MMJP   = 0.0,                        /* JACK EXTENSION FROM S/W */
J5MMJP   = 0.0,                        /* JACK EXTENSION FROM S/W */
J6MMJP   = 0.0,                        /* JACK EXTENSION FROM S/W */
J1MMJPP   = 0.0,                      /* PREVIOUS ITERATION MMJP */
J2MMJPP   = 0.0,                      /* PREVIOUS ITERATION MMJP */
J3MMJPP   = 0.0,                      /* PREVIOUS ITERATION MMJP */
J4MMJPP   = 0.0,                      /* PREVIOUS ITERATION MMJP */
J5MMJPP   = 0.0,                      /* PREVIOUS ITERATION MMJP */
J6MMJPP   = 0.0,                      /* PREVIOUS ITERATION MMJP */
J1MJS   = 0.0,                         /* SOFTSTOPPED JACK EXTENSION */
J2MJS   = 0.0,                         /* SOFTSTOPPED JACK EXTENSION */
J3MJS   = 0.0,                         /* SOFTSTOPPED JACK EXTENSION */
J4MJS   = 0.0,                         /* SOFTSTOPPED JACK EXTENSION */
J5MJS   = 0.0,                         /* SOFTSTOPPED JACK EXTENSION */
J6MJS   = 0.0,                         /* SOFTSTOPPED JACK EXTENSION */
JDECEL = 7.,              		/* RATE OF DECELERATION IN STOPS*/
JPLIM  ,                      		/* JACK MAX EXTENSION ( VEL = 0.0 ) */
VELMAX = 0.0,                         	/* JACK VELOCITY LIMIT SQRT FUNCTION*/
VELLIM ,                     		/* JACK VELOCITY LIMIT H/W */
MJFAD=.2,                   		/* POSITION SYNCHRONISATION FADE [inc/sec]*/
MKSOFTON=0.80,                       	/* SOFTSTOP ONSET was 0.70 */
MKSOFTR=6.67,                        	/* SOFTSTOP RANGE */
MKSOFTR2=0.15,                        	/* SOFTSTOP RANGE */
JVCNT,                        		/* SOFTSTOP COUNTER */
JVCNT2,                         		/* SOFTSTOP COUNTER */
/* &END_SOFT1& */             		/* end defines for macro */
J1MJPC   = 0.0,                        /* JACK POSITION COMMAND */
J2MJPC   = 0.0,                        /* JACK POSITION COMMAND */
J3MJPC   = 0.0,                        /* JACK POSITION COMMAND */
J4MJPC   = 0.0,                        /* JACK POSITION COMMAND */
J5MJPC   = 0.0,                        /* JACK POSITION COMMAND */
J6MJPC   = 0.0;                        /* JACK POSITION COMMAND */
/*
*
** SOFTSTOP2
*
*/
float
J1MJP = 0.0,                    	/* JACK EXTENSION PLUS RAMP */
J2MJP = 0.0,                            /* JACK EXTENSION PLUS RAMP */
J3MJP = 0.0,                            /* JACK EXTENSION PLUS RAMP */
J4MJP = 0.0,                            /* JACK EXTENSION PLUS RAMP */
J5MJP = 0.0,                            /* JACK EXTENSION PLUS RAMP */
J6MJP = 0.0,                            /* JACK EXTENSION PLUS RAMP */
J1MJPP = 0.0,                          	/* previous MJP */
J2MJPP = 0.0,                           /* previous MJP */
J3MJPP = 0.0,                           /* previous MJP */
J4MJPP = 0.0,                           /* previous MJP */
J5MJPP = 0.0,                           /* previous MJP */
J6MJPP = 0.0,                           /* previous MJP */
J1MJQ = 0.0,                         	/* SOFTSTOPED JACK POSITION */
J2MJQ = 0.0,                         	/* SOFTSTOPED JACK POSITION */
J3MJQ = 0.0,                         	/* SOFTSTOPED JACK POSITION */
J4MJQ = 0.0,                         	/* SOFTSTOPED JACK POSITION */
J5MJQ = 0.0,                         	/* SOFTSTOPED JACK POSITION */
J6MJQ= 0.0,                         	/* SOFTSTOPED JACK POSITION */
J1MJXC = 0.0,                         	/* WASHOUT JACK POSITION */
J2MJXC = 0.0,                         	/* WASHOUT JACK POSITION */
J3MJXC = 0.0,                         	/* WASHOUT JACK POSITION */
J4MJXC = 0.0,                         	/* WASHOUT JACK POSITION */
J5MJXC = 0.0,                         	/* WASHOUT JACK POSITION */
J6MJXC  = 0.0,                         	/* WASHOUT JACK POSITION */
J1MJXCL = 0.0,                         	/* LAGGED JACK POSITION */
J2MJXCL = 0.0,                         	/* LAGGED JACK POSITION */
J3MJXCL = 0.0,                         	/* LAGGED JACK POSITION */
J4MJXCL = 0.0,                         	/* LAGGED JACK POSITION */
J5MJXCL = 0.0,                         	/* LAGGED JACK POSITION */
J6MJXCL  = 0.0;                         /* LAGGED JACK POSITION */
/*
*	------------------------------------------------
**	FREEZE AND WASHOUT
*	------------------------------------------------
*/
float
J1MXC   = 0.0,                          /* jack pos command */
J2MXC   = 0.0,                          /* jack pos command */
J3MXC   = 0.0,                          /* jack pos command */
J4MXC   = 0.0,                          /* jack pos command */
J5MXC   = 0.0,                          /* jack pos command */
J6MXC   = 0.0;                          /* jack pos command */
/*
*    	MOTION PROGRAM
*/
float
/*
* 	HIGH PASS FILTER
*/
ZETAHXA,
ZETAHXG,
ZETAHYA,
ZETAHYG,
ZETAHZA,
ZETAHZG,
ZETAXL,
ZETAYL,
MOW2HX,
MOW2HY,
MOW2HZ,
ABSVUG,           /* ABSOLUTE VALUE OF GROUND SPEED   */
MUGFADE,
MCTRAHP[3],      /*  HIGH PASS TRANSLATIONAL INPUT ACCEL */
MX1        ,    /*   INTEGRAL OF X SIM. POSITION                */
MX2        ,    /*   X SIMULATOR POSITION                       */
MX3        ,    /*   X SIMULATOR VELOCITY                       */
MX4        ,    /*   X SIMULATOR ACCELERATION                   */
MPX        ,    /*   ADAPTIVE GAIN ON X ACCELERATION            */
MY1        ,    /*   INTEGRAL OF Y SIM. POSITION                */
MY2        ,    /*   Y SIMULATOR POSITION                       */
MY3        ,    /*   Y SIMULATOR VELOCITY                       */
MY4        ,    /*   Y SIMULATOR ACCELERATION                   */
MPY        ,    /*   ADAPTIVE GAIN ON Y ACCELERATION            */
MZ1        ,    /*   INTEGRAL OF Z SIM. POSITION                */
MZ2        ,    /*   Z SIMULATOR POSITION                       */
MZ3        ,    /*   Z SIMULATOR VELOCITY                       */
MZ4        ,    /*   Z SIMULATOR ACCELERATION                   */
MPZ        ,    /*   ADAPTIVE GAIN ON Z ACCELERATION            */
MPHI1      ,    /*   INTEGRAL OF SIM. ROLL ANGLE                */
MPHI2      ,    /*   SIMULATOR ROLL ANGLE                       */
MPHI3      ,    /*   SIMULATOR ROLL RATE                        */
MPPHI      ,    /*   ADAPTIVE PARAMETER ON ROLL FILTER          */
MFPHI      ,    /*   FUNC. FOR ADAPT. H.P. ROLL FILTER          */
MTHE1      ,    /*   INTEGRAL OF SIM. PITCH ANGLE               */
MTHE2      ,    /*   SIMULATOR PITCH ANGLE                      */
MTHE3      ,    /*   SIMULATOR PITCH RATE                       */
MPTHE      ,    /*   ADAPTIVE PARAMETER ON PITCH FILTER         */
MPSI1      ,    /*   INTEGRAL OF SIM. YAW FILTER                */
MPSI2      ,    /*   SIMULATOR YAW ANGLE                        */
MPSI3      ,    /*   SIMULATOR YAW RATE                         */
MPPSI      ,    /*   ADAPTIVE PARAMETER ON YAW FILTER           */
MLPXDD     ,    /*    LOW PASS PITCH TILT ACCELERATION          */
MLPXD      ,    /*    LOW PASS PITCH TILT RATE                  */
MLPX       ,    /*    LOW PASS PITCH TILE ANGLE                 */
MLPYDD     ,    /*    LOW PASS ROLL TILT ACCELERATION           */
MLPYD      ,    /*    LOW PASS ROLL TILT RATE                   */
MLPY       ,    /*    LOW PASS ROLL TILT ANGLE                  */
MTHESL     ,    /*    LOW PASS PITCH ANGLE                      */
MPHISL     ,    /*    LOW PASS ROLL ANGLE                       */
MPHIS      ,    /*    SIMULATOR ROLL ANGLE (RAD)                */
MTHES      ,    /*    SIMULATOR PITCH ANGLE (RAD)               */
MPSIS      ,    /*    SIMULATOR YAW ANGLE (RAD)                 */
MPHISDEG   ,    /*    SIMULATOR ROLL ANGLE (DEG)                */
MTHESDEG   ,    /*     SIMULATOR PITCH ANGLE (DEG)              */
MPSISDEG   ,    /*   SIMULATOR YAW ANGLE (DEG)                  */
MACX       ,    /*    SIMULATOR H.P. X ACCELERATION             */
MACY       ,    /*    SIMULATOR H.P. Y ACCELERATION             */
MACZ       ,    /*    SIMULATOR H.P. Z ACCELERATION             */
MRTVEL[3]     ,    /*    A/C SCALED ROT. RATES AT COCKPIT          */
MPHICD     ,    /*    ROLL RATE IN SIM. PLATFORM AXES           */
MTHECD     ,    /*    PITCH RATE IN SIM. PLATFORM AXES          */
MPSICD     ;    /*    YAW RATE IN SIM. PLATFORM AXES            */
/*
*         -----------------------------------------------
**        Motion test program MTFAA and MORNING READINESS
*         -----------------------------------------------
*/
int
MTADDR = 0,		/* address of label to tune - autotune */
MTPLOTKEY = 0,		/* test key for plotting routine */
MTPLOTREQ = FALSE,	/* plot request */
MTMTFAAKEY=0,			/* MTFAA test key from DFC util. 1:TAB */
MTMORNKEY=0,			/* MORNING READ key from DFC util. 1:MORN1 */
MTTEST_KEY=0,	 	/* 1tab,2legbal,3frq,4fric,5pfrq,11m1,12m2,13m3,14m4 */
MTAUTOTUNE_KEY=0,               /* key for selected gain to tune: AUTOMTP */
MTTUNE_KEY=0,                   /* key for selection of gain to tune */
MTSCALEKEY=0,                  /* key for selection of scaling case to run */
MTIN_KEY=0,                     /* key for selection of analysis input signal */
MTOUT_KEY=0,			/* key for selection of analysis output signal */
MTMANUALON=0,			/* manual page active */
MTRSFINISH=0;                   /* test finished flag from sing resl test toDFC */
/*
*         --------------------------
**        Motion test program DRIVER
*         --------------------------
*/
int
MTONREQ=0,			/* Activate MT programs command from host */
MTMODE = 3,			/* MT mode : 1attitude,2actuator,3none */
MTSTATUS=1,			/* MT program status 1off2stop3wash4drive5anal*/
MTDRIVE = FALSE,		/* dfc utility drive command */
MTDRIVEREQ=0,			/* MT drive request */
MTSTOPREQ=0,			/* MT stop request */
MTAMPSEL=1,			/* amplitude selection mode : 1disp 2 accel*/
MTLENTABLE=0,			/* lenght of frequency table */
MTFRQPOINT=0,			/* pointer to the freq table */
MTAUTOFREQ=0,			/* freq table auto set-up request*/
MTAUTOFRSM=0,			/* SMALL freq table auto set-up request*/
MTCLTABREQ=0,			/* freq table clear request */
MTWAVEREQ=1,			/* requested MT wave 1sine 2triang 3square */
MTWAVE=1,			/* actual MT wave 1sine 2triang 3square */
MTAXISREQ=2,			/* requested MT axis : 0long 5yaw 6j1 11j6 */
MTAXIS=2,			/* actual MT wave : 0long 5yaw 6j1 11j6 */
MTNOLIMIT=0,			/* disable of amplitude limiting */
MTNOPOS=0,			/* drive with no position command */
MTNOACC=0, 			/* drive with no acc and acc command */
MTRESTART=0;			/* restart after axis wave change flag */

float
MTAMPDISP = 0.0,		/* demanded amplitude [inches] */
MTAMPDISPD = 0.0,		/* DEMANDED demanded amplitude [inches] */
MTAMPACC = 0.0,			/* demanded amplitude [g] */
MTAMPACCD = 0.0,		/* DEMANDED demanded amplitude [g] */
MTAMPREQ = 0.0,			/* MT requested drive amplitude [in or deg] */
MTAMP = 0.0,			/* MT actual drive amplitude [in or deg] */
MTFREQREQ = 0.0,		/* MT requested drive frequency */
MTFREQ = 0.0,			/* MT actual drive frequency */
MTPERIOD[7]={0.,0.,0.,0.,0.,0.,0.},/* MT actual drive period for each channel */
MTTIME = 0.0,			/* MT wave generator time base */
MTTHETA = 0.0,			/* MT angle for sine wave */
MTGEN = 0.0,			/* selected generator signal */
MTBIASX = 0.0,			/* motion x bias */
MTBIASY = 0.0,			/* motion y bias */
MTBIASZ = 0.0,			/* motion z bias */
MTBIASP = 0.0,			/* motion roll bias */
MTBIASQ = 0.0,			/* motion pitch bias */
MTBIASR = 0.0;			/* motion yaw bias */
/*
*         ----------------------------
**        Motion test program ANALYSIS
*         ----------------------------
*/
int
MTANALYS=0,   			/* type of analysis: 1fra, 2rms */
MTDRIVMODE=0,			/* mode of DRIVE: continuous, table  */
MTCHANANAL=0,			/* channel to be analysed: single or multiple*/
MTRSLTMODE =0,			/* mode of result generation */
MASTATUS= FALSE,		/* analysis program status flag */
MTJACK=0,			/* jack to be analysed 0jack1,..,5jack6*/
MTANALRST=0,			/* frequency analysis reset */
MTFRQCHANGE=0,			/* Make MTLOGIC use the next frq from table */
MRNEWRESULT=0,			/* new result found flag for MTRECORD */
MTREPORTNUM=1,                  /* number of result in report mode */
MTANALSB=0;			/* mtanal subbanding control*/
float
MTEPS = 0.0,			/* epsilon: error threshold for convergence */
MTOUTGAIN = 1.0,                /* scaling gain on output signal from motion */
MTPHASEOFS = 0.0,               /* offset on phase result */
MTGAINHIDE = 1.0,		/* hide gain result when not being tuned */
MTPHASHIDE = 1.0;		/* hide phase result when not being tuned */
/* &BEGIN_ANALYSE& */           /* macro variables for mtanalysis*/
int
J1MTRPOINT = 0,			/* MT result circular buffer pointer */
J2MTRPOINT = 0,			/* MT result circular buffer pointer */
J3MTRPOINT = 0,			/* MT result circular buffer pointer */
J4MTRPOINT = 0,			/* MT result circular buffer pointer */
J5MTRPOINT = 0,			/* MT result circular buffer pointer */
J6MTRPOINT = 0,			/* MT result circular buffer pointer */
JXMTRPOINT = 0,			/* MT result circular buffer pointer */
J1MTRESET = 0,			/* */
J2MTRESET = 0,
J3MTRESET = 0,
J4MTRESET = 0,
J5MTRESET = 0,
J6MTRESET = 0,
JXMTRESET = 0;
float
J1MTINPUT = 0.0,                        /* input signal for analysis */
J2MTINPUT = 0.0,                        /* input signal for analysis */
J3MTINPUT = 0.0,                        /* input signal for analysis */
J4MTINPUT = 0.0,                        /* input signal for analysis */
J5MTINPUT = 0.0,                        /* input signal for analysis */
J6MTINPUT = 0.0,                        /* input signal for analysis */
JXMTINPUT = 0.0,                        /* input signal for analysis */
J1MTOUTPUT = 0.0,			/* output signal for analysis */
J2MTOUTPUT = 0.0,			/* output signal for analysis */
J3MTOUTPUT = 0.0,			/* output signal for analysis */
J4MTOUTPUT = 0.0,			/* output signal for analysis */
J5MTOUTPUT = 0.0,			/* output signal for analysis */
J6MTOUTPUT = 0.0,			/* output signal for analysis */
JXMTOUTPUT = 0.0,			/* output signal for analysis */
J1MTMINTIME = 0.0,			/* minimum analysis time */
J2MTMINTIME = 0.0,			/* minimum analysis time */
J3MTMINTIME = 0.0,			/* minimum analysis time */
J4MTMINTIME = 0.0,			/* minimum analysis time */
J5MTMINTIME = 0.0,			/* minimum analysis time */
J6MTMINTIME = 0.0,			/* minimum analysis time */
JXMTMINTIME = 0.0,			/* minimum analysis time */
J1MTELTIME = 0.0,			/* elapse time */
J2MTELTIME = 0.0,			/* elapse time */
J3MTELTIME = 0.0,			/* elapse time */
J4MTELTIME = 0.0,			/* elapse time */
J5MTELTIME = 0.0,			/* elapse time */
J6MTELTIME = 0.0,			/* elapse time */
JXMTELTIME = 0.0,			/* elapse time */
J1MTGAINSQ = 0.0,			/* analysis gain squared */
J2MTGAINSQ = 0.0,			/* analysis gain squared */
J3MTGAINSQ = 0.0,			/* analysis gain squared */
J4MTGAINSQ = 0.0,			/* analysis gain squared */
J5MTGAINSQ = 0.0,			/* analysis gain squared */
J6MTGAINSQ = 0.0,			/* analysis gain squared */
JXMTGAINSQ = 0.0,			/* analysis gain squared */
J1MTRELERR = 0.0, 			/* analysis relative error*/
J2MTRELERR = 0.0, 			/* analysis relative error*/
J3MTRELERR = 0.0, 			/* analysis relative error*/
J4MTRELERR = 0.0, 			/* analysis relative error*/
J5MTRELERR = 0.0, 			/* analysis relative error*/
J6MTRELERR = 0.0, 			/* analysis relative error*/
JXMTRELERR = 0.0, 			/* analysis relative error*/
MTGAINMAX  = 0.0,			/* MAXIMUM VALUE for analysis gain */
J1MTFGAIN = 0.0,			/* analysis gain */
J2MTFGAIN = 0.0,			/* analysis gain */
J3MTFGAIN = 0.0,			/* analysis gain */
J4MTFGAIN = 0.0,			/* analysis gain */
J5MTFGAIN = 0.0,			/* analysis gain */
J6MTFGAIN = 0.0,			/* analysis gain */
JXMTFGAIN = 0.0,			/* analysis gain */
J1MTFGAINDB = 0.0,			/* analysis gain in dB */
J2MTFGAINDB = 0.0,			/* analysis gain in dB */
J3MTFGAINDB = 0.0,			/* analysis gain in dB */
J4MTFGAINDB = 0.0,			/* analysis gain in dB */
J5MTFGAINDB = 0.0,			/* analysis gain in dB */
J6MTFGAINDB = 0.0,			/* analysis gain in dB */
JXMTFGAINDB = 0.0,			/* analysis gain in dB */
J1MTPHASE = 0.0,			/* analysis phase */
J2MTPHASE = 0.0,			/* analysis phase */
J3MTPHASE = 0.0,			/* analysis phase */
J4MTPHASE = 0.0,			/* analysis phase */
J5MTPHASE = 0.0,			/* analysis phase */
J6MTPHASE = 0.0,			/* analysis phase */
JXMTPHASE = 0.0,			/* analysis phase */
J1MTRMSV = 0.0,				/* rms value */
J2MTRMSV = 0.0,				/* rms value */
J3MTRMSV = 0.0,				/* rms value */
J4MTRMSV = 0.0,				/* rms value */
J5MTRMSV = 0.0,				/* rms value */
J6MTRMSV = 0.0,				/* rms value */
JXMTRMSV = 0.0,				/* rms value */
J1MTPPEAKV = 0.0,			/* peak to peak value*/
J2MTPPEAKV = 0.0,			/* peak to peak value*/
J3MTPPEAKV = 0.0,			/* peak to peak value*/
J4MTPPEAKV = 0.0,			/* peak to peak value*/
J5MTPPEAKV = 0.0,			/* peak to peak value*/
J6MTPPEAKV = 0.0,			/* peak to peak value*/
JXMTPPEAKV = 0.0,			/* peak to peak value*/
J1TESTRSL1,				/*single output result #1 */
J2TESTRSL1,				/*single output result #1  */
J3TESTRSL1,				/*single output result #1  */
J4TESTRSL1,				/*single output result #1  */
J5TESTRSL1,				/*single output result #1  */
J6TESTRSL1,				/*single output result #1  */
JXTESTRSL1,				/*single output result #1  */
J1TESTRSL2,				/*single output result #2  */
J2TESTRSL2,				/*single output result #2  */
J3TESTRSL2,				/*single output result #2  */
J4TESTRSL2,				/*single output result #2  */
J5TESTRSL2,				/*single output result #2  */
J6TESTRSL2,				/*single output result #2  */
JXTESTRSL2,				/*single output result #2  */
J1TESTRSL3,				/*single output result #3  */
J2TESTRSL3,				/*single output result #3  */
J3TESTRSL3,				/*single output result #3  */
J4TESTRSL3,				/*single output result #3  */
J5TESTRSL3,				/*single output result #3  */
J6TESTRSL3,				/*single output result #3  */
JXTESTRSL3,				/*single output result #3  */
J1TESTRSL4,				/*single output result #4  */
J2TESTRSL4,				/*single output result #4  */
J3TESTRSL4,				/*single output result #4  */
J4TESTRSL4,				/*single output result #4  */
J5TESTRSL4,				/*single output result #4  */
J6TESTRSL4,				/*single output result #4  */
JXTESTRSL4,				/*single output result #4  */
J1MTRSULT1[10],                 	/* MT analysis result - parameter 1 */
J1MTRSULT2[10],                 	/* MT analysis result - parameter 2 */
J1MTRSULT3[10],                 	/* MT analysis result - parameter 3 */
J2MTRSULT1[10],                 	/* MT analysis result - parameter 1 */
J2MTRSULT2[10],                 	/* MT analysis result - parameter 2 */
J2MTRSULT3[10],                 	/* MT analysis result - parameter 3 */
J3MTRSULT1[10],                 	/* MT analysis result - parameter 1 */
J3MTRSULT2[10],                 	/* MT analysis result - parameter 2 */
J3MTRSULT3[10],                 	/* MT analysis result - parameter 3 */
J4MTRSULT1[10],                 	/* MT analysis result - parameter 1 */
J4MTRSULT2[10],                 	/* MT analysis result - parameter 2 */
J4MTRSULT3[10],                 	/* MT analysis result - parameter 3 */
J5MTRSULT1[10],                 	/* MT analysis result - parameter 1 */
J5MTRSULT2[10],                 	/* MT analysis result - parameter 2 */
J5MTRSULT3[10],                 	/* MT analysis result - parameter 3 */
J6MTRSULT1[10],                 	/* MT analysis result - parameter 1 */
J6MTRSULT2[10],                 	/* MT analysis result - parameter 2 */
J6MTRSULT3[10],                 	/* MT analysis result - parameter 3 */
JXMTRSULT1[10],                 	/* MT analysis result - parameter 1 */
JXMTRSULT2[10],                 	/* MT analysis result - parameter 2 */
JXMTRSULT3[10];                 	/* MT analysis result - parameter 3 */
int
J1MTRSFLAG[10] = {0,0,0,0,0,0,0,0,0,0}, /* MT new analysis result flag */
J2MTRSFLAG[10] = {0,0,0,0,0,0,0,0,0,0}, /* MT new analysis result flag */
J3MTRSFLAG[10] = {0,0,0,0,0,0,0,0,0,0}, /* MT new analysis result flag */
J4MTRSFLAG[10] = {0,0,0,0,0,0,0,0,0,0}, /* MT new analysis result flag */
J5MTRSFLAG[10] = {0,0,0,0,0,0,0,0,0,0}, /* MT new analysis result flag */
J6MTRSFLAG[10] = {0,0,0,0,0,0,0,0,0,0}, /* MT new analysis result flag */
JXMTRSFLAG[10] = {0,0,0,0,0,0,0,0,0,0}, /* MT new analysis result flag */
J1NUMRSULT = 0,	              	/* number of results, if less display size */
J2NUMRSULT = 0,	              	/* number of results, if less display size */
J3NUMRSULT = 0,	              	/* number of results, if less display size */
J4NUMRSULT = 0,	              	/* number of results, if less display size */
J5NUMRSULT = 0,	              	/* number of results, if less display size */
J6NUMRSULT = 0,	              	/* number of results, if less display size */
JXNUMRSULT = 0,	              	/* number of results, if less display size */
J1MTOVERW = FALSE,	      	/* result buffer overwrite flag */
J2MTOVERW = FALSE,	      	/* result buffer overwrite flag */
J3MTOVERW = FALSE,	      	/* result buffer overwrite flag */
J4MTOVERW = FALSE,	      	/* result buffer overwrite flag */
J5MTOVERW = FALSE,	      	/* result buffer overwrite flag */
J6MTOVERW = FALSE,	      	/* result buffer overwrite flag */
JXMTOVERW = FALSE;	      	/* result buffer overwrite flag */
/* &END_ANALYSE& */           /* macro variables for mtanalysis*/

/*      ----------------
*   	mtrecord program
*       ----------------
*/
int
MTRECORD=0,			/* enable recording of signals */
MRRESET=0,			/* record program reset */
MRNCURVE=0,			/* number of curve on graph */
MTNPOINT=0,			/* size of the curve(s) [points] */
MRCOUNT=0,			/* point counter for sampling */
MRSAMSB=0,			/* mtrecord sampling subanding control */
MRSAMFRQ=0;			/* sampling rate, subband of sys_itim_500 */

/*
*   	accelerometers signals
*/
float
JXACC1 ,			/* accel box #1 x accelerometer */
JYACC1 ,			/* accel box #1 y accelerometer */
JZACC1 ,			/* accel box #1 z accelerometer */
JXACC2 ,			/* accel box #2 x accelerometer */
JYACC2 ,			/* accel box #2 y accelerometer */
JZACC2 ,			/* accel box #2 z accelerometer */
JZACC3 ,			/* accel box #3 z accelerometer */
JXACCOFS1,			/* accel box #1 x offset */
JYACCOFS1,        		/* accel box #1 y offset */
JZACCOFS1,	 		/* accel box #1 z offset */
JXACCOFS2,			/* accel box #2 x offset */
JYACCOFS2,			/* accel box #2 y offset */
JZACCOFS2,			/* accel box #2 z offset */
JZACCOFS3,			/* accel box #3 z offset */
JXACCGAIN1 = 1.0,		/* accel box #1 x gain */
JYACCGAIN1 = 1.0,		/* accel box #1 y gain */
JZACCGAIN1 = 1.0,		/* accel box #1 z gain */
JXACCGAIN2 = 1.0,		/* accel box #2 x gain */
JYACCGAIN2 = 1.0,		/* accel box #2 y gain */
JZACCGAIN2 = 1.0,		/* accel box #2 z gain */
JZACCGAIN3 = 1.0,		/* accel box #3 z gain */
JXACCEL = 0.0,			/* platform x axis acceleration */
JYACCEL = 0.0,			/* platform y axis acceleration */
JZACCEL = 0.0,			/* platform z axis acceleration */
JPACCEL = 0.0,			/* platform roll axis acceleration */
JQACCEL = 0.0,			/* platform pitch axis acceleration */
JRACCEL = 0.0;			/* platform yaw axis acceleration */

/*
*         ------------------------
**        MESSAGE BUFFER VARIABLES
*         ------------------------
*/
int
MTMSPOINT=0,          		/* pointer to message buffer */
MTMSNUM=0,			/* number of new message lines */
MTMSOVERW=0,			/* message buffer overwrite flag */
MTMSRESET=0;                    /* reset message buffer flag */

/*
*         -----------
**        Motion ramp
*         -----------
*/
float
MORAMP = 0.0,  			/* motion up+down ramp */
UPRATE = 2.7,                   /* motion ramp up rate [IN/SEC]*/
DWRATE = 2.0,                   /* motion ramp down rate [IN/SEC]*/
JXACMAX = 0.0,                  /* lenght of higher jack [IN] */
JXACMIN = 0.0;                  /* lenght of lowest jack [IN] */
/*
*         ----------------------------
**        MOTION SAFETIES AND FAILURES
*         ----------------------------
*/
/*
*         -------------------------
**        Motion failure test FLAGS
*         -------------------------
*/
int
MOVERRUNTS = 0,         /* MOTION OVERRUN TEST FLAG */
MHOSTCHKSM = 0;		/* MOTION HOST CHECKSUM ERROR FLAG */
/* &BEGIN_SAFETY& */    /* begin defines for macro */
int
J1VELERTS = 0,		/* VELOCITY ERROR TEST FLAG */
J2VELERTS = 0,		/* VELOCITY ERROR TEST FLAG */
J3VELERTS = 0,		/* VELOCITY ERROR TEST FLAG */
J4VELERTS = 0,		/* VELOCITY ERROR TEST FLAG */
J5VELERTS = 0,		/* VELOCITY ERROR TEST FLAG */
J6VELERTS = 0,		/* VELOCITY ERROR TEST FLAG */

J1CTLSTBTS = 0,		/* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
J2CTLSTBTS = 0,		/* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
J3CTLSTBTS = 0,		/* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
J4CTLSTBTS = 0,		/* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
J5CTLSTBTS = 0,		/* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
J6CTLSTBTS = 0,		/* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */

J1EXVELTS = 0, 		/* EXCESSIVE VELOCITY TEST FLAG */
J2EXVELTS = 0, 		/* EXCESSIVE VELOCITY TEST FLAG */
J3EXVELTS = 0, 		/* EXCESSIVE VELOCITY TEST FLAG */
J4EXVELTS = 0, 		/* EXCESSIVE VELOCITY TEST FLAG */
J5EXVELTS = 0, 		/* EXCESSIVE VELOCITY TEST FLAG */
J6EXVELTS = 0, 		/* EXCESSIVE VELOCITY TEST FLAG */

J1POSERTS = 0, 		/* POSITION ERROR TEST FLAG */
J2POSERTS = 0, 		/* POSITION ERROR TEST FLAG */
J3POSERTS = 0, 		/* POSITION ERROR TEST FLAG */
J4POSERTS = 0, 		/* POSITION ERROR TEST FLAG */
J5POSERTS = 0, 		/* POSITION ERROR TEST FLAG */
J6POSERTS = 0, 		/* POSITION ERROR TEST FLAG */

J1JFRICTS = 0, 		/* JACK FRICTION TEST FLAG */
J2JFRICTS = 0, 		/* JACK FRICTION TEST FLAG */
J3JFRICTS = 0, 		/* JACK FRICTION TEST FLAG */
J4JFRICTS = 0, 		/* JACK FRICTION TEST FLAG */
J5JFRICTS = 0, 		/* JACK FRICTION TEST FLAG */
J6JFRICTS = 0,	 	/* JACK FRICTION TEST FLAG */

J1EXFORTS = 0, 		/* EXCESS FORCE TEST FLAG */
J2EXFORTS = 0, 		/* EXCESS FORCE TEST FLAG */
J3EXFORTS = 0, 		/* EXCESS FORCE TEST FLAG */
J4EXFORTS = 0, 		/* EXCESS FORCE TEST FLAG */
J5EXFORTS = 0, 		/* EXCESS FORCE TEST FLAG */
J6EXFORTS = 0,	 	/* EXCESS FORCE TEST FLAG */

J1POSDCTS = 0,		/* POSITION DISCONTINUITY TEST FLAG */
J2POSDCTS = 0,		/* POSITION DISCONTINUITY TEST FLAG */
J3POSDCTS = 0,		/* POSITION DISCONTINUITY TEST FLAG */
J4POSDCTS = 0,		/* POSITION DISCONTINUITY TEST FLAG */
J5POSDCTS = 0,		/* POSITION DISCONTINUITY TEST FLAG */
J6POSDCTS = 0;		/* POSITION DISCONTINUITY TEST FLAG */

/*
*         -------------------------------------
**        Motion failure disable FLAGS
*         -------------------------------------
*/
int
SDISMAXTIM = 450000,           /* safety disable maximum time : 5 min @500Hz*/
NOSAFE = FALSE,			/* ALL FAILURE DISABLE FLAG */
ENVEL = FALSE,			/* ENVELOPPE TEST FLAG */
FRICTION = FALSE,		/* FRICTION DISABL FLAG */
J1BIAS = FALSE,			/* BIAS TEST FLAG*/
J2BIAS = FALSE,			/* BIAS TEST FLAG*/
J3BIAS = FALSE,			/* BIAS TEST FLAG*/
J4BIAS = FALSE,			/* BIAS TEST FLAG*/
J5BIAS = FALSE,			/* BIAS TEST FLAG*/
J6BIAS = FALSE,			/* BIAS TEST FLAG*/
J1TRAVLDIS = 0,			/* travel limit failure disable */
J2TRAVLDIS = 0,			/* travel limit failure disable */
J3TRAVLDIS = 0,			/* travel limit failure disable */
J4TRAVLDIS = 0,			/* travel limit failure disable */
J5TRAVLDIS = 0,			/* travel limit failure disable */
J6TRAVLDIS = 0,			/* travel limit failure disable */
J1POSERDIS = 0,			/* position error failure disable */
J2POSERDIS = 0,			/* position error failure disable */
J3POSERDIS = 0,			/* position error failure disable */
J4POSERDIS = 0,			/* position error failure disable */
J5POSERDIS = 0,			/* position error failure disable */
J6POSERDIS = 0,			/* position error failure disable */
J1VELERDIS = 0,			/* velocity error failure disable */
J2VELERDIS = 0,			/* velocity error failure disable */
J3VELERDIS = 0,			/* velocity error failure disable */
J4VELERDIS = 0,			/* velocity error failure disable */
J5VELERDIS = 0,			/* velocity error failure disable */
J6VELERDIS = 0,			/* velocity error failure disable */
J1CURERDIS = 0,			/* current error failure disable */
J2CURERDIS = 0,			/* current error failure disable */
J3CURERDIS = 0,			/* current error failure disable */
J4CURERDIS = 0,			/* current error failure disable */
J5CURERDIS = 0,			/* current error failure disable */
J6CURERDIS = 0,			/* current error failure disable */

J1TRAVLTIM = 0,			/* safety disable timer */
J2TRAVLTIM = 0,			/* safety disable timer */
J3TRAVLTIM = 0,			/* safety disable timer */
J4TRAVLTIM = 0,			/* safety disable timer */
J5TRAVLTIM = 0,			/* safety disable timer */
J6TRAVLTIM = 0,			/* safety disable timer */

J1POSERTIM = 0,			/* safety disable timer */
J2POSERTIM = 0,			/* safety disable timer */
J3POSERTIM = 0,			/* safety disable timer */
J4POSERTIM = 0,			/* safety disable timer */
J5POSERTIM = 0,			/* safety disable timer */
J6POSERTIM = 0,			/* safety disable timer */

J1VELERTIM = 0,			/* safety disable timer */
J2VELERTIM = 0,			/* safety disable timer */
J3VELERTIM = 0,			/* safety disable timer */
J4VELERTIM = 0,			/* safety disable timer */
J5VELERTIM = 0,			/* safety disable timer */
J6VELERTIM = 0,			/* safety disable timer */

J1CURERTIM = 0,			/* safety disable timer */
J2CURERTIM = 0,			/* safety disable timer */
J3CURERTIM = 0,			/* safety disable timer */
J4CURERTIM = 0,			/* safety disable timer */
J5CURERTIM = 0,			/* safety disable timer */
J6CURERTIM = 0;			/* safety disable timer */
/*
*	--------------
**	friction force
*	--------------
*/
float
J1FRIC       = 0.0,			/* jack friction force */
J2FRIC       = 0.0,			/* jack friction force */
J3FRIC       = 0.0,			/* jack friction force */
J4FRIC       = 0.0,			/* jack friction force */
J5FRIC       = 0.0,			/* jack friction force */
J6FRIC       = 0.0,			/* jack friction force */
HYDFORLAG=2.0 ,			/* jack hydr force lag */
J1HYDFOR = 0.0,			/* jack hydraulic force */
J2HYDFOR = 0.0,			/* jack hydraulic force */
J3HYDFOR = 0.0,			/* jack hydraulic force */
J4HYDFOR = 0.0,			/* jack hydraulic force */
J5HYDFOR = 0.0,			/* jack hydraulic force */
J6HYDFOR = 0.0,			/* jack hydraulic force */
J1CAPPINP = 0.0,		/* jack cap end pressure input */
J2CAPPINP = 0.0,		/* jack cap end pressure input */
J3CAPPINP = 0.0,		/* jack cap end pressure input */
J4CAPPINP = 0.0,		/* jack cap end pressure input */
J5CAPPINP = 0.0,		/* jack cap end pressure input */
J6CAPPINP = 0.0,		/* jack cap end pressure input */
J1CAPOFS =  0.0,		/* jack cap end pressure offset */
J2CAPOFS =  0.0,		/* jack cap end pressure offset */
J3CAPOFS =  0.0,		/* jack cap end pressure offset */
J4CAPOFS =  0.0,		/* jack cap end pressure offset */
J5CAPOFS =  0.0,		/* jack cap end pressure offset */
J6CAPOFS =  0.0,		/* jack cap end pressure offset */
J1RODPINP = 0.0,		/* jack rod end pressure input */
J2RODPINP = 0.0,		/* jack rod end pressure input */
J3RODPINP = 0.0,		/* jack rod end pressure input */
J4RODPINP = 0.0,		/* jack rod end pressure input */
J5RODPINP = 0.0,		/* jack rod end pressure input */
J6RODPINP = 0.0,		/* jack rod end pressure input */
J1RODOFS =  0.0,		/* jack rod end pressure offset */
J2RODOFS =  0.0, 		/* jack rod end pressure offset */
J3RODOFS =  0.0,		/* jack rod end pressure offset */
J4RODOFS =  0.0,		/* jack rod end pressure offset */
J5RODOFS =  0.0,		/* jack rod end pressure offset */
J6RODOFS =  0.0,		/* jack rod end pressure offset */
FACLAG = 2.0,   		/* actual force lag constant for friction */
J1FACL = 0.0,			/* actual force filtered for friction */
J2FACL = 0.0,			/* actual force filtered for friction */
J3FACL = 0.0,			/* actual force filtered for friction */
J4FACL = 0.0,			/* actual force filtered for friction */
J5FACL = 0.0,			/* actual force filtered for friction */
J6FACL  = 0.0			/* actual force filtered for friction */
;
/*
*	---------------------------------
**	failure thresholds and max values
*	---------------------------------
*/
float
J1VELERLO  = 2.7 ,              /* jack pos error min threshold [inch/s] */
J2VELERLO  = 2.7,          	/* jack pos error  min threshold [inch/s] */
J3VELERLO  = 2.7,               /* jack pos error  min threshold [inch/s] */
J4VELERLO  = 2.7,               /* jack pos error  min threshold [inch/s] */
J5VELERLO  = 2.7,               /* jack pos error  min threshold [inch/s] */
J6VELERLO  = 2.7,               /* jack pos error  min threshold [inch/s] */
J1VELERHI  = 54.,               /* jack pos error [inch/s] */
J2VELERHI  = 54.,               /* jack pos error max threshold [inch/s] */
J3VELERHI  = 54.,               /* jack pos error max threshold [inch/s] */
J4VELERHI  = 54.,               /* jack pos error max threshold [inch/s] */
J5VELERHI  = 54.,               /* jack pos error max threshold [inch/s] */
J6VELERHI  = 54.,               /* jack pos error max threshold [inch/s] */
JVELERVGAIN  = 2.0,             /* jack vel error GAIN ON VEL */
JVELERAGAIN  = 50.,             /* jack vel error GAIN ON ACCEL */
JVELERATE = 14.,		/* vel err thershold neg rate of change */
JVELERLAG = .004,		/* vel err lag constant */
J1VELER  = 0.,                  /* jack vel error effective value [inch/s]*/
J2VELER  = 0.,                  /* jack vel error effective value[inch/s]*/
J3VELER  = 0.,                  /* jack vel error effective value[inch/s]*/
J4VELER  = 0.,                  /* jack vel error effective value[inch/s]*/
J5VELER  = 0.,                  /* jack vel error effective value[inch/s]*/
J6VELER  = 0.,                  /* jack vel error effective value[inch/s]*/
J1VELERMAX = 0.0,               /* jack velocity error max value [inch/s]*/
J2VELERMAX = 0.0,               /* jack velocity error max value [inch/s]*/
J3VELERMAX = 0.0,               /* jack velocity error max value [inch/s]*/
J4VELERMAX = 0.0,               /* jack velocity error max value [inch/s]*/
J5VELERMAX = 0.0,               /* jack velocity error max value [inch/s]*/
J6VELERMAX = 0.0,               /* jack velocity error max value [inch/s]*/
JEXVEL  =35.,                	/* jack excess velocity failure [inch/s] */
J1EXVELMAX = 0.0,               /* jack excess velocity max value [inch/s]*/
J2EXVELMAX = 0.0,               /* jack excess velocity max value [inch/s]*/
J3EXVELMAX = 0.0,               /* jack excess velocity max value [inch/s]*/
J4EXVELMAX = 0.0,               /* jack excess velocity max value [inch/s]*/
J5EXVELMAX = 0.0,               /* jack excess velocity max value [inch/s]*/
J6EXVELMAX = 0.0,               /* jack excess velocity max value [inch/s]*/
J1POSERLO  = 2.0,               /* jack pos error min threshold [inch] WAS 1.4.*/
J2POSERLO  = 2.0,          	/* jack pos error  min threshold [inch]WAS 1.4*/
J3POSERLO  = 2.0,               /* jack pos error  min threshold [inch] WAS 1.4*/
J4POSERLO  = 2.0,               /* jack pos error  min threshold [inch] */
J5POSERLO  = 2.0,               /* jack pos error  min threshold [inch] */
J6POSERLO  = 2.0,               /* jack pos error  min threshold [inch] */
J1POSERHI  = 27.,               /* jack pos error [inch] */
J2POSERHI  = 27.,               /* jack pos error max threshold [inch] */
J3POSERHI  = 27.,               /* jack pos error max threshold [inch] */
J4POSERHI  = 27.,               /* jack pos error max threshold [inch] */
J5POSERHI  = 27.,               /* jack pos error max threshold [inch] */
J6POSERHI  = 27.,               /* jack pos error max threshold [inch] */
JPOSERATE = 2.7,		/* neg err thershold neg rate of change */
JPOSERVGAIN  = .50,             /* jack pos error GAIN ON VELOCITY */
JPOSERAGAIN  = 50.,             /* jack pos error GAIN ON ACCEL */
J1POSER = 0.0,                  /* jack pos error effective thresh [inch] */
J2POSER = 0.0,                  /* jack pos error effective thresh [inch] */
J3POSER = 0.0,                  /* jack pos error effective thresh [inch] */
J4POSER = 0.0,                  /* jack pos error effective thresh [inch] */
J5POSER = 0.0,                	/* jack pos error effective thresh [inch] */
J6POSER = 0.0,                  /* jack pos error effective thresh [inch] */
J1POSERMAX = 0.0,               /* max jack pos error [inch] */
J2POSERMAX = 0.0,               /*  max jack pos error [inch] */
J3POSERMAX = 0.0,               /*  max jack pos error [inch] */
J4POSERMAX = 0.0,               /*  max jack pos error [inch] */
J5POSERMAX = 0.0,               /*  max jack pos error [inch] */
J6POSERMAX = 0.0,               /*  max jack pos error [inch] */
J1CURER = 300.,	        	/*valve current error */
J2CURER = 300.,   	  	/*valve current error */
J3CURER = 300., 	  		/*valve current error */
J4CURER = 300.,		       	/*valve current error */
J5CURER = 300.,		       	/*valve current error */
J6CURER = 300., 	       		/*valve current error */
J1CURERMAX     = 0.0,           /*valve current error max */
J2CURERMAX     = 0.0,           /*valve current error max */
J3CURERMAX     = 0.0,           /*valve current error max */
J4CURERMAX     = 0.0,           /*valve current error max */
J5CURERMAX     = 0.0,           /*valve current error max */
J6CURERMAX     = 0.0,           /*valve current error max */
J1TRAVL = 0.,                   /*jack travel limit failure [in] */
J2TRAVL = 0.,                   /*jack travel limit failure [in] */
J3TRAVL = 0.,                   /*jack travel limit failure [in] */
J4TRAVL = 0.,                   /*jack travel limit failure [in] */
J5TRAVL = 0.,                   /*jack travel limit failure [in] */
J6TRAVL = 0.,                   /*jack travel limit failure [in] */
J1TRAVLMAX  = 0.0,              /* jack travel limit max value [ in ] */
J2TRAVLMAX  = 0.0,              /* jack travel limit max value [ in ] */
J3TRAVLMAX  = 0.0,              /* jack travel limit max value [ in ] */
J4TRAVLMAX  = 0.0,              /* jack travel limit max value [ in ] */
J5TRAVLMAX  = 0.0,              /* jack travel limit max value [ in ] */
J6TRAVLMAX  = 0.0,              /* jack travel limit max value [ in ] */
JFRIC = 250.   ,           	/*jack friction threshold WAS 250*/
J1JFRICMAX     = 0.0,           /*max jack friction [lbs] */
J2JFRICMAX     = 0.0,           /*max jack friction [lbs] */
J3JFRICMAX     = 0.0,           /*max jack friction [lbs] */
J4JFRICMAX     = 0.0,           /*max jack friction [lbs] */
J5JFRICMAX     = 0.0,           /*max jack friction [lbs] */
J6JFRICMAX     = 0.0,           /*max jack friction [lbs] */
JEXPOSFOR = 14000.   ,          /*jack excessive positive force [lbs] */
JEXNEGFOR = 7000.  ,            /*jack excessive negative force [lbs] */
J1EXFORMAX    = 0.0,            /*max jack positive force [lbs] */
J2EXFORMAX    = 0.0,            /*max jack positive force [lbs] */
J3EXFORMAX    = 0.0,            /*max jack positive force [lbs] */
J4EXFORMAX    = 0.0,            /*max jack positive force [lbs] */
J5EXFORMAX    = 0.0,            /*max jack positive force [lbs] */
J6EXFORMAX    = 0.0,            /*max jack positive force [lbs] */
J1EXFORMIN    = 0.0,            /*min jack negative force [lbs] */
J2EXFORMIN    = 0.0,            /*min jack negative force [lbs] */
J3EXFORMIN    = 0.0,            /*min jack negative force [lbs] */
J4EXFORMIN    = 0.0,            /*min jack negative force [lbs] */
J5EXFORMIN    = 0.0,            /*min jack negative force [lbs] */
J6EXFORMIN    = 0.0,            /*min jack negative force [lbs] */
JPOSDC = 3.  ,                  /*position signal discontinuity [inch] */
J1POSDCMAX    = 0.0,            /*position signal discontinuity */
J2POSDCMAX    = 0.0,            /*position signal discontinuity */
J3POSDCMAX    = 0.0,            /*position signal discontinuity */
J4POSDCMAX    = 0.0,            /*position signal discontinuity */
J5POSDCMAX    = 0.0,            /*position signal discontinuity */
J6POSDCMAX    = 0.0,            /*position signal discontinuity */
JPOSRNG = 1.1   ,               /*position signal out of range */
J1POSRNGMAX    = 0.0,           /*position signal out of range */
J2POSRNGMAX    = 0.0,           /*position signal out of range */
J3POSRNGMAX    = 0.0,           /*position signal out of range */
J4POSRNGMAX    = 0.0,           /*position signal out of range */
J5POSRNGMAX    = 0.0,           /*position signal out of range */
J6POSRNGMAX    = 0.0,           /*position signal out of range */
JCAPRNGPOS    = 3000.0,         /*cap pres out of range pos limit [psi]*/
JCAPRNGNEG    = -200.0,         /*cap pres out of range neg limit [psi]*/
J1CAPRNGMAX    = 0.0,           /*cap pressure signal out of range */
J2CAPRNGMAX    = 0.0,           /*cap pressure signal out of range */
J3CAPRNGMAX    = 0.0,           /*cap pressure signal out of range */
J4CAPRNGMAX    = 0.0,           /*cap pressure signal out of range */
J5CAPRNGMAX    = 0.0,           /*cap pressure signal out of range */
J6CAPRNGMAX    = 0.0,           /*cap pressure signal out of range */
J1CAPRNGMIN    = 0.0,           /*cap pressure signal out of range */
J2CAPRNGMIN    = 0.0,           /*cap pressure signal out of range */
J3CAPRNGMIN    = 0.0,           /*cap pressure signal out of range */
J4CAPRNGMIN    = 0.0,           /*cap pressure signal out of range */
J5CAPRNGMIN    = 0.0,           /*cap pressure signal out of range */
J6CAPRNGMIN    = 0.0,           /*cap pressure signal out of range */
JRODRNGPOS    = 3000.0,         /*cap pressure signal out of range */
JRODRNGNEG    = -200.0,         /*cap pressure signal out of range */
J1RODRNGMAX    = 0.0,           /*rod pressure signal out of range */
J2RODRNGMAX    = 0.0,           /*rod pressure signal out of range */
J3RODRNGMAX    = 0.0,           /*rod pressure signal out of range */
J4RODRNGMAX    = 0.0,           /*rod pressure signal out of range */
J5RODRNGMAX    = 0.0,           /*rod pressure signal out of range */
J6RODRNGMAX    = 0.0,           /*rod pressure signal out of range */
J1RODRNGMIN    = 0.0,           /*rod pressure signal out of range */
J2RODRNGMIN    = 0.0,           /*rod pressure signal out of range */
J3RODRNGMIN    = 0.0,           /*rod pressure signal out of range */
J4RODRNGMIN    = 0.0,           /*rod pressure signal out of range */
J5RODRNGMIN    = 0.0,           /*rod pressure signal out of range */
J6RODRNGMIN    = 0.0,           /*rod pressure signal out of range */
JCAPTHRES = 0.0,                /* cap pres thres for valve hydr problem */
JSTNDBYVEL = 1.5,		/* velocity threshold for "BU not in standby" */
J1BUWAIT       = 0.0,           /* wait counter after change */
J2BUWAIT       = 0.0,           /* wait counter after change */
J3BUWAIT       = 0.0,           /* wait counter after change */
J4BUWAIT       = 0.0,           /* wait counter after change */
J5BUWAIT       = 0.0,           /* wait counter after change */
J6BUWAIT        = 0.0,          /* wait counter after change */
JMRMPTIMOUT=60.,		/* motion ramp time out [sec] */
JL2MCOMTIM = 0.250,		/* L2M commun. fail thres[sec] */
JL2MTIMMAX = 0.0;		/* MAXIMUM time delay in communi. */
/*
*         --------------------
**        Motion WARNING flags
*         --------------------
*/
int
ERRDIS=FALSE,		        /* disable tracking errors */
J1MW_VELER    = FALSE,          /*  velocity error */
J2MW_VELER    = FALSE,          /*  velocity error */
J3MW_VELER    = FALSE,          /*  velocity error */
J4MW_VELER    = FALSE,          /*  velocity error */
J5MW_VELER    = FALSE,          /*  velocity error */
J6MW_VELER    = FALSE,          /*  velocity error */
J1MW_EXVEL   = FALSE,           /*  excess velocity */
J2MW_EXVEL   = FALSE,           /*  excess velocity */
J3MW_EXVEL   = FALSE,           /*  excess velocity */
J4MW_EXVEL   = FALSE,           /*  excess velocity */
J5MW_EXVEL   = FALSE,           /*  excess velocity */
J6MW_EXVEL   = FALSE,           /*  excess velocity */
J1MW_POSER    = FALSE,          /*jack position error */
J2MW_POSER    = FALSE,          /*jack position error */
J3MW_POSER    = FALSE,          /*jack position error */
J4MW_POSER    = FALSE,          /*jack position error */
J5MW_POSER    = FALSE,          /*jack position error */
J6MW_POSER    = FALSE,          /*jack position error */
J1MW_CURER    = FALSE,          /*valve current error */
J2MW_CURER    = FALSE,          /*valve current error */
J3MW_CURER    = FALSE,          /*valve current error */
J4MW_CURER    = FALSE,          /*valve current error */
J5MW_CURER    = FALSE,          /*valve current error */
J6MW_CURER    = FALSE,          /*valve current error */
J1MW_TRAVL   = FALSE,           /*travel limit */
J2MW_TRAVL   = FALSE,           /*travel limit */
J3MW_TRAVL   = FALSE,           /*travel limit */
J4MW_TRAVL   = FALSE,           /*travel limit */
J5MW_TRAVL   = FALSE,           /*travel limit */
J6MW_TRAVL   = FALSE,           /*travel limit */
J1MW_JFRIC    = FALSE,          /*jack friction */
J2MW_JFRIC    = FALSE,          /*jack friction */
J3MW_JFRIC    = FALSE,          /*jack friction */
J4MW_JFRIC    = FALSE,          /*jack friction */
J5MW_JFRIC    = FALSE,          /*jack friction */
J6MW_JFRIC    = FALSE,          /*jack friction */
J1MW_EXFOR   = FALSE,           /*jack excessive force */
J2MW_EXFOR   = FALSE ,          /*jack excessive force */
J3MW_EXFOR   = FALSE,           /*jack excessive force */
J4MW_EXFOR   = FALSE,           /*jack excessive force */
J5MW_EXFOR   = FALSE,           /*jack excessive force */
J6MW_EXFOR   = FALSE,           /*jack excessive force */
J1MW_POSDC   = FALSE,           /*position signal discontinuity */
J2MW_POSDC   = FALSE,           /*position signal discontinuity */
J3MW_POSDC   = FALSE,           /*position signal discontinuity */
J4MW_POSDC   = FALSE,           /*position signal discontinuity */
J5MW_POSDC   = FALSE,           /*position signal discontinuity */
J6MW_POSDC   = FALSE;           /*position signal discontinuity */
/*
*         --------------------
**        Motion failure RESET
*         --------------------
*/
int
MFRESET = FALSE,		/* global reset flag, flags and max values */
MRSTENABLE = TRUE,		/* enabling of failure reset */
MSMAXRST = 0,			/* global reset maximum values flag */
J1MSMAXRESET = 0,		/* reset maximum values flag */
J2MSMAXRESET = 0,		/* reset maximum values flag */
J3MSMAXRESET = 0,		/* reset maximum values flag */
J4MSMAXRESET = 0,		/* reset maximum values flag */
J5MSMAXRESET = 0,		/* reset maximum values flag */
J6MSMAXRESET = 0,		/* reset maximum values flag */
MSFAILWRST = 0,			/* global reset failure flags */
J1MSFWRESET = 0,		/* reset failure flags */
J2MSFWRESET = 0,		/* reset failure flags */
J3MSFWRESET = 0,		/* reset failure flags */
J4MSFWRESET = 0,		/* reset failure flags */
J5MSFWRESET = 0,		/* reset failure flags */
J6MSFWRESET = 0;		/* reset failure flags */

/*
*         --------------------
**        Motion failure FLAGS
*         --------------------
*/
int
MFAILEVEL=99,			/* failure level 1ABORT 2STND 3OFF 4FRZ 5WNR */
MFAILNUMBER = 0,  		/* number of failures since last transfer */
MF_OVERRUN = 0,			/* motion program overrunning failure */
MF_NOOPT = 0,			/* MOTION OPTION NOT SENT */
MF_L2MCOMM = 0,			/* Logic to motion communication fail */
MF_ADIO1 = 0,			/* MOTION ADIO #1 not responding */
MF_ADIO2 = 0,			/* MOTION ADIO #2 not responding */
MF_ADIO3 = 0,			/* MOTION ADIO #3 not responding */
MF_ADIO4 = 0,			/* MOTION ADIO #4 not responding */
MF_ADIO5 = 0,			/* MOTION ADIO #5 not responding */
MF_FADIO1 = 0,			/* MOTION ADIO #1 not responding */
MF_FADIO2 = 0,			/* MOTION ADIO #2 not responding */
MF_FADIO3 = 0,			/* MOTION ADIO #3 not responding */
MF_FADIO4 = 0,			/* MOTION ADIO #4 not responding */
MF_FADIO5 = 0,			/* MOTION ADIO #5 not responding */
MF_PRES1100 = FALSE,            /*  motion pressure less than 1100 psi */
MF_PRES1350= FALSE,             /*  motion pressure less than 1350 psi at startup */
MF_INVSW   = FALSE,             /*  invalid s/w command */
MF_HCHKSUM = FALSE,             /*  host checksum */
J1MF_VELER    = FALSE,          /*  velocity error */
J2MF_VELER    = FALSE,          /*  velocity error */
J3MF_VELER    = FALSE,          /*  velocity error */
J4MF_VELER    = FALSE,          /*  velocity error */
J5MF_VELER    = FALSE,          /*  velocity error */
J6MF_VELER    = FALSE,          /*  velocity error */
J1MF_EXVEL   = FALSE,           /*  excess velocity */
J2MF_EXVEL   = FALSE,           /*  excess velocity */
J3MF_EXVEL   = FALSE,           /*  excess velocity */
J4MF_EXVEL   = FALSE,           /*  excess velocity */
J5MF_EXVEL   = FALSE,           /*  excess velocity */
J6MF_EXVEL   = FALSE,           /*  excess velocity */
J1MF_POSER    = FALSE,          /*jack position error */
J2MF_POSER    = FALSE,          /*jack position error */
J3MF_POSER    = FALSE,          /*jack position error */
J4MF_POSER    = FALSE,          /*jack position error */
J5MF_POSER    = FALSE,          /*jack position error */
J6MF_POSER    = FALSE,          /*jack position error */
J1MF_CURER    = FALSE,          /*valve current error */
J2MF_CURER    = FALSE,          /*valve current error */
J3MF_CURER    = FALSE,          /*valve current error */
J4MF_CURER    = FALSE,          /*valve current error */
J5MF_CURER    = FALSE,          /*valve current error */
J6MF_CURER    = FALSE,          /*valve current error */
J1MF_TRAVL   = FALSE,           /*travel limit */
J2MF_TRAVL   = FALSE,           /*travel limit */
J3MF_TRAVL   = FALSE,           /*travel limit */
J4MF_TRAVL   = FALSE,           /*travel limit */
J5MF_TRAVL   = FALSE,           /*travel limit */
J6MF_TRAVL   = FALSE,           /*travel limit */
J1MF_JFRIC    = FALSE,          /*jack friction */
J2MF_JFRIC    = FALSE,          /*jack friction */
J3MF_JFRIC    = FALSE,          /*jack friction */
J4MF_JFRIC    = FALSE,          /*jack friction */
J5MF_JFRIC    = FALSE,          /*jack friction */
J6MF_JFRIC    = FALSE,          /*jack friction */
J1MF_BUPFAIL   = FALSE,         /*BU power fail */
J2MF_BUPFAIL   = FALSE,         /*BU power fail */
J3MF_BUPFAIL   = FALSE,         /*BU power fail */
J4MF_BUPFAIL   = FALSE,         /*BU power fail */
J5MF_BUPFAIL   = FALSE,         /*BU power fail */
J6MF_BUPFAIL   = FALSE,         /*BU power fail */
J1MF_NOTC    = FALSE,           /*BU not connected */
J2MF_NOTC    = FALSE,           /*BU not connected */
J3MF_NOTC    = FALSE,           /*BU not connected */
J4MF_NOTC    = FALSE,           /*BU not connected */
J5MF_NOTC    = FALSE,           /*BU not connected */
J6MF_NOTC    = FALSE,           /*BU not connected */
J1MF_BUSTDBY   = FALSE,         /*BU in standby */
J2MF_BUSTDBY   = FALSE,         /*BU in standby */
J3MF_BUSTDBY   = FALSE,         /*BU in standby */
J4MF_BUSTDBY   = FALSE,         /*BU in standby */
J5MF_BUSTDBY   = FALSE,         /*BU in standby */
J6MF_BUSTDBY   = FALSE,         /*BU in standby */
J1MF_BUNORM    = FALSE,         /*BU not in normal mode */
J2MF_BUNORM    = FALSE,                 /*BU not in normal mode */
J3MF_BUNORM    = FALSE,                 /*BU not in normal mode */
J4MF_BUNORM    = FALSE,                 /*BU not in normal mode */
J5MF_BUNORM    = FALSE,                 /*BU not in normal mode */
J6MF_BUNORM    = FALSE,                 /*BU not in normal mode */
J1MF_EXFOR   = FALSE,                  /*jack excessive force */
J2MF_EXFOR   = FALSE,                  /*jack excessive force */
J3MF_EXFOR   = FALSE,                  /*jack excessive force */
J4MF_EXFOR   = FALSE,                  /*jack excessive force */
J5MF_EXFOR   = FALSE,                  /*jack excessive force */
J6MF_EXFOR   = FALSE,                  /*jack excessive force */
J1MF_VLVPROB   = FALSE,                 /* valve hydr problem*/
J2MF_VLVPROB   = FALSE,                 /* valve hydr problem*/
J3MF_VLVPROB   = FALSE,                 /* valve hydr problem*/
J4MF_VLVPROB   = FALSE,                 /* valve hydr problem*/
J5MF_VLVPROB   = FALSE,                 /* valve hydr problem*/
J6MF_VLVPROB   = FALSE,                 /* valve hydr problem*/
J1MF_POSDC   = FALSE,                 /*position signal discontinuity */
J2MF_POSDC   = FALSE,                 /*position signal discontinuity */
J3MF_POSDC   = FALSE,                 /*position signal discontinuity */
J4MF_POSDC   = FALSE,                 /*position signal discontinuity */
J5MF_POSDC   = FALSE,                 /*position signal discontinuity */
J6MF_POSDC   = FALSE,                 /*position signal discontinuity */
J1MF_POSRNG= FALSE,                  /*position signal out of range */
J2MF_POSRNG= FALSE,                  /*position signal out of range */
J3MF_POSRNG= FALSE,                  /*position signal out of range */
J4MF_POSRNG= FALSE,                  /*position signal out of range */
J5MF_POSRNG= FALSE,                  /*position signal out of range */
J6MF_POSRNG= FALSE,                  /*position signal out of range */
J1MF_CAPRNG   = FALSE,                  /*cap pressure signal out of range */
J2MF_CAPRNG   = FALSE,                  /*cap pressure signal out of range */
J3MF_CAPRNG   = FALSE,                  /*cap pressure signal out of range */
J4MF_CAPRNG   = FALSE,                  /*cap pressure signal out of range */
J5MF_CAPRNG   = FALSE,                  /*cap pressure signal out of range */
J6MF_CAPRNG   = FALSE,                  /*cap pressure signal out of range */
J1MF_RODRNG   = FALSE,                  /*rod pressure signal out of range */
J2MF_RODRNG   = FALSE,                  /*rod pressure signal out of range */
J3MF_RODRNG   = FALSE,                  /*rod pressure signal out of range */
J4MF_RODRNG   = FALSE,                  /*rod pressure signal out of range */
J5MF_RODRNG   = FALSE,                  /*rod pressure signal out of range */
J6MF_RODRNG   = FALSE,                  /*rod pressure signal out of range */
J1MF_FORRNG   = FALSE,                  /*force transducer out of range */
J2MF_FORRNG   = FALSE,                  /*force transducer out of range */
J3MF_FORRNG   = FALSE,                  /*force transducer out of range */
J4MF_FORRNG   = FALSE,                  /*force transducer out of range */
J5MF_FORRNG   = FALSE,                  /*force transducer out of range */
J6MF_FORRNG   = FALSE,                  /*force transducer out of range */
J1MF_STDBY   = FALSE,                  /*loss of control in standby mode */
J2MF_STDBY   = FALSE,                  /*loss of control in standby mode */
J3MF_STDBY   = FALSE,                  /*loss of control in standby mode */
J4MF_STDBY   = FALSE,                  /*loss of control in standby mode */
J5MF_STDBY   = FALSE,                  /*loss of control in standby mode */
J6MF_STDBY   = FALSE,                  /*loss of control in standby mode */
MF_DMCTOG    = FALSE,              /*DMC to DSC toggle DOP failure */
JMUPTIMOUT= FALSE,			/* motion UP ramp time out */
JMDWTIMOUT= FALSE,			/* motion DOWN ramp time out */
M_POSDC[6] = {0,0,0,0,0,0}, 		/* internal pos disc failure flag */
M_POSDTIM[6] = {0,0,0,0,0,0}, 		/* pos disc failure validation timer */
M_WTRAVL[6] = {0,0,0,0,0,0}, 		/* internal travel limit warning flag */
M_WTRAVLTIM[6] = {0,0,0,0,0,0}, 		/* internal travlimit validation timer*/
M_TRAVL[6] = {0,0,0,0,0,0},    		/* internal travel limit failure flag */
M_TRAVLTIM[6] = {0,0,0,0,0,0}; 		/* internal travlimit validation timer*/

/* &END_SAFETY& */             /* ends defines for macro */

/*
*	I/O SCALING FACTORS
*/
int
MAP_KEY1=0,
MAP_KEY2=0,
MAP_KEY3=0,
MAP_KEY4=0,
MAP_KEY5=0,
MAP_KEY6=0,
MAP_KEY7=0;
float
MAPGAIN1 = 1.0,			  /* MAPPING UTILITY - GAIN ON SIGNAL 1 */
MAPGAIN2 = 1.0,			  /* MAPPING UTILITY - GAIN ON SIGNAL 2 */
MAPGAIN3 = 1.0,			  /* MAPPING UTILITY - GAIN ON SIGNAL 3 */
MAPGAIN4 = 1.0,			  /* MAPPING UTILITY - GAIN ON SIGNAL 4 */
MAPGAIN5 = 1.0,			  /* MAPPING UTILITY - GAIN ON SIGNAL 5 */
MAPGAIN6 = 1.0,			  /* MAPPING UTILITY - GAIN ON SIGNAL 6 */
MAPGAIN7 = 1.0,			  /* MAPPING UTILITY - GAIN ON SIGNAL 7 */
K_ACC = .*********;		  /* ACCEL INPUT SCALING (10V/5V per g)/32767 */
/*
*
* 	LOGIC EMULATOR
*
*/
int
B_NORMTEST = 0, 		/* normal/test mode P/B */
B_ATTACT = 0, 			/* attitude/actuator P/B */
B_MOTON = 0; 			/* motion on P/B */
/*
**************************************************************************
**                                                                      **
**           LOGIC TO MOTION C30 TRANSFER BUFFER 			**
**                                                                      **
**************************************************************************
*/
int
L2M_MNONREQ = FALSE,               /* MOTION ramp up request */
L2M_MNATREST = FALSE,		   /* motion at rest switch */
L2M_FAILEVEL = 99,		   /* motion failure level detected by logic */
L2M_CABSTATE = 10;                  /* cab state for motion code */
/*
*	MTpanel
*/
int
L2M_AXIS = 0,                   	/* axis being driven */
L2M_POTATT  = 0,                      	/* att/pos pot */
L2M_POTJ[6]   =  {0,0,0,0,0,0}; 	/* jack 1 pot */
/*
*	EL DISPLAY SCALING
*/
float
L2M_OILT   = 0.,                   	/* HPU oil temperature */
L2M_OILTOF = 4.,                   	/* HPU oil temperature offset */
L2M_MPRS   = 0.,                   	/* motion pressure */
L2M_MPRSOF = -750.,                   	/* motion pressure offset */
L2M_CPRS   = 0.,                   	/* C/L pressure */
L2M_CPRSOF = -750.;                   	/* C/L pressure offset */
/*
*    -------------------------------------------------------
*     LOGIC to motion options buffer
*    -------------------------------------------------------
*/
struct L2C_OPTION {
  int abort_type;
  int accel_pres;
  int freq_valve;
  int pos_force;
  int a_b;
  int c_d;
  int e_f;
  int g_h;
  };

struct L2C_OPTION OPTION;
/*
*    -------------------------------------------------------
*     LOGIC TO C30 CHANNEL REQUEST BUFFER
*    -------------------------------------------------------
*/

#define  NUM_CHANNEL  6

struct L2C_REQUEST {
  int toggle;
  int cl_request;
  int mot_request;
  int gs_request;
  int logic_options;
  int logic_state;
  int cab_state;
  int fail_reset;
  };

struct L2C_REQUEST LOGIC_REQUEST;

/*
*    -------------------------------------------------------
*     LOGIC TO C30 MOTION TEST BUFFER
*    -------------------------------------------------------
*/
int LOGICUPDT=0;	/* new set of values sent from LOGIC */
struct L2C_TEST {
  int axis;
  int attitude;
  int actuator[6];
  int pmot;
  int pcl;
  int toil;
  };

struct L2C_TEST LOGIC_MOT_TEST;

/*
**************************************************************************
**                                                                      **
**   	MOTION C30 TO LOGIC TRANSFER BUFFER AREA         		**
**                                                                      **
**************************************************************************
*/
int
M2L_FAILEVEL = 99,		  /* motion failevel as detected by motion */
M2L_MNON = FALSE;                /* Motion system up at neutral */
/*
*    -------------------------------------------------------
*     MOTION C30 TO LOGIC CHANNEL STATUS BUFFER
*    -------------------------------------------------------
*/
struct C2L_STATUS {
  int toggle;
  int reply;
  int flevl;
  int mtpmode;
  int jacknumber;
  int position[3];
  int velocity[3];
  int current[3];
  int force;
  int friction;
  int cap;
  int rod;
  int mofst;
  int cofst;
  int tofst;
  };

struct C2L_STATUS MOTION;

/*
*    -------------------------------------------------------
*     MOTION JACK INFO C30 TO LOGIC CHANNEL STATUS BUFFER
*    -------------------------------------------------------
*/
/*
	0: demanded, 1: actual, 2: error
*/
/*
struct MOTION_DISP{
int jacknumber ;
int position[3] ;
int velocity[3] ;
int current[3] ;
int force ;
int friction ;
};

struct MOTION_DISP JACK_DATA ;
*/
/*
*    -------------------------------------------------------
*     MOTION C30 TO LOGIC CHANNEL DEFINITION BUFFER
*    -------------------------------------------------------
*/

struct C2L_DEFINITION {
  int number;
  int type;
  int name[NUM_CHANNEL][3];
  };

struct C2L_DEFINITION CHANDEF;

/*
*    -------------------------------------------------------
*     MOTION C30 TO LOGIC ERROR LOGGER BUFFER
*    -------------------------------------------------------
*/

struct  C2L_ERROR {
  int   number;
  int   code[ MAX_ERROR ];
};

struct  C2L_ERROR  CHANERR;

/*
**************************************************************************
**                                                                      **
**           HOST COMPUTER TO MOTION C30 TRANSFER BUFFER AREA           **
**                                                                      **
**************************************************************************
*/
/*
*	  ------------
**        MOTION LOGIC
*         ------------
*/
float
MOTIMER   = 0.0,             /* MOTION FREEZE TIMER                */
MOWASH    = 0.0,             /* MOTION WASHOUT FLAG         */
/*
*	  ---------------
**        SPECIAL EFFECTS
*         ---------------
*/
MOSFXI    = 0.0,             /* SPECIAL EFFECT X ACCELERATION      */
MOSFYI    = 0.0,             /* SPECIAL EFFECT Y ACCELERATION      */
MOSFZI    = 0.0,             /* SPECIAL EFFECT Z ACCELERATION      */

MOSFXO    = 0.0,             /* SPECIAL EFFECT X POSITION          */
MOSFYO    = 0.0,             /* SPECIAL EFFECT Y POSITION          */
MOSFZO    = 0.0,             /* SPECIAL EFFECT Z POSITION          */

MOSFPI    = 0.0,             /* SPECIAL EFFECT ROLL ACCELERATION   */
MOSFQI    = 0.0,             /* SPECIAL EFFECT PITCH ACCELERATION  */
MOSFRI    = 0.0,             /* SPECIAL EFFECT YAW ACCELERATION    */

MOSFPO    = 0.0,             /* SPECIAL EFFECT ROLL POSITION       */
MOSFQO    = 0.0,             /* SPECIAL EFFECT PITCH POSITION      */
MOSFRO    = 0.0,             /* SPECIAL EFFECT YAW POSITION */
/*
*	  ---------------
**        BUFFET COMMANDS
*         ---------------
*/
MOBAMP1   = 0.0,             /* BUFFET GENERATOR #1 AMPLITUDE       */
MOBAMP2   = 0.0,             /* BUFFET GENERATOR #2 AMPLITUDE       */
MOBAMP3   = 0.0,             /* BUFFET GENERATOR #3 AMPLITUDE       */
MOBAMP4   = 0.0,             /* BUFFET GENERATOR #4 AMPLITUDE       */
MOBAMP5   = 0.0,             /* BUFFET GENERATOR #5 AMPLITUDE       */
MOBFRE1   = 0.0,             /* BUFFET GENERATOR #1 FREQUENCY       */
MOBFRE2   = 0.0,             /* BUFFET GENERATOR #2 FREQUENCY       */
MOBFRE3   = 0.0,             /* BUFFET GENERATOR #3 FREQUENCY       */
MOBFRE4   = 0.0,             /* BUFFET GENERATOR #4 FREQUENCY       */
MOBFRE5   = 0.0,             /* BUFFET GENERATOR #5 FREQUENCY       */
MOBNOIS   = 0.0,             /* WHITE NOISE GENERATOR AMPLITUDE     */
MOBFLOW   = 0.0,             /* WHITE NOISE FILTER LOW PASS CUTOFF  */
MOBFHIG   = 0.0,             /* WHITE NOISE FILTER HIGH PASS CUTOFF */
MOCHKSM   = 0.0,             /* INTERFACE CHECKSUM                  */
/*
*	  ------------------------
**        GRANULAR BUFFET COMMANDS
*         ------------------------
*/
MOBGAMP[20] =  { 0.,0.,0.,0.,0.,0.,0.,0.,0.,0.,
                 0.,0.,0.,0.,0.,0.,0.,0.,0.,0. },/* granular buffet amplitudes */                  
MOBINDX = 0.0,               /* BUFFET BUFFER INDEX */
/*
*	  ----------------------------------
**        DISCRETE BUFFET COMMANDS - LATERAL
*         ----------------------------------
*/   
MOBAML1   = 0.0,             /* BUFFET GENERATOR #1 AMPLITUDE       */
MOBAML2   = 0.0,             /* BUFFET GENERATOR #2 AMPLITUDE       */
MOBAML3   = 0.0,             /* BUFFET GENERATOR #3 AMPLITUDE       */
MOBAML4   = 0.0,             /* BUFFET GENERATOR #4 AMPLITUDE       */
MOBAML5   = 0.0,             /* BUFFET GENERATOR #5 AMPLITUDE       */
MOBFRL1   = 0.0,             /* BUFFET GENERATOR #1 FREQUENCY       */
MOBFRL2   = 0.0,             /* BUFFET GENERATOR #2 FREQUENCY       */
MOBFRL3   = 0.0,             /* BUFFET GENERATOR #3 FREQUENCY       */
MOBFRL4   = 0.0,             /* BUFFET GENERATOR #4 FREQUENCY       */
MOBFRL5   = 0.0,             /* BUFFET GENERATOR #5 FREQUENCY       */
/*
*         -------------
**        FLIGHT INPUTS
*         -------------
*/
MOVAXB    = 0.0,             /* FLIGHT X BODY ACCELERATION          */
MOVAYB    = 0.0,             /* FLIGHT Y BODY ACCELERATION          */
MOVAZB    = 0.0,             /* FLIGHT Z BODY ACCELERATION          */
MOVPD     = 0.0,             /* FLIGHT ROTATIONAL ACC*/
MOVQD     = 0.0,             /* FLIGHT ROTATIONAL ACC*/
MOVRD     = 0.0,             /* FLIGHT ROTATIONAL ACC*/
MOXCG     = 0.0,             /* PILOT TO C OF G X LENGHT [FEET]     */
MOZCG     = 0.0,             /* PILOT TO C OF G Z LENGHT [FEET]     */
MODX      = 0.0,             /* GAIN ON MOXCG                      */
MODZ      = 0.0,             /* GAIN ON MOZCG                      */
MOVUG     = 0.0,	     /* GROUND SPEED */
MOVP     = 0.0,             /* FLIGHT ROTATIONAL RATE*/
MOVQ     = 0.0,             /* FLIGHT ROTATIONAL RATE*/
MOVR     = 0.0,             /* FLIGHT ROTATIONAL RATE*/
MOVEE[5] = {0.,0.,0.,0.,0.},/* GEAR POSITION */
MOABFL = 0. ,		    /* LEFT BRAKING FORCE */
MOABFR = 0. ,		    /* RIGHT BRAKING FORCE */
MOVBOG = 1.;		    /* on groung flag */
/*
*         --------
**        CHECKSUM        
*         --------
*/
float
MOCHKSUM  = 0.0,             /* MOTION COMMANDS CHECKSUM FOR FLIGHT+SE */
MOBGCKS   = 0.0,             /* MOTION COMMANDS CHECKSUM FOR GRAN BUFFETS */
MOBDCKS   = 0.0,             /* MOTION COMMANDS CHECKSUM FOR DISCRETE BUFFETS */
/*
*         --------------------
**        MOTION SPARE OUTPUTS
*         --------------------
*/
MOSPRE[20]= { 0.,0.,0.,0.,0.,0.,0.,0.,0.,0.,
              0.,0.,0.,0.,0.,0.,0.,0.,0.,0. },/*     MOTION SPARE VARIABLES              */
/*
******************************************************************************
**									    **
**		HOST TO MOTION C30 DEBUG TRANSFER BUFFERS                   **
**									    **
******************************************************************************
*/
/*
*         -------------------
**        S/W OPTION SELECTED
*         -------------------
*/
MOXFADE=0.,         /* FADE OUT OF X ACCEL AT ZERO SPEED FLAG     */   
MONLSCL=0.,         /* NON-LINEAR SCALING OF FLIGHT INPUTS FLAG   */   
MOPHCR =0.,         /* CENTER OF ROTATION LOCATED AT PILOT'S HEAD */   
MOGBUNT=0.,         /* GRAN BUFFET UNITS : 0=PSD, 1=G'S           */   
MOVISF1=0.,         /* VISUAL MIRROR RESONNANCE FILTER-CUT FREQ 1 */   
MOVISF2=0.,         /* VISUAL MIRROR RESONNANCE FILTER-CUT FREQ 2 */   

/*
*         --------------------
**        ADAPTIVE MAIN MOTION
*         --------------------
*/
MOALM[3]= {0.,0.,0.}    ,   	/*       TRANS. MOTION INPUTS LIMITS        */

MOARM[3]= {0.,0.,0.}     ,   /*      TRANS. MOTION INPUTS LIMITS        */

/*
*         --------------------------------------
**        HIGH PASS TRANSLATIONS
*         --------------------------------------
*/
MOKXA     = 0.0,             /* X ACCELERATION GAIN IN AIR         */
MOKXG     = 0.0,             /* X ACCELERATION GAIN ON GROUND      */
MOKYA     = 0.0,             /* Y ACCELERATION GAIN IN AIR         */
MOKYG     = 0.0,             /* Y ACCELERATION GAIN ON GROUND      */
MOKZA     = 0.0,             /* Z ACCELERATION GAIN IN AIR         */
MOKZG     = 0.0,             /* Z ACCELERATION GAIN ON GROUND      */
/*
*         --------------------------------------
**        HIGH PASS ROTATIONS
*         --------------------------------------
*/
MOKPA     = 0.0,             /* ROLL ACCELERATION GAIN IN AIR      */
MOKPG     = 0.0,             /* ROLL ACCELERATION GAIN ON GROUND   */
MOKQA     = 0.0,             /* PITCH ACCELERATION GAIN IN AIR     */
MOKQG     = 0.0,             /* PITCH ACCELERATION GAIN ON GROUND  */
MOKRA     = 0.0,             /* YAW ACCELERATION GAIN IN AIR       */
MOKRG     = 0.0,             /* YAW ACCELERATION GAIN ON GROUND    */
/*
*         --------------------------------------
**        HIGH PASS FILTER PARAMETERS
*         --------------------------------------
*/
MOWHXG    = 0.0,             /* BREAK FREQ OF HIGH PASS FILTER-IN Air*/
MOWHXA    = 0.0,             /* BREAK FREQ OF HIGH PASS FILTER-ON GNd*/
MOWHYG    = 0.0,             /* BREAK FREQ OF HIGH PASS FILTER ON GROUND*/
MOWHYA    = 0.0,             /* BREAK FREQ OF HIGH PASS FILTER-ON GND   */
MOWHZG    = 0.0,             /* BREAK FREQ OF HIGH PASS FILTER-IN AIR     */
MOWHZA    = 0.0,             /* BREAK FREQ OF HIGH PASS FILTER-ON GND     */
   
MOXWEI1  = 0.0,          /* H-P FILTER WEIGHT FACTOR FOR RATE        */    
MOXWEI2  = 0.0,          /* H-P FILTER WEIGHT FACTOR FOR ANGLE       */    
MOXWEI3  = 0.0,          /* H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE */    
          
MOYWEI1  = 0.0,          /* H-P FILTER WEIGHT FACTOR FOR RATE        */    
MOYWEI2  = 0.0,          /* H-P FILTER WEIGHT FACTOR FOR ANGLE       */    
MOYWEI3  = 0.0,          /* H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE */    
          
MOZWEI1  = 0.0,          /* H-P FILTER WEIGHT FACTOR FOR RATE        */    
MOZWEI2  = 0.0,          /* H-P FILTER WEIGHT FACTOR FOR ANGLE       */    
MOZWEI3  = 0.0,          /* H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE */    
 
MOGXA   = 0.0,             /* GAIN ON DERIVATIVE OF COST FUNCTION-AIR  */    
MOGXG   = 0.0,             /* GAIN ON DERIVATIVE OF COST FUNCTION-GROUND */   
 
MOGYA   = 0.0,             /* GAIN ON DERIVATIVE OF COST FUNCTION-AIR */     
MOGYG   = 0.0,             /* GAIN ON DERIVATIVE OF COST FUNCTION-GROUND */
 
MOGZA   = 0.0,             /* GAIN ON DERIVATIVE OF COST FUNCTION-AIR*/
MOGZG   = 0.0,             /* GAIN ON DERIVATIVE OF COST FUNCTION-GROUND */
/*
*	ROTATIONAL AXIS
*/
MOWHPHI   = 0.0,             /* BREAK FREQ OF HIGH PASS FILTER  */             
MOWHTHE   = 0.0,             /* BREAK FREQ OF HIGH PASS FILTER  */             
MOWHPSI   = 0.0,             /* BREAK FREQ OF HIGH PASS FILTER  */             

MOWTHE1   = 0.0,             /* H-P FILTER WEIGHT FACTOR FOR RATE  */
MOWTHE2   = 0.0,             /* H-P FILTER WEIGHT FACTOR FOR ANGLE */
MOWTHE3   = 0.0,             /* H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE*/

MOWPHI1   = 0.0,             /* H-P FILTER WEIGHT FACTOR FOR RATE  */
MOWPHI2   = 0.0,             /* H-P FILTER WEIGHT FACTOR FOR ANGLE */
MOWPHI3   = 0.0,             /* H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE */

MOWPSI1   = 0.0,             /* H-P FILTER WEIGHT FACTOR FOR RATE  */
MOWPSI2   = 0.0,             /* H-P FILTER WEIGHT FACTOR FOR ANGLE */
MOWPSI3   = 0.0,             /* H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE*/

MOGPHI  = 0.0,                /* GAIN ON DERIVATIVE OF COST FUNCTION */
MOGTHE  = 0.0,                /* GAIN ON DERIVATIVE OF COST FUNCTION */
MOGPSI  = 0.0,                /* GAIN ON DERIVATIVE OF COST FUNCTION */

/*
*         --------------------------------------
**        LOW PASS FILTERS
*         --------------------------------------
*/
MOKXLA    = 0.0,             /* IN AIR X LOW PASS GAIN             */
MOKXLGA   = 0.0,             /* GND X LOW PASS GAIN - ACCELERATING */
MOKXLGD   = 0.0,             /* GND X LOW PASS GAIN - DECELERATING */

MOKYLA    = 0.0,             /* IN AIR Y LOW PASS GAIN             */
MOKYLG    = 0.0,             /* GND Y LOW PASS GAIN                */

MOKLIM1   = 0.0,             /* L-P PITC RATE LIMIT (VUG <100)*/
MOKLIM2   = 0.0,             /* L-P PITC RATE LIMIT IF DECCEL      */
MOKLIM3   = 0.0,             /* L-P PITC RATE LIMIT IN AIR (VUG>100) */
MOKLIM4   = 0.0,             /* L-P PITC RATE LIMIT WHEN BRAKE RELEASE*/

MOSTOPA   = 0.0,             /* MKSTOP FADE RATE IF ACCEL          */
MOSTOPD   = 0.0,             /* MKSTOP FADE RATE IF DECEL          */
MOFDSLP   = 0.0,             /* LOW PASS FADE SLOPE FOR ACCEL.     */
MOFDSTA   = 0.0,             /* LOW PASS FADE START FOR ACCEL.     */
MOFDSTD   = 0.0,             /* LOW PASS FADE START FOR DECEL.     */
MOTUNE    = 0.0,             /* MAIN MOTION GAIN RECOMPUTATION FLAG*/

MOWXL     = 0.0,             /* BREAK FREQ OF LOW PASS FILTER X   */
MOWYL     = 0.0,             /* BREAK FREQ OF LOW PASS FILTER Y   */

/*
**************************************************************************
**                                                                      **
**           MOTION C30 TO HOST TRANSFER BUFFER AREA         		**
**                                                                      **
**************************************************************************
*/
/*
*         --------------------------------------
**        HIGH PASS TRANSLATIONS
*         --------------------------------------
*/
MIKXACC    = 0.0,             /* EFFECTIVE X ACCELERATION GAIN      */
MIKYACC    = 0.0,             /* EFFECTIVE Y ACCELERATION GAIN      */
MIKZACC    = 0.0,             /* EFFECTIVE Z ACCELERATION GAIN      */
/*
*         --------------------------------------
**        HIGH PASS ROTATIONS
*         --------------------------------------
*/
MIKROLL    = 0.0,             /* EFFECTIVE ROLL ACCELERATION GAIN   */
MIKPITCH   = 0.0,             /* EFFECTIVE PITCH ACCELERATION GAIN  */
MIKYAW     = 0.0,             /* EFFECTIVE YAW ACCELERATION GAIN */
/*
*         --------------------------------------
**        HIGH PASS FILTER
*         --------------------------------------
*/
MITRACC[3]= {0.,0.,0.},		/* TRANSLATIONAL COCKPIT ACCELERATION */
/*
*         --------------------------------------
**        LOW PASS FILTERS
*         --------------------------------------
*/
MILPXACC   = 0.0,             /* LOW PASS INPUT X ACCELERATION    [F/SEC**2] */
MILPYACC   = 0.0,             /* LOW PASS INPUT Y ACCELERATION    [FT/SEC**2] */

MIKLPY     = 0.0,             /* EFFECTIVE LOW PASS Y ACCELERATION GAIN*/
MIKLPX     = 0.0,             /* EFFECTIVE LOW PASS X ACCELERATION GAIN*/

MIKXLG     = 0.0,             /* EFFECTIVE GND X LOW PASS GAIN*/

MILPYDLIM  = 0.0,             /* L.P. ROLL TILT RATE LIMIT          */
MILPXDLIM  = 0.0,             /* L.P. PITCH TILT RATE LIMIT         */

MIKSTOP    = 0.0,             /* LOW PASS PITCH ANGLE FADE FACTOR   */
/*
*         ------------------------------
**        VARIOUS I/F MAINTENANCE LABELS
*         ------------------------------
*/
MIPHI3      ,    /*   SIMULATOR ROLL RATE                        */
MITHE3      ,    /*   SIMULATOR PITCH RATE                        */
MIPSI3      ,    /*   SIMULATOR YAW RATE                        */
MILPXD      ,    /*   LOW PASS PITCH TILT RATE                   */
MILPYD      ,    /*   LOW PASS ROLL TILT RATE                   */
MIPX        ,    /*   ADAPTIVE GAIN ON X ACCELERATION            */
MIPY        ,    /*   ADAPTIVE GAIN ON Y ACCELERATION            */
MIPZ        ,    /*   ADAPTIVE GAIN ON Z ACCELERATION            */
MIPPHI      ,    /*   ADAPTIVE PARAMETER ON ROLL FILTER          */
MIPTHE      ,    /*   ADAPTIVE PARAMETER ON PITCH FILTER          */
MIPPSI      ,    /*   ADAPTIVE PARAMETER ON YAW FILTER          */
/*
*         --------------------------------------
**        FINAL CABIN POSITIONS
*         --------------------------------------
*/
MITHESL    = 0.0,             /* LOW PASS PITCH ANGLE  [RAD]  */
MIPHISL    = 0.0,             /* LOW PASS ROLL ANGLE   [RAD] */
MIPHIS     = 0.0,             /* SIMULATOR ROLL ANGLE  [RAD] */
MITHES     = 0.0,             /* SIMULATOR PITCH ANGLE [RAD] */
MIPSIS     = 0.0,             /* SIMULATOR YAW ANGLE   [RAD] */
MIPHISDEG  = 0.0,             /* SIMULATOR ROLL ANGLE  [DEG] */
MITHESDEG  = 0.0,             /* SIMULATOR PITCH ANGLE [DEG] */
MIPSISDEG  = 0.0,             /* SIMULATOR YAW ANGLE   [DEG] */
MIACX      = 0.0,             /* SIMULATOR H.P. X ACCELERATION    [FT/SEC**2] */
MIACY      = 0.0,             /* SIMULATOR H.P. Y ACCELERATION    [FT/SEC**2] */
MIACZ      = 0.0,             /* SIMULATOR H.P. Z ACCELERATION    [FT/SEC**2] */

MIPOS_LONG = 0.0,             /* CABIN TRANSLATION  */
MIPOS_LAT  = 0.0,             /* CABIN TRANSLATION    */
MIPOS_HEAV = 0.0,             /* CABIN TRANSLATION      */
MIPOS_PICH = 0.0,             /* CABIN ROTATION          */
MIPOS_ROLL = 0.0,             /* CABIN ROTATION            */
MIPOS_YAW  = 0.0,             /* CABIN ROTATION              */
/*
*         --------------------------------------
**        COMMANDS TO SERVO CONTROLLER
*         --------------------------------------
*/
MIJ1XC     = 0.0,             /* JACK POSITION COMMAND              */
MIJ2XC     = 0.0,             /* JACK POSITION COMMAND              */
MIJ3XC     = 0.0,             /* JACK POSITION COMMAND              */
MIJ4XC     = 0.0,             /* JACK POSITION COMMAND              */
MIJ5XC     = 0.0,             /* JACK POSITION COMMAND              */
MIJ6XC     = 0.0,             /* JACK POSITION COMMAND              */

MIJ1VC     = 0.0,             /* JACK VELOCITY COMMAND              */
MIJ2VC     = 0.0,             /* JACK VELOCITY COMMAND              */
MIJ3VC     = 0.0,             /* JACK VELOCITY COMMAND              */
MIJ4VC     = 0.0,             /* JACK VELOCITY COMMAND              */
MIJ5VC     = 0.0,             /* JACK VELOCITY COMMAND              */
MIJ6VC     = 0.0,             /* JACK VELOCITY COMMAND              */

MIJ1AC     = 0.0,             /* JACK ACCELERATION COMMAND              */
MIJ2AC     = 0.0,             /* JACK ACCELERATION COMMAND              */
MIJ3AC     = 0.0,             /* JACK ACCELERATION COMMAND              */
MIJ4AC     = 0.0,             /* JACK ACCELERATION COMMAND              */
MIJ5AC     = 0.0,             /* JACK ACCELERATION COMMAND              */
MIJ6AC     = 0.0,             /* JACK ACCELERATION COMMAND              */
/*
*         --------------------------------------
**        MOTION RESPONSE
*         --------------------------------------
*/
MIKJSCALE  = 0.0,             /* ACTUATOR LEGHT                     */

MIJ1XAC    = 0.0,             /* ACTUAL JACK POSITION  [IN]  */
MIJ2XAC    = 0.0,             /* ACTUAL JACK POSITION  [IN]  */
MIJ3XAC    = 0.0,             /* ACTUAL JACK POSITION  [IN]  */
MIJ4XAC    = 0.0,             /* ACTUAL JACK POSITION  [IN]  */
MIJ5XAC    = 0.0,             /* ACTUAL JACK POSITION  [IN]  */
MIJ6XAC    = 0.0,             /* ACTUAL JACK POSITION  [IN]  */

MIJ1FAC    = 0.0,             /* ACTUAL JACK FORCE                  */
MIJ2FAC    = 0.0,             /* ACTUAL JACK FORCE                  */
MIJ3FAC    = 0.0,             /* ACTUAL JACK FORCE                  */
MIJ4FAC    = 0.0,             /* ACTUAL JACK FORCE                  */
MIJ5FAC    = 0.0,             /* ACTUAL JACK FORCE                  */
MIJ6FAC    = 0.0,             /* ACTUAL JACK FORCE                  */

MIXACC     = 0.0,             /* X-ACCELEROMETER                     */
MIYACC     = 0.0,             /* Y-ACCELEROMETER                     */
MIZACC     = 0.0,             /* Z-ACCELEROMETER                     */
MIPACC     = 0.0,             /* ROLL ACCELEROMETER                  */
MIQACC     = 0.0,             /* PITCH ACCELEROMETER                 */
MIRACC     = 0.0,             /* YAW ACCELEROMETER                   */
/*
*         -------------------
**        MOTION SPARE INPUTS
*         -------------------
*/
MISPRE[20] = { 0.,0.,0.,0.,0.,0.,0.,0.,0.,0.,
               0.,0.,0.,0.,0.,0.,0.,0.,0.,0. } , /* MOTION SPARE VARIABLES */
/*
*
**  special cts and mtp testing
*
*/
YTITRN = .0005;                  /* cts and prgm iteration time */
