/*****************************************************************************
C
C                             FEELSPRING MACRO
C
C'Revision History
C
C   05-AUG-92     M ZAHN
C        ADDED FIX FOR BAD FEELSFO OUTPUT BECAUSE OF LOST INDEX VALUE
C
C   09-JUN-92     M ZAHN
C        ADDED CHECKS FOR ALLOCATED MEMORY AND VALID FEELSPRING DATA.
C        REVISED INTERPOLATION INDEX LIMITING TO USE NUMBER OF BREAKPOINTS
C        INSTEAD OF MINIMUM AND MAXIMUM POSITION BREAKPOINT VALUES.
C
C   08-APR-92     M ZAHN
C        INITIAL MACRO RELEASE
C
C'
C
C*****************************************************************************
C
CC      This macro is to be included in the fast band for all controls
CC   utilizing the DFC feelspring utility.  It carries out the notch
CC   force calculation, as well as feelspring force and friction
CC   interpolations.  The net displacement of the feelspring mechanism
CC   is determined from the trim position and the aft position.
CC   Trim velocity is integrated to give a local trim position which
CC   will vary more smoothly than if the trim position were fed in
CC   directly at the slow band iteration rate.
C
*/

  if (TRUE)
  {
/*
C
CC      Declarations of local variables.
C
*/
           float      notch_for;      /* Notch force before limiting        */
           float      notch_neg;      /* Net negative notch level           */
           float      notch_pos;      /* Net positive notch level           */
           float      netpos;         /* Net displacement of feelspring     */
    static int        init = 0;       /* Memory allocated flag              */
    struct FEEL_INT   *ptr;           /* Pointer to data structure          */
    static float      *c0;            /* Pointer to force intercept         */
    static float      *c1;            /* Pointer to force slope             */
    static float      *c2;            /* Pointer to friction intercept      */
    static float      *c3;            /* Pointer to friction slope          */
    static float      *xb;            /* Pointer to position breakpoints    */
    static int        xindex = 0;     /* Position index                     */
    static int        first = 1;      /* First pass flag                    */

    if (first)
    {
       if (FEEL_FUNC > -1 && FEEL_FUNC < MAX_SERVO)
       {
          init = 1;
          ptr = (struct FEEL_INT *) feel_table[FEEL_FUNC].datain;
          xb  = ptr->pos;
          c0  = ptr->force;
          c1  = ptr->slfor;
          c2  = ptr->fric;
          c3  = ptr->slfri;
          first = 0;
       }
    }
/*
C
CC      The trim velocity (generated at a slower iteration rate) is integrated
CC   to give the trim position.
C
*/
    TRIMP = TRIMP + YITIM * TRIMV;
/*
C
CC      This trim position is subtracted from the aft position to give
CC   the net displacement of the feelspring mechanism.
C
*/
    netpos = QPOS - TRIMP;
/*
C
CC      If the feelspring data is valid, then the feelspring force
CC   and friction values are generated using previously calculated
CC   slope and intercept coefficients.  The net notch force levels
CC   -- calculated by adding the model data values (NNL and NPL) and
CC   the feelspring utility levels (FEELNNL and FEELNPL) -- are added to
CC   the interpolated feelspring force.
C
*/

    if (init && feel_table[FEEL_FUNC].valid)
    {
       if (netpos < FEELXMN) netpos = FEELXMN;
       if (netpos > FEELXMX) netpos = FEELXMX;
       while (xb[++xindex] < netpos && (xindex < FEELBCN-1));
       while (xb[--xindex] > netpos && (xindex > 0));

       notch_for = netpos*KN;
       notch_neg = FEELNNL + NNL;
       notch_pos = FEELNPL + NPL;
       FEELSFO = c0[xindex] + netpos*c1[xindex]
                 + limit(notch_for,notch_neg,notch_pos);
       FEELSFR = c2[xindex] + netpos*c3[xindex];
    }
  }
/*
C
CC      Undefine all variables which were defined.
C
*/

#undef     FEEL_FUNC
#undef     FEELBCN

#undef     QPOS
#undef     TRIMV
#undef     KN
#undef     NNL
#undef     NPL
#undef     FEELNNL
#undef     FEELNPL

#undef     TRIMP
#undef     FEELSFO
#undef     FEELSFR

#undef     FEELXMX
#undef     FEELXMN
