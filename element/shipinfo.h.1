/* $ScmHeader: 9996891yz06z6wyyy8v1999999916&21|@ $*/
/* $Id: shipinfo.h,v 3.78 2002/07/10 10:12:54 scm(MASTER_VERSION|CAE_MR) Rel $*/
/*
C'Title           General Shipinfo file
C'Module_ID       N/A
C'Entry_point     N/A
C'Documentation   N/A
C'Customer        All
C'Application     Define Parameters for different customers
C
C'Author          Integration Specialists
C
C'Date            1998
C
C'System          N/A
C'Iteration time  N/A
C'Process         N/A
C
C'Revision_History
C
C'
C ----------------------
C'Compilation_directives
C ----------------------
C
C                N/A
C
C ------------------------
C'Include_files_directives
C ------------------------
C
C                N/A
C
C
C
C -----------
C'Description
C -----------
C
C	This file is included in many fortran programs that are common
C       to different customers. The mnemonics for the different customers 
C       are defined in this file to indicate to the fortran programs 
C       which simultor the code is running on.
C
C ----------
C'References
C ----------
C
C                N/A
*/
#define FFS  0x46465320
#define FTD  0x46544420
#define CATS 0x43415453
#define FMST 0x464D5354
/* 
      MD11
*/
#define SW11 0x53573131  /* Swissair MD11 FFS */
#define SWFB 0x53574642  /* Swissair MD11 FBS */
#define DFFS 0x44464653  /* McDonnell Douglas MD11 FFS */
#define DCS1 0x44435331  /* McDonnell Douglas MD11 CSS #1 */
#define DCS2 0x44435332  /* McDonnell Douglas MD11 CSS #2 */
#define DCS3 0x44435333  /* McDonnell Douglas MD11 CSS #3 */
#define FNFF 0x464E4646  /* Finnair MD11 FFS */
#define AA11 0x41413131  /* American Airlines MD11 FFS */
#define DA11 0x44413131  /* Delta Airlines MD11 FFS */
#define FX11 0x46583131  /* Federal Express MD11 FFS */
#define CH11 0x43483131  /* China Airlines MD11 FFS */
#define AATD 0x41415444  /* American Airlines MD11 FTD */
#define JL11 0x4A4C3131  /* JAL MD11 FFS */
#define DATD 0x44415444  /* Delta Airlines MD11 FTD */
#define KL11 0x4B4C3131  /* KLM MD11 FFS */
#define JLFM 0x4A4C464D  /* JAL Airlines FMS Trainer */
#define JLSM 0x4A4C534D  /* JAL System Maintenance Trainer */
#define GA11 0x47413131  /* Garuda MD11 FFS */
#define FN11 0x464E3131  /* ???? */
#define CH1T 0x43483154  /* China Airlines MD11 FTD */
#define FX1U 0x46583155  /* Federal Express MD11 Upgrade */
#define FXF1 0x46584631  /* Federal Express MD11 FFS */
#define FXT1 0x46585431  /* Federal Express MD10 FTD #1 */
#define FXT2 0x46585432  /* Federal Express MD10 FTD #2 */
#define FXT3 0x46585433  /* Federal Express MD11 Level 7 FTD */
#define FXT4 0x46585434  /* Federal Express MD11 Level 6 FTD #1 */
#define FXT5 0x46585435  /* Federal Express MD11 Level 6 FTD #2 */
/*  
      MD80 Series
*/
#define SW82 0x53573832  /* Swissair MD81 FFS */
#define AA82 0x41413832  /* American Airlines MD82 */
#define DA81 0x44413831  /* Delta Airlines MD88 FFS #1 */
#define DA82 0x44413832  /* Delta Airlines MD88 FFS #2 */
#define DA88 0x44413838  /* */
#define DAT1 0x44415431  /* Delta Airlines MD88 FTD #1 */
#define DAT2 0x44415432  /* Delta Airlines MD88 FTD #2 */
#define IB87 0x49423837  /* Iberia MD87 */
#define IGPT 0x49475054  /* Iberia MD87 GPT */
#define JA81 0x4A413831  /* JAS MD81 FFS */
#define MD88 0x4D443838  /* McDonnell Douglas MD88 FFS */
/* 
      MD90
*/
#define MD8C 0x4D443843  /* McDonnell Douglas MD88 conv to MD90 */
#define MD9T 0x4D443954  /* McDonnell Douglas MD90 FTD */
#define D90F 0x44393046  /* Delta Airlines MD90 FFS */
#define D90T 0x44393054  /* Delta Airlines MD90 FTD */
#define DCT1 0x44435431  /* Delta Airlines MD90 CAPT #1 */
#define DCT2 0x44435432  /* Delta Airlines MD90 CAPT #2 */
#define M90F 0x4D393046  /* McDonnell Douglas MD90 SV FFS */
/*
      B717
*/
#define BD17 0x42443137  /* Boeing Douglas B717-200 FFS */
#define FS17 0x46533137  /* FSBTI B717 FFS */
/*
      B737
*/
#define AW37 0x41573337  /* America West B737-300/400 FFS */
#define RW37 0x52573337  /* RWL B737-300/400 FFS */
#define KL73 0x4b4c3733  /* KLM B737-406 (-300) FFS#2 */
#define C737 0x43373337  /* China Southern B737-300 FFS */
#define C733 0x43373333  /* CAFC B737-300 FFS */
#define GA73 0x47413733  /* Garuda B737-300/400 FFS */
#define B73T 0x42373354  /* Boeing  B737-300 Level 5 FTD */
#define RM73 0x524d3733  /* Royal Air Maroc B737 400/500 FFS */
#define LU73 0x4c553733  /* DLH B737-300 FFS #1 */
#define LU73 0x4c553733  /* Lufthansa B737-300 FFS #1 */
#define L73F 0x4C373346  /* Lufthansa B737-300 FFS #2 */
#define AN73 0x414e3733  /* ANK B737-500 FFS/MTS */
#define CA73 0x43413733  /* Air China B737-300 FFS */
#define CA3C 0x43413343  /* Air China B737-300 CATS */
#define CA3T 0x43413354  /* Air China B737-300 FTD */
#define B73F 0x42373346  /* Boeing B737-700 FFS Update */
#define B73C 0x42373343  /* */
#define B3FC 0x42334643  /* Boeing B737-700 FBS/MTS Update */
#define SI73 0x53493733  /* SILAB B737-700 #1 */
#define SI32 0x53493332  /* SILAB B737-700 #2 */
#define SA73 0x53413733  /* SAS B737-700 FFS */
#define SA3C 0x53413343  /* SAS B737-700 CSS */
#define SA32 0x53413332  /* SAS B737-700 FFS #2 */
#define C73M 0x4337334D  /* CAIC B737-300 Maintenance Trainer */
#define C73C 0x43373343  /* CAIC B737-300 CATS */
#define AA37 0x41413337  /* American Airlines B737-800 FFS #1 */
#define AA32 0x41413332  /* American Airlines B737-800 FFS #2 */
#define AA33 0x41413333  /* American Airlines B737-800 FFS #3 */
#define BRFT 0x42524654  /* Braathens B737-700 FTD/MTS */
#define BR73 0x42523733  /* Braathens B737-700/800 FFS */
#define KL3F 0x4B4C3346  /* KLM 737-800 FFS */
#define CA37 0x43413337  /* Air China B737-800 FFS */
#define CAC3 0x43414333  /* Air China B737-800 CATS*/
#define B3WT 0x42335754  /* BCAG B737-600/700/800 FFS  */
#define DA3F 0x44413346  /* Delta Airlines B737-200 FFS */
#define DA3T 0x44413354  /* Delta Airlines B737-200 FTD */
#define DAC1 0x44414331  /* Delta Airlines B737-200 CAPT #1 */
#define DAC2 0x44414332  /* Delta Airlines B737-200 CAPT #2 */
#define DA72 0x44413732  /* Delta Airlines B737-800 FFS */
#define DA73 0x44413733  /* Delta Airlines B737-700 FFS */
#define DA78 0x44413738  /* Delta Airlines B737-800 FFS #2 */
#define DA7T 0x44413754  /* Delta Airlines B737-600/700/800 FTD */
#define DA8T 0x44413854  /* Delta Airlines B737-800 FTD #2 */
#define DAC3 0x44414333  /* Delta Airlines B737-600/700/800 CAPT */
#define CT73 0x43543733  /* Continental B737-800 FFS */
#define CT3T 0x43543354  /* Continental B737-800 FTD */
#define FS3T 0x46533354  /* FSBTI B737-700 FTD/MTS */
#define FS37 0x46533337  /* FSBTI B737-700/800 FFS */
#define TA73 0x54413733  /* Turkish Airlines B737-800 FFS */ 
#define TA3M 0x5441334D  /* Turkish Airlines B737-800 FMST */
#define JT7F 0x4A543746  /* Jet Airways B737-800 FFS */
#define JT7T 0x4A543754  /* Jet Airways B737-800 FTD */
#define CH73 0x43483733  /* China Airlines B737-600/700/800 FFS */
#define CH7T 0x43483754  /* China Airlines B737-800 FTD */
#define AZ73 0x415A3733  /* Air New Zealand B737-300 FFS */
#define FS78 0x46533738  /* FSC B737-800 FFS */
#define BFB1 0x42464231  /* Boeing B737-300 FBS to FFS Update */
#define JT73 0x4A543733  /* Jet Airways B737 - xxx xxx */
#define A734 0x41373334  /* American Airlines B737-800 FFS # 4*/
#define FS30 0x46533330  /* Boeing B737-300 FBS to FFS */
#define WJ7F 0x574A3746  /* WestJet B737-700 FFS */
#define CS73 0x43533733  /* GFTS - Sao Paulo B737-800 FFS */
#define RA7F 0x52413746  /* Ryan Air B737-800 FFS # 1*/
#define A735 0x41373335  /* American Airlines B737-800 FFS # 5*/
#define SFS7 0x53465337  /* FSC B737-800 FFS #2 */
#define SWJ3 0x53574A33  /* Southwest B737-700 FFS #1 */
#define SWT3 0x5357A533  /* Southwest B737-700 FFS #1 */
#define B737 0x42373337  /* B737 Platform C */
#define GD73 0x47443733  /* Emirates Aviation Training Center B737-800 */
#define AB42 0x41423432  /* Advance Build B737NG FFS B42 */
#define AB43 0x41423433  /* Advance Build B737NG FFS B43 */
#define AB44 0x41423434  /* Advance Build B737NG FFS B44 */
#define B73P 0x42373350  /* B737NG 700/800 Product Platform */
#define CB37 0x43423337  /* China Airlines B737-600/700/800 FFS */
#define TCA1 0x54434131  /* B737 Training Center #1 FFS */
/*
      B747
*/
#define S742 0x53373432  /* Singapore Airlines B747-412 FFS#2 */
#define S747 0x53373437  /* Singapore Airlines B747-400 FFS#1 */
#define N747 0x4E373437  /* Northwest B747-400 FFS */
#define UA74 0x55413734  /* United Airlines B747-422 FFS */
#define UA47 0x55413437  /* United Airlines B747-422 FFS#2 */
#define KL47 0x4B4C3437  /* KLM B747-400 FFS#2 */
#define KL74 0x4B4C3734  /* KLM B747-400 FFS#1 */
#define LU74 0x4C553734  /* DLH B747-430 FFS#1 */
#define ANF1 0x414E4631  /* ANA B747-400 FFS#1 */
#define ANF2 0x414E4632  /* ANA B747-400 FFS#2 */
#define ANF3 0x414e4633  /* ANA B747-400 FFS#3 */
#define ANMT 0x414E4D54  /* ANA B747-400 Maint Trainer */
#define QA74 0x51413734  /* Qantas B747-400 FFS */
#define CH74 0x43483734  /* China Airlines B747-400 FFS */
#define CH72 0x43483732  /* China Airlines B747-400 FFS #2 */
#define KR74 0x4B523734  /* Korean Airlines B747-400 FFS */
#define CA74 0x43413734  /* Air China B747-400 FFS */
#define CA4C 0x43413443  /* Air China B747-400 CATS */
#define CA4T 0x43413454  /* Air China B747-400 FTD */
#define KLMC 0x4b4c4d43  /* KLM B747-400 Maint. CATS */
#define KLCT 0x4b4c4354  /* */
#define CY74 0x43593734  /* Cathay Pacific  B747-400 FFS */
#define JL74 0x4A4C3734  /* JAL 747-400 */
#define NA74 0x4E413734  /* NASA AMES B747-400 FFS */
#define AS74 0x41533734  /* Asiana Airlines B747-400 FFS */
#define AI74 0x41493734  /* Air India B747-400 FFS#1 */
#define ASCT 0x41534354  /* Asiana Airlines B747-400 Maint CATS */
#define CH4T 0x43483454  /* China airlines B747 ftd */
#define UA4F 0x55413446  /* United Airlines B747-400 #3 */
#define UA44 0x55413434  /* United Airlines B747-400 #4 */
#define KR47 0x4B523437  /* Korean Airlines B747-400 #2 */
#define CR74 0x43523734  /* Cargolux B747-400 FFS */
#define UA7T 0x55413754  /* United Airlines B747-400 FMAST #1 */
#define UA2T 0x55413254  /* United Airlines B747-400 FMAST #2 */
#define CR7C 0x43523743  /* Cargolux B747-400 CATS */
#define B747 0x42373437  /* */
/*
      B757
*/
#define AA57 0x41413537  /* American Airlines B757-200 FFS */
#define AW57 0x41573537  /* America West B757-200 FFS */
#define BA57 0x42413537  /* British Airways B757-236 update */
#define LT57 0x4c543537  /* LTU 757 */
#define NW57 0x4e573537  /* Northwest B757-200 FFS */
#define B757 0x42373537  /* */
#define C757 0x43373537  /* China Southern B757-200 FFS */
#define CA75 0x43413735  /* Air China B757 FFS */
#define CA5C 0x43413543  /* Air China B757 CATS */
#define CA5T 0x43413554  /* Air China B757 FTD */
#define UA5T 0x55413554  /* United Air Lines B757 FMAST #1 */
#define UA52 0x55413532  /* United Air Lines B757 FMAST #2 */
#define UA57 0x55413537  /* United Air Lines B757 FFS #3 */
#define DAC4 0x44414334  /* Delta Airlines B757-200 CAPT #1 */
#define DAC5 0x44414335  /* Delta Airlines B757-200 CAPT #2 */ 
#define A753 0x41373533  /* America Airlines B757-200 FFS #3 */
#define D51U 0x44353155  /* Delta 757 #1 FFS Update */
#define D52U 0x44353255  /* Delta 757 #2 FFS Update */
/*
      B767
*/
#define BA76 0x42413736  /* British Airways B757 conv to B767 */
#define DA67 0x44413637  /* Delta Airlines B767-300ER */
#define DA76 0x44413736  /* Delta Airlines B767-300ER FFS */
#define KU67 0x4B553637  /* Kuwait Airways B767-200ER */
#define KUMT 0x4B554D54  /* Kuwait Airways B767 MTS */
#define LT67 0x4C543637  /* LTU 767-300 FFS */
#define CA76 0x43413736  /* Air China B767-300 */
#define CA6C 0x43413643  /* Air China B767-300 CATS */
#define CA6T 0x43413654  /* Air China B767-300 FTD */
#define KL67 0x4B4C3637  /* KLM B767-300 ER FFS */
#define AS76 0x41533736  /* Asiana Airlines B767-300/300 ER FFS */
#define DA6T 0x44413654  /* Delta Airlines B767-300 ER FTD */
#define AS6M 0x4153364d  /* Asiana B767-300ER FMS Trainer */
#define JL6M 0x4A4C364d  /* JAL B767-300 MTS Level 5 */
#define DA6F 0x44413646  /* Delta Airlines B767-300 ER FFS */
#define D6T1 0x44365431  /* Delta Airlines B767-300 ER FTD */
#define AA67 0x41413637  /* American Airlines B767-300 ER FFS */
#define DA62 0x44413632  /* Delta Airlines B767-400 ER FFS */
#define U762 0x55373632  /* United Airlines B767-300 ER UPDATE */
#define FS6F 0x46533646  /* FSBTI B767-400 ER FFS */
#define UA76 0x55413736  /* United Airlines B767-300 ER FFS */
#define CT76 0x43543736  /* Continental B767-400 ER FFS */
#define AC73 0x41433733  /* Air Canada B767-300 ER FFS */
#define AL67 0x414C3637  /* Alitalia (GTS-Rome) B767-300 ER FFS */
#define JL63 0x4A4C3633  /* JAL B767-300 ER FFS */
#define D61U 0x44363155  /* Delta 767 #1 FFS Update */
#define B767 0x42373637  /* */
/*
      B757 - B767
*/
#define FS76 0x46533736  /* FSC B767/757 FFS */
#define FS75 0x46533735  /* FSBTI (B34) B767/757 FFS */
/*
      B777
*/
#define B7FF 0x42374646  /* Boeing 777 FFS */
#define B7MT 0x42374D54  /* Boeing 777 MTS */
#define B7FT 0x42374654  /* Boeing 777 FTD */
#define BA7F 0x42413746  /* British Airways 777 FFS */
#define BA77 0x42413737  /* British Airways 777 FFS #2 */
#define BA73 0x42413733  /* British Airways 777 FFS #3 */
#define BA7G 0x42413747  /* British Airways 777 GMS */
#define JL7F 0x4A4C3746  /* Japan Airlines 777 FFS */
#define JL7T 0x4A4C3754  /* Japan Airlines 777 FMS Trainer */
#define CY77 0x43593737  /* Cathay Pacific 777 FFS */
#define CS77 0x43533737  /* China Southern 777 FFS */
#define EA77 0x45413737  /* Emirates 777 FFS */
#define JA7F 0x4A413746  /* JAS 777 FFS */
#define JA7T 0x4A413754  /* JAS 777 FMS Trainer */
#define KR77 0x4B523737  /* Korean 777 FFS */
#define KRMT 0x4B524D54  /* Korean 777 MTS */
#define RH77 0x52483737  /* Rehost 777 Avionics Lab */
#define S777 0x53373737  /* Singapore 777 FFS */
#define S7F1 0x53374631  /* Singapore 777 FMS Trainer 2 nd generation #1 */
#define S7F2 0x53374632  /* Singapore 777 FMS Trainer 2 nd generation #2 */
#define SFMS 0x53464D53  /* Singapore 777 FMS Trainer */
#define S772 0x53373732  /* Singapore 777 FFS #2*/
#define S2ER 0x53324552  /* Singapore 777 FFS #2 -200ER CONFIG*/
#define S300 0x53333030  /* Singapore 777 FFS #2 -300 CONFIG */
#define AA77 0x41413737  /* American Airlines 777 FFS */
#define AA72 0x41413732  /* American Airlines 777 FFS #2 */
#define AA73 0x41413733  /* American Airlines 777 FFS #3 */
#define CA77 0x43413737  /* Air China 777 FFS */
#define CA7T 0x43413754  /* Air China 777 FTD */
#define CA7C 0x43413743  /* Air China 777 CATS */
#define CT77 0x43543737  /* Continental 777 FFS */
#define CT7T 0x43543754  /* Continental 777 FTD */
#define DA77 0x44413737  /* Delta Airlines 777 FFS */
#define D7T1 0x44375431  /* Delta Airlines 777 FTD */
#define AL77 0x414C3737  /* GTS Rome B777-200 FFS */
#define AL7D 0x414C3744  /* GTS Rome B777-200 FTD */
#define JL75 0x4A4C3735  /* JAL B777-200ER FFS */
#define JL7X 0x4A4C3758  /* JAL B777-200ER LAB */
#define JL73 0x4A4C3733  /* JAL B777-200ER MTS */
#define E772 0x45373732  /* Emirates B777-300 FFS */
#define B77P 0x42373750  /* Internal B777 Software Development Product */

#define B777_200     0  
#define B777_200ER   1  
#define B777_300     2  
#define B777_100     3  
#define B777_200LR   4
#define B777_300ER   5

/*
      Dash 8
*/
#define USD8 0x55534438  /* US Air Dash 8/100/300 FFS */
#define SAD8 0x53414438  /* SAS Dash 8-100A/300A FFS */
#define SD84 0x53443834  /* SAS Dash 8-400 FFS */
#define QAD8 0x51414438  /* Quanatas Dash 100-300 FFS */
#define GFL2 0x47464C32  /* GTS Dash 8 - 100/300 FFS Madrid */
#define GTT8 0x47545438  /* CAE Aviation Training Dash 8-300/100 FFS Toronto */
#define GTV8 0x47545638  /* CAE Aviation Training Dash 8-300/100 FFS Vancouver */
#define AWD8 0x41574438  /* American West Dash 8-100 FFS */
#define D100 0x44313030  /* Standard Dash 8 100 */
#define D300 0x44333030  /* Standard Dash 8 300 */
#define Q300 0x51333030  /* Standard Dash 8 Q300 */
/*
    A330/A340
*/
#define A34X 0x41333458  /* Airbus A330-A340 Standard Product Lab */
#define A330 0x41333330  /* Airbus 330 */
#define A340 0x41333430  /* Airbus 340 */
#define CY33 0x43593333  /* Cathay Pacific A330 FFS #1 */
#define CY32 0x43593332  /* Cathay Pacific A330 FFS #2  */
#define CYFT 0x43594654  /* Cathay Pacific A330 FTD */
#define L340 0x4C333430  /* Lufthansa A340 FFS */
#define L34C 0x4C333443  /* Lufthansa A340 conversion 330 FFS */
#define S340 0x53333430  /* Singapore A340 FFS */
#define SW33 0x53573333  /* Swissair A330/A340 FFS */
#define SW32 0x53573332  /* Swissair A330/A340 FFS #2 */
#define E330 0x45333330  /* United Arab Emirates A330/A340 FFS */
#define E332 0x45333332  /* United Arab Emirates A330/A340 FFS #2 */
#define AC34 0x41433334  /* Air Canada A330/A340 FFS */
#define L343 0x4C333433  /* Lufthansa A340 FFS #3 */
#define US34 0x55533333  /* US Airways A330 FFS */
#define US3T 0x55533354  /* US Airways A330 FTD #1 */
#define US2T 0x55533254  /* US Airways A330 FTD #2 */
#define CH34 0x43483334  /* China Airlines A330/A340 FFS */
#define SA34 0x53413334  /* SAS A330/340 FFS #1 */
#define C330 0x43333330  /* CSAT A330/340 FFS */
#define L346 0x4C333436  /* Lufthansa Flight Training A340-600 FFS */
#define SW34 0x53573334  /* Swissair A340-600 FFS */
#define A346 0x41333436  /* Standard A340-600 (yibody) */
#define AM18 0x414D3138  /* GFTS Toronto A330/340 FFS */
#define FS33 0x46533333  /* FSBTI Manchester A330/340 FFS */
#define A4KR 0x41344B52  /* Air France A330/340 FFS */
#define AM16 0x414D3136  /* Advance build A340-600 FFS */
#define IB34 0x49423334  /* Iberia A340-313 FFS */
#define AC40 0x41433430  /* Air Canada A330/A340 FFS */
#define A34P 0x41333450  /* Internal A330/A340 S/W Development Product */
#define GD34 0x47443334  /* Emirates Aviation Center A330-200/A340-300 FFS */
#define EV33 0x45563333  /* EVA Air A330-200 (A16) FFS */
/*
      A321/A320/A319
*/
#define A320 0x41333230  /* Standard A320 */
#define A321 0x41333231  /* Standard A321 */
#define A32X 0x41333258  /* Standard A32X Deployment Ship */
#define L319 0x4C333139  /* Lufthansa A319 FFS */
#define L321 0x4C333231  /* Lufthansa A321 FFS */
#define L322 0x4C333232  /* Lufthansa A320-212 FFS */
#define AB32 0x41423332  /* Airbus A320 FFS */
#define NA32 0x4E413332  /* NATCO A320 FFS */
#define C320 0x43333230  /* China Southern A320 FFS */
#define C32T 0x43333254  /* China Southern A320 FTD */
#define AN32 0x414E3332  /* ANA A320/A321 FFS */
#define US3F 0x55533346  /* US Airways A320 FFS #1 */
#define US32 0x55533332  /* US Airways A320 FFS #2 */
#define US33 0x55533333  /* US Airways A320 FFS #3 */
#define US4F 0x55533446  /* US Airways A320 FFS #4 */
#define US5F 0x55533546  /* US Airways A320 FFS #5 */
#define DR32 0x44523332  /* Dragon Air A320 FFS */
#define DR33 0x44523333  /* Dragon Air A330/340 FFS */
#define PA32 0x50413332  /* PAIFA A320 FFS */
#define PA3F 0x50413346  /* PAIFA A320 FFS #2 */
#define FN32 0x464E3332  /* Finnair A320 FFS */
#define AL32 0x414C3332  /* Aer Lingus A320 FFS */
#define FSB2 0x46534232  /* FSBTI A320 FFS */
#define CS32 0x43533332  /* CSAT A320 FFS */
#define SFS3 0x53465333  /* Schreiner FSC A320 FFS #1 */
#define ASI2 0x41534932  /* Asiana Airlines A320-232 FFS */
#define BA32 0x42413332  /* British Airways A320 GMS */
#define PA33 0x50413333  /* PAIFA A320 FFS # 3 */
#define A319 0x41333139  /* Standard A319 (yibody) */
#define AM19 0x414D3139  /* CAE Advance build A320 FFS */
#define AM20 0x414D3230  /* Alitalia A320 FFS */
#define GT32 0x47543332  /* GFTS Toronto A320 FFS */
#define SA2U 0x53413255  /* SAS A320 FFS Update */
#define A32P 0x41333250  /* Internal A320 S/W Development Product */
#define AC23 0x41433233  /* Air Canada A320 FFS #3 */
#define AL20 0x414C3230  /* CAE Aviation Training Rome A320-200 FFS */
#define AM25 0x414D3235  /* Advance Build A320 FFS (A25) */
#define GDA3 0x47444133  /* Emirates Aviation Training Center EATC FFS  */
/*
      A310/A300
*/
#define JA30 0x4A413330  /* JAS A300-600ER FFS */
#define LU10 0x4C553130  /* DLH A310-300/A300-600 FFS */
#define FX30 0x46583330  /* FEDEX A300-600R FFS */
#define FXTD 0x46585444  /* FEDEX A300-600R FTD */
#define S312 0x53333132  /* SIA A310-324 #2 */
#define EK30 0x454b3330  /* Emirates A310-300/A300-600 FFS */
#define EA30 0x45413330  /* Egypt Air A300-600R CSS */
#define EK31 0x454b3331  /* Emirates A310-300/A300-600 FFS */
#define E31T 0x45333154  /* Emirates A310-300 FTD */
#define KU30 0x4b553330  /* Kuwait Airways A310-200/A300-600 FFS */
#define KU31 0x4b553331  /* Kuwait Airways A310-200/A300-600 FFS */
#define KUUP 0x4b555550  /* Kuwait A310-200/A300-600 Upgrade */
#define CH3T 0x43483354  /* China Airlines A300-600 FTD */
#define CH3F 0x43483346  /* China Airlines A300-600 FFS */
#define A300 0x41333030  /* */
#define A310 0x41333130  /* */
#define KU10 0x4b553130  /* Kuwait A310-200/A300-600 */
#define L313 0x4c333133  /* DLH A310-300 #3 FFS */
#define E30T 0x45333054  /* Emirates A300 FTD */
/*
      Saab 340
*/
#define AA34 0x41413334  /* American Airlines SAAB 340 FFS */
#define JA34 0x4A413334  /* JAS SAAB 340B FFS */
#define NA34 0x4E413334  /* PAIFA SAAB 340B FFS */
#define SA3U 0x53413355  /* SAS SAAB 340B FFS Upgrade */
/*
      Fokker 100
*/
#define F070 0x46303730  /* */
#define F100 0x46313030  /* */
#define FS10 0x46533130  /* FSC Fokker 100/70 FFS */
#define FSUP 0x46535550  /* FSC Fokker 100/70 Upgrade */
#define AA10 0x41413130  /* America Airlines Fokker 100 FFS */
#define AA16 0x41413136  /* America Fokker 100 Level 6 */
#define AA17 0x41413137  /* America Fokker 100 Level 7 */
#define KR10 0x4B523130  /* Korean Airlines Fokker 100 FFS */
#define SWF1 0x53574631  /* Swissair Fokker 100 FFS */
#define KLF1 0x4B4C4631  /* KLM Fokker 100 */ 
#define CSF1 0x43534631  /* CSAT Fokker 100 FFS */
/*
      Bombardier
*/
#define RJFF 0x524A4646  /* Canadair RJ FFS #1 */
#define RJF2 0x524A4632  /* Canadair RJ FFS #2 */
#define RJTD 0x524A5444  /* Canadair RJ FTD #1 */
#define RCBT 0x52434254  /* Canadair RJ CBT (CATS) */
#define DLRJ 0x444C524A  /* DLH RJ FFS */
#define CLRJ 0x434C524A  /* DLH RJ FFS #2 */
#define RJST 0x524A5354  /* Canadair RJ FFS */
#define BGEF 0x42474546  /* Canadair Global Express FFS */
#define BGET 0x42474554  /* Canadair Global Express FTD */
#define BGFT 0x42474654  /* Global Express FTD */
#define BGFU 0x42474655  /* Global Express FFS Level D Update */
#define ACRJ 0x4143524A  /* Air Canada RJ FFS */
#define B604 0x42363034  /* Canadair CL-604 FFS */
#define B64T 0x42363454  /* Canadair CL-604 FTD */
#define S601 0x53363031  /* Simuflite 601 FFS */
#define BRJF 0x42524A46  /* Canadair CRJ-700 FFS */
#define BRJT 0x42524A54  /* Canadair CRJ-700 FTD */
#define BRJC 0x42524A43  /* Canadair CRJ-700 CATS */
#define BST2 0x42535432  /* Canadair CRJ-700 System Trainer */
#define PARJ 0x5041524A  /* PAIFA CRJ-200 FFS */
#define PAC2 0x50414332  /* PAIFA CRJ-200 FFS #2 */
#define PAC3 0x50414333  /* PAIFA CRJ-200 FFS #3 */
#define IRJF 0x49524A46  /* ICARE CRJ-200/700 FFS */
#define FSRJ 0x4653524A  /* FSC CRJ-200 FFS */
#define CST7 0x43535437  /* CST CRJ-700 FFS */
#define RJET 0x524A4554  /* RJET Aircraft */
#define GFF1 0x47464631  /* GTS MADRID CRJ-200/700 FFS */
#define GF74 0x47463734  /* GTS SHANDONG CRJ-200/700 FFS */
#define MCRJ 0x4D43524A  /* MCRJ-200/700 FFS */
#define GD2A 0x47443241  /* GTS DENVER 1 CRJ-200/700 FFS */
#define GD2B 0x47443242  /* GTS DENVER 2 CRJ-200/700 FFS */
#define GDST 0x47445354  /* GTS DENVER CRJ-700 System Trainer */
#define AC16 0x41433136  /* Advance Build CRJ-200/700 FFS C16 */
#define AC17 0x41433137  /* Advance Build CRJ-200/700 FFS C17 */
#define B5FF 0x42354646  /* CRJ 700 FFS EICAS 5.0 Update */
#define B5FT 0x42354654  /* CRJ 700 FTD EICAS 5.0 Update */
#define B5ST 0x42355354  /* CRJ 700 ST/CATS EICAS 5.0 & System Update */
#define BLC2 0x424C4332  /* CL604 FFS # 2 */
#define BR9F 0x42523946  /* CRJ-900 FFS Update */
#define BR9T 0x42523954  /* CRJ-900 FTD Update */
#define BST3 0x42535433  /* CRJ-900 ST/CATS Update */
#define BSTU 0x42535455  /* CRJ 200/700 ST Level D Update */
#define GFHU 0x47464855  /* CAE Aviation Training - Horizon CRJ-700 FFS Level D/Tail Update */
/*
      Learjet
*/
#define BL3F 0x424C3346  /* Learjet 31A FFS */
#define BL4F 0x424C3446  /* Learjet 45 FFS #1 */
#define BL42 0x424C3432  /* Learjet 45 FFS #2 */
#define BL6F 0x424C3646  /* Learjet 60 FFS */
#define BL62 0x424C3632  /* Learjet 60 FFS #2 */
#define BL6T 0x424C3654  /* Learjet 60 FTD */
#define BLS1 0x424C5331  /* Learjet 31 FMST */
#define BLS2 0x424C5332  /* Learjet 45 FMST */
#define BLS3 0x424C5333  /* Learjet 60 FMST */
/*
      Embraer
*/
#define AAE1 0x41414531  /* American Airlines ERJ-145 FFS */
#define AAE2 0x41414532  /* American Airlines ERJ-145 FFS #2 */
#define CTE1 0x43544531  /* Continental ERJ-145 FFS#1 */
#define CTE2 0x43544532  /* Continental ERJ-145 FTD#1 */
#define CTE3 0x43544533  /* Continental ERJ-145 CATS  */
#define CTE4 0x43544534  /* Continental ERJ-145 FFS#2 */
#define CTE5 0x43544535  /* Continental ERJ-145 FTD#2 */
#define CTE6 0x43544536  /* Continental ERJ-145 FFS#3 */
#define PAE1 0x50414531  /* PAIFA ERJ-145 FFS #1 */
#define PAE2 0x50414532  /* PAIFA ERJ-145 FFS #2*/
#define G17E 0x47313745  /* Embraer ERJ-170 EDS */
#define CE45 0x43453435  /* Crossair ERJ-145 FFS */
#define E171 0x45313731  /* Embraer/Crossair ERJ-170 FFS */
#define C452 0x43343532  /* Crossair ERJ-145 FFS # 2 */
/*
      Cessna
*/
#define SCCE 0x53434345  /* Simuflite Citation Excel FFS */
#define SCCB 0x53434342  /* Simuflite Citation Bravo FFS */
#define SCCU 0x53434355  /* Simuflite Citation Ultra FFS */
#define CCTE 0x43435445  /* Cessna Citation Excel FFS    */
#define CCTB 0x43435442  /* Cessna Citation Bravo FFS    */
#define CCTU 0x43435455  /* Cessna Citation Ultra FFS    */ 
/*
      Dornier
*/
#define FS38 0x46533338  /* FSC Dornier 328-300 FFS */
#define FS32 0x46533332  /* FSC Dornier 328-300 FFS #2 */
#define SFSD 0x53465344  /* Schreiner FSC D328-300 FFS #3 */
#define SFD4 0x53464434  /* Schreiner FSC D328-300 FFS #4 */
#define SFD5 0x53464435  /* Schreiner FSC D328-300 FFS #5 */
#define PAD1 0x50414431  /* PAIFA Dornier 328-300 FFS */
#define L722 0x4C373232  /* LFTB Dornier 728 FFS # 2 */
#define L728 0x4C373238  /* Lufthansa Flight Training Berlin DO728 FFS */
#define SFS4 0x53465334  /* Schreiner FSC ATR 42/72 FFS */
#define SF42 0x53463432  /* Schreiner FSC ATR 42 */
#define SF72 0x53463732  /* Schreiner FSC ATR 72 */
/*
      Simuflite
*/
#define ADH8 0x41444838  /* CAE Aviation Training Center - Dubai Hawker 800/800XP FFS */
#define SGS4 0x53475334  /* Simuflite Gulfstream IV FFS */
#define SGS5 0x53475335  /* Simuflite Gulfstream V FFS */
#define SF76 0x53463736  /* Simuflite S76 C+ FFS */
#define SH80 0x53483830  /* Simuflite Hawker 800/800 XP FFS */
#define GDG4 0x47444734  /* Emirates Aviation Center Gulfstream IV FFS */
#define GDG5 0x47444735  /* Emirates Aviation Center Gulfstream V FFS */
#define GDGE 0x47444745  /* Emirates Aviation Center Global Express FFS */
#define H8XP 0x48385850  /* Standard HAWKER 800 XP */ 
#define H800 0x48383030  /* Standard HAWKER 800 */ 

/*
      Helicopter
*/
#define S412 0x53343132  /* SAS Bell 412 */
/*
      Military
*/
#define M29F 0x4d323946  /* Royal Malaysian Air Force MiG-29 FMS */
#define M29T 0x4d323954  /* Royal Malaysian Air Force MiG-29 OFT */
#define E101 0x45313031  /* Merlin */
#define LYM8 0x4c594d38  /* Lynx */
#define BFMS 0x42464D53  /* Australian Army Black Hawk */
#define MSP1 0x4D535031  /* MSH Puma DMS */
#define MSM1 0x4D534D31  /* MSH Merlin DMS #1 */
#define MSM2 0x4D534D32  /* MSH Merlin DMS #2 */
#define MSC1 0x4D534331  /* MSH Chinook DMS #1 */
#define MSC2 0x4D534332  /* MSH Chinook DMS #2 */
#define MSC3 0x4D534333  /* MSH Chinook DMS #3 */
#define MSTC 0x4D535443  /* MSH TCC */
#define NFET 0x4E464554  /* Bombardier NFTC NFET */
#define NTP2 0x4E545032  /* Bombardier NFTC NTP2 */
#define NFBH 0x4E464248  /* Bombardier NFTC NFBH */
#define NBH2 0x4E424832  /* Bombardier NFTC NBH2 */
#define IC30 0x49433330  /* Indonesian Air Force C130H FFS */
/*
      Sim XX1
*/
#define SI21 0x53493231  /* Sim XX1 Program RJ 700 FFS */
/*
      CMS
*/
#define ECMS 0x45434D53  /* EADS/Airbus Cabin Motion System CMS/Ro-Ro */
/* 
      PC-BASE UPDATE 
*/ 
#define AINU 0x41494E55  /* AIRBUS A310-300 PC-BASE TCAS */ 
#define FSCU 0x46534355  /* FOKKER 50 PC-BASE TCAS       */ 
#define INTU 0x494E5455  /* AIRBUS A320 PC-BASE TCAS     */ 
#define CSAU 0x43534155  /* BRAATHENS 737 PC-BASE TCAS   */ 
#define SB3U 0x53423355  /* SABENA PC-BASE TCAS          */ 
#define JATU 0x4A415455  /* JAS A300-B PC-BASE TCAS      */ 
