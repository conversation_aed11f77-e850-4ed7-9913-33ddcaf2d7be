C'Title              COEFFICIENT BUILDUP
C'Module_ID          USD8VZ
C'Entry_point        LIFT
C'Documentation      TBD
C'Application        Buildup of aerodynamic coefficients
C'Author             Department 24, Flight
C'Date               May 1, 1991
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C'
C
C'Revision_history
C
C  usd8vz.for.34 27Aug1993 01:18 usd8 pve
C       < hardcode tuning gains for left engine out flaps 15 >
C
C  usd8vz.for.33 24Aug1993 23:44 usd8 paul va
C       < flaps 15 vmca tuning >
C
C  usd8vz.for.32 28Jan1993 00:32 usd8 paul va
C       < reduce lthrust to .15 and vcdart to 0.0 as a result of NRC
C         reconstruction of stopping time with reverse thrust case. >
C
C  usd8vz.for.31 23Aug1992 00:48 usd8 PVE
C       < HARDCODE LTHRUST TO .3 FROM .35 , AND HARDCODE VCDART TO .05
C         FROM .224 AS A RESULT OF NEW THRUST CURVES FOR A7_1D2 >
C
C  usd8vz.for.30 22Aug1992 22:07 usd8 PVE
C       < TUNING TO COMMENTS FROM STEWART BAILLIE >
C
C  usd8vz.for.29 31Jul1992 03:55 usd8 pve
C       < more yaw tuning >
C
C  usd8vz.for.28 31Jul1992 02:00 usd8 pve
C       <
C
C  usd8vz.for.27 30Jul1992 21:10 usd8 pve
C       < more yaw tuneing >
C
C  usd8vz.for.26 30Jul1992 19:37 usd8 pve
C       < yaw tuning >
C
C  usd8vz.for.25 30Jul1992 12:48 usd8 pve
C       < drift tuneing >
C
C  usd8vz.for.24 30Jul1992 11:32 usd8 pve
C       < tuning code to check drift on approach >
C
C  usd8vz.for.23 29Jul1992 23:07 usd8 PVE
C       < INCREASE LIFT DUE TO THRUST AS TWO TAKEOFF CASES ARE AFFECTED >
C
C  usd8vz.for.22 28Jul1992 17:44 usd8 paulv
C       < new assymetric thrust model plus tuneing >
C
C  usd8vz.for.21 30Apr1992 15:20 usd8 pve
C       < hardcode stall tuning >
C
C  usd8vz.for.20 30Apr1992 11:22 usd8 pve
C       < stall tuning >
C
C  usd8vz.for.19 30Apr1992 11:17 usd8 pve
C       < stall tuning >
C
C  usd8vz.for.18 29Apr1992 06:27 usd8 pve
C       < add tuning flags >
C
C  usd8vz.for.17 29Apr1992 00:11 usd8 pve
C       < add tuning labels >
C
C  usd8vz.for.16 22Apr1992 08:23 usd8 PVE
C       < MANUAL ATG LOGIC FOR AILERONS  >
C
C  usd8vz.for.15 17Apr1992 04:34 usd8 PVE
C       <
C
C  usd8vz.for.14 17Apr1992 04:25 usd8 pve
C       < add nose down moment for flaps 30 stall >
C
C  usd8vz.for.13 16Apr1992 00:44 usd8 PVE
C       < CORRECT CNRUD TERM (USE SCALED RUDDER DEFLECTION) >
C
C  usd8vz.for.12 10Apr1992 00:04 usd8 PLam
C       < Increased spoiler drag coefficient >
C
C  usd8vz.for.11  4Apr1992 01:02 usd8 pve
C       < update aero model for large beta and ground spoilers >
C
C  usd8vz.for.10 23Mar1992 00:21 usd8 pve
C       < add code for gear change as gear not at 60 hertz >
C
C  usd8vz.for.9 10Mar1992 00:12 usd8 PVE
C       < ASSIGN OUTER SPOILERS IF VAILCON AND SPEED GT 135 >
C
C  usd8vz.for.8  6Mar1992 12:52 usd8 pve
C       < correct roll due to assymetric ice (it was on all the time) >
C
C  usd8vz.for.7  5Mar1992 12:51 usd8 PLam
C       < Added label declarations >
C
C  usd8vz.for.6 17Feb1992 15:19 usd8 pve
C       < update to latest aero version 21 >
C
C  usd8vz.for.5 20Dec1991 21:56 usd8 paulv
C       < add divide by zero protection >
C
C  usd8vz.for.4 20Dec1991 16:24 usd8 paulv
C       < remove compile errors >
C
C  usd8vz.for.3 20Dec1991 16:22 usd8 paulv
C       < update to level of latest stf software >
C
C'
C
      SUBROUTINE USD8VZ
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/20/91 - 16:24 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vz.for.34 27Aug1993 01:18 usd8 pve    $'/
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
CQ    USD8 XRFTEST(*)
C
C  INPUTS
C
CP    USD8
CPI  C     CATRIM,
CPI  V     VAI,            VALPHA,         VCNDA,          VCNDSPI,
CPI  V     VCNDSPO,        VCNPHAT,        VCNRHAT,        VCNRUD,
CPI  V     VCRADD,         VCRDA,          VCRDSPI,        VCRDSPO,
CPI  V     VCRPHAT,        VCRRHAT,        VCRRUD,         VCRWB,
CPI  V     VCSGD1,         VCSGD2,         VCSGD7,         VCSGD8,
CPI  V     VCSPI,          VCSPLI,         VCSPLO,         VCSPO,
CPI  V     VCSPRI,         VCSPRO,         VCT1,           VCT2,
CPI  V     VCTSUM,         VDUC,           VEFNS,          VELVL,
CPI  V     VELVR,          VFCDBAS,        VFCLBAS,        VFCLDEGE,
CPI  V     VFCLGE,         VFCM0,          VFCMB2,         VFCMBAS,
CPI  V     VFCNDA,         VFCNDSPI,       VFCNDSPO,       VFCNRUD,
CPI  V     VFCRDA,         VFCRDSPI,       VFCRDSPO,       VFCRRUD,
CPI  V     VFCYB,          VFDCDG,         VFDCLCT,        VFDCLDE,
CPI  V     VFDCLNZ,        VFDCLQH,        VFDCMQH,        VFDCNB,
CPI  V     VFDCNP,         VFDCNR,         VFDCRB,         VFDCRP,
CPI  V     VFDCRR,         VFDCYP,         VFDCYR,         VFLAPD,
CPI  V     VFRZAER,        VH,             VICEF1,         VNZL,
CPI  V     VPHAT,          VPRESS,         VQHAT,          VQPRS,
CPI  V     VR,             VRHAT,          VRSPARE0,       VRSPARE1,
CPI  V     VSTALLQ1,       VSTALLQ2,       VSTALLZ,        VTAI,
CPI  V     VTAIL,          VTAIR,          VUA,            VVA,
CPI  V     VVDA,           VVT1INV,        VWA,            VXCG,
C
C  OUTPUTS
C
CPO  V     VBETA,          VCDBAS,         VCDBETA,        VCDCT,
CPO  V     VCDGER,         VCDS,           VCLBAS,
CPO  V     VCLBETA,        VCLCT,          VCLDE,          VCLGE,
CPO  V     VCLLS,          VCLNZ,          VCLQHAT,        VCLS,
CPO  V     VCM,            VCM0,           VCMBAS,
CPO  V     VCMDE,          VCMGE,          VCMGER,         VCMQHAT,
CPO  V     VCNDE,          VCNS,           VCNWB,          VCRDE,
CPO  V     VCY,            VCYADD,         VCYBAS,         VCYDA,
CPO  V     VCYDSPI,        VCYDSPO,        VCYGER,         VCYPHAT,
CPO  V     VCYRHAT,        VCYRUD,         VDCNCT,         VDCRCT,
CPO  V     VTRIM
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:09:57 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  CATRIM         ! AILERON TRIM POSITION
     &, VAI            ! INVERSE OF SPEED OF SOUND AT A/C      [s/ft]
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VCNDA          ! TOTAL YAW COEFFICIENT DUE TO AILERONS
     &, VCNDSPI        ! CN DUE TO INBOARD SPOILERS
     &, VCNDSPO        ! CN DUE TO OUTBOARD SPOILERS
     &, VCNPHAT        ! EFFECT OF ROLL RATE ON CN
     &, VCNRHAT        ! EFFECT OF YAW RATE ON CN
     &, VCNRUD         ! RIGID RUDDER YAW MOMENT COEFF.
     &, VCRADD         ! ADDITIONAL ROLL COEFFICIENT D611T305 PG 6-31
     &, VCRDA          ! TOTAL ROLL MOMENT DUE TO AILERONS
     &, VCRDSPI        ! CLL DUE TO INBOARD SPOILERS
     &, VCRDSPO        ! CLL DUE TO OUTBOARD SPOILERS
     &, VCRPHAT        ! EFFECT OF ROLL RATE ON CLL
     &, VCRRHAT        ! EFFECT OF YAW RATE
     &, VCRRUD         ! RIGID RUDDER ROLL MOMENT COEFF.
     &, VCRWB          ! RIGID WING BODY CLL DUE TO BETA
     &, VCSGD1         ! LEFT OUTBOARD GND SPLR POSITION        [deg]
     &, VCSGD2         ! LEFT INBOARD GND SPLR POSITION         [deg]
     &, VCSGD7         ! RIGHT INBOARD GND SPLR POSITION        [deg]
     &, VCSGD8         ! RIGHT OUTBOARD GND SPLR POSITION       [deg]
     &, VCSPI          ! AVERAGE OF INBOARD SPOILERS            [deg]
     &, VCSPLI         ! LEFT INBOARD SPOILER                   [deg]
     &, VCSPLO         ! LEFT OUTBOARD SPOILER                  [deg]
     &, VCSPO          ! AVERAGE OF OUTBOARD SPOILERS           [deg]
     &, VCSPRI         ! RIGHT INBOARD SPOILER                  [deg]
     &, VCSPRO         ! RIGHT OUTBOARD SPOILER                 [deg]
     &, VCT1           ! ENGINE 1 THRUST COEFFICIENT
     &, VCT2           ! ENGINE 2 THRUST COEFFICIENT
     &, VCTSUM         ! SUM OF THRUST COEFFICIENTS
     &, VDUC           ! AVG. GEAR EXT.   1=DN,0=UP
      REAL*4   
     &  VEFNS          ! SUM OF NET THRUSTS                     [lbs]
     &, VELVL          ! LEFT ELEVATOR ANGLE +TED               [deg]
     &, VELVR          ! RIGHT ELEVATOR ANGLE +TED              [deg]
     &, VFCDBAS        ! Cd BASIC f(Cl,flap)
     &, VFCLBAS        ! Basic lift as a function of alpha and flaps
     &, VFCLDEGE       ! CM GROUND EFFECTS TERM ON ELEVATOR
     &, VFCLGE         ! LIFT DUE TO GROUND EFFECTS
     &, VFCM0          ! First order pitching moment (alpha = 0)
     &, VFCMB2         ! Additional CM at high angle of attack
     &, VFCMBAS        ! Cm due to alpha, Ct as a function of cg
     &, VFCNDA         ! TOTAL YAW COEFFICIENT DUE TO AILERONS
     &, VFCNDSPI       ! CN DUE TO INBOARD SPOILERS
     &, VFCNDSPO       ! CN DUE TO OUTBOARD SPOILERS
     &, VFCNRUD        ! RIGID RUDDER YAW MOMENT COEFF.
     &, VFCRDA         ! TOTAL ROLL MOMENT DUE TO AILERONS
     &, VFCRDSPI       ! CLL DUE TO INBOARD SPOILERS
     &, VFCRDSPO       ! CLL DUE TO OUTBOARD SPOILERS
     &, VFCRRUD        ! RIGID RUDDER ROLL MOMENT COEFF.
     &, VFCYB          ! BASIC CY BETA FOR |BETA|<10 DEG
     &, VFDCDG         ! GEAR drag --> f(flap setting)
     &, VFDCLCT        ! Derivative of lift due to Ct
     &, VFDCLDE        ! Delta CL due to elevator deflection
     &, VFDCLNZ        ! Derivative of lift due to Normal Load
     &, VFDCLQH        ! Derivative of lift due to pitch rate
     &, VFDCMQH        ! Derivative of pitch due to pitch rate
     &, VFDCNB         ! EFFECT OF BETA ON CN
     &, VFDCNP         ! NON NORMALIZE CN DUE TO ROLL RATE
     &, VFDCNR         ! NON NORMALIZE CN DUE TO YAW  RATE
     &, VFDCRB         ! CLL DUE TO BETA PER DEGREE BETA
     &, VFDCRP         ! NON NORMALIZE CLL DUE TO ROLL RATE
     &, VFDCRR         ! NON NORMALIZE CLL DUE TO YAW  RATE
      REAL*4   
     &  VFDCYP         ! CY DEPENDENCE ON PHAT
     &, VFDCYR         ! SIDEFORCE COEFFICIENT CYr
     &, VFLAPD         ! FLAP DETENT POSITION
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VICEF1         ! FLIGHT ICEING FACTOR
     &, VNZL           ! BODY AXES NORMAL LOAD FACTOR             [G]
     &, VPHAT          ! NON DIMENSIONAL ROLL RATE
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, VQHAT          ! NON DIMENSIONAL PITCH RATE
     &, VQPRS          ! DYNAMIC PRESSURE * WING AREA           [lbs]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VRHAT          ! NON DIMENSIONAL YAW RATE
     &, VRSPARE0       ! REAL SPARE
     &, VRSPARE1       ! REAL SPARE
     &, VSTALLQ1       ! STALL HYSTERISIS PITCH TERM
     &, VSTALLQ2       ! STALL HYSTERISIS PITCH TERM
     &, VSTALLZ        ! STALL HYSTERISIS LIFT TERM
     &, VTAI           ! AVERAGE STATUS OF THERMAL ANTI-ICE SYSTEM
     &, VTAIL          ! STATUS OF THERMAL ANTI-ICE SYSTEM LEFT W.
     &, VTAIR          ! STATUS OF THERMAL ANTI-ICE SYSTEM RIGHT W.
     &, VUA            ! BODY AXES X VELOCITY WRT AIR          [ft/s]
     &, VVA            ! BODY AXES Y VELOCITY WRT AIR          [ft/s]
     &, VVDA           ! NAE AILERON ARGUMENT (= - VAIL)        [deg]
     &, VVT1INV        ! INVERSE OF VVT1                       [s/ft]
     &, VWA            ! BODY AXES Z VEL. WRT AIR              [ft/s]
     &, VXCG           ! CENTRE OF GRAVITY                       [in]
C$
      LOGICAL*1
     &  VFRZAER        ! FLAG TO SET ALL AERO FORCES TO ZERO
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VCDBAS         ! BASIC ELASTIC A/P CD WITH ZERO CONTROL INPUT
     &, VCDBETA        ! CHANGE IN CD DUE TO BETA
     &, VCDCT          ! TOTAL CD DUE TO THRUST EFFECTS
     &, VCDGER         ! CD DUE TO EXTENSION OF LANDING GEAR
     &, VCDS           ! STABILITY AXIS TOTAL DRAG COEFFICIENT
     &, VCLBAS         ! BASIC WING BODY LIFT COEFFICIENT
     &, VCLBETA        ! CL DUE TO SIDESLIP
     &, VCLCT          ! CL DUE TO SECONDARY THRUST EFFECTS
     &, VCLDE          ! CL DUE TO ELEVATOR DEFLECTION
     &, VCLGE          ! LIFT DUE TO GROUND EFFECTS
     &, VCLLS          ! STABILITY AXES TOTAL ROLL MOMENT COEFFICIENT
     &, VCLNZ          ! TOTAL CL DUE TO NORMAL LOAD
     &, VCLQHAT        ! TOTAL CL DUE TO NORMALIZED PITCH RATE
     &, VCLS           ! TOTAL CL IN THE STABILITY AXIS
     &, VCM            ! TOTAL PITCHING MOMENT COEFF (BODY AXES)
     &, VCM0           ! BASIC A/C CM INDEPENDANT OF ALPHA
     &, VCMBAS         ! BASIC WING-BODY CM AS A FUNCTION OF ALPHA
     &, VCMDE          ! TOTAL CHANGE IN CM DUE TO ELEVATOR
     &, VCMGE          ! PITCHING MOMENT DUE TO GROUND EFFECTS
     &, VCMGER         ! CM DUE TO EXTENSION OF LANDING GEAR
     &, VCMQHAT        ! TOTAL CM DUE TO NORMALIZED VQ
     &, VCNDE          ! YAWING MOMENT DUE TO ASSYMMETRIC ELEVATOR
     &, VCNS           ! TOTAL YAW MOMENT COEFFICIENT (STAB. AXES)
     &, VCNWB          ! RIGID WING-BODY CN DUE TO BETA
     &, VCRDE          ! ROLLING MOMENT DUE TO ASSYMMETRIC ELEVATOR
     &, VCY            ! BODY AXES TOTAL SIDEFORCE COEFFICIENT
     &, VCYADD         ! Additional sideforce for |beta|>10 deg
     &, VCYBAS         ! BASIC ELASTIC A/P CY WITH ZERO CONTROL INPUT
     &, VCYDA          ! CY DUE TO AILERONS
     &, VCYDSPI        ! SIDEFORCE DUE TO INBOARD SPOILERS
      REAL*4   
     &  VCYDSPO        ! SIDEFORCE DUE TO INBOARD SPOILERS
     &, VCYGER         ! CY DUE TO EXTENSION OF LANDING GEAR
     &, VCYPHAT        ! TOTAL CY DUE ROLL RATE
     &, VCYRHAT        ! TOTAL CY DUE YAW RATE
     &, VCYRUD         ! RIGID RUDDER SIDEFORCE COEFFICIENT
     &, VDCNCT         ! Additional yawing moment due to CT
     &, VDCRCT         ! Additional rolling moment due to CT
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
C$
      LOGICAL*1
     &  DUM0000001(16383),DUM0000002(96),DUM0000003(12)
     &, DUM0000004(12),DUM0000005(76),DUM0000006(4)
     &, DUM0000007(16),DUM0000008(32),DUM0000009(8)
     &, DUM0000010(28),DUM0000011(4),DUM0000012(12)
     &, DUM0000013(8),DUM0000014(8),DUM0000015(48)
     &, DUM0000016(4),DUM0000017(4),DUM0000018(4),DUM0000019(4)
     &, DUM0000020(20),DUM0000021(40),DUM0000022(252)
     &, DUM0000023(12),DUM0000024(12),DUM0000025(8)
     &, DUM0000026(20),DUM0000027(4),DUM0000028(4)
     &, DUM0000029(16),DUM0000030(4),DUM0000031(20)
     &, DUM0000032(8),DUM0000033(40),DUM0000034(8)
     &, DUM0000035(52),DUM0000036(8),DUM0000037(12)
     &, DUM0000038(16),DUM0000039(4),DUM0000040(360)
     &, DUM0000041(1888),DUM0000042(52),DUM0000043(480)
     &, DUM0000044(896),DUM0000045(1272),DUM0000046(8)
     &, DUM0000047(4),DUM0000048(40),DUM0000049(8604)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VFRZAER,DUM0000002,VELVL,VELVR,DUM0000003
     &, VVDA,DUM0000004,VCSPI,VCSPO,VCSPLI,VCSPRI,VCSPLO,VCSPRO
     &, VCSGD1,VCSGD2,VCSGD7,VCSGD8,DUM0000005,VCT1,VCT2,DUM0000006
     &, VEFNS,DUM0000007,VCTSUM,DUM0000008,VFLAPD,DUM0000009
     &, VDUC,DUM0000010,VSTALLZ,VSTALLQ1,VSTALLQ2,VCDBETA,VCDCT
     &, VCDGER,VCLBETA,VCLCT,VCLDE,VCLNZ,VCLBAS,VCLQHAT,VCLS
     &, DUM0000011,VCLGE,DUM0000012,VNZL,DUM0000013,VWA,DUM0000014
     &, VALPHA,DUM0000015,VCYDA,VCYDSPI,VCYDSPO,DUM0000016,VCYADD
     &, DUM0000017,VCYPHAT,VCYRHAT,DUM0000018,VCYBAS,VCYRUD,VCYGER
     &, DUM0000019,VCY,DUM0000020,VVA,VBETA,DUM0000021,VCDBAS
     &, VCDS,DUM0000022,VUA,DUM0000023,VVT1INV,DUM0000024,VPRESS
     &, DUM0000025,VQPRS,DUM0000026,VCRDA,VCRDSPI,VCRDSPO,VCRPHAT
     &, VCRRHAT,VDCRCT,DUM0000027,VCRRUD,DUM0000028,VCRWB,DUM0000029
     &, VCRDE,VCLLS,DUM0000030,VCRADD,DUM0000031,VPHAT,VCMDE
     &, VCMGER,DUM0000032,VCM0,VCMBAS,VCMQHAT,VCMGE,DUM0000033
     &, VCM,DUM0000034,VQHAT,DUM0000035,VCNDA,VCNDSPI,VCNDSPO
     &, VCNPHAT,VCNRHAT,VDCNCT,DUM0000036,VCNRUD,DUM0000037,VCNWB
     &, VCNS,VCNDE,DUM0000038,VR,DUM0000039,VRHAT,DUM0000040
     &, VH,DUM0000041,VICEF1,VTAI,VTAIL,VTAIR,DUM0000042,VXCG
     &, DUM0000043,VTRIM,DUM0000044,VRSPARE0,VRSPARE1,DUM0000045
     &, VAI,VFCLBAS,VFCM0,VFCMBAS,VFCMB2,DUM0000046,VFDCDG,VFDCLCT
     &, VFDCLDE,VFDCLNZ,VFDCLQH,VFDCMQH,VFCLGE,VFCYB,VFDCYP,VFDCYR
     &, VFDCRB,VFDCRP,VFDCRR,VFCRDA,VFCRDSPI,VFCRDSPO,VFCNDSPI
     &, VFCNDSPO,VFCRRUD,VFDCNB,VFDCNP,VFDCNR,VFCNDA,VFCNRUD
     &, DUM0000047,VFCLDEGE,DUM0000048,VFCDBAS,DUM0000049,CATRIM    
C------------------------------------------------------------------------------
C
      LOGICAL LP1/.FALSE./,LP2/.FALSE./,LP3/.FALSE./,LP4/.FALSE./
      REAL X1,LCMQ,LBETA,lbeta2
      REAL TEMP,LEXTEND
      REAL LCYCT,lsp0,lduc,lrlstall,istallb,i2
      REAL Lalphmax,astall,astick,abuff
      Integer istall
      REAL LVCTSUM,LVCTSUM2,LVCT1,LVCT2,lrud,LVCTDIF
      REAL VCDSP,VCDSPI,VCDSPO,VFCDSPI/.0002/,VFCDSPO/.0002/
      real vcdart/.0/
      REAL VCLDSPI,VCLDSPO,VCLSP,VCDSPGND,VFCDSPGD/.0002/
      real lsp1,lsp2,rho,sf1,SF3,sm1/1./,SM3/1./,lfbeta,labeta
      real qprst,dua,LUA,VFCYRUD,CN_LBETA/.05/,LHBETA/10./,K1
      REAL CR_ct/.006/,lctr/.16/,f10/2./,f11/1.0/,lrg
      real ldrud,lrudg/.5/,lcylim/1./,lsbeta,lrudb/10./,labetaf,lbetaf
      real lthrust/0.15/,rctsum
      REAL VCLSPGND,VCLICE,VCMICE,VCRICE,VCDICE
      REAL VFCLICE /-.05/     !  Lift due to icing
      REAL VFCMICE /-.075/    !  Pitch due to icing
      REAL VFCDICE /.01/      !  Drag due to icing
      REAL VFCRICE /-.005/    !  Roll due to assymetrical icing
      REAL LREVTF /1.0/       !  Reverse thrust effectiveness factor
      REAL SCALET             !  Tail versus body dynamic pressure ratio
      real LGE,lg1,lg2,lg3,f12/.5/,LG4/.3/,f13/1./,f14/1./,f7,f8/.5/,f9
      REAL LGT/.05/    ! AILERON TRIM TAB EFFECTIVENESS
      real lc1,lc15/-.0008/,lc115/0.0/,lc135/-.0008/
      real valpha1,valpha2/15./,VALPHA3/3./
      real lct/-.02433 /
      real lct5/-.02433 /
      real lct15/-.02433 /
      real lct35/-.02433 /
C !FM+
C !FM  24-Aug-93 23:40:46 paul van esbroeck
C !FM    < flap 15 vmca tuning >
C !FM
C
      real LT1,lt15/0.0/,lt115/3.0/,lt1a/.3/,lt135/1.0/
      real LLT,LT2A/-.15/,llt5/0.0/,llt15/-9.5/,llt35/0.0/
C
C     retuned above constanst for flaps 15 only.
C     left and right engine failures.
C     all other flaps unchanged
C     august 24 1993, Paul van Esbroeck
C !FM-
C
      ENTRY LIFT
C
C     Limit CT values for aerodynamic purposes
C
        LVCTSUM = amax1(-.5,AMIN1(.5,VCTSUM))
        LVCT1 = amin1(.25,vct1)
        LVCT2 = amin1(.25,vct2)
        LSP1 = AMAX1(0.,VCT1)
        LSP2 = AMAX1(0.,VCT2)
        LVCTDIF = AMAX1(-.5,AMIN1(.5,(LSP1-LSP2)))
C
C Protection limits and fades on beta
C
        labeta  = abs(vbeta)
        lbeta  = vbeta
        lbeta2 = vbeta
        if(labeta .gt. 20.)then
          lbeta  = 10.*vbeta/labeta
          lbeta2 = 20.*vbeta/labeta
        elseif(labeta.gt.10.)then
          lbeta  = 10.*vbeta/labeta
          lbeta2 = vbeta
        endif
        if (labeta.gt.90)then
          lfbeta = abs(sin(vbeta/57.3))
        else
          lfbeta = 1.
        endif
C
c        limit rudder effectiveness at large angles
c
c
        lbetaf = vbeta - vr*40.*vvt1inv*57.3 * f11
        labetaf = abs(lbetaf)
        if ((lbetaf*vrspare0).gt.0)then
          lsbeta = 1.
        else
          lsbeta = -1.
        endif
        if (labetaf.gt.lrudb) then
          ldrud = 1.- lsbeta * (labetaf-lrudb)/labetaf * lrudg*lfbeta
        else
          ldrud = 1.0
        endif
        IF (LP2)THEN
          lrud=vrspare1 * ldrud
        ELSE
          lrud=vrspare0 * ldrud
        ENDIF
c
c        Dynamic pressure correction at tail for low airspeeds
c
c       calculate tail dynamic pressure
c
        lsp0 = vua/2.
        rho = 1.4 * vpress * vai * vai
        if(rho.gt.0.)then
         lsp1 = lsp0*lsp0 + vefns*.001/rho
        else
         lsp1 = 0.
        endif
        if (lsp1 .lt. 0.)lsp1=0.
        dua =  -lsp0 + sqrt(lsp1)
        lua = vua + dua
        qprst = .5*rho*(lua*lua+vva*vva+vwa*vwa)*585.
C
C       calculate ratio of tail dynamic pressure to that of c.g.
C
        IF (VQPRS .LT. .01)THEN
          scalet = qprst/.01
          LSP0 = (VEFNS/.01)
        ELSE
          scalet = qprst/vqprs
          LSP0 = (VEFNS/VQPRS)
        ENDIF
C
C       fade in in/decreased effects due to tail dynamic pressure
C       depending on forward or reverse thrust.
C
        if(lsp0.gt.0.)then
         sf1 = (scalet-1.)*(LSP0-LVCTSUM)/LSP0
         IF (SF1 .LT. 0.) SF1=0.
         sf3 = SF1
        else
         sf1 = (scalet-1.) * LREVTF
         if( sf1.lt.-.75)sf1=-.75
         SF3 = 0.
        endif
C
c       set all aero forces to zero if Vfrzaer = true
c
        if(VTRIM.eq.0)istall=0
        if(vfrzaer)then
         VCLS=0.0
         VCDS=0.0
         VCY=0.0
         VCM=0.0
         VCNS=0.0
         VCLLS=0.0
        RETURN
        endif
c
C        Calculation of CL
C
c       Stall model
c
c        input shaker, buffet and stall angles for the given flap setting
c
        if(vflapd.lt.4.0)then
C         astick = 14.
C         abuff  = 18.5
         astall = 19.
        elseif(vflapd.lt.10.)then
C         astick = 14.3
C         abuff  = 17.6
         astall = 18.0
        elseif(vflapd.lt.20.)then
C         astick = 14.0
C         abuff  = 18.7
         astall = 20.0
        else
C         astick = 12.4
C         abuff  = 17.3
         astall = 19.0
        endif
c
c       stall hysterisis added to basic lift
c
        VCLBAS = VFCLBAS - VSTALLZ
c
c       Delta CL for Ground Effect
c
        VCLGE = VFCLGE
c
c       power effect on CL
c
        VCLCT  = VFDCLCT * (LVCT1+LVCT2)
        if(vclct.lt.0.)vclct=0.
C
C        ELEVATOR INDUCED CL
C
        VCLDE = VFDCLDE
C
c        reduction in Cl due to sideslip
c
        VCLBETA =(-.00165*LBETA-.001297*LBETA*LBETA)*lfbeta
c
        VCLQHAT = VFDCLQH * VQHAT
c
c        lift curve slope change due to dynamic effects
c        or wing torsion with applied 'g'
C
        lsp0 = amin1(1.,amax1(-1.,(vnzl-1.)))
        VCLNZ = VFDCLNZ * lsp0
C
C       Lift due to spoilers
C
C         25.4 ft is approx distance of centre of inner spoiler
C         to aircraft centre line.  Outer spoiler is about
C         31.4 ft. from A/C centreline.
C
        VCLDSPI = VFCRDSPI * (VCSPLI+VCSPRI) * 84./25.4
        VCLDSPO = VFCRDSPO * (VCSPLO+VCSPRO) * 84./31.4
C
        vclspgnd = vfcrdspi *84/25.4 * (VCSGD1+VCSGD2+VCSGD7+VCSGD8)
        vclsp = vclspgnd + (vcldspi + vcldspo)
C
        VCLICE = (VTAIL + VTAIR) * VFCLICE
C
C        SUMMATION OF TERMS
C
        VCLS= VCLBAS + VCLCT + VCLDE + VCLBETA + VCLQHAT +
     &       VCLNZ + VCLSP + VCLGE + VCLICE
C
C        CALCULATION OF CM
C
C
        k1= 0.1 * vflapd / 5.
        if(vflapd.ge. 5.0) k1 = .1 + .14 * (vflapd-5.)/10.
        if(k1.gt.0.3)k1=0.3
        if (vflapd.le.15)then
          lvctsum2 = lvctsum
        else
          lvctsum2 = amax1(0.,amin1(((VALPHA3-valpha)*.1),lvctsum))
        endif
        VCM0 = VFCM0 - k1 * ( lVCTSUM2 )
C
        VCMBAS = VFCMBAS - VSTALLQ1
        if (f12.gt.0.)then
          lg1 = amax1(0.,amin1(1.,(1.- (vh-8.)/8.)))
          lg2 = amin1(5.,amax1(0.,(0.25-valpha)))
          lg3 = amin1(1.,amax1(0.,(lvctsum*4.)))
          vcmge = f12 * lg1 * lg2 * lg3
          IF (VCMGE.GT. LG4)VCMGE=LG4
        endif
c
c       Stall moment
c       bypassed during vtrim=0
c
        if(vtrim.ne.0)then
         if((valpha.gt.astall).and.(valpha.gt.lalphmax))then
          lalphmax=valpha
          istall=1
         else
          istall=0
          lalphmax=0.
         endif
        endif
C
C        ELEVATOR MOMENT CONTRIBUTION
C
c
c       reduce clde by ground effect term
c
        VCMDE =(VFCLDEGE+VCLDE)*(VXCG-847.56)/87. * VSTALLQ2
C
C        PITCH RATE INDUCED MOMENT
C
        LCMQ= .59 * (VXCG -400.)
C
        VCMQHAT = (VFDCMQH + LCMQ) * VQHAT
C
C       GEAR TRANSITION TERMS
C
c        as per fax.dat 28 nov 91
c
        IF (VDUC .EQ.0.)THEN
           VCMGER = 0.0
        ELSEIF (VDUC.EQ. 1.)THEN
           VCMGER = 0.0
        ELSE
           IF (VDUC.GT.LDUC)THEN
             LEXTEND = -0.015
           ELSEIF(VDUC .LT. LDUC)THEN
             LEXTEND = 0.015
           ENDIF
           VCMGER = LEXTEND*VDUC*(1.-VDUC)*4.
        ENDIF
        LDUC = VDUC
C
C  Gear extension term
C
        vcmger = vcmger - .008 * vduc
C
        VCMICE = VTAI * VFCMICE
C
C
C        SUMMATION OF TERMS
C
        VCM = VCM0 + VFCMB2 + VCMBAS + VCMDE + VCMQHAT +
     &        VCMGER + VCMICE + vcmge
C
C   CALCULATION OF cd *** Revised into function form 15 JAN 92 sb
C
        VCDBAS = VFCDBAS
C
C        INCREMENT DUE TO GEAR EXTENSION
C         + drag due to gear doors during gear in transit
C
        lsp0 =  .005 * AMIN1(1.,(VDUC*(1.-VDUC)*10.))
        VCDGER =VDUC*VFDCDG + lsp0
C
C        THRUST EFFECTS
C
        VCDCT =+.035*VCTSUM
        if (vctsum .lt. -0.1) then
           lsp0   = AMAX1(vqprs,1.)
           Rctsum = vefns / lsp0
           vcdct  = lthrust * rctsum + vcdart * ((-.1-vctsum)/.4)
        endif
C
C        REDUCTION IN DRAG DUE TO BETA
C
        VCDBETA =-.17*SIN(LBETA/57.3)**2.
C
C        REDUCTION IN cdBASIC WITH GROUND EFFECT
C
C        GROUND EFFECT REDUCTION IN EFFECTIVE CDBAS
C
        IF (VH.GE.15)THEN
          x1=(16.0*vh/84)**2.
        ELSE
          x1=(16.0*15./84)**2.
        ENDIF
        if(x1.le.0.)x1=0.
        VCDBAS =VCDBAS*X1/(1.+X1)
        if (labeta.gt.90.)then
          vcdbas = vcdbas * (90.-labeta)*(1./90.)
          vcdbeta = 0.0
        elseif (labeta.gt.45.)then
          vcdbas = vcdbas * (90.-labeta)*(1./45.)
          vcdbeta = vcdbeta * (90.-labeta)*(1./45.)
        endif
C
C         DRAG DUE TO SPOILERS
C
C
        VCDSPI = (VCSPLI+VCSPRI) * VFCDSPI
        VCDSPO = (VCSPLO+VCSPRO) * VFCDSPO
        VCDSPGND = (VCSGD1+VCSGD2+VCSGD7+VCSGD8) * VFCDSPGD
        VCDSP = VCDSPI + VCDSPO + VCDSPGND
C
        VCDICE = VICEF1 * VFCDICE
C
C
C        SUMMATION OF TERMS
C
        VCDS = VCDBAS + VCDGER + VCDCT + VCDBETA + VCDSP + VCDICE
C
C        CALCULATION OF cy
C
C        BETA TERMS RELY ON BOTH mmle ANALYSIS AND
C        WIND TUNNEL DATA
C
        VCYADD = 0.0
        IF(VBETA.LT.-10.)THEN
          VCYADD = -(.2332+.9568*LVCTSUM)*(VBETA+10.)/57.3
          if (vcyadd.gt.lcylim)vcyadd = lcylim
          if (vbeta.lt.-90.)vcyadd = -vcyadd*lfbeta
        ELSE IF(VBETA.GT.10.)THEN
          VCYADD =-(.2564+.3156*LVCTSUM)*(VBETA-10.)/57.3
          if (vcyadd.lt.-lcylim)vcyadd = -lcylim
          if (vbeta.gt.90.)vcyadd = vcyadd*lfbeta
        ENDIF
C
C	CY Dr
C
        vfcyrud = .0099
C
C        power induced Cy
c
        Lcyct= -.089*lvctsum+(.019-.0006*vflapd)*(1.-f9)
        IF(VFLAPD.GT.0.AND.VFLAPD.LT.5.0) THEN
          VFCYRUD = .0095 - 0.0004 * (VFLAPD-5)/5.
        ELSEIF(VFLAPD.GE.5.0.AND.VFLAPD.LT.15.0) THEN
          VFCYRUD = .0090 - 0.0005 * (VFLAPD-15)/10.
        ELSEIF(VFLAPD.GE.15.0) THEN
          VFCYRUD = .0085 - 0.0005 * (VFLAPD-35)/20.
        ENDIF
C
        VCYBAS  = VFCYB * LBETA *lfbeta + VCYADD
        VCYPHAT = VFDCYP  * VPHAT
        VCYRHAT = VFDCYR  * VRHAT
C
C        ADDITIONAL CY DUE TO LANDING GEAR AND BETA
C
        VCYGER =-.0037*LBETA*VDUC*lfbeta
C
C
C        CONTROL INDUCED CY TERMS
C
        VCYDA   = (-0.003386*LVCTSUM - 0.00003)*VVDA
        VCYRUD  = VFCYRUD * LRUD   ! 18 nov 91
        VCYDSPI = (-0.000024*VALPHA - 0.00006)*VCSPI
        VCYDSPO = (-0.000024*VALPHA - 0.00006)*VCSPO
C
C        SUMMATION OF TERMS
C
        VCY =VCYBAS+VCYPHAT+VCYRHAT+VCYDA+VCYRUD+VCYDSPI+VCYDSPO
     &            +VCYGER+LCYCT*f8
C
C        CALCULATION OF croll
C        BETA TERM (DIHEDRAL EFFECT)
C        ROLL DAMPING
C        YAW RATE INDUCED ROLLING MOMENT (SECONDARY DIHEDRAL EFFECT)
c
c        Power induced rolling moment (slipstream + torque effects)
c
c       - assymetric power term included
c
        if(LVCTSUM.gt.0.0001)then
        VDCRCT = .0384 * VCLCT * (LVCT1 - LVCT2)/LVCTSUM
        else
        VDCRCT=0.0
        endif
        vdcrct = vdcrct + amax1(0.,(Lvctsum-lctr))*cr_ct
c
c       rolling moment at stall
c
        lrlstall=0.0
        if(istall.ne.1)then
         i2=0.0
         istallb=0.0
         if(vbeta.gt.1.0)istallb=1.0
         if(vbeta.lt.-1.0)istallb=-1.0
        else
         i2=i2+1./30.
         if(i2.gt.1.0)i2=1.0
         lrlstall = istallb * i2 * 0.06
        endif
C
       VCRWB   = VFDCRB*LBETA2*lfbeta
       VCRPHAT = VFDCRP*VPHAT
       VCRRHAT = VFDCRR*VRHAT
C
C      INCREASE OF ROLL POWER IN GROUND EFFECT
C
       IF (VFCLBAS .NE. 0.)THEN
         LGE = 1. + ABS(VFCLGE/VFCLBAS)*f14
       ELSE
         LGE = 1.
       ENDIF
C
       VCRRUD  = VFCRRUD * LRUD * 1.4
       VCRDA   = VFCRDA * (VVDA  + lgt * CATRIM) * lge
       VCRDSPI = VFCRDSPI * VCSPI *lge
       VCRDSPO = VFCRDSPO * VCSPO *lge
c
c        assymetric elevator rolling moment
c
        VCRDE = -.00019 * (VELVR - VELVL)
c
        VCRICE = (VTAIL-VTAIR) * VFCRICE
C
C
C        SUMMATION OF TERMS
C
       VCLLS =VCRWB+VCRPHAT+VCRRHAT+VCRDA+VCRRUD+VCRDSPI+VCRDSPO
     &        + VDCRCT + LRLSTALL + VCRDE + VCRICE + VCRADD*f13
C
C        CALCULATION OF CN
C
C        DIRECTIONAL STABILITY (cN BETA)
C
C        ROLL RATE INDUCED cN
C        YAW RATE DAMPING
C
c       Power induced yawing moment (slipstream and torque effects)
c
c
c	symmetrical part
c
        LSP0 = LVCTSUM
        if (lsp0 .lt. 0.)LSP0 = 0.
        IF (LSP0.GT. .3) LSP0 = .3
        if (vflapd.le.5.)then
          lt1 = lt15
          lc1 = lc15
          lct = lct5
          llt = llt5
        elseif (vflapd.lt.15)then
          lt1 = lt15 + (lt115 - lt15)*(vflapd-5.)*.1
          lc1 = lc15 + (lc115 - lc15)*(vflapd-5.)*.1
          llt = llt5 + (llt15 - llt5)*(vflapd-5.)*.1
          lct = lct15
        else
          lt1 = lt115 + (lt135 - lt115)*(vflapd-15)/20.
          lc1 = lc115 + (lc135 - lc115)*(vflapd-15)/20.
          llt = llt15 + (llt35 - llt15)*(vflapd-15)/20.
          lct = lct35
        endif
C !FM+
C !FM  24-Aug-93 23:41:34 paul van esbroeck
C !FM    < vmca tuning >
C !FM
        VDCNCT = lct * (LSP0  + LT1 * AMAX1(0.,(LVCTDIF-LT1A))
     &              +  LLT * (AMAX1(0.,(LT2A - LVCTDIF)))**2)
C !FM-
C
        IF (VFLAPD.GT.5.AND.VFLAPD.LT.15) THEN
          VDCNCT = vdcnct - (.002 * (vflapd -5.)/10.)*(1-f7)
        ELSEIF (VFLAPD.GE.15) THEN
          VDCNCT = vdcnct - (.002 +.001*(vflapd-15.)/20.)*(1-f7)
        ENDIF
C
        valpha1 = amax1(0.,amin1(valpha2,valpha))
        vdcnct = vdcnct + lc1 * vctsum * Valpha1
C
        VDCNCT = VDCNCT * (1. + SM3*SF3)
c
       IF (LP1)THEN
         IF (VBETA .GT. LHBETA) THEN
            VCNWB   = VFDCNB * LHBETA*lfbeta
         ELSEIF (VBETA .LT. -LHBETA) THEN
            VCNWB   = -VFDCNB * LHBETA*lfbeta
         ELSE
            VCNWB   = VFDCNB * VBETA*lfbeta
         ENDIF
       ELSE
         VCNWB   = VFDCNB * LBETA*lfbeta
       ENDIF
C
C        yaw due to beta for large beta angles to aid in tuning crosswind
C          takeoff
c
      IF ((VBETA .GE. LHBETA).and.(vbeta.ne.0))THEN
         LSP0 = (VBETA - LHBETA)/VBETA * SIN (VBETA/57.3)
       ELSEIF ((VBETA .LE. -LHBETA).and.(vbeta.ne.0))THEN
         LSP0 = (VBETA + LHBETA)/VBETA * SIN (VBETA/57.3)
       ELSE
         LSP0 = 0.
       ENDIF
       VCNWB = VCNWB + LSP0 * CN_LBETA
       VCNPHAT = VFDCNP * VPHAT
       if (abs(vr) .gt. .2) then
         lrg = 1. + (abs(vr)-.2)*f10
       else
         lrg = 1.
       endif
       VCNRHAT = VFDCNR * VRHAT * lrg
C
C        CONTROL POWER TERMS
C
       VCNDA  = VFCNDA * VVDA
       VCNRUD = VFCNRUD * LRUD * (1. + sm1*sf1)
       VCNDSPI = VFCNDSPI * VCSPI
       VCNDSPO = VFCNDSPO * VCSPO
c
c        code for single elevator induced lateral moments
c
c
c        Yawing moment
c
          VCNDE = (.000256-.00001*valpha) * (VELVR-VELVL)
C
C
C        SUMMATION OF TERMS
C
       VCNS =VCNWB+VCNPHAT+VCNRHAT+VCNDA+VCNRUD+VCNDSPI+VCNDSPO
     &       + VDCNCT + VCNDE
C
       RETURN
       END
C
