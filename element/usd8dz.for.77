C'Module_ID       USD8DZ
C'Entry_point     DMISC
C'Documentation
C'Customer        USAIR
C'Application     ECS miscellaneous resets for the DASH8-100/100A/300A
C'Author(s)       J. Bilodeau
C'Date            September, 1991
C
C'System          ECS (Environmental Control Systems)
C'Iteration_rate  266 msec
C'Process         Synchronous
C ----------------------
C'Revision_History
C
C  usd8dz.for.17 18Dec1992 02:31 usd8 M.WARD
C       < ADDED TCM0SRVD FOR SERVICE DOOR BUTTON >
C
C  usd8dz.for.16 16Dec1992 16:14 usd8 m.ward
C       < added dark concept for -300a malfunctions t052051,2 >
C
C  usd8dz.for.15  5Nov1992 01:00 usd8 mm
C       < removed dzsbi from cnia flag since dzsbi only used for hbov
C         position >
C
C  usd8dz.for.14 29Jun1992 10:36 usd8 m.ward
C       < set all doors closed when sim is first loaded >
C
C  usd8dz.for.13 27Mar1992 15:48 usd8 j.bilod
C       < remove i/f dark concecp logic to put it in dv >
C
C  usd8dz.for.12 26Mar1992 17:38 usd8 JGB
C       < MODIFY MAINTENANCE RESET LOGIC  >
C
C  usd8dz.for.11 24Mar1992 16:38 usd8 JGB
C       < INTRODUCE MAINTENANCE RESET SELECTION AVAILABLE >
C
C  usd8dz.for.10 19Mar1992 09:34 usd8 JGB
C       < GET RID OF DZNP >
C
C  usd8dz.for.9 19Mar1992 09:32 usd8 JGB
C       <get rid of dzne
C
C  usd8dz.for.8 19Mar1992 09:02 usd8 jgb
C       < change dzne from integer*2 to integer*4 as hrecs >
C
C  usd8dz.for.7 18Mar1992 13:39 usd8 jgb
C       < added spare label for debug >
C
C  usd8dz.for.6 18Mar1992 12:41 usd8 JGB
C       < ADDED SPARE LOGICAL LABEL TO BY PASS TCMDORCL I/F LABEL >
C
C  usd8dz.for.5 18Mar1992 09:42 usd8 JGB
C       < CORRECTED COMPILATION ERROR >
C
C  usd8dz.for.4 18Mar1992 09:36 usd8 JGB
C       < ADDED EQ. 1040 AND 1060 FOR REPOS LOGIC  >
C
C  usd8dz.for.3  2Mar1992 16:01 usd8 jgb
C       < change text in module >
C
C  usd8dz.for.2 22Feb1992 11:45 usd8 JGB
C       < INTRODUCED APU AVAIL. FLAG DZFAPU >
C
C  usd8dz.for.1 30Jan1992 09:22 usd8 J.BILOD
C       < CORRECTED RESET TO SELECTION LOGIC >
C
C'
C'Compilation_directives
C ----------------------
C
C
C
C ------------------------
C'Include_files_directives
C ------------------------
C
C
C
C -----------
C'Description
C -----------
C
C
C
C
C
C
C ----------
C'References
C ----------
C
C
      SUBROUTINE USD8DZ
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C
C
C
C
C
C -------------
C'Include_files
C -------------
C
      INCLUDE 'disp.com'  !NOFPC
C
C
C
C -----------------
C'Subroutine_called
C -----------------
C
C
C
C
C --------------------------
C'Common_data_base_variables
C --------------------------
C
C
C
C
C     ---------------------------------------------------------
C
C                 C O M M O N    D A T A    B A S E
C
C     ---------------------------------------------------------
C
C     --------
C      Inputs
C     --------
C
C
C     --------
C      QMR bus
C     --------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,XRFTEST5,XRFTEST6
C
CE    INTEGER*4 DZNE         , !E- REPOS. FLAG                HRECS
CE    INTEGER*4 DZNP         , !E- REPOS. FLAG                HRPNEU
CE    INTEGER*4 DZNT         , !E- SHIP NUMBER                YITAIL
C
CE    REAL*4    DZTR         , !E- AMBIENT TEMPERATURE        ETT1
CE    REAL*4    DZPLLI(2)    , !E- ENG LOW BLEED PRESSURE     EPBL
CE    REAL*4    DZPHHI(2)    , !E- ENG HIGH BLEED PRESSURE    EPBH
C
CE    LOGICAL*1 DZSDP        , !E- PASS DOOR I/F              TCMDOR01
CE    LOGICAL*1 DZSDB        , !E- CARGO DOOR I/F             TCMDOR02
CE    LOGICAL*1 DZSDF        , !E- FRONT EMER EXIT            TCMDOR04
CE    LOGICAL*1 DZSDG        , !E- GALLEY SERVICE DOOR        TCMDOR03
CE    LOGICAL*1 DZFB         , !E- BLEED CNIA FLAG            TCATMBLS
CE    LOGICAL*1 DZFP         , !E- PACK CNIA FLAG             TCATMPS
CE    LOGICAL*1 DZFD         , !E- I/F TEMP RESET DARK CONC.  TCR0MAIN
CE    LOGICAL*1 DZFDN        , !E- ALL DOOR CLOSED I/F        DZSPL
CE    LOGICAL*1 DZFG         , !E- AIRCRAFT ON GROUND         VBOG
CE    LOGICAL*1 DZFPK        , !E- I/F PACK TEMP RST DARK C.  TCR0PKTM
CE    LOGICAL*1 DZSA         , !E- CABIN TEMP RESET TO AMB    TCRACTAT
CE    LOGICAL*1 DZSBI(2)     , !E-                            IDDBSBI
CE    LOGICAL*1 DZSFI(2)     , !E-                            IDDBSFI
CE    LOGICAL*1 DZSGI        , !E- GROUND CART SUPPLY FLAG
CE    LOGICAL*1 DZSHI(2)     , !E-                            IDDBSHI
CE    LOGICAL*1 DZSM         , !E-                            TCRMAINT
CE    LOGICAL*1 DZSP         , !E- PACK RESET TEMPERATURES    TCRPKTMP
CE    LOGICAL*1 DZSS         , !E- CABIN TEMP RESET TO SELECT TCRACONS
CE    LOGICAL*1 DZST         , !E- AIRCOND TEMP RESET         TCRALLT
C
CE    EQUIVALENCE   ( DZNT      ,  YITAIL   ) ,
CE    EQUIVALENCE   ( DZFB      ,  TCATMBLS ) ,
CE    EQUIVALENCE   ( DZFD      ,  TCR0MAIN ) ,
CE    EQUIVALENCE   ( DZFDN     ,  DZSPL    ) ,
CE    EQUIVALENCE   ( DZFG      ,  VBOG     ) ,
CE    EQUIVALENCE   ( DZFP      ,  TCATMPS  ) ,
CE    EQUIVALENCE   ( DZFPK     ,  TCR0PKTM ) ,
CE    EQUIVALENCE   ( DZNE      ,  HRECS    ) ,
CE    EQUIVALENCE   ( DZNP      ,  HRPNEU   ) ,
CE    EQUIVALENCE   ( DZPLLI    ,  EPBL     ) ,
CE    EQUIVALENCE   ( DZPHHI    ,  EPBH     ) ,
CE    EQUIVALENCE   ( DZSA      ,  TCRACTAT ) ,
CE    EQUIVALENCE   ( DZSBI(1)  ,  IDDBSBI  ) ,
CE    EQUIVALENCE   ( DZSFI(1)  ,  IDDBSFI  ) ,
CE    EQUIVALENCE   ( DZSDP     ,  TCMDOR01 ) ,
CE    EQUIVALENCE   ( DZSDB     ,  TCMDOR02 ) ,
CE    EQUIVALENCE   ( DZSDF     ,  TCMDOR04 ) ,
CE    EQUIVALENCE   ( DZSDG     ,  TCMDOR03 ) ,
CE    EQUIVALENCE   ( DZSHI(1)  ,  IDDBSHI  ) ,
CE    EQUIVALENCE   ( DZSM      ,  TCRMAINT ) ,
CE    EQUIVALENCE   ( DZSP      ,  TCRPKTMP ) ,
CE    EQUIVALENCE   ( DZSS      ,  TCRACONS ) ,
CE    EQUIVALENCE   ( DZST      ,  TCRALLT  ) ,
CE    EQUIVALENCE   ( DZTR      ,  ETT1     )
C
CP    USD8
C
C
CP    DAPAI     ,!E-
CP    DAPBI     ,!E-
CP    DAPD      ,!E-
CP    DAWA      ,!E-
CP    DAWAI     ,!E-
CP    DAWB      ,!E-
CP    DAWBI     ,!E-
CP    DAWBKI    ,!E-
CP    DAWHI     ,!E-
CP    DBFBI     ,!E-
CP    DCTAI     ,!E-
CP    DCTBI     ,!E-
CP    DCTD      ,!E-
CP    DCTXHI    ,!E-
CP    DKPFI     ,!E-
CP    DKTCI     ,!E- COMP DISCH TEMP                  deg C
CP    DKTFI     ,!E- PACK INLET TEMP                  deg C
CP    DKTKAI    ,!E- PACK DISCH ADV TEMP              deg C
CP    DKTKI     ,!E- PACK DISCH TEMP                  deg C
CP    DKTPI     ,!E- COMPRESS INLET TEMP              deg C
CP    DKTSI     ,!E- TURBINE INLET TEMP               deg C
CP    DKTTI     ,!E- TURBINE DISCH TEMP               deg C
CP    DKWFI     ,!E- PACK INLET FLOW                  lb/min
CP    DKWKI     ,!E- PACK DISCHARGE AIRFLOW           lb/min
CP    DNTAI     ,!E- AIR SUPPLY DUCT TEMP             deg C
CP    DNTAAI    ,!E- AIR SUPPLY DUCT ADV TEMP         deg C
CP    DNTCI     ,!E- ZONE TEMPERATURE                 deg C
CP    DNTCAI    ,!E- ZONE ADVANCE TEMP                deg C
CP    DNTDI     ,!E-
CP    DNTDAI    ,!E-
CP    DNTFI     ,!E- MANIFOLD DUCT TEMPERATURE        deg C
CP    DNTRI     ,!E-
CP    DNTRAI    ,!E-
CP    DNHS      ,!E-
CP    DNWDI     ,!E-
CP    DNTS      ,!E- A/C SKIN TEMP                    deg C
CP    DOFAI     ,!E-
CP    DOFCI     ,!E-
CP    DOTSI     ,!E- ZONE SELECTED TEMP               deg C
CP    DTFD      ,!E-
CP    DTPA      ,!E-
CP    DVFN      ,!E-
CP    DVFR      ,!E-
CP    DVFD      ,!E-
CP    DVFC      ,!E-
CP    DVNM      ,!E-
CP    ETT1      ,!E- RAM AIR TEMPERATURE              deg C
CP    EPBH      ,!E- ENG HIGH BLEED PRESSURE          psi
CP    EPBL      ,!E- ENG LOW BLEED PRESSURE           psi
CP    HRECS     ,!E- ECS REPOSITION FLAG              -
CP    HRPNEU    ,!E- PNEU REPOSITION FLAG             -
CP    IADOHSI   ,!E-
CP    IADOHS2   ,!E-
CP    IDDBSBI   ,!E-
CP    IDDBSFI   ,!E-
CP    IDDBSHI   ,!E-
CP    TCRACONS  ,!E- TEMP RESET TO SELECT             -
CP    TCRACTAT  ,!E- TEMP RESET TO AMBIENT            -
CP    TCRALLT   ,!E-
CP    TCR0ALLT  ,!E-
CP    TCR0MAIN  ,!E-
CP    TCRMAINT  ,!E-
CP    TCRPKTMP  ,!E-
CP    TCR0PKTM  ,!E-
CP    VBOG      ,!E-
CP    YITAIL    ,!E-
C
C
C     ---------
C      outputs
C     ---------
C
CP    DZF       ,!E- DMISC FREEZE FLAG                -
CP    DZF300    ,!X-
CP    DZFA      ,!X-
Cp    DZFAPU    ,!X- APU AVAIL. FLAG
CP    DZPAI     ,!X- ANTI-ICE DUCT PRESSURE           psi
CP    DZPBI     ,!X- ENG BLEED DUCT PRESS             psi
CP    DZPD      ,!X-
CP    DZPFI     ,!X-
CP    DZPHI     ,!X- ENG HIGH BLEED PRESS             psi
CP    DZPLI     ,!X- ENG LOW BLEED PRESS              psi
CP    T021021   ,!X-
CP    T021022   ,!X-
CP    T021061   ,!X-
CP    T021062   ,!X-
CP    T021071   ,!X-
CP    T021111   ,!X-
CP    T021121   ,!X-
CP    T021122   ,!X-
CP    T021131   ,!X-
CP    T021141   ,!X-
CP    T021201   ,!X-
CP    T021202   ,!X-
CP    T021211   ,!X-
CP    T021212   ,!X-
CP    T030021   ,!X-
CP    T030022   ,!X-
CP    T030031   ,!X-
CP    T030141   ,!X-
CP    T030371   ,!X-
CP    T030381   ,!X-
CP    T030382   ,!X-
CP    T036011   ,!X-
CP    T036012   ,!X-
CP    T036021   ,!X-
CP    T036022   ,!X-
CP    T036051   ,!X-
CP    T036052   ,!X-
CP    T049051   ,!X-
CP    T052011   ,!X-
CP    T052041   ,!X-
CP    T052051   ,!X-
CP    T052052   ,!X-
CP    DZSPL     ,!X-
CP    TCMDOR01  ,!X-
CP    TCMDOR02  ,!X-
CP    TCMDOR03  ,!X-
CP    TCMDOR04  ,!X-
CP    TCMDORCL  ,!X-
CP    TCM0SRVD  ,!X- SERVICE DOOR BUTTON AVAILABLE -300A ONLY
CP    TCATMBLS  ,!X- BLEED CNIA FLAG
CP    TCATMPS    !X- PACK CNIA FLAG
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:41:26 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DAPAI(2)       ! ANTI-ICE DUCT PRESS                   [psia]
     &, DAPBI(2)       ! ENG BLD DUCT PRESS                    [psia]
     &, DAPD           ! PNEU DUCT PRESS                       [psia]
     &, DAWA           ! BOOT AIR ISOLATION VLV FLOW         [lb/min]
     &, DAWAI(2)       ! ANTI-ICE PRV DUCT FLOW              [lb/min]
     &, DAWB           ! APU BLEED DUCT AIRFLOW              [lb/min]
     &, DAWBI(2)       ! ENG BLEED DUCT FLOW                 [lb/min]
     &, DAWBKI(2)      ! ENG BLEED DUCT LEAK FLOW            [lb/min]
     &, DAWHI(2)       ! HI BLEED VALVE DUCT FLOW            [lb/min]
     &, DCTAI(2)       ! ANTI-ICE DUCT TEMP                   [deg C]
     &, DCTBI(2)       ! ENG BLD DUCT TEMP                    [deg C]
     &, DCTD           ! PNEU DUCT TEMP                       [deg C]
     &, DCTXHI(2)      ! HBOV HX OUTLET TEMP                  [deg C]
     &, DKPFI(2)       ! PACK INLET DUCT PRESS                 [psia]
     &, DKTCI(2)       ! COMPR DISCH TEMP                     [deg C]
     &, DKTFI(2)       ! PACK INLET TEMP                      [deg C]
     &, DKTKAI(2)      ! PACK DISCH ADV TEMP                  [deg C]
     &, DKTKI(2)       ! PACK DISCH TEMP                      [deg C]
     &, DKTPI(2)       ! COMPR INLET TEMP                     [deg C]
     &, DKTSI(2)       ! TURB INLET TEMP                      [deg C]
     &, DKTTI(2)       ! TURBINE DISCH TEMP                   [deg C]
     &, DKWFI(2)       ! PACK INLET AIRFLOW                  [lb/min]
     &, DKWKI(2)       ! PACK DISCHARGE FLOW                 [lb/min]
     &, DNHS           ! SKIN TEMP FF                         [deg_C]
     &, DNTAAI(2)      ! PACK DUCT ADV TEMP                   [deg_C]
     &, DNTAI(2)       ! PACK DUCT TEMP                       [deg_C]
     &, DNTCAI(2)      ! ZONE ADV TEMP                        [deg_C]
     &, DNTCI(2)       ! ZONE TEMP                            [deg_C]
     &, DNTDAI(2)      ! ZONE DUCT ADV TEMP                   [deg_C]
     &, DNTDI(2)       ! ZONE DUCT TEMP                       [deg_C]
     &, DNTFI(2)       ! EQ COOLING TEMP                      [deg_C]
      REAL*4   
     &  DNTRAI(2)      ! DUCT RISER ADV TEMP                  [deg_C]
     &, DNTRI(2)       ! L/R DUCT RISER TEMP                  [deg_C]
     &, DNTS           ! SKIN TEMPERATURE                     [deg_C]
     &, DNWDI(2)       ! F DECK / CABIN DUCT AIRFLOW         [lb/min]
     &, DOTSI(2)       ! COMPT TEMPERATURE SELECTED           [deg_C]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, DZPAI(2)       ! A/I BLEED PRESSURE
     &, DZPBI(2)       ! ENG BLEED DUCT PRESSURE
     &, DZPD           ! CENTER DUCT PRESSURE
     &, DZPFI(2)       ! PACK INLET PRESSURE
     &, DZPHI(2)       ! HIGH BLEED PRESSURE
     &, DZPLI(2)       ! LOW BLEED PRESSURE
     &, EPBH(2)        ! HIGH BLEED PRESSURE                    [PSI]
     &, EPBL(2)        ! LOW BLEED PRESSURE                     [PSI]
     &, ETT1           ! FREE STREAM TOTAL TEMP.                  [C]
     &, IADOHS2        ! CABIN TEMP SELECTOR            [degC] AI053
     &, IADOHSI        ! F COMP TEMP SELECTOR           [degC] AI052
C$
      INTEGER*4
     &  DVNM           ! MODE NUMBER                              [-]
     &, HRECS          ! ECS BACKDRIVE INDEX
     &, HRPNEU         ! PNEUMATICS BACKDRIVE INDEX
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  DBFBI(2)       ! HP BLEED CONT TRIP FLAG
     &, DOFAI(2)       ! FLT COMP/CAB DUCT OVHT
     &, DOFCI(2)       ! PACK HOT FLAG
     &, DTFD           ! CABIN PRESS RESET FLAG
     &, DVFC           ! CAB EVER CLAMP FLAG              -
     &, DVFD           ! CAB PRES CONT DESCENT FLAG       -
     &, DVFN           ! NUMODE FLAG                      -
     &, DVFR           ! RESET FLAG                       -
     &, DZF            ! DMISC FREEZE FLAG
     &, DZF300         ! DASH8 100/300 OPTION  (.T. => 300)
     &, DZFA           ! DASH8 100-300 / 100A-300A OPTION  (.T. => A)
     &, DZFAPU         ! DASH8 WITH APU OPTION ( .T. = APU )
     &, DZSPL(10)      ! ECS LOGICAL SPARES
     &, IDDBSBI        ! L BLEED SWITCH @BLEED (-300/300A)     DI0568
     &, IDDBSFI        ! L BLEED SWITCH @OFF (-300/300A)       DI0569
     &, IDDBSHI        ! L BLEED SWITCH @BLEED (-100)          DI0223
     &, T021021        ! TEMP CONTROLLER FAIL CABIN
     &, T021022        ! TEMP CONTROLLER FAIL FLT COMPT
     &, T021061        ! PACK FAULT
     &, T021062        ! PACK FAULT #2 (300A)
     &, T021071        ! AUTO CABIN PRESSURE CONTROLLER FAI
     &, T021111        ! LOSS OF CABIN PRESSURE
     &, T021121        ! DISTRIBUTION DUCT OVERHEAT CABIN
     &, T021122        ! DISTRIBUTION DUCT OVERHEAT FLT COMPT
     &, T021131        ! RAPID DEPRESSURIZATION
     &, T021141        ! SLOW DEPRESSURIZATION
     &, T021201        ! DISTRIBUTION DUCT OVERHEAT - CABIN
     &, T021202        ! DISTRIBUTION DUCT OVERHEAT - FLT
     &, T021211        ! PACK FAULT (300A) - CAB
     &, T021212        ! PACK FAULT (300A) - FLT CMP
     &, T030021        ! TAIL DE-ICE FAILURE LEFT
      LOGICAL*1
     &  T030022        ! TAIL DE-ICE FAILURE RIGHT
     &, T030031        ! PNEUMATIC DE-ICING TIMER FAIL
     &, T030141        ! AIRFRAME DE-ICE FAIL
     &, T030371        ! MANUAL DE-ICING FAILS
     &, T030381        ! INFLATOR #2 LEAKS LEFT
     &, T030382        ! INFLATOR #2 LEAKS RIGHT
     &, T036011        ! HP BLEED VLV STUCK 1
     &, T036012        ! HP BLEED VLV STUCK 2
     &, T036021        ! HP VLV FAILS OPEN 1
     &, T036022        ! HP VLV FAILS OPEN 2
     &, T036051        ! BLEED DUCT OVERHEAT 1
     &, T036052        ! BLEED DUCT OVERHEAT 2
     &, T049051        ! BLEED AIR OVERHEAT
     &, T052011        ! PASSENGER DOOR OPEN
     &, T052041        ! BAGGAGE DOOR OPEN
     &, T052051        ! SERVICE DOOR OPEN (GND-300A)
     &, T052052        ! SERVICE DOOR OPEN (AIR-300A)
     &, TCATMBLS       ! CNIA ATM BLEED SWITCH
     &, TCATMPS        ! CNIA ATM PACK SWITCH
     &, TCM0SRVD       ! SERVICE DOOR AVAILABLE
     &, TCMDOR01       ! DOOR # 01 OPEN
     &, TCMDOR02       ! DOOR # 02 OPEN
     &, TCMDOR03       ! DOOR # 03 OPEN
     &, TCMDOR04       ! DOOR # 04 OPEN
     &, TCMDORCL       ! CLOSE ALL DOORS
     &, TCR0ALLT       ! ALL TEMPERATURES
     &, TCR0MAIN       ! ALL MAINTENANCE
     &, TCR0PKTM       ! PACK TEMPERATURE RESET - AVAIL FLAG
     &, TCRACONS       ! AIRCONDITIONING TEMP SELECTION
     &, TCRACTAT       ! AIRCONDITIONING TEMP TO AMBIENT
     &, TCRALLT        ! ALL TEMPERATURES
      LOGICAL*1
     &  TCRMAINT       ! MAINTENANCE
     &, TCRPKTMP       ! PACK TEMPERATURE RESET
     &, VBOG           ! ON GROUND FLAG
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(11696),DUM0000003(996)
     &, DUM0000004(1),DUM0000005(1),DUM0000006(3651)
     &, DUM0000007(5731),DUM0000008(74308),DUM0000009(4)
     &, DUM0000010(28),DUM0000011(8),DUM0000012(12)
     &, DUM0000013(8),DUM0000014(81),DUM0000015(57)
     &, DUM0000016(4),DUM0000017(8),DUM0000018(356)
     &, DUM0000019(44),DUM0000020(16),DUM0000021(8)
     &, DUM0000022(72),DUM0000023(4),DUM0000024(4)
     &, DUM0000025(4),DUM0000026(80),DUM0000027(9)
     &, DUM0000028(19),DUM0000029(81),DUM0000030(82)
     &, DUM0000031(25),DUM0000032(2),DUM0000033(57)
     &, DUM0000034(12),DUM0000035(1590),DUM0000036(212)
     &, DUM0000037(206444),DUM0000038(28),DUM0000039(56)
     &, DUM0000040(6756),DUM0000041(22),DUM0000042(24)
     &, DUM0000043(3779),DUM0000044(71),DUM0000045(3214)
     &, DUM0000046(184),DUM0000047(19),DUM0000048(120)
     &, DUM0000049(8),DUM0000050(1)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,IADOHSI,IADOHS2,DUM0000003
     &, IDDBSHI,DUM0000004,IDDBSFI,DUM0000005,IDDBSBI,DUM0000006
     &, VBOG,DUM0000007,HRPNEU,HRECS,DUM0000008,DAPAI,DAPBI,DUM0000009
     &, DAPD,DUM0000010,DAWA,DAWAI,DUM0000011,DAWBI,DAWBKI,DUM0000012
     &, DAWHI,DUM0000013,DAWB,DUM0000014,DBFBI,DUM0000015,DCTAI
     &, DUM0000016,DCTBI,DCTD,DUM0000017,DCTXHI,DUM0000018,DKPFI
     &, DUM0000019,DKTCI,DKTFI,DKTKAI,DKTKI,DKTPI,DKTSI,DKTTI
     &, DUM0000020,DKWFI,DUM0000021,DKWKI,DUM0000022,DNHS,DUM0000023
     &, DNTAAI,DNTAI,DNTDAI,DNTCAI,DNTCI,DNTDI,DNTFI,DUM0000024
     &, DNTRAI,DNTRI,DNTS,DUM0000025,DNWDI,DUM0000026,DOTSI,DUM0000027
     &, DOFCI,DOFAI,DUM0000028,DTPA,DUM0000029,DTFD,DUM0000030
     &, DVNM,DUM0000031,DVFC,DVFD,DUM0000032,DVFN,DVFR,DUM0000033
     &, DZPLI,DZPHI,DZPBI,DZPAI,DZPFI,DZPD,DZFAPU,DZFA,DZF300
     &, DZSPL,DUM0000034,DZF,DUM0000035,ETT1,DUM0000036,EPBH
     &, EPBL,DUM0000037,TCRALLT,TCRMAINT,DUM0000038,TCRACONS
     &, TCRACTAT,DUM0000039,TCR0ALLT,TCR0MAIN,DUM0000040,TCMDOR01
     &, TCMDOR02,TCMDOR03,TCMDOR04,DUM0000041,TCMDORCL,DUM0000042
     &, TCM0SRVD,DUM0000043,TCATMPS,TCATMBLS,DUM0000044,TCRPKTMP
     &, TCR0PKTM,DUM0000045,T021021,T021022,T021061,T021062,T021071
     &, T021201,T021202,T021211,T021212,T021121,T021122,T021111
     &, T021131,T021141,DUM0000046,T030141,T030021,T030022,T030371
     &, T030031,DUM0000047,T030381,T030382,DUM0000048,T036051
     &, T036052,T036011,T036012,T036021,T036022,T049051,DUM0000049
     &, T052041,DUM0000050,T052011,T052051,T052052   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DZTR     
     &, DZPLLI(2)     
     &, DZPHHI(2)     
C$
      INTEGER*4
     &  DZNE     
     &, DZNP     
     &, DZNT     
C$
      LOGICAL*1
     &  DZSDP     
     &, DZSDB     
     &, DZSDF     
     &, DZSDG     
     &, DZFB     
     &, DZFP     
     &, DZFD     
     &, DZFDN     
     &, DZFG     
     &, DZFPK     
     &, DZSA     
     &, DZSBI(2)     
     &, DZSFI(2)     
     &, DZSGI     
     &, DZSHI(2)     
     &, DZSM     
     &, DZSP     
     &, DZSS     
     &, DZST     
C$
      EQUIVALENCE
     &  (DZNT,YITAIL),(DZFB,TCATMBLS),(DZFD,TCR0MAIN),(DZFDN,DZSPL)     
     &, (DZFG,VBOG),(DZFP,TCATMPS),(DZFPK,TCR0PKTM),(DZNE,HRECS)        
     &, (DZNP,HRPNEU),(DZPLLI,EPBL),(DZPHHI,EPBH),(DZSA,TCRACTAT)       
     &, (DZSBI(1),IDDBSBI),(DZSFI(1),IDDBSFI),(DZSDP,TCMDOR01)          
     &, (DZSDB,TCMDOR02),(DZSDF,TCMDOR04),(DZSDG,TCMDOR03)              
     &, (DZSHI(1),IDDBSHI),(DZSM,TCRMAINT),(DZSP,TCRPKTMP)              
     &, (DZSS,TCRACONS),(DZST,TCRALLT),(DZTR,ETT1)                      
C------------------------------------------------------------------------------
C
C
C ---------------
C'Local_Variables
C ---------------
C
C
C
C    ----------------------------------------------------------------------
C                             M O D U L E
C               V A R I A B L E S    C O N V E N T I O N
C    ----------------------------------------------------------------------
C
C                L - local variable
C
C                X - common data base variable (read/write)
C
C                E - common data base variable (read only)
C
C
C'Ident
C
      CHARACTER*55
     &  REV /
     &  '$Source: usd8dz.for.17 18Dec1992 02:31 usd8 M.WARD $'/
C
C
C
C ----------------------
C ECS' Integer_Variables
C ----------------------
C
C       Label       Description                 Units        Equival
C       -----       -----------                 -----        -------
C
       INTEGER*4 I    !L- DO LOOP INDEX
C
C
C --------------------
C ECS'  Real_Variables
C --------------------
C
C
      REAL*4
C
C
C       Label       Description                       Units        Equival
C       -----       -----------                       -----        -------
C
     & DZTL         !L- ITER TIME LAST
C
C ----------------------
C ECS' Logical_Variables
C ----------------------
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
C       -----       -----------                              -------
C
C
     & FIRST     /.TRUE./!L- FIRST PASS INITILIZATION FLAG
     &,DZFTD     !L- I/F TEMP RESET DARCK CONCEPT LOCAL
     &,DZFT      !L- I/F TEMP RESET DARK CONCEPT LATCH
     &,DZFU1A    !L- 100A
     &,DZFU3A    !L- 300A
C
C
C --------------
C ECS' Constants
C --------------
C
C
C
      REAL*4
C
C       Label      Value           Description              Units
C       -----      -----           -----------              -----
C
C
     &  DZC10    /0.5  /!TEMP RESET MAXIMUM ALLOWABLE FLOW  lb/min
C
C
C
       ENTRY DMISC
C
C
C ----------------
C'Start_of_program
C ----------------
C
C
CD    MODULE FREEZE FLAG
C     ------------------
C
      IF ( DZF ) THEN
C
      ELSE
C
C
C -------------------------
C First_Pass_Initialization
C -------------------------
C
      IF ( FIRST ) THEN
C !FM+
C !FM  29-Jun-92 10:35:54 M.WARD
C !FM    < CLOSE ALL DOORS WHEN FIRST LOADED >
C !FM
        DO I = 1,10
          DZSPL(I) = .TRUE.
        ENDDO
C !FM-
C
      FIRST = .FALSE.
C
C     GREY CONCEPT INITIALIZATION
C
      T021021 = .TRUE.
      T021022 = .TRUE.
      T021061 = .TRUE.
      T021062 = .TRUE.
      T021071 = .TRUE.
      T021111 = .TRUE.
      T021121 = .TRUE.
      T021122 = .TRUE.
      T021131 = .TRUE.
      T021141 = .TRUE.
      T021201 = .TRUE.
      T021202 = .TRUE.
      T021211 = .TRUE.
      T021212 = .TRUE.
*
      T030021 = .TRUE.
      T030022 = .TRUE.
      T030031 = .TRUE.
      T030141 = .TRUE.
      T030371 = .TRUE.
      T030381 = .TRUE.
      T030382 = .TRUE.
*
      T036011 = .TRUE.
      T036012 = .TRUE.
      T036021 = .TRUE.
      T036022 = .TRUE.
      T036051 = .TRUE.
      T036052 = .TRUE.
*
      T049051 = .TRUE.
*
      T052011 = .TRUE.
      T052041 = .TRUE.
C
      ELSE
C
      ENDIF
C
CD --------------------------------
CD   FUNCTION A - CONFIGURATION FLAGS
CD --------------------------------
C
CD  010   FIRST PASS INITIALIZATION
C
          IF ( DZTL .NE. YITIM ) THEN
C
CD  020   UPDATE [DZTL] ITER. TIME LAST
C         -----------------------------
C
          DZTL = YITIM
C
CD A020   IF  USAIR DASH8-100A  THEN
C         --------------------------
C
          IF ( DZNT.EQ.226 ) THEN
C
CD A040   UPDATE [DZF300] CONFIG. FLAG /300
C         ---------------------------------
C
          DZF300 = .FALSE.
C
CD A060   UPDATE [DZFA] CONFIG FLAG /A
C         ----------------------------
C
          DZFA = .TRUE.
C
CD A080   UPDATE [DZFAPU] APU AVAIL. FLAG
C         -------------------------------
C
          DZFAPU = .TRUE.
C
CD        ELSE
C         ----
C
          ELSE
C
          TCM0SRVD = .TRUE.
          T052051 = .TRUE.
          T052052 = .TRUE.
 
CD        ENDIF
C         -----
C
          ENDIF
C
CD A100   IF USAIR DASH8-300A THEN
C         ------------------------
C
          IF ( DZNT.EQ.230 ) THEN
C
CD A120   UPDATE [DZF300] CONFIG. FLAG /300
C         ---------------------------------
C
          DZF300 = .TRUE.
C
CD A140   UPDATE [DZFA] CONFIG FLAG /A
C         ----------------------------
C
          DZFA = .TRUE.
C
CD A160   UPDATE [DZFAPU] APU AVAIL. FLAG
C         -------------------------------
C
          DZFAPU = .TRUE.
C
CD        ELSE
C         ----
C
          ELSE
C
CD        ENDIF
C         -----
C
          ENDIF
C
CD        ELSE
C
          ELSE
C
CD        ENDIF
C
          ENDIF
C
C
CD ----------------------------------
CD   FUNCTION B - TEMPERATURE RESETS
CD ----------------------------------
C
CD B200 IF GENERAL RESET OR MAINTNANCE RESET THEN
C       ---------------------
C
        IF ( DZST .OR. DZSM ) THEN
C
CD B500 IF NO FLOW TO CENTER PNEU DUCT THEN
C       -----------------------------------
C
        IF ( ( DAWBI(1) + DAWBI(2) + DAWB ) .LT. DZC10 ) THEN
C
CD B520 UPDATE [DCTD] PNEU DUCT TEMP
C       ----------------------------
C
        DCTD = DZTR
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD      DO I = 1 , 2
C       ------------
C
        DO I = 1 , 2
C
CD B540 IF NO FLOW TO A/I SYSTEM  THEN
C       ------------------------------
C
        IF ( DAWAI(I) + ABS(DAWA) .LT. DZC10 ) THEN
C
CD B560 UPDATE [DCTAI] A/I DUCT TEMP
C       ----------------------------
C
        DCTAI(I) = DZTR
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD B800 IF NO FLOW TO ENG BLD DUCT THEN
C       -------------------------------
C
        IF ( ( DAWAI(I) + DAWBI(I) + DAWBKI(I) ) .LT. DZC10 ) THEN
C
CD B820 UPDATE [DCTBI] ENG BLD DUCT TEMP
C       --------------------------------
C
        DCTBI(I) = DZTR
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD B840 IF NO FLOW TO HBOV HX OUTLET THEN
C       ---------------------------------
C
        IF ( DAWHI(I) .LT. DZC10 ) THEN
C
CD B860 UPDATE [DCTXHI] HBOV HX OUTLET TEMP
C       -----------------------------------
C
        DCTXHI(I) = DZTR
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD B1000 IF NO FLOW TO PACK THEN
C        -----------------------
C
         IF ( DKWFI(I) .LT. DZC10 ) THEN
C
CD B1020 UPDATE [] PACK TEMPERATURES
C        ---------------------------
C
         DKTCI(I) = DZTR
         DKTFI(I) = DZTR
         DKTKAI(I)= DZTR
         DKTKI(I) = DZTR
         DKTPI(I) = DZTR
         DKTSI(I) = DZTR
         DKTTI(I) = DZTR
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD B1040 IF NO FLOW TO AIR CONDITIONNING SYSTEM THEN
C        -------------------------------------------
C
         IF ( ( DKWKI(1) + DKWKI(2) ) .LT. DZC10 ) THEN
C
CD B1060 UPDATE [] AIR COND TEMPERATURES
C        -------------------------------
C
         DNTAI(I) = DZTR
         DNTAAI(I)= DZTR
         DNTRI(I) = DZTR
         DNTRAI(I)= DZTR
         DNTFI(I) = DZTR
         DNTDI(I) = DZTR
         DNTDAI(I)= DZTR
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD B1200 IF NO FLOW TO ZONES THEN
C        ------------------------
C
         IF ( DNWDI(I) .LT. DZC10 ) THEN
C
CD B1220 UPDATE [DNTCI DNTCAI] ZONES TEMP
C        --------------------------------
C
         DNTCI(I) = DZTR
         DNTCAI(I)= DZTR
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDDO
C        -----
C
         ENDDO
C
CD B1260 UPDATE [DNTS] SKIN TEMP
C        -----------------------
C
         DNTS = DNHS
C
CD      ELSE
C       ----
C
        ELSE
C
CD      DO I = 1 , 2
C       ------------
C
        DO I = 1 , 2
C
CD B220 IF COMPT RESET TO AMBIENT THEN
C       ------------------------------
C
        IF ( DZSA ) THEN
C
CD B281 UPDATE [DNTCI,DNTCAI] COMPT TEMP
C       --------------------------------
C
        DNTCI(I) = DZTR
        DNTCAI(I)= DZTR
C
CD      ELSE
C       ----
C
        ELSE
C
CD B240 IF COMPT RESET TO SELECTION THEN
C       --------------------------------
C
        IF ( DZSS ) THEN
C
CD B260 UPDATE [DOTSI] COMPT TEMP SELECTED
C       ----------------------------------
C
        DOTSI(1) = IADOHSI
        DOTSI(2) = IADOHS2
C
CD B280 UPDATE [DNTCI,DNTCAI] COMPT TEMP
C       --------------------------------
C
        DNTCI(I) = DOTSI(I)
        DNTCAI(I)= DOTSI(I)
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD      ENDDO
C       -----
C
        ENDDO
C
CD      ENDIF (B200)
C       -----
C
        ENDIF
C
C
CD B1280 IF PACK TEMP RESET SELECTED THEN
C        --------------------------------
C
         IF ( DZSP ) THEN
C
C
         DO I = 1 , 2
C
CD B1300 RESET PACK TEMPERATURES
C        -----------------------
C
         DKTCI(I) = DZTR
         DKTFI(I) = DZTR
         DKTKAI(I)= DZTR
         DKTKI(I) = DZTR
         DKTPI(I) = DZTR
         DKTSI(I) = DZTR
         DKTTI(I) = DZTR
C
C
         ENDDO
C
C
         ELSE
C
         ENDIF
C
CD ----------------------------------
CD   FUNCTION C - DARK CONCEPT
CD ----------------------------------
C
CD C200 UPDATE [DZFPK] I/F PACK TEMP RST DARK CONCEPT
C       --------------------------------------------
C
        DZFPK = DOFCI(1) .OR. DOFCI(2)
C
CD ------------------------------------
CD   FUNCTION E - GROUND CART AND DOORS
CD ------------------------------------
C
CD E200   IF [DZNP] NE -1 THEN
C         --------------------
C
          IF (HRPNEU .NE. -1) THEN
C
CD E220   IF [DZNP] EQ 1 THEN
C         -------------------
C
           IF (HRPNEU .EQ. 1) THEN
C
CD E240   IF [DZSGI] PNEU GRND CART CONNECTED THEN
C         ----------------------------------------
C
            IF (DZSGI) THEN
C
CD        ELSE
C         ----
            ELSE
C
CD E260   SET [DZSGI] PNEU GRND CART CONNECTED
C         -------------------------------------
C
             DZSGI = .TRUE.
C
CD        ENDIF
C         -----
            ENDIF
C
CD        ELSE
C         ----
           ELSE
C
CD E300   IF [DZSGI] PNEU GRND CARTS CONNECTED THEN
C         -----------------------------------------
C
            IF (DZSGI) THEN
C
CD E320   RESET [DZSGI] PNEU GRND CARTS CONNECTED
C         ---------------------------------------
C
             DZSGI = .FALSE.
C
CD        ENDIF
C         -----
            ENDIF
C
CD        ENDIF
C         -----
           ENDIF
C
CD        ENDIF
C         -----
          ENDIF
C
          dzspl(3) = .true.
C
CD E500   IF [DZNE] NE -1 THEN
C         --------------------
C
          IF (HRECS .NE. -1) THEN
C
          dzspl(4)=.true.
C
CD E520   IF [DZNE] EQ 0 THEN
C         -------------------
C
           IF (HRECS .EQ. 0) THEN
C
          dzspl(5) =.true.
C
CD E540   SET [DZSDP] DOOR LEFT 1 OPEN COMD
C         ---------------------------------
C
             DZSDP =  .TRUE.
C
C
CD E560   SET [DZSDB] CGO DOOR OPEN CMD
C         -----------------------------
C
             DZSDB =  .TRUE.
C
C
CD E580   SET [DVFR] RESET FLAG
C         ---------------------
C
             DVFR = .TRUE.
C
CD        ELSE
C         ----
           ELSE
C
C
C
CD E800   RESET [DZFDN] DOOR CLOSE COMMAND
C         --------------------------------
C
              DZFDN = .TRUE.
              dzspl(2) = .true.
C
C
CD E820   IF [DZNE] EQ 2,3 THEN
C         -------------------
C
          IF ( (HRECS .EQ. 2) .OR. ( HRECS .EQ. 3 ) )  THEN
C
CD E840   SET CPCS MODE
C         -------------
C
          DVNM  =  3
C
CD E860   SET NUMODE  FLAG
C         -----------------
C
          DVFN  = .TRUE.
C
CD E1000  SET [DVFC] CAB EVER CLAMP FLAG
C         ------------------------------
C
          DVFC = .TRUE.
C
CD E1020  SET [DVFD] CAB PRESS CONT DESCENT FLAG
C         --------------------------------------
C
          DVFD = .FALSE.
C
CD E1040  IF DZNE = 3 THEN
C         ----------------
C
          IF ( HRECS .EQ. 3 ) THEN
C
CD E1060  SET [DVFD] CAB PRESS CONT DESCENT FLAG
C         --------------------------------------
C
          DVFD = .TRUE.
C
          ELSE
C
CD        ENDIF
C         -----
          ENDIF
C
CD        ELSE
C         ----
C
          ELSE
C
CD        ENDIF
C         -----
C
          ENDIF
C
CD        ENDIF
C         -----
C
          ENDIF
C
CD        ELSE
C         ----
C
          ELSE
C
          DZFDN = .FALSE.
C
CD        ENDIF
C         -----
C
          ENDIF
C
C
C
C
CD ------------------------------------
CD   FUNCTION G - CNIA LOGIC
CD ------------------------------------
C
CD G200 UPDATE [DZFB] BLEED CNIA FLAG
C       -----------------------------
C
        DZFB = DZSHI(1) .OR. DZSHI(2) ! .OR. DZSBI(1) .OR. DZSBI(2)
C
CD G220 UPDATE [DZFP] PACK CNIA FLAG
C       ----------------------------
C
        DZFP = .NOT. ( DZSFI(1) .OR. DZSFI(2) )
C
C
C
CD ------------------------------------
CD   FUNCTION G - CNIA LOGIC
CD ------------------------------------
C
C
CD H200 DO I 1 , 2
C       ----------
C
        DO I = 1 , 2
C
CD      UPDATE [] PNEU GAGE PRES
C       ------------------------
C
        DZPLI(I) = DZPLLI(I) - DTPA
        DZPHI(I) = DZPHHI(I) - DTPA
        DZPBI(I) = DAPBI(I)  - DTPA
        DZPAI(I) = DAPAI(I)  - DTPA
        DZPFI(I) = DKPFI(I)  - DTPA
C
CD H220 ENDDO
C       -----
C
        ENDDO
C
CD H240 UPDATE [DZPD] PNEU CENTER GAGE PRESS.
C       ------------------------------------
C
        DZPD = DAPD - DTPA
C
C
C
C
CD ------------------------------------
CD   FUNCTION L - DOOR REPOSITION LOGIC
CD ------------------------------------
C
CD L200 IF DURING REPOS. AND IN FLIGHT THEN
C       -----------------------------------
C
        IF ( DTFD .AND. .NOT. DZFG ) THEN
C
CD L220 UPDATE [DZSDP] PASS DOOR CLOSE CMD
C       ----------------------------------
C
        DZSDP = .FALSE.
C
CD L240 UPDATE [DZSDB] BAG DOOR CLOSE CMD
C       ---------------------------------
C
        DZSDB = .FALSE.
C
CD L260 UPDATE [DZSDF] FRONT EMER EXIT DOOR CLOSE CMD
C       ---------------------------------------------
C
        DZSDF = .FALSE.
C
CD L280 UPDATE [DZSDG] GALLEY SERVICE DOOR CLOSE CMD
C       --------------------------------------------
C
        DZSDG = .FALSE.
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD -----------------
CD End_of_simulation
CD -----------------
C
CD    MODULE FREEZE ENDIF
C     -------------------
      ENDIF
C
CD    RETURN
C     ------
C
      RETURN
C
CD    END
C     ---
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00641 MODULE FREEZE FLAG
C$ 00706 --------------------------------
C$ 00707 FUNCTION A - CONFIGURATION FLAGS
C$ 00708 --------------------------------
C$ 00710 010   FIRST PASS INITIALIZATION
C$ 00714 020   UPDATE [DZTL] ITER. TIME LAST
C$ 00719 A020   IF  USAIR DASH8-100A  THEN
C$ 00724 A040   UPDATE [DZF300] CONFIG. FLAG /300
C$ 00729 A060   UPDATE [DZFA] CONFIG FLAG /A
C$ 00734 A080   UPDATE [DZFAPU] APU AVAIL. FLAG
C$ 00739 ELSE
C$ 00748 ENDIF
C$ 00753 A100   IF USAIR DASH8-300A THEN
C$ 00758 A120   UPDATE [DZF300] CONFIG. FLAG /300
C$ 00763 A140   UPDATE [DZFA] CONFIG FLAG /A
C$ 00768 A160   UPDATE [DZFAPU] APU AVAIL. FLAG
C$ 00773 ELSE
C$ 00778 ENDIF
C$ 00783 ELSE
C$ 00787 ENDIF
C$ 00792 ----------------------------------
C$ 00793 FUNCTION B - TEMPERATURE RESETS
C$ 00794 ----------------------------------
C$ 00796 B200 IF GENERAL RESET OR MAINTNANCE RESET THEN
C$ 00801 B500 IF NO FLOW TO CENTER PNEU DUCT THEN
C$ 00806 B520 UPDATE [DCTD] PNEU DUCT TEMP
C$ 00811 ELSE
C$ 00816 ENDIF
C$ 00821 DO I = 1 , 2
C$ 00826 B540 IF NO FLOW TO A/I SYSTEM  THEN
C$ 00831 B560 UPDATE [DCTAI] A/I DUCT TEMP
C$ 00836 ELSE
C$ 00841 ENDIF
C$ 00846 B800 IF NO FLOW TO ENG BLD DUCT THEN
C$ 00851 B820 UPDATE [DCTBI] ENG BLD DUCT TEMP
C$ 00856 ELSE
C$ 00861 ENDIF
C$ 00866 B840 IF NO FLOW TO HBOV HX OUTLET THEN
C$ 00871 B860 UPDATE [DCTXHI] HBOV HX OUTLET TEMP
C$ 00876 ELSE
C$ 00881 ENDIF
C$ 00886 B1000 IF NO FLOW TO PACK THEN
C$ 00891 B1020 UPDATE [] PACK TEMPERATURES
C$ 00902 ELSE
C$ 00907 ENDIF
C$ 00912 B1040 IF NO FLOW TO AIR CONDITIONNING SYSTEM THEN
C$ 00917 B1060 UPDATE [] AIR COND TEMPERATURES
C$ 00928 ELSE
C$ 00933 ENDIF
C$ 00938 B1200 IF NO FLOW TO ZONES THEN
C$ 00943 B1220 UPDATE [DNTCI DNTCAI] ZONES TEMP
C$ 00949 ELSE
C$ 00954 ENDIF
C$ 00959 ENDDO
C$ 00964 B1260 UPDATE [DNTS] SKIN TEMP
C$ 00969 ELSE
C$ 00974 DO I = 1 , 2
C$ 00979 B220 IF COMPT RESET TO AMBIENT THEN
C$ 00984 B281 UPDATE [DNTCI,DNTCAI] COMPT TEMP
C$ 00990 ELSE
C$ 00995 B240 IF COMPT RESET TO SELECTION THEN
C$ 01000 B260 UPDATE [DOTSI] COMPT TEMP SELECTED
C$ 01006 B280 UPDATE [DNTCI,DNTCAI] COMPT TEMP
C$ 01012 ELSE
C$ 01017 ENDIF
C$ 01022 ENDIF
C$ 01027 ENDDO
C$ 01032 ENDIF (B200)
C$ 01038 B1280 IF PACK TEMP RESET SELECTED THEN
C$ 01046 B1300 RESET PACK TEMPERATURES
C$ 01065 ----------------------------------
C$ 01066 FUNCTION C - DARK CONCEPT
C$ 01067 ----------------------------------
C$ 01069 C200 UPDATE [DZFPK] I/F PACK TEMP RST DARK CONCEPT
C$ 01074 ------------------------------------
C$ 01075 FUNCTION E - GROUND CART AND DOORS
C$ 01076 ------------------------------------
C$ 01078 E200   IF [DZNP] NE -1 THEN
C$ 01083 E220   IF [DZNP] EQ 1 THEN
C$ 01088 E240   IF [DZSGI] PNEU GRND CART CONNECTED THEN
C$ 01093 ELSE
C$ 01097 E260   SET [DZSGI] PNEU GRND CART CONNECTED
C$ 01102 ENDIF
C$ 01106 ELSE
C$ 01110 E300   IF [DZSGI] PNEU GRND CARTS CONNECTED THEN
C$ 01115 E320   RESET [DZSGI] PNEU GRND CARTS CONNECTED
C$ 01120 ENDIF
C$ 01124 ENDIF
C$ 01128 ENDIF
C$ 01134 E500   IF [DZNE] NE -1 THEN
C$ 01141 E520   IF [DZNE] EQ 0 THEN
C$ 01148 E540   SET [DZSDP] DOOR LEFT 1 OPEN COMD
C$ 01154 E560   SET [DZSDB] CGO DOOR OPEN CMD
C$ 01160 E580   SET [DVFR] RESET FLAG
C$ 01165 ELSE
C$ 01171 E800   RESET [DZFDN] DOOR CLOSE COMMAND
C$ 01178 E820   IF [DZNE] EQ 2,3 THEN
C$ 01183 E840   SET CPCS MODE
C$ 01188 E860   SET NUMODE  FLAG
C$ 01193 E1000  SET [DVFC] CAB EVER CLAMP FLAG
C$ 01198 E1020  SET [DVFD] CAB PRESS CONT DESCENT FLAG
C$ 01203 E1040  IF DZNE = 3 THEN
C$ 01208 E1060  SET [DVFD] CAB PRESS CONT DESCENT FLAG
C$ 01215 ENDIF
C$ 01219 ELSE
C$ 01224 ENDIF
C$ 01229 ENDIF
C$ 01234 ELSE
C$ 01241 ENDIF
C$ 01249 ------------------------------------
C$ 01250 FUNCTION G - CNIA LOGIC
C$ 01251 ------------------------------------
C$ 01253 G200 UPDATE [DZFB] BLEED CNIA FLAG
C$ 01258 G220 UPDATE [DZFP] PACK CNIA FLAG
C$ 01265 ------------------------------------
C$ 01266 FUNCTION G - CNIA LOGIC
C$ 01267 ------------------------------------
C$ 01270 H200 DO I 1 , 2
C$ 01275 UPDATE [] PNEU GAGE PRES
C$ 01284 H220 ENDDO
C$ 01289 H240 UPDATE [DZPD] PNEU CENTER GAGE PRESS.
C$ 01297 ------------------------------------
C$ 01298 FUNCTION L - DOOR REPOSITION LOGIC
C$ 01299 ------------------------------------
C$ 01301 L200 IF DURING REPOS. AND IN FLIGHT THEN
C$ 01306 L220 UPDATE [DZSDP] PASS DOOR CLOSE CMD
C$ 01311 L240 UPDATE [DZSDB] BAG DOOR CLOSE CMD
C$ 01316 L260 UPDATE [DZSDF] FRONT EMER EXIT DOOR CLOSE CMD
C$ 01321 L280 UPDATE [DZSDG] GALLEY SERVICE DOOR CLOSE CMD
C$ 01326 ELSE
C$ 01331 ENDIF
C$ 01336 -----------------
C$ 01337 End_of_simulation
C$ 01338 -----------------
C$ 01340 MODULE FREEZE ENDIF
C$ 01344 RETURN
C$ 01349 END
