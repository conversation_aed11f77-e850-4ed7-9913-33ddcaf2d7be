/* $ScmHeader: 99963zv7z817v08zCCu2999999978&6|@ $*/
/* $Id: ship_34g.h,v 1.3 2002/03/21 12:05:23 navrad(MASTER_VERSION|CAE_MR) Exp $*/
/*
C'Revision_History
C
C  ship_34g.h.1  6Feb2002 15:07 brj7 hall   
C       < change MK8 stuff again >
*/

/* EGPWS SYSTEM DEFINITIONS */

#define WNV 0
#define XSW 1

/* SELF-TEST Command define section 000-099 */

#define SELF_TEST_SHORT 1
#define SELF_TEST_LONG  2
#define SELF_TEST_CF    3

/* GENERAL Command define section 100-199 */

#define GEN_INP_SET 100
#define GEN_LT_SET  101

/* WINVIEWS Command define section 900-999 */

/* Protocol, General */

#define WNV_CTRL_Z    900
#define WNV_CTRL_Y    901
#define WNV_RESET     902

/* Overwrites */

#define WNV_REPOS_ON  910
#define WNV_REPOS_OFF 911

/* Any EGPWS */

#define WNV_MKX_PS    920
#define WNV_MKX_ST1   921
#define WNV_MKX_ST2   922
#define WNV_MKX_ST3   923
#define WNV_MKX_ST4   924
#define WNV_MKX_ST5   925
#define WNV_MKX_ST6   926
#define WNV_MKX_STL   927
#define WNV_MKX_STC   928
#define WNV_MKX_FHE   929
#define WNV_MKX_TXT   930
#define WNV_MKX_YES   931
#define WNV_MKX_EXIT  932

/* MK8 Configuration Module */

#define WNV_MK8_CFG   950
#define WNV_MK8_CUW   951
#define WNV_MK8_CMR   952
#define WNV_MK8_CAT   953
#define WNV_MK8_CFG_CUW 954

#define CR 0x0d

/* EGPWS X-SWITCH DEFINITIONS */

#define XSW_READ 1
#define XSW_WRITE 2

