C'Module_ID             X9DA
C'Documentation
C'Customer              All simulators with Digital Audio System
C'Author                D.Bowness
C'Date                  January 1991
C'Application           Store and retrieve digital audio data
C                       to/from Digital Voice Phrase Database
C'Revision_History
C
C     23-Apr-92 D.BOWNESS
C       Fix record size bug in reading/writing buffers.
C
C     18-Feb-92 D.BOWNESS
C       Add start/end ptrs to phrase length reply.
C
C     11-Feb-92 D.BOWNESS
C       Remove reset of "datios" in playback section.
C
C     13-Jan-92 D.BOWNESS
C       Adding CVAX/CIBM code.
C
C      4-Dec-91 19:14:36 D.BOWNESS
C       Use I*4 variable to calculate playback block number.
C
C     20-Nov-91 17:17:41 D.BOWNESS
C       Clear src_file flag if file not opened in COPYDAT routine.
C
C     22-Oct-91 04:35:37 D.BOWNESS
C       When last phrase deleted, re-initialize header.
C
C     18-Oct-91 16:07:46 D.BOWNESS
C       Modify processing of phrase inventory command.
C
C     17-Oct-91 D.BOWNESS
C       Don't wait for X9REND to be set before ending a recording.
C       When record or playback start, set X9CHNACT to YTSIMTM.
C
C     31-May-91 D.BOWNESS
C       Add checks for bad buf or chn for BLKIO read
C
C     29-May-91 D.BOWNESS
C       Call at most one BLKIO read/write per iteration
C
C      1-May-91 20:23:24 D.BOWNESS
C       Use separate DAS data and DAS header files
C
C      1-May-91 17:33:10 D.BOWNESS
C       Reset X9RCUR when starting a recording
C
C     23-Apr-91 21:04:36 D.BOWNESS
C       Add phrase leading/trailing edge edit
C
C     16-Apr-91 D.BOWNESS
C       Add start of phrase ptr to allow for leading/trailing edge edits
C
C     10-Apr-91 13:33:55 D.BOWNESS
C       Debug initial request to fill all buffers
C'
C
      SUBROUTINE USD8X9DA
C     -------------------
      IMPLICIT NONE
C
C'Functions
      INTEGER   ALLOCREC, COPYDAT
     .,         IOR, IAND
CIBM
     .,         CAE_CVFS
     .,         LG
CIBMEND
CVAX
CVAX  EXTERNAL  ASTC1B1, ASTC1B2
CVAX .,         ASTC2B1, ASTC2B2
CVAX .,         ASTC3B1, ASTC3B2
CVAX .,         ASTC4B1, ASTC4B2
CVAX .,         ASTC5B1, ASTC5B2
CVAX .,         ASTC6B1, ASTC6B2
CVAX .,         ASTC7B1, ASTC7B2
CVAX .,         ASTC8B1, ASTC8B2
CVAX .,         ASTC9B1, ASTC9B2
CVAX .,         ASTC10B1,ASTC10B2
CVAXEND
C
C'Include_files
      INCLUDE 'usd8x9da.par'                ! NOFPC
      INCLUDE 'usd8x9da.inc'                ! NOFPC
CVAX
CVAX  INCLUDE '($IODEF)/NOLIST'             ! NOFPC
CVAXEND
C
C'Common_variables
CP    USD8
CP   .    X9STATUS,  X9HDRCTL,  X9BUFFER,
CP   .    X9MLIST,
CP   .    X9RREQ,    X9REND,    X9RSTOP,   X9RPTR,    X9RCUR,
CP   .    X9RPUT,    X9RBUF,    X9RRDY,    X9RPKTP,   X9RREC,
CP   .    X9RERR,
CP   .    X9PREQ,    X9PEND,    X9PSTOP,   X9PPOS,    X9PLIS,
CP   .    X9PGET,    X9PBUF,    X9PRDY,    X9PREC,    X9PPTR,
CP   .    X9PBUFEP,  X9PBUFSP,  X9PSAV,
CP   .    X9CHNACT,  RFACTRE1,  RFACTPL1,  YTSIMTM
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 21-Aug-2019 19:30:02 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*4   
     &  YTSIMTM        ! SIMULATOR TIME (SEC)
C$
      INTEGER*4
     &  X9CHNACT(10)   ! CHANNEL ACTIVE TIMER
     &, X9PRDY(2,10)   ! VOICE DATA BLOCK INPUT COMPLETE
     &, X9RRDY(2,10)   ! VOICE DATA BLOCK OUTPUT COMPLETE
     &, X9STATUS(50)   ! STATUS CODES
C$
      INTEGER*2
     &  X9BUFFER(4096,2,10)
C$                     ! VOICE DATA BUFFERS
     &, X9HDRCTL       ! STATUS OF PHRASE DATABASE HEADER
     &, X9MLIST(50)    ! MAINTENANCE REPLY LIST
     &, X9PBUF(10)     ! POINTS TO ACTIVE PLAYBACK BUFFER
     &, X9PBUFEP(2,10) ! PLAYBACK BUFFER END POINTER
     &, X9PBUFSP(2,10) ! PLAYBACK BUFFER START POINTER
     &, X9PGET(10)     ! PTR TO NEXT BUFFER FOR VOICE DATA RECORD
     &, X9PLIS(32,10)  ! LIST OF PHRASES FOR PLAYING A MESSAGE
     &, X9PPOS(10)     ! CURRENT POSITION IN X9PLIS
     &, X9PPTR(10)     ! CURRENT PLAY POSITION IN ACTIVE BUFFER
     &, X9PREC(10)     ! NEXT RECORD TO BE READ FOR PLAYBACK
     &, X9PREQ(10)     ! BEGIN PLAYING A LIST OF PHRASES
     &, X9PSAV(10)     ! LAST POINTER SAVED IN CASE OF RETRANSMISION
     &, X9RBUF(10)     ! POINTS TO ACTIVE RECORDING BUFFER
     &, X9RCUR(10)     ! NUMBER OF MOST RECENTLY RECEIVED PACKET
     &, X9RERR(10)     ! ERROR CODE FOR EACH RECORD CHANNEL
     &, X9RPKTP(10)    ! POINT TO DATA BEING COPIED FROM ET. PACKET
     &, X9RPTR(10)     ! CURRENT RECORDING POSITION IN ACTIVE BUFFER
     &, X9RPUT(10)     ! WRITE VOICE DATA INTO THIS BUFFER
     &, X9RREC(10)     ! MOST RECENT RECORD WRITTEN
     &, X9RREQ(10)     ! BEGIN RECORDING THIS PHRASE
C$
      LOGICAL*1
     &  X9PEND(10)     ! PLAYBACK FINISHED
     &, X9PSTOP(10)    ! STOP PLAYBACK
     &, X9REND(10)     ! RECORDING FINISHED
     &, X9RSTOP(10)    ! STOP RECORDING
C$
      LOGICAL*1
     &  DUM0000001(24),DUM0000002(128492),DUM0000003(96)
     &, DUM0000004(10),DUM0000005(10),DUM0000006(60)
     &, DUM0000007(148),DUM0000008(6),DUM0000009(60)
     &, DUM0000010(40),DUM0000011(40)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YTSIMTM,DUM0000002,X9STATUS,DUM0000003,X9PLIS
     &, X9PPOS,X9PREQ,X9RREQ,DUM0000004,X9PEND,X9PSTOP,DUM0000005
     &, X9REND,X9RSTOP,DUM0000006,X9CHNACT,DUM0000007,X9HDRCTL
     &, DUM0000008,X9BUFFER,X9PREC,X9PBUF,X9PGET,X9PPTR,X9PSAV
     &, DUM0000009,X9PBUFSP,X9PBUFEP,DUM0000010,X9RREC,X9RBUF
     &, X9RPUT,X9RPTR,X9RRDY,X9PRDY,X9RCUR,X9RPKTP,X9RERR,DUM0000011
     &, X9MLIST   
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFACTPL1       ! DV ACTive PLay channel 1              MO2800
     &, RFACTRE1       ! DV ACTive REcord channel 1            MOB000
C$
      LOGICAL*1
     &  DUM0100001(4328),DUM0100002(1182)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFACTPL1,DUM0100002,RFACTRE1  
C------------------------------------------------------------------------------
C
C'CDB_Equivalences
      INTEGER*2
     &    RFACTRE(10)
     &,   RFACTPL(10)
      EQUIVALENCE
     .   (RFACTRE,   RFACTRE1)
     .,  (RFACTPL,   RFACTPL1)
C
C'Local_variables
      INTEGER*4
     .    blknum              ! BLKIO control variable
     .,   amnt, posn
     .,   hdrdcb              ! BLKIO control variable
     .,   hdrinfo(3)          ! BLKIO control variable
     .,   datinfo(3)          ! BLKIO control variable
     .,   hdrrdy
     .,   datrdy
     .,   hdrios              ! status from DAS header BLKIO
     .,   datios              ! status from DAS data BLKIO
     .,   prstat(2,10)        ! status from BLKIO read
     .,   rrstat(2,10)        ! status from BLKIO write
     .,   astadr(2,10)        ! read/write data BLKIO AST address
     .,   playast             /1/
     .,   rcrdast             /0/
     .,   hdrstate            ! processing state for header control section
     .,   buf                 ! voice data buffer index
     .,   chn                 ! channel index
     .,   pchn                ! save next playback channel index
     .,   rchn                ! save next record channel index
     .,   phr                 ! phrase directory index
     .,   rec                 ! voice data record number
     .,   mai                 ! maintenance list index
     .,   cpystate            ! status from COPYDAT routine
     .,   mode, force
      CHARACTER
     .    hdrname*64 /'DV$DASHDR'/
     .,   datname*64 /'DV$DASDAT'/
     .,   tmpname*64
     .,   hdr_dcb*512
     .,   dat_dcb*512
      LOGICAL*1
     .    hdrsp(256)
     .,   datsp(256)
     .,   band1
     .,   fpass     /.TRUE./
C=======================================================================
C
      ENTRY X9DA
C     ----------
C
      IF( fpass )THEN
        pchn = 1
        rchn = 1
        hdrios = 1
        datios = 1
        hdrrdy = 1
        datrdy = 1
        hdrstate = 0
CVAX
CVAX    hdrdcb = %LOC(hdr_dcb)
CVAX    DatDcb = %LOC(dat_dcb)
CVAX    astadr(1,1) = %LOC(ASTC1B1)
CVAX    astadr(2,1) = %LOC(ASTC1B2)
CVAX    astadr(1,2) = %LOC(ASTC2B1)
CVAX    astadr(2,2) = %LOC(ASTC2B2)
CVAX    astadr(1,3) = %LOC(ASTC3B1)
CVAX    astadr(2,3) = %LOC(ASTC3B2)
CVAX    astadr(1,4) = %LOC(ASTC4B1)
CVAX    astadr(2,4) = %LOC(ASTC4B2)
CVAX    astadr(1,5) = %LOC(ASTC5B1)
CVAX    astadr(2,5) = %LOC(ASTC5B2)
CVAX    astadr(1,6) = %LOC(ASTC6B1)
CVAX    astadr(2,6) = %LOC(ASTC6B2)
CVAX    astadr(1,7) = %LOC(ASTC7B1)
CVAX    astadr(2,7) = %LOC(ASTC7B2)
CVAX    astadr(1,8) = %LOC(ASTC8B1)
CVAX    astadr(2,8) = %LOC(ASTC8B2)
CVAX    astadr(1,9) = %LOC(ASTC9B1)
CVAX    astadr(2,9) = %LOC(ASTC9B2)
CVAX    astadr(1,10) = %LOC(ASTC10B1)
CVAX    astadr(2,10) = %LOC(ASTC10B2)
CVAXEND
        X9HDRCTL = OPEN                          ! open dv-das database
        X9STATUS(10) = IOR(X9STATUS(10),1)       ! flag "d.v. online"
        fpass = .FALSE.
      ENDIF
      IF( X9STATUS(1) .NE. 0 ) RETURN
C.......................................................................
C
 100  CONTINUE
      IF( X9HDRCTL .EQ. 0 ) GOTO 200   ! no header control processing
C
C     Header Control Section
C     ----------------------
C     Processes Digital Audio Data file open and close requests and
C     requests to read or write the file header
C
C --               Exit program when outstanding header I/O.
C                  Otherwise process the control request.
CIBM
      IF( hdrios .EQ. 0 .OR. datios .EQ. 0 ) RETURN
CIBMEND
CVAX
CVAX  IF( hdrrdy .EQ. 0 .OR. datrdy .EQ. 0 ) RETURN
CVAXEND
C
      GOTO( 101, 102, 103, 104 ) X9HDRCTL
C
C --               Header control error, set the Digital Voice status
C
      X9STATUS(1) = 1000 - X9HDRCTL
      X9STATUS(2) = hdrios
      hdrstate = 0
      RETURN
C
C ----             Open DV-DAS database and request header read.
C                  The database comprises 2 files.  They must both
C                  exist and are opened for READ/WRITE.
C
 101  CONTINUE
      IF( hdrstate .EQ. 0 )THEN
        hdrstate = 1
CVAX
CVAX    CALL NBLKIOO( %VAL(hdrdcb), %REF(hdrname), %VAL(20),
CVAX .                hdrios, hdrinfo, %REF(hdrsp), %VAL(2),
CVAX .                hdrrdy, , )
CVAX    CALL NBLKIOO( %VAL(DatDcb), %REF(datname), %VAL(20),
CVAX .                datios, datinfo, %REF(datsp), %VAL(2),
CVAX .                datrdy, , )
CVAXEND
CIBM
        force = 0
        mode = 1
        hdrrdy = CAE_CVFS( 'DV$DASHDR', tmpname )
        CALL REV_CURR( tmpname, hdrname,
     .                 'dat', force, mode, hdrrdy )
        buf = 1 + LG(hdrname)
        hdrname(buf:buf) = '\0'
        hdrrdy = 0
        hdrios = 0
C
C ------           Files opened using SZBLK recordsize in order to
C                  mimic VAX/VMS BLKIO operation.
C
        CALL CAE_IO_OPEN( hdrrdy, hdrios, hdrdcb,
     .                    %VAL(2*SZBLK), hdrname, %VAL(7) )
        IF( hdrrdy .NE. 1 )THEN
          X9HDRCTL = -19
          hdrios = hdrrdy
        ELSE
          datrdy = CAE_CVFS( 'DV$DASDAT', tmpname )
          CALL REV_CURR( tmpname, datname,
     .                   'dat', force, mode, datrdy )
          buf = 1 + LG(datname)
          datname(buf:buf) = '\0'
          datrdy = 0
          datios = 0
          CALL CAE_IO_OPEN( datrdy, datios, DatDcb,
     .                      %VAL(2*SZBLK), datname, %VAL(7) )
          IF( datrdy .NE. 1 )THEN
            X9HDRCTL = -18
            hdrios = datrdy
          ENDIF
        ENDIF
CIBMEND
      ELSE IF( hdrstate .EQ. 1 )THEN
        IF( hdrios .NE. 1 )THEN
          X9HDRCTL = -11
        ELSE IF( datios .NE. 1 )THEN
          X9HDRCTL = -12
        ELSE
          X9HDRCTL = READ
          hdrstate = 0
          X9STATUS(10) = IOR(X9STATUS(10),2) ! flag "database open"
        ENDIF
      ENDIF
      GOTO 100
C
C ----             Read DV-DAS database header
C
 102  CONTINUE
      IF( hdrstate .EQ. 0 )THEN
        hdrstate = 1
CVAX
CVAX    CALL NBLKIORW( %VAL(hdrdcb),Header,%VAL(1),%VAL(2*SZHDR),
CVAX .                 hdrios,%VAL(IO$_READVBLK),hdrsp,
CVAX .                 hdrrdy, , )
CVAXEND
CIBM
        hdrrdy = 0
        hdrios = 0
        blknum = 0
        amnt = 2 * SZHDR
        posn = 0
        CALL CAE_IO_READ( hdrrdy, hdrios, %VAL(hdrdcb), %VAL(2*SZBLK),
     .                    Header, blknum, amnt, posn )
        IF( hdrrdy .NE. 1 )THEN
          X9HDRCTL = -29
          hdrios = hdrrdy
        ENDIF
CIBMEND
      ELSE IF( hdrstate .EQ. 1 )THEN
        IF( hdrios .NE. 1 )THEN
          X9HDRCTL = -21
        ELSE
          X9HDRCTL = 0
          hdrstate = 0
        ENDIF
      ENDIF
      GOTO 100
C
C ----             Write DV-DAS database header
C
 103  CONTINUE
      IF( hdrstate .EQ. 0 )THEN
        hdrstate = 1
CVAX
CVAX    CALL NBLKIORW( %VAL(hdrdcb),Header,%VAL(1),%VAL(2*SZHDR),
CVAX .                 hdrios,%VAL(IO$_WRITEVBLK),hdrsp,
CVAX .                 hdrrdy, , )
CVAXEND
CIBM
        hdrrdy = 0
        hdrios = 0
        blknum = 0
        amnt = 2 * SZHDR
        posn = 0
        CALL CAE_IO_WRITE( hdrrdy, hdrios, %VAL(hdrdcb), %VAL(2*SZBLK),
     .                     Header, blknum, amnt, posn )
        IF( hdrrdy .NE. 1 )THEN
          X9HDRCTL = -39
          hdrios = hdrrdy
        ENDIF
CIBMEND
      ELSE IF( hdrstate .EQ. 1 )THEN
        IF( hdrios .NE. 1 )THEN
          X9HDRCTL = -31
        ELSE
          X9HDRCTL = 0
          hdrstate = 0
        ENDIF
      ENDIF
      GOTO 100
C
C ----             Close DAS header file
C
 104  CONTINUE
      IF( hdrstate .EQ. 0 )THEN
        hdrstate = 1
CVAX
CVAX    CALL NBLKIOC( %VAL(hdrdcb), hdrios, %REF(hdrsp), hdrrdy, , )
CVAX    CALL NBLKIOC( %VAL(DatDcb), datios, %REF(datsp), datrdy, , )
CVAXEND
CIBM
        hdrios = 0
        datios = 0
        CALL CAE_IO_CLOSE( hdrrdy, hdrios, %VAL(hdrdcb) )
        CALL CAE_IO_CLOSE( datrdy, datios, %VAL(DatDcb) )
        IF( hdrrdy .NE. 1 )THEN
          X9HDRCTL = -49
          hdrios = hdrrdy
        ELSE IF( datrdy .NE. 1 )THEN
          X9HDRCTL = -48
          hdrios = datrdy
        ENDIF
CIBMEND
      ELSE IF( hdrstate .EQ. 1 )THEN
        IF( hdrios .NE. 1 )THEN
          X9HDRCTL = -41
        ELSE IF( datios .NE. 1 )THEN
          X9HDRCTL = -42
        ELSE
          X9HDRCTL = 0
          hdrstate = 0
          X9STATUS(10)=IAND(X9STATUS(10),-3)     ! flag "database closed"
        ENDIF
      ENDIF
      GOTO 100
C.......................................................................
C
 200  CONTINUE
C
C     Record Control Section
C     ----------------------
C     Controls the recording process when a request to record a phrase
C     is active.  Signals the ReceiveVoiceData process to fill buffers
C     assigned to the recording channel.  When a buffer is full, DVRECCTL
C     writes the buffer and updates the record map.
C
C
C --               check each channel for an active record request
C
      chn = rchn
      DO WHILE( chn .GT. 0 )
        IF( X9RREQ(chn) .GT. 0 )THEN
C
C ----             initialize ptrs and flags for recording buffers
C
          IF( X9RBUF(chn) .LE. 0 )THEN
            IF( PhrCnt(X9RREQ(chn)) .GT. 0 )THEN
              X9RERR(chn) = E_EXIST      ! cannnot record over existing phr
              X9RREQ(chn) = 0
            ELSE
              DO buf = 1, NRBUFS
                X9RRDY(buf,chn) = .TRUE.
              ENDDO
              X9RPKTP(chn) = 9
              X9RPTR(chn) = 1
              X9RCUR(chn) = 1
              X9RBUF(chn) = 1
              X9REND(chn) = .FALSE.
              X9RSTOP(chn) = .FALSE.
              X9CHNACT(chn) = YTSIMTM
            ENDIF
          ENDIF
C
          IF( X9RBUF(chn) .GT. 0 )THEN
            RFACTRE(chn) = .NOT.X9RSTOP(chn)
C
C ----             if end of recording then request a write for
C                  the current buffer
C
C+FM  17-Oct-91 / bowness / don't wait until audio data stops coming,
C                           end recording as soon as stop requested.
C           IF( X9REND(chn) )THEN
C             X9RPUT(chn) = X9RBUF(chn)
C           ENDIF
C-FM  17-Oct-91
C
            IF( X9RSTOP(chn) )THEN
              X9RPUT(chn) = X9RBUF(chn)
              X9REND(chn) = .TRUE.
            ENDIF
C
C ----             process a request to write a buffer
C
            IF( X9RPUT(chn) .GT. 0 )THEN
              phr = X9RREQ(chn)
              rec = ALLOCREC()
              IF( rec .LE. 0 )THEN
                X9RERR(chn) = E_DFULL
                X9RPTR(chn) = SZREC
                X9RSTOP(chn) = .TRUE.
              ELSE
                buf = X9RPUT(chn)
                rchn = 0               ! signal end of channel loop
CVAX
CVAX            blknum = 1 + ((rec-1) * NRRBLK)
CVAX            CALL NBLKIORW( %VAL(DatDcb),
CVAX .                         X9BUFFER(1,buf,chn),
CVAX .                         %VAL(blknum),%VAL(2*SZREC),
CVAX .                         rrstat(buf,chn),%VAL(IO$_WRITEVBLK),
CVAX .                         datsp,datrdy,
CVAX .                         %VAL(astadr(buf,chn)),rcrdast )
CVAXEND
CIBM
                datrdy = 0
                X9RRDY(buf,chn) = 0
                blknum = (rec-1) * NRRBLK
                amnt = 2 * SZREC
                posn = 0
                CALL CAE_IO_WRITE( datrdy, X9RRDY(buf,chn),
     .                             %VAL(DatDcb),
     .                             %VAL(2*SZBLK),
     .                             X9BUFFER(1,buf,chn),
     .                             blknum, amnt, posn )
                IF( datrdy .NE. 1 )THEN
                  CONTINUE ! error from call to write rtn
                ENDIF
CIBMEND
                X9RPUT(chn) = 0
                PhrLast(phr) = rec
                PhrCnt(phr) = 1 + PhrCnt(phr)
                IF( PhrCnt(phr) .EQ. 1 )THEN
                  PhrFirst(phr) = rec
                  PhrSptr(phr) = 1
                ELSE
                  Rnext(X9RREC(chn)) = rec
                ENDIF
                IF( X9REND(chn) )THEN
                  PhrEptr(phr) = X9RPTR(chn)
                  IF( PhrEptr(phr) .LT. 100 ) PhrEptr(phr) = 100
                ENDIF
                X9RREC(chn) = rec
              ENDIF
C
              IF( X9REND(chn) )THEN
                X9HDRCTL = WRITE
                X9RREQ(chn) = 0
                X9RBUF(chn) = 0
              ENDIF
            ENDIF
          ENDIF
        ENDIF
C
        IF( rchn .EQ. 0 )THEN
          rchn = chn
          chn = 0
        ELSE
          chn = chn + 1
          IF( chn .GT. NRCHNS ) chn = 1
          IF( chn .EQ. rchn   ) chn = 0
        ENDIF
      ENDDO
C.......................................................................
C
 300  CONTINUE
C
C     Playback Control Section
C     ------------------------
C     A request to play a message is made by depositing in X9PREQ
C     the number of phrases in the message.  X9PLIS contains the
C     list of phrase numbers to be played.
C
C --               check for an active play request on current channel
C
      chn = pchn
      DO WHILE( chn .GT. 0 )
        IF( X9PREQ(chn) .GT. 0 )THEN
C
C ----             If start of a message playback, make request to fill
C                  DV data buffers, init active buffer ptr, reset play
C                  list and next record ptrs, clear play end/stop flags,
C                  clear buffer ready flags, and set play active flag.
C
          IF( X9PBUF(chn) .LE. 0 )THEN
            X9PGET(chn) = (2 * NRBUFS) - 1       ! request to fill all bufs
            X9PBUF(chn) = 1
            X9PPTR(chn) = PhrSptr( X9PLIS(1,chn) )
            X9PSAV(chn) = X9PPTR(chn)
            X9PPOS(chn) = 0
            X9PREC(chn) = 0
            X9PEND(chn) = .FALSE.
            X9PSTOP(chn) = .FALSE.
            DO buf = 1, NRBUFS
              X9PRDY(buf,chn) = 0
            ENDDO
            X9CHNACT(chn) = YTSIMTM
            RFACTPL(chn) = .TRUE.
          ENDIF
C
C ----             Process a stop playback request or an end of playback
C
          IF( X9PSTOP(chn) )THEN
            X9PREQ(chn) = 0
            X9PBUF(chn) = 0
            X9PGET(chn) = 0
            RFACTPL(chn) = 0
          ENDIF
C
C ----             Process request for a buffer of DV data
C
          IF( X9PGET(chn) .GT. 0 )THEN
C
C ------           Get ptr to buffer which should be filled.
C                  If start of playback, fill all buffers (one per iter)
C
            buf = X9PGET(chn)
            X9PGET(chn) = 0
            IF( buf .GT. NRBUFS )THEN
              buf = (2*NRBUFS) - buf
              X9PGET(chn) = (2*NRBUFS) - buf - 1
            ENDIF
C
C ------           If end of phrase then, either begin playing next phrase
C                  in list, or signal end of message playback
C
            DO WHILE( X9PREC(chn) .EQ. 0 )
              IF( X9PPOS(chn) .GE. X9PREQ(chn) )THEN
                X9PEND(chn) = .TRUE.
                X9PREC(chn) = -1
              ELSE
                X9PPOS(chn) = X9PPOS(chn) + 1
                X9PREC(chn) = PhrFirst(X9PLIS(X9PPOS(chn),chn))
              ENDIF
            ENDDO
C
C ------           Process request to read a record: queue read then
C                  set ptr to next record.  If phrase's last record
C                  was read then buffer may not be full.
C
            IF( X9PREC(chn) .GT. 0 )THEN
              rec = X9PREC(chn) - 1
              pchn = 0                 ! signal end of channel loop
              IF( buf.LT.1 .OR. buf.GT.2 )THEN
                ! X9STATUS(49)=1+X9STATUS(49) !'dbg bad buf value
              ELSE IF( chn.LT.1 .OR. chn.GT.10 )THEN
                ! X9STATUS(48)=1+X9STATUS(48) !'dbg bad chn value
              ELSE
CVAX
CVAX            blknum = 1 + (rec * NRRBLK)
CVAX            CALL NBLKIORW( %VAL(DatDcb),
CVAX .                       X9BUFFER(1,buf,chn),
CVAX .                       %VAL(blknum),%VAL(2*SZREC),
CVAX .                       prstat(buf,chn),%VAL(IO$_READVBLK),
CVAX .                       datsp,datrdy,
CVAX .                       %VAL(astadr(buf,chn)),playast )
CVAXEND
CIBM
                datrdy = 0
                blknum = rec * NRRBLK
                amnt = 2 * SZREC
                posn = 0
                CALL CAE_IO_READ( datrdy, X9PRDY(buf,chn),
     .                            %VAL(DatDcb),
     .                            %VAL(2*SZBLK),
     .                            X9BUFFER(1,buf,chn),
     .                            blknum, amnt, posn )
                IF( datrdy .NE. 1 )THEN
                  CONTINUE ! error from call to readrtn
                ENDIF
CIBMEND
                phr = X9PLIS(X9PPOS(chn),chn)
                IF( X9PREC(chn) .EQ. PhrLast(phr) )THEN
                  X9PBUFEP(buf,chn) = PhrEptr(phr)
                ELSE
                  X9PBUFEP(buf,chn) = SZREC
                ENDIF
                IF( X9PREC(chn) .EQ. PhrFirst(phr) )THEN
                  X9PBUFSP(buf,chn) = PhrSptr(phr)
                ELSE
                  X9PBUFSP(buf,chn) = 1
                ENDIF
                X9PREC(chn) = Rnext(X9PREC(chn))
C
                IF( X9PBUFSP(buf,chn) .GT. X9PBUFEP(buf,chn) )THEN
                  X9PBUFSP(buf,chn) = 1
                  X9PBUFEP(buf,chn) = SZREC
                  ! X9STATUS(47)=1+X9STATUS(47) !'dbg cnt strt/end ptr errors
                ENDIF
              ENDIF
            ENDIF
          ENDIF
        ENDIF
C
        IF( pchn .EQ. 0 )THEN
          pchn = chn
          chn = 0
        ELSE
          chn = chn + 1
          IF( chn .GT. NRCHNS ) chn = 1
          IF( chn .EQ. pchn   ) chn = 0
        ENDIF
      ENDDO
C.......................................................................
C
 400  CONTINUE
C
C     Maintenance Control Section
C     ---------------------------
C     Process maintenance commands.
C
C
      GOTO(
     .      4001   ! delete phrase
     .,     4002   ! number of free records
     .,     4003   ! phrase length (number of records)
     .,     4004   ! phrase inventory
     .,     4005   ! copy phrase
     .,     4006   ! edit phrase trailing edge
     .,     4007   ! edit phrase leading edge
     .)X9MLIST(1)
C
      GOTO 4999
C
C --               Delete Phrase:  Link the phrase's record list to the
C                  front of the free records list.  Add the phrase's
C                  record count to the free count.  If all records have
C                  been freed, re-initialize all the lists.  Finally,
C                  request a header write.
C
 4001 CONTINUE
      phr = X9MLIST(2)
      IF( PhrCnt(phr) .LE. 0 )THEN
        X9MLIST(3) = 0
      ELSE
        X9MLIST(3) = PhrCnt(phr)
        Rnext(PhrLast(phr)) = FrFirst
        FrFirst = PhrFirst(phr)
        FrCnt = FrCnt + PhrCnt(phr)
        IF( FrCnt .LT. MAXREC )THEN
          PhrFirst(phr) = 0
          PhrLast(phr) = 0
          PhrCnt(phr) = 0
        ELSE
          DO rec = 1, MAXREC
            Rnext(rec) = rec + 1
          ENDDO
          Rnext(MAXREC) = 0
          FrCnt = MAXREC
          FrFirst = 1
          FrLast = MAXREC
          DO phr = 1, MAXPHR
            PhrCnt(phr) = 0
            PhrFirst(phr) = 0
            PhrLast(phr) = 0
            PhrSptr(phr) = 1
            PhrEptr(phr) = SZREC
          ENDDO
        ENDIF
        X9HDRCTL = WRITE
      ENDIF
      X9MLIST(1) = -1
      GOTO 4999
C
C --               Get number of free records.
C
 4002 CONTINUE
      X9MLIST(2) = FrCnt
      X9MLIST(1) = -2
      GOTO 4999
C
C --               Phrase Record List:  Traverse the phrase's record
C                  list, copying record numbers to X9MLIST.
C
 4003 CONTINUE
      phr = X9MLIST(2)
      X9MLIST(3) = PhrCnt(phr)
      X9MLIST(4) = PhrSptr(phr)
      X9MLIST(5) = PhrEptr(phr)
      rec = PhrFirst(phr)
      mai = 6
      DO WHILE( rec.GT.0 .AND. mai.LE.50 )
        X9MLIST(mai) = rec
        rec = Rnext(rec)
        mai = mai + 1
      ENDDO
      X9MLIST(1) = -3
      GOTO 4999
C
C --               Phrase Inventory:  If given start phrase is zero then
C                  just get a count of active phrases.  Otherwise get a list
C                  of active phrase number >= the given start phrase.  The
C                  list can be no longer than 45 (due to size of X9MLIST).
C
 4004 CONTINUE
      X9MLIST(3) = 0
      IF( X9MLIST(2) .LE. 0 )THEN
        DO phr = 1, MAXPHR
          IF( PhrCnt(phr) .GT. 0 ) X9MLIST(3) = X9MLIST(3) + 1
        ENDDO
      ELSE
        mai = 5
        DO phr = X9MLIST(2), MAXPHR
          IF( PhrCnt(phr) .GT. 0 )THEN
            mai = mai + 1
            X9MLIST(mai) = phr
            IF( mai .GE. 50 ) GOTO 4994
          ENDIF
        ENDDO
 4994   CONTINUE
        X9MLIST(3) = mai - 5
      ENDIF
      X9MLIST(1) = -4
      GOTO 4999
C
C --               Copy Phrase:  Call COPYDAT routine to make a copy
C                  a phrase's records.  Since this routine uses asynchronous
C                  I/O, it must be called many times before the copying
C                  completes.
C
 4005 CONTINUE
      IF( band1 ) cpystate = -1 ! COPYDAT( X9MLIST(2), X9MLIST(3) )
      IF( cpystate .NE. 0 )THEN
        X9MLIST(4) = cpystate
        IF( cpystate .GT. 0 ) X9HDRCTL = WRITE
        X9MLIST(1) = -5
      ENDIF
      GOTO 4999
C
C --               Edit Trailing Edge
C
 4006 CONTINUE
      phr = X9MLIST(2)
      PhrEptr(phr) = PhrEptr(phr) + X9MLIST(3)
C
      IF( PhrEptr(phr) .GT. SZREC )THEN
        mai = ALLOCREC()
        IF( mai .LE. 0 )THEN
          X9MLIST(4) = -1
          PhrEptr(phr) = SZREC
        ELSE
          Rnext(PhrLast(phr)) = mai
          PhrLast(phr) = mai
          PhrEptr(phr) = X9MLIST(3)
        ENDIF
C
      ELSE IF( PhrEptr(phr) .LE. 0 )THEN
        IF( PhrCnt(phr) .EQ. 1 )THEN
          PhrEptr(phr) = 32
        ELSE
          Rnext(PhrLast(phr)) = FrFirst
          FrFirst = PhrLast(phr)
          FrCnt = FrCnt + 1
          mai = PhrFirst(phr)
          DO WHILE( Rnext(mai) .NE. FrFirst )
            mai = Rnext(mai)
          ENDDO
          Rnext(mai) = 0
          PhrLast(phr) = mai
          PhrEptr(phr) = SZREC
        ENDIF
      ENDIF
      X9MLIST(1) = -6
      GOTO 4999
C
C --               Edit Leading Edge
C
 4007 CONTINUE
      phr = X9MLIST(2)
      PhrSptr(phr) = PhrSptr(phr) - X9MLIST(3)
C
      IF( PhrSptr(phr) .LE. 0 )THEN
        mai = ALLOCREC()
        IF( mai .LE. 0 )THEN
          X9MLIST(4) = -1
          PhrSptr(phr) = 1
        ELSE
          Rnext(mai) = PhrFirst(phr)
          PhrFirst(phr) = mai
          PhrSptr(phr) = SZREC - X9MLIST(3)
        ENDIF
C
      ELSE IF( PhrSptr(phr) .GT. SZREC )THEN
        IF( PhrCnt(phr) .EQ. 1 )THEN
          PhrSptr(phr) = SZREC - 32
        ELSE
          mai = PhrFirst(phr)
          PhrFirst(phr) = Rnext(mai)
          Rnext(mai) = FrFirst
          FrFirst = mai
          FrCnt = FrCnt + 1
          PhrSptr(phr) = 1
        ENDIF
      ENDIF
      X9MLIST(1) = -7
      GOTO 4999
C
 4999 CONTINUE
      band1 = .NOT.band1
      RETURN
      END
C.......................................................................
C
      INTEGER FUNCTION ALLOCREC()
C     -------------------------
C'Purpose
C     Get a ptr to the next record on the free record list and
C     remove that record from the free record list.
C'
      IMPLICIT NONE
      INCLUDE 'usd8x9da.par'                ! NOFPC
      INCLUDE 'usd8x9da.inc'                ! NOFPC
C
      IF( FrCnt .GT. 0 )THEN
        ALLOCREC = FrFirst
        FrFirst = Rnext(ALLOCREC)
        FrCnt = FrCnt - 1
        Rnext(ALLOCREC) = 0
      ELSE
        ALLOCREC = 0
      ENDIF
      RETURN
      END
C.......................................................................
C
      INTEGER FUNCTION COPYDAT(from,to)
C     ------------------------
C'Purpose
C     To copy DV data for one recording.  The source or the target
C     record number is positive, the data is moved from or to the
C     DV-DAS database.  A negative source/target record number causes
C     the data to be taken/put from/to a DVxxxx.DAT file.
C     The copying is done using asynchronous I/O.
C
      IMPLICIT NONE
C
C'Output
C     While the copying is in progress, the function returns zero.
C     When copying is finished, the number of records copied is returned.
C     If an error occurs, a negative number is returned.
C
C'Inputs
      INTEGER*2
     .    from               ! source record number
     .,   to                 ! target record number
C
C'Include_files
      INCLUDE 'usd8x9da.par'                ! NOFPC
      INCLUDE 'usd8x9da.inc'                ! NOFPC
CVAX
CVAX  INCLUDE '($IODEF)/NOLIST'                     ! NOFPC
CVAXEND
C
C'Functions
      INTEGER*4
     .    ALLOCREC
C
C'Local_variables
      INTEGER*4
     .    src_dcb
     .,   src_rdy
     .,   src_ios
     .,   src_blk
     .,   src
     .,   src_id
     .,   tgt_dcb
     .,   tgt_rdy
     .,   tgt_ios
     .,   tgt_blk
     .,   tgt
     .,   tgt_id
     .,   blknum
     .,   x, r
     .,   state /1/
      INTEGER*2
     .    src_cnt
     .,   src_sptr
     .,   src_eptr
     .,   src_hdr(SZBLK)
     .,   record(SZREC)
      LOGICAL*1
     .    src_file
     .,   tgt_file
      CHARACTER
     .    version*5
     .,   src_name*16 /'dv0000.dat'/
     .,   src_sp*256
     .,   tgt_name*16 /'dv0000.dat'/
     .,   tgt_sp*256
      EQUIVALENCE
     .   (src_hdr(1), src_cnt)
     .,  (src_hdr(2), src_sptr)
     .,  (src_hdr(3), src_eptr)
C
      COPYDAT = 0
      GOTO(
     .    100,     ! init control variable, open source/target files
     .    200,     ! get source header info
     .    300,     ! write target header or get records for free list
     .    400,     ! read a source DV data record
     .    500,     ! write a target DV data record
     .    600      ! close source/target files
     .) state
      COPYDAT = -1
      GOTO 999
C
C --               Initialize the source and target information; and
C                  if required, open the source and/or target files
C
 100  CONTINUE
      src_ios = 1
      src_rdy = 1
      tgt_ios = 1
      tgt_rdy = 1
C
      IF( from .LT. 0 )THEN
        WRITE( src_name(3:6), '(I4.4)') -from
        src = 1
        src_blk = 2 - NRRBLK
        src_file = .TRUE.
      ELSE IF( from .GT. 0 )THEN
        IF( PhrCnt(from) .LE. 0 )THEN
          COPYDAT = -16
        ELSE
          src_dcb = DatDcb
          src = PhrFirst(from)
          src_cnt = PhrCnt(from)
          src_sptr = PhrSptr(from)
          src_eptr = PhrEptr(from)
          src_blk = 1 - NRRBLK
        ENDIF
        src_file = .FALSE.
      ELSE
        COPYDAT = -11
        src_dcb = -1
        src_file = .FALSE.
      ENDIF
C
      tgt = to
      IF( tgt .EQ. 0 ) tgt = -from
C
      IF( tgt .LT. 0 )THEN
        WRITE( tgt_name(3:6), '(I4.4)') -tgt
        tgt_blk = 2 - NRRBLK
        tgt_file = .TRUE.
      ELSE
        IF( PhrCnt(tgt) .GT. 0 )THEN
          COPYDAT = -17
          src_file = .FALSE.
        ELSE
          tgt_dcb = DatDcb
          tgt_blk = 1 - NRRBLK
        ENDIF
        tgt_file = .FALSE.
      ENDIF
C
      IF( src_file )THEN
        x = 2*SZBLK
CVAX
CVAX    CALL NBLKOPEN( src_id, src_name, x, 'OLD', r,
CVAX .                 version, src_ios, src_rdy, )
CVAXEND
      ENDIF
C
      IF( tgt_file )THEN
        x = 2*SZBLK
        r = 1 + (src_cnt * NRRBLK)
CVAX
CVAX    CALL NBLKOPEN( tgt_id, tgt_name, x, 'NEW', r,
CVAX .                 version, tgt_ios, tgt_rdy, )
CVAXEND
      ENDIF
      state = 2
      GOTO 999
C
C --               Verify that the source file is properly opened then
C                  if the source is not the DV-DAS database, read the
C                  source file header
C
 200  CONTINUE
      IF( src_rdy )THEN
        IF( src_ios .NE. 1 )THEN
          COPYDAT = -21
          src_file = .FALSE.
        ELSE IF( src_file )THEN
CVAX
CVAX      CALL NBLKDCB(src_id,src_dcb,src_ios)
CVAXEND
          IF( src_ios .NE. 1 )THEN
            COPYDAT = -22
          ELSE
            blknum = 1
CVAX
CVAX        CALL NBLKIORW( %VAL(src_dcb), src_hdr,
CVAX .                     %VAL(blknum), %VAL(2*SZBLK),
CVAX .                     src_ios, %VAL(IO$_READVBLK),
CVAX .                     src_sp, src_rdy, , )
CVAXEND
          ENDIF
        ENDIF
        state = 3
      ENDIF
      GOTO 999
C
C --               Verify that the target file is properly opened and
C                  the source header inforation is available.  Then if
C                  the target is not the DV-DAS database, get the target
C                  DCB and write the source header to the target file.
C                  Otherwise (if the target IS the DV-DAS database) get
C                  records from the free list and update the DV-DAS
C                  header.
C
 300  CONTINUE
      IF( tgt_rdy .AND. src_rdy )THEN
        IF( tgt_ios .NE. 1 )THEN
          COPYDAT = -31
          tgt_file = .FALSE.
        ELSE IF( src_ios .NE. 1 )THEN
          COPYDAT = -32
          tgt_file = .FALSE.
        ELSE IF( tgt_file )THEN
CVAX
CVAX      CALL NBLKDCB(tgt_id,tgt_dcb,tgt_ios)
CVAXEND
          IF( tgt_ios .NE. 1 )THEN
            COPYDAT = -33
          ELSE
            tgt = 1
            blknum = 1
CVAX
CVAX        CALL NBLKIORW( %VAL(tgt_dcb), src_hdr,
CVAX .                     %VAL(blknum), %VAL(2*SZBLK),
CVAX .                     tgt_ios, %VAL(IO$_WRITEVBLK),
CVAX .                     tgt_sp, tgt_rdy, , )
CVAXEND
          ENDIF
        ELSE IF( src_cnt .GT. FrCnt )THEN
          COPYDAT = -34
        ELSE
          PhrCnt(tgt) = src_cnt
          PhrSptr(tgt) = src_sptr
          PhrEptr(tgt) = src_eptr
          r = ALLOCREC()
          PhrFirst(tgt) = r
          DO x = 2, PhrCnt(tgt)
            Rnext(r) = ALLOCREC()
            r = Rnext(r)
          ENDDO
          PhrLast(tgt) = r
          tgt = PhrFirst(tgt)
        ENDIF
        state = 4
      ENDIF
      GOTO 999
C
C --               Verify that the previous target output completed
C                  properly then read the next source DV data record.
C
 400  CONTINUE
      IF( tgt_rdy )THEN
        IF( tgt_ios .NE. 1 )THEN
          COPYDAT = -41
        ELSE
          blknum = src_blk + (src * NRRBLK)
CVAX
CVAX      CALL NBLKIORW( %VAL(src_dcb), record,
CVAX .                   %VAL(blknum), %VAL(2*SZREC),
CVAX .                   src_ios, %VAL(IO$_READVBLK),
CVAX .                   src_sp, src_rdy, , )
CVAXEND
          IF( src_file )THEN
            src = src + 1
          ELSE
            src = Rnext(src)
          ENDIF
        ENDIF
        state = 5
      ENDIF
      GOTO 999
C
C --               Verify that the previous source input completed
C                  properly then write the current DV data record.
C
 500  CONTINUE
      IF( src_rdy )THEN
        state = 4
        IF( src_ios .NE. 1 )THEN
          COPYDAT = -51
        ELSE
          blknum = tgt_blk + (tgt * NRRBLK)
CVAX
CVAX      CALL NBLKIORW( %VAL(tgt_dcb), record,
CVAX .                   %VAL(blknum), %VAL(2*SZREC),
CVAX .                   tgt_ios, %VAL(IO$_WRITEVBLK),
CVAX .                   tgt_sp, tgt_rdy, , )
CVAXEND
          IF( tgt_file )THEN
            tgt = tgt + 1
            IF( tgt .GE. src_cnt ) state = 6
          ELSE
            tgt = Rnext(tgt)
            IF( tgt .LE. 0 ) state = 6
          ENDIF
        ENDIF
      ENDIF
      GOTO 999
C
C --               Copy complete:  If required, close the source and/or
C                  target files then set return code to the number of
C                  DV data records copied
C
 600  CONTINUE
      IF( tgt_rdy )THEN
        IF( src_file )THEN
          src_file = .FALSE.
CVAX
CVAX      CALL NBLKIOC( %VAL(src_dcb), src_ios,
CVAX .                  %REF(src_sp), src_rdy, , )
CVAXEND
        ENDIF
        IF( tgt_file )THEN
          tgt_file = .FALSE.
CVAX
CVAX      CALL NBLKIOC( %VAL(tgt_dcb), tgt_ios,
CVAX .                  %REF(tgt_sp), tgt_rdy, , )
CVAXEND
        ENDIF
        IF( COPYDAT .EQ. 0 ) COPYDAT = src_cnt
        state = 1
      ENDIF
      GOTO 999
C
C --               Before returning, if an error occurred, close
C                  the source and/or target files as required.
C
 999  CONTINUE
      IF( COPYDAT .LT. 0 )THEN
        IF( src_file ) GOTO 600
        IF( tgt_file ) GOTO 600
        state = 1
      ENDIF
      RETURN
      END
C.......................................................................
CVAX
CVAX  SUBROUTINE DVASTS
CVAX  -----------------                                                   \C
CVAX  IMPLICIT NONE
CVAX  DVSTD  X9PRDY, X9RRDY                                               \CP
CVAX  Inserted by CAE Fortran Pre-Processor Revision 7.0  on  3-MAR-1992 2\C+---
CVAX                                                                      \C$
CVAX  Labels Access Files :                                               \C$
CVAX                                                                      \C$
CVAX  DUB1:[BOWNESS.DV30.CDB]DVSTD.XSL;9                                  \C$@
CVAX                                                                      \C$
CVAX  CDB inputs taken from DUB1:[BOWNESS.DV30.CDB]DVSTD.XSL;9        XRFT\C$
CVAX                                                                      \C$
CVAX  INTEGER*4
CVAX &  X9PRDY(2,10)     ! VOICE DATA BLOCK INPUT COMPLETE
CVAX &, X9RRDY(2,10)     ! VOICE DATA BLOCK OUTPUT COMPLETE
CVAX                                                                      \C$
CVAX  LOGICAL*1
CVAX &  DUM0000001(166476)
CVAX                                                                      \C$
CVAX  COMMON   /XRFTEST   /
CVAX &  DUM0000001,X9RRDY,X9PRDY
CVAX ---------------------------------------------------------------------\C----
CVAX  INTEGER play
CVAX                                                                      \C
CVAX  ENTRY ASTC1B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,1) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,1) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC1B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,1) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,1) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC2B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,2) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,2) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC2B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,2) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,2) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC3B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,3) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,3) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC3B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,3) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,3) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC4B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,4) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,4) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC4B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,4) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,4) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC5B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,5) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,5) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC5B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,5) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,5) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC6B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,6) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,6) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC6B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,6) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,6) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC7B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,7) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,7) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC7B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,7) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,7) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC8B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,8) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,8) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC8B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,8) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,8) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC9B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,9) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,9) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC9B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,9) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,9) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC10B1(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(1,10) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(1,10) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  ENTRY ASTC10B2(play)
CVAX  IF( play .NE. 0 )THEN
CVAX    X9PRDY(2,10) = .TRUE.
CVAX  ELSE
CVAX    X9RRDY(2,10) = .TRUE.
CVAX  ENDIF
CVAX  RETURN
CVAX                                                                      \C
CVAX  END
CVAXEND
