#!  /bin/csh -f
#!  $Revision: CDS_BLD - Build the spare files V1.0 (DF) Feb-92$
#!
#! &
#! @$.
#! &*.XSS
#! &*.NLB
#! &$.XSL
#! &$?.XSL
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/cdsl_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/cdsm_$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set FSE_LINE="`sed -n 1p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
#
set FSE_DATA="`echo '$FSE_LINE' | cut -c4-`"
set lcount=2
FSE_BUILD_LOOP:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  echo "$FSE_FILE" >>$FSE_LIST
  goto FSE_BUILD_LOOP
FSE_BUILD_FULL:
#
set FSE_LINE="`sed -n 1p $argv[3]`"
set FSE_LINE="`echo '$FSE_LINE' | cut -c4-`"
set FSE_LINE="`cvfs $FSE_LINE`"
set FSE_LINE="`norev $FSE_LINE`"
set FSE_LINE="$FSE_LINE:t"
set FSE_CDB="$FSE_LINE:r"
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias cdbs
cdbs -simex "$FSE_CDB.cdb"
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if ($stat != 0)  exit
if (! -e "$FSE_MAKE") then
  echo "0MBTOX $FSE_CDB.XEQ" >>$argv[4]
  exit
endif
#
#
set NEW_XEQ=""
foreach FSE_LINE ("`grep ' Generated  ' $FSE_MAKE`")
  set FSE_FILE="`echo '$FSE_LINE' | cut -c13-`"
  set FSE_TEST="`norev $FSE_FILE`"
  set FSE_TEST="$FSE_TEST:t"
  set FSE_TYPE="$FSE_TEST:e"
#
  if ("$FSE_TYPE" == "xss") then
    echo "0MRBOX $FSE_FILE" >>$argv[4]
  else if ("$FSE_TYPE" == "nlb") then
    echo "0MRTTX $FSE_FILE" >>$argv[4]
  else if ("$FSE_TYPE" == "xeq") then
    set NEW_XEQ="$FSE_FILE"
  endif
end
#
if ("$NEW_XEQ" == "") then
  echo "0MBBOX $FSE_CDB.XEQ" >>$argv[4]
else
  echo "0MRTTX $NEW_XEQ" >>$argv[4]
endif
#
rm $FSE_MAKE
exit
