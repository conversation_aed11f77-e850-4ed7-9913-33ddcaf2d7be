#!  /bin/csh -f
#!  $Revision: SCS_ENT - Enter/Extract an SCSGEN Data File V1.1 (MT) May-91$
#!
#! &
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
if ( "$argv[1]" == "Y" ) then
  set echo
  set verbose
endif
if  ! ("$argv[2]" == "ENTER" || "$argv[2]" == "EXTRACT") exit
set argv[3]="`revl '-$argv[3]' `"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
#
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
if ("$argv[2]" == "ENTER") then
  set FSE_FILE="`cvfs '$FSE_FILE'`"
  set FSE_SAVE=$SIMEX_DIR/enter/$FSE_FILE:t
  setenv fse_select "$FSE_FILE"
  setenv fse_source "$FSE_SAVE"
  setenv fse_target "$argv[4]"
  setenv fse_action "S"
  fse_enter
endif
#
if ("$argv[2]" == "EXTRACT") then
  echo "0ECTSD $FSE_FILE" > "$argv[4]"
endif
#
exit

