C
C   DMCUNIX.INC: This include files contains UNIX specific constants
C
C
C      CHARACTER*80 RVLSTR_DMCSPEC_INC 
C     . /'$Revision: dmcspec.inc V1.0 26-Feb-92 $'/
      LOGICAL*4      MAPOK     / .FALSE. /
      INTEGER*4      ETH_HEADSIZE            ! Ethernet header size (Bytes)
      PARAMETER     (ETH_HEADSIZE = 14)
      INTEGER*4      CAESIZE                 ! CAE Header size
      PARAMETER     (CAESIZE = 16)
      INTEGER*4      OUTSIZE                 ! Maximum output data size
      PARAMETER     (OUTSIZE = 1500 - CAESIZE)
C
      INTEGER*2      DMC_Num   / 'FFFF'X /   ! DMC number that appear
                                             ! in the CAEHeader
      INTEGER*2      DMC_Swap  / 'FF00'X/    ! DMC Swap bit
C
      INTEGER*4      MultiCast(2)            ! Multi cast code
      INTEGER*4      LocalCast(2)            ! Local cast code
      INTEGER*2      MC(4)                   !
      INTEGER*2      LC(4)                   !
      EQUIVALENCE   (MC(1),MultiCast(1))     !
      EQUIVALENCE   (LC(1),LocalCast(1))     !
C
      INTEGER*4 DMCTAG(64)                   ! 64 <-- DMCNUM
      INTEGER*2 SSAPDSAP /'0000'X/           !
C
C     INTEGER*1 is not Fortran standard.
C
      INTEGER*4      MAX4INP                 !
      INTEGER*1      MAXINP(4)               ! Number of STD O/P per EC
      EQUIVALENCE   (MAX4INP,MAXINP(1))      !
C
      CHARACTER*6    CETHADDR                !
      INTEGER*1      I1ETHADDR(6)            !
      INTEGER*2      I2ETHADDR(3)            !
      EQUIVALENCE   (CETHADDR,I1ETHADDR(1))  !
      EQUIVALENCE   (CETHADDR,I2ETHADDR(1))  !
C
      DATA DMCTAG/   X'30313043',X'30323043',X'30333043'
     .,              X'30343043',X'30353043',X'30363043'
     .,              X'30373043',X'30383043',X'30393043'
     .,              X'30413043',X'30423043',X'30433043'
     .,              X'30443043',X'30453043',X'30463043'
     .,              X'30303143',X'30313143',X'30323143'
     .,              X'30333143',X'30343143',X'30353143'
     .,       43*0 /
C
C     Set multicast address
C
      DATA MC(1) / 'FFFF'X / ! FF
      DATA MC(2) / 'FF43'X / ! FC
      DATA MC(3) / '4D44'X / ! MD
C
C     Set local cast address
C
      DATA LC(1) / 'FFFF'X / ! FF
      DATA LC(2) / 'FF43'X / ! FC
      DATA LC(3) / '4F4C'X / ! OL

