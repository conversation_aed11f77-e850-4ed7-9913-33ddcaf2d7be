C***********************************************************************
C
C'Title                NOSEWHEEL STEERING SYSTEM
C'Module_ID            USD8CN
C'Entry_point          CNOSEWHL
C'Documentation
C'Customer             USAIR
C'Application          Simulation of DHC8-100/100A/300A nose gear system
C'Author               <PERSON>'Date                 June 1991
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       33 msec
C'Process              Synchronous process
C
C***********************************************************************
C
C'Revision_history
C
C  changes for NWS switch logic after update Roy 22Jan13
C
C  usd8cn.for.27 17Feb2012 04:13 usd8 tom
C       < asdk fasdkf kasd flaksd flkas dfl;aksd f >
C
C  usd8cn.for.26 24Dec2011 00:55 usd8 Tom
C       < Corrected logic for operation after landing. >
C       < revised Roy 09/19/2012 >
C
C  usd8cn.for.25 11Aug1993 18:36 usd8 steve
C       < added trip level due to large >
C
C  usd8cn.for.24 10Jun1992 02:00 usd8 sbw
C       < added negative tiller label (CNTILLER) >
C
C  usd8cn.for.23 30Apr1992 07:48 usd8 sbw
C       < added handle deadband >
C
C  usd8cn.for.22 17Apr1992 21:43 usd8 sbw
C       < added atg flag >
C
C  usd8cn.for.21  4Apr1992 00:08 usd8 sbw
C       < stopped CNPL calc for flight freeze >
C
C  usd8cn.for.20  4Mar1992 05:48 usd8 sbw
C       < tuned pedal to cnws slope >
C
C  usd8cn.for.17 21Feb1992 07:35 usd8 changed
C       < changed sign on pedal sterring >
C
C File: /cae1/ship/usd8cn.for.14
C       Modified by: sbw
C       Mon Nov 25 17:09:40 1991
C       < added vmzgp index >
C
C File: /cae1/ship/usd8cn.for.12
C       Modified by: SBW
C       Mon Nov 25 17:06:45 1991
C       < ADDED VDUC >
C
C File: /cae1/ship/usd8cn.for.11
C       Modified by: INSTALLING ON USD8
C       Mon Nov 25 17:04:08 1991
C       < SBW >
C
CC'References
CC
CC  [1] Dash 8 Series 100 Maintenance Manual ATA 32, Feb 1989.
CC
CC  [2] Dash 8 Series 100 Operating Data, Mar 1989.
CC
CC  [3] Dash 8 Series 300 Maintenance Manual ATA 32, AUG 1990.
CC
CC  [4] Dash 8 Series 300 Operating Data, Sep 1989.
CC
CC  [5] DeHavilland AEROC 8.6.U.1 "NOSEWHEEL STEERING SYSTEM"
CC
CC  [6] B757 Ground Handling Model BOEING Doc. D613T120-1101, 19
CC
CC'
CC
CC   Purpose : This module simulates the  nosewheel steering system of  the
CC             DeHavilland Dash-8-100/100A/300A.
CC
CC   Theory : The ground handling capabilities of the aircraft is  provided
CC            by the nosewheel  steering system. Control of the steering is
CC            through an electronic control unit (ECU) with inputs from the
CC            nosewheel control handle or the rudder pedals. The  nosewheel
CC            arming switch is placed to STEERING position to  make  system
CC            active.  With  the  arming switch at OFF, in the event of ECU
CC            failure  or if  the nosewheel position exceeds 60 degree, the
CC            nosewheel assumes castoring. Any malfunctions occurred in the
CC            system are annunciated by means of caution light.
CC
CC   Input : Nosewheel steering  arming switch status,  nosewheel position,
CC           control  handle  position, rudder pedal position, PSEU weight-
CC           on-wheel signal, nose gear up discrete, ground torque on tire,
CC           hydraulic pressure and electrical power.
CC
CC   Output : Nosewheel position, caution light
CC
C
C
C    =================================
C     N O S E W H E E L   M O D U L E
C    =================================
C
C
C    ===================
      SUBROUTINE USD8CN
C    ===================
C
C
      IMPLICIT NONE
C
      INCLUDE 'disp.com'    !NOFPC
C
C     ==============
C     XREF VARIABLES
C     ==============
C
C     HYDRAULICS
C
CPI   USD8    AHP2,
C
C     PROXIMITY SWITCH SENSORS
C
CPI  A   AGFPS17,AGFPS75,AGFPS76,
C
C     CIRCUIT BREAKERS
C
CPI  B   BIRP06,BILB06,
C
C     FLIGHT BACKDRIVE
C
CPI  H   HCNMODE,HNWS,HTIL,CNATGON,
C
C     MALFUNCTIONS
C
CPI  T   TF32061,
C
C     FLIGHT
C
CPI  V   VMZGP,VDUC,TCFFLPOS,
C
C     INTERNAL to NOSEWHEEL
C
CP   C   CIRFPOS,CNLIM,CNZMODE,CNZCMD,CNXV,CNPL,CNRATE,
C
C     OUTPUTS from NOSEWHEEL
C
CPO  C   CNWS, CNHPDMD, CNTILLER,
C
C     NOSEWHEEL FPMC
C
CPO  C   CN$NSTR,
C
C     INTERFACE
C
CPO  I   IACNHND,IDCNSSW
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 23-Jan-2013 13:28:35
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AHP2           ! Hyd pressure node 2                    [psi]
     &, CIRFPOS        ! RUDDER PEDAL FOKKER POSITION            [IN]
     &, CNPL           ! NOSEWHEEL ACTUATOR LOAD PRESSURE      [PSI]
     &, CNRATE         ! NOSEWHEEL POSITION RATE           [DEG/SEC]
     &, CNXV           ! NOSEWHEEL INPUT ERROR                 [DEG]
     &, CNZCMD         ! ECU NOSEWHEEL COMMAND                 [DEG]
     &, HNWS           ! MODE 1 NOSEWHEEL     COMMAND (+RT)     [deg]
     &, HTIL           ! MODE 2 TILLER        COMMAND (+RT)     [deg]
     &, VDUC           ! AVG. GEAR EXT.   1=DN,0=UP
     &, VMZGP(6)       ! YAWING MOMENT ABOUT GEAR TRUCK      [ft*lbs]
C$
      INTEGER*4
     &  HCNMODE        ! NOSEWHEEL  BACKDRIVE MODE
C$
      INTEGER*2
     &  CNZMODE        ! NOSEWHEEL STEERING OPERATIONAL MODE
C$
      LOGICAL*1
     &  AGFPS17        ! PSEU eq17  [A18] NOSEWHEEL STEERING
     &, AGFPS75        ! PSEU eq75  [B40] NOSEWHEEL STEERING
     &, AGFPS76        ! PSEU eq76  [B53] NOSEWHEEL STEERING
     &, BILB06         ! NLG STEER IND               32 PDLMN  DI2056
     &, BIRP06         ! NLG STEER CONT              32 PDRMN  DI2222
     &, CNATGON        ! NOSEWHEEL CTS TEST ON
     &, CNLIM          ! NOSE GEAR 60 DEG LIMIT SW T=ACTUATED
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TF32061        ! NWS ECU
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  CNHPDMD        ! NOSEWHEEL NORMALIZED PCU POSITION [-1 TO 1]
     &, CNTILLER       ! TILLER POSITION (STD SIGN)
     &, CNWS           ! NOSE WHEEL ANGLE                      [DEG]
     &, IACNHND        ! NOSEWHEEL CONTROL HANDLE POSITION     AI028
C$
      LOGICAL*1
     &  CN$NSTR        ! NOSE STEERING CAUTION LIGHT           DO0721
     &, IDCNSSW        ! NOSEWHEEL STEERING SWITCH             DI0349
C$
      LOGICAL*1
     &  DUM0000001(8860),DUM0000002(2707),DUM0000003(1014)
     &, DUM0000004(1116),DUM0000005(3007),DUM0000006(1924)
     &, DUM0000007(3184),DUM0000008(152),DUM0000009(52)
     &, DUM0000010(3924),DUM0000011(5597),DUM0000012(1)
     &, DUM0000013(2),DUM0000014(71960),DUM0000015(569)
     &, DUM0000016(35),DUM0000017(201890),DUM0000018(13711)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,CN$NSTR,DUM0000002,IACNHND,DUM0000003,IDCNSSW
     &, DUM0000004,BIRP06,BILB06,DUM0000005,VDUC,DUM0000006,VMZGP
     &, DUM0000007,HCNMODE,DUM0000008,HNWS,DUM0000009,HTIL,DUM0000010
     &, CIRFPOS,DUM0000011,CNATGON,CNLIM,DUM0000012,CNZMODE,DUM0000013
     &, CNWS,CNZCMD,CNXV,CNPL,CNRATE,CNHPDMD,CNTILLER,DUM0000014
     &, AHP2,DUM0000015,AGFPS17,DUM0000016,AGFPS75,AGFPS76,DUM0000017
     &, TCFFLPOS,DUM0000018,TF32061
C------------------------------------------------------------------------------
C
C     ---------------
C     LOCAL VARIABLES
C     ---------------
C
      LOGICAL*1  cn_first /.TRUE./     ! First pass flag
      LOGICAL*1  cn_zpwr               ! ECU power (T=powered)
      LOGICAL*1  cn_sol                ! Solenoid valve state (T=Energized)
      LOGICAL*1  cn_trip  /.FALSE./    ! NWS trip off (from large comma
      LOGICAL*1  cn_rk1   /.FALSE./    ! Relay K1             (T=Energized)
C     LOGICAL*1  cn_oldssw             ! prev ssw position to reset ECU
C     don't think we need old sw posn any more Roy
C
      REAL*4  cn_tripl/55./               ! NWS trip off level
      REAL*4  cn_ahp2              ! Hyd press system 2                [PSI]
      REAL*4  cn_hand              ! Handle input                      [DEG]
      REAL*4  cn_hnddb/1.0/        ! Handle input                      [DEG]
      REAL*4  cn_pedal             ! Pedal input                       [DEG]
      REAL*4  cn_hcnv/1.25/       ! Handle input to nosewheel angle factor
      REAL*4  cn_pcnv/-2.6/         ! Pedal input to nosewheel angle factor
      REAL*4  cn_tlim/120/         ! Nosewheel travel limit            [DEG]
      REAL*4  cn_zcmdo             ! Old ECU nosewheel cmd             [DEG]
      REAL*4  cn_cmd               ! ECU nosewheel cmd with hysteresis [DEG]
      REAL*4  cn_rudl   /7.0  /    ! Rudder pedal steering limit       [DEG]
      REAL*4  cn_cmdl   /60.0 /    ! Normal nosewheel command limit    [DEG]
      REAL*4  cn_casl   /120.0 /   ! Castoring nosewheel command limit [DEG]
      REAL*4  cn_maxpi  /.0083 /   ! inverse NWS pcu limit            [1/DEG]
      REAL*4  cn_db     /0.000/    ! Input deadband                    [DEG]
      REAL*4  cn_vl     /0.000/    ! Valve leakage                     [DEG]
      REAL*4  cn_sprk   /-1.0 /    ! Centering spring gain       [FT-LB/DEG]
      REAL*4  cn_cast   /1.0  /    ! Castor coefficient                [DEG]
      REAL*4  cn_hyst   /0.0  /    ! Nosewheel hysteresis              [DEG]
      REAL*4  cn_xvl    /5.0  /    ! Input limit                       [DEG]
      REAL*4  cn_kval   /.01/      ! Gain term                 [IN^2/LB-SEC]
      REAL*4  cn_rlim   /30/       ! Nose gear pos rate limit      [DEG/SEC]
      REAL*4  cn_dpxv              ! Delta pressure * cn_xv        [DEG-PSI]
      REAL*4  cn_iarm   /1./       ! Inverse of moment arm            [1/IN]
      REAL*4  cn_ipa    /0.5/       ! Inverse of actuator piston area  [1/IN]
C
C
C     --------------
      ENTRY CNOSEWHL
C     --------------
C
C  -------------------------------
CD CN010 FIRST PASS INITIALIZATION
C  -------------------------------
C
CR CAE Calculation
C
CC This equation may be not necessary.
C
      IF (cn_first) THEN
        cn_first = .FALSE.
      ENDIF
C
C  -----------------------------
CD CN020 ATG NOSEWHEEL BACKDRIVE
C  -----------------------------
C
CR CAE Calculation
CC
CC During the ATG test, the nosewheel position can be backdriven to
CC the desired position. Since the nosewheel control is a fly-by-wire
CC type, the nosewheel control handle cannot be backdriven. However, an
CC equivalent handle signal is simulated and ramped for ATG test purpose.
CC The backdriven position is limited in accord to the travel limits.
CC
CC Backdrive modes : 0 - backdrive off
CC                   1 - backdrive to commanded nosewheel position
CC                   2 - backdrive to commanded equivalent tiller position
CC
      IF (HCNMODE.EQ.1) THEN
        CNWS = HNWS
        IF (CNWS.GT. cn_tlim) CNWS =  cn_tlim
        IF (CNWS.LT.-cn_tlim) CNWS = -cn_tlim
      ELSE IF (HCNMODE.EQ.2) THEN
        cn_hand = HTIL * cn_hcnv
        CNTILLER = (-HTIL)
        IF (cn_hand.GT. cn_cmdl) cn_hand =  cn_cmdl
        IF (cn_hand.LT.-cn_cmdl) cn_hand = -cn_cmdl
      ENDIF
C
C  -----------------------
CD CN030 OPERATIONAL MODES
C  -----------------------
C
CR [1] ATA 32-50-00 p.16, ATA 32-61-50 p.43,56
CR [5] Sect 2.7 Issue 1 p.20
CC
CC There are three operational modes for the nosewheel steering system.
CC They are the steering, steered center and shimmy damped mode. The
CC conditions for the normal steering mode are:
CC
CC      1) aircraft 28Vdc power available,
CC      2) nosewheel steering switch at ON position,
CC      3) nose gear down lock signal from the PSEU,
CC      4) steering limit switch not actuated,
CC      5) system no. 2 hydraulic pressure available,
CC      6) no fault detected by ECU, and
CC      7) weight-on-wheel signals from the PSEU.
CC
CC If only condition (7) is not satified (i.e. in flight), the system
CC goes into the steered center mode. Any other violated conditions
CC lead the system into the shimmy damped mode.
CC
      cn_ahp2 = AHP2                                      ! Hydraulics sys
C
      CNLIM = ABS(CNWS) .GT. cn_tripl                      ! 60 deg limit s
C
C      cn_rk1 = IDCNSSW.AND.CNLIM.AND.(BIRP06.OR.BILB06)   ! Relay K1
C     +         .OR. CNATGON
C
      IF(IDCNSSW.AND.CNLIM.AND.(BIRP06.OR.BILB06).OR.CNATGON) THEN
      cn_rk1 = .TRUE.
      ELSE
      cn_rk1 = .FALSE.
      ENDIF
C
C     ecu power = circuit breakers and switch and proximity switches
C     < I took out the not warning light Roy >
C
C     cn_zpwr = BIRP06.AND.BILB06.AND.IDCNSSW .AND.         ! ECU power logi
C    +          AGFPS75.AND.AGFPS76.AND..NOT.CN$NSTR
C
      cn_zpwr = BIRP06.AND.BILB06.AND.IDCNSSW .AND.         ! ECU power logi
     +          AGFPS75.AND.AGFPS76.AND..NOT.cn_rk1
C
      IF (.NOT.cn_zpwr) cn_trip = .FALSE.   !reset trip logic
C
      cn_sol = cn_zpwr .AND. (.NOT.TF32061).AND.(.NOT.cn_trip)! Solenoid valve
     +          .AND.(.NOT.CNLIM)
C
C    Changed logic so the steering is off after flight regardless of
C    steering switch position.  Reset the ECU by cycling the switch.
C    Tom M  12-24-2011
C		After re-evaluation of gripe 17088 commented out changes made by TDM
C       Roy 09/19/2012
C
C      IF ((CNZMODE.EQ. 1) .AND. IDCNSSW) THEN
C          cn_oldssw = .FALSE.
C      ELSE
C          cn_oldssw = IDCNSSW
C      ENDIF
C
C     IF (cn_sol .AND. (cn_ahp2.GT.2000)) THEN           ! Availability o
C        IF ((AGFPS17) .AND. (cn_oldssw)) THEN       ! Weight on all
C          CNZMODE = 0                                     ! Steering mode
C
C    if solenoid power and hyd press>2000 and
C        if ECU in mode WOW then
C          steering mode
C     < I got rid of the old sw posn Roy >
C
       IF (AGFPS17) THEN
          IF (cn_sol .AND. (cn_ahp2.GT.2000)) THEN
          CNZMODE = 0                                 ! Steering mode
          ENDIF
          IF (.NOT.(cn_sol).AND.(cn_ahp2.GT.2000))THEN
          CNZMODE = 2                                 ! Shimmy damped
          ENDIF
          IF (.NOT.(cn_sol).AND.(.NOT.(cn_ahp2.GT.2000)))THEN
          CNZMODE = 1                                 ! Steer center
          ENDIF
       ENDIF
C
C  -------------------
CD CN040 CAUTION LIGHT
C  -------------------
C
CR [5] Sect. 2.7 p.13
CC
CC The NOSE STEERING caution light comes on when ECU internal failure occurs
CC or the steering limit switch is actuated.
C
      IF (IDCNSSW) THEN
        CN$NSTR = (CN$NSTR .OR. cn_rk1) .OR. TF32061
      ELSE
        CN$NSTR = .FALSE.
      ENDIF
C
C  ------------------
CD CN050 NOSE GEAR UP
C  ------------------
C
CR CAE Calculation
CC
CC The nosewheel module is terminated here if the nose gear is retracted
CC or the backdrive mode 1 is active.
CC
      IF ((VDUC.EQ.0).OR.(HCNMODE.EQ.1)) RETURN
C
C  -----------------------
CD CN060 NOSEWHEEL COMMAND
C  -----------------------
C
CR [1] ATA 32-50-00 p.15,18
CR [5] Sect 2.7 Issue 1 p.19,21
CC
CC The nosewheel is steered according to the current operational mode.
CC
CC Under the steering mode, the nosewheel is positioned by the nosewheel
CC handle input with authority of +/- 60 degrees from center with an
CC additional +/- 7 degree provided by the rudder pedals. Power steering
CC angle is limited to +/- 60 degrees. If it is exceeded, the system reverts
CC to the shimmy damped mode until the overtravel is removed.
CC
CC Under the steered center mode, the nosewheel control handle and the
CC rudder pedal inputs are inhibited and replaced by a neutral command
CC which drives the nosewheel to the center position
CC
CC Under the shimmy damped mode, the nosewheel is allowed to castor freely
CC up to +/- 120 degrees on ground.
CC
      IF (CNZMODE .EQ. 0) THEN                                  ! Steering mode
        IF (HCNMODE.NE.2) THEN
          CNTILLER = (-IACNHND)
          cn_hand = IACNHND * cn_hcnv
          IF (abs(cn_hand).LT.cn_hnddb) cn_hand = 0
          IF (cn_hand.GT. cn_cmdl) cn_hand =  cn_cmdl
          IF (cn_hand.LT.-cn_cmdl) cn_hand = -cn_cmdl
        ENDIF
        cn_pedal = CIRFPOS * cn_pcnv
        IF (cn_pedal.GT. cn_rudl) cn_pedal =  cn_rudl
        IF (cn_pedal.LT.-cn_rudl) cn_pedal = -cn_rudl
        CNZCMD = cn_hand + cn_pedal
        IF (CNZCMD.GT. cn_cmdl) CNZCMD =  cn_cmdl
        IF (CNZCMD.LT.-cn_cmdl) CNZCMD = -cn_cmdl
        cn_vl = 0.0001                                     ! Valve leakage
        cn_tlim = cn_cmdl
C
      ELSE IF (CNZMODE .EQ. 1) THEN                        ! Steered center
        CNZCMD = 0.0
        cn_vl = 0.0001
        cn_tlim = 2                                        ! Nosewheel trav
C
      ELSE                                                 ! Shimmy damped
        CNXV = 0.0
        cn_vl = cn_cast
        cn_ahp2 = 0.0
        cn_tlim = cn_casl
      ENDIF
C
C  ---------------------
CD CN070 NOSEWHEEL ANGLE
C  ---------------------
C
CR [6] p.
CC
CC This equation computes the nosewheel position from the current operational
CC mode and nosewheel command.
CC
      IF (CNZMODE .NE. 2) THEN                            ! Castor mode
        IF (CNZCMD.GT.cn_zcmdo) THEN                      ! Nosewheel hyst
          IF (CNZCMD-cn_cmd.GT.cn_hyst) cn_cmd = CNZCMD - cn_hyst
        ELSE
          IF (cn_cmd-CNZCMD.GT.cn_hyst) cn_cmd = CNZCMD + cn_hyst
        ENDIF
        cn_zcmdo = CNZCMD
C
        CNXV = cn_cmd - CNWS                              ! Demanded & act
        IF (ABS(CNXV).GT.cn_tripl) THEN
          cn_trip = .TRUE.            ! Trip nws if large command
        ELSE
          cn_trip = .FALSE.
        ENDIF
C
        IF (ABS(CNXV).LT.cn_db) THEN                      ! Input deadband
          CNXV = 0.0
        ELSE IF (CNXV .GT. 0) THEN
          CNXV = CNXV - cn_db
        ELSE
          CNXV = CNXV + cn_db
        ENDIF
        IF (CNXV .GT. cn_xvl) CNXV =  cn_xvl                 ! Input limit
        IF (CNXV .LT.-cn_xvl) CNXV = -cn_xvl
      ENDIF
C
      IF (.NOT.TCFFLPOS) THEN
        CNPL = (VMZGP(1)+CNWS*cn_sprk)*cn_ipa*cn_iarm        ! Actuator lo
      ELSE
        CNPL = 0.0                                           ! Actuator lo
      ENDIF
      cn_dpxv = CNXV*cn_ahp2+CNPL*(cn_vl+ABS(CNXV))          ! Delta pressure
C
      CNRATE = cn_kval * cn_dpxv                             ! Nosewheel pos
      IF (CNRATE .GT. cn_rlim) CNRATE =  cn_rlim
      IF (CNRATE .LT.-cn_rlim) CNRATE = -cn_rlim
C
      CNWS = CNWS + CNRATE * YITIM                           ! Nosewheel angl
C
      IF (CNWS .GT. cn_tlim) CNWS =  cn_tlim
      IF (CNWS .LT.-cn_tlim) CNWS = -cn_tlim
C
C  --------------------------------
CD CN080 NOSEWHEEL HYDRAULIC DEMAND
C  --------------------------------

      CNHPDMD = CNWS * (cn_maxpi)

      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00275 CN010 FIRST PASS INITIALIZATION
C$ 00287 CN020 ATG NOSEWHEEL BACKDRIVE
C$ 00314 CN030 OPERATIONAL MODES
C$ 00397 CN040 CAUTION LIGHT
C$ 00412 CN050 NOSE GEAR UP
C$ 00423 CN060 NOSEWHEEL COMMAND
C$ 00474 CN070 NOSEWHEEL ANGLE
C$ 00525 CN080 NOSEWHEEL HYDRAULIC DEMAND
