#! /bin/csh/ -f
#
# This command file is used to run STEDIT
#
unalias rm
set DEFDIR=`pwd`
set NDBSDIR=`logicals -t caendbs`
set EXECDIR=`logicals -t cae_caelib_path`
if ($NDBSDIR == "") then
  echo "The location of the NDBS directory is undefined."
  echo "Define the logical name caendbs."
  echo "Aborted..."
  exit
endif
set RTNLABEL="GSXENV"
goto GET_SX_ENV
GSXENV:
if ($simex != 0) then
  logicals -c target $NDBSDIR/rstedit.info
  if (`logicals -t caerz` == "") then
    set RZFILE = `ls *rz.dat.* >& /dev/null`
    set SSTATUS = $status
    if ($SSTATUS == 0) then
      set RZFILE = `ls *rz.dat.*`
      set RZFILE = $RZFILE[$#RZFILE]
      set FILE = `echo $RZFILE | sed "s/rz/rzg/"`
      set FILE = `echo $FILE | sed "s/[a-z,0-9]/& /g"`
      set END = $#FILE
      while ($FILE[$END] != "t")
        @ END = $END - 1
      end
      set RZGFILE = $FILE[1]
      set I = 2
      while ($I <= $END)
        set RZGFILE = $RZGFILE$FILE[$I]
        @ I = $I + 1
      end
      set FILE = `echo $RZFILE | sed "s/rz/rzx/"`
      set FILE = `echo $FILE | sed "s/[a-z,0-9]/& /g"`
      set END = $#FILE
      while ($FILE[$END] != "t")
        @ END = $END - 1
      end
      set RZXFILE = $FILE[1]
      set I = 2
      while ($I <= $END)
        set RZXFILE = $RZXFILE$FILE[$I]
        @ I = $I + 1
      end
      logicals -c caerz  $RZFILE
      logicals -c caerzg $RZGFILE
      logicals -c caerzx $RZXFILE
    endif
    $EXECDIR/stedit.exe
    logicals -d caerz
    logicals -d caerzg
    logicals -d caerzx
    set INFO_FILES = `ls $NDBSDIR/*.info`
    set RZSTATUS  = "none"
    set RZGSTATUS = "none"
    set RZXSTATUS = "none"
    foreach FILE ($INFO_FILES)
      grep -i RZ.DAT $FILE > /dev/null
      if ($status == 0) then
        set RZSTATUS = `grep -i RZ.DAT $FILE`
      endif
#
      grep -i RZG.DAT $FILE > /dev/null
      if ($status == 0) then
        set RZGSTATUS = `grep -i RZG.DAT $FILE`
      endif
#
      grep -i RZX.DAT $FILE > /dev/null
      if ($status == 0) then
        set RZXSTATUS = `grep -i RZX.DAT $FILE`
      endif
    end
#
    set SIMEX_DIR = `logicals -t CAE_SIMEX_PLUS`
    if ($status == 0) then
      set ENTER_FILE = "$SIMEX_DIR/enter/$RZSTATUS[2]:t"
      rm -f $ENTER_FILE
      touch $ENTER_FILE
#
      if ($RZSTATUS[1] != "none") then
        echo " $RZSTATUS `fmtime $RZSTATUS[2]`" >> $ENTER_FILE
      endif
      if ($RZGSTATUS[1] != "none") then
        echo " $RZGSTATUS `fmtime $RZGSTATUS[2]`" >> $ENTER_FILE
      endif
      if ($RZXSTATUS[1] != "none") then
        echo " $RZXSTATUS `fmtime $RZXSTATUS[2]`" >> $ENTER_FILE
      endif
    endif
  else
    $EXECDIR/stedit.exe
  endif
else
  $EXECDIR/stedit.exe
endif
exit
#
GET_SX_ENV:
set EDFUN = `logicals -t caeedfun`
if ($EDFUN != "") then
  if (`echo $EDFUN | wc -c` == 6) then
    set simex = 1
  else
    set simex = 0
  endif
else
  set simex = 0
endif
goto $RTNLABEL
