C'Title                 Interface DMC dispatcher
C'Module_ID             SHIPDFG
C'Entry_point           DMCDISP
C'Customer              GENERIC CUSTOMER BUYING A CAE PRODUCT
C'Application           Host-Interface link for DMC-based interface
C'Author                <PERSON>
C'Date                  July 1990
C
C'System                System executive
C'Iteration_rate        Critical Band
C'Process               CPU0.S00 (or CPU0.SP0)
C'Revision_history
C
C
C Verison 6.4c (<PERSON> Jul-09-92)
C    - Initialized Bptr Pointer to first BlockList entry. This will
C      avoid potentiel problem to run with MAX_ECC.
C    - Modified host.inc (V1.1) to increase the maximum number of
C      Ethernet CAE header (MAX_HPTR increased from 200 to 400). This last
C      modification has been necessary for 60Hz site with 6 ARINC DMCs.
C    - Increased maximum number of ARINC dmc in the table of change from
C      4 to 6. Also increased MAX_CPTR to 100.
C    - Added test for CAE_RUN_INTERFACE_1 = LOAD to not run dmcdisp.
C
C Version 6.4b (<PERSON>-Jean Apr-10-92)
C    - Modification for a special case when a block of change
C      overlaps 2 ARINC packets. Previous versions may crash the DMCs.
C
C Verison 6.4a (<PERSON> Jan-20-91)
C    - Adjusted the address of the internal buffer where
C      the copy of the error logger messages goes to make
C      it point to the proper location (Sub Version 6.3c)
C    - To be compatible with old EC_OPEN routine that does not support
C      the SEND_ON_CHANGE yet (like the VAX), a test IF(NEW_FORMAT) is
C      done before the treatment of the OutList returned by EC_BLDOP
C                                      (Sub Version 6.3b)
C
C Verison 6.4  (Andre St-Jean Oct-31-91)
C    - This version support ARINC SIMLOAD feature.
C      For the SIMLOAD, If the logical name CAE_SIMLOAD = Yes is defined
C      the dmcdisp will assume that the last block of the last leg of the
C      ARINC section (Band 4H for 30Hz sim. or band 5P for 60Hz sim.)
C      contains the simload data. In this case, the block should not be
C      transmited in regular mode of operation. When the SIMLOAD CDB flag is
C      set to true, the SIMLOAD data should be sent as leg#8(or16) instead
C      of the regular ARINC output. The STANDARD output section is sent as
C      usual. The SIMLOAD CDB flag must be EQUIVALENCEd with the ACTIVE_SL
C      local label in the CCU.INC include file.
C
C Version 6.3a (Andre St-Jean Oct-28-91)
C    - Fixed ASCB inputs by looking in the proper address in the
C      subsystem host data for the sub-system ID. (ARIPTR+6)
C        ptr     ptr+1    ptr+2     ptr+3    ptr+4     ptr+5    ptr+6
C      .-------.--------.---------.--------.---------.--------.--------------.
C      | DMC # | OP adr | OP size | IP adr | IP size | 1      | sub-systemID |
C      `-------`--------`---------`--------`---------`--------`--------------`
C    - For ARINC and ASCB system, on the old format, put the sub-system ID
C      in the first byte of the last frame flag word of the CAE Header.
C
C Version 6.3  (Andre St-Jean Oct-28-91)
C    - Fixed a bug with the ARINC packet for band 1A, should not
C      be set mark as the last packet if not.
C    - Give the Output status to the error logger and increase output
C      message details.
C
C Version 6.2a (Andre St-Jean Oct-12-91)
C    - Recalculate the pointer index in the subsequent input packets
C      outside the WHILE DO loop to make sure that if only 2 bytes
C      of data have to be copied, it will be.
C    - Do not update OFFSET for ByteInput, CDB_Addr is the start of the
C      DIP or CB section and should not be alter by offset
C    - Increased the size of the compare table pointer from 20 to 50
C    - Send the ARINC band 1A in a separate packet.
C    - Add more information in error messages.
C    - Changed DMC1_BLDIO to support 16 legs.
C
C Version 6.2  (Andre St-Jean Sep-24-91)
C    - Fixed a confusion with EC and UNIT in DMC1_BLDIO
C    - Use proper unit for input from many ports
C
C Version 6.1  (Andre St-Jean Jul-26-91)
C    - This version support up to 6 ASCB sub-systems per DMC
C    - Do not skip a leg if the EC_OUTPUT routine returns an error.
C    - Added MY_CCUREQ to remember that the last ccu request was mine.
C    - If the ARINC is not responding after 100 iterations, forget it
C      and do not send any more retry for ARINC lost input frame.
C      This last modification will allow the STD interface to work
C      properly even if the ARINC interface is not ready yet.
C
C Version 6.0  (Andre St-Jean Jul-19-91)
C    - This version send the output packets in a new format.
C      A list of block of changes is send instead of the raw data.
C    - For the error logger, if there is no input frames received,
C      send one error message NO INPUT RECEIVED, and do not send
C      ARINC or STANDARD INPUT FRAME LOST.
C    - Saving the error messages in local buffer of 64 entries.
C    - Fixed a potential bug in the treatment of the GENERAL outputs.
C    - For VAX system, we must build at least one input ring if we
C      want to do output to a specific EC.
C    - Added a new interface type CAE_DMC1_TYPEx = P1T_ to tell
C      the DMCDISP to toggle the P1 toggle DOP.
C    - Do not process CCU request if no input request is done
C    - For the transfer of output change sections, limit the number
C      of changes to 20% of the standard interface and 5% of the ARINC
C    - Make sure that at least 2 legs is used to ensure the double buffering
C    - If the standard interface inputs are not received yet,
C      assume that the concentrator had not enough time to collect
C      the input. In that case (INPUTLATE=.TRUE.), we skip the output
C      for that iteration and we expect all the inputs packet to be
C      ready for the next iteration. If it's still not ready, do RETRY.
C
C
C Version 5.5b (Andre St-Jean May-27-91)
C    - Do not call chgarinc routine if there is no ARINC input
C
C Version 5.5a (Andre St-Jean May-22-91)
C    - Put the status of a transmit or receive ethernet error in the
C      error logger buffer
C
C Version 5.5  (Andre St-Jean Mar-26-91)
C    - This version support one more level for ARINC banding (16 LEGS)
C      for simulator running at 60 Hz.
C    - Added code in LEGMASK1 to support up to 16 legs
C    - Also changed the retry logic to send a retry once or even twice in case
C      of consecutive errors.
C
C Version 5.4  (Andre St-Jean Mar-22-91)
C    - Use CHARACTER*255 variables to store logical name.
C
C Version 5.3b (Andre St-Jean Mar-05-91)
C    - Compute the MRCDB_ADDR before transfering the Circuit Breakers.
C      This fixed a problem on the VAX.
C
C Version 5.3a (Andre St-Jean Mar-04-91)
C    - Do not send a retry after the first iteration. On a VAX, the
C      first iteration just initialize the Eth.controller. Only at the
C      second iteration (OUTITER=2) that we send an input request.
C      No inputs will be received on the second iteration for the VAX
C      so we cannot ask for a retry otherwise the DMC will crash.
C
C Version 5.3  (Andre St-Jean Feb-28-91)
C    - Do not change the concentrator DMC.
C      A logical name can be defined to reactivate the change of
C      concentrator logic, CAE_CONC_CHANGE
C
C Version 5.2a (Andre St-Jean Feb-19-91)
C    - Moved the declaration of variable MAXBYTES from the main dmcdisp
C      to the include file HOST.INC because CCUFG needs it.
C
C Version 5.2  (Andre St-Jean Feb-05-91)
C    - Added new logical name CAE_INPUT1_RATE
C      If INPUT_RATE = 1   Input each iteration
C                    = 2   Input each 2 iteration etc...
C    - This version make sur that the input request is send at proper
C      rate even with banding on the standard output. Was wrong on the
C      previous version (before 5.2)
C    - Support YADMCDI buffer for nucleair DIPs.
C
C Version 5.1  (Andre St-Jean Feb-04-91)
C    - Put all equivalences with CDB labels in CCU.INC include file
C
C Version 5.0b (Andre St-Jean Jan-18-91)
C    - Changed DMC1_ASCB routine to add leg number in the output packet.
C    - Ask for retry each two iterations.
C
C
C
C'
C
C'Compilation_directives
C
C       - This file needs to be FPC because the include file CCU.INC
C         contains CP statements.
C
C'
C'Include_files_directives
C
C       - This module has four include files:
C         HOST.INC, CCU.INC, DMCFG1.INC and DMCSPEC.INC
C
C       - HOST.INC contains  the declarations  for  the host data
C           memory partition. This file do not have to be FPC.
C
C       - CCU.INC contains the labels necessary for the dispatcher
C           to handle the error logger and the on-line diagnostics
C           requests. This file has to be FPC.
C
C       - DMCFG1.INC contains a list of parameters plus common blocks
C         to share information between subroutines.
C
C       - DMCSPEC.INC Contains declaration and data statements that
C         are specific to a computer type. This file exist in many
C         different copies to support different system like VAX, UNIX.
C
C'
C
C'Description
C
C         This version of DMCDISP configures itself depending on the
C       value of  the CAE_DMC1_TYPE1 logical name. For instance;
C
C       CAE_DMC1_TYPE1 == IRQ_STD_ARI_GEN_ASC_INP_P1T_
C       CAE_DMC1_PORT1 == /dev/ent1      or  DELQA
C       CAE_DMC1_ADDR1 == UNX010         or  VAX010
C
C       will do Input Request, Standard output, ARINC Output, General
C       Output, ASCB Output and process input. The logical name
C       CAE_DMC1_PORT1 determines on wich port to do the I/O and the
C       logical name CAE_DMC1_ADDR1 the address of the Host first
C       Ethernet Controller.
C
C       In the same way you can define other logical name for up to four
C       Ethernet controller by using CAE_DMC1_TYPE4, CAE_DMC1_PORT4 and
C       CAE_DMC1_ADDR4.
C
C         This module first  generates the data structure required to
C       send/receive the  data to/from  the interface. To do that,
C       the host mapping data must be loaded into the host memory by
C       the DMC dispatcher.
C
C         During the  run-time  operation,  DMCDISP sets  the proper
C       values in the  INPUT request  buffer and sends on the ETHER-
C       NET  the OUTPUT data  and  the INPUT  request buffer.
C
C         When the  interface processors  will have acknowledged the
C       INPUT request buffer sent by the host,  they will respond by
C       sending back to the host the input data that  they  have ga-
C       thered. At that point, this module will have submitted INPUT
C       command  to the ETHERNET controllers receiving the data from
C       the interface processors.
C
C         When these INPUT  commands are  satisfied, DMCDISP has the
C       CDB data  available in its local memory. It is then its res-
C       ponsability to copy the appropriate data into the proper CDB
C       INPUT data buffers.
C
C'Subroutines_called
C
C         DMCDISP calls numerous CAE.LIB routines to prepare itself
C       for I/O and also to  transfer the data it received from the
C       interface into the CDB.
C
C'
        SUBROUTINE DMCFG1
        IMPLICIT NONE
C
        CHARACTER*80 RVLSTR
     +        /'$Revision: DMCFG1 V_6.4c Jul 92 Andre St-Jean $'/
        CHARACTER*4  RVL /'6.4c'/!Revision level
C
C
C'Include_files
C
        INCLUDE 'dmcfg1.inc' !NOFPC
        INCLUDE 'host1.inc' !NOFPC
        INCLUDE 'ccu1.inc'
C
C
C
C'Local_variables
C
C       *** PARAMETERS ***
C
C ---   Interface type code
C
        INTEGER*4   INP_Code,IRQ_Code,STD_Code,ARI_Code
        INTEGER*4   ASC_Code,GEN_Code,AUD_Code,RDR_Code
        INTEGER*4   P1T_Code
        INTEGER*4   MAX_TYPE            ! Maximum DMC interface type
        PARAMETER  (MAX_TYPE =13)       ! Maximum type code
        PARAMETER  (INP_Code = 1 )      ! Input code
        PARAMETER  (IRQ_Code = 2 )      ! Input Request
        PARAMETER  (STD_Code = 3 )      ! Standard command code
        PARAMETER  (ARI_Code = 4 )      ! ARINC command code
        PARAMETER  (ASC_Code = 5 )      ! ASCB command code
        PARAMETER  (GEN_Code = 6 )      ! General command code
        PARAMETER  (AUD_Code = 7 )      ! Audio command code
        PARAMETER  (P1T_Code = 8 )      ! P1 toggle dop
        PARAMETER  (RDR_Code = 12)      ! Radar command code
C
C ---   Input tasks code
C
        INTEGER*2 ERRTYP, DGNTYP, ByteInput, TC_CBS, TC_ERRLG
        INTEGER*2 IOSTATCODE,IOSTAT     ! Status for error logger
        INTEGER*2 SlotBuf(6,64)         ! ERLSBUF save buffer
        INTEGER*4 SlotCnt               ! SlotBuf pointer
        INTEGER*4 Slot_Addr             ! SlotBuf address where to copy data
C
        PARAMETER ( TC_CBS   = 16 )
        PARAMETER ( TC_ERRLG = 27 )
        PARAMETER ( ERRTYP    = '0300'X )
        PARAMETER ( DGNTYP    = '0340'X )
        PARAMETER ( ByteInput = '0040'X )
C
C ---   Interface loaded code
C
        INTEGER*4 DONE
        PARAMETER ( DONE     = '444F4E45'X )
C
C ---   Initialize the old value buffer
C
        INTEGER*4 DeadBeef              ! Initialization of oldvalue
        PARAMETER (DeadBeef  = 'deadbeef'X )
C
C ---   Set maximum number of input packet to save
C
        INTEGER*4 MAX_SAV
        PARAMETER ( MAX_SAV = 10)       ! Maximum number of packet saved
C
C ---   Set maximum number of consecutive iterations with ARINC or ASCB
C       input lost before considering the system OFF
C
        INTEGER*4 MAX_LOST
        PARAMETER ( MAX_LOST = 100)     ! Number of iteration with no inputs
C
C       *** END PARAMETERS ***
C
C
C       2 bytes integer variables used to extract informations
C       from the input packets received from the DMC or to
C       build output packets.
C
        INTEGER*2 BLOCK                 ! CDB block #
        INTEGER*2 ROUTINE               ! Contains the routine #
        INTEGER*2 CONC/ 0 /             ! CONCENTRATOR DMC #
        INTEGER*2 CONCSLOT(6)           ! CONCENTRATOR change error slot
        INTEGER*2 RMASK/ '03E0'X /      ! Routine # mask
        INTEGER*2 BMASK/ '001F'X /      ! Block # mask
C
C       The following labels are used when data is splited in two packety
C
        INTEGER*2 SAV_ROUTINE           ! Remember routine #
        INTEGER*2 SAV_BLOCK             ! Remember block
        INTEGER*2 SAV_CDB_ADDR          ! Remember CDB address
        INTEGER*2 SAV_NWORD             ! Remember number of words
        INTEGER*2 SavPacket(750,MAX_SAV)! Save buffer to collect packets
        INTEGER*2 Saved                 ! Save the last two bytes of the
C                                       !  ARINC packet in case of more
C                                       !  then one packet received
C       Logical declarations
C
        LOGICAL*4 FPASS     / .TRUE. /  ! First pass flag
        LOGICAL*4 NOINPUT   / .TRUE.  / ! No input yet
        LOGICAL*4 SKIPINPUT / .FALSE.  / ! Skip input
        LOGICAL*4 BADCMDCODE/ .TRUE.  / ! Bad command code
        LOGICAL*4 GENERR    / .FALSE. / ! Generate error message
        LOGICAL*4 InpInProg / .FALSE. / ! I/P I/O in progress flag
        LOGICAL*4 RETRY     / .FALSE. / ! Request last input again
        LOGICAL*4 CHNGCONC  / .FALSE. / ! CONCENTRATOR change flag
        LOGICAL*4 HWARE     / .FALSE. / ! No DMC responding flag
        LOGICAL*4 INPLOST   / .FALSE. / ! ARINC input lost
        LOGICAL*4 WRONGADR  / .FALSE. / ! Packet received with wrong adr
        LOGICAL*4 FRAMISS   / .FALSE. / ! Input frame missing
        LOGICAL*4 CALLERR   / .FALSE. / ! Error calling Eth I/O routines
        LOGICAL*4 STDLOST   / .FALSE. / ! Standard interface frame(s) lost
        LOGICAL*4 TOCOMPLETE/ .FALSE. / ! Input to be completed
        LOGICAL*4 TOSTART   / .FALSE. / ! Input to be started
        LOGICAL*4 RUN_INTERFACE         ! CAE_RUN_INTERFACE_1 logical name
        LOGICAL*4 MEMORY_REPEATER       ! CAE_MEMORY_REPEATER logical name
        LOGICAL*4 NUCLEAR               ! Nuclear site
        LOGICAL*4 CONC_CHANGE /.FALSE./ ! Change of concentrator
        LOGICAL*4 NORUN                 ! Run DMCDISP flag
        LOGICAL*4 SENDBACK    /.TRUE./  ! Send back all output flag
        LOGICAL*4 INPUTLATE   /.FALSE./ ! This flag is set if all
        LOGICAL*4 MY_CCUREQ   /.FALSE./ ! This tell me it is my ccu request
        LOGICAL*4 FirstLEGS   /.TRUE./  ! Set if not all the LEGS for the
                                        ! standard interface has not been sent
C
C       All purpose integer locations
C
        INTEGER*4 PCONC          / 0 /  ! Pointer to the current CONCENTRATOR
        INTEGER*4 NCONC          / 0 /  ! Number of concentrator in the list
        INTEGER*4 HMCONC         / 0 /  ! Index through the CONCENTRATOR list
        INTEGER*4 MAX_AR_CHNG    / 0 /  ! Maxi. ARINC changes
        INTEGER*4 CCUCOUNT       / 0 /  ! # of iteration DMCDISP waited to get
        INTEGER*4 LEGCNT         / 0 /  ! Output leg counter
        INTEGER*4 MAXINPWAIT     / 10/  ! Delay before CONCENTRATOR switch
        INTEGER*4 INPWAIT        / 0 /  ! Inpuit wait time
        INTEGER*4 RetryCnt       / 0 /  ! Number of consecutif retry
        INTEGER*4 SendBackCnt    / 0 /  ! Send back counter for stats.
        INTEGER*4 Unit(MAX_ECC)         ! Unit number for each E.C.
        INTEGER*4 Eslot                 ! Error slot processed by ERRLOGGER
        INTEGER*4 LIMIT(2)              ! Limit pointers through DMC data
        INTEGER*4 PACK_TYP              ! Get the input packet type
        INTEGER*4 I, J                  ! Intermediate labels
        INTEGER*4 LEG                   ! Leg counter
        INTEGER*4 NERRSLOT              ! # of error slots received
        INTEGER*4 LBuffer               ! Address of local input buffer
        INTEGER*4 NWORD                 ! DMC data block size
        INTEGER*4 CDB_OFFSET            ! Add this offset to CDB in address
        INTEGER*4 DATASIZE              ! Size of the data in packet
        INTEGER*4 DSAPCTL               ! Size of DSAP SSAP and CTL
        INTEGER*4 FGHISTORY( 0:50 )     ! Error history buffer
        INTEGER*4 MaxChng               ! Maximum number of ARINC changes
        INTEGER*4 InputStat             ! EC_INPUT status
        INTEGER*4 OutStatus             ! EC_OUTPUT status
        INTEGER*4 DataPtr               ! Pointer to input packet data
        INTEGER*4 ByteCnt               ! Number of byte to transfer
        INTEGER*4 Ptr                   ! various pointer
        INTEGER*4 InpBlkPtr             ! Host input block pointer
        INTEGER*4 StdPtr                ! Pointer to host data for standard IF
        INTEGER*4 GenPtr                ! Pointer to general IO data
        INTEGER*4 InPtr                 ! Pointer into general input pkt
        INTEGER*4 SavPtr                ! Pointer into SavPacket
        INTEGER*4 Intrf_Seq(MAX_ECC,0:MAX_TYPE) ! List of types used
        INTEGER*4 LateCnt               ! input late counter
        INTEGER*4 SubSys                ! ASCB sub-system ID
        INTEGER*4 SS_Code               ! ASCB sub-system ID
C
C       Ethernet input packet type counter
C
        INTEGER*4   STD_Cnt /0/         ! Standard I/P count/iter
        INTEGER*4   STD_Loc(MAX_PACKETS)! Data location for Standard
        INTEGER*4   ARI_Cnt /0/         ! ARINC I/P count/iter
        INTEGER*4   ARI_Loc(MAX_PACKETS)! Data location for ARINC
        INTEGER*4   ASC_Cnt /0/         ! ASCB  I/P count/iter
        INTEGER*4   ASC_Loc(MAX_PACKETS)! Data location for ASCB
        INTEGER*4   GEN_Cnt /0/         ! General I/P count/iter
        INTEGER*4   GEN_Loc(MAX_PACKETS)! Data location for General
        INTEGER*4   RDR_Cnt /0/         ! Standard I/P count/iter
        INTEGER*4   RDR_Loc(MAX_PACKETS)! Data location for Radar
C
C       Ethernet input error counters
C
        INTEGER*4   RbBadCnt/0/         ! Number of bad inputs
        INTEGER*4   RbBadCmd/0/         ! Number of bad command code
        INTEGER*4   RbOvfCnt/0/         ! Number of overflow
        INTEGER*4   Rb12Cnt/0/          ! Number of error 12
        INTEGER*4   RbRuntCnt/0/        ! Number of Runt
        INTEGER*4   RbCRCCnt/0/         ! Number or CRC errors
        INTEGER*4   RbFRmCnt/0/         ! Number of frase missing errors
        INTEGER*4   ARI_LostCnt/0/      ! Number of consecutive lost ARINC INPUT
        INTEGER*4   ASC_LostCnt/0/      ! Number of consecutive lost ASCB INPUT
C
C       Nuclear
C
        INTEGER*4   TOT_NB_ENTRY        ! Total number of DIP entries (Nuc.)
        INTEGER*4   CURRENT_ENTRY       ! Current entry in DMCDI buffer
        INTEGER*4   YADMCDI_Addr        ! Address of DMCDI CDB label
C
C       Functions
C
        INTEGER*4 MOVE1, MOVE2, MOVE1MR, MOVE2MR ! Functions
        INTEGER*4 BASEADDR              ! Function returning CDB base address
        INTEGER*4 MRBASEADDR            ! Function returning Memory Repeater
C                                       ! CDB base address
C
C       Logical names variables local to the main pgm
C
        CHARACTER*255 DMC_PORTS(MAX_ECC) !CAE_DMC1_PORT 1,2,3,4
        CHARACTER*255 DMC_TYPES(MAX_ECC) !CAE_DMC1_TYPE 1,2,3,4
        CHARACTER*255 DMC_DAT            !CAE_DMC1_DAT
        CHARACTER*255 SCS_MDF            !CAE_SCS1_MDF
        CHARACTER*255 ASC_MDF            !CAE_ASC1_MDF
        CHARACTER*12  CStat              !Status return to error routine
        INTEGER*4     STD_BASE           !Standard IF CDB base
        INTEGER*4     ARI_BASE           !Standard IF CDB base
        INTEGER*4     ASCB_BASE          !Standard IF CDB base
C
        CHARACTER*3 IO_TYPE(MAX_TYPE)   ! List of available types
C
        CHARACTER*80  PHASE             ! DMCDISP execution phase
        INTEGER*4     IPHASE(20)
        EQUIVALENCE  (IPHASE(1),PHASE)
C
        COMMON    /ERR1_COM/  NORUN
        COMMON    /PHASE_COM/ IPHASE
C
        INCLUDE 'dmcspec.inc' !NOFPC
C
      DATA DMC_DAT     / ' '/
      DATA SCS_MDF     / ' '/
      DATA ASC_MDF     / ' '/
      DATA SAV_ROUTINE / 0  /
      DATA SAV_BLOCK   / 0  /
      DATA SAV_CDB_ADDR/ 0  /
      DATA SAV_NWORD   / 0  /
      DATA CONCSLOT    /'5FFF'X, 5*0 /
C
      DATA IO_TYPE(INP_Code) / 'INP'/  ! Input
      DATA IO_TYPE(IRQ_Code) / 'IRQ'/  ! Input Request output
      DATA IO_TYPE(STD_Code) / 'STD'/  ! Standard interface output
      DATA IO_TYPE(ARI_Code) / 'ARI'/  ! ARINC interface output
      DATA IO_TYPE(ASC_Code) / 'ASC'/  ! ASCB interface output
      DATA IO_TYPE(GEN_Code) / 'GEN'/  ! General I/O interface output
      DATA IO_TYPE(AUD_Code) / 'AUD'/  ! Audio output
      DATA IO_TYPE(P1T_Code) / 'P1T'/  ! P1 toggle DOP
      DATA IO_TYPE(9)        / '***'/  ! ---
      DATA IO_TYPE(10)       / '***'/  ! ---
      DATA IO_TYPE(11)       / '***'/  ! ---
      DATA IO_TYPE(RDR_Code) / '***'/  ! ---
      DATA IO_TYPE(13)       / '***'/  ! ---
C
C     ................................
C     :   ########################   :
C     :   #     ENTRY DMCDISP1    #   :
C     :   ########################   :
C     :...............................
C
C
      ENTRY DMCDISP1
C
      FStatus = 0
      IF (P1TOG_ON) THEN
         P1TOGGLE = .NOT. P1TOGGLE               ! P1 TOGGLE DOP
      END IF
C
      IF (NORUN .OR. HWARE) RETURN
      IF (FPASS) THEN
C
C     Decode logical names
C
      PHASE = 'logical name translation '
C
      CALL DMC1_LOGN(RUN_INTERFACE
     -,                 DMC_TYPES               ! Interface types
     -,                 DMC_PORTS               ! Interface ports
     -,                 EC_ADDRS                ! Interface EC addresses
     -,                 DMC_RETRY               ! Input req. retry period
     -,                 DMC_TIMOUT              ! DMC timout period
     -,                 INPUT_WAITTIME          ! Input wait time
     -,                 DMC_DAT                 ! DMC down load file
     -,                 SCS_MDF                 ! ARINC down load file
     -,                 ASC_MDF                 ! ASCB down load file
     -,                 MEMORY_REPEATER         ! Memory repeater Y/N
     -,                 OLD_EPROM               ! Old EPROM used
     -,                 STD_BASE                ! Standard IF CDB base
     -,                 ARI_BASE                ! ARINC IF CDB base
     -,                 ASCB_BASE               ! ASCB IF base
     -,                 NUCLEAR                 ! Nuclear site?
     -,                 INPUT_RATE              ! Input rate
     -,                 SIMLOAD                 ! SIMLOAD logic
     -,                 CONC_CHANGE )           ! Change of concentrator
C
      IF (.NOT. RUN_INTERFACE ) THEN
         NORUN = .TRUE.
      END IF
      IF (NORUN) RETURN
C
C     Get the list of all interface types to do for each
C     Ethernet controllers in the proper order.
C
C     EX: if        DMC_TYPE1 is  INP_IRQ_ARI_STD_
C                                  1   2   4   3
C         then      Intrf_Seq(1,1) = 1              Do input
C                   Intrf_Seq(1,2) = 2              first input request
C                   Intrf_Seq(1,3) = 4              second arinc output
C                   Intrf_Seq(1,4) = 3              third standard output
C
      PHASE = 'first pass logic'
C
      DO EC = 1, MAX_ECC
         DO I = 1, MAX_TYPE
            Intrf_Seq( EC,(INDEX(DMC_TYPES(EC), IO_TYPE(I))+3)/4) = I
         END DO
      END DO
C
C   Get different base addr
C
      IFBASE_ADDR = BASEADDR(STD_BASE)         ! Get address of STD I/F base
      ARINCBase_addr = BASEADDR(ARI_BASE)      ! Get address of ARINC I/F base
      ASCBBase_Addr  = BASEADDR(ASCB_BASE)      ! Get address of ASCB I/F base
      IF (MEMORY_REPEATER) THEN
         MR_BASE_OFFSET = MRBASEADDR(0) - BASEADDR(0)
      ELSE
         MR_BASE_OFFSET = 0
      END IF
C
C     Get YADMCDI address for nucleair DIPs.
C
      YADMCDI_Addr = LOC(ADMCDI)
C
C  Load Host data files.
C
C     The typical host data file loaded by hostload is
C     the standard host data file      * shipdmc.dat.rev
C     the ARINC host data file         * ship4290.mdf.rev
C
      PHASE ='first pass logic: Loading of host interface data files'
C
      CALL HOSTLOAD( DMC_DAT, SCS_MDF, ASC_MDF
     .,               IFBase_addr, ARINCBase_ADDR, ASCBBase_Addr
     .,               HOSTMEM, MaxBytes, STATUS)
C
      IF (Status .NE. 1) THEN
         FStatus = 40 + STATUS
         IF ( STATUS .EQ. 2 .OR. STATUS .EQ. 4) THEN
            CALL DMCFG1ERR( FStatus, DMC_DAT )
         ELSE IF ( STATUS .EQ. 5 .OR. STATUS .EQ. 6) THEN
            CALL DMCFG1ERR( FStatus, SCS_MDF )
         ELSE IF ( STATUS .EQ. 8) THEN
            CALL DMCFG1ERR( FStatus, ASC_MDF )
         END IF
         GOTO 900
      END IF
C
C   Open all ethernet controllers needed and assign them a unit number
C
      PHASE = 'first pass logic: Open of the Ethernet controller(s)'
C
      DO EC = 1, MAX_ECC
         IF (DMC_TYPES(EC) .NE. ' ') THEN
            CALL EC_OPEN(Status, DMC_PORTS(EC), EC_ADDRS(EC), UNIT(EC))
C
C           Check status and send error messages if any error
C
            IF (Status .NE. 1 ) THEN
               IF (Status .EQ. 5 .OR. Status  .EQ. 6) THEN
                  FStatus = 36
                  CALL DMCFG1ERR(FStatus, DMC_PORTS(EC))
               ELSE IF (Status .EQ. 7) THEN
                  FStatus = 35
                  CALL DMCFG1ERR(FStatus, DMC_PORTS(EC))
               ELSE IF (Status .EQ. 2) THEN
                  FStatus = 3
                  CALL DMCFG1ERR(FStatus, ' ')
               ELSE
                  CStat = 'Error =     '
                  WRITE(CStat(8:12),'(I5)') Status
                  FStatus = 35
                  CALL DMCFG1ERR(FStatus, CStat )
                  FStatus = 33
                  CALL DMCFG1ERR(FStatus, DMC_PORTS(EC))
               END IF
               RETURN
            END IF
         ELSE
            UNIT(EC) = 0
         END IF
      END DO
C
C     Initialize all pointers to the different data structures.
C     All data structres are in a common block that is used in
C     most of the subroutines.
C
      PHASE ='first pass logic: Parsing of host data file informations'
C
      Pptr  = 1                     ! Start pointer at first packet
      Dptr  = 1                     ! Pointer to first data entry
      Bptr  = 1                     ! Pointer to first BlockList entry
      Hptr  = 1                     ! Pointer to first CAE header
      CTptr = 1                     ! Pointer to Compare table
      OldPtr= 1                     ! initialize OldValues pointer
      ChangeIDX = 1                 ! Change list entry index
      PacketNum = 1                 ! Packet number start at 1
      InputList(1) = 0              ! Reset Input List count
      Iptr         = 2              ! Reset Input Pointer
      TOTIFRAME    = 0              ! Total input frame initial value
      InputUnit    = 0              ! No input unit determined yet
      IRQ_ON       = .FALSE.        ! Assume no input request unless specified
      P1TOG_ON     = .FALSE.        ! Assume no toggle DOP for now, unless
C                                   !   specfied in CAE_DMC1_TYPE log.name
C
C     Assume that there is no CDB sections to send on change so
C     we use the old format of packet unless SIMLOAD is on.
C
      IF (SIMLOAD) THEN
         NEW_FORMAT = .TRUE.
      ELSE
         NEW_FORMAT = .FALSE.
      END IF
C
C     Extract all the compare tables from the STD and ARINC host data file
C     to see if we use the new or the old format
C
      CALL DMC1_CMPTBL
C
C     Build the data structure that will describe the Ethernet packet
C     to be sent to the interface for all EC.
C
      DO EC = 1, MAX_ECC
         IF (Unit(EC) .NE. 0) THEN
            DO I = 1, MAX_TYPE
C
C              Call the appropriate routine to build output legs
C              and inputs this for each Ethernet controller.
C
               GO TO (10,20,30,40,50,60,70,80) Intrf_Seq(EC,I)
C
               GOTO 100
   10          InputUnit = Unit(EC)
               CALL DMC1_INP
               GOTO 100
   20          CALL DMC1_IRQ
               GOTO 100
   30          CALL DMC1_STD
               GOTO 100
   40          CALL DMC1_ARIC
               GOTO 100
   50          CALL DMC1_ASCB
               GOTO 100
   60          CALL DMC1_GENE
   70          GOTO 100
   80          CALL DMC1_P1T
               GOTO 100
  100       END DO
C
C           For VAX system, we need to build at least one input ring per EC
C
            IF (Iptr .LE. 2) CALL DMC1_ADDIP(1)
C
            PHASE ='first pass logic: Building Ethernet O/P frames'
C
            CALL DMC1_BLDIO(Unit(EC))
         END IF
      END DO
C
C  Get pointer to INPUT frames
C
      StdPtr = HOSTMEM( 1 )/4     ! Pointer to interface section 1
C
C  CONCENTRATOR DMC is first one in the list
C
      PCONC  = HOSTMEM( StdPtr+2 )/4  ! Pointer to the chassis list
      NCONC  = HOSTMEM( PCONC )       ! First chassis in the list
      PCONC  = PCONC + 1
      CONC   = IAND( HOSTMEM(PCONC), '7FFFFFFF'X )
      HMCONC = 1
      INPUTREQ(12) = IOR( DMC_Swap, CONC )
C
C  Initialize some HOST.INC arrays
C
      DO I=1, 64
         NON_STD(I)  = -1
         PNON_STD(I) = -1
      END DO
C
C  Send a START OF SIMULATION message to the ERROR LOGGER
C
      SlotCnt = 1
      SlotBuf( 1,SlotCnt ) = '5FFD'X
      ITERATION = 0
      OUTITER   = 0
      INPITER   = 0
C
      DO I = 0, 50
         FGHISTORY(I) = 0
      END DO
C
C  END of first pass
C
       FPASS = .FALSE.           !First pass successfully completed
       PHASE = 'run time '
      END IF
C
C
C
C  ....................................................................
C  :               ####################################               :
C  :               #      M A I N   R U N   T I M E   #               :
C  :               #      -------------------------   #               :
C  :               ####################################               :
C  :..................................................................:
C
C
        GENERR  = .FALSE.      ! Assume everything ok
C
C       If there was some input packet missing from previous iteration
C       continue to collect them
C
        IF (.NOT.INPUTLATE) THEN
           OUTITER = OUTITER + 1
C
C          Reset input counters
C
           STD_Cnt = 0
           ARI_Cnt = 0
           ASC_Cnt = 0
           GEN_Cnt = 0
        END IF
C
        IF (InpInProg) THEN
C
C          ................................
C          :   ########################   :
C          :   #       INPUTS         #   :
C          :   ########################   :
C          :...............................
C
           IF (MOD(OUTITER-1,INPUT_RATE).NE.0) THEN
              SKIPINPUT = .TRUE.
              GOTO 800
           ELSE
              SKIPINPUT = .FALSE.
           END IF
C
C          1- Read all input packets until the end (InputStat .EQ. 0)
C          2- Count the number of frame for each type
C          3- Save for each type the pointer to data
C
           CALL EC_INPUT(InputStat, DataPtr, InputUnit)
           DO WHILE (InputStat .NE. 0)
              IF (InputStat .EQ. 1) THEN      ! Good packet received?
C
C GOOD INPUT
C ----------     Get hostmem data address for the proper type
C
                 PktPtr = (DataPtr - LOC(HostMem))/2
                 IF (PktPtr .LT. 0 .OR. PktPtr .GT. HMEM2DIM) THEN
                    FStatus = 37
                    GOTO 900
                 END IF
                 IF (HOST2MEM(PktPtr+1) .NE. 4) THEN     ! DMC Pkt ?
                    RbBadCnt = RbBadCnt + 1          ! Error Not a DMC packet
                    BADCMDCODE = .TRUE.
                 ELSE
                    NOINPUT  = .FALSE.
                    PACK_TYP = HOST2MEM(PktPtr+3)        ! Yes Process it
C
C
C                         --- --- STD ARI ASC GEN --- --- --- ---
                    GOTO (201,201,203,204,205,206,201,201,201,201,
     .                    201,206)    PACK_TYP
C                         --- RDR
C
  201                 RbBadCmd = RbBadCmd + 1
                      BADCMDCODE = .TRUE.
                    GOTO 200
  203                 STD_Cnt = STD_Cnt + 1   ! *** STANDARD ***
                      STD_Loc(STD_Cnt) = PktPtr
                    GOTO 200
  204                 ARI_Cnt = ARI_Cnt + 1   ! ***  ARINC   ***
                      ARI_Loc(ARI_Cnt) = PktPtr
                    GOTO 200
  205                 ASC_Cnt = ASC_Cnt + 1   ! ***  ASCB    ***
                      ASC_Loc(ASC_Cnt) = PktPtr
                    GOTO 200
  206                 GEN_Cnt = GEN_Cnt + 1   ! ***  GENERAL ***
                      GEN_Loc(GEN_Cnt) = PktPtr
                    GOTO 200
  200               CONTINUE
                 END IF
              ELSE
C
C          ................................
C          :   ########################   :
C          :   #    INPUTS  ERRORS    #   :
C          :   ########################   :
C          :...............................
C
C                Input error treatment
C
                 GENERR = .TRUE.
                 IF (InputStat .LT. 0) THEN
                     IOSTAT    = InputStat
                     InputStat = 8
                 END IF
C
                 GOTO (301,302,303,304,305,306,307,308,309,310,311)
     +           InputStat
C
 301             GOTO 330
 302               InputUnit = 0                   ! Unit invalid
                   FStatus = 4
                   CALL DMCFG1ERR(FStatus,' Invalid unit # ')
                 RETURN
 303               RbOvfCnt = RbOvfCnt + 1         ! Ring overflow error
                 GOTO 300
 304               Rb12Cnt = Rb12Cnt + 1           ! bit 12 error
                 GOTO 300
 305               RbRuntCnt = RbRuntCnt + 1       ! RUNC error
                 GOTO 300
 306               RbCRCCnt = RbCRCCnt + 1         ! CRC error
                 GOTO 300
 307               RbFrmCnt = RbFrmCnt + 1         ! Frame missing error
                 GOTO 300
 308               CALLERR = .TRUE.                ! Routine call error
                   IOSTATCODE = 100                ! Input error
                 GOTO 330
 309               NOINPUT = .TRUE.                ! No input
                 GOTO 300
 310               WRONGADR = .TRUE.               ! Wrong paket address receive
                 GOTO 330
 311               FRAMISS = .TRUE.                ! Frame missing
                 GOTO 300
 300             RETRY = .TRUE.
 330             CONTINUE
              END IF
              CALL EC_INPUT(InputStat, DataPtr, InputUnit)
           END DO
C
C          Check if we have received enough packets for ...
C
C          ASCB
C
           IF (ASC_Cnt .LT. TOTASCFRAME) THEN
              IF ( ASC_LostCnt .LT. MAX_LOST) THEN
                 INPLOST = .TRUE.
                 RETRY   = .TRUE.
                 ASC_LostCnt = ASC_LostCnt + 1
              END IF
           ELSE
              ASC_LostCnt = 0
           END IF
C
C          ARINC
C
           IF (ARI_Cnt .LT. TOTARIFRAME) THEN
              IF ( ARI_LostCnt .LT. MAX_LOST) THEN
                 INPLOST = .TRUE.
                 RETRY   = .TRUE.
                 ARI_LostCnt = ARI_LostCnt + 1
              END IF
           ELSE
              ARI_LostCnt = 0
           END IF
C
C          STANDARD
C
           IF (STD_Cnt .NE. TOTSTDFRAME) THEN
              STDLOST = .TRUE.
              RETRY   = .TRUE.
           END IF
C
C          If an input late condition was detected in the last iteration
C          the inputs for this iteration are considered received or lost
C          but not late anymore.
C
           IF (INPUTLATE) THEN                  !If input late on last
              INPUTLATE = .FALSE.               !  iteration, it's no
              IF (.NOT.STDLOST) THEN            !  more late now
                 LateCnt = LateCnt + 1          !Increment input latecnt
              END IF
           ELSE
C
C          Have we received all the inputs on time?
C
              IF (STDLOST) THEN
C
C                If input missing we assume the inputs are late.
C                Save any packet received so far,
C                and skip this iteration.
C
                 INPUTLATE = .TRUE.
                 RETRY     = .FALSE.
                 INPLOST   = .FALSE.
                 STDLOST   = .FALSE.
C
C                Save all packets received so far
C
                 P_Bytes = 1500
                 SavPtr = 1
                 DO I = 1, STD_Cnt
                    CDB_ADDR   = LOC(SavPacket(1,SavPtr))
                    PTR_BLOCK  = STD_Loc(I)        ! For VAX COMMON BLOCK
                    LBuffer    = LOC(HOST2MEM(PTR_BLOCK))
                    Status     = MOVE2(CDB_ADDR,LBuffer,P_Bytes/2)
                    STD_Loc(I) = (CDB_ADDR - LOC(HostMem))/2
                    SavPtr     = MIN( SavPtr+1, MAX_SAV)
                 END DO
                 DO I = 1, ARI_Cnt
                    CDB_ADDR   = LOC(SavPacket(1,SavPtr))
                    PTR_BLOCK  = ARI_Loc(I)        ! For VAX COMMON BLOCK
                    LBuffer    = LOC(HOST2MEM(PTR_BLOCK))
                    Status     = MOVE2(CDB_ADDR,LBuffer,P_Bytes/2)
                    ARI_Loc(I) = (CDB_ADDR - LOC(HostMem))/2
                    SavPtr     = MIN( SavPtr+1, MAX_SAV)
                 END DO
                 DO I = 1, ASC_Cnt
                    CDB_ADDR   = LOC(SavPacket(1,SavPtr))
                    PTR_BLOCK  = ASC_Loc(I)        ! For VAX COMMON BLOCK
                    LBuffer    = LOC(HOST2MEM(PTR_BLOCK))
                    Status     = MOVE2(CDB_ADDR,LBuffer,P_Bytes/2)
                    ASC_Loc(I) = (CDB_ADDR - LOC(HostMem))/2
                    SavPtr     = MIN( SavPtr+1, MAX_SAV)
                 END DO
                 DO I = 1, GEN_Cnt
                    CDB_ADDR   = LOC(SavPacket(1,SavPtr))
                    PTR_BLOCK  = GEN_Loc(I)        ! For VAX COMMON BLOCK
                    LBuffer    = LOC(HOST2MEM(PTR_BLOCK))
                    Status     = MOVE2(CDB_ADDR,LBuffer,P_Bytes/2)
                    GEN_Loc(I) = (CDB_ADDR - LOC(HostMem))/2
                    SavPtr     = MIN( SavPtr+1, MAX_SAV)
                 END DO
C
                 IF (SavPtr .GE. MAX_SAV) THEN
                    Fstatus = 30
                    CALL DMCFG1ERR(FStatus,' ')
                 END IF
                 CALL EC_RESET(Status, InputUnit)
                 RETURN
              END IF
           END IF
C
C          Is a retry required
C
           IF (RETRY) THEN
              IF (OUTITER .GT. 2) THEN
C
C                Ask DMC's to resend the last inputs
C
                 RetryCnt = RetryCnt + 1
C
C                Ask for retry on the first occurrence of an error
C                or even after two consecutive errors but after the
C                third error, no retry.
C
                 IF (MOD(RetryCnt,3) .EQ. 1) THEN
                    InputReq(21) = IOR(InputReq(21),'0001'X)    !retry
                 ELSE
                    InputReq(21) = IAND( InputReq(21),'FFFE'X ) !No retry
                 END IF
              ELSE
                 InputReq(21) = IAND( InputReq(21),'FFFE'X ) !No retry
              END IF
C
              IF (RetryCnt .LE. 1) THEN
C
C                If first retry then do not send error message
C
                 GENERR  = .FALSE.          ! If first retry, no messages
                 STDLOST = .FALSE.
                 INPLOST = .FALSE.
                 CALLERR = .FALSE.
                 NOINPUT = .FALSE.
                 WRONGADR= .FALSE.
                 FRAMISS = .FALSE.
C
              ELSE
                 GENERR = .TRUE.
              END IF
              RETRY = .FALSE.
           ELSE
              InputReq(21) = IAND( InputReq(21), 'FFFE'X ) !No retry
              RetryCnt = 0
           END IF
C
C          Is the change of concentrator allowed
C
           IF (CONC_CHANGE) THEN
               InpWait = InpWait + 1
C
C              Check if a change of concentrator is needed
C
               IF (InpWait .GT. MaxInpWait) THEN
                   IF (HMCONC .EQ. NCONC) THEN
                       HMCONC = 0
                       PCONC  = HOSTMEM( StdPtr+2 )/4
                       HWARE  = .TRUE.
                   END IF
C
C                  Concentrator change
C
                   HMCONC = HMCONC + 1
                   PCONC  = PCONC + 1
                   CONCSLOT(2) = CONC
                   CONC        = IAND( HOSTMEM(PCONC), '7FFFFFFF'X )
                   CONCSLOT(3) = CONC
                   CHNGCONC    = .TRUE.
                   CONCSLOT(5) = ITER(1)
                   CONCSLOT(6) = ITER(2)
                   FStatus = 12
C
                   INPUTREQ(12) = IOR( DMC_Swap, CONC )
                   INPWAIT    = 0
                   MaxInpWait = 5
                   INPUTREQ(21) = IAND( INPUTREQ(21), 'FFFE'X )
               END IF
           END IF
        END IF
C
C       If an input request has to be send, process the CCU request if any
C
        IF (IRQ_ON) THEN
           INPUTREQ(13) = INPUTREQ(13) + 1      ! Another I/P request
C
C          Process ON-LINE DIAGNOSTICS counter
C
           IF (.NOT. CCUPROG) THEN
               IF (CCUREQ) THEN
                   CCUFAIL( 1 ) = .FALSE.
                   CCUCOUNT      = 0
                   INPUTREQ(17) = CCUDATA(1)    ! DMC # // DGN FLAGS
                   INPUTREQ(18) = CCUDATA(2)    ! C-BUS ADDRESS
                   INPUTREQ(16) = INPUTREQ(16)+1
                   CCUPROG   = .TRUE.
                   MY_CCUREQ = .TRUE.
                   CCUREQ    = .FALSE.
               END IF
           END IF
C
C          Increment the error counter in the input request
C
           INPUTREQ(15) = INPUTREQ(15) + 1
        END IF
C
C       Before doing the output, if the standard interface is in legs
C       do not schedule the output task before all legs has been sent
C       at least once.
C
        IF (FirstLEGS) THEN
C
C           if there is a standard interface output in legs
C
            IF (STDUnit .GT. 0 .AND.
     &          STDNumleg(STDUnit) .GT. 1 .AND.
     &          STDNumleg(STDUnit) .LE. MAX_LEG) THEN
C
C              Do not schedule the O/P task in the first LEGS
C
               IF (OUTITER .LE. 1) THEN
                  INPUTREQ(19)= 'FFFF'X          ! Do not schedule O/P task
                  INPUTREQ(20)= 'FFFF'X          !
               ELSE IF (OUTITER .GE. STDNumleg(STDUnit)+1) THEN
                  INPUTREQ(19)= 0                ! Start to schedule O/P task
                  INPUTREQ(20)= 0
                  FirstLEGS = .FALSE.
               END IF
            ELSE
               FirstLEGS = .FALSE.
            END IF
        END IF
C
C       ................................
C       :   ########################   :
C       :   #     Do the OUTPUT    #   :
C       :   ########################   :
C       :...............................
C
C       Check if an output error requested to resend the output
C
        IF (SENDBACK) THEN
           DO I = 1, OldPtr
              OldValues(I) = DeadBeef
           END DO
           SendBackCnt = SendBackCnt + 1
           SENDBACK = .FALSE.
        END IF
C
C       Go through each ECC and do the output.
C
 800    DO EC = 1, MAX_ECC
           IF (Unit(EC) .NE. 0) THEN                  ! is this EC used?
              LegCnt  = MOD( OUTITER-1, LegSize(EC) ) + 1
C
C             If SIMLOAD send other set of legs
C
              IF (SIMLOAD .AND. ACTIVE_SL) THEN
                 LegCnt = LegCnt + LegSize(EC)
              END IF
C
C             Do the comparison of sections of the CDB to send on change.
C
              IF (Cptr(LegCnt,EC) .GT. 0 )THEN
                 CALL MOVECHANGE(ChangeList(1,1,LegCnt,EC),
     &                                 Cptr(LegCnt,EC))
              END IF
C
C             Do the output
C
              CALL EC_OUTPUT( OutStatus, Unit(EC), LegCnt)
              IF (OutStatus .NE. 1) THEN
                 IF (OutStatus .EQ. 3) THEN
                    CStat = 'Leg =       '
                    WRITE(CStat(7:8),'(i2)') LegCnt
                    FStatus = 32
                    CALL DMCFG1ERR(Fstatus,CStat)
                 ELSE
                    OUTITER    = OUTITER - 1
                    SENDBACK   = .TRUE.
                    IOSTATCODE = 200                     ! Output
                    IOSTAT     = OutStatus
                    Fstatus    = 10
                    CALLERR    = .TRUE.
                    GENERR     = .TRUE.
                 END IF
              END IF
           END IF
        END DO
C
C       ..........................................
C       :   ##################################   :
C       :   # Process input packets received #   :
C       :   ##################################   :
C       :.........................................
C
C       is any input received
C
        IF ( (.NOT. NOINPUT ) .AND. (.NOT. SKIPINPUT) ) THEN
C
           DO I=1, 64
              IF (NON_STD(I) .NE. -1) THEN
                  PNON_STD(I) = NON_STD(I)  ! Save current state
                  NON_STD(I)  = 0           ! IRIS not responding
              END IF
           END DO
C
C     Reset DIPs counter for nuclear
C
           IF (NUCLEAR) THEN
              TOT_NB_ENTRY  = 0
              CURRENT_ENTRY = 1
           END IF
C
C          ................................
C          :   ########################   :
C          :   #    STANDARD INPUTS   #   :
C          :   ########################   :
C          :...............................
C
           IF ((STD_Cnt .GT. 0) .AND. (STD_Cnt .EQ. TOTSTDFRAME)) THEN
              StdPtr = HOSTMEM( 1 )/4         ! Pointer to interface sec. 1
C
C             Go accross each received packets
C
              DO I = 1, STD_Cnt
                 PktPtr = STD_Loc(I)
                 IF (HOST2MEM( PktPtr+2 ) .EQ. 0) GOTO 190  !If empty then skip
C
C                Skip CAE Header
C
                 DSAPCTL = ETH_HEADSIZE/2 - 7
                 Ptr = PktPtr + HOST2MEM(PktPtr+7)/2 - DSAPCTL
                 IF (I .EQ. 1) ByteCnt = HOST2MEM(Ptr)
                 LIMIT(2) = Ptr + ByteCnt/2 - 1
                 DATASIZE = 750 - DSAPCTL
                 IF (LIMIT(2) .GE. (PktPtr + DATASIZE)) THEN
                    LIMIT(1) = PktPtr + DATASIZE -1
                    ByteCnt = ByteCnt - (1500 - HOST2MEM( PktPtr+7))
                 ELSE
                    LIMIT(1) = LIMIT(2)    ! STD I/P in one packet
                    ByteCnt = 0
                 END IF
                 IF (I .EQ. 1) Ptr = Ptr + HOST2MEM( Ptr+1 )/2  ! Skip DMC data
C
C                For subsequent input packets, recalculate the Ptr
C
                 IF (ToComplete) THEN   ! DMC data crossing packets
                    Ptr = Ptr - 2
                    HOST2MEM(Ptr)   = IOR(SAV_ROUTINE,SAV_BLOCK)
                    HOST2MEM(Ptr+1) = SAV_NWORD*2
                 ELSE
                    CDB_OFFSET = 0
                 END IF
C
                 IF (ToStart) THEN
                    Ptr = Ptr - 1
                    HOST2MEM(Ptr) = SAV_ROUTINE
                    ToStart = .FALSE.
                 END IF
C
C  Process the blocks as they appear in the buffer
C
                 DO WHILE (Ptr .LT. LIMIT(1))
C
                    ROUTINE = IAND( HOST2MEM(Ptr), RMASK )
                    BLOCK   = IAND( HOST2MEM(Ptr), BMASK )
C
C  ERROR LOGGER data only contains routine #, and since DMCDISP triggers
C    on the block #, it has to be artificially inserted in the I/P data
C
                    IF (ROUTINE .EQ. ERRTYP) BLOCK = TC_ERRLG
C
C  Get CDB pointers
C
C   Ptr:       points to the header of a block in the master INPUT buffer
C   StdPtr:    always point to the start of host data for STANDARD interface
C   InpBlkPtr: will point to the start of host data for block # BLOCK
C
C   Some information is required to the different subroutine that copy
C   the input datas into the CDB. This information resides in
C   CDB_ADDR    : Address of transfer in the CDB
C   MRCDB_ADDR : Address of transfer in the Memory Repeater
C   P_BYTES     : Number of bytes to transfer
C   PTR_MAP     : Pointer to map table for byte input (DIP)
C   PTR_OFF     : Pointer to circuit breaker table
C   PTR_BLOCK   : Pointer to input block info. in HOST2MEM
C
C   All these labels are passed to VAXs copy routines via COMMON BLOCK.
C   Parameters  are used  in case  of other  machines linke  UNIX.  Be
C   carefull if you have to modify this piece of code.
C
                    P_BYTES = HOST2MEM( Ptr+1)   ! Number of bytes
                    NWORD = P_BYTES / 2
C
C                   Validate NWORD
C
                    IF (NWORD .LT. 0) THEN
                       FStatus = 17
                       CALL DMCFG1ERR(Fstatus,' NWORD invalid')
                    END IF
C
                    InpBlkPtr = HOSTMEM( StdPtr + Block + 6 )/4
                    IF ((InpBlkPtr .EQ. 0) .AND. (ROUTINE.NE.DGNTYP)
     &                  .AND. (ROUTINE .NE. ERRTYP)) GOTO 189
C
C                   Get proper information for the copy
C                   Get CDB input buffer
C
                    CDB_ADDR = HOSTMEM( InpBlkPtr) + CDB_OFFSET
                    MRCDB_ADDR = CDB_ADDR + MR_BASE_OFFSET
                    PTR_BLOCK  = Ptr + 2         ! For VAX COMMON BLOCK
                    LBuffer = LOC(HOST2MEM(Ptr+2))
C
C  Check for data block crossing a packet
C
                    IF ( (Ptr+NWORD+1) .GT. LIMIT(1)  .AND.
     &                   (LIMIT(1) .NE. LIMIT(2)  ))  THEN
                       SAV_ROUTINE = ROUTINE
                       SAV_BLOCK   = BLOCK
                       SAV_NWORD   = NWORD
                       NWORD       = LIMIT(1) - Ptr -1
                       SAV_NWORD   = SAV_NWORD - NWORD
                       P_BYTES     = NWORD*2
                       IF (ROUTINE .NE. ByteInput) THEN
                          CDB_OFFSET  = CDB_OFFSET + P_BYTES
                       END IF
                    ELSE
                       SAV_NWORD   = 0
                       CDB_OFFSET  = 0
                    END IF
C
C  Process the DMC data
C
                    IF (ROUTINE .EQ. DGNTYP) THEN
C
C                      ########################
C                      # STANDARD DIAGNOSTICS #
C                      ########################
C
                       IF (CCUPROG .AND. MY_CCUREQ) THEN
                          IF (HOST2MEM(Ptr+8) .EQ. INPUTREQ(16)) THEN
                             CDB_ADDR  = LOC( CCURES(1) )
                             MRCDB_ADDR = CDB_ADDR + MR_BASE_OFFSET
                             P_BYTES   = 12
                             IF(MEMORY_REPEATER) THEN
                                Status = MOVE2MR( CDB_ADDR,
     +                                   MRCDB_ADDR, LBuffer, 6)
                             ELSE
                                Status = MOVE2( CDB_ADDR, LBuffer, 6)
                             END IF
C
                             CCUCOUNT  = 0
                             CCUFAIL(1)= .FALSE.
                             CCUPROG   = .FALSE.
                             MY_CCUREQ = .FALSE.
                             CCUCOMP   = .TRUE.
                          ELSE IF (CCUCOUNT .GE. 10) THEN
                             CCUFAIL(1)= .TRUE.
                             CCUPROG   = .FALSE.
                             MY_CCUREQ = .FALSE.
                             CCUCOMP   = .TRUE.
                          ELSE
                             CCUCOUNT  = CCUCOUNT + 1
                          END IF
                       END IF
                    ELSE IF (ROUTINE .EQ. ByteInput) THEN
C
C                      ########################
C                      # STANDARD BYTE INPUTS #
C                      ########################
C
                        PTR_MAP  = HOSTMEM( InpBlkPtr + 2 )
                        IF (Block .EQ. TC_CBS) THEN
C
C                          ### C.Bs. ###
C
                           PTR_OFF  = HOSTMEM( InpBlkPtr + 3)
                           IF (MEMORY_REPEATER) THEN
                              CALL MOVECBMR(CDB_Addr, MRCDB_ADDR,
     +                                      LBuffer, NWORD,
     +                                LOC(HOSTMEM(PTR_MAP/4)),PTR_OFF)
                           ELSE
                              CALL MOVECB(CDB_Addr, LBuffer, NWORD,
     +                                 LOC(HOSTMEM(PTR_MAP/4)),PTR_OFF)
                           END IF
                        ELSE
C
C                          ### DIPs ###
C
                           IF (NUCLEAR) THEN
                              TOT_NB_ENTRY = TOT_NB_ENTRY + NWORD
                              CALL MOVENUC(YADMCDI_Addr, TOT_NB_ENTRY,
     +                                    CURRENT_ENTRY, LBuffer, NWORD,
     +                                    LOC(HOSTMEM(PTR_MAP/4)))
                              CURRENT_ENTRY = TOT_NB_ENTRY + 1
                           ELSE
                              IF(MEMORY_REPEATER) THEN
                                 CALL MOVEDIPMR(CDB_Addr, MRCDB_ADDR,
     +                                        LBuffer, NWORD,
     +                                     LOC(HOSTMEM(PTR_MAP/4)))
                              ELSE
                                 CALL MOVEDIP(CDB_Addr, LBuffer, NWORD,
     +                                     LOC(HOSTMEM(PTR_MAP/4)))
                              END IF
                           END IF
                        END IF
                    ELSE IF (ROUTINE .EQ. ERRTYP) THEN
C
C                      ########################
C                      #   STANDARD ERRORLOG  #
C                      ########################
C
C
C                       Check if the error require to resend all output
C                       for DMC back online, output frame lost or
C                       output frame corrupted.
C                       code 9001  Chassis online
C                            7002  Lost output frame
C                            600C  ARINC output frame lost
C                            600D  ARINC output frame corrupted
C                            600E  ARINC frame queue is full
C                            7102  Overrun on output
C
                        DO J = 0, NWORD, 6
                           IF ( (HOST2MEM(Ptr+2+J) .EQ.'9001'X) .OR.
     +                          (HOST2MEM(Ptr+2+J) .EQ.'7002'X) .OR.
     +                          (HOST2MEM(Ptr+2+J) .EQ.'600C'X) .OR.
     +                          (HOST2MEM(Ptr+2+J) .EQ.'600D'X) .OR.
     +                          (HOST2MEM(Ptr+2+J) .EQ.'600E'X) .OR.
     +                          (HOST2MEM(Ptr+2+J) .EQ.'7102'X) ) THEN
                              SENDBACK = .TRUE.
                           END IF
                        END DO
C
                        CDB_ADDR = LOC(SlotBuf(1,SlotCnt+1))
                        NERRSLOT = MIN( (64-SlotCnt), NWORD/6 )
                        P_BYTES = 12*NERRSLOT
                        Status = MOVE2(CDB_ADDR, LBuffer,P_BYTES/2)
C
                        DO Eslot=SlotCnt + 1, SlotCnt + NERRSLOT
                           IF ((SlotBuf(1,Eslot) .EQ. '9001'X).OR.
     .                         (SlotBuf(1,Eslot) .EQ. '600C'X).OR.
     .                         (SlotBuf(1,Eslot).EQ.'9002'X))THEN
C
                                SlotBuf( 5,Eslot ) = ITER(1)
                                SlotBuf( 6,Eslot ) = ITER(2)
                           ELSE IF
     .                         ((SlotBuf(1,Eslot) .EQ. '7002'X) .OR.
     .                          (SlotBuf(1,Eslot) .EQ. '7102'X) .OR.
     .                          (SlotBuf(1,Eslot) .EQ. '7202'X)) THEN
C
                                SlotBuf( 2,Eslot ) = ITER(1)
                                SlotBuf( 3,Eslot ) = ITER(2)
                           END IF
                        END DO
                        SlotCnt = SlotCnt + NERRSLOT
                     ELSE
C
C                      ########################
C                      # STANDARD WORD INPUTS #
C                      ########################
C
                       IF(MEMORY_REPEATER) THEN
                          Status = MOVE2MR(CDB_Addr, MRCDB_ADDR,
     +                                   LBuffer, NWORD)
                       ELSE
                          Status = MOVE2(CDB_Addr, LBuffer, NWORD)
                       END IF
                    END IF
C
C                   Skip to the next block
C
  189               Ptr = Ptr + NWORD + 2
                    ToComplete = (SAV_NWORD .NE. 0)
                END DO
C
C               Check if we are at the end of the packet with a routine
C               number that start a new block
C
                IF (Ptr .EQ. LIMIT(1)) THEN
                   SAV_ROUTINE = HOST2MEM(Ptr)
                   ToStart = .TRUE.
                END IF
  190        END DO
          END IF
C
C
C          ................................
C          :   ########################   :
C          :   #    ARINC  INPUTS     #   :
C          :   ########################   :
C          :...............................
C
C     In input to to the routine CHNGARINC that copy the ARINC inputs
C     these labels are needed
C     ARINCINADR = Address in the CDB where the ARINC data goes
C     AriPtr     = Pointer in HOST2MEM to the CAEHEADER of the ARINC packet
C     AriNbChg   = Number of changes
C
C     *** Note that    AriPtr and AriNbChg   are in the HOST.INC common
C                      block and are passed to VAX subroutines through
C                      that common block.
C
           IF (ARI_Cnt .GT. 0) THEN
              Remain = 0                 ! Remain changes to copy
              DO I = 1, ARI_CNT
                 AriPtr = ARI_Loc(I)
C
C  Process ARINC I/P
C
                 DO J=1, MAX_SUBSYS
                    IF (HOST2MEM(AriPtr) .EQ. ARINP(1,J,ARI_CODE)) THEN
                       ARINCINADR = ARINP( 2,J,ARI_CODE )  ! Trans. adr.
                       MRCDB_ADDR = ARINCINADR + MR_BASE_OFFSET
C
C                      Check if many ARINC packets to process
C
                       IF (HOST2MEM(AriPtr+5) .EQ. 0) THEN  ! First packet
                           IF (Remain .NE. 0) THEN
                              INPLOST = .TRUE.
                              Remain = 0
                           END IF
                           AriNbChg = HOST2MEM ( AriPtr+8 ) ! Number of changes
                           MaxChng = (OUTSIZE/4)-1   !370
                           Saved  = HOST2MEM(AriPtr+OUTSIZE/2+7)
                       ELSE
                           IF (Remain .EQ. 0) THEN
                              INPLOST = .TRUE.
                              GOTO 145
                           END IF
                           AriNbChg = Remain                ! Number of changes
                           HOST2MEM(AriPtr+7) = Saved
                           Saved  = HOST2MEM(AriPtr+OUTSIZE/2+7)
                           AriPtr = AriPtr - 2               ! Adjust location
                           MaxChng = (OUTSIZE/4)   !371
                       END IF
C
C                      Test if number of changes is
C                      in more then one packet
C
                       IF (AriNbChg .GT. MaxChng) THEN
                          Remain = AriNbChg - MaxChng
                          AriNbChg      = MaxChng
                       ELSE
                          Remain = 0
                       END IF
C
C                      Copy data
C
                       IF (AriNbChg .GT. 0) THEN
                          IF(MEMORY_REPEATER) THEN
                             CALL CHNGARINCMR(ARINCINADR,MRCDB_ADDR,
     +                             LOC(HOST2MEM(AriPtr+9)),AriNbChg)
                          ELSE
                             CALL CHNGARINC(ARINCINADR,
     +                             LOC(HOST2MEM(AriPtr+9)),AriNbChg)
                          END IF
                       END IF
                       GOTO 145
                    END IF
                 END DO
  145            IF ((AriNbChg/2) .GT. MAX_AR_CHNG) THEN
                    MAX_AR_CHNG = AriNbChg/2
                 END IF
              END DO
              IF (Remain .NE. 0) INPLOST = .TRUE.
            END IF
C
C
C          ................................
C          :   ########################   :
C          :   #     ASCB  INPUTS     #   :
C          :   ########################   :
C          :...............................
C
C     In input to to the routine CHNGARINC that copy the ASCB inputs
C     these labels are needed
C     ARINCINADR = Address in the CDB where the ARINC data goes
C     AriPtr     = Pointer in HOST2MEM to the CAEHEADER of the ARINC packet
C     AriNbChg   = Number of changes
C
C     *** Note that    AriPtr and AriNbChg   are in the HOST.INC common block.
C                      are passed to VAX subroutines through that comm block.
C
           IF (ASC_Cnt .GT. 0) THEN
              Remain = 0                 ! Remain changes to copy
              DO I = 1, ASC_CNT
                 AriPtr  = ASC_Loc(I)
                 SubSys  = IAND(HOST2MEM(AriPtr+6),'FF00'X)
                 SS_Code = SubSys + HOST2MEM(AriPtr)
C
C  Process ASCB I/P
C
                 DO J=1, MAX_SUBSYS
                    IF (SS_Code .EQ. ARINP(1,J,ASC_CODE)) THEN
                       ARINCINADR = ARINP( 2,J,ASC_CODE )  ! Trans. adr.
                       MRCDB_ADDR = ARINCINADR + MR_BASE_OFFSET
C
C                      Check if many ASCB packets to process
C
                       IF (HOST2MEM(AriPtr+5) .EQ. 0) THEN  ! First packet
                           IF (Remain .NE. 0) THEN
                              INPLOST = .TRUE.
                              Remain = 0
                           END IF
                           AriNbChg = HOST2MEM ( AriPtr+8 ) ! Number of changes
                           MaxChng = (OUTSIZE/4)-1   !370
                           Saved  = HOST2MEM(AriPtr+749)
                       ELSE
                           IF (Remain .EQ. 0) THEN
                              INPLOST = .TRUE.
                              GOTO 146
                           END IF
                           AriNbChg = Remain              ! Number of changes
                           HOST2MEM(AriPtr+7) = Saved
                           Saved  = HOSTMEM(AriPtr+749)
                           AriPtr = AriPtr - 2               ! Adjust location
                           MaxChng = (OUTSIZE/4)   !371
                       END IF
C
C                      Test if number of changes is
C                      in more then one packet
C
                       IF (AriNbChg .GT. MaxChng) THEN
                          Remain = AriNbChg - MaxChng
                          AriNbChg      = MaxChng
                       ELSE
                          Remain = 0
                       END IF
C
C                      Copy data
C
                       IF(MEMORY_REPEATER) THEN
                          CALL CHNGARINCMR(ARINCINADR,MRCDB_ADDR,
     +                          LOC(HOST2MEM(AriPtr+9)),AriNbChg)
                       ELSE
                          CALL CHNGARINC(ARINCINADR,
     +                          LOC(HOST2MEM(AriPtr+9)),AriNbChg)
                       END IF
                       GOTO 146
                    END IF
                 END DO
C
C                If end of do loop, something wrong, cannot find
C                the sub_code with that DMC
C
  146            IF ((AriNbChg/2) .GT. MAX_AR_CHNG) THEN
                    MAX_AR_CHNG = AriNbChg/2
                 END IF
              END DO
              IF (Remain .NE. 0) INPLOST = .TRUE.
            END IF
C
C
C          ................................
C          :   ########################   :
C          :   #    GENERAL  INPUTS   #   :
C          :   ########################   :
C          :...............................
C
C
C     In input to to the routine MOVE1 that copy the GENERAL inputs
C     these labels are needed.
C     CDB_Addr   = Address in the CDB where the data goes
C     P_BYTES    = Number of bytes to transfer
C     Ptr        = Pointer in HOST2MEM to the CAEHEADER of the packet
C     InPtr      = Points to the data to tranfer in HOST2MEM
C
           IF (GEN_Cnt .GT. 0) THEN
              DO I = 1, GEN_Cnt
                 Ptr = GEN_Loc(I)        ! Ptr points to CAE header of packet
C
C  Process GENERAL I/P groups
C
                 DO J=1, MAX_SUBSYS
                   IF (HOST2MEM(Ptr) .EQ. ARINP(1,J,GEN_CODE)) THEN
                      CDB_ADDR    = ARINP( 2,J,GEN_CODE )
                      MRCDB_ADDR  = CDB_ADDR + MR_BASE_OFFSET
                      P_BYTES     = HOST2MEM( Ptr+2 )
                      InPtr       = Ptr + HOST2MEM(Ptr+7)/2 !Skip DMC data hdr
                      IF(MEMORY_REPEATER) THEN
                         Status = MOVE1MR( LOC(HOST2MEM(InPtr)),
     +                                CDB_Addr, MRCDB_ADDR, P_BYTES)
                      ELSE
                         Status = MOVE1( LOC(HOST2MEM(InPtr)),
     +                                CDB_Addr, P_BYTES)
                      END IF
                      NON_STD( HOST2MEM(Ptr) ) = 1
                   END IF
                 END DO
              END DO
            END IF
C
C
C  Account for non-standard DMCs
C
            IF (SlotCnt .LT. 64) THEN
                DO I=1, 64
                   IF (NON_STD(I) .NE. -1) THEN
                       IF (PNON_STD(I) .NE. NON_STD(I)) THEN
                           IF (SlotCnt .GE. 64) GOTO 220
                           SlotCnt              = SlotCnt + 1
                           SlotBuf( 1,SlotCnt ) = '5FF2'X
                           SlotBuf( 2,SlotCnt ) = I            ! DMC #
                           SlotBuf( 3,SlotCnt ) = NON_STD(I)   ! State
                           SlotBuf( 5,SlotCnt ) = ITER(1)
                           SlotBuf( 6,SlotCnt ) = ITER(2)
                           GENERR = .TRUE.
                       END IF
                   END IF
                END DO
            END IF
        END IF
C
  220   IF (.NOT. InpInProg) THEN
           IF (TOTIFRAME .GT. 0) THEN
               InpInProg = .TRUE.
               InpWait   = 0
           END IF
        END IF
C
C
C          ................................
C          :   ########################   :
C          :   #    ERROR LOGGER      #   :
C          :   ########################   :
C          :...............................
C
C  Send a message to the error logger (if some have to be send)
C
  900   IF (GENERR) THEN
C
C  Handle host data not loaded error
C
           IF (FStatus .EQ. 2) THEN
              IF (SlotCnt .LT. 64) THEN
                 SlotCnt              = SlotCnt + 1
                 SlotBuf( 1,SlotCnt ) = '5FF9'X
              END IF
           END IF
C
C  Handle transmission error calling Eth routine
C
           IF (CALLERR) THEN
              IF (SlotCnt .LT. 64) THEN
                 SlotCnt = SlotCnt + 1
                 SlotBuf( 1,SlotCnt ) = '5FF0'X
                 SlotBuf( 2,SlotCnt ) = ITER(1)
                 SlotBuf( 3,SlotCnt ) = ITER(2)
                 SlotBuf( 4,SlotCnt ) = IOSTATCODE
                 SlotBuf( 6,SlotCnt ) = IOSTAT
              END IF
              CALLERR = .FALSE.
           END IF
C
C  Handle change of CONCENTRATOR DMC
C
           IF (CHNGCONC) THEN
              IF (SlotCnt .LT. 64) THEN
                 SlotCnt = SlotCnt + 1
                 DO I=1, 6
                    SlotBuf( I,SlotCnt ) = CONCSLOT(I)
                 END DO
              END IF
              CHNGCONC = .FALSE.
           END IF
C
C  Handle messages if interface not responding
C
           IF (HWARE) THEN
              IF (SlotCnt .LT. 64) THEN
                 SlotCnt = SlotCnt + 1
                 SlotBuf( 1,SlotCnt ) = '5FFE'X
                 SlotBuf( 2,SlotCnt ) = ITER(1)
                 SlotBuf( 3,SlotCnt ) = ITER(2)
              END IF
              HWARE    = .FALSE.
           END IF
C
C  Handle NO INPUT frames received
C
           IF (NOINPUT) THEN
              IF (SlotCnt .LT. 64) THEN
                 SlotCnt              = SlotCnt + 1
                 SlotBuf( 1,SlotCnt ) = '5FF6'X
                 SlotBuf( 2,SlotCnt ) = ITER(1)
                 SlotBuf( 3,SlotCnt ) = ITER(2)
              END IF
              NOINPUT = .FALSE.
              INPLOST = .FALSE.
              STDLOST = .FALSE.
           END IF
C
C  Handle ARINC input frames lost
C
           IF (INPLOST) THEN
              IF (SlotCnt .LT. 64) THEN
                 SlotCnt              = SlotCnt + 1
                 SlotBuf( 1,SlotCnt ) = '5FFC'X
                 SlotBuf( 2,SlotCnt ) = ITER(1)
                 SlotBuf( 3,SlotCnt ) = ITER(2)
              END IF
              INPLOST               = .FALSE.
           END IF
C
C  Handle bad command code or routine in frame received
C
           IF (BADCMDCODE) THEN
              IF (SlotCnt .LT. 64) THEN
                 SlotCnt              = SlotCnt + 1
                 SlotBuf( 1,SlotCnt ) = '5FF3'X
                 SlotBuf( 2,SlotCnt ) = ITER(1)
                 SlotBuf( 3,SlotCnt ) = ITER(2)
              END IF
              BADCMDCODE = .FALSE.
           END IF
C
C  Handle STANDARD interface frames lost
C
           IF (STDLOST) THEN
              IF (SlotCnt .LT. 64) THEN
                 SlotCnt              = SlotCnt + 1
                 SlotBuf( 1,SlotCnt ) = '5FFB'X
                 SlotBuf( 2,SlotCnt ) = ITER(1)
                 SlotBuf( 3,SlotCnt ) = ITER(2)
              END IF
              STDLOST               = .FALSE.
           END IF
        END IF
C
C       Once the errors have been collected in the local SlotBuf
C       copy them into the CDB if the error logger queue is available
C
        IF (ERLREAD .AND. .NOT.ERLPROC .AND. SlotCnt .GT.0) THEN
C
C          Validate the error logger counter
C
            IF (ERLSCNT.GT.64 .OR. ERLSCNT.LT.0) THEN
               FStatus = 31
               CALL DMCFG1ERR(Fstatus,'YERLSCNT')
            ELSE
               I = 0
               DO Eslot = 1, SlotCnt
                  IF (ERLSCNT .LE. 64) THEN
                     ERLSCNT = ERLSCNT + 1
                     ERLSBUF(1,ERLSCNT) = SlotBuf(1,Eslot)
                     ERLSBUF(2,ERLSCNT) = SlotBuf(2,Eslot)
                     ERLSBUF(3,ERLSCNT) = SlotBuf(3,Eslot)
                     ERLSBUF(4,ERLSCNT) = SlotBuf(4,Eslot)
                     ERLSBUF(5,ERLSCNT) = SlotBuf(5,Eslot)
                     ERLSBUF(6,ERLSCNT) = SlotBuf(6,Eslot)
                  ELSE
                     I = I + 1
                     SlotBuf(1,I) = SlotBuf(1,Eslot)
                     SlotBuf(2,I) = SlotBuf(2,Eslot)
                     SlotBuf(3,I) = SlotBuf(3,Eslot)
                     SlotBuf(4,I) = SlotBuf(4,Eslot)
                     SlotBuf(5,I) = SlotBuf(5,Eslot)
                     SlotBuf(6,I) = SlotBuf(6,Eslot)
                  END IF
               END DO
               SlotCnt = I
C
C              Tell DMCERRLG to process messages
C
               ERLPROC = .TRUE.
            END IF
         END IF
C
C
C       ###########
C       # CLEANUP #
C       ###########
C
C       Make intermediate buffers available for data
C
        IF (.NOT. SKIPINPUT) THEN
           IF (InputUnit .NE. 0) THEN
              CALL EC_RESET(Status , InputUnit)
           END IF
        END IF
        ITERATION = ITERATION + 1
        FGHISTORY( FStatus ) = FGHISTORY( FStatus ) + 1
        RETURN
        END
C
C
C  ....................................................................
C  :               ####################################               :
C  :               #      R U N    T I M E    E N D   #               :
C  :               #      -------------------------   #               :
C  :               ####################################               :
C  :..................................................................:
C
C
C   The different subroutines are called in first pass to build the
C   output frames.
C
C Step (1) Call the appropriate routines, in the sequence decribed
C ======== by the DMC_TYPES, to build the data structure that
C          describes the Ethernet packet for each type of interface.
C
C          the DMC1_CMPTBL and
C              DMC1_BLDCMP routines
C          Go into the HOSTMEM data and get ...
C
C          1- The list of CDB block (addr|size) that has to be send on change
C             only. (the transmission of this data is done only if the dmcdisp
C             detect a change on a 4 bytes value). This is done by the routine
C             DMC1_CMPTBL. The data structure built is ...
C
C             CompareTable(1,CTptr) = Address of compare block
C             CompareTable(2,CTptr) = Size  in bytes
C             CompareTable(3,CTptr) = Pointer on the old value save buffer
C
C          The following subroutines ...
C             DMC1_IRQ   for Input Request
C             DMC1_STD   for Standard outputs
C             DMC1_GENE  for General outputs
C             DMC1_ARIC  for ARINC outputs
C             DMC1_ASCB  for ASCB outputs,
C
C          Go into the HOSTMEM data and get ...
C
C          2- The list of CDB block (addr|size) that describes each legs in that
C             interface type
C
C          For each pair (CDB addr| size), a routine is called
C          DMC1_CHKBLK.
C          That routine will check if that CDB block is part of the
C          table to send on change or a regular block to be copied
C          completely. The DMC1_CHKBLK routine will then call
C          DMC1_ADDOP.
C          This routine will build an entry in the data structure that
C          will describe for all Ethernet controller, all Ethernet packets
C          with the destination address, size, list of block contained and
C          the CAE header for it.
C
C          The PacketList and DataList arrays contains all the
C          specific information describing each Ethernet packet
C          in this format ...
C
C               PacketList
C        --------------------------
C First  | Packet priority number |    1 is first packet, 2 just after 1
C Packet | Leg number             |    0 if not in leg, 1 to 8 for leg #
C        | Destination(1)         |    Ethernet destination adress
C        | Destination(2)         |
C        | Pointer to data table  |--- Pointer to an entry in the
C        |------------------------|  | DataList
C Second |  "     "     "     "   |  |
C Packet |                        |  |
C                                    |
C                 DataList           |
C        --------------------------  |
C        | N Number of data block |<-' Number of block of data in packet
C        | Address of block 1     |    First block address
C        | Size of block 1        |    Size of first data block
C        | Address of block 2     !    Second block addres
C        | Size of block 2        |    Size of second data block
C        |      "       "         |
C        | Address of block N     |    Nth block address
C        | Size of block N        |    Size of Nth block address
C        |------------------------|
C        |     "         "        |
C
C
C          The routine DMC1_INP will call different routine to build
C          the data structure that will describe the input buffer
C             DMC1_STDIP   for Standard inputs
C             DMC1_GENIP  for General inputs
C             DMC1_ARIIP  for ARINC inputs
C             DMC1_ASCIP  for ASCB inputs,
C          That data structure is called InputList ...
C
C                 InputList
C        --------------------------------
C        |    Total number of Packets   |
C        |    Address of packet # 1     |
C        |    Size of packet # 1        |
C        |    Address of packet # 2     |
C        |    Size of packet # 2        |
C        |     "   "    "   "   "       |
C
C
C Step (2) A call to the routine DMC1_BLDIO is done to build the
C ======== input output packet.
C
C          DMC1_BLDIO will go through the PacketList and Data list,
C          it will sort the ethernet packet based on the Packet
C          priority number into a list of packets for each legs.
C          This list is ...
C
C          IOTABLE struc               Example
C          ----------------------      ---------
C          | Destination Pkt 1  |      | DMCF  |
C          | Destination        |      | FF..  |
C          | Number of block    |      |     2 |
C          | Adr. block 1       |      | 25000 |
C          | Size               |      |  1050 |
C          | Adr. block 2       |      | 27100 |
C          | Size               |      |   434 |
C          ----------------------      ---------
C          | Destination Pkt 2  |      |  "    |
C
C
C Step (3) After one IOTABLE for a leg has been built, this structure is
C ======== passed to the EC_BLDOP routine that will prepare the Ethernet
C          transfer.
C          The EC_BLDOP routine is computer type specific.
C          On VAX, it builds an input/output ring.
C          On UNIX, it builds an IOCL.
C          Also the DMC1_BLDIO routine will call the routine
C          EC_BLDIP with the InputList tabel to build input receive buffer
C
C
C          After all legs has been built, we just have to call
C          EC_OUTPUT ( Status, Unit, Leg#) to transmit the Ethernet packet or
C          EC_INPUT  ( Status, InputList, Unit) to receive input frame.
C
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_IRQ  SUBROUTINE       #               :
C  :               #    Build input request tables    #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_IRQ
C
      IMPLICIT NONE
C'Purpose
C
C        This routine build the input request table.
C     In input  Dptr , points to the next DataList entry
C               Pptr , points to the next PacketList entry
C               PacketNum , the actual packet priority number
C
C     In output Dptr, Pptr and PacketNum updated
C               One new entry in PacketList as well as
C               two new entries in DataList for an input request
C               INPUTREQ containing the input request.
C
C'Include file
C
C     INCLUDE 'DMCFG1.INC'
C
C'Subroutines_called
C
C     None
C
C  Set-up an I/P request
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4   I                    !Loop pointer
      CHARACTER*6 STR6                 !Swap string tempo. location
      INTEGER*4   LEGMASK1              !Function that retrun a leg mask
C
      INCLUDE 'dmcspec.inc' !NOFPC
C
      IF (DMC_Swap .NE. 0) THEN
C
C     If the bytes are swapped in the DMC, the host ETH address must be
C     swapped in the input request so that when the DMC returns the inputs,
C     the host address will be in the proper order.
C
         STR6 = EC_ADDRS(EC)
         DO I = 1, 6, 2
            CETHADDR(I:I) = STR6(I+1:I+1)
            CETHADDR(I+1:I+1) = STR6(I:I)
         END DO
      ELSE
         CETHADDR = EC_ADDRS(EC)
      END IF
C
C     Build input request.
C
      INPUTREQ(1) = DMC_Num          !DMC number
      INPUTREQ(2) = 4                !Input Request command code
      INPUTREQ(3) = '20'X            !Size of an input request
      INPUTREQ(4) = 2                !Buffer ID
      INPUTREQ(5) = 0                !Segment
      INPUTREQ(6) = 0                !Offset
      INPUTREQ(7) = 0                !Spare
      INPUTREQ(8) = '10'X            !Header size
      INPUTREQ(9) = I2ETHADDR(1)     !Host input EC address.
      INPUTREQ(10)= I2ETHADDR(2)     ! "
      INPUTREQ(11)= I2ETHADDR(3)     ! "
      INPUTREQ(12)= DMC_Swap+1       ! VAX='0001'X OTHER='FF01'X
      INPUTREQ(13)= 0                !Input request counter
      INPUTREQ(14)= DMC_TIMOUT+INPUT_WAITTIME !DMC Timout|input delay
      INPUTREQ(15)= 0
      INPUTREQ(16)= 0
      INPUTREQ(17)= 0
      INPUTREQ(18)= 0
      INPUTREQ(19)= 0
      INPUTREQ(20)= 0
      INPUTREQ(21)= 0
      INPUTREQ(22)= 1997*DMC_RETRY
      INPUTREQ(23)= 0
      INPUTREQ(24)= 0
      INPUTREQ(25)= 0
C
      PacketList(1,Pptr) = PacketNum          !Packet Number
      PacketList(2,Pptr) = LEGMASK1(-1,1)      !Leg mask for input request
      IF (Old_EPROM) THEN                     !Is old EPROM used
         PacketList(3,Pptr) = MultiCast(1)    ! yes, Multicast Inp. Req.
         PacketList(4,Pptr) = MultiCast(2)
      ELSE                                    !New EPROM
         PacketList(3,Pptr) = LocalCast(1)    ! LocalCast Inp. Req.
         PacketList(4,Pptr) = LocalCast(2)
      END IF
      PacketList(5,Pptr) = Dptr               !Pointer to data info
C
      DataList(DPtr)   = 1                    !One block
      DataList(DPtr+1) = LOC(INPUTREQ(1))     !Adr. of the input request
      DataList(DPtr+2) = 16 + 32              !Size of the input request
C
      PacketNum = PacketNum + 1               ! Next packet number
C
C     update pointers
C
      Dptr   = MIN( Dptr+3 , MAX_DPTR )
      Pptr   = MIN( Pptr+1 , MAX_PPTR )
      IRQ_ON = .TRUE.
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_STD  SUBROUTINE       #               :
C  :               #     Build the STD output table   #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_STD
C
      IMPLICIT NONE
C
C'Purpose
C
C       The purpose of this routine is to build the data structure
C     that will defined the standard interface output blocks to be
C     send to the interface.
C
C     In input  Dptr , points to the next DataList entry
C               Pptr , points to the next PacketList entry
C               Hptr , points to the next CAEHEADER entry.
C               Cptr , points to the next changelist entry
C               PacketNum , the actual packet priority number
C               CAEHEADER , Ethernet packet CAE header
C               HOSTMEM
C
C     In output Dptr, Pptr, Hptr and PacketNum updated
C
C
C     A special data structure is built to describe those CDB blocks
C     that need to be transfer on change only
C
C     ChangeList (i, entry#, LEG#, EC#)
C
C                  Where i =  1-  CDB address of block 1
C                             2-  Size of that block
C                             3-  Offset in the packet
C                             4-  Pointer to Old values
C                             5-  Pointer into the Eth.packet first sec.
C                             6-  Reserved size in the packet
C                             7-  pointer into the Eth.packet second sec.
C                             8-  Reserved size in the packet
C                             9-  Last byte pointer
C                            10-  Change bloc index
C                            11-  Overflow counter
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     DMC1_CHKBLK(Op_CDBAddr,SSize,LEG,ComCode,SubCode,NewFrame,Offset)
C
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4  I,J,N                ! Counter
      INTEGER*4  Sptr                 ! Pointer into legs structure
      INTEGER*4  OP_CDBAddr           ! Pointer to the O/P block
      INTEGER*4  OP_Size              ! Size of the O/P block
      INTEGER*4  SIZE                 ! Size of segment
      INTEGER*4  SSize                ! Block size
      INTEGER*4  DataBlock            ! Current data block in the leg
      INTEGER*4  LEG                  ! Current O/P leg being processed
      INTEGER*4  NBLOCK               ! Number of blocks
      INTEGER*4  OFFSET               ! Offset in output buffer
      INTEGER*4  NumBlkChg            ! Number of block to send on changes
      INTEGER*4  Start_Old            ! Pointer in OLD_CDB for old values
      INTEGER*4  Offset_CDB           ! CDB offet where start the cmp block
      INTEGER*4  ComCode/3/           ! STD output command code
      INTEGER*4  SubCode/0/           ! STD output sub code
      INTEGER*4  OPTR                 ! Pointer to the old value buffer
      INTEGER*4  Block_Count          ! Block counter
C
      LOGICAL*4  NewFrame             ! New Frame flag
      LOGICAL*4  ChangeOnly           ! Send change only
C
      INCLUDE 'dmcspec.inc' !NOFPC
C
      STDUnit = EC            ! Save the unit used for standard
      Chg_Factor = STD_FACTOR ! Send STD_FACTOR number of change max.
C
      Offset = 0
      I  = HOSTMEM( 1 )/4     ! Start of STANDARD interface data
C
C     Determine whether the standard interface section is in bands or not.
C     If the number we get is 1 of 8 or even less or equal to 16, it
C     corresponds to a leg number and we use banding logic. Otherwise
C     the value is an address and the old way to transfer data is used.
C
      IF ( HOSTMEM( I+38 ) .GT. 16) THEN
C
C        One block STANDARD output to transfer, no LEGs
C        ----------------------------------------------
C
         LEG        = 0                  ! Leg number for DMC1_ADDOP (CAEHeader)
         LEG_COM    = 0                  ! Ethernet leg number
         STDNumLeg(EC) = 1               ! # of legs for STANDARD
C
         OP_CDBAddr = HOSTMEM( I+38 )    ! OutPut Block pointer
         Op_Size    = HOSTMEM( I+39 )    ! Size of O/P block (in bytes)
         Offset     = 0
         NewFrame = .TRUE.
C
C        Check if there is some block to compare for changes.
C
         DO N = STDCTBeg, STDCTEnd-1
C
C           Block of raw data to send
C
            SSize  = CompareTable(1,N) - Op_CDBAddr
            IF (SSize .GT. 0) THEN
               CALL DMC1_CHKBLK(Op_CDBAddr,SSize,LEG,ComCode,
     &                         SubCode,NewFrame,Offset,STDNumLeg(EC))
               Offset = Offset + SSize
               Op_CDBAddr = Op_CDBAddr + SSize
            END IF
C
C           Block of data to send on change
C
            SSize = CompareTable(2,N)
            CALL DMC1_CHKBLK(Op_CDBAddr,SSize,LEG,ComCode,
     &                      SubCode,NewFrame,Offset,STDNumLeg(EC))
            Offset = Offset + SSize
            Op_CDBAddr = Op_CDBAddr + SSize
         END DO
C
         SSize = Op_Size - Offset
         IF (SSize .LT. 0) THEN
            FStatus = 16
            CALL DMCFG1ERR(FStatus,' in Standard Intrf.')
            RETURN
         ELSE
            CALL DMC1_CHKBLK(Op_CDBAddr,SSize,LEG,ComCode,
     &                      SubCode,NewFrame,Offset,STDNumLeg(EC))
         END IF
      ELSE
C
C        The STANDARD interface is in LEGs
C        ---------------------------------
C
         Sptr    = I+38
         STDNumLeg(EC) = HOSTMEM( Sptr )   ! # of legs for STANDARD
         Sptr    = Sptr + 1                ! Skip to first leg
C
         DO LEG=1, STDNumLeg(EC)           ! for each leg
            NBLOCK    = HOSTMEM( Sptr+1 )  ! # of data block in that leg
            Sptr      = Sptr + 2
            NewFrame  = .TRUE.             ! We are starting a new LEG
            Offset    = 0
C
            DO DATABLOCK = 1, NBLOCK
               OP_CDBAddr = HOSTMEM( Sptr )
               OP_Size    = HOSTMEM( Sptr+1 )
C
C              Test if that block fall in the output block
C              with changes only or not and add this block
C              in output table.
C
               LEG_COM = LEG
               CALL DMC1_CHKBLK(Op_CDBAddr,Op_Size,LEG,ComCode,
     &                         SubCode,NewFrame,Offset,STDNumLeg(EC))
C
               Offset = Offset + Op_Size
               Sptr = Sptr + 2
            END DO             !Next Block
         END DO             !Next Leg
      END IF
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_GENE SUBROUTINE       #               :
C  :               #    Build general output tables   #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_GENE
C
      IMPLICIT NONE
C
C'Purpose
C
C       The purpose of this routine is to build the data structure
C     that will defined the General interface output blocks to be
C     send to the interface.
C
C
C     In input  Dptr , points to the next DataList entry
C               Pptr , points to the next PacketList entry
C               Hptr , points to the next CAEHEADER entry.
C               PacketNum , the actual packet priority number
C               CAEHEADER , Ethernet packet CAE header
C               HOSTMEM
C
C     In output Dptr, Pptr, Hptr and PacketNum updated
C
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     DMC1_ADDOP(BAddr,BSize,LEG,CommandCode,SubCode,NewFrame,Offset,Changes)
C
C     This routine add the data for a block into a packet description
C     in PacketList and DataList.
C
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4 GenPtr               ! Pointer to general O/P
      INTEGER*4 I                    ! Counter
      INTEGER*4 NGroup               ! Number of general I/O groups
      INTEGER*4 CDB_Address          ! CDB Address of a GEN I/O group
      INTEGER*4 Offset               ! Portion of group
      INTEGER*4 Size                 ! Size for one segment
      INTEGER*4 Group_Size           ! Size of a group
      INTEGER*4 SubCode              ! DMC Sub code
      INTEGER*4 ComCode              ! DMC Sub code
      LOGICAL*4 NewF                 ! New frame flag
C
      INCLUDE 'dmcspec.inc' !NOFPC
C
C     Generate Packets table for GENERAL O/P groups
C
      GenPtr = HOSTMEM(HOSTMEM(1)/4+4)/4
      IF (HOSTMEM(GenPtr) .EQ. '50555245'X) THEN  ! PURE
         NGroup  = HOSTMEM( GenPtr + 1 )
         IF (NGroup .GT. MAX_PACKETS) THEN
            FStatus = 29
            CALL DMCFG1ERR( Fstatus, ' ')
            RETURN
         END IF
         GenPtr = GenPtr + 2
C
C        Set output leg number
C
         LEG_COM = 0     ! all legs
C
C        Go across each groups
C
         DO I=1, NGroup
            IF (HOSTMEM(GenPtr+1) .EQ. 7) THEN    ! process only O/P
               CDB_Address = HOSTMEM(GenPtr+2)    ! Get CDB address
               Group_Size  = HOSTMEM(GenPtr+3)     ! Get size for that group
               ComCode = ISHFT(HOSTMEM(GenPtr+4),-16)
               SubCode = IAND( HOSTMEM(GenPtr+4), '0000FFFF'X)
               NewF = .TRUE.
               CALL DMC1_ADDOP(CDB_Address,Group_Size,0,
     &                        ComCode,SubCode,NewF,0,.FALSE.)
            END IF
            GenPtr = GenPtr + 7            ! Next Ptr to section in a group
         END DO                      ! Next Group
      END IF
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_ARIC  SUBROUTINE      #               :
C  :               #     Build ARINC output tables    #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_ARIC
      IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to build the data structure
C     that will defined the ARINC interface output blocks to be
C     send to the interface.
C
C     In input  HOSTMEM
C
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     DMC1_ADDOP(BAddr,BSize,LEG,CommandCode,SubCode,NewFrame,Offset,Changes)
C
C     This routine add the data for a block into a packet description
C     in PacketList and DataList.
C
C
        INCLUDE 'dmcfg1.inc' !NOFPC
        INCLUDE 'host1.inc' !NOFPC
C
        INTEGER*4 NARINC                ! Number of ARINC intrf. sec.
        INTEGER*4 NextDMC               ! Pointer to ARINC section.
        INTEGER*4 Sptr                  ! Pointer in hostmem
        INTEGER*4 PacketStart           ! Save the first packet priority
        INTEGER*4 BAddr                 ! Block address
        INTEGER*4 BSize                 ! Block size
        INTEGER*4 LEG                   ! Current O/P leg being processed
        INTEGER*4 SimLoadLeg            ! Extras legs for the simload
        INTEGER*4 DATABLOCK             ! Current data block in the leg
        INTEGER*4 NBLOCK                ! Number of output data block in leg
        INTEGER*4 I, J                  !
        INTEGER*4 ComCode/3/            ! Arinc command code
        INTEGER*4 SubCode/1/            ! Arinc sub code
        INTEGER*4 Offset                ! Data offset in the packet
        INTEGER*4 Nb_1Ablock            ! Number of 1A block ( Tx separately)
        INTEGER*4 NumLeg                ! Number of legs for ARINC
        LOGICAL*4 NewFrame              ! Start new frame
C
        INCLUDE 'dmcspec.inc' !NOFPC
C
        IF (HOSTMEM(2) .EQ. 0) RETURN   ! No ARINC
        Chg_Factor = ARI_FACTOR         ! Send ARI_FACTOR% number of O/P change
C
        NARINC = HOSTMEM( HOSTMEM(2)/4 ) ! Get # of ARINC interface sections
        IF (NARINC .GT. MAX_SUBSYS) THEN
            FStatus = 19
            CALL DMCFG1ERR(FStatus, ' ' )
            RETURN
        END IF
        NEXTDMC = HOSTMEM(2)/4 + 1     ! Ptr to first interface section
C
        MaxPNum = 0                    ! The last PacketNumber for the
C                                      ! biggest leg is assume to be 0
        PacketStart = PacketNum        ! Remember wich packetnum we started
        DO I=1, NARINC
C
           Sptr = NEXTDMC                ! Pointer to current ARINC chassis
           IF (HOSTMEM( Sptr+1 ) .EQ. 0) THEN
               FStatus = 18
               CALL DMCFG1ERR(FStatus, ' ')
               RETURN
           END IF
           Nb_1ABlock = HOSTMEM( Sptr + 2)   ! Number of block for band 1a
C
           Sptr    = Sptr + 4
           CURRDMC = HOSTMEM( Sptr )         ! DMC # of current ARINC chassis
           NEXTDMC = HOSTMEM( Sptr+5 )/4     ! Pointer to the next DMC info
           Sptr    = HOSTMEM( Sptr+1 )/4     ! Pointer to chassis info
           ARINumLeg(EC) = HOSTMEM( Sptr )   ! # of legs
           Sptr    = Sptr + 1                ! Skip to first leg
C
           IF (SIMLOAD) THEN
              NumLeg = ARINumLeg(EC)*2       ! Double number of legs if simload
           ELSE
              NumLeg = ARINumLeg(EC)
           END IF
C
           DO LEG=1, ARINumLeg(EC)           ! for each leg
              PacketNum = PacketStart
              NBLOCK    = HOSTMEM( Sptr+1 )  ! # of data block in that leg
              Sptr      = Sptr + 2
              NewFrame  = .TRUE.             ! We are starting a new LEG
              Offset  = 0                    ! Offset in the packet start at 0
C
              DO DATABLOCK=1, NBLOCK
                 BAddr = HOSTMEM( Sptr )
                 BSize = HOSTMEM( Sptr+1 )
C
C                Validate the size to make sure that it is valid
C
                 IF (BSize .GT. OUTSIZE*MAX_PPTR) THEN
                     FStatus = 13
                     CALL DMCFG1ERR(FStatus, '... in ARINC section')
                     RETURN
                 END IF
C
C                Send the band 1A on a separate packet if old format
C
                 IF (DATABLOCK .EQ. Nb_1ABlock+1 .AND.
     +               .NOT. NEW_FORMAT)THEN
                    Remain = 0
                 END IF
C
C                Check if SIMLOAD logic.
C
                 IF (SIMLOAD                .AND. ! SIMLOAD on
     &               LEG .EQ. ARINumleg(EC) .AND. ! Last leg
     &               DATABLOCK .EQ. NBLOCK) THEN  ! Last block (band 4h or 5p)
C
C                    Special case SIMLOAD, build extras set of legs to
C                    send the simload data only
C
                     DO LEG_COM = LEG+1, LEG*2
                         NewFrame = .TRUE.
                         PacketNum = PacketStart
                         CALL DMC1_ADDOP(BAddr, BSize, LEG,
     &                     ComCode, SubCode, NewFrame, Offset, .TRUE.)
                     END DO
C
                 ELSE
C
C                   Build the block
C
                    LEG_COM = LEG
                    CALL DMC1_CHKBLK(BAddr,BSize,LEG,
     +                  ComCode,SubCode,NewFrame,Offset,NumLeg)
                 END IF
                 Offset = Offset + BSize
                 Sptr = Sptr + 2
              END DO                                           !Next Block
              IF (PacketNum .GT. MaxPNum) MaxPNum = PacketNum
           END DO             !Next Leg
        END DO             !Next ARINC DMC
C
        PacketNum = MaxPNum
        RETURN
        END
C
C  ....................................................................
C  :               ####################################               :
C  :               #       DMC1_ASCB  SUBROUTINE       #               :
C  :               #     Build ASCB output tables     #               :
C  :               ####################################               :
C  :..................................................................:
C
C
        SUBROUTINE DMC1_ASCB
        IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to build the data structure
C     that will defined the ASCB interface output blocks to be
C     send to the interface.
C
C     In input  HOSTMEM
C
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C    CALL DMC1_ADDOP(BAddr,BSize,Leg,CommandCode,SubCode,NewFrame,Offset,Changes
C
C     This routine add the data for a block into a packet description
C     in PacketList and DataList.
C
C
        INCLUDE 'dmcfg1.inc' !NOFPC
        INCLUDE 'host1.inc' !NOFPC
C
        INTEGER*4  NARINC                ! Number of ARINC intrf. sec.
        INTEGER*4  Sptr                  ! Pointer in hostmem
        INTEGER*4  Ptr                   ! Pointer to DMC info in hostmem
        INTEGER*4  SSize                 ! Total Block size
        INTEGER*4  BAddr                 ! block segment address
        INTEGER*4  SubSys                ! SubSystem ID
        INTEGER*4  SS_LEG                ! SubSystem and leg
        INTEGER*4  BSize                 ! block segment size
        INTEGER*4  I, J                  !
        INTEGER*4  ComCode /3/           ! ASCB Command code
        INTEGER*4  SubCode /6/           ! ASCB sub-code
        LOGICAL*4  NewFrame              ! New packet
C
        INCLUDE 'dmcspec.inc' !NOFPC
C
C       Set leg number for ASCB
C
        LEG_COM = 1
C
        IF (HOSTMEM(3) .EQ. 0) RETURN          !No ASCB
        NARINC = HOSTMEM( HOSTMEM(3)/4 )
        Ptr   = HOSTMEM( 3 )/4 + 1             ! Pointers to DMC info
C
        DO I=1, NARINC
           SPTR    = HOSTMEM( Ptr )/4   ! Pointer to current DMC
           CURRDMC = HOSTMEM( SPTR )    ! DMC #
           BAddr   = HOSTMEM( SPTR+1 )  ! Start of ASCB O/P block
           BSize   = HOSTMEM( SPTR+2 )  ! Size of ASCB O/P block
           SubSys  = HOSTMEM( SPTR+6 )  ! Sub-system ID
C
C          Validate the size to make sure that it is valid
C
           IF (BSize .GT. OUTSIZE*MAX_PPTR) THEN
               FStatus = 13
               CALL DMCFG1ERR(FStatus, '... in ARINC section')
               RETURN
           END IF
           NewFrame = .TRUE.
           SS_LEG   = SubSys*256 + 1
           CALL DMC1_ADDOP
     &         (BAddr,BSize,SS_LEG,ComCode,SubCode,NewFrame,0,.TRUE.)
           ptr = ptr + 1                ! Skip to next ASCB DMC
C
        END DO
        RETURN
        END
C
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_CMPTBL  SUBROUTINE    #               :
C  :               #       Build the compare table    #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_CMPTBL
      IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to read the host data files
C     to determine if there is some CDB sections to send on changes
C     and to call the routine that will build the compare table
C
C
C'Include
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     None
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4 NARINC                ! Number of ARINC intrf. sec.
      INTEGER*4 NextDMC               ! Pointer to ARINC section.
      INTEGER*4 Sptr                  ! Pointer in hostmem
      INTEGER*4 LEG                   ! Leg counter
      INTEGER*4 I,J
C
      DO EC = 1, MAX_ECC            ! For each Ethernet Controller
         DO LEG = 1, MAX_LEG        !   in each legs
            CPtr(LEG,EC) = 0        !      set the compare table to null
         END DO
      END DO
C
C     Look for a compare table in the STANDARD host data
C
      I  = HOSTMEM( 1 )/4     ! Start of STANDARD interface data
C
C     Get the list of CDB blocks to be transfer on changes only
C
      IF ( HOSTMEM( I+5 ) .GT. 4 ) THEN
         J = HOSTMEM(I+5)/4
         STDCTBeg = CTptr           ! Remember wher starts the STD blocks
         CALL DMC1_BLDCMP(J)
         STDCTEnd = CTptr           ! Remember wher ends the STD blocks
      ELSE
         STDCTBeg = 0
         STDCTEnd = 0
      END IF
C
C     Look for a compare table in the ARINC host data
C
      IF (HOSTMEM(2) .GT. 0) THEN          ! ARINC yes
         NARINC = HOSTMEM( HOSTMEM(2)/4 )  ! Get # of ARINC interface sections
         IF (NARINC .LE. MAX_SUBSYS) THEN  ! Validate it
            NEXTDMC = HOSTMEM(2)/4 + 1     ! Ptr to first interface section
C
            DO I=1, NARINC
C
               Sptr = NEXTDMC                ! Pointer to current ARINC chassis
C
C              Look for a compare table
C
               IF ( HOSTMEM( Sptr+3 ) .GT. 0 ) THEN
                  J = HOSTMEM(Sptr+3)/4
                  CALL DMC1_BLDCMP(J)
               END IF
C
               Sptr    = Sptr + 4
               NEXTDMC = HOSTMEM( Sptr+5 )/4     ! Pointer to the next DMC info
            END DO
         END IF
      END IF
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_BLDCMP  SUBROUTINE    #               :
C  :               #Get the compare table from HOSTMEM#               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_BLDCMP( J )
      IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to determine, based on the
C     compare table, if that CDB block has to be compare or has to
C     be send as it is to the interface.
C
C     In input   J , Pointer into hostmem to a list of block to
C                    send on change.
C                      ---------------------------------
C                J --> | Number of block               |
C                      | Address of first block        |
C                      | Size in bytes of first block  |
C                      | Address of second block       |
C                      | Size in bytes of second block |
C                      |  "  "  "  "  "  "  "  "  "  " |
C                      ---------------------------------
C                CTptr , CompareTable pointer
C                CompareTable , CDB block to send on change
C                OldPtr , Pointer to old value buffer
C
C     In output  CTptr and CompareTable containing new entry(ies)
C
C                        CompareTable
C                      ---------------------------------
C                      | Address of first block        |
C                      | Size of first block           |
C                      | Pointer to OLD value buffer   |
C                      | Address of second block       |
C                      | Size of second block          |
C                      | Pointer to OLD value buffer   |
C                      |  "  "  "  "  "  "  "  "  "  " |
C                      ---------------------------------
C
C'Include
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     None
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4 J
      INTEGER*4 Num_Entries
C
      IF (J .EQ. 0) RETURN
      J = HOSTMEM( J )/4
      Num_Entries = HOSTMEM( J )
C
      IF (CTptr+Num_Entries .GT. MAX_CTPtr) THEN
         FStatus = 39
         CALL DMCFG1ERR(FStatus,' ')
         RETURN
      END IF
C
      IF (Num_Entries .GT. 0) THEN
         NEW_FORMAT = .TRUE.
         DO CTptr = CTptr, CTptr+Num_Entries-1
C
C           Validation of the Address and size (multiple of 4)
C
            IF ( MOD(HOSTMEM(J+1),4) .NE. 0   .OR.
     +           MOD(HOSTMEM(J+2),4) .NE. 0 ) THEN
                 FStatus = 38
                 CALL DMCFG1ERR(FStatus,' in Compare Table ')
            END IF
C
            CompareTable(1,CTptr) = HOSTMEM(J+1)       ! Address
            CompareTable(2,CTptr) = HOSTMEM(J+2)       ! Size
            CompareTable(3,CTptr) = OldPtr             ! OLD_CDB Pointer
            OldPtr = OldPtr + HOSTMEM(J+2)/4
            J = J + 2
         END DO
      END IF
C
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_CHKBLK  SUBROUTINE    #               :
C  :               # Check if we must compared the blk#               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_CHKBLK( Op_CDBAddr, SSize, LEG,
     &                       ComCode,SubCode,NewFrame,
     &                       Offset,NumLeg)
      IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to determine, based on the
C     compare table, if that CDB block has to be compare or has to
C     be send as it is to the interface.
C
C     In input  Op_CDBAdr , CDB block address
C               Size      , size of the block
C               leg       , leg number
C               ComCode   , Command code for that block
C               subCode   , Sub code for that block
C               NewFrame  , new frame flag to start with a new packet
C               Offset    , Offset of that block in the output leg
C               Numleg    , Number of leg in that interface
C
C     In output No parameter should change.
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     DMC1_ADDOP(BAddr,BSize,LEG,CommandCode,SubCode,NewFrame,Offset,Changes)
C
C     These routines add the data for a block into a packet description
C     in PacketList and DataList.
C     DMC1_ADDOP add a block like it it
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4  Op_CDBAddr
      INTEGER*4  SSize
      INTEGER*4  LEG
      INTEGER*4  ComCode
      INTEGER*4  SubCode
      LOGICAL*4  NewFrame
      INTEGER*4  Offset
      INTEGER*4  NumLeg
      INTEGER*4  Offset_CDB
      INTEGER*4  Start_Old
C
      LOGICAL*4  ChangeOnly
      INTEGER*4  OP_Size
      INTEGER*4  N
C
      IF (SSize .EQ. 0) RETURN
      ChangeOnly = .FALSE.
      DO N = 1, CTptr-1
         IF (OP_CDBAddr .GE. CompareTable(1,N)) THEN
            IF (OP_CDBAddr .LT. CompareTable(1,N) +
     &                          CompareTable(2,N)    ) THEN
               Offset_CDB = CompareTable(1,N)
               Start_Old  = CompareTable(3,N)
               ChangeOnly = .TRUE.
            END IF
         END IF
      END DO
C
C     Different processing whether it's a block to send
C     on changes or not.
C
      IF (ChangeOnly) THEN
         ChangeIDX = ChangeIDX + 1
         OP_Size = ((SSize * CHG_FACTOR / 400)+1)*8
C
         DO N = LEG_COM, MAX_LEG, NumLeg
C
            CPtr(N,EC) = CPtr(N,EC) + 1
            IF (CPtr(N,EC) .GT. MAX_CPTR) THEN
               Fstatus = 21
               CALL DMCFG1ERR(FStatus,' CPtr ')
               CPtr(N,EC) = MAX_CPTR
            END IF
C
C           Validation of the Address and size (multiple of 4)
C
            IF ( MOD(OP_CDBAddr,4) .NE. 0   .OR.
     +           MOD(SSize     ,4) .NE. 0 ) THEN
                 FStatus = 38
                 CALL DMCFG1ERR(FStatus,' in Change List.')
            END IF
C
            ChangeList(1, CPtr(N,EC), N, EC) = OP_CDBAddr
            ChangeList(2, CPtr(N,EC), N, EC) = SSize
            ChangeList(3, CPtr(N,EC), N, EC) = Offset
            ChangeList(4, CPtr(N,EC), N, EC) =
     &        OP_CDBAddr-Offset_CDB+Start_Old*4+LOC(OldValues)
            ChangeList(5, CPtr(N,EC), N, EC) = 0
            ChangeList(6, CPtr(N,EC), N, EC) = OP_Size
            ChangeList(7, CPtr(N,EC), N, EC) = 0
            ChangeList(9, CPtr(N,EC), N, EC) = 0
            ChangeList(10,CPtr(N,EC), N, EC) = ChangeIDX
            ChangeList(11, CPtr(N,EC), N, EC) = 0
         END DO
C
C        Add this special block that has to be filled by the routine
C        that will compare old buffer and cdb buffer
C
         CALL DMC1_ADDOP(-ChangeIDX, SSize, LEG, ComCode, SubCode,
     &                   NewFrame,Offset,.FALSE.)
      ELSE
C
C        Add this block like a regular block of change.
C
         CALL DMC1_ADDOP(Op_CDBAddr, SSize, LEG, ComCode, SubCode,
     &                   NewFrame, Offset, .TRUE.)
      END IF
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_P1T SUBROUTINE        #               :
C  :               # Set the P1 toggle flag to true   #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_P1T
      IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to activate the
C     Toggeling of the P1 cabinet toggle DOP
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C
C'Subroutines_called
C
C     None
C
      INCLUDE 'dmcfg1.inc' !NOFPC
C
      P1TOG_ON = .TRUE.
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_ADDOP SUBROUTINE      #               :
C  :               # Build a change output block      #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_ADDOP(Buff_Addr, Buff_Size, Leg,
     &                      ComCode,  SubCode,   NewFrame,
     &                      Offset,   Changes)
      IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to build the data structure
C     that will define an interface output blocks to be sent to
C     the interface.
C
C       If the size of the data is zero, we build the CAE header.
C     In the case of ARINC, some modifications to the CAE header has
C     to be done.
C
C     In input
C               Parameters
C               ----------
C               Buff_Addr, is the buffer address to transmit
C               Buff_Size, is the size of the block of data
C               Leg,       is the leg number
C               ComCode,   is the Command code for this output
C               SubCode,   is the Sub-code for this output
C               NewFrame,  is a flag telling if we shall start new frame
C               Offset,    is the offset of this block of change in packet
C
C
C               Common block
C               ------------
C               DMC_Num  , is the DMC number
C               Dptr , points to the next DataList entry
C               Pptr , points to the next PacketList entry
C               Hptr , points to the next CAEHEADER entry.
C               PacketNum , the actual packet priority number
C               CAEHEADER , Ethernet packet CAE header
C
C     In output Dptr, Pptr, Hptr, Remain and PacketNum updated
C
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     None
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4 Buff_Addr    ! Buffer address to transmit
      INTEGER*4 Buff_Size    ! Size of the buffer
      INTEGER*4 LEG          ! Leg number
      INTEGER*4 ComCode      ! Command code
      INTEGER*4 SubCode      ! Sub code
      LOGICAL*4 NewFrame     ! Is this a new eth. frame
      INTEGER*4 Offset       ! Offset of the block of change in the packet
      LOGICAL*4 Changes      ! Send a block of changes
      LOGICAL*4 SEND_ON_CHANGE! Send on change block flag
      INTEGER*4 LOffset      ! Local copy of the offset parameter
      INTEGER*4 LBuff_Size   ! Local copy of Buff_Size
      INTEGER*4 LBuff_Addr   ! Local copy of Buff_Addr
C
      INTEGER*4 OldDPtr      ! Remember previous data pointer
      INTEGER*4 DataSize     ! Size of the packet data
      INTEGER*4 LEGMASK1      ! Function returning the leg mask
C
      INTEGER*2 BlkSize      ! Block size
      INTEGER*2 BlkOffset    ! Block offset
      INTEGER*2 Last/'C000'X/! Last flag
C
      INCLUDE 'dmcspec.inc' !NOFPC
C
C     When Buff_Addr is positive or very big and negative, it is an address
C     When Buff_Addr is a small negative number, it is a block of change index
C     in the later case, we calculate the size in the packet to contain the
C     CHG_FACTOR% of changes.
C
      IF (NEW_FORMAT) THEN
         Last = 'C000'X
      ELSE
         Last = 0
      END IF
C
      LBuff_Addr = Buff_Addr               ! Get the buffer address
      IF (LBuff_Addr.GT.0 .OR.             ! If it is a valid address
     &    LBuff_Addr.LT.-(MAX_Cptr*MAX_LEG*MAX_ECC)) THEN  !
         SEND_ON_CHANGE = .FALSE.
         LBuff_Size = Buff_Size            ! Get the buffer size
      ELSE
         SEND_ON_CHANGE = .TRUE.
         LBuff_Size = ((Buff_Size * CHG_FACTOR / 400) +1)*8  ! Adjust the size
      END IF
C
      LOffset = Offset                     ! Get the offset in packet
      IF (NewFrame) THEN                   ! If we start with a new packet
         Remain = 0                        ! Last packet is completed
         OffWithin = 0                     ! Offset within buffer is zero
      END IF
C
      DO WHILE (LBuff_Size .GT. 0)          ! Do until the buffer is emty
C
         IF (Changes .AND. NEW_FORMAT) THEN
            DataSize = MIN(LBuff_Size, Remain-4)! Size of the block in packet
         ELSE
            DataSize = MIN(LBuff_Size, Remain)  ! Size of the block in packet
            IF (SEND_ON_CHANGE) THEN
               DataSize = (DataSize/8) * 8      ! Mult. of 8
            END IF
         END IF
C
         IF (DataSize .LE. 0) THEN         ! New Frame
            DataSize = 0
C
C           Build CAE Header because beggining of new frame
C
            CAEHEADER(1,Hptr) = DMC_Num         ! DMC# (0;VAX  -1;UNIX)
            CAEHEADER(2,Hptr) = ComCode         ! Command code
            CAEHEADER(3,Hptr) = 0               ! Byte count
            CAEHEADER(4,Hptr) = SubCode         ! Offset of buffer
            CAEHEADER(5,Hptr) = 0               ! Segment of buffer
            CAEHEADER(6,Hptr) = OffWithin + Last! Offset within buffer
            CAEHEADER(7,Hptr) = LEG             ! Leg number
            CAEHEADER(8,Hptr) = 16              ! Size of header
C
            IF (NEW_FORMAT) THEN
               OffWithin = OffWithin + 1
               IF (.NOT. NewFrame) THEN            ! Previous not last frame.
                   CAEHEADER(6,Hptr-1) = CAEHEADER(6,Hptr-1) - '8000'X
               END IF
            END IF
C
            PacketList(1,PPtr) = PacketNum
            PacketList(2,Pptr) = LEGMASK1(SubCode,LEG_COM)
            PacketList(3,Pptr) = MultiCast(1)
            PacketList(4,Pptr) = MultiCast(2)
            PacketList(5,PPtr) = Dptr
            Remain = OUTSIZE
C
C           Special case for ARINC and ASCB
C
            IF (SubCode .EQ. 1   .OR.            !  ARINC or
     &          SubCode .EQ. 6 ) THEN            !  ASCB
C
               PacketList(3,PPtr) = DMCTAG(CURRDMC)
C
               IF (.NOT. NEW_FORMAT) THEN
                  CAEHEADER(7,Hptr) = 0          ! No leg number in CAE hdr
                  CAEHEADER(9,Hptr) = 1 + IAND(Leg,'FF00'X) ! Last frame flag
                  IF (NewFrame) THEN
                     CAEHEADER(3,Hptr) = 4       ! Start byte count
                     CAEHEADER(10,Hptr) = LEG    ! Leg number
                     DataSize = 4
                  ELSE
                     CAEHEADER(3,Hptr)  = 2       ! Start byte count
                     CAEHEADER(9,Hptr-1)= IAND(Leg,'FF00'X) ! Previous, not
                     DataSize = 2                           !  the last one
                  END IF
               END IF
            END IF
C
            NewFrame = .FALSE.
            DataList(Dptr)     = 1
            DataList(Dptr+1)   = LOC(CAEHEADER(1,HPtr))
            DataList(Dptr+2)   = 16 + DataSize
C
C           Update pointers
C
            OldDPtr = Dptr
            Pptr  = MIN( Pptr+1 , MAX_PPTR )
            Dptr  = MIN( Dptr+3 , MAX_DPTR )
            Hptr  = MIN( Hptr+1 , MAX_HPTR )
            PacketNum = PacketNum + 1
         ELSE
C
            IF (Changes .AND. NEW_FORMAT) THEN
C
C              Add block header [Offset | Size]
C
               BlockList(Bptr)     = LOffset
               BlockList(Bptr+1)   = DataSize
C
               CAEHEADER(3,Hptr-1) = CAEHEADER(3,Hptr-1) + 4
               CAEHEADER(5,Hptr-1) = LOffset+DataSize
C
               DataList(DPtr)      = LOC(BlockList(BPtr))
               DataList(DPtr+1)    = 4
               Dptr                = MIN( Dptr+2 , MAX_DPTR )
               DataList(OldDPtr)   = DataList(OldDPtr) + 1
               BPtr                = MIN( Bptr+2 , MAX_BPTR )
               Remain              = Remain -4
            END IF
C
C           Add the block itself
C
            CAEHEADER(3,Hptr-1) = CAEHEADER(3,Hptr-1) + DataSize
            DataList(DPtr)      = LBuff_Addr
            DataList(DPtr+1)    = DataSize
            Dptr                = MIN( Dptr+2 , MAX_DPTR )
            DataList(OldDPtr)   = DataList(OldDPtr) + 1
C
            IF (.NOT. SEND_ON_CHANGE) THEN
               LBuff_Addr  = LBuff_Addr + DataSize  !it's a special case
            ELSE
               IF (NEW_FORMAT) THEN
                  CAEHEADER(5,Hptr-1) = Offset+Buff_Size
               END IF
            END IF                                  !where changes has
            LBuff_Size = LBuff_Size - DataSize      !to be copied
            LOffset    = LOffset   + DataSize
C
         END IF
         Remain    = Remain    - DataSize
C
         IF (.NOT. NEW_FORMAT) THEN
            OffWithin = OffWithin + DataSize
         END IF
C
      END DO
C
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_BLDIO SUBROUTINE      #               :
C  :               #  Build inputs and outputs tables #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_BLDIO(Unit)
      IMPLICIT NONE
C
C'Purpose
C
C       This routine goes across the PacketList and DataList tables,
C     and for each legs, it takes all packet for that leg and build
C     an I/O list (IOTABLE) with all packets sorted by packet number
C     (priority).
C
C     The IOTABLE for each leg is then passed to the EC_BLDOP
C     routine that will build the appropriate command for the
C     Ethernet Controller to submit this list of packet to the
C     interface.
C
C     IOTABLE struc               Example
C     ----------------------      ---------
C     | Destination Pkt 1  |      | DMCF  |
C     | Destination        |      | FF..  |
C     | Number of block    |      |     2 |
C     | Adr. block 1       |      | 25000 |
C     | Size               |      |  1050 |
C     | Adr. block 2       |      | 27100 |
C     | Size               |      |   434 |
C     ----------------------      ---------
C     | Destination Pkt 2  |      |  "    |
C
C
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     EC_BLDOP( Status, Unit, Leg, Data)
C
C     This routine builds the Ethernet header for each packet to be send
C     for a leg and save all the transfer addresses that will be used
C     by the routine EC_OUTPUT to output that leg.
C
C     INPUT   Unit  : Unit number used for that leg.
C             Leg   : Leg number associated to this list of packets.
C             Data  : Data structure describing the packets.
C
C     OUTPUT  Status : Return status  1 = successful
C                                     2 = Invalid unit #
C                                     3 = Leg # not between 0 and 8
C                                     7 = Cannot build transmit buffer
C                                     8 = Too much data
C
C     EC_BLDIP( Status, Unit, Data)
C
C     This routine build Ethernet input buffer
C
C     INPUT   Unit  : Unit number used for that leg
C             Data  : Data structure describing the input buffers
C
C     OUTPUT  Status : Return status  1 = successful
C                                     2 = Invalid unit #
C                                     7 = Cannot build input buffer
C                                     8 = Too much inputs
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4   Unit              ! EC unit number
      INTEGER*4   I,J               !
      INTEGER*4   OutList(MAX_OPTR+2)! output table
      INTEGER*4   OPtr              ! Pointer into OutList
      INTEGER*4   Pkt_Count         ! Total number of packet defined
      INTEGER*4   Leg               ! Leg counter
      INTEGER*4   Limit             ! Limit number of packet per leg
      INTEGER*4   Count             ! Packet counter
      INTEGER*4   Priority          ! Packet priority
      INTEGER*4   PNumber           ! number of packet
      INTEGER*4   DataPtr           ! Pointer to data table
      INTEGER*4   ChgPtr            ! Change table pointer
      INTEGER*4   NbOfBlock         ! Number of blocks
      INTEGER*4   Total_Leg         ! Total number of legs
      INTEGER*4   SHIFT(0:32)       ! Leg # shift
     &          /'00000001'X,'00000001'X,'00000002'X,'00000004'X,
     &           '00000008'X,'00000010'X,'00000020'X,'00000040'X,
     &           '00000080'X,'00000100'X,'00000200'X,'00000400'X,
     &           '00000800'X,'00001000'X,'00002000'X,'00004000'X,
     &           '00008000'X,'00010000'X,'00020000'X,'00040000'X,
     &           '00080000'X,'00100000'X,'00200000'X,'00400000'X,
     &           '00800000'X,'01000000'X,'02000000'X,'04000000'X,
     &           '08000000'X,'10000000'X,'20000000'X,'40000000'X,
     &           '80000000'X/
C
      CHARACTER*12  CStat              !Status return to error routine
C
C     First check if any tables have reach there maximum
C
      IF (Pptr .GE. MAX_PPTR) THEN       ! PacketList limit?
         Fstatus = 29
         CALL DMCFG1ERR(FStatus,' ')
         RETURN
      END IF
C
      IF (Dptr .GE. MAX_DPTR) THEN       ! DataList limits?
         Fstatus = 21
         CALL DMCFG1ERR(FStatus,' Dptr ')
         RETURN
      END IF
C
      IF (Bptr .GE. MAX_BPTR) THEN       ! Block limits?
         Fstatus = 21
         CALL DMCFG1ERR(FStatus,' Bptr ')
         RETURN
      END IF
C
      IF (Hptr .GE. MAX_HPTR) THEN       ! CAE Header limits?
         Fstatus = 21
         CALL DMCFG1ERR(FStatus,' Hptr ')
         RETURN
      END IF
C
      DO I = 1, MAX_ECC
         DO LEG = 1, MAX_LEG
            IF (CPtr(LEG,I) .GE. MAX_CPTR) THEN
               FStatus = 39
               CALL DMCFG1ERR(FStatus,' ')
               RETURN
            END IF
         END DO
      END DO
C
      Pkt_Count = Pptr - 1
      LegSize(EC) = MAX(STDNumLeg(EC),ARINumLeg(EC))
C
C     If the input has to be done at a particular input rate
C     adjust the LegSize accordingly
C
      IF (InputUnit .EQ. Unit) THEN
         LegSize(EC) = MAX(LegSize(EC),INPUT_RATE)
      END IF
C
C     To make sure that the double buffering is used, at least 2 legs
C     must be constructed because the choice of buffer is based on the
C     leg number parity.
C
      IF (     (LegSize(EC) .EQ. 0 )
     &     .OR.(LegSize(EC) .EQ. 1 )) LegSize(EC) = 2   ! double buffering
C
C     For SIMLOAD logic, must double the number of legs
C
      IF (SIMLOAD) THEN
         Total_Leg = LegSize(EC)*2
      ELSE
         Total_Leg = LegSize(EC)
      END IF
C
C     Build output table
C
      DO Leg = 1, Total_Leg
         Limit = Pkt_Count
         Count = 0
         Priority = 1
         Optr = 1
         DO WHILE ( Count .LT. Limit .AND. Priority .LT. 100)
            Pnumber = 0
            DO I = 1, Pkt_Count
               IF (IAND(PacketList(2,I),SHIFT(Leg)) .NE. 0) THEN
                  PNumber = PNumber + 1
                  IF ( PacketList(1,I) .EQ. Priority) THEN
                     OutList(Optr)   = PacketList(3,I)     ! Destination
                     OutList(Optr+1) = PacketList(4,I)     ! Destination
                     DataPtr         = PacketList(5,I)
                     NbOfBlock       = DataList(DataPtr)
                     OutList(Optr+2) = NbOfBlock           ! # block
                     Optr            = MIN( Optr+3 , MAX_OPTR)
                     DO J = DataPtr+1 , DataPtr+NbOfBlock*2,2
                        OutList(Optr  ) = DataList(J)        ! Adr.
                        OutList(Optr+1) = DataList(J+1)      ! Size.
                        Optr            = MIN( Optr+2 , MAX_OPTR)
                     END DO
                     count = count + 1
                  END IF
               END IF
            END DO
            Limit = PNumber
            Priority = Priority + 1
         END DO
C
         OutList(Optr) = 0                         ! THE END
C
C        Check for a possible overflow
C
         IF (Optr .GE. MAX_OPTR) THEN
            Fstatus = 21
            CALL DMCFG1ERR(FStatus,' ')
            RETURN
         END IF
C
         IF (PNumber .NE. 0) THEN
            CALL EC_BLDOP(Status, Unit, Leg, OutList)
            IF (Status .NE. 1) THEN
               IF (Status .EQ. 3) THEN
                  CStat = 'Leg =       '
                  WRITE(CStat(7:8),'(i2)') Leg
                  FStatus = 32
                  CALL DMCFG1ERR(Fstatus,CStat)
                  RETURN
               END IF
               IF (Status .EQ. 2) Fstatus = 4
               IF (Status .EQ. 7) FStatus = 6
               IF (Status .EQ. 8) FStatus = 29
               CALL DMCFG1ERR(FStatus , ' ')
               RETURN
            END IF
C
C           Update the ChangeList with the address(es) within the packet
C           where to copy the changes
C
            IF (NEW_FORMAT) THEN
              Optr = 1
              DO WHILE(OutList(Optr) .NE. 0)
                 ChgPtr = -OutList(Optr)
C
C              Look in the ChangeList entries the one that correspond
C              to the ChangeIDX (ChgPtr)
C
                 DO I = 1, CPtr(Leg,EC)
                    IF (ChangeList(10,I,Leg,EC) .EQ. ChgPtr) THEN
                       IF (ChangeList(5, I, Leg, EC) .EQ. 0) THEN
                           ChangeList(5, I, Leg, EC) = OutList(Optr+1)
                           ChangeList(6, I, Leg, EC) = OutList(Optr+2)/8
                       ELSE
                           ChangeList(7, I, Leg, EC) = OutList(Optr+1)
                           ChangeList(8, I, Leg, EC) = OutList(Optr+2)/8
                       END IF
                       GOTO 400
                    END IF
                 END DO
 400           Optr = Optr + 3
              END DO
            ENDIF
         END IF
      END DO
C
      PPtr = 1           !Reset Packet Pointer
C
C     Build Input table
C
      IF ( Iptr .GT. 2) THEN
         InputList(Iptr) = 0                        ! THE END
         CALL EC_BLDIP(Status, Unit, InputList)
         IF (Status .NE. 1) THEN
            IF (Status .EQ. 2 .OR. Status .EQ. 3) Fstatus = 4
            IF (Status .EQ. 7) FStatus = 11
            IF (Status .EQ. 8) FStatus = 30
            CALL DMCFG1ERR(FStatus , ' ')
            RETURN
         END IF
      END IF
C
      InputList(1) = 0   !Reset Input List
      Iptr = 2           !Reset Input Pointer
C
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_INP  SUBROUTINE       #               :
C  :               #         Build input table        #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_INP
      IMPLICIT NONE
C
C'Purpose
C     Each specific interface input routines (DMC1_STDIP, DMC1_ARIIP,
C     DMC1_ASCIP and DMC1_GENIP) is responsable to build enough entries
C     in the input table to satify the expected number of input packets.
C
C     The input table InputList describes how many packets we expect
C     to receive and where to store them.
C
C              InputList
C     --------------------------------
C     |    Total number of Packets   |
C     |    Address of packet # 1     |
C     |    Size of packet # 1        |
C     |    Address of packet # 2     |
C     |    Size of packet # 2        |
C     |     "   "    "   "   "       |
C     |                              |
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     DMC1_STDIP, DMC1_ARIIP, DMC1_ASCIP and DMC1_GENIP
C     These routines are described
C
C  Generate STD input tables
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4 PurePtr                ! Pointer to PURE data
C
      INCLUDE 'dmcspec.inc' !NOFPC
C
      InputList(1) = 0              ! No input entries yet
      Iptr = 2                      ! Point to first entry
      PktPtr = HOSTMEM(0) / 4           ! Point to free location in HOSTMEM
C                                   !    for input buffers
C  Generate Standard input tables
C
      IF (HOSTMEM( 1 ) .NE. 0) THEN
         CALL DMC1_STDIP
         IF (FStatus .NE. 0) RETURN
      END IF
C
C  Generate General input tables
C
      PurePtr = HOSTMEM( HOSTMEM(1)/4 + 4 )/4   ! Point to PURE data
      IF (HOSTMEM(PurePtr) .EQ. '50555245'X) THEN
         CALL DMC1_GENIP
         IF (FStatus .NE. 0) RETURN
      END IF
C
C  Generate ARINC I/P tables
C
      IF (HOSTMEM( 2 ) .NE. 0) THEN
         CALL DMC1_ARIIP
         IF (FStatus .NE. 0) RETURN
      END IF
C
C  Generate ASCB I/P tables
C
      IF (HOSTMEM( 3 ) .NE. 0) THEN
         CALL DMC1_ASCIP
         IF (FStatus .NE. 0) RETURN
      END IF
C
C  Add one more input frame for luck
C
      CALL DMC1_ADDIP(1)
C
C     Check for limit in InputList
C
      IF (Iptr .GE. MAX_IPTR) THEN
         FStatus = 30
         CALL DMCFG1ERR(FStatus, ' ')
      END IF
      HOST2MEM( HOSTMEM( 0 )/2 ) = 0      ! No data received yet
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_STDIP SUBROUTINE      #               :
C  :               #  Build input table  for standard #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_STDIP
      IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to build enough input buffer
C     to receive all Standard input packets expected.
C
C     In input  HOSTMEM
C
C     In output Iptr and InputList updated by DMC1_ADDIP
C
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     DMC1_ADDIP(NumberOfBuff) : Build input frame
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4 I
C
      INCLUDE 'dmcspec.inc' !NOFPC
C
C  Get number of Standard input frames
C
      I = HOSTMEM(1)/4
      MAX4INP = HOSTMEM( I+3 )     ! # of standard I/P frames
C
C  Generate INPUT tables (for each ETHERNET line) of the STANDARD interface
C
      TOTSTDFRAME = MAXINP(1)
C
      CALL DMC1_ADDIP(2*TOTSTDFRAME)            ! Build twice the number
C                                             !  of input frame
      TOTIFRAME = TOTIFRAME + TOTSTDFRAME     ! Total number of I/P frames
      FStatus = 0
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_GENIP SUBROUTINE      #               :
C  :               #  Build input table for general   #               :
C  :               ####################################               :
C  :..................................................................:
C
C
      SUBROUTINE DMC1_GENIP
      IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to build enough input buffer
C     to receive all General input packets expected.
C
C     In input  HOSTMEM
C
C     In output Iptr and InputList updated by DMC1_ADDIP
C               ARINP data structure that contains the transfer address
C               for data coming from any DMC
C               NON_STD array that indicates those in use.
C
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     DMC1_ADDIP(NumberOfBuff)  : Build input frame
C
      INCLUDE 'dmcfg1.inc' !NOFPC
      INCLUDE 'host1.inc' !NOFPC
C
      INTEGER*4 PurePtr          ! Pointer to PURE data
      INTEGER*4 J3               ! ARINP index
      INTEGER*4 NGROUP           ! Number of groups
      INTEGER*4 N,O
C
      INCLUDE 'dmcspec.inc' !NOFPC
C
C  Generate INPUTS table for GENERAL I/P groups
C
      TOTGENFRAME = 0
      PurePtr = HOSTMEM( HOSTMEM(1)/4 + 4 )/4   ! Point to PURE data

      NGROUP = HOSTMEM( PurePtr+1 )
      J3     = 1  ! Index through ARINP
      PurePtr = HOSTMEM(HOSTMEM(1)/4+4)/4 + 2
C
      DO N=1, NGROUP
         IF (HOSTMEM(PurePtr+1) .EQ. 8) THEN  ! process only I/P
C
            HOSTMEM( PurePtr+5 ) = HOSTMEM( PurePtr+3 )
            ARINP( 1,J3,6 ) = HOSTMEM( PurePtr )    ! DMC #
            NON_STD( HOSTMEM(PurePtr) ) = 0
            ARINP( 2,J3,6 ) = HOSTMEM( PurePtr+2 )  ! transfer address
            ARINP( 3,J3,6 ) = HOSTMEM( PurePtr+3 )  ! byte count
            J3              = J3 + 1
C
            DO O=1, HOSTMEM(PurePtr+3), OUTSIZE
               IF (HOSTMEM(PurePtr+5) .GT. OUTSIZE) THEN
                  HOSTMEM( PurePtr+5 ) = HOSTMEM( PurePtr+5 ) - OUTSIZE
               ELSE
                  HOSTMEM( PurePtr+5 ) = 0
               END IF
               TOTGENFRAME = TOTGENFRAME + 1
            END DO
         END IF
         PurePtr = PurePtr + 7
      END DO
      CALL DMC1_ADDIP(2*TOTGENFRAME)        ! Build 2 time the number of
      TOTIFRAME = TOTIFRAME + TOTGENFRAME !    Gerenal input frame
      FStatus = 0
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_ASCIP SUBROUTINE      #               :
C  :               #       Build ASCB input table     #               :
C  :               ####################################               :
C  :..................................................................:
C
C
        SUBROUTINE DMC1_ASCIP
        IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to build enough input buffer
C     to receive all ASCB input packets expected.
C
C     In input  HOSTMEM
C
C     In output Iptr and InputList updated by DMC1_ADDIP
C               ARINP data structure that contains the transfer address
C               for data coming from any DMC
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     None
C
        INCLUDE 'dmcfg1.inc' !NOFPC
        INCLUDE 'host1.inc' !NOFPC
C
        INTEGER*4 I
        INTEGER*4 NARINC         ! Number of ARINC
        INTEGER*4 Ptr            ! Pointer to DMC info
        INTEGER*4 Sptr           ! Pointer in HOSTMEM ARINC section
C
        INCLUDE 'dmcspec.inc' !NOFPC
C
        TOTASCFRAME = 0
        FStatus = 0
        NARINC  = HOSTMEM( HOSTMEM(3)/4 ) ! Get # of DMC
        Ptr     = HOSTMEM(3)/4 + 1        ! Pointers to DMC info
C
        TOTASCFRAME = NARINC
        DO I=1, NARINC
           SPTR = HOSTMEM( Ptr )/4
           ARINP( 1,I,5 ) = HOSTMEM( SPTR  )      ! DMC #
     &                    + HOSTMEM( SPTR+6)*256
           ARINP( 2,I,5 ) = HOSTMEM( SPTR+3 )     ! Start of ASCB I/P
           Ptr = Ptr + 1
        END DO
        CALL DMC1_ADDIP(2*TOTASCFRAME)
        TOTIFRAME = TOTIFRAME + TOTASCFRAME
        RETURN
        END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_ARIIP SUBROUTINE      #               :
C  :               #      Build ARINC input table     #               :
C  :               ####################################               :
C  :..................................................................:
C
C
        SUBROUTINE DMC1_ARIIP
        IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to build enough input buffer
C     to receive all ARINC input packets expected.
C
C     In input  HOSTMEM
C
C     In output Iptr and InputList updated by DMC1_ADDIP
C               ARINP data structure that contains the transfer address
C               for data coming from any DMC
C
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C
C'Subroutines_called
C
C     None
C
        INCLUDE 'dmcfg1.inc' !NOFPC
        INCLUDE 'host1.inc' !NOFPC
C
        INTEGER*4 I
        INTEGER*4 NARINC         ! Number of ARINC
        INTEGER*4 NEXTDMC        ! Pointer to ARINC section
        INTEGER*4 Sptr           ! Pointer in HOPSTMEM ARINC section
C
        INCLUDE 'dmcspec.inc' !NOFPC
C
        TOTARIFRAME = 0
        FStatus = 0
        NARINC  = HOSTMEM( HOSTMEM(2)/4 )
        NEXTDMC = HOSTMEM(2)/4 + 1     ! Pointer to first ARINC chassis info
C
        TOTARIFRAME = NARINC                    ! Save number of expected
        DO I=1, NARINC                          !    ARINC packets
           SPTR           = NEXTDMC + 4         ! Pointer to chassis information
           ARINP( 1,I,4 ) = HOSTMEM( SPTR )     ! DMC #
           ARINP( 2,I,4 ) = HOSTMEM( SPTR+3 )   ! Start of SCS I/P
           NEXTDMC        = HOSTMEM( SPTR+5 )/4
        END DO
        CALL DMC1_ADDIP(2*TOTARIFRAME)            ! Add a new receive block
        TOTIFRAME = TOTIFRAME + TOTARIFRAME
        RETURN
        END
C
C  ....................................................................
C  :               ####################################               :
C  :               #       DMC1_ADDIP SUBROUTINE       #               :
C  :               #      Build 2 receive buffers     #               :
C  :               ####################################               :
C  :..................................................................:
C
C
        SUBROUTINE DMC1_ADDIP(Number)
        IMPLICIT NONE
C
C
C'Purpose
C
C       The purpose of this routine is to build the data structure
C     that will defined an interface input blocks to receive the
C     data from the interface
C
C     In input  Number : Number of input frame to build.
C
C               Iptr , points to the next InputList entry.
C               PktPtr , points to the next available Input Buffer in HOSTMEM
C               InputList , List of input buffers.
C
C     In output Iptr, PktPtr and InputList updated.
C
C
C'Include
C
C     INCLUDE 'DMCFG1.INC'
C     INCLUDE 'HOST.INC'
C     INCLUDE 'DMCSPEC.INC'
C
C'Subroutines_called
C
C     DMCFG1GERR described internally
C     LOC(")    : return the address of "
C
        INCLUDE 'dmcfg1.inc' !NOFPC
        INCLUDE 'host1.inc' !NOFPC
C
        INTEGER*4 Number        ! Number of input frame to add.
        INTEGER*4 I
C
        INCLUDE 'dmcspec.inc' !NOFPC
C
        IF (Number .LT. 1) RETURN
        DO I=1,Number
C
C  Check for memory partition too small
C
           IF ((PktPtr+380) .GT. HMEMDIM) THEN
              FStatus = 15
              CALL DMCFG1ERR(FStatus, ' ')
              RETURN
           END IF
C
C  Only to satisfy VAX Ethernet protocol, a SSAPDSAP flag
C  has to be in the receive buffer, for other system it is
C  ignored.
C
           HOST2MEM((2*PktPtr)+7)= SSAPDSAP
C
           InputList(Iptr)   = LOC(HOSTMEM(PktPtr))
           InputList(Iptr+1) = 1500 + ETH_HEADSIZE
           InputList(1)      = InputList(1) + 1
           Iptr              = MIN( Iptr+2 , MAX_IPTR )
           PktPtr            = PktPtr + 380       ! Skip to next frame
        END DO
        RETURN
        END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMC1_LOGN SUBROUTINE       #               :
C  :               #       Decode logical names       #               :
C  :               ####################################               :
C  :..................................................................:
C
      SUBROUTINE DMC1_LOGN(RUN_INTERFACE           ! CAE_RUN_INTERFACE_1
     -,                 DMC_TYPES                ! CAE_DMC1_TYPE 1,2,3,4
     -,                 DMC_PORTS                ! CAE_DMC1_PORT 1,2,3,4
     -,                 EC_ADDRS                 ! CAE_DMC1_ADDR 1,2,3,4
     -,                 DMC_RETRY                ! CAE_DMC_RETRY
     -,                 DMC_TIMOUT               ! CAE_DMC_TIMOUT
     -,                 INPUT_WAITTIME           ! CAE_INPUT_WAITTIME
     -,                 DMC_DAT                  ! CAE_DMC1_DAT
     -,                 SCS_MDF                  ! CAE_SCS1_MDF
     -,                 ASC_MDF                  ! CAE_ASC1_MDF
     -,                 MEMORY_REPEATER          ! CAE_MEMORY_REPEATER
     -,                 OLD_EPROM                ! CAE_OLD_EPROM
     -,                 STD_BASE                 ! CAE_STD1_BASE
     -,                 ARI_BASE                 ! CAE_ARI1_BASE
     -,                 ASCB_BASE                ! CAE_ASCB1_BASE
     -,                 NUCLEAR                  ! CAE_NUCLEAR
     -,                 INPUT_RATE               ! CAE_INPUT1_RATE
     -,                 SIMLOAD                  ! CAE_SIMLOAD
     -,                 CONC_CHANGE )            ! Change of concentrator
C
      IMPLICIT NONE
C
C
C'Purpose
C
C         This routine decodes all the logical names used by DMCFG1
C
C'Include
C
C     None
C
C'Subroutines_called
C
C     INTEGER*4 FUNCTION CAE_TRNL( LOGNAM, LENGTH, EQUNAM, LEVEL)
C
C     Where   CAE_TRNL is an integer*4 function.
C             LOGNAM   is a  CHARACTER variable wich specifies the
C                                      logical name.
C             LEN      is an INTEGER*4 variable that return the
C                                      equivalence name length
C             EQUNAM   is a  CHARACTER variable containing the equivalent
C                                      name.
C             LEVEL    is an INTEGER*4 variable containing the level.
C
C
      INTEGER*4    MAX_ECC
      PARAMETER   (MAX_ECC = 4)            !
C
      LOGICAL*4     RUN_INTERFACE           !CAE_RUN_INTERFACE_1
      CHARACTER*255 DMC_PORTS(MAX_ECC)      !CAE_DMC1_PORT 1,2,3,4
      CHARACTER*6   EC_ADDRS(MAX_ECC)       !CAE_EC_ADDRS 1,2,3,4
      CHARACTER*255 DMC_TYPES(MAX_ECC)      !CAE_DMC1_TYPE 1,2,3,4
C
      INTEGER*4     DMC_RETRY               !CAE_DMC_RETRY
      INTEGER*4     DMC_TIMOUT              !CAE_STD1_OP_DELAY
      INTEGER*4     INPUT_WAITTIME          !CAE_INPUT_WAITTIME
      CHARACTER*255 DMC_DAT                 !CAE_DMC1_DAT
      CHARACTER*255 SCS_MDF                 !CAE_SCS1_MDF
      CHARACTER*255 ASC_MDF                 !CAE_ASC1_MDF
      LOGICAL*4     MEMORY_REPEATER         !CAE_MEMORY_REPEATER
      LOGICAL*4     OLD_EPROM               !CAE_OLD_EPROM
      INTEGER*4     STD_BASE                !Standard IF CDB base
      INTEGER*4     ARI_BASE                !Standard IF CDB base
      INTEGER*4     ASCB_BASE               !Standard IF CDB base
      LOGICAL*4     NUCLEAR                 !Nuclear site?
      INTEGER*4     INPUT_RATE              !input rate in use
      LOGICAL*4     SIMLOAD                 !SIMLOAD logic in use?
      LOGICAL*4     CONC_CHANGE             !Concentrator change?
C
      INTEGER*4    CAE_TRNL,CNUMBER
      INTEGER*4    LEVEL/0/                 !CAE_TRNL logical name level
      INTEGER*4    FStatus,Status
C
      CHARACTER*255  DMCGLOB
      INTEGER*4      I,J
      CHARACTER*6    EC_ADDR
      INTEGER*4      EC

C
      CHARACTER*14 PORT_LN(MAX_ECC)        ! CAE_DMC1_PORT1,2,3,4 logical name
      CHARACTER*14 ADDR_LN(MAX_ECC)        ! CAE_DMC1_ADDR1,2,3,4 logical name
      CHARACTER*14 TYPE_LN(MAX_ECC)        ! CAE_DMC1_TYPE1,2,3,4 logical name
C
      INCLUDE 'dmcspec.inc' !NOFPC
C
      DATA TYPE_LN(1) /'CAE_DMC1_TYPE1'/
      DATA TYPE_LN(2) /'CAE_DMC1_TYPE2'/
      DATA TYPE_LN(3) /'CAE_DMC1_TYPE3'/
      DATA TYPE_LN(4) /'CAE_DMC1_TYPE4'/
C
      DATA PORT_LN(1) /'CAE_DMC1_PORT1'/
      DATA PORT_LN(2) /'CAE_DMC1_PORT2'/
      DATA PORT_LN(3) /'CAE_DMC1_PORT3'/
      DATA PORT_LN(4) /'CAE_DMC1_PORT4'/
C
      DATA ADDR_LN(1) /'CAE_DMC1_ADDR1'/
      DATA ADDR_LN(2) /'CAE_DMC1_ADDR2'/
      DATA ADDR_LN(3) /'CAE_DMC1_ADDR3'/
      DATA ADDR_LN(4) /'CAE_DMC1_ADDR4'/
C
C
C  ### CAE_RUN_INTERFACE_1 ###
C
C  Check if only a LOAD of the interface is desired
C
      RUN_INTERFACE = .TRUE.
      Status = CAE_TRNL( 'CAE_RUN_INTERFACE_1', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         IF ((DMCGLOB(1:2) .EQ. 'NO').OR.
     &       (DMCGLOB(1:2) .EQ. 'no').OR.
     &       (DMCGLOB(1:2) .EQ. 'LOAD').OR.
     &       (DMCGLOB(1:2) .EQ. 'load').OR.
     &       (DMCGLOB(1:2) .EQ. 'OFF').OR.
     &       (DMCGLOB(1:2) .EQ. 'off')) THEN
            RUN_INTERFACE = .FALSE.
            RETURN
         END IF
      END IF
C
C     Decode the logical name CAE_DMC1_TYPEX    X=1,2,3,4
C     If the logical name exist, look for CAE_DMC1_ADDRX and
C     for CAE_DMC1_PORTX
C
C
      DO EC = 1, MAX_ECC
C
C     ### CAE_DMC1_TYPEx ###
C
C        Get interface type
C
         Status = CAE_TRNL( TYPE_LN(EC), I, DMC_TYPES(EC), LEVEL )
         CALL LOWUPCASE(DMC_TYPES(EC),I)
         IF (Status .NE. 1) THEN
            IF (EC .EQ. 1) THEN     ! The first one CAE_TYPE1 must exist
               FStatus = 41         ! If not, send an error and exit
               CALL DMCFG1ERR(FStatus,TYPE_LN(1))
               FStatus = 47
               CALL DMCFG1ERR(FStatus,
     +              'CAE_DMC1_TYPE1, CAE_DMC1_ADDR1,'//
     +              'CAE_DMC1_PORT1, and CAE_DMC1_DAT')
               RETURN
            ELSE
               DMC_TYPES(EC) = ' '
            END IF
         ELSE
C
C        ### CAE_DMC1_PORTx ###
C
C           Get device files
C
            Status = CAE_TRNL( PORT_LN(EC), I, DMC_PORTS(EC), LEVEL )
            IF (Status .NE. 1) THEN
               FStatus = 41
               CALL DMCFG1ERR(FStatus,PORT_LN(EC))
               FStatus = 47
               CALL DMCFG1ERR(FStatus,
     +              'CAE_DMC1_TYPE1, CAE_DMC1_ADDR1,'//
     +              ' CAE_DMC1_PORT1, and CAE_DMC1_DAT')
               RETURN
            END IF
C
C        ### CAE_EC_ADDRx ###
C
C           Get Ethernet controller address
C
            Status = CAE_TRNL( ADDR_LN(EC), I, DMCGLOB, LEVEL )
            IF (Status .NE. 1) THEN
               FStatus = 41
               CALL DMCFG1ERR(FStatus,ADDR_LN(EC))
               FStatus = 47
               CALL DMCFG1ERR(FStatus,
     +              'CAE_DMC1_TYPE1, CAE_DMC1_ADDR1,'//
     +              'CAE_DMC1_PORT1, and CAE_DMC1_DAT')
               RETURN
            ELSE
C
C              Validate the address received
C
               IF (I .EQ. 6) THEN
                  IF (DMCGLOB(1:1) .EQ. '0') THEN  ! 0A0XAV  010DLG 010XNU
                     IF (INDEX(DMCGLOB(1:I),'DLG') .GT. 0 .OR.
     +                   INDEX(DMCGLOB(1:I),'XAV') .GT. 0 .OR.
     +                   INDEX(DMCGLOB(1:I),'XNU') .GT. 0) THEN
                         CETHADDR(1:6) = DMCGLOB(1:6)
                     ELSE
                         FStatus = 22
                         CALL DMCFG1ERR(FStatus,DMCGLOB)
                         RETURN
                     END IF
                  ELSE IF (DMCGLOB(6:6) .EQ. '0') THEN ! VAX0A0 GLD010 UNX010
                     IF (INDEX(DMCGLOB(1:I),'GLD') .GT. 0 .OR.
     +                   INDEX(DMCGLOB(1:I),'VAX') .GT. 0 .OR.
     +                   INDEX(DMCGLOB(1:I),'UNX') .GT. 0) THEN
C
C                        Change EC address string order for
C                        0A0XAV or 010DLG or 010XNU
C
                         DO I=1,6
                            CETHADDR((7-I):(7-I)) = DMCGLOB(I:I)
                         END DO
                     ELSE
                         FStatus = 22
                         CALL DMCFG1ERR(FStatus,DMCGLOB)
                         RETURN
                     END IF
                  ELSE
                     FStatus = 22
                     CALL DMCFG1ERR(FStatus,DMCGLOB)
                     RETURN
                  END IF
               ELSE
                  DO I=1, 6, 2
                    J = 3*I-2
                    READ(DMCGLOB(J:J+1),'(Z2)',ERR=39) I1ETHADDR(I)
                    READ(DMCGLOB(J+3:J+4),'(Z2)',ERR=39)I1ETHADDR(I+1)
                    GOTO 38
  39                   FStatus = 22
                       CALL DMCFG1ERR(FStatus,DMCGLOB)
                       RETURN
  38                CONTINUE
                  END DO
               END IF
               EC_ADDRS(EC) = CETHADDR
            END IF
         END IF
      END DO
C
C  ### CAE_DMC_RETRY ###
C
C  Get off-line DMC retry period
C
      Status = CAE_TRNL( 'CAE_DMC_RETRY', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         DMC_RETRY = CNUMBER( DMCGLOB, I )
         IF (DMC_RETRY .GT. 30) THEN
            FStatus = 28
            CALL DMCFG1ERR(FStatus,' ')
            RETURN
         END IF
      ELSE
         DMC_RETRY = 0
      END IF
C
C  ### CAE_DMC_TIMOUT ###
C
C  Get DMC timout value
C
      Status = CAE_TRNL( 'CAE_DMC_TIMOUT', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         DMC_TIMOUT = CNUMBER( DMCGLOB, I )
         IF (DMC_TIMOUT .GT. 50) THEN
            FStatus = 25
            CALL DMCFG1ERR(FStatus,' ')
            RETURN
         END IF
      ELSE
C
C     Default
C
         DMC_TIMOUT = 24
      END IF
      DMC_TIMOUT = DMC_TIMOUT*256
C
C
C  ### CAE_INPUT_WAITTIME ###
C
C  Figure out the INPUT wait time
C
      Status = CAE_TRNL( 'CAE_INPUT_WAITTIME', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         INPUT_WAITTIME = CNUMBER( DMCGLOB, I )
         IF (INPUT_WAITTIME .GT. 33) THEN
            FStatus = 24
            CALL DMCFG1ERR(FStatus,' ')
            RETURN
         END IF
      ELSE
         INPUT_WAITTIME = 0
      END IF
C
C   ### CAE_DMC1_DAT ###
C
C  Get DMC data file
C
      Status = CAE_TRNL( 'CAE_DMC1_DAT', I, DMC_DAT, LEVEL )
      IF (Status .NE. 1) THEN
         FStatus = 41
         CALL DMCFG1ERR(FStatus,'CAE_DMC1_DAT')
         FStatus = 47
         CALL DMCFG1ERR(FStatus,
     +        'CAE_DMC1_TYPE1, CAE_DMC1_ADDR1,'//
     +        ' CAE_DMC1_PORT1, and CAE_DMC1_DAT')
         RETURN
      END IF
C
C  ### CAE_SCS1_MDF ###
C
C  Get SCS data file
C
      Status = CAE_TRNL( 'CAE_SCS1_MDF', I, SCS_MDF, LEVEL )
      IF (Status .NE. 1) THEN
         SCS_MDF = ' '
      END IF
C
C  ### CAE_ASC1_MDF ###
C
C  Get ASC data file
C
      Status = CAE_TRNL( 'CAE_ASC1_MDF', I, ASC_MDF, LEVEL )
      IF (Status .NE. 1) THEN
         ASC_MDF = ' '
      END IF
C
C  ### CAE_MEMORY_REPEATER ###
C
C  Is MR based simulator
C
      MEMORY_REPEATER = .FALSE.
      Status = CAE_TRNL( 'CAE_MEMORY_REPEATER', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         IF (DMCGLOB(1:I).EQ.'ON' .OR. DMCGLOB(1:I).EQ.'YES' .OR.
     +       DMCGLOB(1:I).EQ.'on' .OR. DMCGLOB(1:I).EQ.'yes' ) THEN
            MEMORY_REPEATER = .TRUE.
         END IF
      END IF
C
C  ### CAE_OLD_EPROM ###
C
C  Is simulator with old DMC Interface EPROM
C
      Status = CAE_TRNL( 'CAE_OLD_EPROM', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         OLD_EPROM = .TRUE.
      ELSE
         OLD_EPROM = .FALSE.
      END IF
C
C  ### CAE_STD1_BASE ###
C
C  Standard interface is in wich base
C
      Status = CAE_TRNL( 'CAE_STD1_BASE', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         IF (I .EQ. 1) THEN
            STD_BASE = CNUMBER( DMCGLOB, 1 )
            IF (STD_BASE .EQ. -1) THEN
               FStatus = 40
               CALL DMCFG1ERR(FStatus, 'CAE_STD1_BASE')
               RETURN
            END IF
         ELSE
            FStatus = 40
            CALL DMCFG1ERR(FStatus, 'CAE_STD1_BASE')
            RETURN
         END IF
      ELSE
         STD_BASE = 0
      END IF
C
C  ### CAE_ARI1_BASE ###
C
C  Standard interface is in wich base
C
      Status = CAE_TRNL( 'CAE_ARI1_BASE', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         IF (I .EQ. 1) THEN
            ARI_BASE = CNUMBER( DMCGLOB, 1 )
            IF (ARI_BASE .EQ. -1) THEN
               FStatus = 40
               CALL DMCFG1ERR(FStatus, 'CAE_ARI1_BASE')
               RETURN
            END IF
         ELSE
            FStatus = 40
            CALL DMCFG1ERR(FStatus, 'CAE_ARI1_BASE')
            RETURN
         END IF
      ELSE
         ARI_BASE = 0
      END IF
C
C  ### CAE_ASCB1_BASE ###
C
C  Standard interface is in wich base
C
      Status = CAE_TRNL( 'CAE_ASCB1_BASE', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         IF (I .EQ. 1) THEN
            ASCB_BASE = CNUMBER( DMCGLOB, 1 )
            IF (ASCB_BASE .EQ. -1) THEN
               FStatus = 40
               CALL DMCFG1ERR(FStatus, 'CAE_ASCB1_BASE')
               RETURN
            END IF
         ELSE
            FStatus = 40
            CALL DMCFG1ERR(FStatus, 'CAE_ASCB1_BASE')
            RETURN
         END IF
      ELSE
         ASCB_BASE = 0
      END IF
C
C  ### CAE_NUCLEAR ###
C
C  Is this a nuclear simulator
C
      Status = CAE_TRNL( 'CAE_NUCLEAR', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         NUCLEAR = .TRUE.
      ELSE
         NUCLEAR = .FALSE.
      END IF
C
C  ### CAE_INPUT1_RATE ###
C
C  Does the inputs running each iteration or not?
C
      Status = CAE_TRNL( 'CAE_INPUT1_RATE', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         INPUT_RATE = CNUMBER( DMCGLOB, I )
         IF ((INPUT_RATE .NE. 1)  .AND.
     .       (INPUT_RATE .NE. 2)  .AND.
     .       (INPUT_RATE .NE. 4)  .AND.
     .       (INPUT_RATE .NE. 8)) THEN
            FStatus = 49
            CALL DMCFG1ERR(FStatus,' ')
            RETURN
         END IF
      ELSE
         INPUT_RATE = 1
      END IF
C
C  ### CAE_SIMLOAD ###
C
C  Is SIMLOAD logic active
C
      SIMLOAD = .FALSE.
      Status = CAE_TRNL( 'CAE_SIMLOAD', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         IF (DMCGLOB(1:I).EQ.'ON' .OR. DMCGLOB(1:I).EQ.'YES' .OR.
     +       DMCGLOB(1:I).EQ.'on' .OR. DMCGLOB(1:I).EQ.'yes' ) THEN
            SIMLOAD = .TRUE.
         END IF
      END IF
C
C  ### CAE_CONC_CHANGE ###
C
C  Do we change concentrator on input error
C
      Status = CAE_TRNL( 'CAE_CONC_CHANGE', I, DMCGLOB, LEVEL )
      IF (Status .EQ. 1) THEN
         CONC_CHANGE = .TRUE.
      ELSE
         CONC_CHANGE = .FALSE.
      END IF
      RETURN
      END
C
C  ....................................................................
C  :               ####################################               :
C  :               #        DMCFG1ERR SUBROUTINE       #               :
C  :               # Send error messages to std.output#               :
C  :               ####################################               :
C  :..................................................................:
C
C
C'Purpose
C               This routine send to the standard output the error
C               message corresponding to the given error number.
C
C'Include file
C
C    None
C
C'Subroutines_called
C
C    None
C
      SUBROUTINE DMCFG1ERR( ErrorNum, Message)
C
      IMPLICIT NONE
C
      INTEGER*4     ErrorNum       ! Error number
      CHARACTER*(*) Message        ! Message to display with error
C
      INTEGER*4     Err_Max        ! Maximum number of errors
      PARAMETER    (Err_Max=50)
      CHARACTER*45  Error(Err_Max) ! Error messages list
      CHARACTER*80  Display        ! Message to be displayed
C
      INTEGER*4     STOP /0/       ! Stop if this error occurred
      INTEGER*4     LG             ! Length function
      LOGICAL*4     NORUN
      CHARACTER*80  PHASE          ! DMCDISP execution phase
      INTEGER*4     IPHASE(20)     !
      EQUIVALENCE  (IPHASE(1),PHASE)
C
      COMMON   /ERR1_COM/ NORUN
      COMMON   /PHASE_COM/ IPHASE
C
C     List of errors
C
      DATA
     -Error(1) /'W- Some INPUT frames have been lost.         '/,
     -Error(2) /'E- Host data not loaded in memory.           '/,
     -Error(3) /'E- Too many Ethernet Controller opened.      '/,
     -Error(4) /'E- Internal error, code corruption.          '/,
     -Error(5) /'W- Previous I\O completed with error.        '/,
     -Error(6) /'E- Cannot build Eth. Output frames.          '/,
     -Error(7) /'W- No INPUT frames have been received.       '/,
     -Error(8) /'W- Error doing I\P from the EC.              '/,
     -Error(9) /'E- Error trying to map HOSTMEM.              '/,
     -Error(10)/'W- Error doing O\P to the EC.                '/,
     -Error(11)/'E- Cannot build Eth. receive buffer.         '/,
     -Error(12)/'E- CONCENTRATOR DMC not responding.          '/,
     -Error(13)/'E- Invalid or incompatible hostmem data      '/,
     -Error(14)/'W- ARINC input frame missing                 '/,
     -Error(15)/'E- HOSTMEM too small for host data.          '/,
     -Error(16)/'E- Invalid table of change in Host data.     '/,
     -Error(17)/'W- Garbage frame received.                   '/,
     -Error(18)/'E- ARINC mapping data not suitable.          '/,
     -Error(19)/'E- Down load data for more then 4 ARINCS.    '/
      DATA
     -Error(20)/'W- ARINC I\O error.                          '/,
     -Error(21)/'E- Too many interface blocks in CDB.         '/,
     -Error(22)/'E- Invalid CAE_DMC1_ADDRx address.           '/,
     -Error(23)/'E- Cannot determine host`s receiving EC.     '/,
     -Error(24)/'E- Invalid INPUT wait time.                  '/,
     -Error(25)/'E- Invalid CAE_DMC_TIMOUT value.             '/,
     -Error(26)/'E- Incompatible compare table in HOSTMEM     '/,
     -Error(27)/'E- DMC_CONFIG logical name invalid.          '/,
     -Error(28)/'E- Invalid DMC retry period.                 '/,
     -Error(29)/'E- Too many output frames.                   '/,
     -Error(30)/'E- Too many input frames.                    '/,
     -Error(31)/'E- Error Logger or CCU label corrupted.      '/,
     -Error(32)/'E- Invalid output leg number.                '/,
     -Error(33)/'I- Ethernet controller device CAE_DMC1_PORTx '/,
     -Error(34)/'E-                                           '/,
     -Error(35)/'E- Open error on Ethernet controller.        '/,
     -Error(36)/'E- Cannot lock scratch pad memory.           '/,
     -Error(37)/'E- EC_INPUT internal error.                  '/,
     -Error(38)/'E- Host data not properly aligned.           '/,
     -Error(39)/'E- Compare table too big or corrupted.       '/
      DATA
     -Error(40)/'E- Logical name having wrong value.          '/,
     -Error(41)/'E- Logical name missing:                     '/,
     -Error(42)/'E- Cannot open Standard interface data file: '/,
     -Error(43)/'E- Too much interface data.                  '/,
     -Error(44)/'E- Error reading Standard interface datafile:'/,
     -Error(45)/'E- Cannot open ARINC interface data file:    '/,
     -Error(46)/'E- Error reading ARINC data file:            '/,
     -Error(47)/'I- Compulsary logical names are:             '/,
     -Error(48)/'E- Error reading ASCB data file:             '/,
     -Error(49)/'E- Invalid CAE_INPUT1_RATE use 1, 2, 4 or 8  '/,
     -Error(50)/'E- DMC Dispatcher stops.                     '/
C
C    -----------------------------------------------------------------
C
C     If the error massage is a warning or an error display in which
C     phase of execution in the dmcdisp the error occured
C
      IF ( Error(ErrorNum)(1:1) .NE. 'I') THEN
         DISPLAY(1:36) = 'I- Error encountered during the  ...'
         IF (LG(PHASE) .GT. 37) THEN
            WRITE(6,99) DISPLAY(1:36)
            WRITE(6,99) 'I- '//PHASE(1:LG(PHASE))
         ELSE
            WRITE(6,99) DISPLAY(1:32),PHASE(1:LG(PHASE))
         END IF
      END IF
C
C     Send the appropriate message
C
      IF (ErrorNum .GT. 0 .AND. ErrorNum .LT. Err_Max) THEN
         IF (LG(Message) .LT. 25) THEN
            Display(1:70) = Error(ErrorNum)//Message
            WRITE(6,99) Display(1:79)
         ELSE
            Display(1:45) = Error(errorNum)
            WRITE(6,99) Display(1:45)
            WRITE(6,99) 'I- ',Message(1:LG(Message))
         END IF
C
C        Stop DMCDISP if the message is an error
C
         IF ( Error(ErrorNum)(1:1) .EQ. 'E') THEN
            NORUN = .TRUE.
            Display(1:45) = Error(50)
            WRITE(6,99) Display(1:45),' '
         END IF
C
C        To debug, set the variable STOP to the error corresponding
C
         IF (ErrorNum .EQ. STOP) THEN
            NORUN = .TRUE.
         END IF
      ELSE
C
C        If error message number greater then Err_Max, display the
C        message string.
C
         DISPLAY(1:70) = 'I- '//Message
         WRITE (6,99) DISPLAY
      END IF
   99 FORMAT('%DMCDISP1-',A,A)
      RETURN
      END
C
C
C  ....................................................................
C  :               ####################################               :
C  :               #        LEGMASK1 FUNCTION          #               :
C  :               ####################################               :
C  :..................................................................:
C
      INTEGER FUNCTION LEGMASK1(IF_Type, Leg)
      IMPLICIT NONE
C
C'Purpose
C        This function return a leg mask that is used to build the
C    output table. The leg mask determines on wich legs the packet
C    will be transmitted.
C
C    Ex:   LEGMASK1 = 00000000000000000000000000001001
C                                                |  +--> leg #1
C                                                +-----> leg #4
C          This mask send the packet for leg #1 and leg #4
C
C'Include file
C
C    None
C
C'Subroutines_called
C
C    None
C
C
      INTEGER*4 IF_Type             ! Interface type subcode 0=STD 1=ARI
                                    !                       -1=IRQ
      INTEGER*4 STANDARD
      PARAMETER (STANDARD = 0)
      INTEGER*4 ARINC
      PARAMETER (ARINC    =  1)
      INTEGER*4 INPREQ
      PARAMETER (INPREQ   = -1)
C
      INTEGER*4 LEG                 !                       -1=IRQ
      INTEGER*4 MASK(0:32)          ! Leg mask
     &          /'FFFFFFFF'X,'FFFFFFFF'X,'55555555'X,'00000000'X, ! 0  1  2  3
     &           '11111111'X,'00000000'X,'00000000'X,'00000000'X, ! 4  5  6  7
     &           '01010101'X,'00000000'X,'00000000'X,'00000000'X, ! 8  9  10 11
     &           '00000000'X,'00000000'X,'00000000'X,'00000000'X, ! 12 13 14 15
     &           '00010001'X,'00000000'X,'00000000'X,'00000000'X, ! 16 17 18 19
     &           '00000000'X,'00000000'X,'00000000'X,'00000000'X, ! 20 21 22 23
     &           '00000000'X,'00000000'X,'00000000'X,'00000000'X, ! 24 25 26 27
     &           '00000000'X,'00000000'X,'00000000'X,'00000000'X, ! 28 29 30 31
     &           '00000001'X/                                     ! 32
C
      INTEGER*4 SHIFT(0:32)
     &          /'00000001'X,'00000001'X,'00000002'X,'00000004'X,
     &           '00000008'X,'00000010'X,'00000020'X,'00000040'X,
     &           '00000080'X,'00000100'X,'00000200'X,'00000400'X,
     &           '00000800'X,'00001000'X,'00002000'X,'00004000'X,
     &           '00008000'X,'00010000'X,'00020000'X,'00040000'X,
     &           '00080000'X,'00100000'X,'00200000'X,'00400000'X,
     &           '00800000'X,'01000000'X,'02000000'X,'04000000'X,
     &           '08000000'X,'10000000'X,'20000000'X,'40000000'X,
     &           '80000000'X/
C
      INCLUDE 'dmcfg1.inc' !NOFPC
C
      IF (IF_Type .EQ. STANDARD) THEN
         LEGMASK1 = MASK(STDNumLeg(EC))*SHIFT(LEG)
C
      ELSE IF (IF_Type .EQ. ARINC) THEN
         IF (SIMLOAD) THEN
            LEGMASK1 = MASK(ARINumLeg(EC)*2)*SHIFT(LEG)
         ELSE
            LEGMASK1 = MASK(ARINumLeg(EC))*SHIFT(LEG)
         END IF
C
      ELSE IF (IF_Type .EQ. INPREQ) THEN
         LEGMASK1 = MASK(INPUT_RATE*SHIFT(LEG))
C
      ELSE
         LEGMASK1 = MASK(1)
      END IF
      RETURN
      END
C
C
C  ....................................................................
C  :               ####################################               :
C  :               #        CNUMBER FUNCTION          #               :
C  :               ####################################               :
C  :..................................................................:
C
        INTEGER FUNCTION CNUMBER( String, Length )
        IMPLICIT NONE
C
C'Purpose
C        This function convert a string containing a number into
C    an INTEGER number.
C
C'Include file
C
C    None
C
C'Subroutines_called
C
C    None
C
        CHARACTER*80 String
        INTEGER*4 Length

        INTEGER*4 I, J, Local

        local = 0
        DO I=1, Length
           J = ICHAR( String(I:I) ) - 48
           IF ((J .LT. 0) .or. (J .GT. 9)) THEN
               Cnumber = -1
               RETURN
           END IF
           Local = 10*Local + J
        END DO
        Cnumber = Local
        RETURN
        END
C
        BLOCK DATA
        IMPLICIT NONE
C
        LOGICAL*4 NORUN
        COMMON    /ERR1_COM/  NORUN
C
        DATA NORUN / .FALSE. /
        END
