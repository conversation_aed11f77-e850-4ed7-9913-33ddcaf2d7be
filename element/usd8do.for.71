C'Module_ID             USD8DO
C'SDD_#
C'Customer              USAIR
C
C'Application           Simulation of the DASH8 Air Conditioning
C                       System Dynamics
C'Author                J<PERSON> <PERSON><PERSON><PERSON>                       (<PERSON><PERSON>)
C'Date                  July 1991
C
C'System                ECS (Environmental Control Systems)
C'Iteration_Rate        266 msec
C'Process               Synchronous
C
C ----------------
C'Revision_History
C
C* USD8DO.FOR;001   18Nov1992 09:04 USD8 MM
C    ADDED CHANGES TO MALF DEFINITIONS FROM FADY'S CHANGES
C
C
C  usd8do.for.18 10Nov1992 22:59 usd8 Steve W
C       < added changes to J200 from fax from Fady Naccache >
C
C  usd8do.for.17  6Nov1992 21:34 usd8 MM
C       < REMOVED TF21201/02 FROM EQUALITY WITH DOZTI AND EQUALLED TO
C         DOZAI TO HAVE CORRECT MALFUNCTION EFFECTS >
C
C  usd8do.for.16  6Nov1992 03:19 usd8 MM
C       < COMMENTED DOSRI(1) = IDDOSRI(2) AT END OF INIT AND PUT IT IN 100
C         OPTION >
C
C  usd8do.for.15  6Nov1992 00:55 usd8
C       <   >
C
C  usd8do.for.14  6Nov1992 00:45 usd8
C       <   >
C
C  usd8do.for.13  6Nov1992 00:44 usd8 mm
C       < equalled dobfi(1) to bira06 for fc comp fan cont cb (300) >
C
C  usd8do.for.12  4May1992 09:50 usd8 jgb
C       < corrected constant dkcd10 from 202 to 192 to increase hysterisis
C         on overheat ( snag # 1051 )  >
C
C  usd8do.for.11 27Apr1992 00:58 usd8 JGB
C       < CORRECTED DOWD HIGH AND LOW LIMITS >
C
C  usd8do.for.10 11Feb1992 09:35 usd8 jgb
C       < corrected compilation error >
C
C  usd8do.for.9 11Feb1992 07:38 usd8 j.bilod
C       < corrected compilation error >
C
C  usd8do.for.8 11Feb1992 07:28 usd8 J.BILOD
C       < PUT BACK DO$K*L AND IADOVFL, VFR  >
C
C  usd8do.for.7  4Feb1992 17:09 usd8 j.bilod
C       < deleted eq. m200 to alloy divert valve to be driven in /100a
C         config >
C
C  usd8do.for.6  4Feb1992 17:06 usd8 jgb
C       < corrected compaling error >
C
C  usd8do.for.5  4Feb1992 16:52 usd8 ehtisha
C       < comment out DO$KCL R KOL KOR >
C
C  usd8do.for.4  4Feb1992 16:42 usd8 jgb
C       < corrected compaling errors >
C
C  usd8do.for.3  4Feb1992 16:39 usd8 J.BILOD
C       < ADDED NEW CODE FOR COMFORT VALVES  >
C
C
C ----------------------
C'Compilation_Directives
C ----------------------
C
C
C'
C ------------------------
C'Include_Files_Directives
C ------------------------
C
C
C'
C -----------
C'Description
C -----------
C
C
C
C ----------
C'References
C ----------
C
C     The list of references is given in the PPS and
C     PDS documents relative to this project.
C
      SUBROUTINE USD8DO
C
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C'
C -------------
C'Include_Files
C -------------
C
      INCLUDE 'disp.com'  !NOFPC
C
C
C'
C ------------------
C'Subroutines_Called
C ------------------
C
C
C --------------------------
C'Common_Data_Base_Variables
C --------------------------
C
C
C     ---------------------------------------------------------
C
C                 C O M M O N    D A T A    B A S E
C
C     ---------------------------------------------------------
C
C
C
C     ---------
C      QMR bus
C     ---------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ         XRFTEST5,XRFTEST6
C
C
CE    LOGICAL*1  DOBAI(2)   , !E- F COMP / CABIN TEMP AUTO PWR   BIRN05/BILE04
CE    LOGICAL*1  DOBE       , !E- AVIONICS FAN CB                BIAN03
CE    LOGICAL*1  DOBF       , !E- FC FAN PWR                     BIRD05
CE    LOGICAL*1  DOBFD      , !E- FC FAN CONT                    BIRE05
CE    LOGICAL*1  DOBFI(2)   , !E- RECIRC FAN CONT                ???/BILN06
CE    LOGICAL*1  DOBMI(2)   , !E- F COMP / CABIN TEMP MAN PWR    BILS06/BIRB06
CE    LOGICAL*1  DOBRI(2)   , !E- RECIRC FAN PWR                 BIRA10/BILS10
CE    LOGICAL*1  DOBXL      , !E- BLEED SYS FLOW CONT            BILQ05
CE    LOGICAL*1  DOFFD      , !E- GND PROVIDED WITH WEIGHT ON    AGFPS65
C                                 BOTH WHEELS
CE    LOGICAL*1  DOKCI(2)    ,!E- COMFORT VALVE CLOSE DOP         DO$KCL/R
CE    LOGICAL*1  DOKOI(2)    ,!E- COMFORT VALVE OPEN DOP          DO$KOL/R
CE    LOGICAL*1  DOZAI(2)   , !E- DISTRIBUTION DUCT OVHT          TF21201
CE    LOGICAL*1  DOZAAI(2)  , !E- TCV FAILS IN POS ( AUTO MODE ONLY )
C                  ZAAI(1) ......... 300 ONLY
C                  ZAAI(2) ......... 300 & 100
CE    LOGICAL*1  DOZTA      , !E- TRIM FAILS IN POS IN AUTO (100 ONLY)
CE    LOGICAL*1  DOZCI(2)   , !E- PROVISION
CE    LOGICAL*1  DOZDI(2)   , !E- ZONE DUCT OVHT (100 ONLY )   TF21122/1
CE    LOGICAL*1  DOZBI(2)   , !E- TCV FAIL IN POSITION
C                  ZBI(1) ......... 300 ONLY
C                  ZBI(2) ......... 300 & 100
CE    LOGICAL*1  DOZFI(2)   , !E- PRSOV FAIL IN POSITION (300 ONLY)
CE    LOGICAL*1  DOZTI(2)   , !E- F CMP / CABIN TEMP CONT FAIL   TF21
C                  ZTI(1) ......... 300 ONLY
C                  ZTI(2) ......... 300 & 100
CE    REAL*4   DOHA         , !E- A/C ALTITUDE               [feet]  VHH
CE    REAL*4   DOVDI(2)     , !E- CONFORT VALVE POSITION             DO$VDL,VDR
CE    REAL*4   DOVFFI(2)    , !E- L/R COMFORT VALVE POSN             IADOVFL/R
C
CE      EQUIVALENCE  (  DOVFFI(1)  , IADOVFL   ),
CE      EQUIVALENCE  (  DOVDI(1)   , DO$VDL    ),
CE      EQUIVALENCE  (  DOFFD      , AGFPS65   ),
CE      EQUIVALENCE  (  DOHA       , VHH       ),
C
CE      EQUIVALENCE  (  DOKCI(1)    , DO$KCL   ),
CE      EQUIVALENCE  (  DOKOI(1)    , DO$KOL   ),
CE      EQUIVALENCE  (  DOZDI(1)    , TF21121  ),
CE      EQUIVALENCE  (  DOBE        , BIAN03   )
C
CP USD8
C
CPO  & DOFE    ,    !E- AVIONICS FAN CMD FLAG
CPO  & DOFF    ,    !E- FLIGHT COMPT CMD FLAG
CPO  & DOFRI   ,    !E- RECIRC FAN CMD FLAG
CPO  & DOFCI   ,    !L- PACK OVERTEMP FLAG
CPO  & DOFAI   ,    !L- DUCT OVERHEAT FLAG
CPO  & DOGA    ,    !E- RETURN TO CENTER RLY  (K6)
CPO  & DOGBI   ,    !E- PRSOV SOLENOID  (K11-K12)
CPO  & DOGMI   ,    !E- AUTO/MANUAL RELAY  (K1-K2)
CPO  & DO$KAI(2)  , !E- F COMP / CABIN HOT LIGHTS
CPO  & DO$KCI(2)  , !E- F COMP / CABIN PACK HOT LIGHTS
CPO  & DO$KCL     , !E-
CPO  & DO$KCR     , !E-
CPO  & DO$KOL     , !E-
CPO  & DO$KOR     , !E-
CPO  & DOTSI   ,    !E- COMPT TEMPERATURE SELECTED                 [deg_C]
CPO  & DOUT    ,    !E- TRIM COMPT SIDE RATE
CPO  & DOVBI   ,    !E- TCV COLD SIDE POSN
CPO  & DOVBLI  ,    !E- TCV COLD SIDE INTEG POSN
CPO  & DOVD    ,    !E- DIVERTER VALUE ROTARY ACT POSN
CPO  & DO$VDL  ,    !E- COMF VALVE DEM POSN
CPO  & DOVFI   ,    !E- PRSOV POSITION
CPO  & DOVFLI  ,    !E- PRSOV INTEGRAL PSN
CPO  & DOVHI   ,    !E- TCV HOT SIDE POSN
CPO  & DOVT    ,    !E- TRIM COMPT ACTUATOR POSN
CPO  & DOVTL   ,    !E- TRIM COMPT ACTUATOR INTEG POSN
CPO  & DOWD    ,    !E- F_DECK AIRFLOW                             [lb/min]
C
CPI  & DOF     ,    !E- COND2 FREEZE FLAG
CPI  & DAPD    ,    !E- PNEU DUCT PRESS ADV                        [psia]
CPI  & DAPAI   ,    !E-                                            [psia]
CPI  & DKXPFI  ,    !E- PRSOV BLEED VALVE PROCESS GAIN 	           [coeff]
CPI  & DKPFAI  ,    !E- PACK INLET DUCT ADV PRESS                  [psia]
CPI  & DKPFI   ,    !E- PACK INLET DUCT PRESS 	                   [psia]
CPI  & DKTCI   ,    !E- COMPR DISCH TEMP                           [deg_C]
CPI  & DNTAAI  ,    !E- PACK DUCT ADV TEMP                         [deg_C]
CPI  & DNTAI   ,    !E- PACK DUCT TEMP                             [deg_C]
CPI  & DNTCAI  ,    !E- ZONE ADV TEMP                              [deg_C]
CPI  & DNTCI   ,    !E- ZONE TEMP                                  [deg_C]
CPI  & DNTDI   ,    !E- ZONE DUCT TEMP                             [deg_C]
CPI  & DNTDAI  ,    !E- ZONE DUCT ADV TEMP                         [deg_C]
CPI  & DNWDI   ,    !E- F DECK / CABIN DUCT AIRFLOW                [lb/min]
CPI  & DNWFD   ,    !E-
CPI  & DTPA    ,    !E- AMBIANT PRESSURE                           [psia]
CPI  & DTPCI   ,    !E- COMPT PRESSURE                             [psia]
CPI  & DZFA    ,    !E- DASH8 100-300 / 100A-300A OPTION  (.T. => A)
CPI  & DZF300  ,    !E- DASH8 100/300 OPTION  (.T. => 300)
CPI  & VHH     ,    !E- A/C ALTITUDE                               [feet]
C
CPI  & AGFPS65 ,    !E- GND PROVIDED WITH WEIGHT ON BOTH WHEELS         DOFFD
CPI  & BIAN03  ,    !E- AVIONICS FAN CB
CPI  & BILQ05  ,    !E- BLEED SYS FLOW CONT
CPI  & BIRB06  ,    !E- CABIN TEMP MAN PWR (100)
CPI  & BIRS06  ,    !E- CABIN TEMP MAN PWR (300)
CPI  & BILS06  ,    !E- FC TEMP MAN PWR (100)
CPI  & BILB08  ,    !E- FC TEMP MAN PWR (300)
CPI  & BIRA06  ,    !E- FC COMP FAN SW CONT (300)
CPI  & BIRE05  ,    !E- FC FAN CONT
CPI  & BILN06  ,    !E- RECIRC FAN CONT
CPI  & BIRD05  ,    !E- FC FAN PWR
CPI  & BILS10  ,    !E- RECIRC FAN PWR
CPI  & BIRA10  ,    !E- RECIRC FAN PWR
CPI  & BILE04  ,    !E- CABIN TEMP AUTO PWR
CPI  & BIRN05  ,    !E- FC TEMP AUTO PWR
C
CPI  & IADBXB     ,    !E- BLEED FLOW CONTROL SELECTOR
CPI  & IADOHSI(2) ,    !E- TEMPERATURE SELECTED SIGNAL             [deg_C]
CPI  & IADOVFL    ,    !E- LEFT COMF VALVE POSN                    [coeff]
CPI  & IADOVFR    ,    !E- RIGHT COMF VALVE POSN                   [coeff]
CPI  & IDDOSFD    ,    !E- FC FAN SW @OFF (300A)
CPI  & IDDOSFI(2) ,    !E- F COMP / CABIN PACK SW @OFF (300/300A)
CPI  & IDDOSA2    ,    !E- CABIN PACK SW @ AUTO (300/300A)
CPI  & IDDOSMI(2) ,    !E- SMI FLT COMP PACK AUTO(300/300A) & MANUAL (100/100A)
C                          SM2 CABIN AT MANUAL (100/100A)
CPI  & IDDOSAC    ,    !E- AIR DIVERTER TO CABIN
CPI  & IDDOSAF    ,    !E- AIR DIVERTER TO FLT COMP
CPI  & IDDOSCI(2) ,    !E- F COMP / CABIN MAN CONT TEMP COOL SW
CPI  & IDDOSRI(2) ,    !E- SRI FC FAN (100/100A)
C                          SR2 RECIRC FAN (100/100A/300/300A)
CPI  & IDDOSFC    ,    !E- RECIRC FC FAN (300A)
CPI  & IDDOSHI(2) ,    !E- F COMP / CABIN MAN CONT TEMP HOT SW
C
CPI  & TF21021    ,    !E- CABIN TEMP CONTROLLER FAIL (100/300)          DOZTI
CPI  & TF21022    ,    !E- F COMP TEMP CONTROLLER FAIL (300 ONLY)        DOZTI
CPI  & TF21121    ,    !E- DISTRIBUTION DUCT OVER CABIN  ( 100A )        DOZDI
CPI  & TF21122    ,    !E- DISTRIBUTION DUCT OVER F DECK ( 100A )        DOZDI
CPI  & TF21201    ,    !E- DISTRIBUTION DUCT OVERHEAT CABIN (300)        DOZTI
CPI  & TF21202         !E- DISTRIBUTION DUCT OVERHEAT F DECK (300)       DOZTI
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:40:09 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DAPAI(2)       ! ANTI-ICE DUCT PRESS                   [psia]
     &, DAPD           ! PNEU DUCT PRESS                       [psia]
     &, DKPFAI(2)      ! PACK INLET DUCT ADV PRESS             [psia]
     &, DKPFI(2)       ! PACK INLET DUCT PRESS                 [psia]
     &, DKTCI(2)       ! COMPR DISCH TEMP                     [deg C]
     &, DKXPFI(2)      ! PRSOV PROCESS GAIN                   [coeff]
     &, DNTAAI(2)      ! PACK DUCT ADV TEMP                   [deg_C]
     &, DNTAI(2)       ! PACK DUCT TEMP                       [deg_C]
     &, DNTCAI(2)      ! ZONE ADV TEMP                        [deg_C]
     &, DNTCI(2)       ! ZONE TEMP                            [deg_C]
     &, DNTDAI(2)      ! ZONE DUCT ADV TEMP                   [deg_C]
     &, DNTDI(2)       ! ZONE DUCT TEMP                       [deg_C]
     &, DNWDI(2)       ! F DECK / CABIN DUCT AIRFLOW         [lb/min]
     &, DNWFD          ! FC FAN FLOW                         [lb/min]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, DTPCI(2)       ! COMPT PRESS                            [psi]
     &, IADBXB         ! FLOW CONTROL SELECTOR         [coeff] AI054
     &, IADOHSI(2)     ! F COMP TEMP SELECTOR           [degC] AI052
     &, IADOVFL        ! COMFORT VALVE POSN L                  AI0077
     &, IADOVFR        ! COMFORT VALVE PSN R                   AI0076
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
C$
      LOGICAL*1
     &  AGFPS65        ! PSEU eq65  [B45] GROUND FAN
     &, BIAN03         ! CLG FAN                     21 PDAL   DI1949
     &, BILB08         ! F/COMP TEMP M/CONT (300)    21 PDLMN  DI2078
     &, BILE04         ! CABIN TEMP AUTO             21 PDLMN  DI2037
     &, BILN06         ! RECIRC FAN CONT             21 PDLSC  DI2061
     &, BILQ05         ! BLEED SYS FLOW CONT         21 PDLSC  DI2052
     &, BILS06         ! F/COMP TEMP M/CONT (100)    21 PDLSC  DI2065
     &, BILS10         ! RECIRC FAN PWR              21 PDLSC  DI2109
     &, BIRA06         ! CABIN TEMP IND (100 ONLY)   21 PDLSC  DI2215
     &, BIRA10         ! RECIRC FAN PWR              21 PDRSC  DI2259
     &, BIRB06         ! CABIN TEMP MAN (100 ONLY)   21 PDLSC  DI2216
     &, BIRD05         ! F/C FAN PWR                 21 PDRSC  DI2207
     &, BIRE05         ! F/C FAN CONT                21 PDRSC  DI2208
     &, BIRN05         ! F/COMP TEMP AUTO            21 PDRMN  DI2210
     &, BIRS06         ! CABIN TEMP MAN  (300 ONLY)  21 PDRMN  DI2225
     &, DOF            ! DCOND2 FREEZE FLAG
     &, DZF300         ! DASH8 100/300 OPTION  (.T. => 300)
     &, DZFA           ! DASH8 100-300 / 100A-300A OPTION  (.T. => A)
     &, IDDOSA2        ! CABIN PACK SW AT AUTO (-300/-300A)    DI0561
     &, IDDOSAC        ! AIR DIVERTER SW @CAB                  DI021E
     &, IDDOSAF        ! AIR DIVERTER SW @FLT COMPT            DI021F
     &, IDDOSCI(2)     ! F COMPT MAN TEMP CTL SW @COOL         DI0220
     &, IDDOSFC        ! RECIRC FC FAN (-300A)                 DI056A
     &, IDDOSFD        ! FC COMP FAN SW (-300A)                DI056F
     &, IDDOSFI(2)     ! FLT COMPT PACK SW AT OFF (-300/-300A) DI056B
     &, IDDOSHI(2)     ! F COMPT MAN TEMP CTL SW @WARM         DI0226
     &, IDDOSMI(2)     ! FLT COMP MAN (-100)/PACK AUTO (-300)  DI0227
     &, IDDOSRI(2)     ! FC FAN (-100)                         DI0225
     &, TF21021        ! TEMP CONTROLLER FAIL CABIN
     &, TF21022        ! TEMP CONTROLLER FAIL FLT COMPT
     &, TF21121        ! DISTRIBUTION DUCT OVERHEAT CABIN
      LOGICAL*1
     &  TF21122        ! DISTRIBUTION DUCT OVERHEAT FLT COMPT
     &, TF21201        ! DISTRIBUTION DUCT OVERHEAT - CABIN
     &, TF21202        ! DISTRIBUTION DUCT OVERHEAT - FLT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  DO$VDL         ! COMF. VALVE DEM. L POSITION    [coef] AODUMY
     &, DOTSI(2)       ! COMPT TEMPERATURE SELECTED           [deg_C]
     &, DOUT           ! TRIM ACTUATOR  RATE                  [coeff]
     &, DOVBI(2)       ! TCV COLD SIDE POSITION               [coeff]
     &, DOVBLI(2)      ! TCV COLD SIDE INTEG POSN             [coeff]
     &, DOVD           ! DIVERTER VALUE ROTARY ACT POSN       [coeff]
     &, DOVFI(2)       ! PRSOV POSITION                       [coeff]
     &, DOVFLI(2)      ! PRSOV INTEGRAL PSN                   [coeff]
     &, DOVHI(2)       ! TCV HOT SIDE POSN                    [coeff]
     &, DOVT           ! TRIM ACTUATOR POSN                   [coeff]
     &, DOVTL          ! TRIM ACTUATOR INTEG POSN             [coeff]
     &, DOWD           ! F_DECK AIRFLOW                      [lb/min]
C$
      LOGICAL*1
     &  DO$KAI(2)      ! F COMP HOT LIGHTS (300)               DO0736
     &, DO$KCI(2)      ! F COMP PACK HOT LIGHTS (300)          DO0729
     &, DO$KCL         ! LEFT COMFORT CLOSED                   DO038E
     &, DO$KCR         ! RIGHT COMFORT CLOSED                  DO038C
     &, DO$KOL         ! LEFT COMFORT OPEN                     DO038D
     &, DO$KOR         ! RIGHT COMFORT OPEN                    DO038B
     &, DOFAI(2)       ! FLT COMP/CAB DUCT OVHT
     &, DOFCI(2)       ! PACK HOT FLAG
     &, DOFE           ! AVIONICS FAN CMD FLAG
     &, DOFF           ! FLIGHT COMPT CMD FLAG
     &, DOFRI(2)       ! RECIRC FAN CMD FLAG
     &, DOGA           ! RETURN TO CENTER RLY  (K6)
     &, DOGBI(2)       ! PRSOV SOLENOID  (K11-K12)
     &, DOGMI(2)       ! AUTO/MANUAL RELAY  (K1-K2)
C$
      LOGICAL*1
     &  DUM0000001(5076),DUM0000002(4013),DUM0000003(2631)
     &, DUM0000004(1006),DUM0000005(738),DUM0000006(3)
     &, DUM0000007(2),DUM0000008(1),DUM0000009(1),DUM0000010(4460)
     &, DUM0000011(78448),DUM0000012(12),DUM0000013(624)
     &, DUM0000014(44),DUM0000015(112),DUM0000016(48)
     &, DUM0000017(36),DUM0000018(4),DUM0000019(16)
     &, DUM0000020(1),DUM0000021(14),DUM0000022(4)
     &, DUM0000023(289),DUM0000024(16),DUM0000025(6323)
     &, DUM0000026(215359),DUM0000027(3),DUM0000028(2)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,DO$VDL,DUM0000002,DO$KAI,DO$KCI,DO$KOL,DO$KOR
     &, DO$KCL,DO$KCR,DUM0000003,IADBXB,IADOHSI,IADOVFL,IADOVFR
     &, DUM0000004,IDDOSMI,IDDOSA2,IDDOSFI,IDDOSAC,IDDOSAF,IDDOSCI
     &, IDDOSHI,IDDOSFD,IDDOSRI,IDDOSFC,DUM0000005,BIAN03,BILB08
     &, BIRS06,DUM0000006,BILN06,BILS10,BIRA10,BIRE05,BIRD05
     &, BIRN05,BILS06,DUM0000007,BILQ05,DUM0000008,BILE04,BIRA06
     &, DUM0000009,BIRB06,DUM0000010,VHH,DUM0000011,DAPAI,DUM0000012
     &, DAPD,DUM0000013,DKPFAI,DKPFI,DUM0000014,DKTCI,DUM0000015
     &, DKXPFI,DUM0000016,DNTAAI,DNTAI,DNTDAI,DNTCAI,DNTCI,DNTDI
     &, DUM0000017,DNWDI,DUM0000018,DNWFD,DUM0000019,DOUT,DOVBI
     &, DOVBLI,DOVD,DOVFI,DOVFLI,DOVHI,DOVT,DOVTL,DOTSI,DOWD
     &, DOFE,DOFF,DUM0000020,DOFRI,DOFCI,DOFAI,DOGA,DOGBI,DOGMI
     &, DUM0000021,DTPA,DUM0000022,DTPCI,DUM0000023,DZFA,DZF300
     &, DUM0000024,DOF,DUM0000025,AGFPS65,DUM0000026,TF21021
     &, TF21022,DUM0000027,TF21201,TF21202,DUM0000028,TF21121
     &, TF21122   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DOHA     
     &, DOVDI(2)     
     &, DOVFFI(2)     
C$
      LOGICAL*1
     &  DOBAI(2)     
     &, DOBE     
     &, DOBF     
     &, DOBFD     
     &, DOBFI(2)     
     &, DOBMI(2)     
     &, DOBRI(2)     
     &, DOBXL     
     &, DOFFD     
     &, DOKCI(2)     
     &, DOKOI(2)     
     &, DOZAI(2)     
     &, DOZAAI(2)     
     &, DOZTA     
     &, DOZCI(2)     
     &, DOZDI(2)     
     &, DOZBI(2)     
     &, DOZFI(2)     
     &, DOZTI(2)     
C$
      EQUIVALENCE
     &  (DOVFFI(1),IADOVFL),(DOVDI(1),DO$VDL),(DOFFD,AGFPS65),(DOHA,VHH)
     &, (DOKCI(1),DO$KCL),(DOKOI(1),DO$KOL),(DOZDI(1),TF21121)          
     &, (DOBE,BIAN03)                                                   
C------------------------------------------------------------------------------
C
C
C
C
C
C ---------------
C'Local_Variables
C ---------------
C
C
C
C    ----------------------------------------------------------------------
C                             M O D U L E
C
C               V A R I A B L E S    C O N V E N T I O N
C    ----------------------------------------------------------------------
C
C                L - local variable
C
C                X - common data base variable (read/write)
C
C                E - common data base variable (read only)
C
C                M - ECAM
C
C'Ident
C
      CHARACTER*55
     &  REV /
     &  '$Source: usd8do.for.18 10Nov1992 22:59 usd8 Steve W$'/
C
C
C  ----------------------
C  ECS  Integer_Variables
C  ----------------------
C
C
      INTEGER*4
C
C       Label       Description                 Units        Equival
C
     &  I          !L- LOOP INDEX
     &, K          !L- 300-INDEX
C
C
C
C
C
C
C
C  -------------------
C  ECS  Real_Variables
C  -------------------
C
C
      REAL*4
C
C
C       Label       Description                 Units        Equival
C
C
     &  DODMI(2)  !E- COMFORT VALVE PULSE PERIOD           [sec]
     &, DODDI(2)  !E- PULSE PERIOD TIME DELAY              [sec]
     &, DODAI(2)  !L- DUCT OVERTEMP TIME DELAY             [sec]
     &, DOHAI(2)  !L- FILTERED TEMP SELECTED               [deg_C]
     &, DOHSI(2)  !L- TEMPERATURE SELECTED                 [deg-C]
     &, DOHSLI(2) !L- TEMPERATURE SELECTED LAST            [deg-C]
     &, DOPDSI(2) !L- PNEU DUCT SENSED PRESS               [psia]
     &, DOPFAI(2) !L- PACK INLET DUCT ADV PRESS WITH NEW   [psia]
C                     INDEX
     &, DOPFEI(2) !L- PRSOV PRESSURE DEVIATION             [psia]
     &, DOPFGI(2) !L- PRSOV SPRING PRESSURE                [psia]
     &, DOPFI(2)  !L- PACK INLET DUCT PRESS WITH NEW INDEX [psia]
     &, DOPFPI(2) !L- PRSOV SUPPLY PRESSURE                [psia]
     &, DOPFSI(2) !L- PRSOV PRESS REG SETTING
     &, DOPFUI(2) !L- PRSOV UP CHAMB PRESS                 [psia]
     &, DOPS      !L- SET DOWN LIMITER PRESS               [psia]
     &, DOPSD     !L- SET DOWN LIMITER DIFF PRESS          [psi]
     &, DOT2      !L- SUB-BAND ITER TIME                   [sec]
     &, DOTCEI(2) !L- ZONE TEMP ERROR                      [deg_C]
     &, DOTCI(2)  !L- COMPR DISCH TEMP WITH NEW INDEX      [deg_C]
     &, DOTCSI(2) !L- COMPT TEMPERATURE SETTING            [deg_C]
     &, DOTCTI(2) !L- A/C OVERTEMP SW SETTING              [deg_C]
     &, DOTDCI(2) !L- ZONE DUCT LOWER TEMP ERROR           [deg_C]
     &, DOTDEI(2) !L- TCV COLD SIDE TEMP ERROR             [deg_C]
     &, DOTDHI(2) !L- ZONE DUCT HIGHER TEMP ERROR          [deg_C]
     &, DOTDSI(2) !L- COMPT DUCT TEMP SENSED               [deg_C]
     &, DOTDTI(2) !L- DUCT OVERHEAT SW SETTING             [deg_C]
     &, DOTL      !L- ITERATION TIME LAST                  [sec]
     &, DOUBC     !L- TCV CLOSE MAX RATE                   [coeff/sec]
     &, DOUBI(2)  !L- TCV COLD SIDE RATE
     &, DOUBLI(2) !L- TCV COLD SIDE INTEG RATE
     &, DOUBO     !L- TCV OPEN MAX RATE                    [coeff/sec]
     &, DOUD      !L- DIVERTER VALVE ROTARY ACT SPEED
     &, DOUDC     !L- DIVERTER VALVE CLOSE MAX RATE        [coeff/sec]
     &, DOUDO     !L- DIVERTER VALVE OPEN MAX RATE         [coeff/sec]
     &, DOUFC     !L- PRSOV CLOSE MAX RATE                 [coeff/sec]
     &, DOUFI(2)  !L- PRSOV RATE
     &, DOUFLI(2) !L- PRSOV INTEGRAL RATE
     &, DOUFO     !L- PRSOV OPEN MAX RATE                  [coeff/sec]
     &, DOUTC     !L- TRIM VALVE CLOSE MAX RATE            [coeff/sec]
     &, DOUTL     !L- TRIM COMPT ACTUATOR INTEG RATE
     &, DOUTO     !L- TRIM VALVE OPEN MAX RATE             [coeff/sec]
     &, DOVBT     !L-
     &, DOVEI(2)  !L- COMFORT VALVE POSN ERROR
     &, DOVMI(2)  !L- COMFORT VALVE ABSOL ERROR
     &, DOVTF     !L- F DECK TRIM MAX. REF. POSITION
     &, DOVTT     !L- TRIM CONTINUITY FLAG SETTING
     &, DOWDI(2)  !L- COMPT LAG AIRFLOW                    [lb/min]
     &, DOXA      !L- PRSOV ALTITUDE FACT
     &, DOXD      !L- F_DECK AIRFLOW TIME CONST            [sec]
     &, DOXEI(2)  !L- TCV COLD SIDE TEMP GAIN
     &, DOXF      !L- PRSOV GAIN FACTOR
     &, DOXFF     !L- PRSOV FLOW CONT SERVO FACT
     &, DOXFI(2)  !L- PRSOV GAIN
     &, DOXFU     !L- CONSTANT
     &, DOXFUI(2) !L- PRSOV UP CHAMB GAIN
     &, DOXAI(2)  !L- FILTERED TEMP TIME CONST
     &, DOXL      !L- SET DOWN LIMITER FACTOR
     &, DOXPFI(2) !L- PRSOV BLEED VALVE PRECESS GAIN WITH NEW INDEX
     &, DOXPI(2)  !L- PRSOV FLOW CONT SUP PRESS FACT
     &, DOXS      !L- TEMPERATURE SELECT TIME CONST           [sec]
     &, DOXSI(2)  !L- PRSOV FLOW CONT SERVO ACT FACT
C
C
C
C
C  ----------------------
C  ECS  Logical_Variables
C  ----------------------
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
C
C
     &  DOSAI(2)  !L- DASH8 SERIES 300 PACK SW @AUTO
     &, DOSFD     !L- FC FAN SW @OFF
     &, DOFB      !L- PACK CONTINUITY FLAG
     &, DOFBI(2)  !L- DUCT OVERTEMP SOLENOID  (K11-K12)
     &, DOFCLI(2) !L- COMFORT VALVE CLOSE FLAG
     &, DOFOI(2)  !L- COMFORT VALVE OPEN FLAG
     &, DOFT      !L- TRIM CONTINUITY FLAG
     &, DOGAC     !L- RETURN TO CENTER FLY (K6)
     &, DOGAI(2)  !L- DUCT OVERHEAT RELAY  (K3-K4)
     &, DOGCI(2)  !L- A/C OVERTEMP SOLENOID  (K9-K10)
     &, DOGDI(2)  !L- PACK OFF/AUTO SOL  (K14-K15)
     &, DOGFD     !L- FAN CONTROL RELAY  (2122-K1)
     &, DOGRI(2)  !L- FAN CONTROL RELAY  (2123-K1)
     &, DOSRI(2)  !L- BUS RECIRC FAN SW
     &, DOZOI(2)  !L- PROVISION ( DUCT OVERHEAT SW STUCK IN POSITION )
     &, FIRST     /  .TRUE.  / !L- FIRST PASS FLAG
C
C
C
C
C  --------------
C  ECS  Constants
C  --------------
C
C
      REAL*4
C
C       Label      Value           Description              Units
C
C
     &  DOC20    /  0.1       / !                           [coeff/sec]
     &, DOC30    /  0.5       / !                           [1/sec]
     &, DOC40    /  0.05      / !                           [coeff/sec]
     &, DOC50    /  0.5       / !                           [1/sec]
     &, DOC60    /  0.2       / !                           [coeff/sec]
     &, DOC80    /  0.5       / !                           [1/sec]
     &, DOC81    /  0.5       / !                           [1/sec]
     &, DOC100   /  1.0       / !                           [coeff/sec]
     &, DOCD10   /  192.0     / !                           [deg_C]
     &, DOCD11   /  207.0     / !                           [deg_C]
     &, DOCD20   /  83.0      / !                           [deg_C]
     &, DOCD21   /  88.0      / !                           [deg_C]
     &, DOCF20   /  20.0      / !                           [sec]
     &, DOCH10   /  0.99      / !
     &, DOCH11   /  0.95      / !
     &, DOCH20   /  0.01      / !
     &, DOCH21   /  0.1       / !
     &, DOCH30   /  0.5       / !
     &, DOCH31   /  1.0       / !
     &, DOCI20   /  0.538     / !
     &, DOCI21   /  0.462     / !
     &, DOCI40   /  1.0       / !
     &, DOCI41   /  0.962     / !
     &, DOCI42   /  25535.9   / !
     &, DOCI80   /  12.0      / !                           [psi]
     &, DOCI100  /  0.067     / !
     &, DOCI101  /  -1.2      / !
     &, DOCI102  /  0.462     / !
     &, DOCI103  /  1.0       / !
     &, DOCI110  /  6.0       / !
     &, DOCI120  /  1.0       / !
     &, DOCI121  /  6.0       / !
     &, DOCI140  /  52.0      / !                           [psia]
     &, DOCI160  /  15.0      / !                           [psia]
     &, DOCI161  /  52.0      / !                           [psia]
     &, DOCI180  /  0.4       / !                           [n/a]
     &, DOCI220                 !
     &, DOCI221                 !
     &, DOCI222  /  1.0       / !                           [psi]
     &, DOCI223  /  1.0       / !                           [psi]
     &, DOCI224                 !
     &, DOCI300  /  0.1       / !
     &, DOCI340  /  0.02      / !                           [coeff/psi]
     &, DOCI400  /  2.0       / !                           [psi/coeff]
     &, DOCI401  /  6.0       / !                           [psi]
     &, DOCI440  /  1.0       / !                           [iter]
     &, DOCI500  /  0.3       / !
     &, DOCJ20   /  0.4       / !                           [n/a]
     &, DOCJ30   /  0.5       / !                           [n/a]
     &, DOCJ40   /  71.0      / !                           [deg_C]
     &, DOCJ50   /  2.0       / !                           [deg_C]
     &, DOCJ60   /  0.0003    / !                           [coeff/deg_C]
     &, DOCJ61   /  0.0008    / !                           [coeff/deg_C]
     &, DOCJ80   /  20.0      / !                           [n/a]
     &, DOCL20   /  0.4       / !                           [n/a]
     &, DOCL30   /  0.5       / !                           [n/a]
     &, DOCL40   /  71.0      / !                           [deg_C]
     &, DOCL50   /  2.0       / !                           [deg_C]
     &, DOCL60   /  0.0003    / !                           [deg_C]
     &, DOCL61   /  0.0008    / !                           [deg_C]
     &, DOCL80   /  20.0      / !                           [n/a]
     &, DOCN20   /  1.5       / !
     &, DOCN40   /  0.0588    / !                           [coeff.min/lb]
     &, DOCP140  /  0.9800    / !                           [coeff]
     &, DOCP141  /  0.0200    / !                           [coeff]
     &, DOCP142  /  0.0010    / !                           [coeff]
     &, DOCP143  /  0.9900    / !                           [coeff]
     &, DOCP160  /  0.2500    / !                           [coeff]
     &, DOCP161  /  0.0500    / !                           [coeff]
     &, DOCP180  /  7.00      / !                           [coeff]
     &, DOCP181  /  0.2786    / !                           [coeff]
C
C
C
C
      ENTRY DCOND2
C
C ----------------
C'Start_of_Program
C ----------------
C
C
C
C
      IF ( DOF ) THEN
C       Module freeze flag
      ELSE
C
C
C
C -------------------------
C First_Pass_Initialization
C -------------------------
C
C
      IF ( FIRST )  THEN
C
C
      FIRST  =  .FALSE.
C
      ENDIF
C
C
C -----------------------------------------------------
C MAPING : TO FIT PACK INDEX WITH ZONE INDEX
C -----------------------------------------------------
C
      IF ( DZF300 ) THEN
C
C
C
      DOPFI(1) = DKPFI(2)
      DOPFI(2) = DKPFI(1)
C
      DOPFAI(1) = DKPFAI(2)
      DOPFAI(2) = DKPFAI(1)
C
      DOTCI(1) = DKTCI(2)
      DOTCI(2) = DKTCI(1)
C
      ELSE
C
C
      DOPFI(1) = DKPFI(1)
C
      DOPFAI(1) = DKPFAI(1)
C
      DOTCI(1) = DKTCI(1)
C
      ENDIF
C
      DOXPFI(1)= DKXPFI(2)
      DOXPFI(2)= DKXPFI(1)
C
C
C
C
C ------------------------------------
C
C
      DOBAI(1) =  BIRN05
      DOBAI(2) =  BILE04
      DOBF     =  BIRD05
      DOBFD    =  BIRE05
      DOBXL    =  BILQ05
C
C
      IF ( DZF300 ) THEN
C
      DOSAI(1) = IDDOSMI(1)
      DOSAI(2) = IDDOSA2
C
      DOBMI(1) = BILB08
      DOBMI(2) = BIRS06
      DOBRI(1) = BIRA10
      DOBRI(2) = BILS10
      DOBFI(1) = BIRA06
      DOBFI(2) = BILN06
C
      DOSFD    = IDDOSFD .AND. IDDOSRI(1)
      DOSRI(1) = IDDOSFC
      DOSRI(2) = IDDOSRI(2)
C
      DOCN40   = 0.0256
C
      DOZAI(1) =  TF21202
      DOZAI(2) =  TF21201
      DOZTI(1) =  TF21022
      DOZTI(2) =  TF21021
C
      ELSE
C
      DOBMI(1) = BILS06
      DOBMI(2) = BIRB06
      DOBRI(1) = BILS10
      DOBRI(2) = .FALSE.
      DOBFI(1) = BILN06
C
      DOSFD    = IDDOSRI(1)
      DOSRI(1) = IDDOSRI(2)
      DOSRI(2) = .FALSE.
C
      DOZTI(1) =  TF21022.OR.TF21022
      DOZTI(2) =  TF21021.OR.TF21021
      ENDIF
C
C
C      DOSRI(1) = IDDOSRI(2)
C
C
CD ----------------------------------------------------
CD Function MAIN - Time Dependent Initialization
CD ----------------------------------------------------
C
C
CD 200  IF ( YITIM  NOT EGUAL  DOTL ) THEN
C  ---------------------------------------
C
      IF ( YITIM .NE. DOTL ) THEN
C
CD 240  UPDATE [DOTL]  ITER TIME LAST   [sec]
C  ------------------------------------------
C
        DOTL = YITIM
C
CD 260  UPDATE [DOT2]  SUB-BAND ITER TIME  [sec]
C  ---------------------------------------------
C
        DOT2 = 2 * DOTL
C
CD 400  UPDATE [DOUBO]  TCV OPEN MAX RATE  [coeff/sec]
C  ---------------------------------------------------
C
        DOUBO = DOC20 * DOTL
C
CD 420  UPDATE [DOUBC]  TCV CLOSE MAX RATE  [coeff/sec]
C  ----------------------------------------------------
C
        DOUBC = - DOUBO
C
CD 440  UPDATE [DOXS]  TEMPERATURE SELECT TIME CONST  [sec]
C  --------------------------------------------------------
C
         DOXS = DOC30 * DOTL
C
CD 460  UPDATE [DOUTO]  TRIM VALVE OPEN MAX RATE  [coeff/sec]
C  ----------------------------------------------------------
C
        DOUTO = DOC40 * DOTL
C
CD 480  UPDATE [DOUTC]  TRIM VALVE CLOSE MAX RATE  [coeff/sec]
C  -----------------------------------------------------------
C
         DOUTC = - DOUTO
C
CD 500  UPDATE [DOXD]  F_DECK AIRFLOW TIME CONST  [sec]
C  ----------------------------------------------------
C
        DOXD = DOC50 * DOTL
C
CD 520  UPDATE [DOUDO]  DIVERTER VALVE OPEN MAX RATE  [coeff/sec]
C  --------------------------------------------------------------
C
        DOUDO = DOC60 * DOTL
C
CD 540  UPDATE [DOUDC]  DIVERTER VALVE CLOSE MAX RATE  [coeff/sec]
C  ---------------------------------------------------------------
C
        DOUDC = - DOUDO
C
CD 600  UPDATE [DOXA1]  FLT COMPT FILTERED TEMP TIME CONST
C  -------------------------------------------------------
C
        DOXAI(1) = DOC80 * DOTL
C
CD 620  UPDATE [DOXA2]  CABIN FILTERED TEMP TIME CONST
C  ---------------------------------------------------
C
        DOXAI(2) = DOC81 * DOTL
C
CD 640  UPDATE [DOUFO]  PRSOV OPEN MAX RATE
C  ----------------------------------------
C
        DOUFO  = DOC100 * DOTL
C
CD 660  UPDATE [DOUFC]  PRSOV CLOSE MAX RATE
C  -----------------------------------------
C
        DOUFC = - DOUFO
C
CD 1000  COMPUTE [DOCI220]
C  -----------------------
C
      DOCI220  =  ( (DOCI400+DOCI401)*DOCI223+DOCI401*DOCI222)
     &              / DOCI400
C
CD 1020  COMPUTE [DOCI221]
C  -----------------------
C
        DOCI221  =  12 * ( DOCI220 + DOCI222 ) / ( DOCI400 + DOCI401 )
C
CD 1040  COMPUTE [DOCI224]
C  -----------------------
C
        DOCI224  =   5 * ( DOCI220 + DOCI222 ) / ( DOCI400 + DOCI401 )
C
CD 1060  COMPUTE [DOXFU]
C  ---------------------
C
        DOXFU    =  DOCI80 * DOCI180 / DOCI221
C
CD 1080  COMPUTE [DOXF] PRSOV GAIN FACTOR
C        --------------------------------
C
         DOXF = DOCI500 / ( 1 + DOCI440 )
C
CD ENDIF
C  -----
C
      ENDIF
C
C
C
C
CD ----------------------------------------------------
CD Function A - Time Dependent Initialization
CD ----------------------------------------------------
C
C
CD A100  IF ( DZF300 ) THEN
C  ------------------------
C
      IF ( DZF300 ) THEN
C
CD A120  UPDATE	[K]  300-INDEX
C  ---------------------------
C
        K = 2
C
CD ELSE
C  ----
C
      ELSE
C
CD A121  UPDATE [K]  300-INDEX
C  ---------------------------
C
        K = 1
C
CD ENDIF
C  -----
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function B -
CD ----------------------------------------------------
C
C B200  IF ( DOSFD ) THEN
C -----------------------
C
      IF ( DOSFD ) THEN
C
CD B220  COMPUTE [DOGFD]  FAN CONTROL RELAY (2122-K1)
C  --------------------------------------------------
C
        DOGFD = DOBFD .AND. DOFFD
C
CD ELSE
C  ----
C
      ELSE
C
CD B221  COMPUTE [DOGFD]
C  ---------------------
C
        DOGFD = .FALSE.
C
CD ENDIF
C  -----
C
      ENDIF
C
CD B240  COMPUTE [DOFF]  FLIGHT COMPT CMD FLAG
C  -------------------------------------------
C
      DOFF = DOGFD .AND. DOBF
C
CD B260  DO I = 1, K
C  -----------
C
      DO I = 1, K
C
CD IF ( DOSRI ) THEN
C  -----------------
C
        IF ( DOSRI(I) ) THEN
C
CD B280 COMPUTE [DOGFI]  FAN CONTROL RELAY  (2123-K1)
C  --------------------------------------------------
C
           DOGRI(I) = DOBFI(I)
C
CD ELSE
C  ----
C
        ELSE
C
CD COMPUTE [DOGRI]
C  ---------------
C
          DOGRI(I) = .FALSE.
C
CD ENDIF
C  -----
C
        ENDIF
C
CD B300  COMPUTE [DOFRI]  RECIRC FAN CMD FLAG
C  ------------------------------------------
C
        DOFRI(I) = DOGRI(I) .AND. DOBRI(I)
C
CD B500 COMPUTE [DOFE] AVIONICS FAN CMD FLAG
C       ------------------------------------
C
        DOFE = DOBE
C
C
CD ----------------------------------------------------
CD Function D -
CD ----------------------------------------------------
C
CD D200  IF ( ( NOT DOZCI ) ) THEN
C  ------------------------------------------
C
        IF ( .NOT. DOZCI(I) ) THEN
C
CD D220  IF ( DOKTCI  GREATER THEN  DOTCTI ) THEN
C  ----------------------------------------------
C
          IF ( DOTCI(I) .GT. DOTCTI(I) ) THEN
C
CD D240  COMPUTE [DOTCTI]  A/C OVERTEMP SW SETTING
C  -----------------------------------------------
C
            DOTCTI(I) = DOCD10
C
CD D260  COMPUTE [DOFCI]  A/C OVERTEMP FLAG
C  ----------------------------------------
C
            DOFCI(I) = .TRUE.
C
CD ELSE
C  ----
C
          ELSE
C
CD D241  COMPUTE [DOTCTI]
C  ----------------------
C
            DOTCTI(I) = DOCD11
C
CD D261  COMPUTE [DOFCI]
C  ---------------------
C
            DOFCI(I) = .FALSE.
C
CD ENDIF
C  -----
C
          ENDIF
C
CD ENDIF
C  -----
C
        ENDIF
C
CD END DO
C  ------
C
      END DO
C
CD D500  DO I = 1, 2
C  -----------------
C
      DO I = 1, 2
C
CD IF ( NOT DOZOI ) THEN
C  ---------------------
C
        IF ( .NOT. DOZOI(I) ) THEN
C
CD D520  IF ( DNTDI .GT. DOTDTI ) THEN
C  -----------------------------------
C
          IF ( DNTDI(I) .GT. DOTDTI(I) ) THEN
C
CD D540  COMPUTE [DOTDTI]  DUCT OVERHEAT SW SETTING
C  ------------------------------------------------
C
            DOTDTI(I) = DOCD20
C
CD D560  COMPUTE [DOFAI]  DUCT OVERHEAT FLAG
C  -----------------------------------------
C
            DOFAI(I) = .TRUE.
C
CD ELSE
C  ----
C
          ELSE
C
CD D541  COMPUTE [DOTDTI]
C  ----------------------
C
            DOTDTI(I) = DOCD21
C
CD D561  COMPUTE [DOFAI]
C  ---------------------
C
            DOFAI(I) = DOZAI(I)
C
CD ENDIF
C  -----
C
          ENDIF
C
CD ENDIF
C  -----
C
        ENDIF
C
CD D580  COMPUTE [DOHAI]  FILTERED TEMP SELECTED
C  ---------------------------------------------
C
        DOHAI(I) = DOHAI(I) + DOXAI(I) * ( IADOHSI(I) - DOHAI(I) )
C
C
C
CD ----------------------------------------------------
CD Function F -
CD ----------------------------------------------------
C
C
CD F200  IF ( DOBAI ) THEN
C  -----------------------
C
        IF ( DOBAI(I) ) THEN
C
CD F220  COMPUTE [DOGAI]  DUCT OVERHEAT RELAY (K3-K4)
C  --------------------------------------------------
C
          DOGAI(I) = DOFAI(I)
C
CD F240  COMPUTE [DOHSI]  TEMPERATURE SELECTED
C  -------------------------------------------
C
          DOHSI(I) = DOHAI(I)
C
CD F260  COMPUTE [DOHSLI] TEMPERATURE SELECTED LAST
C        ------------------------------------------
C
         DOHSLI(I) = DOHSI(I)
C
CD ELSE
C  ----
C
        ELSE
C
CD F221  COMPUTE [DOGAI]
C  ---------------------
C
          DOGAI(I) = .FALSE.
C
CD F241  COMPUTE [DOHSI]
C  ---------------------
C
          DOHSI(I) = DOHSLI(I)
C
CD ENDIF
C  -----
C
        ENDIF
C
CD F260  COMPUTE [DO$KAI]  CABIN / FLT COMPT HOT LIGHTS
C  ---------------------------------------------------
C
        DO$KAI(I) = DOFAI(I)
C
CD F500  IF ( DZF300 ) THEN
C  ------------------------
C
        IF ( DZF300 ) THEN
C
CD F520  IF ( DOBMI ) THEN
C  -----------------------
C
          IF ( DOBMI(I) ) THEN
C
CD F540  COMPUTE [DOGDI]  PACK OFF/AUTO SOL. (K14-K15)
C  ---------------------------------------------------
C
            DOGDI(I) = DOSAI(I) .OR. IDDOSFI(I)
C
CD F560  COMPUTE [DOGCI]  A/C OVERTEMP SOLENOID (K9-K10)
C  -----------------------------------------------------
C
            DOGCI(I) = DOFCI(I)
C
CD F580  IF ( DOFAI ) THEN
C  -----------------------
C
            IF ( DOFAI(I) ) THEN
C
CD F600  COMPUTE [DODAI]  DUCT OVERTEMP TIME DELAY  [sec]
C  ------------------------------------------------------
C
              DODAI(I) = DODAI(I) - DOTL
C
CD ELSE
C  ----
C
            ELSE
C
CD COMPUTE [DODAI]
C  ---------------
C
               DODAI(I) = DOCF20
C
CD ENDIF
C  -----
C
            ENDIF
C
CD F620  COMPUTE [DOFBI]  DUCT OVERTEMP SOLENOID (K11-K12)
C  -------------------------------------------------------
C
            DOFBI(I) = DODAI(I) .LT. 0.0
C
CD F900  COMPUTE [DOGBI]  PRSOV SOLENOID (K11-K12)
C  -----------------------------------------------
C
            DOGBI(I) = IDDOSFI(I) .OR. DOGCI(I) .OR. DOFBI(I)
C
CD ELSE
C  ----
C
          ELSE
C
CD F541  COMPUTE [DOGDI]
C  ---------------------
C
            DOGDI(I) = .FALSE.
C
CD F561  COMPUTE [DOGCI]
C  ---------------------
C
            DOGCI(I) = .FALSE.
C
CD F601  COMPUTE [DODAI]
C  ---------------------
C
            DODAI(I) = 0.0
C
CD F621  COMPUTE [DOFBI]
C  ---------------------
C
            DOFBI(I) = .FALSE.
C
CD ENDIF
C  -----
C
          ENDIF
C
CD F920  COMPUTE [DO$KCI]  CABIN/FLT COMPT PACK HOT LIGHTS
C  ------------------------------------------------------
C
          DO$KCI(I) = DOFCI(I)
C
CD F940  COMPUTE [DOGMI]  AUTO/MANUAL RELAY (K1-K2)
C  ------------------------------------------------
C
          DOGMI(I) = ( DOBAI(I) .AND. DOGAI(I) ) .OR. ( DOBMI(I) .AND.
     &             .NOT. ( DOGDI(I) .OR. DOGAI(I) ) )
C
CD ELSE
C  ----
C
        ELSE
C
CD F921  COMPUTE [DO$KCI]  CABIN/FLT COMPT PACK HOT LIGHTS
C  -------------------------------------------------------
C
      DO$KCI(I) = DOFCI(3-I)
C
CD F941  COMPUTE [DOGMI]
C  ---------------------
C
          DOGMI(I) = ( DOBAI(I) .AND. DOGAI(I) ) .OR. ( DOBMI(I) .AND.
     &               IDDOSMI(I) .AND. .NOT. DOGAI(I) )
C
CD ENDIF
C  -----
C
        ENDIF
C
CD F980  END DO
C  ------------
C
      END DO
C
C
C
CD ----------------------------------------------------
CD Function H -
CD ----------------------------------------------------
C
C
CD H200  IF ( DZF300 ) THEN
C  ------------------------
C
      IF ( DZF300 ) THEN
C
CD I200  IF ( DOBXL ) THEN
C  -----------------------
C
        IF ( DOBXL ) THEN
C
CD I220  COMPUTE [DOXFF]  PRSOV FLOW CONT SERVO FACT
C  ------------------------------------------------
C
          DOXFF = DOCI20 * IADBXB + DOCI21
C
CD ELSE
C  ----
C
        ELSE
C
CD I221  COMPUTE [DOXFF]
C  --------------------
C
          DOXFF = 1.0
C
CD ENDIF
C  -----
C
        ENDIF
C
CD I240  COMPUTE [DOXA]  PRSOV ALTITUDE FACT
C  -----------------------------------------
C
        DOXA = DOCI40 - ( DOCI41 * DOHA ) / ( DOCI42 + DOHA )
C
CD I500  IF ( DOPF1  GREATER THAN  DOPF2 ) THEN
C  --------------------------------------------
C
        IF ( DOPFI(1) .GT. DOPFI(2) ) THEN
C
CD I520  COMPUTE [DOPS]  SET DOWN LIMITER PRESS.
C  ---------------------------------------------
C
          DOPS = DOPFI(1)
C
CD ELSE
C  ----
C
        ELSE
C
CD I521  COMPUTE [DOPS]
C  --------------------
C
          DOPS = DOPFI(2)
C
CD ENDIF
C  -----
C
        ENDIF
C
CD I540  COMPUTE [DOPSD]  SET DOWN LIMITER DIFF PRESS  [psia]
C  ----------------------------------------------------------
C
        DOPSD = DOPS - DTPCI(1)
C
CD I700  DO I = 1, K
C  -----------------
C
        DO I = 1, K
C
CD COMPUTE [DOPFPI]  PRSOV SUPPLY PRESSURE  [psia]
C  -----------------------------------------------
C
          DOPFPI(I) = AMIN1 ( DAPD - DTPA, DOCI80 )
C
CD I720  COMPUTE [DOXPI]  PRSOV CONT SUPP PRESS FACT
C  -------------------------------------------------
C
          DOXPI(I) = AMIN1 ( AMAX1 ( DOCI100 * DAPAI(I) + DOCI101 ,
     &                            DOCI102 ), DOCI103 )
C
CD I740  COMPUTE [DOXSI]  PRSOV FLOW CONT SERVO ACT FACT
C  -----------------------------------------------------
C
          DOXSI(I) = DOXFF / DOXPI(I)
C
CD I760  IF ( DOPSD GREATER THAN DOCI110 ) THEN
C  --------------------------------------------
C
          IF ( DOPSD .GT. DOCI110 ) THEN
C
CD I781  COMPUTE [DOXL]
C  --------------------
C
            DOXL = 1.0
C
CD ELSE
C  ----
C
          ELSE
C
CD I780  COMPUTE [DOXL]  SET DOWN LIMITER FACTOR
C  ---------------------------------------------
C
            DOXL = ( DOCI120 - DOXSI(I) * DOXA ) * DOPSD / DOCI121 +
     &           DOXSI(I) * DOXA
C
CD ENDIF
C  -----
C
          ENDIF
C
CD I800  COMPUTE [DOPFSI]  PRSOV PRESS REG SETTING
C  -----------------------------------------------
C
          DOPFSI(I) = AMIN1 ( AMAX1 ( DOCI140 * DOXSI(I) * DOXA / DOXL,
     &              DOCI160 ), DOCI161 )
C
CD I1000 IF ( NOT DOZFI ) THEN
C  ---------------------------
C
          IF ( .NOT. DOZFI(I) ) THEN
C
CD I1020 IF ( NOT DOGBI ) THEN
C  ---------------------------
C
            IF ( .NOT. DOGBI(I) ) THEN
C
CD I1040 COMPUTE [DOPDSI]  PNEU DUCT SENSED PRESS  [psia]
C  ------------------------------------------------------
C
              DOPDSI(I) = DOPFI(I) + DOCI180 * ( DOPFAI(I) -
     &                  DOPFI(I) )
C
CD I1060 COMPUTE [DOPFEI]  PRSOV PRESSURE DEVIATION  [psia]
C  --------------------------------------------------------
C
              DOPFEI(I) = DOCI220 + DOPFSI(I) - DOPDSI(I)
C
CD I1200 IF ( DOPFEI GREATER THEN DOCI221 ) THEN
C  ---------------------------------------------
C
              IF ( DOPFEI(I) .GT. DOCI221 ) THEN
C
CD I1242  COMPUTE [DOPFUI]
C  -----------------------
C
                DOPFUI(I) = DOPFPI(I)
C
CD I1282  COMPUTE [DOXFI]
C  ----------------------
C
                DOXFI(I) = DOCI340
C
CD ELSEIF ( DOPFEI LESS THAN DOCI224 ) THEN
C  ----------------------------------------
C
              ELSEIF ( DOPFEI(I) .LT. DOCI224 ) THEN
C
CD I1241  COMPUTE [DOPFUI]
C  -----------------------
C
                DOPFUI(I) = 0.0
C
CD I1281  COMPUTE [DOXFI]
C  ----------------------
C
                DOXFI(I) = DOCI340
C
CD ELSE
C  ----
C
              ELSE
C
CD I1240  COMPUTE [DOPFUI]  PRSOV UP CHAMB PRESSURE  [psia]
C  --------------------------------------------------------
C
                DOPFUI(I) = DOPFPI(I) * DOPFEI(I) / DOCI221
C
CD I1260  COMPUTE [DOXFUI]  PRSOV UP CHAMB GAIN
C  --------------------------------------------
C
                DOXFUI(I) = DOXFU * DOXPFI(I)
C
CD I1280  COMPUTE [DOXFI]  PRSOV GAIN
C  ----------------------------------
C
                DOXFI(I) = AMIN1 ( DOXF / ( DOXFUI(I) + DOCI400 ) ,
     &                   DOCI300 )
C
CD ENDIF  I1200
C  ------------
C
              ENDIF
C
CD ELSE I1020
C  ----------
C
            ELSE
C
CD I1041  COMPUTE [DOPFUI]
C  -----------------------
C
              DOPFUI(I) = 0.0
C
CD I1061  COMPUTE [DOXFI]
C  ----------------------
C
              DOXFI(I) = DOCI340
C
CD ENDIF  I1020
C  ------------
C
            ENDIF
C
CD I1500  COMPUTE [DOPFGI]  PRSOV SPRING PRESSURE  [psia]
C  ------------------------------------------------------
C
            DOPFGI(I) = DOCI400 * DOVFI(I) + DOCI401
C
CD I1520  COMPUTE [DOUFLI]  PRSOV INTEGRAL RATE
C  --------------------------------------------
C
            DOUFLI(I) = AMIN1 ( AMAX1 ( DOXFI(I) * ( DOPFUI(I) -
     &                DOPFGI(I) ) , DOUFC ), DOUFO )
C
CD I1540  COMPUTE [DOVFLI]  PRSOV INTEGRAL PSN
C  -------------------------------------------
C
            DOVFLI(I) = AMIN1 ( AMAX1 ( DOVFLI(I) + DOUFLI(I), 0.0 ),
     &                  1.0 )
C
CD I1560  COMPUTE [DOUFI]  PRSOV RATE
C  ----------------------------------
C
            DOUFI(I) = AMIN1 ( AMAX1 ( DOVFLI(I) + DOCI440 *
     &                 DOUFLI(I) - DOVFI(I) , DOUFC ), DOUFO )
C
CD I1580  COMPUTE [DOVFI]  PRSOV POSITION
C  --------------------------------------
C
            DOVFI(I) = AMIN1 ( AMAX1 ( DOVFI(I)+DOUFI(I), 0.0 ), 1.0 )
C
CD ENDIF  I1000
C  ------------
C
          ENDIF
C
C !FM+
C !FM  10-Nov-92 22:57:18 Steve Walkington
C !FM    < modified as per fax from Fady Naccache >
C !FM
C
CD J200 IF ( NOT DOZBI ) THEN
C  ---------------------
CSBW          IF ( .NOT. DOZFI(I) ) THEN
          IF ( .NOT. DOZBI(I) ) THEN
C !FM-
C
CD J240  IF ( DOGMI ) THEN
C  -----------------------
C
            IF ( DOGMI(I) ) THEN
C
CD J260 IF ( DOSCI ) THEN
C  ----------------------
C
              IF ( IDDOSCI(I) .OR. DOGAI(I) ) THEN
C
CD J302  COMPUTE [DOUBI]
C  ---------------------
C
                DOUBI(I) = DOUBO
C
CD J280  ELSEIF ( DOSHI ) THEN
C  ---------------------------
C
              ELSEIF ( IDDOSHI(I) ) THEN
C
CD 3000  COMPUTE [DOUBI]  TCV COLD SIDE RATE
C  -----------------------------------------
C
                DOUBI(I) = DOUBC
C
CD ELSE
C  ----
C
              ELSE
C
CD J301  COMPUTE [DOUBI]
C  ---------------------
C
                DOUBI(I) = 0.0
C
CD ENDIF  J260
C  -----------
C
              ENDIF
C
CD J400  COMPUTE [DOVBI]  TCV COLD SIDE POSN
C  -----------------------------------------
C
              DOVBI(I) = AMIN1 ( AMAX1 ( DOVBI(I) + DOUBI(I) , 0.0 ),
     &                   1.0 )
C
CD J420  COMPUTE [DOVBLI]  TCV COLD SIDE INTEG POSN
C  ------------------------------------------------
C
              DOVBLI(I) = DOVBI(I)
C
CD ELSE J240
C  ---------
C
            ELSE
C
CD J500  IF ( NOT DOZAAI ) THEN
C  ---------------------------
C
              IF ( .NOT. DOZAAI(I) ) THEN
C
CD J520  COMPUTE [DOTSI]  COMPT TEMPERATURE SELECTED  [deg_C]
C  ----------------------------------------------------------
C
                DOTSI(I) = DOTSI(I) + DOXS * ( DOHSI(I) - DOTSI(I) )
C
CD J540  COMPUTE [DOTDSI]  COMPT DUCT TEMP SENSED  [deg_C]
C  -------------------------------------------------------
C
                DOTDSI(I) = DNTAI(I) + DOCJ20 * ( DNTAAI(I) - DNTAI(I) )
C
CD J560  COMPUTE [DOTCSI]  COMPT TEMPERATURE SENSED  [deg_C]
C  ---------------------------------------------------------
C
                DOTCSI(I) = DNTCI(I) + DOCJ30 * ( DNTCAI(I) - DNTCI(I) )
C
CD J800  IF ( DOZTI ) THEN
C  -----------------------
C
                IF ( DOZTI(I) ) THEN
C
CD J1321  COMPUTE [DOVBLI]
C  -----------------------
C
                  DOVBLI(I) = DOVBI(I)
C
CD J1341  COMPUTE [DOUBI]
C  ----------------------
C
                  DOUBI(I) = DOUBC
C
CD J1361  COMPUTE [DOVBI]
C  ----------------------
C
                  DOVBI(I) = AMAX1 ( DOVBI(I) + DOUBI(I), 0.0 )
C
CD ELSE  J800
C  ----------
C
                ELSE
C
CD J820  COMPUTE [DOTDHI]  ZONE DUCT HIGHER TEMP ERROR  [deg_C]
C  ------------------------------------------------------------
C
                  DOTDHI(I) = DOTDSI(I) - DOCJ40
C
CD J840  COMPUTE [DOTDCI]  ZONE DUCT LOWER TEMP ERROR  [deg_C]
C  -----------------------------------------------------------
C
                  DOTDCI(I) = DOTDSI(I) - DOCJ50
C
CD J860  COMPUTE [DOTCEI]  ZONE TEMP ERROR  [deg_C]
C  ------------------------------------------------
C
                  DOTCEI(I) = DOTCSI(I) - DOTSI(I)
C
CD J1000 IF ( DOTDHI  GREATER THAN  DOTCEI ) THEN
C   ---------------------------------------------
C
                  IF ( DOTDHI(I) .GT. DOTCEI(I) ) THEN
C
CD J1042  COMPUTE [DOTDEI]
C  -----------------------
C
                    DOTDEI(I) = DOTDHI(I)
C
CD J1062  COMPUTE [DOXEI]
C  ----------------------
C
                    DOXEI(I) = DOCJ61
C
CD J1020  ELSEIF ( DOTDCI LESS THAN DOTCEI ) THEN
C  ----------------------------------------------
C
                  ELSEIF ( DOTDCI(I) .LT. DOTCEI(I) ) THEN
C
CD J1041  COMPUTE [DOTDEI]
C  -----------------------
C
                    DOTDEI(I) = DOTDCI(I)
C
CD J1061  COMPUTE [DOXEI]
C  ----------------------
C
                    DOXEI(I) = DOCJ61
C
CD ELSE
C  ----
C
                  ELSE
C
CD J1040  COMPUTE [DOTDEI]  TCV COLD SIDE TEMP ERROR  [deg_C]
C  ----------------------------------------------------------
C
                    DOTDEI(I) = DOTCEI(I)
C
CD J1060  COMPUTE [DOXEI]  TCV COLD SIDE TEMP GAIN
C  -----------------------------------------------
C
                    DOXEI(I) = DOCJ60
C
CD ENDIF  J1000
C  ------------
C
                  ENDIF
C
CD J1300  COMPUTE [DOUBLI]  TCV COLD SIDE INTEG RATE
C  -------------------------------------------------
C
                  DOUBLI(I) = AMIN1 ( AMAX1 ( DOXEI(I) * DOTDEI(I),
     &                        DOUBC ) , DOUBO )
C
CD J1320  COMPUTE [DOVBLI]  TCV COLD SIDE INTEG POSN
C  -------------------------------------------------
C
                  DOVBLI(I) = AMIN1 ( AMAX1 ( DOVBLI(I) + DOUBLI(I),
     &                      0.0 ), 1.0 )
C
CD J1340  COMPUTE [DOUBI]  TCV COLD SIDE RATE
C  ------------------------------------------
C
                  DOUBI(I) = AMIN1 ( AMAX1 ( DOVBLI(I) + DOCJ80 *
     &                       DOUBLI(I) - DOVBI(I) , DOUBC ), DOUBO )
C
CD J1360  COMPUTE [DOVBI]
C  ----------------------
C
                  DOVBI(I) = AMIN1 ( AMAX1 ( DOVBI(I) + DOUBI(I),
     &                       0.0 ), 1.0 )
C
CD ENDIF  J800
C  -----------
C
                ENDIF
C
CD ENDIF J500
C  ----------
C
              ENDIF
C
CD ENDIF  J240
C  -----------
C
            ENDIF
C
CD ENDIF  J200
C  -----------
C
          ENDIF
C
CD J1380  COMPUTE [DOVHI]  TCV HOT SIDE POSN
C  -----------------------------------------
C
          DOVHI(I) = 1.0 - DOVBI(I)
C
CD END DO
C  ------
C
        END DO
C
CD ELSE  H200
C  ----------
C
      ELSE
C
C  H210
C
        IF ( DOVBI(1) .GT. DOVBT ) THEN
C
CD H220 COMPUTE [DOFB]  PACK CONTINUITY FLAG
C       ------------------------------------
C
        DOVBT = DOCH11
C H240
        DOFB  = .TRUE.
C
        ELSE
C
C H221
        DOVBT = DOCH10
C H241
        DOFB  = .FALSE.
C
        ENDIF
C
C H300
C
       IF ( DOVT .LT. DOVTT ) THEN
C
CD H320  COMPUTE [DOFT]  TRIM CONTINUITY FLAG
C  ------------------------------------------
C
       DOVTT = DOCH21
C H340
C
       DOFT  = .TRUE.
C
       ELSE
C
C H321
       DOVTT = DOCH20
C
C H341
C
       DOFT = .FALSE.
C
       ENDIF
C
CD H500  IF ( DOBA1 ) THEN
C  -----------------------
C
        IF ( DOBAI(1) ) THEN
C
CD H520  IF ( DOFB  OR  DOSM2 ) THEN
C  ---------------------------------
C
          IF ( DOFB .OR. IDDOSMI(2) ) THEN
C
CD H540  COMPUTE [DOGAC]  RETURN TO CENTER RLY (K6)
C  ------------------------------------------------
C
            DOGAC = DOFT .OR. DOGA
C
CD H560  COMPUTE [DOGA]  RETURN TO CENTER RLY (K7)
C  -----------------------------------------------
C
            DOGA = DOGAC
C
CD H600  COMPUTE [DOVTF] TRIM ACTUATOR DEFAULT POS
C        -----------------------------------------
C
            DOVTF = DOCH30
CD ELSE
C  ----
C
          ELSE
C
CD H541  COMPUTE [DOGAC]
C  ---------------------
C
            DOGAC = .FALSE.
C
CD H561  COMPUTE [DOGA]
C  --------------------
C
            DOGA = .FALSE.
C
CD H581 COMPUTE
C
            DOVTF = DOCH31
CD ENDIF
C  -----
C
          ENDIF
C
CD ELSE
C  ----
C
        ELSE
C
CD H542  COMPUTE [DOGAC]
C  ---------------------
C
          DOGAC = .FALSE.
C
CD H562  COMPUTE [DOGA]
C  --------------------
C
          DOGA = .FALSE.
C
CD H582  COMPUTE
C
          DOVTF = DOCH31
C
CD ENDIF  H500
C  -----------
C
        ENDIF
C
CD J2000  IF ( NOT DOZB2 ) THEN
C  ----------------------------
C
        IF ( .NOT. DOZBI(2) ) THEN
C
CD J2020  IF ( DOGM2 ) THEN
C  -------------------------
C
          IF ( DOGMI(2) ) THEN
C
CD J2040 IF ( DOSC2 OR DOGA2 ) THEN
C  -----------------------
C
            IF ( IDDOSCI(2).OR. DOGAI(2) ) THEN
C
CD J2082  COMPUTE [DOUB1]
C  ----------------------
C
              DOUBI(1) = DOUBO
C
CD J2060  ELSEIF ( DOSH2 ) THEN
C  ---------------------------
C
            ELSEIF ( IDDOSHI(2) ) THEN
C
CD J2080  COMPUTE [DOUB1]  TCV COLD SIDE RATE
C  ------------------------------------------
C
              DOUBI(1) = DOUBC
C
CD ELSE
C  ----
C
            ELSE
C
CD J2081  COMPUTE [DOUB1]
C  ----------------------
C
              DOUBI(1) = 0.0
C
CD ENDIF  J2040
C  ------------
C
            ENDIF
C
CD J2400  COMPUTE [DOVB1]  TCV COLD SIDE POSN
C  ------------------------------------------
C
            DOVBI(1) = AMIN1 ( AMAX1 ( DOVBI(1) + DOUBI(1) , 0.0 ),
     &                   1.0 )
C
CD J2420  COMPUTE [DOVBL1]  TCV COLD SIDE INTEG POSN
C  -------------------------------------------------
C
            DOVBLI(1) = DOVBI(1)
C
CD ELSE J2020
C  ----------
C
          ELSE
C
CD J2500  IF ( DOBA2 AND NOT DOZAA2 ) THEN
C  ----------------------------
C
            IF ( DOBAI(2) .AND. .NOT. DOZAAI(2) ) THEN
C
CD J2520  COMPUTE [DOTS2]  COMPT TEMPERATURE SELECTED  [deg_C]
C  -----------------------------------------------------------
C
              DOTSI(2) = DOTSI(2) + DOXS * ( DOHSI(2) - DOTSI(2) )
C
CD J2540  COMPUTE [DOTDS2]  COMPT DUCT TEMP SENSED  [deg_C]
C  --------------------------------------------------------
C
              DOTDSI(2) = DNTDI(2) + DOCJ20 * ( DNTDAI(2) - DNTDI(2) )
C
CD J2560  COMPUTE [DOTCS2]  COMPT TEMPERATURE SENSED  [deg_C]
C  ----------------------------------------------------------
C
              DOTCSI(2) = DNTCI(2) + DOCJ30 * ( DNTCAI(2) - DNTCI(2) )
C
CD J2800  IF ( DOZT2 AND ) THEN
C  ------------------------
C
              IF ( DOZTI(2) ) THEN
C
CD J3321  COMPUTE [DOVBL1]
C  -----------------------
C
                DOVBLI(1) = DOVBI(1)
C
CD J3341  COMPUTE [DOUB1]
C  ----------------------
C
                DOUBI(1) = DOUBC
C
CD J3361  COMPUTE [DOVB1]
C  ----------------------
C
                DOVBI(1) = AMAX1 ( DOVBI(1) + DOUBI(1), 0.0 )
C
CD ELSE  J2800
C  -----------
C
              ELSE
C
CD J2820  COMPUTE [DOTDH2]  ZONE DUCT HIGHER TEMP ERROR  [deg_C]
C  -------------------------------------------------------------
C
                DOTDHI(2) = DOTDSI(2) - DOCJ40
C
CD J2840  COMPUTE [DOTDC2]  ZONE DUCT LOWER TEMP ERROR  [deg_C]
C  ------------------------------------------------------------
C
                DOTDCI(2) = DOTDSI(2) - DOCJ50
C
CD J2860  COMPUTE [DOTCE2]  ZONE TEMP ERROR  [deg_C]
C  -------------------------------------------------
C
                DOTCEI(2) = DOTCSI(2) - DOTSI(2)
C
CD J3000 IF ( DOTDH2  GREATER THAN  DOTCE2 ) THEN
C   ---------------------------------------------
C
                IF ( DOTDHI(2) .GT. DOTCEI(2) ) THEN
C
CD J3042  COMPUTE [DOTDE2]
C  -----------------------
C
                  DOTDEI(2) = DOTDHI(2)
C
CD J3062  COMPUTE [DOXE1]
C  ----------------------
C
                  DOXEI(2) = DOCJ61
C
CD J3020  ELSEIF ( DOTDC2 LESS THAN DOTCE2 ) THEN
C  ----------------------------------------------
C
                ELSEIF ( DOTDCI(2) .LT. DOTCEI(2) ) THEN
C
CD J3041  COMPUTE [DOTDE2]
C  -----------------------
C
                  DOTDEI(2) = DOTDCI(2)
C
CD J3061  COMPUTE [DOXE2]
C  ----------------------
C
                  DOXEI(2) = DOCJ61
C
CD ELSE
C  ----
C
                ELSE
C
CD J3040  COMPUTE [DOTDE2]  TCV COLD SIDE TEMP ERROR  [deg_C]
C  ----------------------------------------------------------
C
                  DOTDEI(2) = DOTCEI(2)
C
CD J3060  COMPUTE [DOXE2]  TCV COLD SIDE TEMP GAIN
C  -----------------------------------------------
C
                  DOXEI(2) = DOCJ60
C
CD ENDIF  J3000
C  ------------
C
                ENDIF
C
CD J3300  COMPUTE [DOUBL1]  TCV COLD SIDE INTEG RATE
C  -------------------------------------------------
C
                DOUBLI(1) = AMIN1 ( AMAX1 ( DOXEI(2) * DOTDEI(2), DOUBC)
     &                      , DOUBO )
C
CD J3320  COMPUTE [DOVBL1]  TCV COLD SIDE INTEG POSN
C  -------------------------------------------------
C
                DOVBLI(1) = AMIN1 ( AMAX1 ( DOVBLI(1) + DOUBLI(1), 0.0 )
     &  ,
     &1.0)
C
CD J3340  COMPUTE [DOUB1]  TCV COLD SIDE RATE
C  ------------------------------------------
C
                DOUBI(1) = AMIN1 ( AMAX1 ( DOVBLI(1) + DOCJ80 * DOUBLI(1
     &  )
     &-DOVBI(1),DOUBC),DOUBO)
C
CD J3360  COMPUTE [DOVB1]
C  ----------------------
C
                DOVBI(1) = AMIN1 ( AMAX1 ( DOVBI(1) + DOUBI(1), 0.0 ) ,
     &                      1.0 )
C
CD ENDIF  J2800
C  ------------
C
              ENDIF
C
CD ENDIF J2500
C  -----------
C
            ENDIF
C
CD ENDIF  J2020
C  ------------
C
          ENDIF
C
CD ENDIF  J2000
C  ------------
C
        ENDIF
C
CD J3380  COMPUTE [DOVH1]  TCV HOT SIDE POSN
C  -----------------------------------------
C
        DOVHI(1) = 1.0 - DOVBI(1)
C
CD ENDIF  J200
C  -----------
C
      ENDIF
C
C
CD ----------------------------------------------------
CD Function L -
CD ----------------------------------------------------
C
C
CD L200  IF ( DZF300 ) THEN
C  ------------------------
C
      IF ( DZF300 ) THEN
C
CD  ELSE
C   ----
C
      ELSE
C
CD L220  IF ( DOGM1 .OR. DOGA ) THEN
C  -----------------------
C
       IF ((DOGMI(1).OR.DOGA).AND..NOT.(DOZDI(1).OR.DOZDI(2))) THEN
C
CD L240  IF ( DOSH1 .OR DOGA ) THEN
C  ---------------------------------
C
          IF ( IDDOSHI(1) .OR. DOGA ) THEN
C
CD L282  COMPUTE [DOUT]
C  ---------------------
C
            DOUT = DOUTO
C
CD L260  ELSEIF ( DOSC1 OR DOGAI1 ) THEN
C  ---------------------------
C
          ELSEIF ( IDDOSCI(1) .OR. DOGAI(1) ) THEN
C
CD L280  COMPUTE [DOUT]  TRIM F_DECK SIDE RATE
C  --------------------------------------------
C
            DOUT = DOUTC
C
CD ELSE
C  ----
C
          ELSE
C
CD L281  COMPUTE [DOUT]
C  ---------------------
C
            DOUT = 0.0
C
CD ENDIF  L240
C  -----------
C
          ENDIF
C
CD L300  COMPUTE [DOVT1]  TRIM ACTUATOR  POSN
C  -------------------------------------------
C
          DOVT = AMIN1 ( AMAX1 ( DOVT + DOUT , 0.0),DOVTF)
C
CD L320  COMPUTE [DOVTL] TRIM ACTUATOR INTEG. POS.
C        -----------------------------------------
C
          DOVTL = DOVT
C
CD ELSE  L220
C  ----------
C
        ELSE
C
CD L500  IF ( NOT DOZTA AND DOBAI ) THEN
C  --------------------------------
C
            IF ( .NOT. DOZTA .AND. DOBAI(1) ) THEN
C
CD L540  COMPUTE [DOTS1]  F_DECK TEMPERATURE SEL  [deg_C]
C  ------------------------------------------------------
C
              DOTSI(1) = DOTSI(1) + DOXS * ( DOHSI(1) - DOTSI(1) )
C
CD L560  COMPUTE [DOTDS1]  F_DECK DUCT TEMP SENSED  [deg_C]
C  --------------------------------------------------------
C
              DOTDSI(1) = DNTDI(1) + DOCL20 * ( DNTDAI(1) - DNTDI(1) )
C
CD L580  COMPUTE [DOTCS1]  F_DECK TEMP SENSED  [deg_C]
C  ---------------------------------------------------
C
              DOTCSI(1) = DNTCI(1) + DOCL30 * ( DNTCAI(1) - DNTCI(1) )
C
CD L800  IF ( DOZD2 ) THEN
C
         IF ( DOZDI(2) ) THEN
C
CD L1322  COMPUTE [DOVTL1]
C  -----------------------
C
            DOVTL = DOVT
C
CD L1342  COMPUTE [DOUT]
C  ----------------------
C
            DOUT = DOUTO
C
CD L1362  COMPUTE [DOVT]
C  ----------------------
C
            DOVT = AMIN1 ( DOVT + DOUT , 1.0 )
C
CD    ELSE
C     ----
C
      ELSE
C
CD L820  IF ( DOZD1 ) THEN
C  -----------------------
C
              IF ( DOZDI(1) ) THEN
C
CD L1321  COMPUTE [DOVTL1]
C  -----------------------
C
                DOVTL = DOVT
C
CD L1341  COMPUTE [DOUT]
C  ----------------------
C
                DOUT = DOUTC
C
CD L1361  COMPUTE [DOVT2]
C  ----------------------
C
                DOVT = AMAX1 ( DOVT + DOUT , 0.0 )
C
CD ELSE  L800
C  ----------
C
              ELSE
C
CD L820  COMPUTE [DOTDH1]  F_DECK DUCT HIGHER TEMP ERROR  [deg_C]
C  --------------------------------------------------------------
C
                DOTDHI(1) = DOTDSI(1) - DOCL40
C
CD L840  COMPUTE [DOTDC1]  F_DECK DUCT LOWER TEMP ERROR  [deg_C]
C  -------------------------------------------------------------
C
                DOTDCI(1) = DOTDSI(1) - DOCL50
C
CD L860  COMPUTE [DOTDCE1]  F_DECK ZONE TEMP ERROR  [deg_C]
C  --------------------------------------------------------
C
                DOTCEI(1) = DOTCSI(1) - DOTSI(1)
C
CD L1000  IF ( DOTDH1 GREATER THAN DOTCE1 ) THEN
C  ---------------------------------------------
C
                IF ( DOTDHI(1) .GT. DOTCEI(1) ) THEN
C
CD L1042  COMPUTE [DOTDE1]
C  -----------------------
C
                  DOTDEI(1) = - DOTDHI(1)
C
CD L1062  COMPUTE [DOXE1]
C  ----------------------
C
                  DOXEI(1) = DOCL61
C
CD ELSEIF ( DOTDC1 LESS THAN DOTCE1 ) THEN
C  ---------------------------------------
C
                ELSEIF ( DOTDCI(1) .LT. DOTCEI(1) ) THEN
C
CD L1041  COMPUTE [DOTDE1]
C  -----------------------
C
                  DOTDEI(1) =  - DOTDCI(1)
C
CD L1061  COMPUTE [DOXE1]
C  ----------------------
C
                  DOXEI(1) = DOCL61
C
CD ELSE
C  ----
C
                ELSE
C
CD L1040  COMPUTE [DOTDE1]  TRIM F_DECK SIDE TEMP ERROR  [deg_C]
C  -------------------------------------------------------------
C
                  DOTDEI(1) = - DOTCEI(1)
C
CD L1060  COMPUTE [DOXE1]  TRIM F_DECK SIDE TEMP GAIN
C  --------------------------------------------------
C
                  DOXEI(1) = DOCL60
C
CD ENDIF L1000
C  -----------
C
                ENDIF
C
CD L1300  COMPUTE [DOUTL1]  TRIM F_DECK SIDE INTEG RATE
C  ----------------------------------------------------
C
                DOUTL = AMIN1 ( AMAX1 ( DOXEI(1) * DOTDEI(1) , DOUTC
     &                      ), DOUTO )
C
CD L1320  COMPUTE [DOVTL1]  TRIM F_DECK SIDE INTEG POSN
C  ----------------------------------------------------
C
                DOVTL = AMIN1 ( AMAX1 ( DOVTL + DOUTL , 0.0
     &                      ), 1.0 )
C
CD L1340  COMPUTE [DOUT]  TRIM ACTUATOR RATE
C  ---------------------------------------------
C
                DOUT = AMIN1 ( AMAX1 ( DOVTL + DOCL80 * DOUTL
     &                     - DOVT , DOUTC ), DOUTO )
C
CD L1360  COMPUTE [DOVT1]  TRIM ACTUATOR POSN
C  ---------------------------------------------
C
                DOVT = AMIN1 ( AMAX1 ( DOVT + DOUT , 0.0 ),
     &                   1.0 )
C
CD ENDIF  L800
C  -----------
C
              ENDIF
C
CD ENDIF  L520
C  -----------
C
            ENDIF
C
CD ELSE  L500
C  ----------
C
          ELSE
C
CD ENDIF  L500
C  -----------
C
          ENDIF
C
CD ENDIF  L220
C  -----------
C
        ENDIF
C
CD ----------------------------------------------------
CD Function M -
CD ----------------------------------------------------
C
C
CD M220  IF ( DOBM1 ) THEN
C  -----------------------
C
          IF ( DOBMI(1) ) THEN
C
CD M240  IF ( DOSAF ) THEN
C  -----------------------
C
            IF ( IDDOSAF ) THEN
C
CD M260  COMPUTE [DOUD]  DIVERTER VALVE ROTARY ACT SPEED
C  -----------------------------------------------------
C
              DOUD = DOUDO
C
CD M280  ELSEIF ( DOSAC ) THEN
C  ---------------------------
C
            ELSEIF ( IDDOSAC ) THEN
C
CD M300  COMPUTE [DOUD]  DIVERTER VALVE ROTARY ACT SPEED
C  -----------------------------------------------------
C
              DOUD = DOUDC
C
CD ELSE
C  ----
C
            ELSE
C
CD M301  COMPUTE [DOUD]
C  --------------------
C
              DOUD = 0.0
C
CD ENDIF
C  -----
C
            ENDIF
C
CD ELSE  M220
C  ----------
C
          ELSE
C
CD M261  COMPUTE [DOUD]
C  --------------------
C
            DOUD = 0.0
C
CD ENDIF  M220
C  -----------
C
          ENDIF
C
CD M500  COMPUTE [DOVD]  DIVERTER VALVE ROTARY ACT POSN
C  ----------------------------------------------------
C
          DOVD = AMIN1 ( AMAX1 ( DOVD + DOUD , 0.0 ), 1.0 )
C
CD ENDIF  L200
C  -----------
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function N -
CD ----------------------------------------------------
C
C
CD N200  COMPUTE [DOWD1]  F_DECK LAG AIRFLOW  [lb/min]
C  ---------------------------------------------------
C
      DOWDI(1) = DOWDI(1) + DOXD * ( DNWDI(1) + DNWFD - DOWDI(1) )
C
CD N220  COMPUTE [DOWD]  F_DECK AIRFLOW  [lb/min]
C  ----------------------------------------------
C
       DOWD = DNWDI(1) + DNWFD + DOCN20 * ( DNWDI(1) + DNWFD
     &                                                - DOWDI(1) )
C
       IF ( DOWD .LT. 0.0 ) THEN
       DOWD = 0.0
       ELSE
        IF ( DOWD .GT. DNWDI(1) ) THEN
        DOWD =DNWDI(1)
        ELSE
        ENDIF
       ENDIF
C
C
CD N240  DO I = 1, 2
C  -----------------
C
      DO I = 1, 2
C
CD  COMPUTE [DOVDI]  COMF VALVE DEM POSN
C   ------------------------------------
C
        DOVDI(I) = DOCN40 * DOWD
        IF ( DOVDI(I) .GT. 1.0 ) THEN
        DOVDI(I) = 1.0
        ELSE
         IF ( DOVDI(I) .LT. 0.0 ) THEN
         DOVDI(I) = 0.0
         ELSE
         ENDIF
        ENDIF
C
CD END DO
C  ------
C
      END DO
C
C
CD P200 RESET [DOFOI,DOFCLI] COMF VALVE CLOSE/OPEN FLAGS
C       ------------------------------------------------
       DO I = 1 , 2
C
        DOFOI(I)  = .FALSE.
        DOFCLI(I) = .FALSE.
C
CD P220 COMPUTE [DOFOI,DOFCLI] COMF VALVE CLOSE/OPEN FLAGS
C       --------------------------------------------------
C
        IF ( DOVDI(1) .GT. DOCP140 ) THEN
C P241
         IF ( DOVFFI(I) .LT. DOCP143 ) THEN
C P261
          DOFOI(I) = .TRUE.
C P281
          DOFCLI(I) = .FALSE.
C
         ENDIF ! P241
C
        ELSE ! P220
C P240
         IF ( DOVDI(1) .LT. DOCP141 ) THEN
C P242
          IF ( DOVFFI(I) .GT. DOCP142 ) THEN
C P260
           DOFOI(I) = .FALSE.
C P280
           DOFCLI(I) =  .TRUE.
C
          ENDIF ! P242
C
         ELSE ! P240
C P500
          DOVEI(I) = DOVDI(1) - DOVFFI(I)
C P520
          IF ( DOVEI(I) .GT. 0.0 ) THEN
C P541
           DOFOI(I) = .TRUE.
C P561
           DOFCLI(I) = .FALSE.
C
          ELSE ! P520
C P540
           DOFOI(I) = .FALSE.
C P560
           DOFCLI(I) = .TRUE.
C
          ENDIF ! P520
C P1000
          DOVMI(I) = ABS( DOVEI(I) )
C P1020
          IF ( DOVMI(I) .LT. DOCP160 ) THEN
C P1040
           IF ( DOVMI(I) .GT. DOCP161 ) THEN
C P1060
            DODMI(I) = DOCP180 * ( DOCP181 - DOVMI(I) )
C P1080
            DODDI(I) = DODDI(I) - DOTL
C P1100
            IF ( DODDI(I) .LT. 0.0 ) THEN
C P1120
             DODDI(I) = DODMI(I)
C
            ELSE ! P1120
C P1801
             DOFOI(I) = .FALSE.
C P1821
             DOFCLI(I) = .FALSE.
C
            ENDIF ! P1120
C
           ELSE ! P1040
C P1800
            DOFOI(I) = .FALSE.
C P1820
            DOFCLI(I) = .FALSE.
C
           ENDIF ! P1040
C
          ENDIF ! P1020
C
         ENDIF ! P240
C
        ENDIF ! P220
C P2000
        DOKOI(I) = DOFOI(I)
C P2020
        DOKCI(I) = DOFCLI(I)
C
       ENDDO ! P200
C
C
C
C
C  ----------------------------------------------------------------------------
C
      ENDIF              ! FIN DU PROGRAMME
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00795 ----------------------------------------------------
C$ 00796 Function MAIN - Time Dependent Initialization
C$ 00797 ----------------------------------------------------
C$ 00800 200  IF ( YITIM  NOT EGUAL  DOTL ) THEN
C$ 00805 240  UPDATE [DOTL]  ITER TIME LAST   [sec]
C$ 00810 260  UPDATE [DOT2]  SUB-BAND ITER TIME  [sec]
C$ 00815 400  UPDATE [DOUBO]  TCV OPEN MAX RATE  [coeff/sec]
C$ 00820 420  UPDATE [DOUBC]  TCV CLOSE MAX RATE  [coeff/sec]
C$ 00825 440  UPDATE [DOXS]  TEMPERATURE SELECT TIME CONST  [sec]
C$ 00830 460  UPDATE [DOUTO]  TRIM VALVE OPEN MAX RATE  [coeff/sec]
C$ 00835 480  UPDATE [DOUTC]  TRIM VALVE CLOSE MAX RATE  [coeff/sec]
C$ 00840 500  UPDATE [DOXD]  F_DECK AIRFLOW TIME CONST  [sec]
C$ 00845 520  UPDATE [DOUDO]  DIVERTER VALVE OPEN MAX RATE  [coeff/sec]
C$ 00850 540  UPDATE [DOUDC]  DIVERTER VALVE CLOSE MAX RATE  [coeff/sec]
C$ 00855 600  UPDATE [DOXA1]  FLT COMPT FILTERED TEMP TIME CONST
C$ 00860 620  UPDATE [DOXA2]  CABIN FILTERED TEMP TIME CONST
C$ 00865 640  UPDATE [DOUFO]  PRSOV OPEN MAX RATE
C$ 00870 660  UPDATE [DOUFC]  PRSOV CLOSE MAX RATE
C$ 00875 1000  COMPUTE [DOCI220]
C$ 00881 1020  COMPUTE [DOCI221]
C$ 00886 1040  COMPUTE [DOCI224]
C$ 00891 1060  COMPUTE [DOXFU]
C$ 00896 1080  COMPUTE [DOXF] PRSOV GAIN FACTOR
C$ 00901 ENDIF
C$ 00909 ----------------------------------------------------
C$ 00910 Function A - Time Dependent Initialization
C$ 00911 ----------------------------------------------------
C$ 00914 A100  IF ( DZF300 ) THEN
C$ 00919 A120  UPDATE [K]  300-INDEX
C$ 00924 ELSE
C$ 00929 A121  UPDATE [K]  300-INDEX
C$ 00934 ENDIF
C$ 00941 ----------------------------------------------------
C$ 00942 Function B -
C$ 00943 ----------------------------------------------------
C$ 00950 B220  COMPUTE [DOGFD]  FAN CONTROL RELAY (2122-K1)
C$ 00955 ELSE
C$ 00960 B221  COMPUTE [DOGFD]
C$ 00965 ENDIF
C$ 00970 B240  COMPUTE [DOFF]  FLIGHT COMPT CMD FLAG
C$ 00975 B260  DO I = 1, K
C$ 00980 IF ( DOSRI ) THEN
C$ 00985 B280 COMPUTE [DOGFI]  FAN CONTROL RELAY  (2123-K1)
C$ 00990 ELSE
C$ 00995 COMPUTE [DOGRI]
C$ 01000 ENDIF
C$ 01005 B300  COMPUTE [DOFRI]  RECIRC FAN CMD FLAG
C$ 01010 B500 COMPUTE [DOFE] AVIONICS FAN CMD FLAG
C$ 01016 ----------------------------------------------------
C$ 01017 Function D -
C$ 01018 ----------------------------------------------------
C$ 01020 D200  IF ( ( NOT DOZCI ) ) THEN
C$ 01025 D220  IF ( DOKTCI  GREATER THEN  DOTCTI ) THEN
C$ 01030 D240  COMPUTE [DOTCTI]  A/C OVERTEMP SW SETTING
C$ 01035 D260  COMPUTE [DOFCI]  A/C OVERTEMP FLAG
C$ 01040 ELSE
C$ 01045 D241  COMPUTE [DOTCTI]
C$ 01050 D261  COMPUTE [DOFCI]
C$ 01055 ENDIF
C$ 01060 ENDIF
C$ 01065 END DO
C$ 01070 D500  DO I = 1, 2
C$ 01075 IF ( NOT DOZOI ) THEN
C$ 01080 D520  IF ( DNTDI .GT. DOTDTI ) THEN
C$ 01085 D540  COMPUTE [DOTDTI]  DUCT OVERHEAT SW SETTING
C$ 01090 D560  COMPUTE [DOFAI]  DUCT OVERHEAT FLAG
C$ 01095 ELSE
C$ 01100 D541  COMPUTE [DOTDTI]
C$ 01105 D561  COMPUTE [DOFAI]
C$ 01110 ENDIF
C$ 01115 ENDIF
C$ 01120 D580  COMPUTE [DOHAI]  FILTERED TEMP SELECTED
C$ 01127 ----------------------------------------------------
C$ 01128 Function F -
C$ 01129 ----------------------------------------------------
C$ 01132 F200  IF ( DOBAI ) THEN
C$ 01137 F220  COMPUTE [DOGAI]  DUCT OVERHEAT RELAY (K3-K4)
C$ 01142 F240  COMPUTE [DOHSI]  TEMPERATURE SELECTED
C$ 01147 F260  COMPUTE [DOHSLI] TEMPERATURE SELECTED LAST
C$ 01152 ELSE
C$ 01157 F221  COMPUTE [DOGAI]
C$ 01162 F241  COMPUTE [DOHSI]
C$ 01167 ENDIF
C$ 01172 F260  COMPUTE [DO$KAI]  CABIN / FLT COMPT HOT LIGHTS
C$ 01177 F500  IF ( DZF300 ) THEN
C$ 01182 F520  IF ( DOBMI ) THEN
C$ 01187 F540  COMPUTE [DOGDI]  PACK OFF/AUTO SOL. (K14-K15)
C$ 01192 F560  COMPUTE [DOGCI]  A/C OVERTEMP SOLENOID (K9-K10)
C$ 01197 F580  IF ( DOFAI ) THEN
C$ 01202 F600  COMPUTE [DODAI]  DUCT OVERTEMP TIME DELAY  [sec]
C$ 01207 ELSE
C$ 01212 COMPUTE [DODAI]
C$ 01217 ENDIF
C$ 01222 F620  COMPUTE [DOFBI]  DUCT OVERTEMP SOLENOID (K11-K12)
C$ 01227 F900  COMPUTE [DOGBI]  PRSOV SOLENOID (K11-K12)
C$ 01232 ELSE
C$ 01237 F541  COMPUTE [DOGDI]
C$ 01242 F561  COMPUTE [DOGCI]
C$ 01247 F601  COMPUTE [DODAI]
C$ 01252 F621  COMPUTE [DOFBI]
C$ 01257 ENDIF
C$ 01262 F920  COMPUTE [DO$KCI]  CABIN/FLT COMPT PACK HOT LIGHTS
C$ 01267 F940  COMPUTE [DOGMI]  AUTO/MANUAL RELAY (K1-K2)
C$ 01273 ELSE
C$ 01278 F921  COMPUTE [DO$KCI]  CABIN/FLT COMPT PACK HOT LIGHTS
C$ 01283 F941  COMPUTE [DOGMI]
C$ 01289 ENDIF
C$ 01294 F980  END DO
C$ 01301 ----------------------------------------------------
C$ 01302 Function H -
C$ 01303 ----------------------------------------------------
C$ 01306 H200  IF ( DZF300 ) THEN
C$ 01311 I200  IF ( DOBXL ) THEN
C$ 01316 I220  COMPUTE [DOXFF]  PRSOV FLOW CONT SERVO FACT
C$ 01321 ELSE
C$ 01326 I221  COMPUTE [DOXFF]
C$ 01331 ENDIF
C$ 01336 I240  COMPUTE [DOXA]  PRSOV ALTITUDE FACT
C$ 01341 I500  IF ( DOPF1  GREATER THAN  DOPF2 ) THEN
C$ 01346 I520  COMPUTE [DOPS]  SET DOWN LIMITER PRESS.
C$ 01351 ELSE
C$ 01356 I521  COMPUTE [DOPS]
C$ 01361 ENDIF
C$ 01366 I540  COMPUTE [DOPSD]  SET DOWN LIMITER DIFF PRESS  [psia]
C$ 01371 I700  DO I = 1, K
C$ 01376 COMPUTE [DOPFPI]  PRSOV SUPPLY PRESSURE  [psia]
C$ 01381 I720  COMPUTE [DOXPI]  PRSOV CONT SUPP PRESS FACT
C$ 01387 I740  COMPUTE [DOXSI]  PRSOV FLOW CONT SERVO ACT FACT
C$ 01392 I760  IF ( DOPSD GREATER THAN DOCI110 ) THEN
C$ 01397 I781  COMPUTE [DOXL]
C$ 01402 ELSE
C$ 01407 I780  COMPUTE [DOXL]  SET DOWN LIMITER FACTOR
C$ 01413 ENDIF
C$ 01418 I800  COMPUTE [DOPFSI]  PRSOV PRESS REG SETTING
C$ 01424 I1000 IF ( NOT DOZFI ) THEN
C$ 01429 I1020 IF ( NOT DOGBI ) THEN
C$ 01434 I1040 COMPUTE [DOPDSI]  PNEU DUCT SENSED PRESS  [psia]
C$ 01440 I1060 COMPUTE [DOPFEI]  PRSOV PRESSURE DEVIATION  [psia]
C$ 01445 I1200 IF ( DOPFEI GREATER THEN DOCI221 ) THEN
C$ 01450 I1242  COMPUTE [DOPFUI]
C$ 01455 I1282  COMPUTE [DOXFI]
C$ 01460 ELSEIF ( DOPFEI LESS THAN DOCI224 ) THEN
C$ 01465 I1241  COMPUTE [DOPFUI]
C$ 01470 I1281  COMPUTE [DOXFI]
C$ 01475 ELSE
C$ 01480 I1240  COMPUTE [DOPFUI]  PRSOV UP CHAMB PRESSURE  [psia]
C$ 01485 I1260  COMPUTE [DOXFUI]  PRSOV UP CHAMB GAIN
C$ 01490 I1280  COMPUTE [DOXFI]  PRSOV GAIN
C$ 01496 ENDIF  I1200
C$ 01501 ELSE I1020
C$ 01506 I1041  COMPUTE [DOPFUI]
C$ 01511 I1061  COMPUTE [DOXFI]
C$ 01516 ENDIF  I1020
C$ 01521 I1500  COMPUTE [DOPFGI]  PRSOV SPRING PRESSURE  [psia]
C$ 01526 I1520  COMPUTE [DOUFLI]  PRSOV INTEGRAL RATE
C$ 01532 I1540  COMPUTE [DOVFLI]  PRSOV INTEGRAL PSN
C$ 01538 I1560  COMPUTE [DOUFI]  PRSOV RATE
C$ 01544 I1580  COMPUTE [DOVFI]  PRSOV POSITION
C$ 01549 ENDIF  I1000
C$ 01559 J200 IF ( NOT DOZBI ) THEN
C$ 01565 J240  IF ( DOGMI ) THEN
C$ 01570 J260 IF ( DOSCI ) THEN
C$ 01575 J302  COMPUTE [DOUBI]
C$ 01580 J280  ELSEIF ( DOSHI ) THEN
C$ 01585 3000  COMPUTE [DOUBI]  TCV COLD SIDE RATE
C$ 01590 ELSE
C$ 01595 J301  COMPUTE [DOUBI]
C$ 01600 ENDIF  J260
C$ 01605 J400  COMPUTE [DOVBI]  TCV COLD SIDE POSN
C$ 01611 J420  COMPUTE [DOVBLI]  TCV COLD SIDE INTEG POSN
C$ 01616 ELSE J240
C$ 01621 J500  IF ( NOT DOZAAI ) THEN
C$ 01626 J520  COMPUTE [DOTSI]  COMPT TEMPERATURE SELECTED  [deg_C]
C$ 01631 J540  COMPUTE [DOTDSI]  COMPT DUCT TEMP SENSED  [deg_C]
C$ 01636 J560  COMPUTE [DOTCSI]  COMPT TEMPERATURE SENSED  [deg_C]
C$ 01641 J800  IF ( DOZTI ) THEN
C$ 01646 J1321  COMPUTE [DOVBLI]
C$ 01651 J1341  COMPUTE [DOUBI]
C$ 01656 J1361  COMPUTE [DOVBI]
C$ 01661 ELSE  J800
C$ 01666 J820  COMPUTE [DOTDHI]  ZONE DUCT HIGHER TEMP ERROR  [deg_C]
C$ 01671 J840  COMPUTE [DOTDCI]  ZONE DUCT LOWER TEMP ERROR  [deg_C]
C$ 01676 J860  COMPUTE [DOTCEI]  ZONE TEMP ERROR  [deg_C]
C$ 01681 J1000 IF ( DOTDHI  GREATER THAN  DOTCEI ) THEN
C$ 01686 J1042  COMPUTE [DOTDEI]
C$ 01691 J1062  COMPUTE [DOXEI]
C$ 01696 J1020  ELSEIF ( DOTDCI LESS THAN DOTCEI ) THEN
C$ 01701 J1041  COMPUTE [DOTDEI]
C$ 01706 J1061  COMPUTE [DOXEI]
C$ 01711 ELSE
C$ 01716 J1040  COMPUTE [DOTDEI]  TCV COLD SIDE TEMP ERROR  [deg_C]
C$ 01721 J1060  COMPUTE [DOXEI]  TCV COLD SIDE TEMP GAIN
C$ 01726 ENDIF  J1000
C$ 01731 J1300  COMPUTE [DOUBLI]  TCV COLD SIDE INTEG RATE
C$ 01737 J1320  COMPUTE [DOVBLI]  TCV COLD SIDE INTEG POSN
C$ 01743 J1340  COMPUTE [DOUBI]  TCV COLD SIDE RATE
C$ 01749 J1360  COMPUTE [DOVBI]
C$ 01755 ENDIF  J800
C$ 01760 ENDIF J500
C$ 01765 ENDIF  J240
C$ 01770 ENDIF  J200
C$ 01775 J1380  COMPUTE [DOVHI]  TCV HOT SIDE POSN
C$ 01780 END DO
C$ 01785 ELSE  H200
C$ 01794 H220 COMPUTE [DOFB]  PACK CONTINUITY FLAG
C$ 01814 H320  COMPUTE [DOFT]  TRIM CONTINUITY FLAG
C$ 01833 H500  IF ( DOBA1 ) THEN
C$ 01838 H520  IF ( DOFB  OR  DOSM2 ) THEN
C$ 01843 H540  COMPUTE [DOGAC]  RETURN TO CENTER RLY (K6)
C$ 01848 H560  COMPUTE [DOGA]  RETURN TO CENTER RLY (K7)
C$ 01853 H600  COMPUTE [DOVTF] TRIM ACTUATOR DEFAULT POS
C$ 01857 ELSE
C$ 01862 H541  COMPUTE [DOGAC]
C$ 01867 H561  COMPUTE [DOGA]
C$ 01872 H581 COMPUTE
C$ 01875 ENDIF
C$ 01880 ELSE
C$ 01885 H542  COMPUTE [DOGAC]
C$ 01890 H562  COMPUTE [DOGA]
C$ 01895 H582  COMPUTE
C$ 01899 ENDIF  H500
C$ 01904 J2000  IF ( NOT DOZB2 ) THEN
C$ 01909 J2020  IF ( DOGM2 ) THEN
C$ 01914 J2040 IF ( DOSC2 OR DOGA2 ) THEN
C$ 01919 J2082  COMPUTE [DOUB1]
C$ 01924 J2060  ELSEIF ( DOSH2 ) THEN
C$ 01929 J2080  COMPUTE [DOUB1]  TCV COLD SIDE RATE
C$ 01934 ELSE
C$ 01939 J2081  COMPUTE [DOUB1]
C$ 01944 ENDIF  J2040
C$ 01949 J2400  COMPUTE [DOVB1]  TCV COLD SIDE POSN
C$ 01955 J2420  COMPUTE [DOVBL1]  TCV COLD SIDE INTEG POSN
C$ 01960 ELSE J2020
C$ 01965 J2500  IF ( DOBA2 AND NOT DOZAA2 ) THEN
C$ 01970 J2520  COMPUTE [DOTS2]  COMPT TEMPERATURE SELECTED  [deg_C]
C$ 01975 J2540  COMPUTE [DOTDS2]  COMPT DUCT TEMP SENSED  [deg_C]
C$ 01980 J2560  COMPUTE [DOTCS2]  COMPT TEMPERATURE SENSED  [deg_C]
C$ 01985 J2800  IF ( DOZT2 AND ) THEN
C$ 01990 J3321  COMPUTE [DOVBL1]
C$ 01995 J3341  COMPUTE [DOUB1]
C$ 02000 J3361  COMPUTE [DOVB1]
C$ 02005 ELSE  J2800
C$ 02010 J2820  COMPUTE [DOTDH2]  ZONE DUCT HIGHER TEMP ERROR  [deg_C]
C$ 02015 J2840  COMPUTE [DOTDC2]  ZONE DUCT LOWER TEMP ERROR  [deg_C]
C$ 02020 J2860  COMPUTE [DOTCE2]  ZONE TEMP ERROR  [deg_C]
C$ 02025 J3000 IF ( DOTDH2  GREATER THAN  DOTCE2 ) THEN
C$ 02030 J3042  COMPUTE [DOTDE2]
C$ 02035 J3062  COMPUTE [DOXE1]
C$ 02040 J3020  ELSEIF ( DOTDC2 LESS THAN DOTCE2 ) THEN
C$ 02045 J3041  COMPUTE [DOTDE2]
C$ 02050 J3061  COMPUTE [DOXE2]
C$ 02055 ELSE
C$ 02060 J3040  COMPUTE [DOTDE2]  TCV COLD SIDE TEMP ERROR  [deg_C]
C$ 02065 J3060  COMPUTE [DOXE2]  TCV COLD SIDE TEMP GAIN
C$ 02070 ENDIF  J3000
C$ 02075 J3300  COMPUTE [DOUBL1]  TCV COLD SIDE INTEG RATE
C$ 02081 J3320  COMPUTE [DOVBL1]  TCV COLD SIDE INTEG POSN
C$ 02088 J3340  COMPUTE [DOUB1]  TCV COLD SIDE RATE
C$ 02095 J3360  COMPUTE [DOVB1]
C$ 02101 ENDIF  J2800
C$ 02106 ENDIF J2500
C$ 02111 ENDIF  J2020
C$ 02116 ENDIF  J2000
C$ 02121 J3380  COMPUTE [DOVH1]  TCV HOT SIDE POSN
C$ 02126 ENDIF  J200
C$ 02132 ----------------------------------------------------
C$ 02133 Function L -
C$ 02134 ----------------------------------------------------
C$ 02137 L200  IF ( DZF300 ) THEN
C$ 02142 ELSE
C$ 02147 L220  IF ( DOGM1 .OR. DOGA ) THEN
C$ 02152 L240  IF ( DOSH1 .OR DOGA ) THEN
C$ 02157 L282  COMPUTE [DOUT]
C$ 02162 L260  ELSEIF ( DOSC1 OR DOGAI1 ) THEN
C$ 02167 L280  COMPUTE [DOUT]  TRIM F_DECK SIDE RATE
C$ 02172 ELSE
C$ 02177 L281  COMPUTE [DOUT]
C$ 02182 ENDIF  L240
C$ 02187 L300  COMPUTE [DOVT1]  TRIM ACTUATOR  POSN
C$ 02192 L320  COMPUTE [DOVTL] TRIM ACTUATOR INTEG. POS.
C$ 02197 ELSE  L220
C$ 02202 L500  IF ( NOT DOZTA AND DOBAI ) THEN
C$ 02207 L540  COMPUTE [DOTS1]  F_DECK TEMPERATURE SEL  [deg_C]
C$ 02212 L560  COMPUTE [DOTDS1]  F_DECK DUCT TEMP SENSED  [deg_C]
C$ 02217 L580  COMPUTE [DOTCS1]  F_DECK TEMP SENSED  [deg_C]
C$ 02222 L800  IF ( DOZD2 ) THEN
C$ 02226 L1322  COMPUTE [DOVTL1]
C$ 02231 L1342  COMPUTE [DOUT]
C$ 02236 L1362  COMPUTE [DOVT]
C$ 02241 ELSE
C$ 02246 L820  IF ( DOZD1 ) THEN
C$ 02251 L1321  COMPUTE [DOVTL1]
C$ 02256 L1341  COMPUTE [DOUT]
C$ 02261 L1361  COMPUTE [DOVT2]
C$ 02266 ELSE  L800
C$ 02271 L820  COMPUTE [DOTDH1]  F_DECK DUCT HIGHER TEMP ERROR  [deg_C]
C$ 02276 L840  COMPUTE [DOTDC1]  F_DECK DUCT LOWER TEMP ERROR  [deg_C]
C$ 02281 L860  COMPUTE [DOTDCE1]  F_DECK ZONE TEMP ERROR  [deg_C]
C$ 02286 L1000  IF ( DOTDH1 GREATER THAN DOTCE1 ) THEN
C$ 02291 L1042  COMPUTE [DOTDE1]
C$ 02296 L1062  COMPUTE [DOXE1]
C$ 02301 ELSEIF ( DOTDC1 LESS THAN DOTCE1 ) THEN
C$ 02306 L1041  COMPUTE [DOTDE1]
C$ 02311 L1061  COMPUTE [DOXE1]
C$ 02316 ELSE
C$ 02321 L1040  COMPUTE [DOTDE1]  TRIM F_DECK SIDE TEMP ERROR  [deg_C]
C$ 02326 L1060  COMPUTE [DOXE1]  TRIM F_DECK SIDE TEMP GAIN
C$ 02331 ENDIF L1000
C$ 02336 L1300  COMPUTE [DOUTL1]  TRIM F_DECK SIDE INTEG RATE
C$ 02342 L1320  COMPUTE [DOVTL1]  TRIM F_DECK SIDE INTEG POSN
C$ 02348 L1340  COMPUTE [DOUT]  TRIM ACTUATOR RATE
C$ 02354 L1360  COMPUTE [DOVT1]  TRIM ACTUATOR POSN
C$ 02360 ENDIF  L800
C$ 02365 ENDIF  L520
C$ 02370 ELSE  L500
C$ 02375 ENDIF  L500
C$ 02380 ENDIF  L220
C$ 02385 ----------------------------------------------------
C$ 02386 Function M -
C$ 02387 ----------------------------------------------------
C$ 02390 M220  IF ( DOBM1 ) THEN
C$ 02395 M240  IF ( DOSAF ) THEN
C$ 02400 M260  COMPUTE [DOUD]  DIVERTER VALVE ROTARY ACT SPEED
C$ 02405 M280  ELSEIF ( DOSAC ) THEN
C$ 02410 M300  COMPUTE [DOUD]  DIVERTER VALVE ROTARY ACT SPEED
C$ 02415 ELSE
C$ 02420 M301  COMPUTE [DOUD]
C$ 02425 ENDIF
C$ 02430 ELSE  M220
C$ 02435 M261  COMPUTE [DOUD]
C$ 02440 ENDIF  M220
C$ 02445 M500  COMPUTE [DOVD]  DIVERTER VALVE ROTARY ACT POSN
C$ 02450 ENDIF  L200
C$ 02457 ----------------------------------------------------
C$ 02458 Function N -
C$ 02459 ----------------------------------------------------
C$ 02462 N200  COMPUTE [DOWD1]  F_DECK LAG AIRFLOW  [lb/min]
C$ 02467 N220  COMPUTE [DOWD]  F_DECK AIRFLOW  [lb/min]
C$ 02483 N240  DO I = 1, 2
C$ 02488 COMPUTE [DOVDI]  COMF VALVE DEM POSN
C$ 02501 END DO
C$ 02507 P200 RESET [DOFOI,DOFCLI] COMF VALVE CLOSE/OPEN FLAGS
C$ 02514 P220 COMPUTE [DOFOI,DOFCLI] COMF VALVE CLOSE/OPEN FLAGS
