C'Title              HEIGHT ABOVE TERRAIN
C'Module_ID          USD8VH
C'Entry_point        TERRAIN
C'Documentation      TBD
C'Application        Flight/Visual ground height interface
C'Author             Department 24, Flight
C'Date               January 1, 1990
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C'
C
C'Revision_history
C
C  usd8vh.for.4 16Oct2019 15:38 usd8 Tom    
C       < recompiled out of debug >
C
C  usd8vh.for.3  8Oct2019 23:01 usd8 Tom    
C       < increased LPHIMX and LTHETAM<PERSON> prevent crash on ground >
C
C  usd8vh.for.2 20Dec1991 17:39 usd8 PLAM
C       < Added Ident label >
C
C  usd8vh.for.1 20Dec1991 13:55 usd8 PAULVE
C       < COPY STF MODULE DOWN TO SITE >
C
C'
C
      SUBROUTINE USD8VH
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/20/91 - 13:57 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C
C'Ident
C
       CHARACTER*55   REV /
     -  '$Source: usd8vh.for.4 16Oct2019 15:38 usd8 Tom    $'/
C
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
CQ    USD8 XRFTEST(*)
C
C  INPUTS
C
CP    USD8
CPI  H  HTEST,
CPI  R  RTHELE,    RXMISICA,
CPI  V  VH,        VJBOX,     VTRIM,
CPI  V  VBOG,      VCSPSI,    VFRZVH,    VREPOS,    VSAHTDD1,  VSAHATNX,
CPI  V  VSAHATNY,  VSAHATNZ,  VSAHATON,  VSAHATV,   VSNPSI,    VUG,
CPI  V  VVEW,      VVG,       VVNS,      VZD,
C
C  OUTPUTS
C
CPO  T  TCFFLPOS,  TCMCRERR,  TCR0ASH,   RUFLT,     RUPOSN,
CPO  V  VCSPHIG,   VCSTHEG,   VHG,       VPHIG,     VSAHATLR,  VSAHATNR,
CP   V  VKINT,
CPO  V  VSAHATZR,  VSNPHIG,   VSNTHEG,   VTHETAG
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  8-Oct-2019 20:48:05 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*4   
     &  RTHELE         ! GROUND ELEVATION                       [FT]
     &, VCSPSI         ! COSINE OF VPSI
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VKINT          ! INTEGRATION CONSTANT
     &, VSAHATNX(10)   ! HAT TEST POINT EQN X PART
     &, VSAHATNY(10)   ! HAT TEST POINT EQN Y PART
     &, VSAHATNZ(10)   ! HAT TEST POINT EQN Z PART
     &, VSAHTDD1(10)   ! DD AS RECEIVED FROM DATA BLOCK
     &, VSNPSI         ! SINE OF VPSI
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
C$
      INTEGER*4
     &  VJBOX          ! INITIALIZATION COUNTER
C$
      LOGICAL*1
     &  HTEST          ! ATG TEST ACTIVE
     &, VBOG           ! ON GROUND FLAG
     &, VFRZVH         ! FREEZE HEIGHT ABOVE TERRAIN MODULE
     &, VREPOS         ! REPOSITION FLAG
     &, VSAHATON       ! HAT ENABLED
     &, VSAHATV        ! HAT AVAILABLE AND VALID
C$
      INTEGER*1
     &  RXMISICA(4,5)  ! 68 AIRPORT ICAO IDENT (ASCII)
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229             
C$
      REAL*4   
     &  VCSPHIG        ! COSINE OF THE GROUND ROLL  ANGLE
     &, VCSTHEG        ! COSINE OF THE GROUND PITCH ANGLE
     &, VHG            ! GROUND ELEVATION                        [ft]
     &, VPHIG          ! ROLL  ANGLE OF THE GROUND @VPSI
     &, VSAHATLR(3)    ! HAT TEST POINT REQ'D LAT OR X
     &, VSAHATNR(3)    ! HAT TEST POINT REQ'D LON OR Y
     &, VSAHATZR(3)    ! HAT TEST POINT REQ'D ELE OR Z
     &, VSNPHIG        ! SINE   OF THE GROUND ROLL  ANGLE
     &, VSNTHEG        ! SINE   OF THE GROUND PITCH ANGLE
     &, VTHETAG        ! PITCH ANGLE OF THE GROUND @VPSI
C$
      INTEGER*4
     &  RUPOSN         ! REPOS INDEX (BY RECORD NUMBER)
C$
      LOGICAL*1
     &  RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCMCRERR(15)   ! CAUSE OF CRASH
     &, TCR0ASH        ! CRASH
C$
      LOGICAL*1
     &  DUM0000001(16387),DUM0000002(8),DUM0000003(15)
     &, DUM0000004(551),DUM0000005(300),DUM0000006(468)
     &, DUM0000007(72),DUM0000008(4),DUM0000009(4)
     &, DUM0000010(128),DUM0000011(24),DUM0000012(8)
     &, DUM0000013(2140),DUM0000014(4),DUM0000015(288)
     &, DUM0000016(1080),DUM0000017(15403),DUM0000018(40)
     &, DUM0000019(86),DUM0000020(1),DUM0000021(1243)
     &, DUM0000022(4),DUM0000023(599),DUM0000024(38564)
     &, DUM0000025(228401),DUM0000026(141),DUM0000027(3816)
     &, DUM0000028(5699)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VREPOS,DUM0000002,VBOG,DUM0000003,VFRZVH,DUM0000004
     &, VVG,DUM0000005,VUG,DUM0000006,VSNPSI,VCSPSI,DUM0000007
     &, VVNS,VVEW,VPHIG,DUM0000008,VTHETAG,DUM0000009,VCSTHEG
     &, VSNTHEG,VCSPHIG,VSNPHIG,DUM0000010,VZD,DUM0000011,VHG
     &, DUM0000012,VH,DUM0000013,VJBOX,DUM0000014,VKINT,DUM0000015
     &, VTRIM,DUM0000016,HTEST,DUM0000017,VSAHATNX,VSAHATNY,VSAHATNZ
     &, DUM0000018,VSAHATLR,VSAHATNR,VSAHATZR,DUM0000019,VSAHATON
     &, DUM0000020,VSAHATV,DUM0000021,RUPOSN,DUM0000022,RUFLT
     &, DUM0000023,RTHELE,DUM0000024,RXMISICA,DUM0000025,TCFFLPOS
     &, DUM0000026,TCR0ASH,DUM0000027,VSAHTDD1,DUM0000028,TCMCRERR  
C------------------------------------------------------------------------------
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
C     REALS
C
      REAL
     &     LCSPHI         ! Cosine of ground roll angle
     &,    LCSTHE         ! Cosine of ground pitch angle
     &,    LD             ! Coefficient d  in plane equation
     &,    LOTHETA        ! Max allowable delta in phig or thetag   [rad]
     &,    LDELMAX        ! Max allowable delta in phig or thetag   [rad]
     &,    LDELTA         ! Change in phig and thetag               [rad]
     &,    LGAIN          ! Gain on ground speed factor
     &,    LCOUNT         ! Counter for reposition
     &,    LHG            ! Visual ground height above sea level     [ft]
     &,    LNX            ! Coefficient nx in plane equation
     &,    LNY            ! Coefficient ny in plane equation
     &,    LNZ            ! Coefficient nz in plane equation
     &,    LPHI           ! Roll angle of the ground                [rad]
     &,    LPHIMX         ! Maximum roll angle of the ground        [rad]
     &,    LPSI           ! Direction wrt A/C of ground normal      [rad]
     &,    LPSIN          ! Direction wrt PSI=0 of ground normal    [rad]
     &,    LSNPHI         ! Sine of ground roll angle
     &,    LSNTHE         ! Sine of ground pitch angle
     &,    LSS            ! Sum of LNX**2 + LNY**2 + LNZ**2
     &,    LSSI           ! 1./LSSI
     &,    LSP0           ! Spare real
     &,    LSP1           ! Spare real
     &,    LTHETA         ! Pitch angle of the ground               [rad]
     &,    LTHETAMX       ! Maximum pitch angle of the ground       [rad]
     &,    LV             ! Ground speed in inertial axes        [ft/sec]
     &,    LANGFADE         ! Angle fade
     &,    LANGRATE /0.00002 / ! Allowable change in angle (rads) per foot
     &,    LHGFADE          ! Elevation fade
     &,    LHGMIN           ! Minimum elevation fade
     &,    LHGRATE /0.05/    ! Allowable change in elevation (ft) per foot
C
C     INTEGERS
C
      INTEGER
     &    LRUPOSNP         ! Previous RUPOSN
     &,   LREFICAO         ! ICAO code for reference runway
     &,   LREFICP1         ! Previous ICAO code for runway
C
C     LOGICALS
C
      LOGICAL
     &     VDISHAT/.FALSE./ ! Disable HAT
     &,    LEQVALID         ! Flag to indicate a valid plane equation
     &,    LFADE /.FALSE./   !Fading pitch and roll of ground flag
     &,    LHATONP /.FALSE./ !Previous VSAHATON
     &,    LVBOG/.FALSE./   ! Last value of VBOG
C
C     PARAMETERS
C
      REAL
     &     PI             ! Pi
     &,    PHALFPI        ! Half pi
     &,    PTWOPI         ! Two pi
C
      PARAMETER ( PI       = 3.141592654
     &,           PHALFPI  = PI * 0.5
     &,           PTWOPI   = PI * 2.0 )
C
C     DATA
C
      DATA LGAIN    / 1.0E-05 /
C      DATA LPHIMX   / 0.15 /
C      DATA LTHETAMX / 0.15 /
C Increased because SIM crashed because of bumps in
C various scenes (LAS spot 19 for example)
      DATA LPHIMX   / 1.50 /
      DATA LTHETAMX / 0.35 /
C
      EQUIVALENCE (RXMISICA(1,3), LREFICAO)
C
      ENTRY TERRAIN
C
      IF (VFRZVH) RETURN
C
CD VH010 Plane equation coefficients.
CR       N/A
C
CC If a reposition has been activated and a change in the reference
CC airport has occured or an inair reposition has been performed,
CC the HEIGHT ABOVE TERRAIN logic is deactivated for 8 seconds.
C
      IF (RUFLT) THEN
        IF (RUPOSN .NE. LRUPOSNP) THEN
          LCOUNT = 3.0
        ENDIF
        IF (VBOG .AND. (.NOT.LVBOG)) THEN
          LCOUNT = 8.0
        ENDIF
        IF (LREFICAO .NE. LREFICP1) THEN
          LCOUNT = 12.0
        ENDIF
        LREFICP1 = LREFICAO
        LRUPOSNP = RUPOSN
        LVBOG = VBOG
      ENDIF
C
      IF (LCOUNT .GT. 0.0) THEN
        LCOUNT = LCOUNT - VKINT
      ENDIF
C
CC Check that height above terrain has been enabled and that it is
CC valid for this scene. Store the plane equation coefficients in
CC local variables to ensure they don't change within this module.
C
      IF ((VSAHATON.AND.VSAHATV) .AND. (.NOT. VDISHAT) .AND.
     &   (LCOUNT .LE. 0.0)) THEN
        LNX = VSAHATNY(1)  ! VISUAL X IS EAST  , FLIGHT X IS NORTH
        LNY = VSAHATNX(1)  ! VISUAL Y IS NORTH, FLIHGHT Y IS EAST
        LNZ = VSAHATNZ(1)  ! VISUAL Z IS UP
        LD  = VSAHTDD1(1)
C
CD VH020 Visual ground height above sea level (ft).
CR       N/A
C
CC Calculate the ground height above sea level as the ratio of d/z in
CC the plane equation. Ensure that the normal vector points up, and
CC that the calculated ground height lies between -1000 and 15000 ft.
C
        LEQVALID = .FALSE.
        IF (LNZ .GT. 0.0) THEN
          LHG = LD / LNZ
          IF (LHG.GT.-1000. .AND. LHG.LT.15000.) LEQVALID = .TRUE.
        ENDIF
C
CD VH030 Ground height above sea level (ft).
CR       N/A
C
CC Calculate the height of the ground above sea level directly below
CC the c.g. of the aircraft.
C
C
        LFADE = (VH .LT. 2000.) .AND. (VJBOX .GT. 15) .AND.
     &      (VTRIM .GE. 0.5) .AND. VSAHATON .AND. LHATONP
     &      .AND. (.NOT. HTEST)
C
        LHATONP = VSAHATON !Set previous HATON to current
C
        LHGFADE = 1.5 * ABS(VUG) * VKINT * ABS(TAN(VTHETAG))
        LHGMIN  = ABS(VUG) * VKINT * LHGRATE
C
        IF (LHGFADE .LT. LHGMIN) LHGFADE = LHGMIN
C
        IF (LEQVALID) THEN
          IF ((ABS(LHG-VHG) .GT. LHGFADE) .AND. LFADE) THEN
            IF ((LHG-VHG) .GT. 0.0) THEN
              VHG = VHG + LHGFADE
            ELSE
              VHG = VHG - LHGFADE
            ENDIF
          ELSE
            VHG = LHG
          ENDIF
C
CD VH040
CR       N/A
C
C
CD VH050 Visual pitch angle of the ground (rad).
CR       N/A
C
CC Calculate the pitch angle of the ground at A/C heading.
C
CC         ax + by +cz = dd ; thus at x=0 y=0 z = dd/c
CC         also z = dd - 1/c*(ax + by)
CC         For a displacement delta S along a/c heading the
CC         North displacement is delta S * cos( psi) and the
CC         East displacement is delta S * sin( psi) and
CC         therefore the vertical displacement is
CC         delta S *  ( -a/c cos(psi) -b/c sin(psi) )
CC         tan(ground pitch along a/c heading is)
CC            delta Z / delta S = -1/c * ( a cos(psi) + b sin(psi) )
CC         For a displacement delta S to the right along the A/C Y axis
CC         North displacement is -delta S * sin( psi) and the
CC         East displacement is delta S * cos( psi) and
CC         therefore the vertical displacement is
CC         delta S *  ( a/c sin(psi) -b/c cos(psi) )
CC         Also if ground slopes up to the right this cause a negative
CC         roll tan(ground roll across a/c heading is)
CC            delta Z / delta S = 1/c * ( -a sin(psi) + b cos(psi) )
C
          LSP0 = LNX * VCSPSI + LNY * VSNPSI
          LTHETA = ATAN(-LSP0/LNZ)
C
CD VH060 Visual roll angle of the ground (rad).
CR       N/A
C
CC Calculate the roll angle of the ground at A/C heading.
C
          LSP0 = -LNX * VSNPSI + LNY * VCSPSI
          LPHI = ATAN(LSP0/LNZ)
C
CD VH070 Excessive ground attitude while on ground.
CR       N/A
C
          IF (VBOG .AND. .NOT. VREPOS) THEN
            IF ((ABS(LPHI) .GT. LPHIMX) .OR.
     &          (ABS(LTHETA) .GT. LTHETAMX)) THEN
              TCMCRERR(5) = .TRUE.
              TCR0ASH     = .TRUE.
              TCFFLPOS    = .TRUE.
            ENDIF
          ENDIF
C
CD VH080 Change limit on ground pitch and roll angles (rad).
CR       N/A
C
CC Calculate a maximum allowable change in ground pitch and roll
CC angles based on total inertial speed. If the aircraft is in the
CC air, no limit is placed on grpund pitch and roll angle changes.
C
C
          IF ((VBOG) .AND. (.NOT. VREPOS)) THEN
            LV      = SQRT(VVNS*VVNS + VVEW*VVEW)
            LDELMAX = LGAIN * LV
          ELSEIF (LFADE) THEN
            LDELMAX = LANGRATE * ABS(VUG) * VKINT
          ELSE
            LDELMAX = PHALFPI
          ENDIF
C
CD VH090 Pitch angle of the ground (rad).
CR       N/A
C
CC Calculate the actual pitch angle of the ground used for the
CC direction cosines.
C
          LDELTA  = AMIN1(LDELMAX,AMAX1(-LDELMAX,LTHETA-VTHETAG))
          VTHETAG = VTHETAG + LDELTA
C
CD VH100 Roll angle of the ground (rad).
CR       N/A
C
CC Calculate the actual roll angle of the ground used for the
CC direction cosines.
C
          LDELTA = AMIN1(LDELMAX,AMAX1(-LDELMAX,LPHI-VPHIG))
          VPHIG  = VPHIG + LDELTA
C
CD VH110 Predicted position.
CR       N/A
C
CC Calculate the x, y and z offsets from the present a/c position to
CC a predicted position assuming a transfer delay of 150 msec.
C
          VSAHATLR(1) =  0.15 * VVG
          VSAHATNR(1) =  0.15 * VUG
          VSAHATZR(1) = -0.15 * VZD
        ENDIF
      ENDIF
C
CD VH120 Height above terrain inactive or invalid.
CR       N/A
C
CC If height above terrain has been deactivated or an invalid plane
CC equation has been received, fade back to level ground.
C
      IF (.NOT.(VSAHATON.AND.VSAHATV.AND.LEQVALID) .OR. VDISHAT) THEN
        VHG = RTHELE
        IF (VREPOS) THEN
          VTHETAG = 0.0
          VPHIG   = 0.0
        ELSE
          VTHETAG = VTHETAG + AMIN1(0.001,AMAX1(-0.00001,-VTHETAG))
          VPHIG   = VPHIG   + AMIN1(0.001,AMAX1(-0.00001,-VPHIG))
        ENDIF
      ENDIF
C
CD VH130 Sine and cosine of ground pitch and roll angles.
CR       N/A
C
      VCSTHEG = COS(VTHETAG)
      VSNTHEG = SIN(VTHETAG)
      VCSPHIG = COS(VPHIG)
      VSNPHIG = SIN(VPHIG)
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00241 VH010 Plane equation coefficients.
C$ 00278 VH020 Visual ground height above sea level (ft).
C$ 00291 VH030 Ground height above sea level (ft).
C$ 00320 VH040
C$ 00324 VH050 Visual pitch angle of the ground (rad).
C$ 00350 VH060 Visual roll angle of the ground (rad).
C$ 00358 VH070 Excessive ground attitude while on ground.
C$ 00370 VH080 Change limit on ground pitch and roll angles (rad).
C$ 00387 VH090 Pitch angle of the ground (rad).
C$ 00396 VH100 Roll angle of the ground (rad).
C$ 00405 VH110 Predicted position.
C$ 00417 VH120 Height above terrain inactive or invalid.
C$ 00434 VH130 Sine and cosine of ground pitch and roll angles.
