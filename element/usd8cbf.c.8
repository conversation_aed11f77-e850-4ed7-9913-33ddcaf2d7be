/******************************************************************************
C
C'Title                Toebrakes fast Band Control Model
C'Module_ID            usd8cbf.c
C'Entry_point          cbfast()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Secondarie control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8csxrf.ext", "usd8csdata.ext", "cf_fwd.mac", "cf_fspr.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include "zspring.h"
#include <servocal.h>
#include "usd8csxrf.ext"
#include "usd8csdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CBF010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
 
 
cbfast()
{
 
/*
C -----------------------------------------------------------------------------
CD CBF020 LEFT TOEBRAKES FORWARD MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro models the forward mass, friction and damping with a
CC  double integrator to generate the forward position of the control.
CC  Backdrive and the forward jam malfunction are also built into the model
CC  at this point.
C
*/
    CBLFFRI = CBLFEELSFR; 
/*
Constants:
*/
#define     _CHAN    CBL_CHAN          /* Channel number                   */
#define     CFORNUL  0.001             /* Cal mode force fade initial value */
 
/*
Parameters:
*/
#define     KFDMP    CBLKFDMP          /* Forward cable damping gain         */
#define     FDMP     CBLFDMP           /* Forward cable damping              */
#define     FFRI     CBLFFRI           /* Forward friction                   */
#define     KIMF     CBLKIMF           /* Inverse forward mass gain          */
#define     IMF      CBLIMF            /* Inverse forward mass               */
#define     FVLM     CBLFVLM           /* Forward velocity limit             */
#define     FNLM     CBLFNLM           /* Forward neg. pos'n limit           */
#define     FPLM     CBLFPLM           /* Forward pos. pos'n limit           */
#define     MVNVEL   CBLMVNVEL         /* Forward stop moving velocity       */
#define     ZMPOS    CBLZMPOS          /* Control mech compliance pos dir    */
#define     ZMNEG    CBLZMNEG          /* Control mech compliance neg dir    */
#define     CALDMP   CBLCALDMP         /* Calibration mode damping increment */
#define     CALIMF   CBLCALIMF         /* Calibration mode IMF               */
#define     CALKN    CBLCALKN          /* Calibration mode 2 notch stiffness */
#define     CALFOR   CBLCALFOR         /* Calibration mode 2 notch force     */
#define     CFORLAG  CBLCFORLAG        /* Cal For fade lag time constant (s) */
 
/*
Inputs:
*/
#define     HTSTF    CBLHTSTF          /* Test force input from host       */
#define     MTSTF    CBLMTSTF          /* Test force input from utility    */
#define     THPTFOR  CBLTHPTFOR        /* Through put force                */
#define     BUNF     CBLBUNF           /* Bungee force                     */
#define     MUBF     CBLMUBF           /* Mass unbalance force             */
 
/*
Internal Inputs:
*/
#define     GAFOR    CBLAFOR           /* Actual force including gearing   */
#define     AFOR     CBLAFOR           /* Actual force excluding gearing   */
#define     CFOR     CBLFEELSFO        /* Cable force                      */
#define     MFOR     0.0               /* Model force                      */
#define     MF       CBLMF             /* Mechanical friction              */
#define     XP       CBLXP             /* Actual position                  */
#define     BDRATE   CBLBDRATE         /* Backdrive velocity               */
#define     BDMODE   CBLBDMODE         /* Backdrive mode                   */
 
/*
Outputs:
*/
#define     DFOR     CBLDFOR           /* Driving force                    */
#define     DACC     CBLDACC           /* Forward acceleration             */
#define     DVEL     CBLDVEL           /* Forward velocity                 */
#define     DPOS     CBLDPOS           /* Demanded position                */
#define     FPOS     CBLFPOS           /* Fokker position                  */
#define     FFMF     CBLFFMF           /* Forward friction used (minus MF) */
 
/*
Integer Inputs:
*/
#define     CALMOD   CBLCALMOD         /* Calibration mode                 */
#define     FJAM     CBLFJAM           /* Jammed forward quadrant flag     */
 
#include "cf_fwd.mac"
  
/*
C -----------------------------------------------------------------------------
CD CBF030 RIGHT TOEBRAKES FORWARD MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro models the forward mass, friction and damping with a
CC  double integrator to generate the forward position of the control.
CC  Backdrive and the forward jam malfunction are also built into the model
CC  at this point.
C
*/
    CBRFFRI = CBRFEELSFR; 
 
/*
Constants:
*/
#define     _CHAN    CBR_CHAN          /* Channel number                   */
#define     CFORNUL  0.001             /* Cal mode force fade initial value */
 
/*
Parameters:
*/
#define     KFDMP    CBRKFDMP          /* Forward cable damping gain         */
#define     FDMP     CBRFDMP           /* Forward cable damping              */
#define     FFRI     CBRFFRI           /* Forward friction                   */
#define     KIMF     CBRKIMF           /* Inverse forward mass gain          */
#define     IMF      CBRIMF            /* Inverse forward mass               */
#define     FVLM     CBRFVLM           /* Forward velocity limit             */
#define     FNLM     CBRFNLM           /* Forward neg. pos'n limit           */
#define     FPLM     CBRFPLM           /* Forward pos. pos'n limit           */
#define     MVNVEL   CBRMVNVEL         /* Forward stop moving velocity       */
#define     ZMPOS    CBRZMPOS          /* Control mech compliance pos dir    */
#define     ZMNEG    CBRZMNEG          /* Control mech compliance neg dir    */
#define     CALDMP   CBRCALDMP         /* Calibration mode damping increment */
#define     CALIMF   CBRCALIMF         /* Calibration mode IMF               */
#define     CALKN    CBRCALKN          /* Calibration mode 2 notch stiffness */
#define     CALFOR   CBRCALFOR         /* Calibration mode 2 notch force     */
#define     CFORLAG  CBRCFORLAG        /* Cal For fade lag time constant (s) */
 
/*
Inputs:
*/
#define     HTSTF    CBRHTSTF          /* Test force input from host       */
#define     MTSTF    CBRMTSTF          /* Test force input from utility    */
#define     THPTFOR  CBRTHPTFOR        /* Through put force                */
#define     BUNF     CBRBUNF           /* Bungee force                     */
#define     MUBF     CBRMUBF           /* Mass unbalance force             */
 
/*
Internal Inputs:
*/
#define     GAFOR    CBRAFOR           /* Actual force including gearing   */
#define     AFOR     CBRAFOR           /* Actual force excluding gearing   */
#define     CFOR     CBRFEELSFO        /* Cable force                      */
#define     MFOR     0.0               /* Model force                      */
#define     MF       CBRMF             /* Mechanical friction              */
#define     XP       CBRXP             /* Actual position                  */
#define     BDRATE   CBRBDRATE         /* Backdrive velocity               */
#define     BDMODE   CBRBDMODE         /* Backdrive mode                   */
 
/*
Outputs:
*/
#define     DFOR     CBRDFOR           /* Driving force                    */
#define     DACC     CBRDACC           /* Forward acceleration             */
#define     DVEL     CBRDVEL           /* Forward velocity                 */
#define     DPOS     CBRDPOS           /* Demanded position                */
#define     FPOS     CBRFPOS           /* Fokker position                  */
#define     FFMF     CBRFFMF           /* Forward friction used (minus MF) */
 
/*
Integer Inputs:
*/
#define     CALMOD   CBRCALMOD         /* Calibration mode                 */
#define     FJAM     CBRFJAM           /* Jammed forward quadrant flag     */
 
#include "cf_fwd.mac"
  
/*
C -----------------------------------------------------------------------------
CD CBF040 LEFT TOEBRAKES FEELSPRING MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the net feelspring force, summing the feelspring
CC  utility force for the net aft position (trim included) with the notch
CC  force.
C
*/
 
/*
Inputs:
*/
#define     TRIMV      CBLTRIMV     /* Trim Velocity                    */
#define     KN         CBLKN        /* Notch stiffness                  */
#define     NNL        CBLNNL       /* Notch negative level             */
#define     NPL        CBLNPL       /* Notch positive level             */
 
/*
Internal Inputs:
*/
#define     QPOS       CBLFPOS      /* Aft quadrant position            */
#define     FEEL_FUNC  CBLFEEL_FUNC /* Feelspring function              */
#define     FEELNNL    CBLFEELNNL   /* Feelspring util -ve notch level  */
#define     FEELNPL    CBLFEELNPL   /* Feelspring util +ve notch level  */
#define     FEELXMN    CBLFEELXMN   /* Minimum position breakpoint value*/
#define     FEELXMX    CBLFEELXMX   /* Maximum position breakpoint value*/
#define     FEELBCN    CBLFEELBCN   /* Number of position breakpoints   */
 
/*
Outputs:
*/
#define     TRIMP      CBLTRIMP     /* Trim Position actually used      */
 
/*
Internal Outputs:
*/
#define     FEELSFO    CBLFEELSFO   /* Feelspring interpolated force    */
#define     FEELSFR    CBLFEELSFR   /* Feelspring interpolated friction */
 
#include "cf_fspr.mac"
/*#include "dsh8_fspr.mac"
CBLFEELSFO = CBLDPOS * 4;*/

  
/*
C -----------------------------------------------------------------------------
CD CBF050 RIGHT TOEBRAKES FEELSPRING MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the net feelspring force, summing the feelspring
CC  utility force for the net aft position (trim included) with the notch
CC  force.
C
*/
 
/*
Inputs:
*/
#define     TRIMV      CBRTRIMV     /* Trim Velocity                    */
#define     KN         CBRKN        /* Notch stiffness                  */
#define     NNL        CBRNNL       /* Notch negative level             */
#define     NPL        CBRNPL       /* Notch positive level             */
 
/*
Internal Inputs:
*/
#define     QPOS       CBRFPOS      /* Aft quadrant position            */
#define     FEEL_FUNC  CBRFEEL_FUNC /* Feelspring function              */
#define     FEELNNL    CBRFEELNNL   /* Feelspring util -ve notch level  */
#define     FEELNPL    CBRFEELNPL   /* Feelspring util +ve notch level  */
#define     FEELXMN    CBRFEELXMN   /* Minimum position breakpoint value*/
#define     FEELXMX    CBRFEELXMX   /* Maximum position breakpoint value*/
#define     FEELBCN    CBRFEELBCN   /* Number of position breakpoints   */
 
/*
Outputs:
*/
#define     TRIMP      CBRTRIMP     /* Trim Position actually used      */
 
/*
Internal Outputs:
*/
#define     FEELSFO    CBRFEELSFO   /* Feelspring interpolated force    */
#define     FEELSFR    CBRFEELSFR   /* Feelspring interpolated friction */
 
#include "cf_fspr.mac"
/*#include "dsh8_fspr.mac"
CBRFEELSFO = CBRDPOS * 4;*/
  
}  /* end of cbfast */

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00047 CBF010 LOCAL VARIABLES DEFINITIONS                                    
C$ 00060 CBF020 LEFT TOEBRAKES FORWARD MASS MODEL MACRO                        
C$ 00137 CBF030 RIGHT TOEBRAKES FORWARD MASS MODEL MACRO                       
C$ 00214 CBF040 LEFT TOEBRAKES FEELSPRING MACRO                                
C$ 00257 CBF050 RIGHT TOEBRAKES FEELSPRING MACRO                               
*/
