/* $ScmHeader: 9996648428ww1w8w9uu0999999918&1|@ $*/
/* $Id: matrix_res.c,v 1.0, 2003-05-23 15:57:25Z, <PERSON>$*/
/*
C'Title            Matrix Resolution Generic Model
C'Module_ID        matrix_res.c
C'Entry_point      matrix_resolution
C'Author           <PERSON>'Date             Feb 1998
C'System           Ancillaries (21)
C'Subsystem        Network Matrix Resolution
C'Documentation    Matrix Resolution User Guide
C'Compilation_directives


IMPORTANT NOTE:

  Do not modify this file, this is a standard file used by several systems.
  If you encounter any problem with this file, please contact the Ancillaries
  responsable for the Matrix Resolution.
  
     Pre-compile using >cpc [file], then compile using c4l [file].
     When the [file.obj] is created, stamp the file using stamper [file.c].
     Then enter in simex using >simex enter [file.ext.
     It must be CPC'd, compiled and put again in Simex
     after each CDB update. Do not compile this file with c4ld unless
     required.

C'Revision History
C
Rev 1.3 David <PERSON> 28/07/1999
        Corrected protection for division by 0 in the Forward-Reduction Phase.

Rev 1.2 <PERSON> 27/07/1999
        The resolution technique have been changed from Gauss LUD to a 
        Bandwidth resolution method using Gauss elimination.

C  matrix_res.c.1 20Jul1998 16:26 a130 MPoulin
C       < Corrected header so that it complies with CAE Header standards. 
C         Added the document name as well as the subsystem name in the 
C         SImex Lists >

Rev 1.1 Louis Robitaille 25/02/1998
        Initial Release of the Matrix Resolution Technique

*/
/*
C'Ident
*/
  static char rev[] =
     "$Source: matrix_res.c,v $";


void matrix_resolution( double **A , double *b , double **x , int size ,
                        int bw )
  {
   int i,j,p,N;
   double sum;


/* Forward-Reduction Phase (Gauss Elimination Algorithm, "Active Square" Approach) */

   for (i=0; i<size-1; i++)
     {
      if (*(*(A+(i*size)+i)) != 0)
        {
         if (b[i] != 0)
           {
            b[i]=b[i] / *(*(A+(i*size)+i));
           }
   
         if (i < size-bw)
           {
            N=i+bw-1;
           }
         else
           {
            N=size-1;
           }

         for (j=N; j>=i; j--) 
           {
            if (*(*(A+(j*size)+i)) != 0 )
              {
               *(*(A+(j*size)+i)) = *(*(A+(j*size)+i)) / *(*(A+(i*size)+i));
              }
           }

         for (j=i+1; j<=N; j++)
           {
            if (*(*(A+(j*size)+i)) != 0)
              {
               for (p=i+1; p<=N; p++)
                 {
                  if (*(*(A+(p*size)+i)) != 0)
                    {
                     *(*(A+(p*size)+j)) = *(*(A+(p*size)+j)) - *(*(A+(p*size)+i)) * *(*(A+(i*size)+j));
                    }
                 }
               if (b[i] != 0)
                 {
                  b[j] = b[j] - b[i] * *(*(A+(i*size)+j));
                 }
              }
           }
        }
     }


/* Back-Substitution Phase (Gauss Elimination Algorithm, "Active Square" Approach) */

   if (*(*(A+((size-1)*size)+size-1)) == 0)
     {
      *(*(x+size-1)) = 0;
     }
   else
     {
      *(*(x+size-1)) = b[size-1] / *(*(A+((size-1)*size)+size-1));
     }
   
   for (i=size-2; i>=0; i--)
     {
      if (*(*(A+(i*size)+i)) == 0)
        {
         *(*(x+i)) = 0;
        }
      else
        {
         sum = 0.0; 
         if (i < size-bw)
           {
            N=i+bw-1;
           }
         else
           {
            N=size-1;
           }

         for (j=N; j>=i+1; j--) 
           {
            if (*(*(A+(j*size)+i)) != 0 && (*(*(x+j)) != 0)) 
              {
               sum = sum + *(*(A+(j*size)+i)) * *(*(x+j)); 
              }
           }

         *(*(x+i)) = b[i] - sum; 
        }
     }
  }
   
   
