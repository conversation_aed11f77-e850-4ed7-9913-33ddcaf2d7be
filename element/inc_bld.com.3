#!  /bin/csh -f
#!  $Revision: INC_BLD - Build an Include Pre-Compiled source file V1.3 Feb-92$
#!
#! &
#! @$.
#! &$.XSL
#! &$?.XSL
#! @CDB_SPARE.
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!  Version 1.2: <PERSON><PERSON>  (3-Dec-91)
#!     - changed header so that only host's CDBs are passed to script.
#!
#!  Version 1.3: <PERSON><PERSON> (29-Feb-92)
#!     - removed "OX" test because of new handling of spare files.
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/fpcl_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/fpcm_$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set FSE_LINE="`sed -n 1p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set FSE_DATA="`echo '$FSE_LINE' | cut -c4-`"
set lcount=2
#
FSE_BUILD_LOOP:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
#  if ("`echo '$FSE_LINE' | cut -c1-3`" != "OX ") then
#    echo "%FSE-E-WRONGCODE, Unexpected support code in file list"
#    exit
#  endif
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  echo "$FSE_FILE" >>$FSE_LIST
  goto FSE_BUILD_LOOP
FSE_BUILD_FULL:
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias fpc
fpc </dev/null -noinclude $FSE_DATA
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
setenv  fse_source "$FSE_MAKE"
setenv  fse_target "$argv[4]"
setenv  fse_action "I"
fse_build
rm $FSE_MAKE
exit
