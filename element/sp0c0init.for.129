C
C'Revision_history
C
C  sp0c0init.for.24 10Apr1996 04:11 usd8 marc be
C       < added logic to insure logical names are decoded at first pass
C         and not during the run time. Else the task pages and the
C         controls trip>
C
C  sp0c0init.for.23  9Apr1996 23:48 usd8 marcb
C       < added a call to shmrelock routine (in trace.c) to make sure
C         share mem are pinned >
C
C  sp0c0init.for.22  5Nov1992 01:24 usd8 steve e
C       < changed YITAIL init >
C
C  sp0c0init.for.21 27Jul1992 11:08 usd8 m.ward
C       < moved dfcio after second sp0c0.exe routine >
C
C  sp0c0init.for.20  6Jul1992 11:38 usd8 M.WARD
C       < ADDED LABELS TO ADJUST DAP RESPONSE SPEED >
C
C  sp0c0init.for.19 24Jun1992 11:42 usd8 m.wr
C       < put first reposition as t/o (doors closed) >
C
C  sp0c0init.for.18 29Apr1992 23:27 usd8 m.ward
C       < removed old ancil spare labels >
C
C  sp0c0init.for.17  3Apr1992 16:13 usd8 W. Pin
C       < Added call to initialize io server.  Should be done here and not
C         in RAP! >
C
C  sp0c0init.for.16  3Apr1992 15:08 usd8 m.ward
C       < added code from s312 to kill server for rap >
C
C  sp0c0init.for.15 10Mar1992 08:52 usd8 m.ward
C       < first reposition is now ground power (snag 374) >
C
C
C  sp0c0init.for.14  3Mar1992 13:52 usd8 M.WARD
C       < L/G DOPS TO FALSE FO RANCIL >
C
C  sp0c0init.for.13 27Feb1992 20:25 usd8 W. Pin
C       < Changed ref rwy from Atlanta 09R to Charlotte 36L. >
C
C  sp0c0init.for.12 27Feb1992 19:18 usd8 baon
C       < Initialize TAGW=30000 & TACG=0.27 upon testpilot request >
C
C  sp0c0init.for.11 25Feb1992 11:44 usd8 PVE
C       < INITIALIZE VKI2 TO -.5 AS CDB INITIALIZATION DOES NOT WORK >
C
C  sp0c0init.for.10 24Feb1992 11:15 usd8 m.ward
C       < changed yerlofmt to 2 >
C
C  sp0c0init.for.9 21Feb1992 18:27 usd8 W. Pin
C       < Initialize DMC error logger except for ARINC warnings and CCU
C         error messages. >
C
C  sp0c0init.for.8 17Feb1992 18:53 usd8 PAULV
C       < INITIALIZE  TIRE PRESSURES TO REALISTIC VALUES FOR DASH8
C         (UNREALISTIC VALUES WERE GIVING MY NEW GROUND MODEL PROBLEMS) >
C
C  sp0c0init.for.7 20Jan1992 16:40 usd8 W. Pin
C       < Initialize AHFEXT2 to false. >
C
C  sp0c0init.for.6 15Jan1992 16:16 usd8 W. Pin
C       < Initialized some ANCIL DOPs to true. >
C
C  sp0c0init.for.5  9Jan1992 10:45 usd8 marcb
C       < decomment scalload for scaling. >
C
C  sp0c0init.for.4  8Jan1992 20:56 usd8 ku
C       < change call to dn1_init to dfc_init >
C
C  sp0c0init.for.3 23Dec1991 13:45 usd8 m.ward
C       < added logic for yitail to depend on scenario (and error logging) >
C
C File: /cae1/ship/sp0c0init.for.2
C       Modified by: BAON
C       Wed Nov 27 20:57:11 1991
C       < Init. TAGW,TAFUEL,TACG,TARVRMIN,TARVRMAX >
C
C File: /cae1/ship/sp0c0init.for.2
C       Modified by: R.HEIDT
C       Tue Nov 26 16:34:06 1991
C       < Initialize sound level to maximum >
C
C File: /cae1/ship/sp0c0init.for.4
C       Modified by: R.HEIDT
C       Wed Nov 20 15:45:35 1991
C       < Set YITAIL for DASH 8 / 100 to 226 >
C
C File: /cae1/ship/sp0c0init.for.7
C       Modified by: R.HEIDT
C       Mon Nov 18 15:00:07 1991
C       < Setting TAVMODE and XZWXR for dusk scene and CAVOK >
C
C File: /cae1/ship/sp0c0init.for.3
C       Modified by: R.HEIDT
C       Fri Nov 15 14:09:15 1991
C       < Set A/C min/max weights >
C
C File: /cae1/ship/sp0c0init.for.11
C       Modified by: JD
C       Thu Nov 14 17:17:35 1991
C       < now setting tcmrepos upon initial reposition >
C
C File: /cae1/ship/sp0c0init.for.3
C       Modified by: R.HEIDT
C       Thu Nov 14 16:10:04 1991
C       < Initialize TAICAO1,TARWY1 and TAPOSN >
C
C File: /cae/initial/sp0c0init.for.1
C       Modified by: MARC B
C       Thu May  2 11:41:16 1991
C       < Initial edit.>
C
C'Subroutine
C
      subroutine initcdb
C
      implicit none
C
C'Include
C
      include  'disp.inc' !NOFPC
      include  'disp.com' !NOFPC
      Integer   Loc
C
C'Ident
C
      CHARACTER*55
     -            rev /
     -  '$Source: sp0c0init.for.24 10Apr1996 04:11 usd8 marc be$'/
C
CP    USD8   YI(*),YT(*),YS(*),YXE(*),
CP           ABPTN(2), ABPT(4),  AG$HPWR1, AG$HPWR2,
CP           AHFEXT2 ,
CP           TAFL01MN, TAFL03MN, TACGMIN,  TAFUELMN, TAZFWMIN, TAGWMIN,
CP           TAFL01MX, TAFL03MX, TACGMAX,  TAFUELMX, TAZFWMAX, TAGWMAX,
CP           TAGW,     TAFUEL,   TACG,     TARVRMIN, TARVRMAX,
CP           TAICAO1,  TARWY1,   TAPOSN,   YERLOFMT, YERLOFMT1,
CP           TAVMODE,  TASOUND,  TCMREPOS,
CP           VKI2,
CP           XZWXR
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 19:57:15 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.235
C$@   /cae/simex_plus/element/usd8.skx.235
C$@   /cae/simex_plus/element/usd8.spx.235
C$@   /cae/simex_plus/element/usd8.sdx.235
C$@   /cae/simex_plus/element/usd8.xsl.227
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.227              
C$
      REAL*8   
     &  YTSIMTMD       ! SIMULATION TIME - DOUBLE PRECISION (SEC)
C$
      REAL*4   
     &  ABPT(4)        ! Tire pressure wheel 1 LO               [psi]
     &, ABPTN(2)       ! Tire pressure wheel nose left          [psi]
     &, TACG           ! CENTER OF GRAVITY                   [%MAC ]
     &, TACGMAX        ! MAX CENTER OF GRAVITY               [%MAC ]
     &, TACGMIN        ! MIN CENTER OF GRAVITY               [%MAC ]
     &, TAFL01MN       ! FUEL TANK # 01 (MIN)                [LBS  ]
     &, TAFL01MX       ! FUEL TANK # 01 (MAX)                [LBS  ]
     &, TAFL03MN       ! FUEL TANK # 03 (MIN)                [LBS  ]
     &, TAFL03MX       ! FUEL TANK # 03 (MAX)                [LBS  ]
     &, TAFUEL         ! TOTAL FUEL QTY                      [LBS  ]
     &, TAFUELMN       ! MIN TOTAL FUEL QTY                  [LBS  ]
     &, TAFUELMX       ! MAX TOTAL FUEL QTY                  [LBS  ]
     &, TAGW           ! GROSS WEIGHT                        [LBS  ]
     &, TAGWMAX        ! MAX GROSS WEIGHT                    [LBS  ]
     &, TAGWMIN        ! MIN GROSS WEIGHT                    [LBS  ]
     &, TASOUND        ! SOUND VOLUME (0-100%)
     &, TAZFWMAX       ! MAX ZERO FUEL WEIGHT                [LBS  ]
     &, TAZFWMIN       ! MIN ZERO FUEL WEIGHT                [LBS  ]
     &, VKI2           ! INTEGRATION CONSTANT 2
     &, YSCYCAVG(20)   ! CPUi cycle avegage spare time
     &, YSCYCMAX(20)   ! CPUi cycle maximum spare time
     &, YSCYCMIN(20)   ! CPUi cycle minimum spare time
     &, YSITRATE       ! Interval Timer Base Time/Tics (Sec)
     &, YSMONMAX       ! PFU maximum monitor exec. time
     &, YSMONMIN       ! PFU minimum monitor exec. time
     &, YSMONTIM       ! PFU average monitor exec. time
     &, YSSLOITM       ! Slow Down Iteration Frame Time (Sec)
     &, YSSPARER(30)   ! Spare Real for future Dispatcher Update
     &, YSTATU01       ! Gross weight
     &, YSTATU02       ! Height above ground
     &, YSTATU03       ! Airspeed
      REAL*4   
     &  YSTATU04       ! Rate of climb
     &, YSTATU05       ! Flap position
     &, YSTATU06       ! Gear position
     &, YSTATU09       ! Total engines thrust
     &, YSTATU10       ! Turbulence active
     &, YSTIMAVG(20)   ! CPUi avegage spare time
     &, YSTIMMAX(20)   ! CPUi maximum spare time encountered
     &, YSTIMMIN(20)   ! CPUi minimum spare time encountered
     &, YSTIMSIG(20)   ! CPUi spare time square distribution
     &, YSTIMSPA(20)   ! CPUi spare time of current frame
     &, YSTIMTOT(20)   ! CPUi average spare time of frame
     &, YTITRN         ! Basic Iteration Frame Time (Sec.)
     &, YTSIMTM        ! SIMULATOR TIME (SEC)
C$
      INTEGER*4
     &  TAPOSN         ! REPOSITION INDEX
     &, TARVRMAX       ! MAXIMUM RVR                         [Mtres]
     &, TARVRMIN       ! MINIMUM RVR                         [Mtres]
     &, YIFREQ         ! Simulator Iteration Frequency
     &, YISHIP         ! Ship name
     &, YITAIL         ! Ship tail number
     &, YSCONLEG(20)   ! CPUi Process Leg Counter
     &, YSCPUITM(20)   ! CPU Top Process Iteration Mask (OVR Logic)
     &, YSDSPFLG       ! Dispat. counter for scheduling by flag monito
     &, YSDUMSAV(1)    ! Spare words for future dispatcher upd. I4
     &, YSITRCNT(20)   ! CPUi Iteration Counts
     &, YSITROVR(20)   ! CPUi Consecutive Overrun Counters
     &, YSMAXOVR       ! Maximum Number of Overruns Tolerated
     &, YSMONCNT       ! PFU monitor counter
     &, YSOVRCNT(20)   ! CPUi cumulated number of overruns
     &, YSSLODOW       ! Slow Down Factor
     &, YSSLOW         ! Slow loading iteration rate factor
     &, YSTATU07       ! # of radio tuned
     &, YSTATU08       ! # of engines flame on
     &, YSTATU11       ! # of F/D or A/P channel on
     &, YSTATU12       ! # of INS or IRS
     &, YSTICCNT       ! Interval Timer Tic Count
     &, YSTICSLO       ! Interval Timer Slow Loading Tic Count
     &, YSTIMNUM(20)   ! CPUi number of spare time samples
C$
      INTEGER*2
     &  TAVMODE        ! L.L DAY/DAWN/DUSK/NIGHT
     &, XZWXR          ! WEATHER SELECTION
     &, YERLOFMT       ! Error logger output format
     &, YSABRCNT       ! Cumulated number of aborts
     &, YSINTFAC       ! Number of interrupt(s) per frame for INTHDLR.
     &, YSSPAREH(66)   ! Spare Half words for future Dispatcher Update
C$
      LOGICAL*1
     &  AG$HPWR1       ! Emerg handle L/U 1 pwr roof    27-007 DO0382
     &, AG$HPWR2       ! Emerg handle L/U 2 pwr floor   27-007 DO0383
     &, AHFEXT2        ! NO.2 hydraulic external power connected
     &, TCMREPOS       ! REPOSITION A/C
     &, YIABORT        ! Simulator Abort Flag
     &, YIFRECHNG      ! Program Frequency Change Flag
     &, YIFREZ         ! Simulator Total Freeze Flag
     &, YISUSPEND      ! SIMULATOR SUSPEND FLAG
     &, YSPUNLD        ! Simex's unload time out
     &, YSSLOMSK       ! Slow down mask flag
     &, YSSPAREB(33)   ! Spare Bytes for future Dispatcher Updates
     &, YSTATON        ! Ground flag
     &, YXENDXRF0      !      end of base 0
C$
      INTEGER*1
     &  TAICAO1(4)     ! DEPARTURE ICAO CODE
     &, TARWY1(4)      ! DEPARTURE RWY CODE
C$
      LOGICAL*1
     &  DUM0000001(4),DUM0000002(4),DUM0000003(3),DUM0000004(184)
     &, DUM0000005(128),DUM0000006(2171),DUM0000007(5299)
     &, DUM0000008(10783),DUM0000009(83282),DUM0000010(809)
     &, DUM0000011(18600),DUM0000012(190042),DUM0000013(1023)
     &, DUM0000014(4),DUM0000015(4),DUM0000016(48)
     &, DUM0000017(4),DUM0000018(36),DUM0000019(4)
     &, DUM0000020(104),DUM0000021(546),DUM0000022(116)
     &, DUM0000023(52),DUM0000024(4),DUM0000025(1040)
     &, DUM0000026(19733)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YIFREQ,YIFRECHNG,YIFREZ,YIABORT,YISUSPEND
     &, DUM0000002,YTSIMTMD,YTSIMTM,YTITRN,YISHIP,YITAIL,YSMAXOVR
     &, YSTICCNT,YSTICSLO,YSCPUITM,YSITRCNT,YSITROVR,YSCONLEG
     &, YSDUMSAV,YSTATON,DUM0000003,YSTATU01,YSTATU02,YSTATU03
     &, YSTATU04,YSTATU05,YSTATU06,YSTATU07,YSTATU08,YSTATU09
     &, YSTATU10,YSTATU11,YSTATU12,DUM0000004,YSOVRCNT,YSTIMSPA
     &, YSTIMTOT,YSTIMMAX,YSTIMMIN,YSTIMAVG,YSTIMSIG,DUM0000005
     &, YSDSPFLG,YSSPARER,YSSLOITM,YSITRATE,YSSLODOW,YSSLOW,YSTIMNUM
     &, YSCYCMAX,YSCYCMIN,YSCYCAVG,YSMONMAX,YSMONMIN,YSMONCNT
     &, YSMONTIM,YSSPAREH,YSABRCNT,YSINTFAC,YSSPAREB,YSPUNLD
     &, YSSLOMSK,DUM0000006,YERLOFMT,DUM0000007,AG$HPWR1,AG$HPWR2
     &, DUM0000008,VKI2,DUM0000009,AHFEXT2,DUM0000010,ABPTN,ABPT
     &, DUM0000011,XZWXR,DUM0000012,TCMREPOS,DUM0000013,TAGW
     &, TAGWMIN,TAGWMAX,TACG,TACGMIN,TACGMAX,DUM0000014,TAZFWMIN
     &, TAZFWMAX,DUM0000015,TAFUEL,TAFUELMN,TAFUELMX,DUM0000016
     &, TAFL01MN,DUM0000017,TAFL03MN,DUM0000018,TAFL01MX,DUM0000019
     &, TAFL03MX,DUM0000020,TAVMODE,DUM0000021,TARVRMIN,TARVRMAX
     &, DUM0000022,TAPOSN,DUM0000023,TAICAO1,DUM0000024,TARWY1
     &, DUM0000025,TASOUND,DUM0000026,YXENDXRF0 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.235
C$@   /cae/simex_plus/element/usd8.skx.235
C$@   /cae/simex_plus/element/usd8.spx.235
C$@   /cae/simex_plus/element/usd8.sdx.235
C$@   /cae/simex_plus/element/usd8.xsl.227
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF       ! End of CDB
     &, YXENDXRF2      ! End of Base 2
C$
      LOGICAL*1
     &  DUM0200001(65125)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,YXENDXRF2,YXENDXRF  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.235
C$@   /cae/simex_plus/element/usd8.skx.235
C$@   /cae/simex_plus/element/usd8.spx.235
C$@   /cae/simex_plus/element/usd8.sdx.235
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  YERLOFMT1      ! Error logger output format
C$
      LOGICAL*1
     &  YXENDXRF1      !      end of Base 1
C$
      LOGICAL*1
     &  DUM0100001(10),DUM0100002(16776)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,YERLOFMT1,DUM0100002,YXENDXRF1 
C------------------------------------------------------------------------------
C
C
C'Local_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
      INTEGER*4 init_status
      INTEGER*4	log_status
      INTEGER*4	str_len
      INTEGER*4 CURRENT_PROC /0/
      INTEGER*4 PRI /10/
      INTEGER*4 status
      INTEGER*4 setpri
      INTEGER*4
     &   I, J,
     &   CAE_TRNL                      , ! call to lognam translator
     &   CAE_LD_SCENARIO               , ! logical name to translate
     &   LEN                           , ! for lognam translation
     &   CAE_LOG_MESSAGE_                ! for message logger
C
C     ----------------------------------------------------------------------
C     -                            CHARACTER                               -
C     ----------------------------------------------------------------------
C
      CHARACTER*(25) string
      CHARACTER*64
     &   BUF                            ,! for lognam translation message
     &   TYPE/'ASCII'/                  ,! for message logger
     &   MODE/'B'/                       ! for message logger
C
C     ---------------------------------------------------------------------
C     -                     START OF EXECUTABLE                           -
C     _____________________________________________________________________
C
C
      yifrez = .FALSE.
      yiabort = .FALSE.
      dsp$frzflg = loc(yifrez)
      dsp$abort  = loc(yiabort)
      dsp$suspend = loc(yisuspend)
      ysitrcnt(TASKNUM+1) = 0
      dsp$itrcnt = loc(ysitrcnt(1))
      dsp$ovrcnt = loc(ysitrovr(tasknum+1))
      dsp$yitim  = loc(yitim)
      yifrechng = .FALSE.
      dsp$frechng = loc(yifrechng)
      dsp$montim = loc(ysmontim)
      dsp$monmin = loc(ysmonmin)
      dsp$monmax = loc(ysmonmax)
      dsp$moncnt = loc(ysmoncnt)
      ytsimtm = 0.0
      dsp$simtime = loc(ytsimtm)
      ytsimtmd = 0.0
      dsp$dsimtime = loc(ytsimtmd)
      dsp$ytitrn = loc(ytitrn)
      slow_len = 72
      slow_factor = 4
C
C     Dispatcher spare time vars.
C
      dsp$SpaMax = loc(YSCYCMAX(TaskNum+1))
      dsp$SpaMin = loc(YSCYCMIN(TaskNum+1))
      dsp$SpAvg = loc(YSCYCAVG(TaskNum+1))
      dsp$Reset_Max = loc(YSTIMMAX(TaskNum+1))
      dsp$Reset_Min = loc(YSTIMMIN(TaskNum+1))
      dsp$Reset_Avg = loc(YSTIMAVG(TaskNum+1))
      dsp$Reset_Cnt = loc(YSTIMNUM(TaskNum+1))
C
C --- Load scaling file in memory
C
       call scalload
C
C --- Initialize the error logger
C     except for ARINC warnings and CCU error messages.
C
       yerlofmt  = 3
       yerlofmt1 = 2
C
C initialize the cae_io server to save time once slow loading is done
C and log the results to the log file
C
C
      status = setpri( %val(CURRENT_PROC), %val(PRI) )
C
      call cae_init_io_server( init_status )
C
      PRI = 25
      status = setpri( %val(CURRENT_PROC), %val(PRI) )
C
      type(1:1) = 'a'
      mode(1:1) = 'b'
      str_len = 25
      if ( init_status .eq. 1 ) then
        string = "cae_init_io_server passed"
      else
        string = "cae_init_io_server failed in SP0"
      endif
      log_status = cae_log_message_( type, mode, string, str_len )
C
C --- Initialize DN1
C
       call dfc_init
C
C --- call first pass of trqio and flight function generation for logical
C     name decoding. Decoding a logical name after the 1st pass causes the
C     synchronous task to page fault, which trips the controls loading.
C
      call trqio
      do i = 1,4
         call flightfg(i)
      enddo
C
C --- Initialize YITAIL depending on scenario loaded
C
      I = CAE_TRNL('CAE_LD_SCENARIO', LEN, BUF, 0)
C
      IF ( I .EQ. 1) THEN
C
        IF ( BUF(1:LEN) .EQ. 'SCENARIO_1') THEN
          YITAIL = 226           ! Dash 8-100
        ELSEIF ( BUF(1:LEN) .EQ. 'SCENARIO_2') THEN
          YITAIL = 230           ! Dash 8-300
C        ELSE
C          J = CAE_LOG_MESSAGE_(TYPE, MODE, 'ATTENTION:
C     &             Logical name for YITAIL not correct', 50)
C          YITAIL = 0
        ENDIF
      ENDIF
C
C --- Initialization of IF min/max
C
      TAGWMIN  = 20000.
      TAGWMAX  = 34700.
      TAZFWMIN = 20000.
      TAZFWMAX = 31000.
      TAFUELMN =     0.
      TAFUELMX =  5680.
      TAFL01MN =     0.
      TAFL01MX =  2840.
      TAFL03MN =     0.
      TAFL03MX =  2840.
      TACGMIN  =     0.150
      TACGMAX  =     0.388
C
      TARVRMIN =  0.0
C'USD8      TARVRMAX = 5996.0         ! Feets
      TARVRMAX = 32480         ! Feets
      TAGW     = 30000
      TACG     = 0.27
      TAFUEL   = 5680
C
C --- Set Airport, Runway and Takeoff position
C
CWP+  W. PIN  (27-Feb-92)  Change ref rwy from Atlanta to Charlotte.
CWP
CWP   TAICAO1(1) = 75  ! K
CWP   TAICAO1(2) = 65  ! A
CWP   TAICAO1(3) = 84  ! T
CWP   TAICAO1(4) = 76  ! L
CWP   TARWY1(1) = 48   ! 0
CWP   TARWY1(2) = 57   ! 9
CWP   TARWY1(3) = 82   ! R
CWP   TARWY1(4) = 32   !
 
      TAICAO1(1) = 75  ! K
      TAICAO1(2) = 67  ! C
      TAICAO1(3) = 76  ! L
      TAICAO1(4) = 84  ! T
      TARWY1(1) = 51   ! 3
      TARWY1(2) = 54   ! 6
      TARWY1(3) = 76   ! L
      TARWY1(4) = 32   !
CWP-
      TAPOSN = 10     ! ground power reposition
      TCMREPOS = .TRUE.
C
C --- Set DUSK mode and CAVOK
C
      TAVMODE = 3
      XZWXR   = 1
C
C --- Initialize Sound level to maximum
C
      TASOUND = 1.0
C
C --- Initialize several CDB labels for ANCIL.
C
      AHFEXT2 = .false.
      AG$HPWR1 = .FALSE.
      AG$HPWR2 = .FALSE.
C
C --- Initialize several CDB labels for ANCIL.
C     and FLIGHT so that new ground model works correctly
C
      ABPTN(1) =  80.
      ABPTN(2) =  80.
      ABPT(1) =  130.
      ABPT(2) =  130.
      ABPT(3) =  130.
      ABPT(4) =  130.
C
C Reinitialize VKI2 (In CBD it is -.5 but it always comes up 0.5
C
      VKI2 = -0.5
C
      return
      end
C
      subroutine getcdb(cdbadr)
      implicit none
      integer   cdbadr(20),cdbsize(20)
C
CP    USD8 YXSTRT(*),YXEND(*)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 19:57:16 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.235
C$@   /cae/simex_plus/element/usd8.skx.235
C$@   /cae/simex_plus/element/usd8.spx.235
C$@   /cae/simex_plus/element/usd8.sdx.235
C$@   /cae/simex_plus/element/usd8.xsl.227
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.227              
C$
      LOGICAL*1
     &  YXENDXRF0      !      end of base 0
     &, YXSTRTXRF      ! Start of CDB
     &, YXSTRTXRF0     ! Start of Base 0
C$
      LOGICAL*1
     &  DUM0000001(335771)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,YXSTRTXRF0,DUM0000001,YXENDXRF0 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.235
C$@   /cae/simex_plus/element/usd8.skx.235
C$@   /cae/simex_plus/element/usd8.spx.235
C$@   /cae/simex_plus/element/usd8.sdx.235
C$@   /cae/simex_plus/element/usd8.xsl.227
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF1      !      end of Base 1
     &, YXSTRTXRF1     ! Start of Base 1
C$
      LOGICAL*1
     &  DUM0100001(16787)
C$
      COMMON   /XRFTEST1  /
     &  YXSTRTXRF1,DUM0100001,YXENDXRF1 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.235
C$@   /cae/simex_plus/element/usd8.skx.235
C$@   /cae/simex_plus/element/usd8.spx.235
C$@   /cae/simex_plus/element/usd8.sdx.235
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF       ! End of CDB
     &, YXENDXRF2      ! End of Base 2
     &, YXSTRTXRF2     ! Start of Base 2
C$
      LOGICAL*1
     &  DUM0200001(65124)
C$
      COMMON   /XRFTEST2  /
     &  YXSTRTXRF2,DUM0200001,YXENDXRF2,YXENDXRF  
C------------------------------------------------------------------------------
C
      Integer     Loc
 
      cdbadr(1) = loc(yxstrtxrf)
      cdbadr(2) = loc(yxstrtxrf1)
      cdbadr(3) = loc(yxstrtxrf2)
C      cdbadr(4) = loc(yxstrtxrf3)
C      cdbadr(5) = loc(yxstrtxrf4)
C      cdbadr(6) = loc(yxstrtxrf5)
C      cdbadr(7) = loc(yxstrtxrf6)
C
      return
      end
C
      subroutine syncexit
 
      INTEGER*4 kill_status
      INTEGER*4	str_len
      INTEGER*4	log_status
      CHARACTER*(3)  type, mode
      CHARACTER*(25) string
C
C -- Kill the IO server
C
      call cae_kill_io_server( kill_status )
      type(1:1) = 'a'
      mode(1:1) = 'b'
      str_len = 25
      if ( kill_status .eq. 1 ) then
        string = "cae_kill_io_server passed"
      else
        string = "cae_kill_io_server failed in SP0"
      endif
      log_status = cae_log_message_( type, mode, string, str_len )
 
      return
      end
