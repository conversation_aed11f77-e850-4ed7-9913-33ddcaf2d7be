#!  /bin/csh -f
#!
#!  $Revision: E30_BLD - Build a TMS320C30 executable file, Version 2.0 (KU) 1991-NOV-04$
#!
#! %
#! &
#! @
#! ^
#!
#!  Version 1.2: <PERSON> (1991-DEC-06)
#!     - changed support code for CDB files from OB to OX
#!     - changed support code for CDB log file to specify a text file
#!  Version 2.0: <PERSON> (1992-NOV-04)
#!     - Installed IBM-AIX native C30 tools
#
#
source `logicals -t cae_dfc_uproc`/std_log.com
#
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
unalias rm
unalias cat
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`"
mkdir $SIMEX_WORK/work$FSE_UNIK
cd $SIMEX_WORK/work$FSE_UNIK
#
set FSE_WORK=$SIMEX_WORK/e30w.opt
set FSE_LIBS=$SIMEX_WORK/e30l.opt
set FSE_DATA=$SIMEX_WORK/e30d.opt
set FSE_XRF=""
set FSE_SCRATCH=$SIMEX_WORK/e30s.tmp
#
if (-e "$FSE_WORK") rm $FSE_WORK
if (-e "$FSE_LIBS") rm $FSE_LIBS
if (-e "$FSE_DATA") rm $FSE_DATA
#
touch $FSE_WORK
touch $FSE_LIBS
touch $FSE_DATA
#
set FSE_LINE="`sed -n 1p $argv[3]`"
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
set FSE_PARENT="`norev $FSE_FILE`"
#
set FSE_LINE="`sed -n 2p $argv[3]`"
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
set tmp_name=`norev $FSE_FILE`
set FSE_ROOT="`echo $tmp_name:t | tr '[A-Z]' '[a-z]'`"
set FSE_ROOT=$FSE_ROOT:r
#
set FSE_EXE=$SIMEX_WORK/$FSE_ROOT.exe.1
set FSE_MAP=$SIMEX_WORK/$FSE_ROOT.map.1
set FSE_DNL=$SIMEX_WORK/$FSE_ROOT.dnl.1
set FSE_CDB=$SIMEX_WORK/$FSE_ROOT.cdb.1
set FSE_CDB1=$SIMEX_WORK/$FSE_ROOT.log.1
set FSE_CDB2=$SIMEX_WORK/$FSE_ROOT.xsl.1
set FSE_CDB3=$SIMEX_WORK/$FSE_ROOT.xfs.1
set FSE_CDB4=$SIMEX_WORK/$FSE_ROOT.xdl.1
set FSE_CDB5=$SIMEX_WORK/$FSE_ROOT.xds.1
set FSE_CDB6=$SIMEX_WORK/$FSE_ROOT.xdx.1
set FSE_CDB7=$SIMEX_WORK/$FSE_ROOT.xkx.1
set FSE_CDB8=$SIMEX_WORK/$FSE_ROOT.xpx.1
#
if (-e "$FSE_EXE")   rm $FSE_EXE
if (-e "$FSE_MAP")  rm $FSE_MAP
if (-e "$FSE_DNL")  rm $FSE_DNL
if (-e "$FSE_CDB")  rm $FSE_CDB
if (-e "$FSE_CDB1") rm $FSE_CDB1
if (-e "$FSE_CDB2") rm $FSE_CDB2
if (-e "$FSE_CDB3") rm $FSE_CDB3
if (-e "$FSE_CDB4") rm $FSE_CDB4
if (-e "$FSE_CDB5") rm $FSE_CDB5
if (-e "$FSE_CDB6") rm $FSE_CDB6
if (-e "$FSE_CDB7") rm $FSE_CDB7
if (-e "$FSE_CDB8") rm $FSE_CDB8
#
set EOFL=`sed -n '$=' "$argv[3]"`
set lcount=3
set FSE_SOFT=""
#
FSE_BUILD_LIST:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    goto FSE_BUILD
  endif
  @ lcount = $lcount + 1
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set tmp_name=`norev $FSE_FILE`
  set FSE_NAME=$tmp_name:t
  set FSE_NAME=$FSE_NAME:r
  set FSE_TYPE=$tmp_name:t
  set FSE_TYPE=$FSE_TYPE:e
  set FSE_LINK=$FSE_NAME.$FSE_TYPE
  ln -s $FSE_FILE $FSE_LINK
  set FSE_SOFT="$FSE_SOFT $FSE_LINK"

  set FSE_TEMP="`echo $FSE_FILE | grep 'data'`"
  if ("$FSE_TEMP" != "") then
    echo "$FSE_LINK" >>$FSE_DATA
  else
    set FSE_TEMP="`echo $FSE_FILE | grep 'fgen'`"
    if ("$FSE_TEMP" != "") then
      echo "$FSE_LINK" >>$FSE_DATA
    else
      set FSE_TEMP="`echo $FSE_FILE | grep 'xrf'`"
      if ("$FSE_TEMP" != "") then
        set FSE_XRF=$FSE_LINK
      else
        if ("$FSE_TYPE" == "cmd") then   
          set FSE_CMD=$FSE_LINK
        else if ("$FSE_TYPE" == "lib") then   
          echo "$FSE_LINK" >>$FSE_LIBS
        else 
          echo "$FSE_LINK" >>$FSE_WORK
        endif
      endif
    endif
  endif
goto FSE_BUILD_LIST
#
FSE_BUILD_FULL:
#
cat $FSE_LIBS $FSE_DATA >>$FSE_WORK
rm $FSE_LIBS
rm $FSE_DATA
#
if ("$FSE_CMD" == "") then
  echo "%FSE-E-NOCMD, Command file not found."
  goto FSE_BUILD
endif
#
#  Create executable
#
   FSE_BUILD_EXE:
if (-e "$FSE_MAP") rm $FSE_MAP
lnk30 -q -c $FSE_XRF $FSE_WORK $FSE_CMD -o $FSE_EXE -m $FSE_MAP >$FSE_SCRATCH
set stat=$status
rm $FSE_SOFT
rm $FSE_WORK
#
if (! -e "$FSE_EXE") then
  cat $FSE_SCRATCH
  goto FSE_BUILD
endif
rm $FSE_SCRATCH
#
#  Create download file
#
if (-e "$FSE_DNL") rm $FSE_DNL
c30dnlgen "$FSE_EXE" "$FSE_DNL" >$FSE_SCRATCH
if (! -e "$FSE_DNL") then
  cat $FSE_SCRATCH
  goto FSE_BUILD
endif
#
#  Create CDB files
#
c30cdbgen $FSE_EXE $FSE_CDB >$FSE_SCRATCH
if (! -e "$FSE_CDB") then
  cat $FSE_SCRATCH
  goto FSE_BUILD
endif
#
unalias cdbp
cdbp </dev/null -nodat $FSE_CDB >$FSE_SCRATCH
set stat=$status
if ($stat != 0) then
  cat $FSE_SCRATCH
  goto FSE_BUILD
endif
#
set FSE_EXE_INFO="`fmtime $FSE_EXE | cut -c1-17`"
if ("$FSE_EXE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_EXE)"
  goto FSE_BUILD
endif
set FSE_DNL_INFO="`fmtime $FSE_DNL | cut -c1-17`"
if ("$FSE_DNL_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_DNL)"
  goto FSE_BUILD
endif
set FSE_CDB_INFO="`fmtime $FSE_CDB | cut -c1-17`"
if ("$FSE_CDB_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_CDB)"
  goto FSE_BUILD
endif
set FSE_CDB1_INFO="`fmtime $FSE_CDB1 | cut -c1-17`"
if ("$FSE_CDB1_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_CDB1)"
  goto FSE_BUILD
endif

touch $argv[4]
echo "0MNBBU $FSE_PARENT,,,,,"                                    >>$argv[4]
echo "1MMBE3 $FSE_EXE,,,,,Produced by lnk30 on $FSE_EXE_INFO"     >>$argv[4]
echo "1CMTT3 $FSE_MAP,,,,,Produced by lnk30 on $FSE_EXE_INFO"     >>$argv[4]
echo "1CMBBU $FSE_DNL,,,,,Produced by c30dnlgen on $FSE_DNL_INFO" >>$argv[4]
echo "1CMTTX $FSE_CDB,,,,,Produced by c30cdbgen on $FSE_CDB_INFO" >>$argv[4]
echo "1CMTOX $FSE_CDB1,,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
echo "1CMBOX $FSE_CDB2,,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
echo "1CMBOX $FSE_CDB3,,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
echo "1CMBOX $FSE_CDB4,,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
echo "1CMBOX $FSE_CDB5,,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
echo "1CMBOX $FSE_CDB6,,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
echo "1CMBOX $FSE_CDB7,,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
echo "1CMBOX $FSE_CDB8,,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
#
FSE_BUILD:
  cd ..
  if (-e "work$FSE_UNIK") rm -rf work$FSE_UNIK
  exit
