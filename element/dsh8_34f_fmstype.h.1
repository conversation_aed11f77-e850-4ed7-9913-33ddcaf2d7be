/* C+Stamper_off */
/*
 $Id: ship_34f_fmstype.h,v 1.5, 2008-02-15 22:10:22Z, Dinh Cuong Tran$
 $Project: Dept 78$
 $Folder: csoft_avn_src_rel$
 $Log:
  6    CSOFT      1.5         15/02/2008 5:10:22 PM  <PERSON>h Cuong Tran Remove
       ENDIF at the end.
  5    CSOFT      1.4         30/01/2008 11:08:51 AM Dinh Cuong Tran Put back
       version 1.2 which fix error of compilation in version 1.3
  4    CSOFT      1.3         22/01/2008 1:56:46 PM  <PERSON>   Added some
        protection in case the include file is included many times indirectly.
         This will prevent the compiler from complaining about redefinition of
        macro or various statements.
  3    CSOFT      1.2         14/07/2005 4:08:56 PM  <PERSON>
       Removed all the included files.
  2    CSOFT      1.1         11/07/2005 11:30:38 AM Dinh Cuong Tran Fixed
       error of compilation
  1    CSOFT      1.0         07/07/2005 2:51:37 PM  Dinh Cuong Tran 
 $
 $NoKeywords$
 
 PROPRIETARY NOTICE: The information contained herein is confidential 
 and/or proprietary to CAE Inc., and shall not be reproduced or disclosed
 in whole or in part, or used for any purpose whatsoever unless authorized
 in writing by CAE Inc.                                                   
*/
/* C-Stamper_off */
/* $ScmHeader: 9996u4y5221u2wuvw61w999999958&2|@ $*/
/* $Id: ship_34f_fmstype.h,v 1.7 2000/10/03 20:50:13 avnsw(MASTER_VERSION|CAE_MR) Stab $*/


/* ----------------------------------------------------------------------------
The "CSOFT Reported FMS TYPE" corresponds to an INT value set according to the
FMS type by the FMS mapping module. They are associated to the CDB
variable "l34f_csoft_fms_type_i2".
---------------------------------------------------------------------------- */
enum {
  CSOFT_FMS_UNDEFINED                  = 0,
  CSOFT_FMS_NZ2000                     = 1,
  CSOFT_FMS_UNS1B                    = 101,
  CSOFT_FMS_UNS1C                    = 102,
  CSOFT_FMS_UNS1CSP                  = 103,
  CSOFT_FMS_UNS1D                    = 104,
  CSOFT_FMS_UNS1K                    = 105,
  CSOFT_FMS_UNS1E                    = 106,
  CSOFT_FMS_GNSXL                    = 201,
  CSOFT_FMS_GNSXLS                   = 202,
  CSOFT_FMS_EPIC                     = 301
};

