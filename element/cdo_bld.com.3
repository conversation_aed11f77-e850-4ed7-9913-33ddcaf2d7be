#!  /bin/csh -f
#!  $Revision: CDO_BLD - Build a CDB offset file V2.0 (MT) Jan-92$
#!
#! &
#! @$.
#! &$.XSL
#! &$?.XSL
#! @CDB_SPARE.
#! =
#! @
#! ^
#!  Version 1.0: <PERSON>, May-91
#!
#!  Version 2.0: <PERSON> (27-Jan-91)
#!     set the logical names "cdbo_output" & "cdbo_bases" in order
#!     to define addresses for specific bases.
#!
if ("$argv[1]" == "Y") then
  set echo
  set verbose
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`"
set FSE_SOURCE="source$FSE_UNIK.dat"
set FSE_SOURCE="`cvfs '$FSE_SOURCE'`"
set FSE_TARGET="target$FSE_UNIK.dat"
set FSE_TARGET="`cvfs '$FSE_TARGET'`"
#
cat $argv[3] | cut -c4- > $FSE_SOURCE
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  goto exit_handler
endif
#
set CDB_NAME = "`/cae/logicals -t cae_cdbname`"
if ( $status != 0 ) then
  echo "Cannot translate logical name CAE_CDBNAME"
endif
#
set empty_line_flag = "OFF"
set CDBO_BASES = ""
foreach FSE_LINE ("`cat -n $argv[3]`")
  set FSE_LINE = ( $FSE_LINE )
  if ( $#FSE_LINE == 1 ) then
     set empty_line_flag = "ON"
  else if ( "$FSE_LINE[1]" == "1" ) then
     shift FSE_LINE
     set FSE_DATA = "`echo $FSE_LINE | cut -c4-`"
     set FSE_DATA = "$FSE_DATA:t"
     set FSE_DATA = "$FSE_DATA:r"
     set CDBO_OUTPUT = "`echo $FSE_DATA:r`"
  else if ( "$empty_line_flag" == "ON" ) then
     shift FSE_LINE
     set FSE_DATA = "`echo $FSE_LINE | cut -c4-`"
     set FSE_DATA = "$FSE_DATA:t"
     set FSE_BASE = "`echo $FSE_DATA:r | sed s/$CDB_NAME// | sed s/0.dat//`"
     if ( "$FSE_BASE" == "" ) set FSE_BASE = "0"
     set CDBO_BASES = "${CDBO_BASES}${FSE_BASE}_"
  endif
end
#
if ( "$CDBO_OUTPUT" != "" ) setenv cdbo_output $CDBO_OUTPUT
if ( "$CDBO_BASES"  != "" ) setenv cdbo_bases  $CDBO_BASES
setenv SOURCE $FSE_SOURCE
setenv TARGET $FSE_TARGET
unalias cdbo
cdbo
#as cdb.s                       # @@@ SGI specific
#
set RESULT="`grep cdb $FSE_TARGET| cut -c13-`"
set stat=$status
set FSE_INFO="`fmtime $RESULT | cut -c1-17`"
if ($stat != 0) then
  echo "%FSE-E-INFOFAILED, File information not available"
else
  echo "0MRBOX $RESULT,,,CDO_BLD.COM,,Produced by cdbo on $FSE_INFO" >$argv[4]
endif
#
exit_handler:
rm $FSE_SOURCE
rm $FSE_TARGET
exit
