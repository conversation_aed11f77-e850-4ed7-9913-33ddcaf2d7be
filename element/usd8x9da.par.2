C -- X9DA.PAR : Parameters for the Digital Voice / Digital Audio System
C
      INTEGER*4 
     .    MAXPL         !  Maximum message length (number of phrases)
     .,   MAXREC        !  Maximum voice data record number 
     .,   MAXPHR        !  Maximum number of recordings  =phrs*spkrs
     .,   SZHDR         !  word size of header, approx=3+MAXREC+5*MAXPHR
     .,   SZREC         !  word size of each voice data record/buffer
     .,   SZBLK         !  word size of a block for the voice data file
     .,   SZPKT         !  word size of an ethernet packet
     .,   NRRBLK        !  number of file blocks reserved for ea rec
     .,   NRHBLK        !  number of file blocks reserved for hdr
     .,   NRCHNS        !  Number of record/playback channels
     .,   NRBUFS        !  Number of buffers for each channel
     .,   NRPKTS        !  Number of ethernet packets for data transfers
C
C --               header control requests/states
C
     .,   OPEN    
     .,   READ    
     .,   WRITE    
     .,   CLOSE    
C
C --               maintenance control requests/states
C
     .,   M_PDEL        !  Delete a recording
     .,   M_FREE        !  Count and list free records
     .,   M_PLEN        !  Count and list records used for a recording
     .,   M_PINV        !  Count and list active recordings (phrases)
     .,   M_COPY        !  Copy the records for a recording
     .,   M_TEDT        !  Edit trailing edge of a recording
     .,   M_LEDT        !  Edit leading edge of a recording
C
C --               error codes
C
     .,   E_EXIST       !  phrase (non-)existing for (playback) record
     .,   E_DFULL       !  disk full, record stopped
C
      PARAMETER
     .(   MAXPL  = 32        !  Maximum message length (number of phrases)
     .,   MAXREC = 7200      !  Maximum voice data record number (2 hrs)
     .,   MAXPHR = 8192      !  Maximum number of recordings = phrs*spkrs
     .,   SZHDR  = 48384     !  word size of header ~= 3+MAXREC+5*MAXPHR
     .,   SZREC  = 4096      !  word size of each voice data record/buffer
     .,   SZBLK  = 256       !  word size of a block for the voice data file
     .,   SZPKT  = 740       !  word size of an ethernet packet
     .,   NRRBLK = SZREC/SZBLK 
     .,   NRHBLK = SZHDR/SZBLK 
     .,   NRCHNS = 10        !  Number of record/playback channels
     .,   NRBUFS = 2         !  Number of buffers for each channel
     .,   NRPKTS = 2         !  Number of ethernet packets for data transfers
C
C --               header control requests/states
C
     .,   OPEN = 1
     .,   READ = 2
     .,   WRITE = 3
     .,   CLOSE = 4
C
C --               maintenance control requests/states
C
     .,   M_PDEL = 1
     .,   M_FREE = 2
     .,   M_PLEN = 3
     .,   M_PINV = 4
     .,   M_COPY = 5
     .,   M_TEDT = 6
     .,   M_LEDT = 7
C
C --               error codes
C
     .,   E_EXIST  = 1       !  phrase (non-)existing for (playback) record
     .,   E_DFULL  = 2       !  disk full, record stopped
     .)
C
C -- End of X9DA.PAR
