/*
;	+----------------------------------------------------+
;	|                                                    |
;	|           TMS320C30 FEELSPRING ROUTINES            |
;       |                      V2.01                         |
;	|                                                    |
;	+----------------------------------------------------+
;
;	These are the feelspring routines for the C30.
;         feel_init       - initialize feel pointer table and allocate memory
;         feel_mod        - modify feelspring data
;         feel_interp_for - interpolate feelspring force data
;         feel_interp_fri - interpolate feelspring friction data
;         feel_check      - check for a change in the feelspring data
;                           and modify if new data is available
;         feel_vari       - interpolate between multiple feelspring curves
;
*/
/*
C'Revision_History
C
C  fspring.c.3 27Sep1992 09:43 md11 mzahn  
C       < V2.01  initialized curve interpolation slope when only one curve 
C         is defined >
C
C  fspring.c.2 11Jun1992 13:48 ja34 JG     
C       < changed SMALL to NEARZERO because it is defined in cf_def.h >
C
C  zspring.c.1 11Jun1992 12:11 ja34 mzahn  
C       < deleted array size definitions and included cf_def.h >
C
*/
#include "cf_def.h"

extern int *malloc();

#define NEARZERO 0.0000001

struct FEEL_PTR{
   int   valid;            /* data valid flag                    */
   int   use;              /* data buffer to use                 */
   int   *err;             /* pointer to feelspring error index  */
   int   *aft;             /* pointer to aft feelspring flag     */
   int   *nx;              /* pointer to number of breakpoints   */
   int   *ncurve;          /* pointer to number of curves        */
   float *curve;           /* pointer to curve breakpoints       */
   float *pos;             /* pointer to position breakpoints    */
   float *force;           /* pointer to force data              */
   float *fric;            /* pointer to friction data           */
   float *kn;              /* pointer to notch stiffness         */
   float *npl;             /* pointer to positive notch level    */
   float *nnl;             /* pointer to negative notch level    */
   float *kc;              /* pointer to cable stiffness         */
   float *xmin;            /* pointer to minimum position bkpt   */
   float *xmax;            /* pointer to maximum position bkpt   */
   int   *data0;           /* pointer to data buffer 0           */
   int   *data1;           /* pointer to data buffer 1           */
   int   *datain;          /* pointer to interpolated curve data */
   };

struct FEEL_DATA{
   int   nx;               /* number of position breakpoints     */
   int   ic;               /* curve breakpoint index             */
   int   ncurve;           /* number of curves                   */
   float pos[MAX_CURVE][MAX_FEEL];
                           /* feelspring position breakpoints    */
   float slfor[MAX_CURVE][MAX_FEEL];
                           /* force slope values                 */
   float slfri[MAX_CURVE][MAX_FEEL];
                           /* friction slope values              */
   float force[MAX_CURVE][MAX_FEEL];
                           /* force intercept values             */
   float fric[MAX_CURVE][MAX_FEEL];
                           /* friction intercept values          */
   float curve[MAX_CURVE];
                           /* feelspring curve breakpoints       */
   float vardif[MAX_CURVE];
                           /* 1/(curve breakpoint difference)    */
   };

struct FEEL_INT{
   float oldvari;          /* previous value of interp variable  */
   int   nx;               /* number of position breakpoints     */
   int   ix;               /* interpolated curve position index  */
   float pos[MAX_FEEL];    /* interpolated curve position bkpts  */
   float slfor[MAX_FEEL];  /* interpolated curve force slopes    */
   float slfri[MAX_FEEL];  /* interpolated curve friction slopes */
   float force[MAX_FEEL];  /* interpolated curve force intercepts*/
   float fric[MAX_FEEL];   /* interpolated curve friction intcpts*/
   };
   
struct FEEL_RESULT{
   float force;            /* Feelspring force result            */
   float fric;             /* Feelspring friction result         */
   };

struct FEEL_INT feel_inter[MAX_SERVO];

struct FEEL_PTR feel_table[MAX_SERVO];
int feel_func = 0;


/*
;     ------------------------------------------------------------------
;	        ROUTINE TO INITIALIZE FEELSPRING DATA
;     ------------------------------------------------------------------
*/
int feel_init(feel_err,feel_aft,feel_nx,feel_ncurve,feel_curve,feel_pos,
              feel_force,feel_fric,feel_kn,feel_nnl,feel_npl,feel_kc,
              feel_xmin,feel_xmax)
int   *feel_err;
int   *feel_aft;
int   *feel_nx;
int   *feel_ncurve;
float *feel_curve;
float *feel_pos;
float *feel_force;
float *feel_fric;
float *feel_kn;
float *feel_npl;
float *feel_nnl;
float *feel_kc;
float *feel_xmin;
float *feel_xmax;
{
      int      *ptr0;             /* Buffer 0 pointer                        */
      int      *ptr1;             /* Buffer 1 pointer                        */
      int      *inter;            /* Interpolated data pointer               */
/*
;     ---------------------------------------------
;     Check for available space in feelspring table
;     ---------------------------------------------
*/
      if(feel_func >= MAX_SERVO) return -1;
/*
;     --------------------------------
;     Allocate memory for data buffers
;     --------------------------------
*/
      inter = malloc(sizeof(struct FEEL_INT));
      if (inter == 0) return -1;
      ptr0 = malloc(sizeof(struct FEEL_DATA));
      if (ptr0 == 0) return -1;
      ptr1 = malloc(sizeof(struct FEEL_DATA));
      if (ptr1 == 0) return -1;
/*
;     ---------------------------
;     Initialize feelspring table
;     ---------------------------
*/
      feel_table[feel_func].valid  = FALSE;
      feel_table[feel_func].use    = 0;
      feel_table[feel_func].err    = feel_err;
      feel_table[feel_func].aft    = feel_aft;
      feel_table[feel_func].nx     = feel_nx;
      feel_table[feel_func].ncurve = feel_ncurve;
      feel_table[feel_func].curve  = feel_curve;
      feel_table[feel_func].pos    = feel_pos;
      feel_table[feel_func].force  = feel_force;
      feel_table[feel_func].fric   = feel_fric;
      feel_table[feel_func].kn     = feel_kn;
      feel_table[feel_func].npl    = feel_npl;
      feel_table[feel_func].nnl    = feel_nnl;
      feel_table[feel_func].kc     = feel_kc;
      feel_table[feel_func].xmin   = feel_xmin;
      feel_table[feel_func].xmax   = feel_xmax;
      feel_table[feel_func].data0  = ptr0;
      feel_table[feel_func].data1  = ptr1;
      feel_table[feel_func].datain = inter;
/*
;     ------
;     Return
;     ------
*/
      return (feel_func++);
}

/*
;     ------------------------------------------------------------------
;	        ROUTINE TO MODIFY FEELSPRING DATA
;     ------------------------------------------------------------------
*/
int feel_mod(func,vari)
int   func;
float vari;
{
      float    *pos_ptr;          /* Position pointer                        */
      float    *force_ptr;        /* Force pointer                           */
      float    *fric_ptr;         /* Friction pointer                        */
      float    *temp_ptr;         /* Temporary pointer                       */
      float    iposdiff;          /* Inverse of position difference          */
      float    ikc;               /* Inverse cable stiffness                 */
      float    ntch_pos;          /* Notch positive breakout position        */
      float    ntch_neg;          /* Notch negative breakout position        */
      register int    nx;         /* Number of position breakpoints          */
      register int    ncurve;     /* Number of curves                        */
      register int    ix;         /* Index                                   */
      register int    jx;         /* Index                                   */
      register int    ipt;        /* Index                                   */
      struct FEEL_DATA *ptr;      /* Pointer to feelspring data and slopes   */
      struct FEEL_INT *inter;     /* Pointer to interpolated data            */
/*
;     ------------------------
;     Check for valid function
;     ------------------------
*/
      if((func >= MAX_SERVO)||(func < 0)) return -1;
      nx = *feel_table[func].nx;
      if((nx > MAX_FEEL)||(nx < MIN_FEEL)) return -1;
      ncurve = *feel_table[func].ncurve;
      if((ncurve > MAX_CURVE)||(ncurve < 1)) return -1;
/*
;     ----------------------------------
;     Compute pointers to generated data
;     ----------------------------------
*/
      if(feel_table[func].use == 0)
        ptr = (struct FEEL_DATA *) feel_table[func].data1;
      else
        ptr = (struct FEEL_DATA *) feel_table[func].data0;

      inter = (struct FEEL_INT *) feel_table[func].datain;
      inter->ix = 0;
      ptr->nx = nx;
      inter->nx = nx;
      ptr->ic = 0;
      ptr->ncurve = ncurve;
/*
;     -----------------------
;     Initialize coefficients
;     -----------------------
*/
/*
;          -------------------------
;          Convert to aft feelspring
;          -------------------------
*/
      pos_ptr = (float *)feel_table[func].pos;
      force_ptr = (float *)feel_table[func].force;
      if (abs(*feel_table[func].kn) > NEARZERO)
      {
         ntch_pos = *feel_table[func].npl/(*feel_table[func].kn);
         ntch_neg = *feel_table[func].nnl/(*feel_table[func].kn);
      }
      else
      {
         ntch_pos = 0.;
         ntch_neg = 0.;
      }
      if (!*feel_table[func].aft)
      {
         fric_ptr = (float *)feel_table[func].fric;
         if (*feel_table[func].kc > NEARZERO)
         {
            ikc = 1. / (*feel_table[func].kc);
         }
         else
         {
            return 4;             /* ERROR: zero or negative cable stiffness */
         }

         for(jx = 0;jx < ncurve;jx++)
         {
            for(ix = 0;ix <= nx-1;ix++)
            {
               ipt = jx * MAX_FEEL + ix;
               ptr->pos[jx][ix] = *(pos_ptr+ix) - *(force_ptr+ipt) * ikc;

               if((*(pos_ptr+ix) < -NEARZERO) && (ptr->pos[jx][ix] > ntch_neg))
               {
                  return 2;          /* ERROR: non-zero bkpt within notch */
               }
               else
               {
                  if((*(pos_ptr+ix) > NEARZERO) && (ptr->pos[jx][ix] < ntch_pos))
                  {
                     return 2;       /* ERROR: non-zero bkpt within notch */
                  }
               }
            }
         }
      }
      else
      {
         for (jx=0;jx < ncurve;jx++)
         {
            for (ix=0;ix < nx;ix++)
            {
               if ((*(pos_ptr+ix) < -NEARZERO) && (*(pos_ptr+ix) > ntch_neg))
               {
                  return 2;         /* ERROR: non-zero bkpt within notch */
               }
               else
               {
                  if ((*(pos_ptr+ix) > NEARZERO) && (*(pos_ptr+ix) < ntch_pos))
                  {
                      return 2;     /* ERROR: non-zero bkpt within notch */
                  }
                  else
                  {
                      ptr->pos[jx][ix] = *(pos_ptr+ix);
                  }
               }
            }
         }
      }
/*
;          ------------------------------------------
;          Subtract notch level from feelspring force
;          ------------------------------------------
*/
      for(jx=0;jx < ncurve;jx++)
      {
         for(ix=0;ix <= nx-1;ix++)
         {
            ipt = jx * MAX_FEEL + ix;
            if(ptr->pos[jx][ix] < -NEARZERO)
            {
               ptr->force[jx][ix] = *(force_ptr+ipt) - *feel_table[func].nnl;
            }
            else
            {
               if(ptr->pos[jx][ix] > NEARZERO)
               {
                  ptr->force[jx][ix] = *(force_ptr+ipt) - *feel_table[func].npl;
               }
               else
	       {
                  ptr->force[jx][ix] = *(force_ptr+ipt);
	       }
            }
         }
      }

/*
;          ------------------
;          Copy friction data
;          ------------------
*/
      memcpy(ptr->fric,feel_table[func].fric,MAX_FEEL*ncurve*sizeof(float));

/*
;          ----------------------
;          Copy curve breakpoints
;          ----------------------
*/
      memcpy(ptr->curve,feel_table[func].curve,ncurve*sizeof(float));

/*
;          -----------------------------
;          Generate interpolation slopes
;          -----------------------------
*/
      for(jx = 0;jx < ncurve;jx++)
      {
         for(ix = 0;ix < nx-1;ix++)
         {
            if(abs(ptr->pos[jx][ix+1]-ptr->pos[jx][ix]) > NEARZERO)
            {
               iposdiff = 1. / ( ptr->pos[jx][ix+1] - ptr->pos[jx][ix]);
      
               ptr->slfor[jx][ix] = ( ptr->force[jx][ix+1] - ptr->force[jx][ix])
                                    * iposdiff;

               ptr->force[jx][ix] = ptr->force[jx][ix] - ptr->slfor[jx][ix]
                                                         * ptr->pos[jx][ix];
   
               ptr->slfri[jx][ix] = ( ptr->fric[jx][ix+1] - ptr->fric[jx][ix])
                                    * iposdiff;

               ptr->fric[jx][ix]  = ptr->fric[jx][ix] - ptr->slfri[jx][ix]
                                                        * ptr->pos[jx][ix];
            }
            else
            {
               return 1;        /* ERROR: two identical position breakpoints */
            }
         }
      }
      if(ncurve > 1)
      {
         for(jx = 0;jx < ncurve-1;jx++)
         {
            if(abs(ptr->curve[jx+1]-ptr->curve[jx]) > NEARZERO)
            {
               ptr->vardif[jx] = 1./( ptr->curve[jx+1] - ptr->curve[jx]);
            }
            else
            {
               return 3;        /* ERROR: two identical curve breakpoints */
            }
         }
      }
      else
         ptr->vardif[0] = 0.;

/*
;     ---------------------------
;     Initialize feelspring table
;     ---------------------------
*/
      feel_table[func].use   = ! (feel_table[func].use);
      feel_table[func].valid = TRUE;
      inter->oldvari = inter->oldvari + 1.;
      feel_vari(func,vari);
/*
;     ----
;     Exit
;     ----
*/
      return 0;
}

/*
;     ----------------------------------------------------------------
;     	          ROUTINE TO INTERPOLATE FEELSPRING FORCE
;     ----------------------------------------------------------------
*/
float feel_interp_for(func,pos)
int   func;		          /* Function number                         */
float pos;		          /* Position                                */
{
      register int ix;            /* Position breakpoint index               */
      register int nx;            /* Number of position breakpoints          */
      float force;                /* Feelspring output force                 */
      struct FEEL_INT *inter;     /* Pointer to interpolated data            */
/*
;     ------------------------
;     Check for valid function
;     ------------------------
*/
      if((func >= MAX_SERVO)||(func < 0)) return;
      if(feel_table[func].valid != TRUE) return;
/*
;     --------------------
;     Get pointers to data
;     --------------------
*/
      inter = (struct FEEL_INT *) feel_table[func].datain;
      ix    = inter->ix;
      nx    = inter->nx;
/*
;     ------------------------------
;     Find position breakpoint index
;     ------------------------------
*/
      if(pos <= inter->pos[0])
        ix = 0;
      else
      {
        if(pos >= inter->pos[nx-1])
          ix = nx-2;
        else
        {
          while (inter->pos[++ix] < pos);
          while (inter->pos[--ix] > pos);
        }
      }
      inter->ix = ix;
/*
;     ---------------------------
;     Interpolate for force value
;     ---------------------------
*/
      force = pos * inter->slfor[ix] + inter->force[ix];
/*
;     ----
;     Exit
;     ----
*/
      return(force);
}

/*
;     ----------------------------------------------------------------
;     	         ROUTINE TO INTERPOLATE FEELSPRING FRICTION
;     ----------------------------------------------------------------
*/
float feel_interp_fri(func,pos)
int   func;		          /* Function number                         */
float pos;		          /* Position                                */
{
      register int ix;            /* Position breakpoint index               */
      float friction;             /* Feelspring output friction              */
      struct FEEL_INT *inter;     /* Pointer to interpolated data            */
/*
;     ------------------------
;     Check for valid function
;     ------------------------
*/
      if((func >= MAX_SERVO)||(func < 0)) return;
      if(feel_table[func].valid != TRUE) return;
/*
;     -------------------
;     Get pointer to data
;     -------------------
*/
      inter = (struct FEEL_INT *) feel_table[func].datain;
      ix    = inter->ix;
/*
;     ------------------------------
;     Interpolate for friction value
;     ------------------------------
*/
      friction = pos * inter->slfri[ix] + inter->fric[ix];
      return(friction);
}

/*
;     ----------------------------------------------------------------
;     	           ROUTINE CHECK FOR NEW FEELSPRING DATA
;     ----------------------------------------------------------------
*/

int feel_check( feel_change, feel_func, vari )
int   *feel_change;               /* pointer to the feelspring change flag   */
int   feel_func;                  /* feelspring function number              */
float vari;                       /* feelspring curve interpolation variable */
{
      int   c_feelstat;           /* feelspring utility return status        */

      if (*feel_change)
      {
          *feel_table[feel_func].err = feel_mod( feel_func ,vari);
          *feel_change = FALSE;
      }
      else
      {
          feel_vari( feel_func, vari );
      }
      
      return;

}  /**  end of feel_check  **/

/*
;     ----------------------------------------------------------------
;             ROUTINE TO INTERPOLATE BETWEEN FEELSPRING CURVES
;     ----------------------------------------------------------------
*/
int feel_vari(func,vari)
int   func;                       /* Function number                         */
float vari;                       /* Curve interpolation variable            */
{
      int    nx;                  /* Number of position breakpoints          */
      int    ncurve;              /* Number of curves                        */
      int    ix;                  /* Position breakpoint index               */
      int    ic;                  /* Curve breakpoint index                  */
      float  temp;                /* Scratch variable                        */
      struct FEEL_DATA *ptr;      /* Pointer to feelspring data and slopes   */
      struct FEEL_INT *inter;     /* Pointer to interpolated data            */
/*
;     ------------------------
;     Check for valid function
;     ------------------------
*/
      if((func >= MAX_SERVO)||(func < 0)) return;
      if(feel_table[func].valid != TRUE) return;
/*
;     --------------------
;     Get pointers to data
;     --------------------
*/
      if(feel_table[func].use == 0)
        ptr = (struct FEEL_DATA *) feel_table[func].data0;
      else
        ptr = (struct FEEL_DATA *) feel_table[func].data1;

      inter  = (struct FEEL_INT *) feel_table[func].datain;
      nx     = ptr->nx;
      ic     = ptr->ic;
      ncurve = ptr->ncurve;
/*
;     ------------------------------------------
;     Check for change in interpolation variable
;     ------------------------------------------
*/
      if (abs(vari - inter->oldvari) > NEARZERO)
      {
/*
;     ---------------------------
;     Find curve breakpoint index
;     ---------------------------
*/
        if(ncurve > 1)
        {
          if(vari <= ptr->curve[0])
            ic = 0;
          else
          {
            if(vari >= ptr->curve[ncurve-1])
              ic = ncurve-2;
            else
            {
              while (ptr->curve[++ic] < vari);
              while (ptr->curve[--ic] > vari);
            }
          }
          ptr->ic = ic;
        }
        else
          ic = 0;
/*
;     -------------------------------------
;     Interpolate between feelspring curves
;     -------------------------------------
*/
        for (ix = 0; ix < nx; ix++)
        {
           temp             = (vari - ptr->curve[ic]) * ptr->vardif[ic];
           inter->pos[ix]   = (1.-temp) * ptr->pos[ic][ix]
                                 + temp * ptr->pos[ic+1][ix];
           inter->force[ix] = (1.-temp) * ptr->force[ic][ix]
                                 + temp * ptr->force[ic+1][ix];
           inter->slfor[ix] = (1.-temp) * ptr->slfor[ic][ix]
                                 + temp * ptr->slfor[ic+1][ix];
           inter->fric[ix]  = (1.-temp) * ptr->fric[ic][ix] 
                                 + temp * ptr->fric[ic+1][ix];
           inter->slfri[ix] = (1.-temp) * ptr->slfri[ic][ix]
                                 + temp * ptr->slfri[ic+1][ix];
        }
      }
      inter->oldvari = vari;
      *feel_table[func].xmin = inter->pos[0];
      *feel_table[func].xmax = inter->pos[nx-1];
}
