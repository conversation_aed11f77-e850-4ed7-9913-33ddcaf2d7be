C'Module_ID             USD8DN
C'SDD_#                 ?
C'Customer              USAIR
C
C'Application           Simulation of the DASH8 Air Conditioning
C                       System Dynamics
C'Author                <PERSON><PERSON> <PERSON><PERSON>                       (<PERSON><PERSON>)
C'Date                  July 1991
C
C'System                ECS (Environmental Control Systems)
C'Iteration_Rate        266 msec
C'Process               Synchronous
C
C ----------------
C'Revision_History
C
C  usd8dn.for.4  9Dec1992 21:03 usd8 M.WARD
C       < FIXES FROM MICHELLE G200 G220 G280 SEE CMW >
C
C  usd8dn.for.3  7Nov1992 01:24 usd8 mm
C       < added recirc fan flow to pressurized area as shown in maint man >
C
C  usd8dn.for.2  6Nov1992 05:22 usd8
C       <   >
C
C  usd8dn.for.1  6Nov1992 05:12 usd8 mm
C       < modified cst dnce20 from 0.0145 to 0.005 to have flow with eng
C         at start & feather and recirc fans on >
C
C ----------------
C
C'
C
C ----------------------
C'Compilation_Directives
C ----------------------
C
C
C'
C ------------------------
C'Include_Files_Directives
C ------------------------
C
C
C'
C -----------
C'Description
C -----------
C
C
C
C ----------
C'References
C ----------
C
C     The list of references is given in the PPS and
C     PDS documents relative to this project.
C
      SUBROUTINE USD8DN
C
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C'
C -------------
C'Include_Files
C -------------
C
      INCLUDE 'disp.com'  !NOFPC
C
C
C'
C ------------------
C'Subroutines_Called
C ------------------
C
C
C --------------------------
C'Common_Data_Base_Variables
C --------------------------
C
C
C     ---------------------------------------------------------
C
C                 C O M M O N    D A T A    B A S E
C
C     ---------------------------------------------------------
C
C
C
C     ---------
C      QMR bus
C     ---------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ         XRFTEST5,XRFTEST6
C
C
CE    REAL*4     DNPDC   , !E- ZONE DIFF PRESS                [psi]  DTPDI(2)
CE    REAL*4     DNP0    , !E- TOTAL AIR PRESS               [psia]  EP0
CE    REAL*4     DNTA    , !E- AMBIENT TEMPERATURE AT A/C   [deg-c]  VTEMP
CE    REAL*4     DNTR    , !E- RAM AIR TEMPERATURE          [deg C]  ETT1
CE    REAL*4     DNVM    , !E- MACH NUMBER                           VM
CE    REAL*4     DNVT    , !E- AIRSPEED                  [feet/min]  VVT
C
CE    LOGICAL*1  DNSG    , !E- GROUND SUPPLY
C
C
CE      EQUIVALENCE  ( DNPDC   ,  DTPDI(2) ),
CE      EQUIVALENCE  ( DNP0    ,  EP0      ),
CE      EQUIVALENCE  ( DNTA    ,  VTEMP    ),
CE      EQUIVALENCE  ( DNTR    ,  ETT1     ),
CE      EQUIVALENCE  ( DNVM    ,  VM       ),
CE      EQUIVALENCE  ( DNVT    ,  VVT      )
C
C
C
CP USD8
C
CPO  & DXFDN   ,    !E- INSTRUMENT SYNCH FLAG
CPO  & DNHAI   ,    !E- PACK DUCT TEMP FF                    [deg_C]
CPO  & DNHCI   ,    !E- F DECK / CABIN TEMP FF               [deg_C]
CPO  & DNHDI   ,    !E- ZONE DUCT TEMP FF                    [deg_C]
CPO  & DNHFI   ,    !E- EQ COOLING TEMP FF                   [deg_C]
CPO  & DNHRI   ,    !E- L/R DUCT RISER TEMP FF               [deg_C]
CPO  & DNHS    ,    !E- SKIN TEMP FF                         [deg_C]
CPO  & DNPR    ,    !E- RAM AIR GAGE PRESS                   [psi]
CPO  & DNTAAI  ,    !E- PACK DUCT ADV TEMP                   [deg_C]
CPO  & DNTAI   ,    !E- PACK DUCT TEMP                       [deg_C]
CPO  & DNTDAI  ,    !E- ZONE DUCT ADV TEMP                   [deg_C]
CPO  & DNTCAI  ,    !E- ZONE ADV TEMP                        [deg_C]
CPO  & DNTCI   ,    !E- ZONE TEMP                            [deg_C]
CPO  & DNTDI   ,    !E- ZONE DUCT TEMP                       [deg_C]
CPO  & DNTFI   ,    !E- EQ COOLING TEMP                      [deg_C]
CPO  & DNTG    ,    !E- AIR COND GND SUPPLY TEMP             [deg_C]
CPO  & DNTRAI  ,    !E- DUCT RISER ADV TEMP                  [deg_C]
CPO  & DNTRI   ,    !E- L/R DUCT RISER TEMP                  [deg_C]
CPO  & DNTS    ,    !E- SKIN TEMPERATURE                     [deg_C]
CPO  & DNWC    ,    !E- PRESS AREA TOTAL INFLOW              [lb/min]
CPO  & DNWDI   ,    !E- CABIN/DECK DUCT AIRFLOW              [lb/min]
CPO  & DNWE    ,    !E- AVIONICS FAN FLOW                    [lb/min]
CPO  & DNWFD   ,    !E- FC FAN FLOW                          [lb/min]
CPO  & DNWFI   ,    !E- F DECK / CABIN RECIRC FAN FLOW       [lb/min]
CPO  & DNWG    ,    !E- AIR COND GND SUPLY FF                [lb/min]
CPO  & DNWR    ,    !E- AIR COND RAM AIRFLOW                 [lb/min]
C
CPI  & BIAN03  ,    !E- AVIONICS FAN CB
CPI  & DNF     ,    !E- COND1 FREEZE FLAG
CPI  & DZFA    ,    !E- DASH8 100-300 / 100A-300A OPTION  (.T. => A)
CPI  & DZF300  ,    !E- DASH8 100/300 OPTION  (.T. => 300)
C
CPI  & DCTD    ,    !E- PNEU DUCT TEMP                        [deg_C]
CPI  & DKTKAI  ,    !E- PACK DISCH ADV TEMP                   [deg C]
CPI  & DKTKI   ,    !E- PACK DISCH TEMP                       [deg C]
CPI  & DKWKI   ,    !E- PACK DISCHARGE FLOW                  [lb/min]
CPI  & DKWTTI  ,    !E- TRIM AIR VALVE FLOW                  [lb/min]
CPI  & DOFE    ,    !E- AVIONICS FAN CMD FLAG
CPI  & DOFF    ,    !E- FLIGHT COMPT CMD FLAG
CPI  & DOFRI   ,    !E- RECIRC FAN CMD FLAG
CPI  & DOVD    ,    !E- DIVERTER VALUE ROTARY ACT POSN
CPI  & DTPA    ,    !E- AMBIENT PRESSURE                       [psia]
CPI  & DTPCI   ,    !E- COMPARTMENT PRESSURES                  [psia]
CPI  & DTPDI   ,    !E- COMPT DIFF PRESS                        [psi]  DNPDC
CPI  & EP0     ,    !E- TOTAL AIR PRESS                                DNP0
CPI  & ETT1    ,    !E- RAM AIR TEMPERATURE                    [deg C]
CPI  & VM      ,    !E- MACH NUMBER
CPI  & VVT     ,    !E- AIRSPEED                              [ft/sec]
CPI  & VTEMP   ,    !E- AMBIENT TEMPERATURE AT A/C             [deg-c] DNTA
CPI  & DQTFE   ,    !E- ???
CPI  & DQWF         !E- ???
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:39:52 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DCTD           ! PNEU DUCT TEMP                       [deg C]
     &, DKTKAI(2)      ! PACK DISCH ADV TEMP                  [deg C]
     &, DKTKI(2)       ! PACK DISCH TEMP                      [deg C]
     &, DKWKI(2)       ! PACK DISCHARGE FLOW                 [lb/min]
     &, DKWTTI(2)      ! TRIM AIR VALVE FLOW                 [lb/min]
     &, DOVD           ! DIVERTER VALUE ROTARY ACT POSN       [coeff]
     &, DQTFE          ! ???
     &, DQWF           ! ???
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, DTPCI(2)       ! COMPT PRESS                            [psi]
     &, DTPDI(2)       ! COMPT DIFFERENTIAL PRESSURE            [psi]
     &, EP0            ! PRESSURE AT STATION 0                  [PSI]
     &, ETT1           ! FREE STREAM TOTAL TEMP.                  [C]
     &, VM             ! MACH NUMBER
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VVT            ! TOTAL A/C VELOCITY                    [ft/s]
C$
      LOGICAL*1
     &  BIAN03         ! CLG FAN                     21 PDAL   DI1949
     &, DNF            ! DCOND1 FREEZE FLAG
     &, DOFE           ! AVIONICS FAN CMD FLAG
     &, DOFF           ! FLIGHT COMPT CMD FLAG
     &, DOFRI(2)       ! RECIRC FAN CMD FLAG
     &, DZF300         ! DASH8 100/300 OPTION  (.T. => 300)
     &, DZFA           ! DASH8 100-300 / 100A-300A OPTION  (.T. => A)
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  DNHAI(2)       ! PACK DUCT TEMP FF                    [deg_C]
     &, DNHCI(2)       ! F DECK / CABIN TEMP FF               [deg_C]
     &, DNHDI(2)       ! ZONE DUCT TEMP FF                    [deg_C]
     &, DNHFI(2)       ! EQ COOLING TEMP FF                   [deg_C]
     &, DNHRI(2)       ! L/R DUCT RISER TEMP FF               [deg_C]
     &, DNHS           ! SKIN TEMP FF                         [deg_C]
     &, DNPR           ! RAM AIR GAGE PRESS                     [psi]
     &, DNTAAI(2)      ! PACK DUCT ADV TEMP                   [deg_C]
     &, DNTAI(2)       ! PACK DUCT TEMP                       [deg_C]
     &, DNTCAI(2)      ! ZONE ADV TEMP                        [deg_C]
     &, DNTCI(2)       ! ZONE TEMP                            [deg_C]
     &, DNTDAI(2)      ! ZONE DUCT ADV TEMP                   [deg_C]
     &, DNTDI(2)       ! ZONE DUCT TEMP                       [deg_C]
     &, DNTFI(2)       ! EQ COOLING TEMP                      [deg_C]
     &, DNTG           ! AIR COND GND SUPPLY TEMP             [deg_C]
     &, DNTRAI(2)      ! DUCT RISER ADV TEMP                  [deg_C]
     &, DNTRI(2)       ! L/R DUCT RISER TEMP                  [deg_C]
     &, DNTS           ! SKIN TEMPERATURE                     [deg_C]
     &, DNWC           ! PRESS AREA TOTAL INFLOW             [lb/min]
     &, DNWDI(2)       ! F DECK / CABIN DUCT AIRFLOW         [lb/min]
     &, DNWE           ! AVIONICS FAN FLOW                   [lb/min]
     &, DNWFD          ! FC FAN FLOW                         [lb/min]
     &, DNWFI(2)       ! RECIRC FAN FLOW                     [lb/min]
     &, DNWG           ! AIR COND GND SUPLY FF               [lb/min]
     &, DNWR           ! AIR COND RAM AIRFLOW                [lb/min]
C$
      LOGICAL*1
     &  DXFDN          ! DCOND1 INDICATION SYNCHRONIZ FLAG
C$
      LOGICAL*1
     &  DUM0000001(13511),DUM0000002(3768),DUM0000003(20)
     &, DUM0000004(1640),DUM0000005(77772),DUM0000006(440)
     &, DUM0000007(56),DUM0000008(16),DUM0000009(8)
     &, DUM0000010(20),DUM0000011(44),DUM0000012(1)
     &, DUM0000013(11),DUM0000014(4),DUM0000015(4)
     &, DUM0000016(193),DUM0000017(87),DUM0000018(15)
     &, DUM0000019(1529),DUM0000020(64)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,BIAN03,DUM0000002,VVT,DUM0000003,VM,DUM0000004
     &, VTEMP,DUM0000005,DCTD,DUM0000006,DKTKAI,DKTKI,DUM0000007
     &, DKWKI,DUM0000008,DKWTTI,DUM0000009,DNHAI,DNHCI,DNHDI
     &, DNHFI,DNHRI,DNHS,DNPR,DNTAAI,DNTAI,DNTDAI,DNTCAI,DNTCI
     &, DNTDI,DNTFI,DNTG,DNTRAI,DNTRI,DNTS,DNWC,DNWDI,DNWE,DNWFD
     &, DNWFI,DNWG,DNWR,DUM0000010,DOVD,DUM0000011,DOFE,DOFF
     &, DUM0000012,DOFRI,DUM0000013,DQTFE,DQWF,DUM0000014,DTPA
     &, DUM0000015,DTPCI,DTPDI,DUM0000016,DXFDN,DUM0000017,DZFA
     &, DZF300,DUM0000018,DNF,DUM0000019,EP0,DUM0000020,ETT1      
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DNPDC     
     &, DNP0     
     &, DNTA     
     &, DNTR     
     &, DNVM     
     &, DNVT     
C$
      LOGICAL*1
     &  DNSG     
C$
      EQUIVALENCE
     &  (DNPDC,DTPDI(2)),(DNP0,EP0),(DNTA,VTEMP),(DNTR,ETT1),(DNVM,VM)  
     &, (DNVT,VVT)                                                      
C------------------------------------------------------------------------------
C
C
C
C
C
C
C
C ---------------
C'Local_Variables
C ---------------
C
C
C
C    ----------------------------------------------------------------------
C                             M O D U L E
C
C               V A R I A B L E S    C O N V E N T I O N
C    ----------------------------------------------------------------------
C
C                L - local variable
C
C                X - common data base variable (read/write)
C
C                E - common data base variable (read only)
C
C                M - ECAM
C
C'Ident
C
      CHARACTER*55
     &  REV /
     &  '$Source: usd8dn.for.4  9Dec1992 21:03 usd8 M.WARD $'/
C
C
C  ----------------------
C  ECS  Integer_Variables
C  ----------------------
C
C
      INTEGER*4
C
C       Label       Description                 Units        Equival
C
     &  I          !L- LOOP INDEX
     &, K          !L- 300-INDEX
C
C
C
C
C
C
C  -------------------
C  ECS  Real_Variables
C  -------------------
C
C
      REAL*4
C
C
C       Label       Description                 Units        Equival
C
C
     &  DNBAAI(2) !L- PACK DUCT ADV TEMP TIME CST   [sec]
     &, DNBAI(2)  !L- PACK DUCT TEMP TIME CONST     [sec]
     &, DNBCAI(2) !L- ZONE TEMP ADV TEMP TIME CONST [sec]
     &, DNBCI(2)  !L- ZONE TEMP TIME CONST          [sec]
     &, DNBDAI(2) !L- ZONE DUCT ADV TEMP TIME CONST [sec]
     &, DNBDI(2)  !L- ZONE DUCT TEMP TIME CONST     [sec]
     &, DNBF      !L- EQ COOLING TEMP TIME CONST    [sec]
     &, DNBFI(2)  !L- RECIRC CAB/DUCT TEMP TIME CST [sec]
     &, DNBRAI(2) !L- DUCT RISER ADV TEMP TIME CST  [sec]
     &, DNBRI(2)  !L- DUCT RISER TEMP TIME CONST    [sec]
     &, DNDC      !L- TIME DELAY CONFIGURATION      [sec]
     &, DNHAAI(2) !L- PACK DUCT ADV TEMP FF         [deg_C]
     &, DNHCAI(2) !L- ZONE ADV TEMP FF              [deg_C]
     &, DNHDAI(2) !L- ZONE DUCT ADV TEMP FF         [deg_C]
     &, DNHRAI    !L- DUCT RISER ADV TEMP FF        [deg_C]
     &, DNOFX     !L- NEGATIVE RECIRC X-FLOW        [lb/min]
     &, DNOX      !L- NEGATIVE MANIF X-FLOW         [lb/min]
     &, DNPK      !L- MIX MANIFOLD GAGE PRESS       [psi]
     &, DNPD      !L- F DECK LOW DUCT PRESSURE      [psi]
     &, DNPDE     !L- AVIONICS FAN DIFF. PRESSURE   [psi]
     &, DNPDF     !L- F DECK DUCT DIFF. PRESSURE    [psi]
     &, DNPE      !L- AVIONICS FAN HEAD PRESSURE    [psi]
     &, DNPEF     !L- AVIONICS FAN HEAD PRESSURE FF [psi]
     &, DNPF      !L- FC FAN HEAD PRESS.            [psi]
     &, DNPFF     !L- FC FAN HEAD PRESS. FF         [psi]
     &, DNQFX     !L- POSITIVE RECIRC X-FLOW        [lb/min]
     &, DNQX      !L- POSITIVE MANIF X-FLOW         [lb/min]
     &, DNT2      !L- SUB-BAND ITER TIME            [sec]
     &, DNTEI(2)  !L- F DECK / CABIN ELECT THERM LOAD
     &, DNTF      !L- ZONE 1-2 HEAT TRANSFERT
     &, DNTKAI(2) !L- PACK DISCH ADV TEMP WITH NEW INDEX
C                                                   [deg_C]
     &, DNTKI(2)  !L- PACK DISCH TMP WITH NEW INDEX [deg_C]
     &, DNTL      !L- ITER TIME LAST                [sec]
     &, DNTLI(2)  !L- F DECK / CABIN INTERN THERM LOAD
     &, DNTSI(2)  !L- ZONE AMBIANT TEMP             [deg_C]
     &, DNWCI(2)  !L- ZONE GASPER RECIRC FLOW       [lb/min]
     &, DNWFF     !L- RECIRC FAN FLOW FF            [lb/min]
     &, DNWFFD    !L- FC FAN FLOW FF                [lb/min]
     &, DNWFX     !L- RECIRC CROSS-FLOW             [lb/min]
     &, DNWK      !L- AIR COND DUCT TOTAL INFLOW    [lb/min]
     &, DNWKI(2)  !L- PACK DISCH FLW WITH NEW INDEX [lb/min]
     &, DNWRI(2)  !L- F DECK / CABIN RISER AIRFLOW  [lb/min]
     &, DNWSI(2)  !L- ZONE HEAT TR FLOW TO AMBIANT  [lb/min]
     &, DNWT      !L- CABIN DUCT RECIRC INFLOW      [lb/min]
     &, DNWTTI(2) !L- TRIM AIR VALVE FLOW WITH NEW  [lb/min]
C                     INDEX
     &, DNWX      !L- MANIFOLD CROSS-FLOW           [lb/min]
     &, DNXAAI(2) !L- PACK DUCT ADV TEMP TIME FACT  [sec]
     &, DNXAI(2)  !L- PACK DUCT TEMP TIME FACT      [sec]
     &, DNXCAI(2) !L- ZONE ADV TEMP TIME FACT       [sec]
     &, DNXCI(2)  !L- F DECK / CABIN TEMP TIME FACT [sec]
     &, DNXDAI(2) !L- ZONE DUCT ADV TEMP TIME FACT  [sec]
     &, DNXDI(2)  !L- ZONE DUC TEMP TIME FACT       [sec]
     &, DNXE      !L- AVIONICS FAN PRESSURE TIME F
     &, DNXF      !L- FC FAN PRES TIME CONST        [sec]
     &, DNXFFI(2) !L- RECIRC FAN FLOW L/R TIME CNST [sec]
     &, DNXFI(2)  !L- RECIRC DUCT TEMP TIME FACT    [sec]
     &, DNXM      !L- SKIN TEMP PRESS RATIO
     &, DNXRAI    !L- DUCT RISER ADV TEMP TIME FACT [sec]
     &, DNXRI(2)  !L- L/R DUCT RISER TEMP TIME FACT [sec]
     &, DNXS      !L- SKIN TEMP TIME FACT
     &, DNXSS     !L- ???
     &, DNXT      !L- SKIN TEMP AIRSPEED FACT
     &, DNYAI(2)  !L- PACK DUCT HEAT TR FLOW        [lb/min]
     &, DNYCI(2)  !L- ZONE TOTAL HEAT TR FLOW       [lb/min]
     &, DNYDI(2)  !L- ZONE DUCT HEAT TR FLOW        [lb/min]
     &, DNYFI(2)  !L- RECIRC DUCT HEAT TR FLOW      [lb/min]
     &, DNYRI(2)  !L- L/R DUCT RISER HEAT TR FLOW   [lb/min]
C
C
C
C
C  ----------------------
C  ECS  Logical_Variables
C  ----------------------
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
C
C
     &  DNFB      !L- SUB BAND FLAG
     &, DNFBY     !L- BY PASS FLAG
     &, DNZF      !L- PROVISION
     &, DNZRI(2)  !L- PROVISION
     &, FIRST     /  .TRUE.  / !
C
C
C
C
C  --------------
C  ECS  Constants
C  --------------
C
C
      REAL*4
C
C       Label      Value           Description              Units
C
C
     &  DNCRI(2) / 0.984,2.44  / !                            [lb/min][lb/min]
     &, DNCSI(2) / 2.56 ,6.71  / !                            [lb/min][lb/min]
     &, DNC1     /   0.5       / !                               [1/sec]
     &, DNC2     /   0.5       / !                               [1/sec]
     &, DNC3     /   0.5       / !                               [1/sec]
     &, DNC10    /   0.0061    / !                               [min/(lb.sec)]
     &, DNC20    /   0.007143  / !                               [1/sec]
     &, DNC25    /   0.5       / !                               [1/sec]
     &, DNC30I(2)/   0.00476, 0.0  / !                           [min/(lb.sec)]
     &, DNC40I(2)/   0.01064, 0.0  / !                           [min/(lb.sec)]
     &, DNC50    /   2.0       / ! REF CAE
     &, DNC60I(2)/   0.01064, 0.0  / !                           [min/(lb.sec)]
     &, DNC70    /   2.0       / ! REF CAE
     &, DNC80I(2)  /   0.0055 , 0.0026  / !                      [min/(lb.sec)]
     &, DNC90    /   2.0       / ! REF CAE
     &, DNC100I(2) / 0.0005 , 0.00013   / !                      [min/(lb.sec)]
     &, DNC120   /   30.0      / !                               [n/a]
     &, DNCA20   /   0.1740    / !                               [1/mach**2]
     &, DNCA40   /   273.0     / !                               [deg_C]
     &, DNCA60   /   100.0     / !                               [ft/sec]
     &, DNCA61   /   5.0       / !                               [ft/sec]
     &, DNCA80   /   0.009222  / !                               [sec/ft]
     &, DNCA81   /   0.07778   / !                               [n/a]
     &, DNCC20   /   0.0055    / !                               [psi.min/lb]
     &, DNCC40   /   40.0      / !                               [lb/min]
     &, DNCC41   /   0.2       / !                               [psi]
     &, DNCC60   /   40.0      / !                               [lb/min]
     &, DNCC61   /   0.20      / !                               [psi]
     &, DNCC62   /   0.20      / !                               [psi]
     &, DNCC80   /   1.0       / !                               [deg_C]
     &, DNCC81   /   3.636     / !                               [n/a]
     &, DNCC82   /   29.0      / !                               [deg_C]
     &, DNCC83   /   11.0      / !                               [deg_C]
     &, DNCC84   /   0.0       / !                               [deg_C]
     &, DNCE20   /   0.005     / ! REF CAE                       [psi.min/lb]
     &, DNCE25   /   0.25      / !                               [psi]
     &, DNCE30   /   15.0      / !                               [lb/min]
     &, DNCE31   /   0.05      / !                               [psi]
     &, DNCE40I(2)/  21.0, 0.0 / !                           [lb/min] [lb/min]
     &, DNCE50   /   0.01      / !                               [psi]
     &, DNCE55   /   18.0      / !                               [lb/min]
     &, DNCE56   /   0.02      / !                               [psi]
     &, DNCE60   /   0.05      / ! REF CAE
     &, DNCE80   /   0.1       / ! REF CAE
     &, DNCG20   /   0.3       / !
     &, DNCG30   /   1.0       / !
     &, DNCG40   /   2.0       / !  REF CAE
     &, DNCK20   /   1.0       / !                               [lb/min]
     &, DNCK21   /   1.0       / !                               [lb/min]
     &, DNCM20   /   1.0       / !                               [lb/min]
     &, DNCO20   /   1.0       / !                               [lb/min]
     &, DNCQ20   /   1.0       / !                               [lb/min]
     &, DNCQ21   /   1.0       / !                               [lb/min]
     &, DNCS20   /   1.0       / !                               [lb/min]
     &, DNCS21   /   0.5       / !                               [lb/min]
     &, DNCU20   /   0.1       / !                               [deg_C]
     &, DNCV20   /   10.0      / !                               [deg_C]
     &, DNCV21   /   15.0      / !                               [deg_C]
     &, DNCV40   /   0.2       / !
C
C
C
C
      ENTRY DCOND1
C
C ----------------
C'Start_of_Program
C ----------------
C
C
C
C
      IF ( DNF ) THEN
C       Module freeze flag
      ELSE
C
C
C
C -------------------------
C First_Pass_Initialization
C -------------------------
C
C
      IF ( FIRST )  THEN
C
C
C
      FIRST    = .FALSE.
C
      ENDIF
C
C
C INITIALIZATION OF THE CONSTANTS FOR 100A / 300 / 300A
C -----------------------------------------------------
C
C
C     CONFIGURATION FUNCTION
C
C
      IF ( DNDC .GT. -2.0 ) THEN
C
C
C  MODEL 300A
C  ----------
C
      IF (DZF300 .AND. DZFA) THEN
C
        DNC30I(1) = 0.0118
        DNC30I(2) = 0.0029
        DNC40I(1) = 0.0125
        DNC40I(2) = 0.0077
        DNC60I(1) = 0.0125
        DNC60I(2) = 0.0069
        DNC80I(1) = 0.0061
        DNC80I(2) = 0.0027
        DNC100I(1)= 0.0003
        DNC100I(2)= 0.000072
        DNCE40I(1)= 7.5
        DNCE40I(2)= 33.75
        DNCC20 = 0.00303
        DNCSI(1) = 8.0
        DNCRI(1) = 2.85
        DNCSI(2) = 17.5
        DNCRI(2) = 7.77
C
C
C  MODEL 300
C  ---------
C
      ELSEIF ( DZF300 ) THEN
C
C
C  MODEL 100A
C  ----------
C
      ELSEIF ( DZFA ) THEN
C
        DNCE40I(1)=21.0
        DNCE40I(2)=0.0
        DNCSI(1) = 0.2
        DNCRI(1) = 1.22
        DNCSI(2) = 1.5
        DNCRI(2) = 2.54
C
      ENDIF
C
C
      DNDC = DNDC - YITIM
C
      ELSE
C
       IF ( DNTL .NE. YITIM ) THEN
       DNDC = 0.0
       ELSE
       ENDIF
      ENDIF
C
C
C     END OF CONFIGURATION FUNCTION
C
C
C
C -----------------------------------------------------
C MAPING : TO FIT PACK INDEX WITH ZONE INDEX
C -----------------------------------------------------
C
C
      IF ( DZF300 ) THEN
C
        DNWKI(1) = DKWKI(2)
        DNWKI(2) = DKWKI(1)
C
        DNTKI(1) = DKTKI(2)
        DNTKI(2) = DKTKI(1)
C
        DNTKAI(1) = DKTKAI(2)
        DNTKAI(2) = DKTKAI(1)
C
      ELSE
C
        DNWKI(1) = DKWKI(1)
        DNWKI(2) = 0.0
C
        DNTKI(1) = DKTKI(1)
C
        DNTKAI(1) = DKTKAI(1)
C
      ENDIF
C
      DNWTTI(1) = DKWTTI(2)
      DNWTTI(2) = DKWTTI(1)
C
C
C
CD ----------------------------------------------------
CD Function MAIN - Time Dependent Initialization
CD ----------------------------------------------------
C
C
CD 200  UPDATE [DXFDN]  INSTRUMENT SYNCH FLAG
C  ------------------------------------------
C
      DXFDN = .TRUE.
C 220
      IF (DNDC .GT. -2.0) THEN
C
CD 240  UPDATE [DNTL]  ITER TIME LAST  [sec]
C  ------------------------------------------
C
        DNTL = YITIM
C
CD 260  UPDATE [DNT2]  SUB-BAND ITER TIME  [sec]
C  ---------------------------------------------
C
        DNT2 = 2*DNTL
C
CD 400  UPDATE [DNXF]  FC FAN PRESSURE TIME CONST  [sec]
C  ------------------------------------------------------
C
        DNXF  = DNC1*DNTL
C
CD 420  UPDATE [DNXFFI]  RECIRC FAN FLOW LEFT/RIGHT TIME CONST  [sec]
C  ------------------------------------------------------------------
C
        DNXFFI(1) = DNC2*DNTL
C
        DNXFFI(2) = DNC3*DNTL
C
CD 460  UPDATE [DNBF]  EQ COOLING TEMP TIME CONST  [sec]
C  -----------------------------------------------------
C
        DNBF = DNC10*DNT2
C
CD 480  UPDATE [DNXSS] SKIN TEMP TIME CT
C       --------------------------------
C
        DNXSS = DNC20 * DNT2
C
CD 500  UPDATE [DNXE] AVIONICS FAN FLOW TIME CONSTANT
C       ---------------------------------------------
C
        DNXE = DNC25*DNTL
C
C 500
        DO I = 1, 2
C
CD      UPDATE [DNBFI]  RECIRC DUCT TEMP TIME CONST  [sec]
C       --------------------------------------------------
C
          DNBFI(I) = DNC30I(I)*DNT2
C
CD 540  UPDATE [DNBAI]  PACK DUCT TEMP TIME CONST  [sec]
C  -----------------------------------------------------
C
          DNBAI(I) = DNC40I(I)*DNT2
C
CD 560  UPDATE [DNBAAI]  PACK DUCT ADV TEMP TIME CONST  [sec]
C  ----------------------------------------------------------
C
          DNBAAI(I) = DNC50*DNBAI(I)
C
CD 580  UPDATE [DNBRI]  DUCT RISER TEMP TIME CONST  [sec]
C  ------------------------------------------------------
C
          DNBRI(I) = DNC60I(I)*DNT2
C
CD 600  UPDATE [DNBRAI]  DUCT RISER ADV TEMP TIME CONST  [sec]
C  -----------------------------------------------------------
C
          DNBRAI(I) = DNC70*DNBRI(I)
C
CD 800  UPDATE [DNBDI]  ZONE DUCT TEMP TIME CONST  [sec]
C  -----------------------------------------------------
C
          DNBDI(I) = DNC80I(I)*DNT2
C
CD 820  UPDATE [DNBDAI]  ZONE DUCT ADV TEMP TIME CONST  [sec]
C  ----------------------------------------------------------
C
          DNBDAI(I) = DNC90*DNBDI(I)
C
CD 840  UPDATE [DNBCI]  ZONE TEMP TIME CONST  [sec]
C  ------------------------------------------------
C
          DNBCI(I) = DNC100I(I)*DNT2
C
CD 860  UPDATE [DNBCAI]  ZONE TEMP ADV TIME CONST  [sec]
C  -----------------------------------------------------
C
          DNBCAI(I) = DNC120*DNBCI(I)
C 880
        END DO
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function A - Skin Temperature
CD ----------------------------------------------------
C
C
CD A100  UPDATE [K]  300-INDEX
C  ---------------------------
C
      IF ( DZF300 ) THEN
C A120
        K = 2
      ELSE
C A121
        K = 1
      ENDIF
C
CD A200  COMPUTE [DNPR]  RAM AIR GAGE PRESS  [psia]
C  ------------------------------------------------
C
      DNPR = AMAX1 (DNP0-DTPA, 0.0)
C
CD A220  COMPUTE [DNXM]  SKIN TEMP PRESS RATIO
C  -------------------------------------------
C
      DNXM = 1.0+DNCA20*DNVM*DNVM
C
CD A240  COMPUTE [DNHS]  SKIN TEMP FF  [deg]
C  -----------------------------------------
C
      DNHS = (DNTA+DNCA40)*DNXM-DNCA40
C A400
      IF (DNVT .GT. DNCA60) THEN
C A461
        DNXS = DNXSS
C A420
      ELSEIF (DNVT .LT. DNCA61) THEN
C A462
        DNXS = DNCA81*DNXSS
C
      ELSE
C
CD A440  COMPUTE [DNXT]  SKIN TEMP AIRSPEED FACT
C  ---------------------------------------------
C
        DNXT = DNCA80*DNVT+DNCA81
C
CD A460  COMPUTE [DNXS]  SKIN TEMP TIME FACTOR  [sec]
C  --------------------------------------------------
C
        DNXS = DNXT*DNXSS
C
      ENDIF
C
CD A480  COMPUTE [DNTS]  SKIN TEMPERATURE  [deg_C]
C  -----------------------------------------------
C
      DNTS = DNTS+DNXS*(DNHS-DNTS)
C
C
C
C
CD ----------------------------------------------------
CD Function C -
CD ----------------------------------------------------
C
C
CD C200  COMPUTE [DNPK]  MIX MANIFOLD GAGE PRESS  [psig]
C  -----------------------------------------------------
C
      DNPK = DNCC20*(DNWKI(1)+DNWKI(2))+DNPDC
C C240
      IF (DNPR .GT. DNPK) THEN
C
CD C260  COMPUTE [DNWR]  AIR COND RAM AIRFLOW  [lb/min]
C  ----------------------------------------------------
C
        DNWR = DNCC40*(DNPR-DNPK)/(DNCC41+DNPR-DNPK)
C
      ELSE
C C261
        DNWR = 0.0
C
      ENDIF
C C1000
      IF ( DNSG ) THEN
C C1020
        IF (DNPK .LT. DNCC62) THEN
C
CD C1040  COMPUTE [DNWG]  AIR COND GROUND SUPPLY FLOW  [lb/min]
C  ------------------------------------------------------------
C
          DNWG = DNCC60*(DNCC62-DNPK)/(DNCC61+DNCC62-DNPK)
C
CD C1060  COMPUTE [DNTG]  AIR COND GROUND SUPPLY TEMP  [deg_C]
C  -----------------------------------------------------------
C
          DNTG = DNCC80+DNCC81*AMIN1 (AMAX1 (DNCC82-DNTCI(1), DNCC84),
     &                                             DNCC83)
C
        ELSE
C C1041
          DNWG = 0.0
C C1061
          DNTG = DNTR
C
        ENDIF
C
      ELSE
C C1042
        DNWG = 0.0
C C1062
        DNTG = DNTR
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function E - F COMPT. FAN FLOW CALCULATION
CD ----------------------------------------------------
C
CD E200 COMPUTE [DNPD] F DECK LOW DUCT PRESSURE
C       ---------------------------------------
C
        DNPD = DTPDI(1) + DNCE20 * DNWDI(1)
C
C E230
        IF (DOFF .AND. .NOT.DNZF) THEN
C
CD E240 COMPUTE [DNPFF] FC FAN HEAD PRESSURE FF
C       ---------------------------------------
C
          DNPFF = DNCE25
C
        ELSE
C E241
          DNPFF = 0.0
C
        ENDIF
C
CD E260  COMPUTE [DNPF]  FC FAN HEAD PRESSURE FF
C        ---------------------------------------
C
         DNPF = DNPF + DNXF * ( DNPFF - DNPF )
C
CD E280 COMPUTE [DNPDF] F DECK DUCT DIFF. PRESSURE
C       ------------------------------------------
C
        DNPDF = DNPF - ABS (DNPD)
C
C E300
C
        IF ( DNPDF .GT. 0.0 ) THEN
C
CD E320 COMPUTE [DNWFD] FC FAN FLOW
C       ---------------------------
C
        DNWFD = DNCE30 * DNPDF / ( DNCE31 + DNPDF )
C
        ELSE
C E321
C
        DNWFD = 0.0
C
        ENDIF
C
C E1000
      DO I = 1, K
C
        IF (DOFRI(I) .AND. .NOT.DNZRI(I)) THEN
C
CD E1020  COMPUTE [DNWFF]  RECIRC FAN FLOW FF  [lb/min]
C  ----------------------------------------------------
C
          DNWFF = DNCE40I(I)
C
        ELSE
C E1021
          DNWFF = 0.0
C
        ENDIF
C
CD E1040  COMPUTE [DNWFI]  RECIRC FAN FLOW  [lb/min]
C  -------------------------------------------------
C
        DNWFI(I) = DNWFI(I)+DNXFFI(I)*(DNWFF-DNWFI(I))
C E1060
      END DO
C
CD E1200 IF COOLING FAN CB IS ON THEN
C        ----------------------------
C
         IF ( DOFE ) THEN
C
CD E1220 UPDATE [DNPEF] AVIONICS FAN HEAD PRESSURE FF
C        --------------------------------------------
C
         DNPEF = DNCE50
C
C  ELSE
C
         ELSE
C
         DNPEF = 0.0
C
C  ENDIF
C
         ENDIF
C
CD E1240 UPDATE [DNPE] AVIONICS FAN HEAD PRESSURE
C        ----------------------------------------
C
         DNPE = DNPE + DNXE * ( DNPEF - DNPE )
C
CD E1260 UPDATE [DNPDE] AVIONICS FAN DIFFERENCE PRESSURE
C        -----------------------------------------------
C
         DNPDE = DTPCI(1) + DNPE - DTPCI(2)
C
CD E1280 IF DIFF. PRESSURE GREATER THAN 0 THEN
C        -------------------------------------
C
         IF (( DNPDE .GT. 0.0 ) .AND. ( DNPE .GT. 0.001) ) THEN
C
CD E1300 UPDATE [DNWE] AVIONICS FAN FLOW
C        -------------------------------
C
         DNWE = DNCE55 * DNPDE / ( DNPDE + DNCE56 )
C
CD  ELSE
C
         ELSE
C
         DNWE = 0.0
C
CD  ENDIF
C
         ENDIF
C
C E2000
      IF (DZF300 .OR. DZFA) THEN
C
      ELSE
C
CD E2020  COMPUTE [DNWCI]  CABIN/DECK GASPER RECIRC FLOW  [lb/min]
C  ---------------------------------------------------------------
C
        DNWCI(2) = DNCE80*DNWFI(1)
C E2040
        DNWCI(1) = DNCE60*DNWFI(1)
C
      ENDIF
C
CD IF ( F300 ) THEN
C  ----------------
C
      IF ( DZF300 ) THEN
C
CD E2121  COMPUTE [DNWT]
C  ---------------------
C
        DNWT = DNWFI(1)
C
CD ELSE
C  ----
C
      ELSE
C
CD E2100  COMPUTE [DNWT]  CABIN DUCT RECIRC INFLOW  [lb/min]
C  ---------------------------------------------------------
C
        DNWT = DNWFI(1)-DNWCI(1)-DNWCI(2)
C
CD ENDIF
C  -----
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function G -
CD ----------------------------------------------------
C
C
CD G200  COMPUTE [DNWC]  PRESS ARIA TOTAL INFLOW  [lb/min]
C  -------------------------------------------------------
C
CMW   DNWC = DNWKI(1)+DNWKI(2)+DNWTTI(1)+DNWTTI(2)+DNWG+DNWR !+DNWFD
      DNWC = DNWKI(1)+DNWKI(2)+DNWTTI(1)+DNWTTI(2)+DNWG+DNWR+DNWFD
C
CD G220  COMPUTE [DNWK]  AIR COND DUCT TOTAL INFLOW  [lb/min]
C  ----------------------------------------------------------
C
CMW      DNWK = DNWKI(1)+DNWKI(2)+DNWT+DNWFI(2)+DNWR+DNWG+DNWFD
      DNWK = DNWKI(1)+DNWKI(2)+DNWT+DNWFI(2)+DNWR+DNWG
C
CD G240  COMPUTE [DNWRI]  CABIN/DECK RISER AIRFLOW  [lb/min]
C  ---------------------------------------------------------
C
      DNWRI(1) = DNCG20*(1.0+DNCG30*DOVD)*DNWK
C
C G260
C
      DNWRI(2) = DNWK-DNWRI(1)
C
CD G280	 COMPUTE [DNWDI]  CABIN/DECK DUCT AIRFLOW  [lb/min]
C  --------------------------------------------------------
C
CMW      DNWDI(1) = DNWRI(1)+DNWTTI(1)
      DNWDI(1) = DNWRI(1)+DNWTTI(1)+DNWFD
C
C G300
C
      DNWDI(2) = DNWRI(2)+DNWTTI(2)
C
CD G500  IF ( F300 ) THEN
C  ----------------------
C
      IF ( DZF300 ) THEN
C
CD G520  COMPUTE [DNWFX]  RECIRC CROSS-FLOW  [lb/min]
C  --------------------------------------------------
C
        DNWFX = (DNWFI(2)-DNWFI(1))/DNCG40
C
CD G540  COMPUTE [DNQFX]  POSITIVE RECIRC X-FLOW  [lb/min]
C  -------------------------------------------------------
C
        DNQFX = AMAX1 (DNWFX, 0.0)
C
CD G560  COMPUTE [DNOFX]  NEGATIVE RECIRC X-FLOW  [lb/min]
C  -------------------------------------------------------
C
        DNOFX = DNQFX-DNWFX
C
CD G580  COMPUTE [DNWX]  MANIFOLD CROSS-FLOW  [lb/min]
C  ---------------------------------------------------
C
        DNWX = DNWFI(1)+DNWKI(1)-DNWDI(1)
C
CD G600	 COMPUTE [DNQX]  POSITIVE MANIF X-FLOW  [lb/min]
C  -----------------------------------------------------
C
        DNQX = AMAX1 (DNWX, 0.0)
C
CD G600  COMPUTE [DNOX]  NEGATIVE MANIF X-FLOW  [lb/min]
C  -----------------------------------------------------
C
        DNOX = DNQX-DNWX
C
CD ELSE
C  ----
C
      ELSE
C
CD G541  COMPUTE [DNQFX]
C  ---------------------
C
        DNQFX = DNWFI(1) / DNCG40
C
CD ENDIF
C  -----
C
      ENDIF
C
C
CD ----------------------------------------------------
CD Function I -
CD ----------------------------------------------------
C
C
C I100
      IF ( DNFB        ) THEN
C
CD I120  UPDATE [DNFB]  SUB BAND FLAG
C  ---------------------------------
C
        DNFB = .FALSE.
C
CD I140  UPDATE [I]  INDEX
C  -----------------------
C
        I = 2
C
CD I160  UPDATE [DNFBY]  BY-PASS FLAG
C  ----------------------------------
C
        DNFBY = .NOT.DZF300
C
      ELSE
C I121
        DNFB = .TRUE.
C I141
        I = 1
C I161
        DNFBY = .FALSE.
C
      ENDIF
C
C
CD ----------------------------------------------------
CD Function K -
CD ----------------------------------------------------
C
C K200
      IF (DZF300 .OR. DZFA .OR. DNFBY) THEN
C
      ELSE
C
CD K220  COMPUTE [DNYFL]  EQ COOLING HEAT TR FLOW
C  ----------------------------------------------
C
        DNYFI(1) = DNWFI(1)+DNCK20+DNCK21
C
CD K240  COMPUTE [DNXFL]  EQ COOLING TEMP TIME FACT  [SEC]
C  -------------------------------------------------------
C
        DNXFI(1) = DNBF*DNYFI(1)
C
CD K260  COMPUTE [DNHFL]  EQ COOLING TEMP FF  [deg_C]
C  --------------------------------------------------
C
        DNHFI(1) = (DNQFX*DNTCI(1)+(DNWFI(1)-DNQFX)*DNTCI(2)+DNCK20*
     &              DQTFE+DNCK21*DNTR)/DNYFI(1)
C
CD K280  COMPUTE [DNTFL]  EQ COOLING TEMP  [deg_C]
C  -----------------------------------------------
C
        DNTFI(1) = DNTFI(1)+DNXFI(1)*(DNHFI(1)-DNTFI(1))
C
      ENDIF
C
CD K600
C
      IF ( .NOT.DZF300 .AND. DZFA .AND. .NOT.DNFBY ) THEN
C
CD K620  COMPUTE [DNYF1]  RECIRC DUCT HT TR FLOW
C  ---------------------------------------------
C
        DNYFI(1) = DNWFI(1) + DNCK21
C
CD K640  COMPUTE [DNXF1]  RECIRC DUCT TEMP TIME FACT
C  -------------------------------------------------
C
        DNXFI(1) = DNBFI(1) * DNYFI(1)
C
CD K660  COMPUTE [DNHF1]  RECIRC DUCT TEMP FF
C  ------------------------------------------
C
        DNHFI(1) = ( DNQFX * DNTCI(1) + ( DNWFI(1) - DNQFX ) * DNTCI(2)
     &             + DNCK21 * DNTR ) / DNYFI(1)
C
CD K680  COMPUTE [DNTF1]  RECIRC DUCT TEMP
C  ---------------------------------------
C
        DNTFI(1) = DNTFI(1) + DNXFI(1) * ( DNHFI(1) - DNTFI(1) )
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function M -
CD ----------------------------------------------------
C
C M200
      IF ( DZFA .AND. DZF300 .AND. .NOT.DNFBY ) THEN
C
CD M220  COMPUTE [DNYFI]  RECIRC DUCT HT TR FLOW
C  ---------------------------------------------
C
        DNYFI(I) = DNWFI(I)+DNCM20
C
CD M240  COMPUTE [DNXFI]  RECIRC DUCT TEMP TIME FACT  [sec]
C  --------------------------------------------------------
C
        DNXFI(I) = DNBFI(I)*DNYFI(I)
C M260
        IF (I .EQ. 1) THEN
C
CD M280  COMPUTE [DNHFI]  RECIRC DUCT TEMP FF  [deg_C]
C  ---------------------------------------------------
C
          DNHFI(1) = (DNOFX*DNTCI(2)+(DNWFI(1)-DNOFX)*DNTCI(1)+
     &                  DNCM20*DNTR)/DNYFI(1)
C
        ELSE
C M281
          DNHFI(2) = (DNQFX*DNTCI(1)+(DNWFI(2)-DNQFX)*DNTCI(2)+
     &                  DNCM20*DNTR)/DNYFI(2)
C
        ENDIF
C
CD M300  COMPUTE [DNTFI]  RECIRC DUCT TEMP  [deg_C]
C  ------------------------------------------------
C
        DNTFI(I) = DNTFI(I)+DNXFI(I)*(DNHFI(I)-DNTFI(I))
C
      ENDIF    ! M200
C
C
C
CD ----------------------------------------------------
CD Function O -
CD ----------------------------------------------------
C
C O200
      IF ( DNFBY ) THEN
C
      ELSE
C
        IF ( DZF300 ) THEN
C
CD O600  COMPUTE [DNYAI]  PACK DUCT HEAT TR FLOW
C  ---------------------------------------------
C
          DNYAI(I) = DNWFI(I) + DNWKI(I) + DNCO20
C
CD 0620  COMPUTE [DNXAI]  PACK DUCT TEMP TIME FACT
C  -----------------------------------------------
C
          DNXAI(I) = DNBAI(I) * DNYAI(I)
C
CD O640  COMPUTE [DNHAI]  PACK DUCT TEMP FF
C  ----------------------------------------
C
          DNHAI(I) = ( DNWFI(I) * DNTFI(I) + DNWKI(I) * DNTKI(I) +
     &                 DNCO20 * DNTR ) / DNYAI(I)
C
CD O660  COMPUTE [DNTAI]  PACK DUCT TEMPERATURE
C  --------------------------------------------
C
          DNTAI(I) = DNTAI(I) + DNXAI(I) * ( DNHAI(I) - DNTAI(I) )
C
           ELSE
C
CD O240  COMPUTE [DNYA1]  PACK DUCT HT TR FLOW
C  -------------------------------------------
C
          DNYAI(1) = DNWT+DNWKI(1)+DNCO20
C
CD O260  COMPUTE [DNXA1]  PACK DUCT TEMP TIME FACT  [sec]
C  ------------------------------------------------------
C
          DNXAI(1) = DNBAI(1)*DNYAI(1)
C
CD O280  COMPUTE [DNHA1]  PACK DUCT TEMP FF  [deg_C]
C  -------------------------------------------------
C
          DNHAI(1) = (DNWT*DNTFI(1)+DNWKI(1)*DNTKI(1)+DNCO20*DNTR)/
     &                  DNYAI(1)
C
CD O300  COMPUTE [DNTA1]  PACK DUCT TEMPERATURE  [deg_C]
C  -----------------------------------------------------
C
          DNTAI(1) = DNTAI(1) + DNXAI(1) * ( DNHAI(1) - DNTAI(1) )
C
        ENDIF
C
CD O1000  COMPUTE [DNHAAI]  PACK DUCT ADV TEMP FF [deg_C]
C  -----------------------------------------------------
C
        DNHAAI(I) = DNHAI(I)+DNWKI(I)*(DNTKAI(I)-DNTKI(I))/DNYAI(I)
C
CD O1020  COMPUTE [DNXAAI]  PACK DUCT ADV TEMP TIME FACT  [sec]
C  ------------------------------------------------------------
C
        DNXAAI(I) = AMIN1 (DNBAAI(I)*DNYAI(I), 1.0)
C
CD O1040  COMPUTE [DNTAAI]  PACK DUCT ADV TEMPERATURE  [deg_C]
C  -----------------------------------------------------------
C
        DNTAAI(I) = DNTAAI(I)+DNXAAI(I)*(DNHAAI(I)-DNTAAI(I))
C
C
C
CD ----------------------------------------------------
CD Function Q -
CD ----------------------------------------------------
C
C Q200
        IF (I .EQ. 1) THEN
C
          IF ( DZF300 ) THEN
C
CD Q600  COMPUTE [DNYR1]  DUCT 1 RISER HT TR FLOW
C  ----------------------------------------------
C
            DNYRI(1) = ( DNYAI(1) - DNCO20 ) + DNOX + DNCQ20
C
CD Q620  COMPUTE [DNXR1]  DUCT 1 RISER TEMP TIME FACT
C  --------------------------------------------------
C
            DNXRI(1) = DNBRI(1) * DNYRI(1)
C
CD Q640  COMPUTE [DNHR1]  DUCT RISER 1 TEMP FF
C  -------------------------------------------
C
            DNHRI(1) = ( ( DNYAI(1) - DNCO20 ) * DNTAI(1) + DNOX *
     &                   DNTRI(2) + DNCQ20 * DNTR ) / DNYRI(1)
C
          ELSE
C
CD Q240  COMPUTE [DNYR1]
C  ---------------------
C
            DNYRI(1) = ( DNYAI(1) - DNCO20 ) + DNWR + DNWG + DNCQ20
C
CD Q260  COMPUTE [DNXR1]
C  ---------------------
C
            DNXRI(1) = DNBRI(1) * DNYRI(1)
C
CD Q280  COMPUTE [DNHR1]
C  ---------------------
C
            DNHRI(1) = ( ( DNYAI(1) - DNCO20 ) * DNTAI(1) + DNWR *
     &                   DNTR + DNWG * DNTG + DNCQ20 * DNTR ) / DNYRI(1)
C
          ENDIF
C
        ELSE     ! Q200
C
CD Q1000 COMPUTE [DNYR2]  DUCT 2 RISER HT TR FLOW
C  ----------------------------------------
C
          DNYRI(2) = ( DNYAI(2) - DNCO20 ) + DNQX + DNWR + DNCQ21
C
CD COMPUTE [DNXR2]  DUCT 2 RISER TEMP TIME FACT
C  --------------------------------------------
C
          DNXRI(2) = DNBRI(2) * DNYRI(2)
C
CD COMPUTE [DNHR2]  DUCT RISER 2 TEMP FF
C  -------------------------------------
C
          DNHRI(2) = ( (DNYAI(2) - DNCO20) * DNTAI(2) + DNQX *DNTRI(1)
     &                 + DNWR * DNTR + DNCQ21 * DNTR ) / DNYRI(2)
C
        ENDIF    ! Q200
C
CD Q1060  COMPUTE [DNTRI]  DUCT RISER TEMPERATURE
C  ----------------------------------------------
C
        DNTRI(I) = DNTRI(I) + DNXRI(I) * ( DNHRI(I) - DNTRI(I) )
C
CD Q2000  COMPUTE [DNHRAI]  DUCT RISER ADV TEMP FF  [deg_C]
C  --------------------------------------------------------
C
        DNHRAI = DNHRI(I)+(DNYAI(I)-DNCO20)*(DNTAAI(I)-DNTAI(I))
     &                         /DNYRI(I)
C
CD Q2020  COMPUTE [DNXRAI]  DUCT RISER ADV TEMP TIME FACT  [sec]
C  -------------------------------------------------------------
C
        DNXRAI = AMIN1 (DNBRAI(I)*DNYRI(I), 1.0)
C
CD Q2040  COMPUTE [DNTRAI]  DUCT RISER ADV TEMP  [deg_C]
C  -----------------------------------------------------
C
        DNTRAI(I) = DNTRAI(I)+DNXRAI*(DNHRAI-DNTRAI(I))
C
      ENDIF    !  O200
C
C
CD ----------------------------------------------------
CD Function S -
CD ----------------------------------------------------
C
C
CD S200  COMPUTE [DNYDI]  ZONE DUCT HEAT TR FLOW  [lb/min]
C  -------------------------------------------------------
C
      DNYDI(I) =  DNWRI(I)+DNWTTI(I)+DNCS20+DNCS21
C
CD S220  COMPUTE [DNXDI]  ZONE DUCT TEMP TIME FACT [sec]
C  -----------------------------------------------------
C
      DNXDI(I) = DNBDI(I)*DNYDI(I)
C S240
      IF ( DZF300 ) THEN
C
CD S260  COMPUTE [DNHDI]  ZONE DUCT TEMP FF  [deg_C]
C  -------------------------------------------------
C
        DNHDI(I) = (DNWRI(I)*DNTRI(I)+DNWTTI(I)*DCTD+DNCS20*DNTCI(I)+
     &                DNCS21*DNTR)/DNYDI(I)
C
      ELSE
C S261
        DNHDI(I) = (DNWRI(I)*DNTRI(1)+DNWTTI(I)*DCTD+DNCS20*DNTCI(I)+
     &                DNCS21*DNTR)/DNYDI(I)
C
      ENDIF
C
CD S280  COMPUTE [DNTDI]  ZONE DUCT TEMPERATURE  [deg_C]
C  -----------------------------------------------------
C
      DNTDI(I) = DNTDI(I)+DNXDI(I)*(DNHDI(I)-DNTDI(I))
C
CD S1000  IF ( F300 ) THEN
C  -----------------------
C
      IF ( DZF300 ) THEN
C
      ELSE
C
CD S1020  COMPUTE [DNTR2]  DUCT 2 RISER TEMP  [deg_C]
C  --------------------------------------------------
C
        DNTRI(2) = DNTRI(1)
C
CD S1040  COMPUTE [DNTRA2]  DUCT 2 RISER ADV TEMP  [deg_C]
C  -------------------------------------------------------
C
        DNTRAI(2) = DNTRAI(1)
C
      ENDIF
C
CD S1060  COMPUTE [DNHDAI]  ZONE DUCT ADV TEMP FF  [deg_C]
C  -------------------------------------------------------
C
      DNHDAI(I) = DNHDI(I)+DNWRI(I)*(DNTRAI(I)-DNTRI(I))/DNYDI(I)
C
CD S1020  COMPUTE [DNXDAI]  ZONE DUCT ADV TEMP TIME FACT  [sec]
C  ------------------------------------------------------------
C
      DNXDAI(I) = AMIN1 (DNBDAI(I)*DNYDI(I), 1.0)
C
CD S1040  COMPUTE [DNTDAI]  ZONE DUCT ADV TEMP  [deg_C]
C -----------------------------------------------------
C
      DNTDAI(I) = DNTDAI(I)+DNXDAI(I)*(DNHDAI(I)-DNTDAI(I))
C
C
C
CD ----------------------------------------------------
CD Function U - Zone Temperatures
CD ----------------------------------------------------
C
C U200
      IF (DNHCI(I) .GT. DNTSI(I)-DNCU20) THEN
C
CD U220  COMPUTE [DNWSI]  ZONE HEAT TR FLOW TO AMB  [lb/min]
C  ---------------------------------------------------------
C
        DNWSI(I) = DNCRI(I)
C
      ELSE
C U221
        DNWSI(I) = DNCSI(I)
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function V -
CD ----------------------------------------------------
C
C V200
      IF (I .EQ. 2) THEN
C
CD V220  UPDATE [DNTEI]  CABIN/DECK ELECT THERM LOAD
C  -------------------------------------------------
C
        DNTEI(1) = DNCV20
C V240
        DNTEI(2) = DNCV21
C
CD V260  COMPUTE [DNTF]  ZONE 1-2 HEAT TRANSFER
C  --------------------------------------------
C
        DNTF = DNCV40*(DNTCI(1)-DNTCI(2))
C
CD V280  COPUTE [DNTLI]  CABIN/DECK INTERN THERM LOAD
C  --------------------------------------------------
C
        DNTLI(1) = DNTEI(1)-DNTF
C V300
        DNTLI(2) = DNTEI(2)+DNTF
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function W -
CD ----------------------------------------------------
C
C
CD W200  COMPUTE [DNTSI]  ZONE AMBIANT TEMP  [deg_C]
C  -------------------------------------------------
C
      DNTSI(I) = DNTS+DNTLI(I)
C W220
      IF (I .EQ. 1) THEN
C
CD W500  COMPUTE [DNYC1]  F DECK TOTAL HEAT TR FLOW
C  ------------------------------------------------
C
        DNYCI(1) = DNWDI(1)+DNWSI(1)+DNWCI(1)+DNWFD
C
CD W520  COMPUTE [DNXC1]  F DECK TEMP FF  [deg_C]
C  ----------------------------------------------
C
        DNXCI(1) = DNBCI(1)*DNYCI(1)
C
CD W540  COMPUTE [DNHC1]  F DECK TEMP FF  [deg_C]
C  ----------------------------------------------
C
        DNHCI(1) = (DNWDI(1)*DNTDI(1)+DNWSI(1)*DNTSI(1)+
     &                DNWCI(1)*DNTFI(1)+DNWFD*DNTR)/DNYCI(1)
C
      ELSE
C
CD W240  COMPUTE [DNYC2]  CABIN TOTAL HEAT TR FLOW  [lb/min]
C  ---------------------------------------------------------
C
        DNYCI(2) = DNWDI(2)+DNWSI(2)+DNWCI(2)
C
CD W260  COMPUTE [DNXC2]  CABIN TEMP TIME FACT  [sec]
C  --------------------------------------------------
C
        DNXCI(2) = DNBCI(2)*DNYCI(2)
C
CD W280  COMPUTE [DNHC2]  CABIN TEMP FF  [deg_C]
C  ---------------------------------------------
C
        DNHCI(2) = (DNWDI(2)*DNTDI(2)+DNWSI(2)*DNTSI(2)+
     &                DNWCI(2)*DNTFI(2))/DNYCI(2)
C
      ENDIF   ! W220
C
CD W800  COMPUTE [DNTCI]  ZONE TEMPERATURE  [deg_C]
C  ------------------------------------------------
C
      DNTCI(I) = DNTCI(I)+DNXCI(I)*(DNHCI(I)-DNTCI(I))
C
CD W1000  COMPUTE [DNXCAI]  ZONE ADV TEMP TIME FACT  [sec]
C  -------------------------------------------------------
C
      DNXCAI(I) = DNBCAI(I)*DNYCI(I)
C
CD W1020  COMPUTE [DNHCAI]  ZONE ADV TEMP FF  [deg_C]
C  --------------------------------------------------
C
      DNHCAI(I) = DNHCI(I)+DNWDI(I)*(DNHDAI(I)-DNTDI(I))/DNYCI(I)
C
CD W1040  COMPUTE [DNTCAI]  ZONE ADV TEMP  [deg_C]
C  -----------------------------------------------
C
      DNTCAI(I) = DNTCAI(I)+DNXCAI(I)*(DNHCAI(I)-DNTCAI(I))
C W2000
C
C  ----------------------------------------------------------------------------
C
      ENDIF              ! FIN DU PROGRAMME
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00641 ----------------------------------------------------
C$ 00642 Function MAIN - Time Dependent Initialization
C$ 00643 ----------------------------------------------------
C$ 00646 200  UPDATE [DXFDN]  INSTRUMENT SYNCH FLAG
C$ 00653 240  UPDATE [DNTL]  ITER TIME LAST  [sec]
C$ 00658 260  UPDATE [DNT2]  SUB-BAND ITER TIME  [sec]
C$ 00663 400  UPDATE [DNXF]  FC FAN PRESSURE TIME CONST  [sec]
C$ 00668 420  UPDATE [DNXFFI]  RECIRC FAN FLOW LEFT/RIGHT TIME CONST  [sec]
C$ 00675 460  UPDATE [DNBF]  EQ COOLING TEMP TIME CONST  [sec]
C$ 00680 480  UPDATE [DNXSS] SKIN TEMP TIME CT
C$ 00685 500  UPDATE [DNXE] AVIONICS FAN FLOW TIME CONSTANT
C$ 00693 UPDATE [DNBFI]  RECIRC DUCT TEMP TIME CONST  [sec]
C$ 00698 540  UPDATE [DNBAI]  PACK DUCT TEMP TIME CONST  [sec]
C$ 00703 560  UPDATE [DNBAAI]  PACK DUCT ADV TEMP TIME CONST  [sec]
C$ 00708 580  UPDATE [DNBRI]  DUCT RISER TEMP TIME CONST  [sec]
C$ 00713 600  UPDATE [DNBRAI]  DUCT RISER ADV TEMP TIME CONST  [sec]
C$ 00718 800  UPDATE [DNBDI]  ZONE DUCT TEMP TIME CONST  [sec]
C$ 00723 820  UPDATE [DNBDAI]  ZONE DUCT ADV TEMP TIME CONST  [sec]
C$ 00728 840  UPDATE [DNBCI]  ZONE TEMP TIME CONST  [sec]
C$ 00733 860  UPDATE [DNBCAI]  ZONE TEMP ADV TIME CONST  [sec]
C$ 00744 ----------------------------------------------------
C$ 00745 Function A - Skin Temperature
C$ 00746 ----------------------------------------------------
C$ 00749 A100  UPDATE [K]  300-INDEX
C$ 00760 A200  COMPUTE [DNPR]  RAM AIR GAGE PRESS  [psia]
C$ 00765 A220  COMPUTE [DNXM]  SKIN TEMP PRESS RATIO
C$ 00770 A240  COMPUTE [DNHS]  SKIN TEMP FF  [deg]
C$ 00785 A440  COMPUTE [DNXT]  SKIN TEMP AIRSPEED FACT
C$ 00790 A460  COMPUTE [DNXS]  SKIN TEMP TIME FACTOR  [sec]
C$ 00797 A480  COMPUTE [DNTS]  SKIN TEMPERATURE  [deg_C]
C$ 00805 ----------------------------------------------------
C$ 00806 Function C -
C$ 00807 ----------------------------------------------------
C$ 00810 C200  COMPUTE [DNPK]  MIX MANIFOLD GAGE PRESS  [psig]
C$ 00817 C260  COMPUTE [DNWR]  AIR COND RAM AIRFLOW  [lb/min]
C$ 00832 C1040  COMPUTE [DNWG]  AIR COND GROUND SUPPLY FLOW  [lb/min]
C$ 00837 C1060  COMPUTE [DNTG]  AIR COND GROUND SUPPLY TEMP  [deg_C]
C$ 00861 ----------------------------------------------------
C$ 00862 Function E - F COMPT. FAN FLOW CALCULATION
C$ 00863 ----------------------------------------------------
C$ 00865 E200 COMPUTE [DNPD] F DECK LOW DUCT PRESSURE
C$ 00873 E240 COMPUTE [DNPFF] FC FAN HEAD PRESSURE FF
C$ 00884 E260  COMPUTE [DNPF]  FC FAN HEAD PRESSURE FF
C$ 00889 E280 COMPUTE [DNPDF] F DECK DUCT DIFF. PRESSURE
C$ 00898 E320 COMPUTE [DNWFD] FC FAN FLOW
C$ 00915 E1020  COMPUTE [DNWFF]  RECIRC FAN FLOW FF  [lb/min]
C$ 00926 E1040  COMPUTE [DNWFI]  RECIRC FAN FLOW  [lb/min]
C$ 00933 E1200 IF COOLING FAN CB IS ON THEN
C$ 00938 E1220 UPDATE [DNPEF] AVIONICS FAN HEAD PRESSURE FF
C$ 00953 E1240 UPDATE [DNPE] AVIONICS FAN HEAD PRESSURE
C$ 00958 E1260 UPDATE [DNPDE] AVIONICS FAN DIFFERENCE PRESSURE
C$ 00963 E1280 IF DIFF. PRESSURE GREATER THAN 0 THEN
C$ 00968 E1300 UPDATE [DNWE] AVIONICS FAN FLOW
C$ 00973 ELSE
C$ 00979 ENDIF
C$ 00988 E2020  COMPUTE [DNWCI]  CABIN/DECK GASPER RECIRC FLOW  [lb/min]
C$ 00997 IF ( F300 ) THEN
C$ 01002 E2121  COMPUTE [DNWT]
C$ 01007 ELSE
C$ 01012 E2100  COMPUTE [DNWT]  CABIN DUCT RECIRC INFLOW  [lb/min]
C$ 01017 ENDIF
C$ 01024 ----------------------------------------------------
C$ 01025 Function G -
C$ 01026 ----------------------------------------------------
C$ 01029 G200  COMPUTE [DNWC]  PRESS ARIA TOTAL INFLOW  [lb/min]
C$ 01035 G220  COMPUTE [DNWK]  AIR COND DUCT TOTAL INFLOW  [lb/min]
C$ 01041 G240  COMPUTE [DNWRI]  CABIN/DECK RISER AIRFLOW  [lb/min]
C$ 01050 G280  COMPUTE [DNWDI]  CABIN/DECK DUCT AIRFLOW  [lb/min]
C$ 01060 G500  IF ( F300 ) THEN
C$ 01065 G520  COMPUTE [DNWFX]  RECIRC CROSS-FLOW  [lb/min]
C$ 01070 G540  COMPUTE [DNQFX]  POSITIVE RECIRC X-FLOW  [lb/min]
C$ 01075 G560  COMPUTE [DNOFX]  NEGATIVE RECIRC X-FLOW  [lb/min]
C$ 01080 G580  COMPUTE [DNWX]  MANIFOLD CROSS-FLOW  [lb/min]
C$ 01085 G600  COMPUTE [DNQX]  POSITIVE MANIF X-FLOW  [lb/min]
C$ 01090 G600  COMPUTE [DNOX]  NEGATIVE MANIF X-FLOW  [lb/min]
C$ 01095 ELSE
C$ 01100 G541  COMPUTE [DNQFX]
C$ 01105 ENDIF
C$ 01111 ----------------------------------------------------
C$ 01112 Function I -
C$ 01113 ----------------------------------------------------
C$ 01119 I120  UPDATE [DNFB]  SUB BAND FLAG
C$ 01124 I140  UPDATE [I]  INDEX
C$ 01129 I160  UPDATE [DNFBY]  BY-PASS FLAG
C$ 01145 ----------------------------------------------------
C$ 01146 Function K -
C$ 01147 ----------------------------------------------------
C$ 01154 K220  COMPUTE [DNYFL]  EQ COOLING HEAT TR FLOW
C$ 01159 K240  COMPUTE [DNXFL]  EQ COOLING TEMP TIME FACT  [SEC]
C$ 01164 K260  COMPUTE [DNHFL]  EQ COOLING TEMP FF  [deg_C]
C$ 01170 K280  COMPUTE [DNTFL]  EQ COOLING TEMP  [deg_C]
C$ 01177 K600
C$ 01181 K620  COMPUTE [DNYF1]  RECIRC DUCT HT TR FLOW
C$ 01186 K640  COMPUTE [DNXF1]  RECIRC DUCT TEMP TIME FACT
C$ 01191 K660  COMPUTE [DNHF1]  RECIRC DUCT TEMP FF
C$ 01197 K680  COMPUTE [DNTF1]  RECIRC DUCT TEMP
C$ 01206 ----------------------------------------------------
C$ 01207 Function M -
C$ 01208 ----------------------------------------------------
C$ 01213 M220  COMPUTE [DNYFI]  RECIRC DUCT HT TR FLOW
C$ 01218 M240  COMPUTE [DNXFI]  RECIRC DUCT TEMP TIME FACT  [sec]
C$ 01225 M280  COMPUTE [DNHFI]  RECIRC DUCT TEMP FF  [deg_C]
C$ 01238 M300  COMPUTE [DNTFI]  RECIRC DUCT TEMP  [deg_C]
C$ 01247 ----------------------------------------------------
C$ 01248 Function O -
C$ 01249 ----------------------------------------------------
C$ 01258 O600  COMPUTE [DNYAI]  PACK DUCT HEAT TR FLOW
C$ 01263 0620  COMPUTE [DNXAI]  PACK DUCT TEMP TIME FACT
C$ 01268 O640  COMPUTE [DNHAI]  PACK DUCT TEMP FF
C$ 01274 O660  COMPUTE [DNTAI]  PACK DUCT TEMPERATURE
C$ 01281 O240  COMPUTE [DNYA1]  PACK DUCT HT TR FLOW
C$ 01286 O260  COMPUTE [DNXA1]  PACK DUCT TEMP TIME FACT  [sec]
C$ 01291 O280  COMPUTE [DNHA1]  PACK DUCT TEMP FF  [deg_C]
C$ 01297 O300  COMPUTE [DNTA1]  PACK DUCT TEMPERATURE  [deg_C]
C$ 01304 O1000  COMPUTE [DNHAAI]  PACK DUCT ADV TEMP FF [deg_C]
C$ 01309 O1020  COMPUTE [DNXAAI]  PACK DUCT ADV TEMP TIME FACT  [sec]
C$ 01314 O1040  COMPUTE [DNTAAI]  PACK DUCT ADV TEMPERATURE  [deg_C]
C$ 01321 ----------------------------------------------------
C$ 01322 Function Q -
C$ 01323 ----------------------------------------------------
C$ 01330 Q600  COMPUTE [DNYR1]  DUCT 1 RISER HT TR FLOW
C$ 01335 Q620  COMPUTE [DNXR1]  DUCT 1 RISER TEMP TIME FACT
C$ 01340 Q640  COMPUTE [DNHR1]  DUCT RISER 1 TEMP FF
C$ 01348 Q240  COMPUTE [DNYR1]
C$ 01353 Q260  COMPUTE [DNXR1]
C$ 01358 Q280  COMPUTE [DNHR1]
C$ 01368 Q1000 COMPUTE [DNYR2]  DUCT 2 RISER HT TR FLOW
C$ 01373 COMPUTE [DNXR2]  DUCT 2 RISER TEMP TIME FACT
C$ 01378 COMPUTE [DNHR2]  DUCT RISER 2 TEMP FF
C$ 01386 Q1060  COMPUTE [DNTRI]  DUCT RISER TEMPERATURE
C$ 01391 Q2000  COMPUTE [DNHRAI]  DUCT RISER ADV TEMP FF  [deg_C]
C$ 01397 Q2020  COMPUTE [DNXRAI]  DUCT RISER ADV TEMP TIME FACT  [sec]
C$ 01402 Q2040  COMPUTE [DNTRAI]  DUCT RISER ADV TEMP  [deg_C]
C$ 01410 ----------------------------------------------------
C$ 01411 Function S -
C$ 01412 ----------------------------------------------------
C$ 01415 S200  COMPUTE [DNYDI]  ZONE DUCT HEAT TR FLOW  [lb/min]
C$ 01420 S220  COMPUTE [DNXDI]  ZONE DUCT TEMP TIME FACT [sec]
C$ 01427 S260  COMPUTE [DNHDI]  ZONE DUCT TEMP FF  [deg_C]
C$ 01440 S280  COMPUTE [DNTDI]  ZONE DUCT TEMPERATURE  [deg_C]
C$ 01445 S1000  IF ( F300 ) THEN
C$ 01452 S1020  COMPUTE [DNTR2]  DUCT 2 RISER TEMP  [deg_C]
C$ 01457 S1040  COMPUTE [DNTRA2]  DUCT 2 RISER ADV TEMP  [deg_C]
C$ 01464 S1060  COMPUTE [DNHDAI]  ZONE DUCT ADV TEMP FF  [deg_C]
C$ 01469 S1020  COMPUTE [DNXDAI]  ZONE DUCT ADV TEMP TIME FACT  [sec]
C$ 01474 S1040  COMPUTE [DNTDAI]  ZONE DUCT ADV TEMP  [deg_C]
C$ 01481 ----------------------------------------------------
C$ 01482 Function U - Zone Temperatures
C$ 01483 ----------------------------------------------------
C$ 01488 U220  COMPUTE [DNWSI]  ZONE HEAT TR FLOW TO AMB  [lb/min]
C$ 01501 ----------------------------------------------------
C$ 01502 Function V -
C$ 01503 ----------------------------------------------------
C$ 01508 V220  UPDATE [DNTEI]  CABIN/DECK ELECT THERM LOAD
C$ 01515 V260  COMPUTE [DNTF]  ZONE 1-2 HEAT TRANSFER
C$ 01520 V280  COPUTE [DNTLI]  CABIN/DECK INTERN THERM LOAD
C$ 01531 ----------------------------------------------------
C$ 01532 Function W -
C$ 01533 ----------------------------------------------------
C$ 01536 W200  COMPUTE [DNTSI]  ZONE AMBIANT TEMP  [deg_C]
C$ 01543 W500  COMPUTE [DNYC1]  F DECK TOTAL HEAT TR FLOW
C$ 01548 W520  COMPUTE [DNXC1]  F DECK TEMP FF  [deg_C]
C$ 01553 W540  COMPUTE [DNHC1]  F DECK TEMP FF  [deg_C]
C$ 01561 W240  COMPUTE [DNYC2]  CABIN TOTAL HEAT TR FLOW  [lb/min]
C$ 01566 W260  COMPUTE [DNXC2]  CABIN TEMP TIME FACT  [sec]
C$ 01571 W280  COMPUTE [DNHC2]  CABIN TEMP FF  [deg_C]
C$ 01579 W800  COMPUTE [DNTCI]  ZONE TEMPERATURE  [deg_C]
C$ 01584 W1000  COMPUTE [DNXCAI]  ZONE ADV TEMP TIME FACT  [sec]
C$ 01589 W1020  COMPUTE [DNHCAI]  ZONE ADV TEMP FF  [deg_C]
C$ 01594 W1040  COMPUTE [DNTCAI]  ZONE ADV TEMP  [deg_C]
