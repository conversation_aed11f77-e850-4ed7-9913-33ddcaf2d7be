/* $ScmHeader: 9996w2w0z25614C891w2999999918&1|@ $*/
/* $Id: matrix_res.h,v 1.0, 2003-05-23 15:57:25Z, <PERSON>$*/
/*
C'Title            Matrix Resolution Generic Model
C'Module_ID        matrix_res.h
C'Entry_point      matrix_resolution
C'Author           <PERSON>'Date             Feb 1998
C'System           Ancillaries (21)
C'Subsystem        Network Matrix Resolution
C'Documentation    Matrix Resolution User Guide
C'Compilation_directives

IMPORTANT NOTE:

  Do not modify this file, this is a standard file used by several systems.
  If you encounter any problem with this file, please contact the Ancillaries
  responsable for the Matrix Resolution.
  
    
     Pre-compile using >cpc [file], then compile using c4l [file].
     When the [file.obj] is created, stamp the file using stamper [file.c].
     Then enter in simex using >simex enter [file.ext.
     It must be CPC'd, compiled and put again in Simex
     after each CDB update. Do not compile this file with c4ld unless
     required.

C'Revision History

Rev 1.3 <PERSON> 27/07/1999
        The resolution technique have been changed from Gauss LUD to a 
        Bandwidth resolution method using Gaus<PERSON> elimination. The arguments
        of the function prothotype changed.

C  matrix_res.h.2 20Jul1998 16:28 a130 MPoulin
C       < Corrected Subsystem filed in the header to comply with CAE 
C         Standards for header >

Rev 1.2 Louis Robitaille 25/02/1998
        Removed static rev definition

Rev 1.1 Louis Robitaille 25/02/1998
        Initial Release of the Matrix Resolution Technique

C'Ident
*/
static char rev1[] = "";

void matrix_resolution( double **A , double *b , double **x , int size ,
                        int bw );
