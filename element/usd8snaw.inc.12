C                                                                               
C      WAVEGEN CONTROLLER INCLUDE FILE                                          
C                                                                               
      INTEGER*2                                                                 
C                                                                               
     C DSGHADSO( 96, 19: 20)           !DSG address of each source              
C                                                                               
      DATA                                                                      
     & DSGHADSO(  1, 19) /X'A001' /    ,!(  1) SOURCE01                         
     & DSGHADSO(  2, 19) /X'A003' /    ,!(  2) SOURCE02                         
     & DSGHADSO(  3, 19) /X'A005' /    ,!(  3) SOURCE03                         
     & DSGHADSO(  4, 19) /X'A007' /    ,!(  4) SOURCE04                         
     & DSGHADSO(  5, 19) /X'A009' /    ,!(  5) SOURCE05                         
     & DSGHADSO(  6, 19) /X'A00B' /    ,!(  6) SOURCE06                         
     & <PERSON><PERSON><PERSON>DSO(  7, 19) /X'A00D' /    ,!(  7) SOURCE07                         
     & DSGHADSO(  8, 19) /X'A00F' /    ,!(  8) SOURCE08                         
     & <PERSON><PERSON><PERSON><PERSON><PERSON>(  9, 19) /X'A011' /    ,!(  9) SOURCE09                         
     & <PERSON><PERSON><PERSON><PERSON><PERSON>( 10, 19) /X'A013' /    ,!( 10) SOURCE10                         
     & <PERSON><PERSON><PERSON><PERSON><PERSON>( 11, 19) /X'0000' /    ,!( 11)                                  
     & <PERSON><PERSON><PERSON><PERSON><PERSON>( 12, 19) /X'0000' /    ,!( 12)                                  
     & DSGHADSO( 13, 19) /X'0000' /    ,!( 13)                                  
     & DSGHADSO( 14, 19) /X'0000' /    ,!( 14)                                  
     & DSGHADSO( 15, 19) /X'0000' /    ,!( 15)                                  
     & DSGHADSO( 16, 19) /X'0000' /    ,!( 16)                                  
     & DSGHADSO( 17, 19) /X'0000' /    ,!( 17)                                  
     & DSGHADSO( 18, 19) /X'0000' /    ,!( 18)                                  
     & DSGHADSO( 19, 19) /X'0000' /    ,!( 19)                                  
     & DSGHADSO( 20, 19) /X'0000' /    ,!( 20)                                  
     & DSGHADSO( 21, 19) /X'0000' /    ,!( 21)                                  
     & DSGHADSO( 22, 19) /X'0000' /    ,!( 22)                                  
     & DSGHADSO( 23, 19) /X'0000' /    ,!( 23)                                  
     & DSGHADSO( 24, 19) /X'0000' /    ,!( 24)                                  
     & DSGHADSO( 25, 19) /X'0000' /    ,!( 25)                                  
     & DSGHADSO( 26, 19) /X'0000' /    ,!( 26)                                  
     & DSGHADSO( 27, 19) /X'0000' /    ,!( 27)                                  
     & DSGHADSO( 28, 19) /X'0000' /    ,!( 28)                                  
     & DSGHADSO( 29, 19) /X'0000' /    ,!( 29)                                  
     & DSGHADSO( 30, 19) /X'0000' /    ,!( 30)                                  
     & DSGHADSO( 31, 19) /X'0000' /    ,!( 31)                                  
     & DSGHADSO( 32, 19) /X'0000' /    ,!( 32)                                  
     & DSGHADSO( 33, 19) /X'0000' /    ,!( 33)                                  
     & DSGHADSO( 34, 19) /X'0000' /    ,!( 34)                                  
     & DSGHADSO( 35, 19) /X'0000' /    ,!( 35)                                  
     & DSGHADSO( 36, 19) /X'0000' /    ,!( 36)                                  
     & DSGHADSO( 37, 19) /X'0000' /    ,!( 37)                                  
     & DSGHADSO( 38, 19) /X'0000' /    ,!( 38)                                  
     & DSGHADSO( 39, 19) /X'0000' /    ,!( 39)                                  
     & DSGHADSO( 40, 19) /X'0000' /    ,!( 40)                                  
     & DSGHADSO( 41, 19) /X'0000' /    ,!( 41)                                  
     & DSGHADSO( 42, 19) /X'0000' /    ,!( 42)                                  
     & DSGHADSO( 43, 19) /X'0000' /    ,!( 43)                                  
     & DSGHADSO( 44, 19) /X'0000' /    ,!( 44)                                  
     & DSGHADSO( 45, 19) /X'0000' /    ,!( 45)                                  
     & DSGHADSO( 46, 19) /X'0000' /    ,!( 46)                                  
     & DSGHADSO( 47, 19) /X'0000' /    ,!( 47)                                  
     & DSGHADSO( 48, 19) /X'0000' /    ,!( 48)                                  
     & DSGHADSO( 49, 19) /X'0000' /    ,!( 49)                                  
     & DSGHADSO( 50, 19) /X'0000' /    ,!( 50)                                  
     & DSGHADSO( 51, 19) /X'0000' /    ,!( 51)                                  
     & DSGHADSO( 52, 19) /X'0000' /    ,!( 52)                                  
     & DSGHADSO( 53, 19) /X'0000' /    ,!( 53)                                  
     & DSGHADSO( 54, 19) /X'0000' /    ,!( 54)                                  
     & DSGHADSO( 55, 19) /X'0000' /    ,!( 55)                                  
     & DSGHADSO( 56, 19) /X'0000' /    ,!( 56)                                  
     & DSGHADSO( 57, 19) /X'0000' /    ,!( 57)                                  
     & DSGHADSO( 58, 19) /X'0000' /    ,!( 58)                                  
     & DSGHADSO( 59, 19) /X'0000' /    ,!( 59)                                  
     & DSGHADSO( 60, 19) /X'0000' /    ,!( 60)                                  
     & DSGHADSO( 61, 19) /X'0000' /    ,!( 61)                                  
     & DSGHADSO( 62, 19) /X'0000' /    ,!( 62)                                  
     & DSGHADSO( 63, 19) /X'0000' /    ,!( 63)                                  
     & DSGHADSO( 64, 19) /X'0000' /    ,!( 64)                                  
     & DSGHADSO( 65, 19) /X'0000' /    ,!( 65)                                  
     & DSGHADSO( 66, 19) /X'0000' /    ,!( 66)                                  
     & DSGHADSO( 67, 19) /X'0000' /    ,!( 67)                                  
     & DSGHADSO( 68, 19) /X'0000' /    ,!( 68)                                  
     & DSGHADSO( 69, 19) /X'0000' /    ,!( 69)                                  
     & DSGHADSO( 70, 19) /X'0000' /    ,!( 70)                                  
     & DSGHADSO( 71, 19) /X'0000' /    ,!( 71)                                  
     & DSGHADSO( 72, 19) /X'0000' /    ,!( 72)                                  
     & DSGHADSO( 73, 19) /X'0000' /    ,!( 73)                                  
     & DSGHADSO( 74, 19) /X'0000' /    ,!( 74)                                  
     & DSGHADSO( 75, 19) /X'0000' /    ,!( 75)                                  
     & DSGHADSO( 76, 19) /X'0000' /    ,!( 76)                                  
     & DSGHADSO( 77, 19) /X'0000' /    ,!( 77)                                  
     & DSGHADSO( 78, 19) /X'0000' /    ,!( 78)                                  
     & DSGHADSO( 79, 19) /X'0000' /    ,!( 79)                                  
     & DSGHADSO( 80, 19) /X'0000' /    ,!( 80)                                  
     & DSGHADSO( 81, 19) /X'0000' /    ,!( 81)                                  
     & DSGHADSO( 82, 19) /X'0000' /    ,!( 82)                                  
     & DSGHADSO( 83, 19) /X'0000' /    ,!( 83)                                  
     & DSGHADSO( 84, 19) /X'0000' /    ,!( 84)                                  
     & DSGHADSO( 85, 19) /X'0000' /    ,!( 85)                                  
     & DSGHADSO( 86, 19) /X'0000' /    ,!( 86)                                  
     & DSGHADSO( 87, 19) /X'0000' /    ,!( 87)                                  
     & DSGHADSO( 88, 19) /X'0000' /    ,!( 88)                                  
     & DSGHADSO( 89, 19) /X'0000' /    ,!( 89)                                  
     & DSGHADSO( 90, 19) /X'0000' /    ,!( 90)                                  
     & DSGHADSO( 91, 19) /X'0000' /    ,!( 91)                                  
     & DSGHADSO( 92, 19) /X'0000' /    ,!( 92)                                  
     & DSGHADSO( 93, 19) /X'0000' /    ,!( 93)                                  
     & DSGHADSO( 94, 19) /X'0000' /    ,!( 94)                                  
     & DSGHADSO( 95, 19) /X'0000' /    ,!( 95)                                  
     & DSGHADSO( 96, 19) /X'0000' /     !( 96)                                  
C                                                                               
      INTEGER*2                                                                 
C                                                                               
     C DSGHPHSO( 96, 19: 20)           !DSG phase of each source                
C                                                                               
      DATA                                                                      
     & DSGHPHSO(  1, 19) /X'B000' /    ,!(  1) SOURCE01                         
     & DSGHPHSO(  2, 19) /X'B002' /    ,!(  2) SOURCE02                         
     & DSGHPHSO(  3, 19) /X'B004' /    ,!(  3) SOURCE03                         
     & DSGHPHSO(  4, 19) /X'B006' /    ,!(  4) SOURCE04                         
     & DSGHPHSO(  5, 19) /X'B008' /    ,!(  5) SOURCE05                         
     & DSGHPHSO(  6, 19) /X'B00A' /    ,!(  6) SOURCE06                         
     & DSGHPHSO(  7, 19) /X'B00C' /    ,!(  7) SOURCE07                         
     & DSGHPHSO(  8, 19) /X'B00E' /    ,!(  8) SOURCE08                         
     & DSGHPHSO(  9, 19) /X'B010' /    ,!(  9) SOURCE09                         
     & DSGHPHSO( 10, 19) /X'B012' /    ,!( 10) SOURCE10                         
     & DSGHPHSO( 11, 19) /X'0000' /    ,!( 11)                                  
     & DSGHPHSO( 12, 19) /X'0000' /    ,!( 12)                                  
     & DSGHPHSO( 13, 19) /X'0000' /    ,!( 13)                                  
     & DSGHPHSO( 14, 19) /X'0000' /    ,!( 14)                                  
     & DSGHPHSO( 15, 19) /X'0000' /    ,!( 15)                                  
     & DSGHPHSO( 16, 19) /X'0000' /    ,!( 16)                                  
     & DSGHPHSO( 17, 19) /X'0000' /    ,!( 17)                                  
     & DSGHPHSO( 18, 19) /X'0000' /    ,!( 18)                                  
     & DSGHPHSO( 19, 19) /X'0000' /    ,!( 19)                                  
     & DSGHPHSO( 20, 19) /X'0000' /    ,!( 20)                                  
     & DSGHPHSO( 21, 19) /X'0000' /    ,!( 21)                                  
     & DSGHPHSO( 22, 19) /X'0000' /    ,!( 22)                                  
     & DSGHPHSO( 23, 19) /X'0000' /    ,!( 23)                                  
     & DSGHPHSO( 24, 19) /X'0000' /    ,!( 24)                                  
     & DSGHPHSO( 25, 19) /X'0000' /    ,!( 25)                                  
     & DSGHPHSO( 26, 19) /X'0000' /    ,!( 26)                                  
     & DSGHPHSO( 27, 19) /X'0000' /    ,!( 27)                                  
     & DSGHPHSO( 28, 19) /X'0000' /    ,!( 28)                                  
     & DSGHPHSO( 29, 19) /X'0000' /    ,!( 29)                                  
     & DSGHPHSO( 30, 19) /X'0000' /    ,!( 30)                                  
     & DSGHPHSO( 31, 19) /X'0000' /    ,!( 31)                                  
     & DSGHPHSO( 32, 19) /X'0000' /    ,!( 32)                                  
     & DSGHPHSO( 33, 19) /X'0000' /    ,!( 33)                                  
     & DSGHPHSO( 34, 19) /X'0000' /    ,!( 34)                                  
     & DSGHPHSO( 35, 19) /X'0000' /    ,!( 35)                                  
     & DSGHPHSO( 36, 19) /X'0000' /    ,!( 36)                                  
     & DSGHPHSO( 37, 19) /X'0000' /    ,!( 37)                                  
     & DSGHPHSO( 38, 19) /X'0000' /    ,!( 38)                                  
     & DSGHPHSO( 39, 19) /X'0000' /    ,!( 39)                                  
     & DSGHPHSO( 40, 19) /X'0000' /    ,!( 40)                                  
     & DSGHPHSO( 41, 19) /X'0000' /    ,!( 41)                                  
     & DSGHPHSO( 42, 19) /X'0000' /    ,!( 42)                                  
     & DSGHPHSO( 43, 19) /X'0000' /    ,!( 43)                                  
     & DSGHPHSO( 44, 19) /X'0000' /    ,!( 44)                                  
     & DSGHPHSO( 45, 19) /X'0000' /    ,!( 45)                                  
     & DSGHPHSO( 46, 19) /X'0000' /    ,!( 46)                                  
     & DSGHPHSO( 47, 19) /X'0000' /    ,!( 47)                                  
     & DSGHPHSO( 48, 19) /X'0000' /    ,!( 48)                                  
     & DSGHPHSO( 49, 19) /X'0000' /    ,!( 49)                                  
     & DSGHPHSO( 50, 19) /X'0000' /    ,!( 50)                                  
     & DSGHPHSO( 51, 19) /X'0000' /    ,!( 51)                                  
     & DSGHPHSO( 52, 19) /X'0000' /    ,!( 52)                                  
     & DSGHPHSO( 53, 19) /X'0000' /    ,!( 53)                                  
     & DSGHPHSO( 54, 19) /X'0000' /    ,!( 54)                                  
     & DSGHPHSO( 55, 19) /X'0000' /    ,!( 55)                                  
     & DSGHPHSO( 56, 19) /X'0000' /    ,!( 56)                                  
     & DSGHPHSO( 57, 19) /X'0000' /    ,!( 57)                                  
     & DSGHPHSO( 58, 19) /X'0000' /    ,!( 58)                                  
     & DSGHPHSO( 59, 19) /X'0000' /    ,!( 59)                                  
     & DSGHPHSO( 60, 19) /X'0000' /    ,!( 60)                                  
     & DSGHPHSO( 61, 19) /X'0000' /    ,!( 61)                                  
     & DSGHPHSO( 62, 19) /X'0000' /    ,!( 62)                                  
     & DSGHPHSO( 63, 19) /X'0000' /    ,!( 63)                                  
     & DSGHPHSO( 64, 19) /X'0000' /    ,!( 64)                                  
     & DSGHPHSO( 65, 19) /X'0000' /    ,!( 65)                                  
     & DSGHPHSO( 66, 19) /X'0000' /    ,!( 66)                                  
     & DSGHPHSO( 67, 19) /X'0000' /    ,!( 67)                                  
     & DSGHPHSO( 68, 19) /X'0000' /    ,!( 68)                                  
     & DSGHPHSO( 69, 19) /X'0000' /    ,!( 69)                                  
     & DSGHPHSO( 70, 19) /X'0000' /    ,!( 70)                                  
     & DSGHPHSO( 71, 19) /X'0000' /    ,!( 71)                                  
     & DSGHPHSO( 72, 19) /X'0000' /    ,!( 72)                                  
     & DSGHPHSO( 73, 19) /X'0000' /    ,!( 73)                                  
     & DSGHPHSO( 74, 19) /X'0000' /    ,!( 74)                                  
     & DSGHPHSO( 75, 19) /X'0000' /    ,!( 75)                                  
     & DSGHPHSO( 76, 19) /X'0000' /    ,!( 76)                                  
     & DSGHPHSO( 77, 19) /X'0000' /    ,!( 77)                                  
     & DSGHPHSO( 78, 19) /X'0000' /    ,!( 78)                                  
     & DSGHPHSO( 79, 19) /X'0000' /    ,!( 79)                                  
     & DSGHPHSO( 80, 19) /X'0000' /    ,!( 80)                                  
     & DSGHPHSO( 81, 19) /X'0000' /    ,!( 81)                                  
     & DSGHPHSO( 82, 19) /X'0000' /    ,!( 82)                                  
     & DSGHPHSO( 83, 19) /X'0000' /    ,!( 83)                                  
     & DSGHPHSO( 84, 19) /X'0000' /    ,!( 84)                                  
     & DSGHPHSO( 85, 19) /X'0000' /    ,!( 85)                                  
     & DSGHPHSO( 86, 19) /X'0000' /    ,!( 86)                                  
     & DSGHPHSO( 87, 19) /X'0000' /    ,!( 87)                                  
     & DSGHPHSO( 88, 19) /X'0000' /    ,!( 88)                                  
     & DSGHPHSO( 89, 19) /X'0000' /    ,!( 89)                                  
     & DSGHPHSO( 90, 19) /X'0000' /    ,!( 90)                                  
     & DSGHPHSO( 91, 19) /X'0000' /    ,!( 91)                                  
     & DSGHPHSO( 92, 19) /X'0000' /    ,!( 92)                                  
     & DSGHPHSO( 93, 19) /X'0000' /    ,!( 93)                                  
     & DSGHPHSO( 94, 19) /X'0000' /    ,!( 94)                                  
     & DSGHPHSO( 95, 19) /X'0000' /    ,!( 95)                                  
     & DSGHPHSO( 96, 19) /X'0000' /     !( 96)                                  
C                                                                               
      INTEGER*2                                                                 
C                                                                               
     C DSGHPOTA( 64, 19: 20)           !DSG pointer to each table               
C                                                                               
      DATA                                                                      
     & DSGHPOTA(  1, 19) /X'0001' /    ,!(  1) TABLE(  1)                       
     & DSGHPOTA(  2, 19) /X'0003' /    ,!(  2) TABLE(  2)                       
     & DSGHPOTA(  3, 19) /X'0005' /    ,!(  3) TABLE(  3)                       
     & DSGHPOTA(  4, 19) /X'0007' /    ,!(  4) TABLE(  4)                       
     & DSGHPOTA(  5, 19) /X'0009' /    ,!(  5) TABLE(  5)                       
     & DSGHPOTA(  6, 19) /X'000B' /    ,!(  6) TABLE(  6)                       
     & DSGHPOTA(  7, 19) /X'000D' /    ,!(  7) TABLE(  7)                       
     & DSGHPOTA(  8, 19) /X'000F' /    ,!(  8) TABLE(  8)                       
     & DSGHPOTA(  9, 19) /X'0011' /    ,!(  9) TABLE(  9)                       
     & DSGHPOTA( 10, 19) /X'0013' /    ,!( 10) TABLE( 10)                       
     & DSGHPOTA( 11, 19) /X'0015' /    ,!( 11) TABLE( 11)                       
     & DSGHPOTA( 12, 19) /X'0017' /    ,!( 12) TABLE( 12)                       
     & DSGHPOTA( 13, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 14, 19) /X'0019' /    ,!( 13) TABLE( 14)                       
     & DSGHPOTA( 15, 19) /X'001B' /    ,!( 14) TABLE( 15)                       
     & DSGHPOTA( 16, 19) /X'001D' /    ,!( 15) TABLE( 16)                       
     & DSGHPOTA( 17, 19) /X'001F' /    ,!( 16) TABLE( 17)                       
     & DSGHPOTA( 18, 19) /X'0021' /    ,!( 17) TABLE( 18)                       
     & DSGHPOTA( 19, 19) /X'0023' /    ,!( 18) TABLE( 19)                       
     & DSGHPOTA( 20, 19) /X'0025' /    ,!( 19) TABLE( 20)                       
     & DSGHPOTA( 21, 19) /X'0027' /    ,!( 20) TABLE( 21)                       
     & DSGHPOTA( 22, 19) /X'0029' /    ,!( 21) TABLE( 22)                       
     & DSGHPOTA( 23, 19) /X'002B' /    ,!( 22) TABLE( 23)                       
     & DSGHPOTA( 24, 19) /X'002D' /    ,!( 23) TABLE( 24)                       
     & DSGHPOTA( 25, 19) /X'002F' /    ,!( 24) TABLE( 25)                       
     & DSGHPOTA( 26, 19) /X'0031' /    ,!( 25) TABLE( 26)                       
     & DSGHPOTA( 27, 19) /X'0033' /    ,!( 26) TABLE( 27)                       
     & DSGHPOTA( 28, 19) /X'0035' /    ,!( 27) TABLE( 28)                       
     & DSGHPOTA( 29, 19) /X'0037' /    ,!( 28) TABLE( 29)                       
     & DSGHPOTA( 30, 19) /X'0039' /    ,!( 29) TABLE( 30)                       
     & DSGHPOTA( 31, 19) /X'003B' /    ,!( 30) TABLE( 31)                       
     & DSGHPOTA( 32, 19) /X'003D' /    ,!( 31) TABLE( 32)                       
     & DSGHPOTA( 33, 19) /X'003F' /    ,!( 32) TABLE( 33)                       
     & DSGHPOTA( 34, 19) /X'0041' /    ,!( 33) TABLE( 34)                       
     & DSGHPOTA( 35, 19) /X'0043' /    ,!( 34) TABLE( 35)                       
     & DSGHPOTA( 36, 19) /X'0045' /    ,!( 35) TABLE( 36)                       
     & DSGHPOTA( 37, 19) /X'0047' /    ,!( 36) TABLE( 37)                       
     & DSGHPOTA( 38, 19) /X'0049' /    ,!( 37) TABLE( 38)                       
     & DSGHPOTA( 39, 19) /X'004B' /    ,!( 38) TABLE( 39)                       
     & DSGHPOTA( 40, 19) /X'004D' /    ,!( 39) TABLE( 40)                       
     & DSGHPOTA( 41, 19) /X'004F' /    ,!( 40) TABLE( 41)                       
     & DSGHPOTA( 42, 19) /X'0051' /    ,!( 41) TABLE( 42)                       
     & DSGHPOTA( 43, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 44, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 45, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 46, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 47, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 48, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 49, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 50, 19) /X'0053' /    ,!( 42) TABLE( 50)                       
     & DSGHPOTA( 51, 19) /X'0055' /    ,!( 43) TABLE( 51)                       
     & DSGHPOTA( 52, 19) /X'0057' /    ,!( 44) TABLE( 52)                       
     & DSGHPOTA( 53, 19) /X'0059' /    ,!( 45) TABLE( 53)                       
     & DSGHPOTA( 54, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 55, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 56, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 57, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 58, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 59, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 60, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 61, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 62, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 63, 19) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 64, 19) /X'0000' /     !Table does not exist                   
C                                                                               
      INTEGER*2                                                                 
C                                                                               
     C DSGHSZSO( 96, 19: 20)            !DSG size of each source                
C                                                                               
      DATA                                                                      
     & DSGHSZSO(  1, 19) /  512/       ,!(  1) SOURCE01 = TABLE( 26)            
     & DSGHSZSO(  2, 19) /  512/       ,!(  2) SOURCE02 = TABLE( 27)            
     & DSGHSZSO(  3, 19) /  512/       ,!(  3) SOURCE03 = TABLE( 26)            
     & DSGHSZSO(  4, 19) /  512/       ,!(  4) SOURCE04 = TABLE( 27)            
     & DSGHSZSO(  5, 19) /  512/       ,!(  5) SOURCE05 = TABLE(  1)            
     & DSGHSZSO(  6, 19) /  512/       ,!(  6) SOURCE06 = TABLE(  1)            
     & DSGHSZSO(  7, 19) /  512/       ,!(  7) SOURCE07 = TABLE(  1)            
     & DSGHSZSO(  8, 19) /  512/       ,!(  8) SOURCE08 = TABLE(  1)            
     & DSGHSZSO(  9, 19) /  512/       ,!(  9) SOURCE09 = TABLE(  1)            
     & DSGHSZSO( 10, 19) /  512/       ,!( 10) SOURCE10 = TABLE(  1)            
     & DSGHSZSO( 11, 19) /  512/       ,!( 11) SOURCE11 = TABLE(  1)            
     & DSGHSZSO( 12, 19) /  512/       ,!( 12) SOURCE12 = TABLE(  1)            
     & DSGHSZSO( 13, 19) /  512/       ,!( 13) SOURCE13 = TABLE(  1)            
     & DSGHSZSO( 14, 19) /  512/       ,!( 14) SOURCE14 = TABLE(  1)            
     & DSGHSZSO( 15, 19) /  512/       ,!( 15) SOURCE15 = TABLE(  1)            
     & DSGHSZSO( 16, 19) /  512/       ,!( 16) SOURCE16 = TABLE(  1)            
     & DSGHSZSO( 17, 19) /  512/       ,!( 17) SOURCE17 = TABLE(  1)            
     & DSGHSZSO( 18, 19) /  512/       ,!( 18) SOURCE18 = TABLE(  1)            
     & DSGHSZSO( 19, 19) /  512/       ,!( 19) SOURCE19 = TABLE(  1)            
     & DSGHSZSO( 20, 19) /  512/       ,!( 20) SOURCE20 = TABLE(  1)            
     & DSGHSZSO( 21, 19) /  512/       ,!( 21) SOURCE21 = TABLE(  1)            
     & DSGHSZSO( 22, 19) /  512/       ,!( 22) SOURCE22 = TABLE(  1)            
     & DSGHSZSO( 23, 19) /  512/       ,!( 23) SOURCE23 = TABLE(  1)            
     & DSGHSZSO( 24, 19) /  512/       ,!( 24) SOURCE24 = TABLE(  1)            
     & DSGHSZSO( 25, 19) /  512/       ,!( 25) SOURCE25 = TABLE(  1)            
     & DSGHSZSO( 26, 19) /  512/       ,!( 26) SOURCE26 = TABLE(  1)            
     & DSGHSZSO( 27, 19) /  512/       ,!( 27) SOURCE27 = TABLE(  1)            
     & DSGHSZSO( 28, 19) /  512/       ,!( 28) SOURCE28 = TABLE(  1)            
     & DSGHSZSO( 29, 19) /  512/       ,!( 29) SOURCE29 = TABLE(  1)            
     & DSGHSZSO( 30, 19) /  512/       ,!( 30) SOURCE30 = TABLE(  1)            
     & DSGHSZSO( 31, 19) /  512/       ,!( 31) SOURCE31 = TABLE(  1)            
     & DSGHSZSO( 32, 19) /  512/       ,!( 32) SOURCE32 = TABLE(  1)            
     & DSGHSZSO( 33, 19) /  512/       ,!( 33) SOURCE33 = TABLE(  1)            
     & DSGHSZSO( 34, 19) /  512/       ,!( 34) SOURCE34 = TABLE(  1)            
     & DSGHSZSO( 35, 19) /  512/       ,!( 35) SOURCE35 = TABLE(  1)            
     & DSGHSZSO( 36, 19) /  512/       ,!( 36) SOURCE36 = TABLE(  1)            
     & DSGHSZSO( 37, 19) /  512/       ,!( 37) SOURCE37 = TABLE(  1)            
     & DSGHSZSO( 38, 19) /  512/       ,!( 38) SOURCE38 = TABLE(  1)            
     & DSGHSZSO( 39, 19) /  512/       ,!( 39) SOURCE39 = TABLE(  1)            
     & DSGHSZSO( 40, 19) /  512/       ,!( 40) SOURCE40 = TABLE(  1)            
     & DSGHSZSO( 41, 19) /  512/       ,!( 41) SOURCE41 = TABLE(  1)            
     & DSGHSZSO( 42, 19) /  512/       ,!( 42) SOURCE42 = TABLE(  1)            
     & DSGHSZSO( 43, 19) /  512/       ,!( 43) SOURCE43 = TABLE(  1)            
     & DSGHSZSO( 44, 19) /  512/       ,!( 44) SOURCE44 = TABLE(  1)            
     & DSGHSZSO( 45, 19) /  512/       ,!( 45) SOURCE45 = TABLE(  1)            
     & DSGHSZSO( 46, 19) /  512/       ,!( 46) SOURCE46 = TABLE(  1)            
     & DSGHSZSO( 47, 19) /  512/       ,!( 47) SOURCE47 = TABLE(  1)            
     & DSGHSZSO( 48, 19) /  512/       ,!( 48) SOURCE48 = TABLE(  1)            
     & DSGHSZSO( 49, 19) /  512/       ,!( 49) SOURCE49 = TABLE(  1)            
     & DSGHSZSO( 50, 19) /  512/       ,!( 50) SOURCE50 = TABLE(  1)            
     & DSGHSZSO( 51, 19) /  512/       ,!( 51) SOURCE51 = TABLE(  1)            
     & DSGHSZSO( 52, 19) /  512/       ,!( 52) SOURCE52 = TABLE(  1)            
     & DSGHSZSO( 53, 19) /  512/       ,!( 53) SOURCE53 = TABLE(  1)            
     & DSGHSZSO( 54, 19) /  512/       ,!( 54) SOURCE54 = TABLE(  1)            
     & DSGHSZSO( 55, 19) /  512/       ,!( 55) SOURCE55 = TABLE(  1)            
     & DSGHSZSO( 56, 19) /  512/       ,!( 56) SOURCE56 = TABLE(  1)            
     & DSGHSZSO( 57, 19) /  512/       ,!( 57) SOURCE57 = TABLE(  1)            
     & DSGHSZSO( 58, 19) /  512/       ,!( 58) SOURCE58 = TABLE(  1)            
     & DSGHSZSO( 59, 19) /  512/       ,!( 59) SOURCE59 = TABLE(  1)            
     & DSGHSZSO( 60, 19) /  512/       ,!( 60) SOURCE60 = TABLE(  1)            
     & DSGHSZSO( 61, 19) /  512/       ,!( 61) SOURCE61 = TABLE(  1)            
     & DSGHSZSO( 62, 19) /  512/       ,!( 62) SOURCE62 = TABLE(  1)            
     & DSGHSZSO( 63, 19) /  512/       ,!( 63) SOURCE63 = TABLE(  1)            
     & DSGHSZSO( 64, 19) /  512/       ,!( 64) SOURCE64 = TABLE(  1)            
     & DSGHSZSO( 65, 19) /  512/       ,!( 65) SOURCE65 = TABLE(  1)            
     & DSGHSZSO( 66, 19) /  512/       ,!( 66) SOURCE66 = TABLE(  1)            
     & DSGHSZSO( 67, 19) /  512/       ,!( 67) SOURCE67 = TABLE(  1)            
     & DSGHSZSO( 68, 19) /  512/       ,!( 68) SOURCE68 = TABLE(  1)            
     & DSGHSZSO( 69, 19) /  512/       ,!( 69) SOURCE69 = TABLE(  1)            
     & DSGHSZSO( 70, 19) /  512/       ,!( 70) SOURCE70 = TABLE(  1)            
     & DSGHSZSO( 71, 19) /  512/       ,!( 71) SOURCE71 = TABLE(  1)            
     & DSGHSZSO( 72, 19) /  512/       ,!( 72) SOURCE72 = TABLE(  1)            
     & DSGHSZSO( 73, 19) /  512/       ,!( 73) SOURCE73 = TABLE(  1)            
     & DSGHSZSO( 74, 19) /  512/       ,!( 74) SOURCE74 = TABLE(  1)            
     & DSGHSZSO( 75, 19) /  512/       ,!( 75) SOURCE75 = TABLE(  1)            
     & DSGHSZSO( 76, 19) /  512/       ,!( 76) SOURCE76 = TABLE(  1)            
     & DSGHSZSO( 77, 19) /  512/       ,!( 77) SOURCE77 = TABLE(  1)            
     & DSGHSZSO( 78, 19) /  512/       ,!( 78) SOURCE78 = TABLE(  1)            
     & DSGHSZSO( 79, 19) /  512/       ,!( 79) SOURCE79 = TABLE(  1)            
     & DSGHSZSO( 80, 19) /  512/       ,!( 80) SOURCE80 = TABLE(  1)            
     & DSGHSZSO( 81, 19) /    0/       ,!( 81)                                  
     & DSGHSZSO( 82, 19) /    0/       ,!( 82)                                  
     & DSGHSZSO( 83, 19) /    0/       ,!( 83)                                  
     & DSGHSZSO( 84, 19) /    0/       ,!( 84)                                  
     & DSGHSZSO( 85, 19) /    0/       ,!( 85)                                  
     & DSGHSZSO( 86, 19) /    0/       ,!( 86)                                  
     & DSGHSZSO( 87, 19) /    0/       ,!( 87)                                  
     & DSGHSZSO( 88, 19) /    0/       ,!( 88)                                  
     & DSGHSZSO( 89, 19) /    0/       ,!( 89)                                  
     & DSGHSZSO( 90, 19) /    0/       ,!( 90)                                  
     & DSGHSZSO( 91, 19) /    0/       ,!( 91)                                  
     & DSGHSZSO( 92, 19) /    0/       ,!( 92)                                  
     & DSGHSZSO( 93, 19) /    0/       ,!( 93)                                  
     & DSGHSZSO( 94, 19) /    0/       ,!( 94)                                  
     & DSGHSZSO( 95, 19) /    0/       ,!( 95)                                  
     & DSGHSZSO( 96, 19) /    0/        !( 96)                                  
C                                                                               
      DATA                                                                      
     & DSGHADSO(  1, 20) /X'A001' /    ,!(  1) SOURCE01                         
     & DSGHADSO(  2, 20) /X'A003' /    ,!(  2) SOURCE02                         
     & DSGHADSO(  3, 20) /X'A005' /    ,!(  3) SOURCE03                         
     & DSGHADSO(  4, 20) /X'A007' /    ,!(  4) SOURCE04                         
     & DSGHADSO(  5, 20) /X'A009' /    ,!(  5) SOURCE05                         
     & DSGHADSO(  6, 20) /X'A00B' /    ,!(  6) SOURCE06                         
     & DSGHADSO(  7, 20) /X'A00D' /    ,!(  7) SOURCE07                         
     & DSGHADSO(  8, 20) /X'A00F' /    ,!(  8) SOURCE08                         
     & DSGHADSO(  9, 20) /X'A011' /    ,!(  9) SOURCE09                         
     & DSGHADSO( 10, 20) /X'A013' /    ,!( 10) SOURCE10                         
     & DSGHADSO( 11, 20) /X'A015' /    ,!( 11) SOURCE11                         
     & DSGHADSO( 12, 20) /X'A017' /    ,!( 12) SOURCE12                         
     & DSGHADSO( 13, 20) /X'A019' /    ,!( 13) SOURCE13                         
     & DSGHADSO( 14, 20) /X'A01B' /    ,!( 14) SOURCE14                         
     & DSGHADSO( 15, 20) /X'A01D' /    ,!( 15) SOURCE15                         
     & DSGHADSO( 16, 20) /X'A01F' /    ,!( 16) SOURCE16                         
     & DSGHADSO( 17, 20) /X'A021' /    ,!( 17) SOURCE17                         
     & DSGHADSO( 18, 20) /X'A023' /    ,!( 18) SOURCE18                         
     & DSGHADSO( 19, 20) /X'A025' /    ,!( 19) SOURCE19                         
     & DSGHADSO( 20, 20) /X'A027' /    ,!( 20) SOURCE20                         
     & DSGHADSO( 21, 20) /X'A029' /    ,!( 21) SOURCE21                         
     & DSGHADSO( 22, 20) /X'A02B' /    ,!( 22) SOURCE22                         
     & DSGHADSO( 23, 20) /X'A02D' /    ,!( 23) SOURCE23                         
     & DSGHADSO( 24, 20) /X'A02F' /    ,!( 24) SOURCE24                         
     & DSGHADSO( 25, 20) /X'A031' /    ,!( 25) SOURCE25                         
     & DSGHADSO( 26, 20) /X'A033' /    ,!( 26) SOURCE26                         
     & DSGHADSO( 27, 20) /X'A035' /    ,!( 27) SOURCE27                         
     & DSGHADSO( 28, 20) /X'A037' /    ,!( 28) SOURCE28                         
     & DSGHADSO( 29, 20) /X'A039' /    ,!( 29) SOURCE29                         
     & DSGHADSO( 30, 20) /X'A03B' /    ,!( 30) SOURCE30                         
     & DSGHADSO( 31, 20) /X'A03D' /    ,!( 31) SOURCE31                         
     & DSGHADSO( 32, 20) /X'A03F' /    ,!( 32) SOURCE32                         
     & DSGHADSO( 33, 20) /X'A041' /    ,!( 33) SOURCE33                         
     & DSGHADSO( 34, 20) /X'A043' /    ,!( 34) SOURCE34                         
     & DSGHADSO( 35, 20) /X'A045' /    ,!( 35) SOURCE35                         
     & DSGHADSO( 36, 20) /X'A047' /    ,!( 36) SOURCE36                         
     & DSGHADSO( 37, 20) /X'A049' /    ,!( 37) SOURCE37                         
     & DSGHADSO( 38, 20) /X'A04B' /    ,!( 38) SOURCE38                         
     & DSGHADSO( 39, 20) /X'A04D' /    ,!( 39) SOURCE39                         
     & DSGHADSO( 40, 20) /X'A04F' /    ,!( 40) SOURCE40                         
     & DSGHADSO( 41, 20) /X'A051' /    ,!( 41) SOURCE41                         
     & DSGHADSO( 42, 20) /X'A053' /    ,!( 42) SOURCE42                         
     & DSGHADSO( 43, 20) /X'A055' /    ,!( 43) SOURCE43                         
     & DSGHADSO( 44, 20) /X'A057' /    ,!( 44) SOURCE44                         
     & DSGHADSO( 45, 20) /X'A059' /    ,!( 45) SOURCE45                         
     & DSGHADSO( 46, 20) /X'A05B' /    ,!( 46) SOURCE46                         
     & DSGHADSO( 47, 20) /X'A05D' /    ,!( 47) SOURCE47                         
     & DSGHADSO( 48, 20) /X'A05F' /    ,!( 48) SOURCE48                         
     & DSGHADSO( 49, 20) /X'A061' /    ,!( 49) SOURCE49                         
     & DSGHADSO( 50, 20) /X'A063' /    ,!( 50) SOURCE50                         
     & DSGHADSO( 51, 20) /X'A065' /    ,!( 51) SOURCE51                         
     & DSGHADSO( 52, 20) /X'A067' /    ,!( 52) SOURCE52                         
     & DSGHADSO( 53, 20) /X'A069' /    ,!( 53) SOURCE53                         
     & DSGHADSO( 54, 20) /X'A06B' /    ,!( 54) SOURCE54                         
     & DSGHADSO( 55, 20) /X'A06D' /    ,!( 55) SOURCE55                         
     & DSGHADSO( 56, 20) /X'A06F' /    ,!( 56) SOURCE56                         
     & DSGHADSO( 57, 20) /X'A071' /    ,!( 57) SOURCE57                         
     & DSGHADSO( 58, 20) /X'A073' /    ,!( 58) SOURCE58                         
     & DSGHADSO( 59, 20) /X'A075' /    ,!( 59) SOURCE59                         
     & DSGHADSO( 60, 20) /X'A077' /    ,!( 60) SOURCE60                         
     & DSGHADSO( 61, 20) /X'A079' /    ,!( 61) SOURCE61                         
     & DSGHADSO( 62, 20) /X'A07B' /    ,!( 62) SOURCE62                         
     & DSGHADSO( 63, 20) /X'A07D' /    ,!( 63) SOURCE63                         
     & DSGHADSO( 64, 20) /X'A07F' /    ,!( 64) SOURCE64                         
     & DSGHADSO( 65, 20) /X'A081' /    ,!( 65) SOURCE65                         
     & DSGHADSO( 66, 20) /X'A083' /    ,!( 66) SOURCE66                         
     & DSGHADSO( 67, 20) /X'A085' /    ,!( 67) SOURCE67                         
     & DSGHADSO( 68, 20) /X'A087' /    ,!( 68) SOURCE68                         
     & DSGHADSO( 69, 20) /X'A089' /    ,!( 69) SOURCE69                         
     & DSGHADSO( 70, 20) /X'A08B' /    ,!( 70) SOURCE70                         
     & DSGHADSO( 71, 20) /X'A08D' /    ,!( 71) SOURCE71                         
     & DSGHADSO( 72, 20) /X'A08F' /    ,!( 72) SOURCE72                         
     & DSGHADSO( 73, 20) /X'A091' /    ,!( 73) SOURCE73                         
     & DSGHADSO( 74, 20) /X'A093' /    ,!( 74) SOURCE74                         
     & DSGHADSO( 75, 20) /X'A095' /    ,!( 75) SOURCE75                         
     & DSGHADSO( 76, 20) /X'A097' /    ,!( 76) SOURCE76                         
     & DSGHADSO( 77, 20) /X'A099' /    ,!( 77) SOURCE77                         
     & DSGHADSO( 78, 20) /X'A09B' /    ,!( 78) SOURCE78                         
     & DSGHADSO( 79, 20) /X'A09D' /    ,!( 79) SOURCE79                         
     & DSGHADSO( 80, 20) /X'A09F' /    ,!( 80) SOURCE80                         
     & DSGHADSO( 81, 20) /X'0000' /    ,!( 81)                                  
     & DSGHADSO( 82, 20) /X'0000' /    ,!( 82)                                  
     & DSGHADSO( 83, 20) /X'0000' /    ,!( 83)                                  
     & DSGHADSO( 84, 20) /X'0000' /    ,!( 84)                                  
     & DSGHADSO( 85, 20) /X'0000' /    ,!( 85)                                  
     & DSGHADSO( 86, 20) /X'0000' /    ,!( 86)                                  
     & DSGHADSO( 87, 20) /X'0000' /    ,!( 87)                                  
     & DSGHADSO( 88, 20) /X'0000' /    ,!( 88)                                  
     & DSGHADSO( 89, 20) /X'0000' /    ,!( 89)                                  
     & DSGHADSO( 90, 20) /X'0000' /    ,!( 90)                                  
     & DSGHADSO( 91, 20) /X'0000' /    ,!( 91)                                  
     & DSGHADSO( 92, 20) /X'0000' /    ,!( 92)                                  
     & DSGHADSO( 93, 20) /X'0000' /    ,!( 93)                                  
     & DSGHADSO( 94, 20) /X'0000' /    ,!( 94)                                  
     & DSGHADSO( 95, 20) /X'0000' /    ,!( 95)                                  
     & DSGHADSO( 96, 20) /X'0000' /     !( 96)                                  
C                                                                               
      DATA                                                                      
     & DSGHPHSO(  1, 20) /X'B000' /    ,!(  1) SOURCE01                         
     & DSGHPHSO(  2, 20) /X'B002' /    ,!(  2) SOURCE02                         
     & DSGHPHSO(  3, 20) /X'B004' /    ,!(  3) SOURCE03                         
     & DSGHPHSO(  4, 20) /X'B006' /    ,!(  4) SOURCE04                         
     & DSGHPHSO(  5, 20) /X'B008' /    ,!(  5) SOURCE05                         
     & DSGHPHSO(  6, 20) /X'B00A' /    ,!(  6) SOURCE06                         
     & DSGHPHSO(  7, 20) /X'B00C' /    ,!(  7) SOURCE07                         
     & DSGHPHSO(  8, 20) /X'B00E' /    ,!(  8) SOURCE08                         
     & DSGHPHSO(  9, 20) /X'B010' /    ,!(  9) SOURCE09                         
     & DSGHPHSO( 10, 20) /X'B012' /    ,!( 10) SOURCE10                         
     & DSGHPHSO( 11, 20) /X'B014' /    ,!( 11) SOURCE11                         
     & DSGHPHSO( 12, 20) /X'B016' /    ,!( 12) SOURCE12                         
     & DSGHPHSO( 13, 20) /X'B018' /    ,!( 13) SOURCE13                         
     & DSGHPHSO( 14, 20) /X'B01A' /    ,!( 14) SOURCE14                         
     & DSGHPHSO( 15, 20) /X'B01C' /    ,!( 15) SOURCE15                         
     & DSGHPHSO( 16, 20) /X'B01E' /    ,!( 16) SOURCE16                         
     & DSGHPHSO( 17, 20) /X'B020' /    ,!( 17) SOURCE17                         
     & DSGHPHSO( 18, 20) /X'B022' /    ,!( 18) SOURCE18                         
     & DSGHPHSO( 19, 20) /X'B024' /    ,!( 19) SOURCE19                         
     & DSGHPHSO( 20, 20) /X'B026' /    ,!( 20) SOURCE20                         
     & DSGHPHSO( 21, 20) /X'B028' /    ,!( 21) SOURCE21                         
     & DSGHPHSO( 22, 20) /X'B02A' /    ,!( 22) SOURCE22                         
     & DSGHPHSO( 23, 20) /X'B02C' /    ,!( 23) SOURCE23                         
     & DSGHPHSO( 24, 20) /X'B02E' /    ,!( 24) SOURCE24                         
     & DSGHPHSO( 25, 20) /X'B030' /    ,!( 25) SOURCE25                         
     & DSGHPHSO( 26, 20) /X'B032' /    ,!( 26) SOURCE26                         
     & DSGHPHSO( 27, 20) /X'B034' /    ,!( 27) SOURCE27                         
     & DSGHPHSO( 28, 20) /X'B036' /    ,!( 28) SOURCE28                         
     & DSGHPHSO( 29, 20) /X'B038' /    ,!( 29) SOURCE29                         
     & DSGHPHSO( 30, 20) /X'B03A' /    ,!( 30) SOURCE30                         
     & DSGHPHSO( 31, 20) /X'B03C' /    ,!( 31) SOURCE31                         
     & DSGHPHSO( 32, 20) /X'B03E' /    ,!( 32) SOURCE32                         
     & DSGHPHSO( 33, 20) /X'B040' /    ,!( 33) SOURCE33                         
     & DSGHPHSO( 34, 20) /X'B042' /    ,!( 34) SOURCE34                         
     & DSGHPHSO( 35, 20) /X'B044' /    ,!( 35) SOURCE35                         
     & DSGHPHSO( 36, 20) /X'B046' /    ,!( 36) SOURCE36                         
     & DSGHPHSO( 37, 20) /X'B048' /    ,!( 37) SOURCE37                         
     & DSGHPHSO( 38, 20) /X'B04A' /    ,!( 38) SOURCE38                         
     & DSGHPHSO( 39, 20) /X'B04C' /    ,!( 39) SOURCE39                         
     & DSGHPHSO( 40, 20) /X'B04E' /    ,!( 40) SOURCE40                         
     & DSGHPHSO( 41, 20) /X'B050' /    ,!( 41) SOURCE41                         
     & DSGHPHSO( 42, 20) /X'B052' /    ,!( 42) SOURCE42                         
     & DSGHPHSO( 43, 20) /X'B054' /    ,!( 43) SOURCE43                         
     & DSGHPHSO( 44, 20) /X'B056' /    ,!( 44) SOURCE44                         
     & DSGHPHSO( 45, 20) /X'B058' /    ,!( 45) SOURCE45                         
     & DSGHPHSO( 46, 20) /X'B05A' /    ,!( 46) SOURCE46                         
     & DSGHPHSO( 47, 20) /X'B05C' /    ,!( 47) SOURCE47                         
     & DSGHPHSO( 48, 20) /X'B05E' /    ,!( 48) SOURCE48                         
     & DSGHPHSO( 49, 20) /X'B060' /    ,!( 49) SOURCE49                         
     & DSGHPHSO( 50, 20) /X'B062' /    ,!( 50) SOURCE50                         
     & DSGHPHSO( 51, 20) /X'B064' /    ,!( 51) SOURCE51                         
     & DSGHPHSO( 52, 20) /X'B066' /    ,!( 52) SOURCE52                         
     & DSGHPHSO( 53, 20) /X'B068' /    ,!( 53) SOURCE53                         
     & DSGHPHSO( 54, 20) /X'B06A' /    ,!( 54) SOURCE54                         
     & DSGHPHSO( 55, 20) /X'B06C' /    ,!( 55) SOURCE55                         
     & DSGHPHSO( 56, 20) /X'B06E' /    ,!( 56) SOURCE56                         
     & DSGHPHSO( 57, 20) /X'B070' /    ,!( 57) SOURCE57                         
     & DSGHPHSO( 58, 20) /X'B072' /    ,!( 58) SOURCE58                         
     & DSGHPHSO( 59, 20) /X'B074' /    ,!( 59) SOURCE59                         
     & DSGHPHSO( 60, 20) /X'B076' /    ,!( 60) SOURCE60                         
     & DSGHPHSO( 61, 20) /X'B078' /    ,!( 61) SOURCE61                         
     & DSGHPHSO( 62, 20) /X'B07A' /    ,!( 62) SOURCE62                         
     & DSGHPHSO( 63, 20) /X'B07C' /    ,!( 63) SOURCE63                         
     & DSGHPHSO( 64, 20) /X'B07E' /    ,!( 64) SOURCE64                         
     & DSGHPHSO( 65, 20) /X'B080' /    ,!( 65) SOURCE65                         
     & DSGHPHSO( 66, 20) /X'B082' /    ,!( 66) SOURCE66                         
     & DSGHPHSO( 67, 20) /X'B084' /    ,!( 67) SOURCE67                         
     & DSGHPHSO( 68, 20) /X'B086' /    ,!( 68) SOURCE68                         
     & DSGHPHSO( 69, 20) /X'B088' /    ,!( 69) SOURCE69                         
     & DSGHPHSO( 70, 20) /X'B08A' /    ,!( 70) SOURCE70                         
     & DSGHPHSO( 71, 20) /X'B08C' /    ,!( 71) SOURCE71                         
     & DSGHPHSO( 72, 20) /X'B08E' /    ,!( 72) SOURCE72                         
     & DSGHPHSO( 73, 20) /X'B090' /    ,!( 73) SOURCE73                         
     & DSGHPHSO( 74, 20) /X'B092' /    ,!( 74) SOURCE74                         
     & DSGHPHSO( 75, 20) /X'B094' /    ,!( 75) SOURCE75                         
     & DSGHPHSO( 76, 20) /X'B096' /    ,!( 76) SOURCE76                         
     & DSGHPHSO( 77, 20) /X'B098' /    ,!( 77) SOURCE77                         
     & DSGHPHSO( 78, 20) /X'B09A' /    ,!( 78) SOURCE78                         
     & DSGHPHSO( 79, 20) /X'B09C' /    ,!( 79) SOURCE79                         
     & DSGHPHSO( 80, 20) /X'B09E' /    ,!( 80) SOURCE80                         
     & DSGHPHSO( 81, 20) /X'0000' /    ,!( 81)                                  
     & DSGHPHSO( 82, 20) /X'0000' /    ,!( 82)                                  
     & DSGHPHSO( 83, 20) /X'0000' /    ,!( 83)                                  
     & DSGHPHSO( 84, 20) /X'0000' /    ,!( 84)                                  
     & DSGHPHSO( 85, 20) /X'0000' /    ,!( 85)                                  
     & DSGHPHSO( 86, 20) /X'0000' /    ,!( 86)                                  
     & DSGHPHSO( 87, 20) /X'0000' /    ,!( 87)                                  
     & DSGHPHSO( 88, 20) /X'0000' /    ,!( 88)                                  
     & DSGHPHSO( 89, 20) /X'0000' /    ,!( 89)                                  
     & DSGHPHSO( 90, 20) /X'0000' /    ,!( 90)                                  
     & DSGHPHSO( 91, 20) /X'0000' /    ,!( 91)                                  
     & DSGHPHSO( 92, 20) /X'0000' /    ,!( 92)                                  
     & DSGHPHSO( 93, 20) /X'0000' /    ,!( 93)                                  
     & DSGHPHSO( 94, 20) /X'0000' /    ,!( 94)                                  
     & DSGHPHSO( 95, 20) /X'0000' /    ,!( 95)                                  
     & DSGHPHSO( 96, 20) /X'0000' /     !( 96)                                  
C                                                                               
      DATA                                                                      
     & DSGHPOTA(  1, 20) /X'0001' /    ,!(  1) TABLE(  1)                       
     & DSGHPOTA(  2, 20) /X'0003' /    ,!(  2) TABLE(  2)                       
     & DSGHPOTA(  3, 20) /X'0005' /    ,!(  3) TABLE(  3)                       
     & DSGHPOTA(  4, 20) /X'0007' /    ,!(  4) TABLE(  4)                       
     & DSGHPOTA(  5, 20) /X'0009' /    ,!(  5) TABLE(  5)                       
     & DSGHPOTA(  6, 20) /X'000B' /    ,!(  6) TABLE(  6)                       
     & DSGHPOTA(  7, 20) /X'000D' /    ,!(  7) TABLE(  7)                       
     & DSGHPOTA(  8, 20) /X'000F' /    ,!(  8) TABLE(  8)                       
     & DSGHPOTA(  9, 20) /X'0011' /    ,!(  9) TABLE(  9)                       
     & DSGHPOTA( 10, 20) /X'0013' /    ,!( 10) TABLE( 10)                       
     & DSGHPOTA( 11, 20) /X'0015' /    ,!( 11) TABLE( 11)                       
     & DSGHPOTA( 12, 20) /X'0017' /    ,!( 12) TABLE( 12)                       
     & DSGHPOTA( 13, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 14, 20) /X'0019' /    ,!( 13) TABLE( 14)                       
     & DSGHPOTA( 15, 20) /X'001B' /    ,!( 14) TABLE( 15)                       
     & DSGHPOTA( 16, 20) /X'001D' /    ,!( 15) TABLE( 16)                       
     & DSGHPOTA( 17, 20) /X'001F' /    ,!( 16) TABLE( 17)                       
     & DSGHPOTA( 18, 20) /X'0021' /    ,!( 17) TABLE( 18)                       
     & DSGHPOTA( 19, 20) /X'0023' /    ,!( 18) TABLE( 19)                       
     & DSGHPOTA( 20, 20) /X'0025' /    ,!( 19) TABLE( 20)                       
     & DSGHPOTA( 21, 20) /X'0027' /    ,!( 20) TABLE( 21)                       
     & DSGHPOTA( 22, 20) /X'0029' /    ,!( 21) TABLE( 22)                       
     & DSGHPOTA( 23, 20) /X'002B' /    ,!( 22) TABLE( 23)                       
     & DSGHPOTA( 24, 20) /X'002D' /    ,!( 23) TABLE( 24)                       
     & DSGHPOTA( 25, 20) /X'002F' /    ,!( 24) TABLE( 25)                       
     & DSGHPOTA( 26, 20) /X'0031' /    ,!( 25) TABLE( 26)                       
     & DSGHPOTA( 27, 20) /X'0033' /    ,!( 26) TABLE( 27)                       
     & DSGHPOTA( 28, 20) /X'0035' /    ,!( 27) TABLE( 28)                       
     & DSGHPOTA( 29, 20) /X'0037' /    ,!( 28) TABLE( 29)                       
     & DSGHPOTA( 30, 20) /X'0039' /    ,!( 29) TABLE( 30)                       
     & DSGHPOTA( 31, 20) /X'003B' /    ,!( 30) TABLE( 31)                       
     & DSGHPOTA( 32, 20) /X'003D' /    ,!( 31) TABLE( 32)                       
     & DSGHPOTA( 33, 20) /X'003F' /    ,!( 32) TABLE( 33)                       
     & DSGHPOTA( 34, 20) /X'0041' /    ,!( 33) TABLE( 34)                       
     & DSGHPOTA( 35, 20) /X'0043' /    ,!( 34) TABLE( 35)                       
     & DSGHPOTA( 36, 20) /X'0045' /    ,!( 35) TABLE( 36)                       
     & DSGHPOTA( 37, 20) /X'0047' /    ,!( 36) TABLE( 37)                       
     & DSGHPOTA( 38, 20) /X'0049' /    ,!( 37) TABLE( 38)                       
     & DSGHPOTA( 39, 20) /X'004B' /    ,!( 38) TABLE( 39)                       
     & DSGHPOTA( 40, 20) /X'004D' /    ,!( 39) TABLE( 40)                       
     & DSGHPOTA( 41, 20) /X'004F' /    ,!( 40) TABLE( 41)                       
     & DSGHPOTA( 42, 20) /X'0051' /    ,!( 41) TABLE( 42)                       
     & DSGHPOTA( 43, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 44, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 45, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 46, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 47, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 48, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 49, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 50, 20) /X'0053' /    ,!( 42) TABLE( 50)                       
     & DSGHPOTA( 51, 20) /X'0055' /    ,!( 43) TABLE( 51)                       
     & DSGHPOTA( 52, 20) /X'0057' /    ,!( 44) TABLE( 52)                       
     & DSGHPOTA( 53, 20) /X'0059' /    ,!( 45) TABLE( 53)                       
     & DSGHPOTA( 54, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 55, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 56, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 57, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 58, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 59, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 60, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 61, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 62, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 63, 20) /X'0000' /    ,!Table does not exist                   
     & DSGHPOTA( 64, 20) /X'0000' /     !Table does not exist                   
C                                                                               
      DATA                                                                      
     & DSGHSZSO(  1, 20) /  512/       ,!(  1) SOURCE01 = TABLE(  1)            
     & DSGHSZSO(  2, 20) /  512/       ,!(  2) SOURCE02 = TABLE( 12)            
     & DSGHSZSO(  3, 20) /  512/       ,!(  3) SOURCE03 = TABLE( 11)            
     & DSGHSZSO(  4, 20) /  512/       ,!(  4) SOURCE04 = TABLE(  1)            
     & DSGHSZSO(  5, 20) /  512/       ,!(  5) SOURCE05 = TABLE(  1)            
     & DSGHSZSO(  6, 20) /  512/       ,!(  6) SOURCE06 = TABLE( 14)            
     & DSGHSZSO(  7, 20) /  512/       ,!(  7) SOURCE07 = TABLE( 15)            
     & DSGHSZSO(  8, 20) /  512/       ,!(  8) SOURCE08 = TABLE(  1)            
     & DSGHSZSO(  9, 20) /  512/       ,!(  9) SOURCE09 = TABLE(  1)            
     & DSGHSZSO( 10, 20) /  512/       ,!( 10) SOURCE10 = TABLE(  1)            
     & DSGHSZSO( 11, 20) /  512/       ,!( 11) SOURCE11 = TABLE(  1)            
     & DSGHSZSO( 12, 20) /  512/       ,!( 12) SOURCE12 = TABLE(  1)            
     & DSGHSZSO( 13, 20) /  512/       ,!( 13) SOURCE13 = TABLE( 12)            
     & DSGHSZSO( 14, 20) /  512/       ,!( 14) SOURCE14 = TABLE( 11)            
     & DSGHSZSO( 15, 20) /  512/       ,!( 15) SOURCE15 = TABLE(  1)            
     & DSGHSZSO( 16, 20) /  512/       ,!( 16) SOURCE16 = TABLE(  1)            
     & DSGHSZSO( 17, 20) /  512/       ,!( 17) SOURCE17 = TABLE( 14)            
     & DSGHSZSO( 18, 20) /  512/       ,!( 18) SOURCE18 = TABLE( 15)            
     & DSGHSZSO( 19, 20) /  512/       ,!( 19) SOURCE19 = TABLE(  1)            
     & DSGHSZSO( 20, 20) /  512/       ,!( 20) SOURCE20 = TABLE(  1)            
     & DSGHSZSO( 21, 20) /  512/       ,!( 21) SOURCE21 = TABLE(  1)            
     & DSGHSZSO( 22, 20) /  512/       ,!( 22) SOURCE22 = TABLE(  1)            
     & DSGHSZSO( 23, 20) /  512/       ,!( 23) SOURCE23 = TABLE(  1)            
     & DSGHSZSO( 24, 20) /  512/       ,!( 24) SOURCE24 = TABLE(  1)            
     & DSGHSZSO( 25, 20) /  512/       ,!( 25) SOURCE25 = TABLE(  1)            
     & DSGHSZSO( 26, 20) /  512/       ,!( 26) SOURCE26 = TABLE(  1)            
     & DSGHSZSO( 27, 20) /  512/       ,!( 27) SOURCE27 = TABLE(  1)            
     & DSGHSZSO( 28, 20) /  512/       ,!( 28) SOURCE28 = TABLE(  1)            
     & DSGHSZSO( 29, 20) /  512/       ,!( 29) SOURCE29 = TABLE(  1)            
     & DSGHSZSO( 30, 20) /  512/       ,!( 30) SOURCE30 = TABLE(  1)            
     & DSGHSZSO( 31, 20) /  512/       ,!( 31) SOURCE31 = TABLE(  1)            
     & DSGHSZSO( 32, 20) /  512/       ,!( 32) SOURCE32 = TABLE(  1)            
     & DSGHSZSO( 33, 20) /  512/       ,!( 33) SOURCE33 = TABLE(  1)            
     & DSGHSZSO( 34, 20) /  512/       ,!( 34) SOURCE34 = TABLE(  1)            
     & DSGHSZSO( 35, 20) /  512/       ,!( 35) SOURCE35 = TABLE(  1)            
     & DSGHSZSO( 36, 20) /  512/       ,!( 36) SOURCE36 = TABLE(  1)            
     & DSGHSZSO( 37, 20) /  512/       ,!( 37) SOURCE37 = TABLE(  1)            
     & DSGHSZSO( 38, 20) /  512/       ,!( 38) SOURCE38 = TABLE(  1)            
     & DSGHSZSO( 39, 20) /  512/       ,!( 39) SOURCE39 = TABLE(  1)            
     & DSGHSZSO( 40, 20) /  512/       ,!( 40) SOURCE40 = TABLE(  1)            
     & DSGHSZSO( 41, 20) /  512/       ,!( 41) SOURCE41 = TABLE(  1)            
     & DSGHSZSO( 42, 20) /  512/       ,!( 42) SOURCE42 = TABLE(  1)            
     & DSGHSZSO( 43, 20) /  512/       ,!( 43) SOURCE43 = TABLE(  1)            
     & DSGHSZSO( 44, 20) /  512/       ,!( 44) SOURCE44 = TABLE(  1)            
     & DSGHSZSO( 45, 20) /  512/       ,!( 45) SOURCE45 = TABLE(  1)            
     & DSGHSZSO( 46, 20) /  512/       ,!( 46) SOURCE46 = TABLE(  1)            
     & DSGHSZSO( 47, 20) /  512/       ,!( 47) SOURCE47 = TABLE(  1)            
     & DSGHSZSO( 48, 20) /  512/       ,!( 48) SOURCE48 = TABLE(  1)            
     & DSGHSZSO( 49, 20) /  512/       ,!( 49) SOURCE49 = TABLE( 38)            
     & DSGHSZSO( 50, 20) /  512/       ,!( 50) SOURCE50 = TABLE(  1)            
     & DSGHSZSO( 51, 20) /  512/       ,!( 51) SOURCE51 = TABLE( 38)            
     & DSGHSZSO( 52, 20) /  512/       ,!( 52) SOURCE52 = TABLE(  1)            
     & DSGHSZSO( 53, 20) /  512/       ,!( 53) SOURCE53 = TABLE( 37)            
     & DSGHSZSO( 54, 20) /  512/       ,!( 54) SOURCE54 = TABLE(  1)            
     & DSGHSZSO( 55, 20) /  512/       ,!( 55) SOURCE55 = TABLE( 32)            
     & DSGHSZSO( 56, 20) /  512/       ,!( 56) SOURCE56 = TABLE( 33)            
     & DSGHSZSO( 57, 20) /  512/       ,!( 57) SOURCE57 = TABLE( 39)            
     & DSGHSZSO( 58, 20) /  512/       ,!( 58) SOURCE58 = TABLE(  1)            
     & DSGHSZSO( 59, 20) /  512/       ,!( 59) SOURCE59 = TABLE(  1)            
     & DSGHSZSO( 60, 20) /  512/       ,!( 60) SOURCE60 = TABLE(  1)            
     & DSGHSZSO( 61, 20) /  512/       ,!( 61) SOURCE61 = TABLE(  1)            
     & DSGHSZSO( 62, 20) /  512/       ,!( 62) SOURCE62 = TABLE(  1)            
     & DSGHSZSO( 63, 20) /  512/       ,!( 63) SOURCE63 = TABLE( 41)            
     & DSGHSZSO( 64, 20) /  512/       ,!( 64) SOURCE64 = TABLE(  1)            
     & DSGHSZSO( 65, 20) /  512/       ,!( 65) SOURCE65 = TABLE( 36)            
     & DSGHSZSO( 66, 20) /  512/       ,!( 66) SOURCE66 = TABLE(  1)            
     & DSGHSZSO( 67, 20) /  512/       ,!( 67) SOURCE67 = TABLE( 40)            
     & DSGHSZSO( 68, 20) /  512/       ,!( 68) SOURCE68 = TABLE(  1)            
     & DSGHSZSO( 69, 20) /  512/       ,!( 69) SOURCE69 = TABLE( 42)            
     & DSGHSZSO( 70, 20) /  512/       ,!( 70) SOURCE70 = TABLE(  1)            
     & DSGHSZSO( 71, 20) /  512/       ,!( 71) SOURCE71 = TABLE( 40)            
     & DSGHSZSO( 72, 20) /  512/       ,!( 72) SOURCE72 = TABLE(  1)            
     & DSGHSZSO( 73, 20) /  512/       ,!( 73) SOURCE73 = TABLE(  1)            
     & DSGHSZSO( 74, 20) /  512/       ,!( 74) SOURCE74 = TABLE(  1)            
     & DSGHSZSO( 75, 20) /  512/       ,!( 75) SOURCE75 = TABLE(  1)            
     & DSGHSZSO( 76, 20) /  512/       ,!( 76) SOURCE76 = TABLE(  1)            
     & DSGHSZSO( 77, 20) /  512/       ,!( 77) SOURCE77 = TABLE(  1)            
     & DSGHSZSO( 78, 20) /  512/       ,!( 78) SOURCE78 = TABLE(  1)            
     & DSGHSZSO( 79, 20) /  512/       ,!( 79) SOURCE79 = TABLE(  1)            
     & DSGHSZSO( 80, 20) /  512/       ,!( 80) SOURCE80 = TABLE(  1)            
     & DSGHSZSO( 81, 20) /    0/       ,!( 81)                                  
     & DSGHSZSO( 82, 20) /    0/       ,!( 82)                                  
     & DSGHSZSO( 83, 20) /    0/       ,!( 83)                                  
     & DSGHSZSO( 84, 20) /    0/       ,!( 84)                                  
     & DSGHSZSO( 85, 20) /    0/       ,!( 85)                                  
     & DSGHSZSO( 86, 20) /    0/       ,!( 86)                                  
     & DSGHSZSO( 87, 20) /    0/       ,!( 87)                                  
     & DSGHSZSO( 88, 20) /    0/       ,!( 88)                                  
     & DSGHSZSO( 89, 20) /    0/       ,!( 89)                                  
     & DSGHSZSO( 90, 20) /    0/       ,!( 90)                                  
     & DSGHSZSO( 91, 20) /    0/       ,!( 91)                                  
     & DSGHSZSO( 92, 20) /    0/       ,!( 92)                                  
     & DSGHSZSO( 93, 20) /    0/       ,!( 93)                                  
     & DSGHSZSO( 94, 20) /    0/       ,!( 94)                                  
     & DSGHSZSO( 95, 20) /    0/       ,!( 95)                                  
     & DSGHSZSO( 96, 20) /    0/        !( 96)                                  
