C'Title              TRIM PROGRAM
C'Module_ID          USD8VTR
C'Entry_point        TRIM
C'Documentation      N/A
C'Application        Trims A/C for ATG and repositions
C'Author             Department 24
C'Engineer           <PERSON>
C'Date               August 15, 1990
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C     [ 1]    CAE Software Development Standard, CD130931.01.8.300,
C             Rev A, 18 June 1984, CAE.
C
C'
C'Revision_history
C
C  usd8vtr.for.29  7Dec1992 23:13 usd8 BCA
C       < Declared local label >
C
C  usd8vtr.for.28  7Dec1992 23:11 usd8 BCA
C       < Split up local real declaration block >
C
C  usd8vtr.for.27  7Dec1992 23:08 usd8 BCA
C       < Added YITAIL logic for -300 series dependent code >
C
C  usd8vtr.for.26 30Apr1992 07:46 usd8 pve
C       < check zero force trim has reached tolerance >
C
C  usd8vtr.for.25 11Apr1992 00:22 usd8 PVE
C       < PUT IN FINAL CONVENTION FOR FORCE FREE TRIMMING >
C
C  usd8vtr.for.24  3Apr1992 23:31 usd8 PLam
C       < Changed C/F pitch trim label >
C
C  usd8vtr.for.23 11Mar1992 22:11 usd8 steve
C       < added lrfree back in >
C
C  usd8vtr.for.22  9Mar1992 22:47 usd8 pve
C       < nolonger wait for stab to reach backdriven position >
C
C  usd8vtr.for.21  9Mar1992 19:13 usd8 PVE
C       < SOFT CODE SURFACE BACKDRIVE TOLERANCES TO SATISFY TRIM CONDITION >
C
C  usd8vtr.for.20  5Mar1992 18:33 usd8 PVE
C       < LOWER WHEEL TRIM GAIN AND DISABLE RUDDER ZERO FORCE TRIM >
C
C  usd8vtr.for.19 26Feb1992 04:46 usd8 steve
C       < set pitch trim neg limit to -4 (ted) >
C
C  usd8vtr.for.18 25Feb1992 05:29 usd8 steve
C       < changed TABLIMN and P to variables from parameters >
C
C  usd8vtr.for.17  5Feb1992 19:13 usd8 PLam
C       < Put in aileron and rudder zero force trim backdriven logic >
C
C  usd8vtr.for.16 27Jan1992 17:17 usd8 paulv
C       < position limit hcetrim during zero force backdrive >
C
C  usd8vtr.for.15 27Jan1992 15:37 usd8 paulv
C       < remove compile errors >
C
C  usd8vtr.for.14 27Jan1992 15:25 usd8 PAULV
C       < ADD ZERO FORCE BACKDRIVE CODE >
C
C  usd8vtr.for.13 22Jan1992 19:57 usd8 paulv
C       < change sign on pitch trim trimming >
C
C  usd8vtr.for.12 22Jan1992 18:13 usd8 PLam
C       < Added trim tab backdrive logic >
C
C  usd8vtr.for.11 17Jan1992 15:59 usd8 PAULV
C       < REPLACE EFN BY EFNT >
C
C  usd8vtr.for.10 17Jan1992 15:57 usd8 paulv
C       < correct average aileron calculation >
C
C  usd8vtr.for.9 20Dec1991 16:03 usd8 PAULV
C       < ADD LABEL TO CP BLOCK >
C
C  usd8vtr.for.8 20Dec1991 15:55 usd8 paulv
C       < add ident label >
C
C  usd8vtr.for.7 20Dec1991 15:52 usd8 paulv
C       < update trim program to latest stf module >
C
C File: /cae1/ship/usd8vtr.for.6
C       Modified by: Gordon C
C       Tue Oct 22 12:03:55 1991
C       < Getting rid of code non FPC code >
C
      SUBROUTINE USD8VTR
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/20/91 - 15:58 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vtr.for.29  7Dec1992 23:13 usd8 BCA    $'/
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
C     Inputs
C
CQ    USD8 XRFTEST(*)
CP    USD8
CPI  C  CAILL,     CAILR,     CELVL,     CELVR,     CIAFCFOR,  CIRCFOR,
CPI  C  CIETSPOS,  CRUD,      CETRIM,
CPI  E  EFNT,
CPI  H  HCATMODE,  HCETMODE,  HCRTMODE,
CPI  H  HBYGAIN,   HCAMODE,   HCEMODE,   HCHMODE,   HCRMODE,   HCWTRM,
CPI  H  HECMDE,    HECMDG,    HECMDL,    HELVE,     HELVL,     HEMODE,
CPI  H  HPACC,     HPRAT,     HPRATS,    HQACC,     HQRAT,     HQRATS,
CPI  H  HRACC,     HRRAT,     HRRATS,    HRUDE,     HRUDL,     HSTABE,
CPI  H  HSTABL,    HTDP,      HTDR,      HTDW,      HTGH,      HTGP,
CPI  H  HTGP1,     HTGQ,      HTGR,      HTGR1,     HTGU,      HTGU1,
CPI  H  HTGV1,     HTGV2,     HTLH,      HTLP1,     HTLQ,      HTLR1,
CPI  H  HTLU,      HTLU1,     HTLV,      HTLW,      HWACC,     HWHEELE,
CPI  H  HWHEELL,
CPI  R  RUFLT,     RUREPGPA,
CPI  V  VACC1,     VACC2,     VACCEL,    VBANKSET,  VBETA,     VBOG,
CPI  V  VCSPHI,    VCSTHE,    VDRSET,    VEFD,      VENGOUT,   VENGSET,
CPI  V  VFETRIM,   VHH,       VHIAS,     VHMACH,    VIASS,     VIASSET,
CPI  V  VKINT,     VLXB0,     VLYB0,     VLZB0,     VMSET,     VNXB,
CPI  V  VNYB,      VNZB,      VNZL,      VNZLCMD,   VPD,       VPRESS,
CPI  V  VQD,       VQPRS,     VRD,       VSITE,     VSNPHI,    VSNTHE,
CPI  V  VSTABSET,  VTEMPK,    VTNZL,     VUGD,      VUW,       VVGD,
CPI  V  VVT1INV,   VVW,       VVWEW,     VVWNS,     VWGD,      VWHLSET,
CPI  V  VWW,       VXWINDR,   VYACCEL,   VDUMMYR,   VDUMMYL,   VAYB,
CPI  V  VFSTICK,   VZD,
CPI  Y  YITAIL,
C
C  OUTPUTS
C
CPO  H  HAIL,      HCOL,      HECMD,     HECMDGL,   HECMDO,    HELV,
CPO  H  HELVO,     HPEDAL,    HRUD,      HRUDO,     HSTAB,     HSTABO,
CPO  H  HTGV,      HTGW,      HUACC,     HVACC,     HWHEEL,    HWHEELO,
CPO  H  HCETRIM,   HCATRIM,   HCRTRIM,
CPO  T  TCFFLPOS,
CPO  V  VBETASET,  VETRIM,    VMS,       VNINIT,    VNZLSET,   VP,
CPO  V  VPHI,      VPSI0,     VQ,        VR,        VTHETA,    VTRIM,
CPO  V  VUG,       VVG,       VVT,       VWG,       VZDS
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:09:03 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  CAILL          ! LEFT AILERON POSITION
     &, CAILR          ! RIGHT AILERON POSITION
     &, CELVL          ! LEFT ELEVATOR POSITION
     &, CELVR          ! RIGHT ELEVATOR POSITION
     &, CETRIM         ! Elevator Trim Tab position (NRC)
     &, CIAFCFOR       ! F/O WHEEL CABLE FORCE                  [LBS]
     &, CIETSPOS       ! PITCH TRIM TAB SURFACE POSITION        [DEG]
     &, CIRCFOR        ! RUDDER PEDAL CABLE FORCE               [LBS]
     &, CRUD           ! RUDDER ANGLE  + TE LEFT                [DEG]
     &, EFNT(2)        ! TOTAL ENGINE NET THRUST                [LBS]
     &, HECMDE(4)      ! EXPECTED NET THRUST                    [lbs]
     &, HECMDG(4)      ! COMMANDED GROSS THRUST                 [lbs]
     &, HELVE          ! EXPECTED ELEVATOR                      [deg]
     &, HPACC          ! DEMANDED ROLL ACCEL FOR TRIM
     &, HPRAT          ! DEMANDED ROLL RATE FOR TRIM
     &, HQACC          ! DEMANDED PITCH ACCEL FOR TRIM
     &, HQRAT          ! DEMANDED PITCH RATE FOR TRIM
     &, HRACC          ! DEMANDED YAW ACCEL FOR TRIM
     &, HRRAT          ! DEMANDED YAW RATE FOR TRIM
     &, HRUDE          ! EXPECTED RUDDER                        [deg]
     &, HSTABE         ! EXPECTED STABILIZER                    [deg]
     &, HTDP           ! TRIM DAMPING GAIN ON VPD
     &, HTDR           ! TRIM DAMPING GAIN ON VRD
     &, HTDW           ! TRIM DAMPING GAIN ON VWGD
     &, HTGH           ! TRIM GAIN HDG  TO GET BETA
     &, HTGP           ! TRIM GAIN VPD  USE WHEEL
     &, HTGP1          ! TRIM GAIN VPD  WHEEL FIXED
     &, HTGQ           ! TRIM GAIN VQD  ELV OR STAB
     &, HTGR           ! TRIM GAIN VRD  RUDDER FREE
     &, HTGR1          ! TRIM GAIN VRD  RUDD FIXED
     &, HTGU           ! TRIM GAIN VUGD USE THRUST
      REAL*4   
     &  HTGU1          ! TRIM GAIN VUGD USE ROC
     &, HTGV1          ! TRIM GAIN VVGD BANK FREE
     &, HTGV2          ! TRIM GAIN TO GET VNZLS USING BANK
     &, HTLH           ! TRIM LIMIT HDG  TO GET BETA
     &, HTLP1          ! TRIM LIMIT VPD  WHEEL FIXED
     &, HTLQ           ! TRIM LIMIT VQD  ELV OR STAB
     &, HTLR1          ! TRIM LIMIT VRD  RUDD FIXED
     &, HTLU           ! TRIM LIMIT VUGD USE THRUST
     &, HTLU1          ! TRIM LIMIT VUGD USE ROC
     &, HTLV           ! TRIM LIMIT VVGD BANK FREE
     &, HTLW           ! TRIM LIMIT VWGD USE VTHETA
     &, HWACC          ! DEMANDED Z-ACCEL FOR TRIM
     &, HWHEELE        ! EXPECTED WHEEL                         [deg]
     &, RUREPGPA       ! 53 G/P ANGLE (DEGREES)                [DEG]
     &, VACC1          ! TRIM TOLERANCE FOR LINEAR ACCELERATIONS
     &, VACC2          ! TRIM TOLERANCE FOR ANGULAR ACCELERATIONS
     &, VAYB           ! BODY AXES TOTAL Y ACC.             [ft/s**2]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VDUMMYR(30)    ! REAL SPARES
     &, VEFD(2)        ! RAM DRAG                               [lbs]
     &, VFETRIM        ! NEUTRAL TRIM POSITION F(DE)            [deg]
     &, VFSTICK        ! STICK FORCE                            [lbs]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VIASS          ! EQUIV. AIRSP. FOR TRIM                 [kts]
     &, VKINT          ! INTEGRATION CONSTANT
     &, VLXB0          ! VLXB AT VPSI=0
     &, VLYB0          ! VLYB AT VPSI=0
     &, VLZB0          ! VLZB AT VPSI=0
     &, VNXB           ! DC A/C X WITH EARTH Z AXIS
      REAL*4   
     &  VNYB           ! DC A/C Y WITH EARTH Z AXIS
     &, VNZB           ! DC A/C Z WITH EARTH Z AXIS
     &, VNZL           ! BODY AXES NORMAL LOAD FACTOR             [G]
     &, VNZLCMD        ! COMMANDED LOAD FACTOR
     &, VPD            ! BODY AXES ROLL ACCELERATION       [rad/s**2]
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, VQD            ! BODY AXES PITCH ACCELERATION      [rad/s**2]
     &, VQPRS          ! DYNAMIC PRESSURE * WING AREA           [lbs]
     &, VRD            ! A/C YAW ACC. - BODY AXES          [rad/s**2]
     &, VSNPHI         ! SINE OF VPHI
     &, VSNTHE         ! SINE OF VTHETA
     &, VTEMPK         ! AMBIENT TEMPERATURE AT A/C           [deg K]
     &, VUGD           ! BODY AXES VAXB + GRAVITY           [ft/s**2]
     &, VUW            ! X WIND VELOCITY (BODY AXES)           [ft/s]
     &, VVGD           ! BODY AXES VAYB + GRAVITY           [ft/s**2]
     &, VVT1INV        ! INVERSE OF VVT1                       [s/ft]
     &, VVW            ! Y WIND VELOCITY (BODY AXES)           [ft/s]
     &, VVWEW          ! EAST/WEST WIND VELOCITY               [ft/s]
     &, VVWNS          ! NORTH/SOUTH WIND VELOCITY             [ft/s]
     &, VWGD           ! BODY AXES VAZB + GRAVITY           [ft/s**2]
     &, VWW            ! Z WIND VELOCITY (BODY AXES)           [ft/s]
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
C$
      INTEGER*4
     &  HCAMODE        ! AILERON    BACKDRIVE MODE
     &, HCATMODE       ! AILERON  TRIM BACKDRIVE MODE
     &, HCEMODE        ! ELEVATOR   BACKDRIVE MODE
     &, HCETMODE       ! ELEVATOR TRIM BACKDRIVE MODE
     &, HCHMODE        ! STABILIZER BACKDRIVE MODE
     &, HCRMODE        ! RUDDER     BACKDRIVE MODE
     &, HCRTMODE       ! RUDDER   TRIM BACKDRIVE MODE
     &, HEMODE(4)      ! MODE OF ENGINES PROGRAM
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  HBYGAIN        ! BYPASS FAST TRIMMING LOGIC
     &, HCWTRM         ! WIND SET FOR TRIM HEADING
     &, HECMDL         ! REQUEST FOR NET THRUST OFFSET
     &, HELVL          ! REQUEST FOR ELEVATOR OFFSET
     &, HPRATS         ! REQUEST FOR ROLL RATE SET
     &, HQRATS         ! REQUEST FOR PITCH RATE SET
     &, HRRATS         ! REQUEST FOR YAW RATE SET
     &, HRUDL          ! REQUEST FOR RUDDER OFFSET
     &, HSTABL         ! REQUEST FOR STAB OFFSET
     &, HWHEELL        ! REQUEST FOR WHEEL OFFSET
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, VACCEL         ! TRIM WITH AN X ACCELERATION
     &, VBANKSET       ! BANK ANGLE SET FOR TRIMS
     &, VBOG           ! ON GROUND FLAG
     &, VDRSET         ! RUDDER ANGLE SET  FOR TRIM
     &, VDUMMYL(30)    ! LOGICAL SPARE
     &, VENGOUT        ! ENGINE OUT FLAG FOR REPOSITIONS
     &, VENGSET        ! ENGINE SET FOR TRIMS
     &, VHIAS          ! IAS HOLD DURING TRIM
     &, VHMACH         ! MACH HOLD DURING TRIM
     &, VIASSET        ! EQUIV. AIRSP.SET FOR TRIMS
     &, VMSET          ! MACH NO. SET FOR TRIM
     &, VSITE          ! SIMULATOR IS ON SITE
     &, VSTABSET       ! STAB.ANGLE SET FOR TRIMS
     &, VTNZL          ! FLAG TO NOT ZERO VQ, TO GET VNZL < 1
     &, VWHLSET        ! WHEEL ANGLE SET FOR TRIM
     &, VXWINDR        ! X-WIND REPOSITIONS ON
     &, VYACCEL        ! TRIM WITH AN Y ACCELERATION
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  HAIL           ! MODE 1 AILERON       COMMAND (+RWD)    [deg]
     &, HCATRIM        ! AILERON  TRIM BACKDRIVE COMMAND
     &, HCETRIM        ! ELEVATOR TRIM BACKDRIVE COMMAND
     &, HCOL           ! MODE 2 COLUMN        COMMAND (+AFT)    [deg]
     &, HCRTRIM        ! RUDDER   TRIM BACKDRIVE COMMAND
     &, HECMD(4)       ! COMMANDED ENGINE PARAMETER               [-]
     &, HECMDO(4)      ! NET THRUST OFFSET                      [lbs]
     &, HELV           ! MODE 1 ELEVATOR      COMMAND (+TED)    [deg]
     &, HELVO          ! ELEVATOR OFFSET                        [deg]
     &, HPEDAL         ! MODE 2 PEDAL         COMMAND (+RT)     [deg]
     &, HRUD           ! MODE 1 RUDDER        COMMAND (+RT)     [deg]
     &, HRUDO          ! RUDDER OFFSET                          [deg]
     &, HSTAB          ! MODE 1 STABILIZER    COMMAND (+TED)    [deg]
     &, HSTABO         ! STABILIZER OFFSET                      [deg]
     &, HTGV           ! TRIM GAIN VVGD BANK FREE
     &, HTGW           ! TRIM GAIN VWGD USE VTHETA
     &, HUACC          ! DEMANDED X-ACCEL FOR TRIM
     &, HVACC          ! DEMANDED Y-ACCEL FOR TRIM
     &, HWHEEL         ! MODE 2 WHEEL         COMMAND (+RWD)    [deg]
     &, HWHEELO        ! WHEEL OFFSET                           [deg]
     &, VBETASET       ! SIDESLIP ANGLE FOR TRIMS
     &, VETRIM         ! ELEVATOR TRIM TAB POSITION             [deg]
     &, VMS            ! MACH NO. FOR TRIM PROGRAM
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPSI0          ! AIRCRAFT HEADING (NO CORRECTION)       [rad]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VTHETA         ! A/C PITCH ANGLE                        [rad]
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
      REAL*4   
     &  VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VVT            ! TOTAL A/C VELOCITY                    [ft/s]
     &, VWG            ! BODY AXES Z VEL. WRT GROUND           [ft/s]
     &, VZDS           ! RATE OF CLIMB FOR TRIM FPS(-=ROC;+=ROD)
C$
      INTEGER*4
     &  VNINIT         ! MINIMUM ITERATIONS TO TRIM
C$
      LOGICAL*1
     &  HECMDGL        ! RAMPING GROSS THRUST
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, VNZLSET        ! FLAG FOR TRIM TO COMMANDED LOAD FACTOR
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(16272),DUM0000003(73)
     &, DUM0000004(1),DUM0000005(6),DUM0000006(107)
     &, DUM0000007(64),DUM0000008(236),DUM0000009(128)
     &, DUM0000010(4),DUM0000011(288),DUM0000012(8)
     &, DUM0000013(8),DUM0000014(12),DUM0000015(8)
     &, DUM0000016(92),DUM0000017(8),DUM0000018(84)
     &, DUM0000019(124),DUM0000020(4),DUM0000021(12)
     &, DUM0000022(4),DUM0000023(4),DUM0000024(4),DUM0000025(8)
     &, DUM0000026(28),DUM0000027(16),DUM0000028(16)
     &, DUM0000029(16),DUM0000030(168),DUM0000031(956)
     &, DUM0000032(188),DUM0000033(4),DUM0000034(1012)
     &, DUM0000035(272),DUM0000036(3),DUM0000037(8)
     &, DUM0000038(2),DUM0000039(1),DUM0000040(4),DUM0000041(2)
     &, DUM0000042(136),DUM0000043(678),DUM0000044(40)
     &, DUM0000045(112),DUM0000046(16),DUM0000047(70)
     &, DUM0000048(1),DUM0000049(23),DUM0000050(4)
     &, DUM0000051(4),DUM0000052(1),DUM0000053(1),DUM0000054(3)
     &, DUM0000055(8),DUM0000056(88),DUM0000057(13)
     &, DUM0000058(2),DUM0000059(8),DUM0000060(4),DUM0000061(24)
     &, DUM0000062(380),DUM0000063(2),DUM0000064(8)
     &, DUM0000065(4),DUM0000066(8),DUM0000067(4),DUM0000068(8)
     &, DUM0000069(301),DUM0000070(2700),DUM0000071(120)
     &, DUM0000072(220),DUM0000073(5380),DUM0000074(8)
     &, DUM0000075(20),DUM0000076(24),DUM0000077(7036)
     &, DUM0000078(6199),DUM0000079(54804),DUM0000080(206573)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VSITE,DUM0000003,VXWINDR
     &, DUM0000004,VACCEL,VYACCEL,DUM0000005,VBOG,DUM0000006
     &, VETRIM,DUM0000007,VEFD,DUM0000008,VNZL,VWGD,VWG,DUM0000009
     &, VAYB,VVGD,VVG,DUM0000010,VBETA,DUM0000011,VUGD,VUG,DUM0000012
     &, VVT,DUM0000013,VVT1INV,DUM0000014,VPRESS,DUM0000015,VQPRS
     &, DUM0000016,VPD,DUM0000017,VP,DUM0000018,VQD,VQ,DUM0000019
     &, VRD,DUM0000020,VR,DUM0000021,VPHI,DUM0000022,VSNPHI,VCSPHI
     &, DUM0000023,VTHETA,DUM0000024,VSNTHE,VCSTHE,DUM0000025
     &, VPSI0,DUM0000026,VLXB0,DUM0000027,VNXB,VLYB0,DUM0000028
     &, VNYB,VLZB0,DUM0000029,VNZB,DUM0000030,VZD,VHH,DUM0000031
     &, VTEMPK,DUM0000032,VVWNS,VVWEW,DUM0000033,VUW,VVW,VWW
     &, DUM0000034,VKINT,DUM0000035,VACC1,VACC2,VNZLCMD,VTNZL
     &, DUM0000036,VTRIM,VMS,VIASS,DUM0000037,VBETASET,VZDS,VMSET
     &, VHMACH,VIASSET,VHIAS,DUM0000038,VDRSET,VWHLSET,VNZLSET
     &, VBANKSET,DUM0000039,VENGSET,VSTABSET,DUM0000040,VENGOUT
     &, DUM0000041,VNINIT,DUM0000042,VFSTICK,DUM0000043,VDUMMYL
     &, DUM0000044,VDUMMYR,DUM0000045,HEMODE,HECMD,HECMDO,HECMDE
     &, DUM0000046,HECMDG,DUM0000047,HECMDL,DUM0000048,HECMDGL
     &, DUM0000049,HCEMODE,HCAMODE,HCRMODE,DUM0000050,HCHMODE
     &, DUM0000051,HELVL,DUM0000052,HRUDL,DUM0000053,HSTABL,DUM0000054
     &, HELV,DUM0000055,HELVO,HELVE,HAIL,DUM0000056,HRUD,HRUDO
     &, HRUDE,HSTAB,HSTABO,HSTABE,DUM0000057,HWHEELL,DUM0000058
     &, HCOL,DUM0000059,HWHEEL,HWHEELO,HWHEELE,DUM0000060,HPEDAL
     &, DUM0000061,HCETMODE,HCATMODE,HCRTMODE,HCETRIM,HCATRIM
     &, HCRTRIM,DUM0000062,HBYGAIN,HCWTRM,DUM0000063,HTGU,HTGU1
     &, HTGW,HTGV,HTGV1,HTGV2,HTGP,HTGP1,HTGQ,HTGR,HTGR1,HTGH
     &, HTLU,HTLU1,HTLW,HTLV,DUM0000064,HTLP1,HTLQ,DUM0000065
     &, HTLR1,HTLH,DUM0000066,HTDW,HTDP,DUM0000067,HTDR,DUM0000068
     &, HUACC,HVACC,HWACC,HPACC,HQACC,HRACC,HRRAT,HQRAT,HPRAT
     &, HRRATS,HQRATS,HPRATS,DUM0000069,VFETRIM,DUM0000070,CIETSPOS
      COMMON   /XRFTEST   /
     &  DUM0000071,CIAFCFOR,DUM0000072,CIRCFOR,DUM0000073,CELVL
     &, CELVR,DUM0000074,CETRIM,DUM0000075,CAILL,CAILR,DUM0000076
     &, CRUD,DUM0000077,RUFLT,DUM0000078,RUREPGPA,DUM0000079
     &, EFNT,DUM0000080,TCFFLPOS  
C------------------------------------------------------------------------------
C     Outputs
C
C
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
C
C     LOGICALS
C     --------
C
      LOGICAL LFIRST          ! first pass flag
     &,       LS300/.FALSE./  ! Dash-8 series 300 model active
     &,       LSTAB/.TRUE./   ! stabilizer backdriven
     &,       LSURF/.TRUE./   ! surfaces backdriven
     &,       LTHRUST/.TRUE./ ! thrust backdriven
C
      REAL   lfreeg/.0005/    ! zero column force gain
      REAL   lafreeg/.001/    ! zero wheel force gain
      REAL   lrfreeg/.001/    ! zero rudder force gain
C
C     INTEGERS
C     --------
C
      INTEGER*4
     &     I          ! index for engines
     &,    INZL       ! minimum number of iterations to trim for load factor set
     &,    LHDGCORR/5/! counter
     &,    LERROR/4000/
     &,    LTRIM/100/
     &,    LTIMER
     &,    LEND/-1/
     &,    NENG
     &,    LCNT
C
C     REALS
C     -----
C
       REAL*4
     &     CSPSI      ! cosine of difference between initial and actual heading
     &,    DELP       ! diff. between actual and commanded roll accel.
     &,    DELPD      ! damping terms used in calculation of DPRATE
     &,    DELPL      ! damping terms used in calculation of DPRATE
     &,    DELQ       ! diff. between actual and commanded pitch accel.
     &,    DELR       ! diff. between actual and commanded yaw accel.
     &,    DELRD      ! damping terms used in calculation of DRRATE
     &,    DELRL      ! damping terms used in calculation of DRRATE
     &,    DELU       ! diff. between actual and commanded long. accel.
     &,    DELV       ! diff. between actual and commanded lateral accel.
     &,    DELW       ! diff. between actual and commanded heave accel.
     &,    DELWD      ! damping terms used in calculation of DWRATE
     &,    DELWL      ! damping terms used in calculation of DWRATE
     &,    DPH        ! parameter used in DVVT calc.
     &,    DPRATE     ! change in stabilizing parameter due to roll accel.
     &,    DQP        ! parameter used in DVVT calc.
     &,    DQRATE     ! change in stabilizing parameter due to pitch accel.
     &,    DQT        ! parameter used in DVVT calc.
     &,    DQVT       ! parameter used in DVVT calc.
     &,    DRRATE     ! change in stabilizing parameter due to yaw accel.
     &,    DTH        ! temperature lapse rate
     &,    DVVT       ! acceleration correction for constant airspeed/mach climb
     &,    DURATE     ! change in stabilizing parameter due to long. accel.
     &,    DVRATE     ! change in stabilizing parameter due to lateral accel.
     &,    DWRATE     ! change in stabilizing parameter due to heave accel.
     &,    FACT       ! high speed gain modifier
     &,    HDGLATCH   ! initial or desired heading
     &,    HTGAY/.1/  ! gain to trim for Ay with beta
     &,    LAIR       ! velocity of sound in air
     &,    LBETA      ! initial sideslip
     &,    LDIFF      ! difference in wind direction and heading
     &,    LGAIN1     ! gain for engine out reposition
     &,    LGAIN2     ! gain for engine out reposition
     &,    LGAIN3     ! gain for engine out reposition
     &,    LGLIDE3    ! cosine of 3 degrees
     &,    LNEWREF    ! "reference" heading
     &,    LPSIW      ! wind direction
     &,    LREFDIFF   ! difference in wind heading and LNEWREF
     &,    LSNB       ! sine if initial sideslip
     &,    LSP0       ! scratch pad
     &,    LSP1       ! scratch pad
     &,    LSP2       ! scratch pad
     &,    LSP3       ! scratch pad
     &,    LSP4       ! scratch pad
     &,    LSP9       ! scratch pad
     &,    LSURFA/.06/! aileron surface backdrive tolerance
     &,    LSURFE/.06/! elevator surface backdrive tolerance
     &,    LSURFET/.6/! elevator tab zero force backdrive tolerance
     &,    LSURFR/.06/! rudder surface backdrive tolerance
     &,    LTGP       ! local P trim gain
     &,    LTGQ       ! local Q trim gain
     &,    LTGR       ! local R trim gain
     &,    LTGU       ! local U trim gain
     &,    LTGV       ! local V trim gain
     &,    LTGW       ! local W trim gain
     &,    LVW        ! initial Y body velocity w.r.t. wind
     &,    LWIND      ! wind speed
     &,    LWLATCH    ! vertical wind speed
     &,    LZDCORR    ! component of ROC correction due to wind
     &,    LZDS       ! correction in rate of climb due to wind
     &,    LQACC,LPACC,LRACC ! RAD/SEC/SEC EQUIV. OF HQACC,HPACC,HRACC
     &,    OPD        ! old VPD
     &,    OQD        ! old VQD
     &,    ORD        ! old VRD
     &,    OUGD       ! old VUGD
     &,    OVGD       ! old VVGD
     &,    OWGD       ! old VWGD
     &,    P2         ! parameter used in DVVT calc.
     &,    P2INV      ! parameter used in DVVT calc.
     &,    P3         ! parameter used in DVVT calc.
     &,    P4         ! parameter used in DVVT calc.
     &,    P4INV      ! parameter used in DVVT calc.
     &,    SNPSI      ! sine of difference between initial and actual heading
     &,    THRUST     ! total thrust commanded for trim
     &,    VHORIZ     ! speed in horizontal plane
     &,    VTUGD      ! total longitudinal acceleration signal
     &,    VTVGD      ! total lateral acceleration signal
     &,    VTWGD      ! total vertical acceleration signal
     &,    PI         ! Pi
     &,    DEG_RAD    ! Converts - degrees to radians
     &,    PVA0       ! speed of sound in air - standard day
     &,    PPAMB      ! standard ambient press. - sea level
     &,    LEDRM(2)   ! Last iteration ram drag       (lbs)
       REAL*4
     &     AILIM      ! Aileron limit of travel       (deg)
     &,    COLIMN     ! Column limit of travel        (deg)
     &,    COLIMP     ! Column limit of travel        (deg)
     &,    ELVLIMN    ! Elevator limit of travel      (deg)
     &,    ELVLIMP    ! Elevator limit of travel      (deg)
     &,    PEDLIM     ! Pedal limit of travel         (deg)
     &,    RUDLIM     ! Rudder limit of travel        (deg / cm)
     &,    TABLIMN    ! Pitch trim tab neg travel limit   (deg)
     &,    TABLIMP    ! Pitch trim tab pos travel limit   (deg)
     &,    ATBLIMN    ! Aileron trim tab neg travel limit (deg)
     &,    ATBLIMP    ! Aileron trim tab pos travel limit (deg)
     &,    RTRLIMN    ! Rudder trim neg travel limit      (deg)
     &,    RTRLIMP    ! Rudder trim pos travel limit      (deg)
     &,    RATLIMN    ! Pitch trim tab neg rate limit (deg)
     &,    RATLIMP    ! Pitch trim tab pos rate limit (deg)
     &,    WHLIM      ! Wheel limit of travel         (deg)
     &,    AILIM1     ! Aileron limit of travel       (deg)
     &,    COLIMN1    ! Column limit of travel        (deg)
     &,    COLIMP1    ! Column limit of travel        (deg)
     &,    ELVLIMN1   ! Elevator limit of travel      (deg)
     &,    ELVLIMP1   ! Elevator limit of travel      (deg)
     &,    PEDLIM1    ! Pedal limit of travel         (deg)
     &,    RUDLIM1    ! Rudder limit of travel        (deg)
     &,    TABLIMN1/-4./    ! Pitch trim tab neg travel limit   (deg)
     &,    TABLIMP1/11./    ! Pitch trim tab pos travel limit   (deg)
     &,    ATBLIMN1   ! Aileron trim tab neg travel limit (deg)
     &,    ATBLIMP1   ! Aileron trim tab pos travel limit (deg)
     &,    RTRLIMN1   ! Rudder trim neg travel limit      (deg)
     &,    RTRLIMP1   ! Rudder trim pos travel limit      (deg)
     &,    RATLIMN1/-.1/    ! Pitch trim tab neg rate limit (deg)
     &,    RATLIMP1/ .1/    ! Pitch trim tab pos rate limit (deg)
     &,    WHLIM1     ! Wheel limit of travel         (deg)
     &,    AILIM3     ! Aileron limit of travel       (deg)
     &,    COLIMN3    ! Column limit of travel        (deg)
     &,    COLIMP3    ! Column limit of travel        (deg)
     &,    ELVLIMN3   ! Elevator limit of travel      (deg)
     &,    ELVLIMP3   ! Elevator limit of travel      (deg)
     &,    PEDLIM3    ! Pedal limit of travel         (deg)
     &,    RUDLIM3    ! Rudder limit of travel        (cm)
     &,    TABLIMN3/-4./    ! Pitch trim tab neg travel limit   (deg)
     &,    TABLIMP3/11./    ! Pitch trim tab pos travel limit   (deg)
     &,    ATBLIMN3   ! Aileron trim tab neg travel limit (deg)
     &,    ATBLIMP3   ! Aileron trim tab pos travel limit (deg)
     &,    RTRLIMN3   ! Rudder trim neg travel limit      (deg)
     &,    RTRLIMP3   ! Rudder trim pos travel limit      (deg)
     &,    RATLIMN3/-.1/    ! Pitch trim tab neg rate limit (deg)
     &,    RATLIMP3/ .1/    ! Pitch trim tab pos rate limit (deg)
     &,    WHLIM3     ! Wheel limit of travel         (deg)
C
C     PARAMETERS
C     ----------
C
      PARAMETER (
     &     PI    = 3.*********       ! Pi
     &,    PVA0  = 661.0             ! speed of sound in air - standard day
     &,    PPAMB = 2116.2384         ! standard ambient press. - sea level
     &,    DEG_RAD = PI/180.0        ! Converts - degrees to radians
     &,    AILIM1    = 20.0           ! Aileron limit of travel       (deg)
     &,    COLIMN1   = -25.0          ! Column limit of travel        (deg)
     &,    COLIMP1   = 20.0           ! Column limit of travel        (deg)
     &,    ELVLIMN1  = -30.           ! Elevator limit of travel      (deg)
     &,    ELVLIMP1  = 20.            ! Elevator limit of travel      (deg)
     &,    PEDLIM1   = 60.0           ! Pedal limit of travel         (deg)
     &,    RUDLIM1   = 18.5           ! Rudder limit of travel        (deg)
     &,    ATBLIMN1  = -20.           ! Aileron trim tab neg travel limit (deg)
     &,    ATBLIMP1  = 20.            ! Aileron trim tab pos travel limit (deg)
     &,    RTRLIMN1  = -10.           ! Rudder trim neg travel limit      (deg)
     &,    RTRLIMP1  = 10.            ! Rudder trim pos travel limit      (deg)
     &,    WHLIM1    = 60.0           ! Wheel limit of travel         (deg)
     &,    AILIM3    = 20.0           ! Aileron limit of travel       (deg)
     &,    COLIMN3   = -25.0          ! Column limit of travel        (deg)
     &,    COLIMP3   = 20.0           ! Column limit of travel        (deg)
     &,    ELVLIMN3  = -30.           ! Elevator limit of travel      (deg)
     &,    ELVLIMP3  = 20.            ! Elevator limit of travel      (deg)
     &,    PEDLIM3   = 60.0           ! Pedal limit of travel         (deg)
     &,    RUDLIM3   = 48.            ! Rudder limit of travel        (cm)
     &,    ATBLIMN3  = -20.           ! Aileron trim tab neg travel limit (deg)
     &,    ATBLIMP3  = 20.            ! Aileron trim tab pos travel limit (deg)
     &,    RTRLIMN3  = -10.           ! Rudder trim neg travel limit      (deg)
     &,    RTRLIMP3  = 10.            ! Rudder trim pos travel limit      (deg)
     &,    WHLIM3    = 60.0           ! Wheel limit of travel         (deg)
C
     &,    P2       = 145442.56
     &,    P2INV    = 1.0 / P2
     &,    P3       = 472.68486
     &,    P4       = 20805.8257
     &,    P4INV    = 1.0 / P4
     &,    NENG     = 2              ! Number of engines
     & )
C
C     DATA
C     ----
C
      DATA LFIRST /.TRUE./
     &,    LGAIN1 /0.1/
     &,    LGAIN2 /-3.0/
     &,    LGAIN3 /0.04/
C
C     +------------------------------+
C     |                              |
C     |     E N T R Y   P O I N T    |
C     |                              |
C     +------------------------------+
C
      ENTRY TRIM
C
CD VTR010  On first pass do initializations
CR         N/A
C
CC         Initialize local gains to 1 and previous accelerations to zero
C
      IF (LFIRST) THEN
        LFIRST = .FALSE.
        IF (YITAIL.EQ.230) THEN
          LS300 = .TRUE.   !series 300 tail 230
        ELSE
          LS300 = .FALSE.  !series 100 tail 226
        ENDIF
        IF (LS300) THEN
          AILIM      = AILIM3
          COLIMN     = COLIMN3
          COLIMP     = COLIMP3
          ELVLIMN    = ELVLIMN3
          ELVLIMP    = ELVLIMP3
          PEDLIM     = PEDLIM3
          RUDLIM     = RUDLIM3
          TABLIMN    = TABLIMN3
          TABLIMP    = TABLIMP3
          ATBLIMN    = ATBLIMN3
          ATBLIMP    = ATBLIMP3
          RTRLIMN    = RTRLIMN3
          RTRLIMP    = RTRLIMP3
          RATLIMN    = RATLIMN3
          RATLIMP    = RATLIMP3
          WHLIM      = WHLIM3
        ELSE
          AILIM      = AILIM1
          COLIMN     = COLIMN1
          COLIMP     = COLIMP1
          ELVLIMN    = ELVLIMN1
          ELVLIMP    = ELVLIMP1
          PEDLIM     = PEDLIM1
          RUDLIM     = RUDLIM1
          TABLIMN    = TABLIMN1
          TABLIMP    = TABLIMP1
          ATBLIMN    = ATBLIMN1
          ATBLIMP    = ATBLIMP1
          RTRLIMN    = RTRLIMN1
          RTRLIMP    = RTRLIMP1
          RATLIMN    = RATLIMN1
          RATLIMP    = RATLIMP1
          WHLIM      = WHLIM1
        ENDIF
        HTGV   = .01
        HTGW   = .01
        HTGP   = 60.
        LTGU   = 1.0
        LTGV   = 1.0
        LTGW   = 1.0
        LTGP   = 1.0
        LTGR   = 1.0
        LTGQ   = 1.0
        OUGD   = 0.0
        OVGD   = 0.0
        OWGD   = 0.0
        OPD    = 0.0
        ORD    = 0.0
        OQD    = 0.0
      ENDIF
C
CD VTR020  Adjustable gains for quicker trimming
CR         N/A
C
CC         Adjust each degree of freedom gain to increase trimming
CC         speed. Gains are reduced as accelerations reach zero.
C
      IF (VNINIT .EQ. 0.0) THEN
C
        IF (OUGD * VUGD .LE. 0.0) THEN
          LTGU = LTGU * 0.5
        ELSEIF ((ABS(VUGD) - ABS(OUGD)) .GT. 0.0) THEN
          LTGU = LTGU * 1.1
        ELSEIF (ABS(VUGD - OUGD) .LT. (ABS(VUGD) * 0.05)) THEN
          LTGU = LTGU * 1.1
        ELSEIF (ABS(VUGD - OUGD) .GT. (ABS(VUGD) * 0.25)) THEN
          LTGU = LTGU / 1.1
        ENDIF
C
        IF (OVGD * VVGD .LE. 0.0) THEN
          LTGV = LTGV * 0.5
        ELSEIF ((ABS(VVGD) - ABS(OVGD)) .GT. 0.0) THEN
          LTGV = LTGV * 1.1
        ELSEIF (ABS(VVGD-OVGD) .LT. (ABS(VVGD) * 0.10)) THEN
          LTGV = LTGV * 1.1
        ELSEIF (ABS(VVGD-OVGD) .GT. (ABS(VVGD) * 0.20)) THEN
          LTGV = LTGV / 1.1
        ENDIF
C
        IF (OWGD * VWGD .LE. 0.0) THEN
          LTGW = LTGW * 0.5
        ELSEIF ((ABS(VWGD) - ABS(OWGD)) .GT. 0.0) THEN
          LTGW = LTGW * 1.1
        ELSEIF (ABS(VWGD - OWGD) .LT. (ABS(VWGD) * 0.10)) THEN
          LTGW = LTGW * 1.1
        ELSEIF (ABS(VWGD - OWGD) .GT. (ABS(VWGD) * 0.30)) THEN
          LTGW = LTGW / 1.1
        ENDIF
C
        IF (OPD * VPD .LE. 0.0) THEN
          LTGP = LTGP * 0.5
        ELSEIF ((ABS(VPD) - ABS(OPD)) .GT. 0.0) THEN
          LTGP = LTGP * 1.0
        ELSEIF (ABS(VPD - OPD) .LT. (ABS(VPD) * 0.15)) THEN
          LTGP = LTGP * 1.0
        ELSEIF (ABS(VPD - OPD) .GT. (ABS(VPD) * 0.35)) THEN
          LTGP = LTGP / 1.1
        ENDIF
C
        IF (OQD * VQD .LE. 0.0) THEN
          LTGQ = LTGQ * 0.5
        ELSEIF ((ABS(VQD) - ABS(OQD)) .GT. 0.0) THEN
          LTGQ = LTGQ * 1.1
        ELSEIF (ABS(VQD - OQD) .LT. (ABS(VQD) * 0.05)) THEN
          LTGQ = LTGQ * 1.1
        ELSEIF (ABS(VQD - OQD) .GT. (ABS(VQD) * 0.25)) THEN
          LTGQ = LTGQ / 1.1
        ENDIF
C
        IF (ORD * VRD .LE. 0.0) THEN
          LTGR = LTGR * 0.5
        ELSEIF ((ABS(VRD) - ABS(ORD)) .GT. 0.0) THEN
          LTGR = LTGR * 1.1
        ELSEIF (ABS(VRD - ORD) .LT. (ABS(VRD) * 0.05)) THEN
          LTGR = LTGR * 1.1
        ELSEIF (ABS(VRD - ORD) .GT. (ABS(VRD) * 0.25)) THEN
          LTGR = LTGR / 1.1
        ENDIF
      ENDIF
C
CD VTR030  Limit adjustable gains
CR         N/A
C
CC         Adjustable gains are limited to lie between .5 and 10.
CC         The gains can also be frozen at 1. if desired.
C
      IF (HBYGAIN) THEN
        LTGU = 1.0
        LTGV = 1.0
        LTGW = 1.0
        LTGP = 1.0
        LTGQ = 1.0
        LTGR = 1.0
      ELSE
        LTGU = AMIN1(AMAX1( 0.5, LTGU), 10.0)
        LTGV = AMIN1(AMAX1( 0.5, LTGV), 10.0)
        LTGW = AMIN1(AMAX1( 0.5, LTGW), 10.0)
        LTGP = AMIN1(AMAX1( 0.5, LTGP), 10.0)
        LTGQ = AMIN1(AMAX1( 0.5, LTGQ), 10.0)
        LTGR = AMIN1(AMAX1( 0.5, LTGR), 20.0)
      ENDIF
C
CD VTR040  Store previous value of accelerations
CR         N/A
C
      OUGD = VUGD
      OVGD = VVGD
      OWGD = VWGD
      OPD  = VPD
      OQD  = VQD
      ORD  = VRD
C
CD VTR050  Store initial heading
CR         N/A
C
CC         Initial heading is stored. This will be the heading the Aircraft
CC         tracks during a crossing reposition.
C
      IF (VNINIT .EQ. 10) THEN
        HDGLATCH = VPSI0
        LNEWREF = VPSI0
        LZDS = 0.
C
CD VTR060  Initialize counter for trim to a load factor
CR         N/A
C
        INZL = 200
C
CD VTR070  Temperature lapse rate
C
CC         Temperature lapse rate is set to standard atmosphere.
C
        IF (VHH .GT. 36089.0) THEN
          DTH = 0.0
        ELSE
          DTH = -1.98 / 1000.0
        ENDIF
      ENDIF
C
CD VTR080  Compute wind maginitude and direction
C
CC         If a trim with crosswind is active the total wind
CC         direction and magnitude including windshears, microbursts
CC         is computed
C
      IF (VXWINDR .AND. (VNINIT .GT. LEND)) THEN
        LWIND = SQRT(VVWNS * VVWNS + VVWEW * VVWEW)
        IF (ABS(VVWNS) .GT. 0.05) THEN
          LPSIW = ATAN2(VVWEW, VVWNS)
        ELSEIF (ABS(VVWEW) .GT. 0.05) THEN
          LPSIW = (PI / 2.0) * SIGN(1.0, VVWEW)
        ELSE
          LPSIW = 0.0
        ENDIF
C
CD VTR090  Difference in wind direction and initial heading
CR         N/A
C
        LDIFF = LPSIW - HDGLATCH
        IF (LDIFF .GT.  PI) LDIFF = LDIFF - (2.0 * PI)
        IF (LDIFF .LT. -PI) LDIFF = LDIFF + (2.0 * PI)
        IF (ABS(LDIFF) .LE. 1E-30) LDIFF = 0.0
C
CD VTR100  Initial Y body axis velocity w.r.t. air
CR         N/A
C
        LVW = LWIND * SIN(LDIFF)
C
CD VTR110  Sine of initial sideslip angle
CR         N/A
C
        LSNB = AMIN1(AMAX1(-1.0, LVW * VVT1INV), 1.0)
C
CD VTR120  Initial sideslip angle
CR         N/A
C
        LBETA = ASIN(LSNB)
      ENDIF
C
CD VTR130  Increment "reference" heading by initial beta
C
CC         To track the requested heading we offset the actual heading
CC         by an initial beta.
C
      IF (VNINIT .GT. LEND) THEN
        IF (VXWINDR) THEN
          LNEWREF = HDGLATCH + LBETA
        ENDIF
C
CD VTR140  Correct ROC so A/C is still on glideslope.
C
CC         The rate of descent is corrected for the effect of
CC         wind so that the A/C remains on the glideslope.
C
        IF (VXWINDR) THEN
          IF ((VNINIT .GT. LHDGCORR) .AND. (VNINIT .LT. 10)) THEN
            IF (RUFLT .AND. (VZDS .NE. 0.0)) THEN
              IF ( RUREPGPA .NE. 0.) THEN
                LGLIDE3 = RUREPGPA * DEG_RAD
              ELSE
                LGLIDE3 = 3.0 * DEG_RAD
              ENDIF
              LREFDIFF = LPSIW - LNEWREF
              IF (LREFDIFF .GT.  PI) LREFDIFF = LREFDIFF - (2.0 * PI)
              IF (LREFDIFF .LT. -PI) LREFDIFF = LREFDIFF + (2.0 * PI)
              IF (ABS(LREFDIFF) .LE. 1E-30) LREFDIFF = 0.0
              LZDCORR = -LWIND * LGLIDE3 * COS(LREFDIFF) * COS(LBETA)
              LWLATCH = VWW
              LZDS = LZDCORR + LWLATCH
            ELSE
              LZDS = 0.0
            ENDIF
          ENDIF
C
CD VTR150  Increment heading by beta
CR         N/A
C
        ELSEIF (.NOT. HCWTRM) THEN
          LNEWREF = HDGLATCH + ASIN(AMAX1(-1.0,
     &                            AMIN1(1.0, VVW * VVT1INV)))
          LZDS  = 0.0
        ENDIF
      ENDIF
C
CD VTR160  Reduce trim gains at higher speeds
C
CC         When dynamic pressure is very large trim gains are reduced
C
      IF (VQPRS .GT. 2.0E5) THEN
        FACT = 2.0E5/VQPRS
      ELSE
        FACT = 1.0
      ENDIF
C
CD VTR170  Accel. needed for constant airspeed climb
C
CC         If the A/C is to be trimmed in a constant airspeed climb
CC         then an additional acceleration must be calculated to
CC         compenstate for the changing relationship between true
CC         airspeed and indicated airspeed as a function of altitude,
CC         temperature and pressure.
C
      IF (VHIAS) THEN
        IF (VVT .EQ. 0.0) VVT = VIASS
        LSP0 = 0.2 * VVT * VVT
        LSP1 = 4329.0 * VTEMPK
        LSP2 = 1.0 + LSP0 / LSP1
        LSP3 = LSP2 ** 2.5
        LSP4 = VPRESS * 3.5 * LSP3
C
        IF (VHH .LE. 36089.0) THEN
          DPH = - (PPAMB * 5.25588 * P2INV) * (1 - VHH * P2INV)**4.2558
        ELSE
          DPH = - (P3 * P4INV) * EXP( (36089. - VHH) * P4INV )
        ENDIF
        DQP  = LSP2 * LSP3 - 1.0
        DQT  = LSP4 * (-LSP0) / (LSP1 * VTEMPK)
        DQVT = LSP4 * 0.4 * VVT / LSP1
        DVVT = (- VZD * (DQP*DPH + DQT*DTH) / DQVT) * VVT1INV
C
CD VTR180  Accel. needed for constant mach climb
C
CC         If the A/C is to be trimmed in a constant Mach climb
CC         then an additional acceleration must be calculated to
CC         compenstate for the changing relationship between true
CC         airspeed and Mach number as a function of temperature.
C
      ELSEIF (VHMACH) THEN
        DVVT = DTH * VVT * VZD/(2.0 * VTEMPK) * VVT1INV
C
CD VTR190  No acceleration
CR         N/A
C
      ELSE
        DVVT = 0.0
      ENDIF
C
C     -------------------------------------------------
C     |    Zero longitudinal (X-axis) acceleration    |
C     -------------------------------------------------
C
CD VTR200  Diff. in actual and demanded long. accel.
CR         N/A
C
      VTUGD = VUGD + VUG * DVVT - HUACC
C
CD VTR210  Modify diff. as a function of speed
CR         N/A
C
      DELU = VTUGD * FACT
C
CD VTR220  X Acceleration left untrimmed
C
CC         Acceleration is not to be trimmed. Set demanded acceleration
CC         to actual acceleration.
C
      IF (VACCEL) THEN
          HUACC = VUGD
C
CD VTR220  Thrust set - drive rate of climb
C
CC         If thrust is set use rate of climb to trim accelerations
C
      ELSEIF (VENGSET) THEN
        DURATE = AMIN1(HTLU1, AMAX1(-HTLU1, (-LTGU*HTGU1*DELU)))
        VZDS   = VZDS + DURATE
      ELSE
C
CD VTR230  Thrust not set - drive thrust
C
CC         Unless X acceleration is to be left untrimmed drive thrust
CC         to trim acceleration
C
        DURATE = AMIN1(LTGU*HTLU, AMAX1(-LTGU*HTLU,
     &                  (-LTGU * HTGU * DELU)))
        THRUST = THRUST + DURATE
        THRUST = AMAX1(-4500.0, AMIN1(14000.0, THRUST))
        DO I=1,NENG
          HECMD(I) = THRUST / NENG
          IF (HECMDGL) HECMDG(I) = HECMD(I) + VEFD(I)
        ENDDO
      ENDIF
C
CD VTR250  On ground - do not drive controls
C
CC         If on ground controls do not get backdriven
C
      IF (VBOG) GOTO 1000
C
C     -------------------------------------------------
C     |      Zero lateral (Y-axis) acceleration       |
C     -------------------------------------------------
C
CD VTR260  Diff. in actual and demanded lateral accel.
CR         N/A
C
      VTVGD = VVGD + VVG * DVVT - HVACC
      DELV  = VTVGD * FACT
      DVRATE = HTGV1 * DELV
C
CD VTR270  Bank angle set to desired load factor
C
CC         Bank angle is driven to get desired load factor
C
      IF (VBANKSET) THEN
        IF (VNZLSET) THEN
C
C Decrement counter.
C
          INZL = MAX0((INZL - 1), 0)
C
C Once commanded load factor achieved stop driving bank angle.
C
          IF (ABS(VNZL - VNZLCMD) .LE. 0.002 .AND.
     &    (INZL .EQ. 0)) VNZLSET = .FALSE.
C
          VPHI = VPHI + AMIN1(0.0017, (HTGV2 * (VNZLCMD - VNZL)))
        ENDIF
C
CD VTR280  Bank set - drive yaw rate
C
CC         If the bank angle is set yaw rate is driven to zero Y
CC         acceleration. The rate may also be set to a desired value.
CC         in which case beta is used to zero the Y acceleration.
C
        IF (HRRATS) THEN
          VR = HRRAT * DEG_RAD
          VBETASET = VBETASET + DVRATE / HTGH
        ELSE
          VR = VR + DVRATE
        ENDIF
C
CD VTR290  Set pitch rate.
C
CC         Pitch rate is set to desired value or as a function of yaw rate
CC         for a steady coordinated turn.
C
        IF (HQRATS) THEN
          VQ = HQRAT * DEG_RAD
        ELSE
          VQ = VR * VSNPHI / VCSPHI
        ENDIF
C
CD VTR300  Set roll rate.
C
CC         Roll rate is set to desired value or as a function of yaw rate
CC         for a steady coordinated turn.
C
        IF (HPRATS) THEN
          VP = HPRAT * DEG_RAD
        ELSE
          VP = -(VQ * VSNPHI + VR * VCSPHI) * VSNTHE / VCSTHE
        ENDIF
C
CD VTR301 Y acceleration untrimmed.
C
CC        If Y acceleratin is not to be trimmed set the demanded
CC        acceleration to the actual acceleratin
C
        IF (VYACCEL)THEN
          VTVGD = 0.
          HVACC = VVGD + VVG * DVVT
        ENDIF
      ELSE
C
CD VTR310  Bank not set - drive bank angle
C
CC         If the bank angle is not set use bank angle to zero Y
CC         acceleration
C
        DVRATE = AMIN1(HTLV, AMAX1(-HTLV, (-LTGV*HTGV*DELV)))
        VPHI   = VPHI + DVRATE
C
CD VTR320  If roll rate not set then it is zero
CR         N/A
C
C
        IF (HPRATS) THEN
          VP = HPRAT * DEG_RAD
        ELSE
          VP   = 0.0
        ENDIF
C
CD VTR330  If yaw rate not set then it is zero
CR         N/A
C
        IF (HRRATS) THEN
          VR = HRRAT * DEG_RAD
        ELSE
          VR   = 0.0
        ENDIF
C
CD VTR340  If pitch rate not set then it is zero
CR         N/A
C
        IF (HQRATS) THEN
          VQ = HQRAT * DEG_RAD
        ELSE
          IF (.NOT. VTNZL) VQ = 0.0
        ENDIF
      ENDIF
C
C     -------------------------------------------------
C     |       Zero heave (Z-axis) acceleration        |
C     =================================================
C
CD VTR350  Diff. in actual and demanded heave accel.
CR         N/A
C
      VTWGD = VWGD + DVVT * VWG - HWACC
      DELW  = VTWGD * FACT
C
CD VTR360  Drive pitch to zero out heave accel.
CR         N/A
C
      DELWL  = DELWL + (DELW - DELWL) * VKINT
      DELWD  = DELW - DELWL
      DWRATE = AMIN1(HTLW, AMAX1(-HTLW,
     &                          (LTGW*HTGW*DELW + HTDW*DELWD)*VKINT))
      VTHETA = VTHETA + DWRATE
C
C     -------------------------------------------------
C     |       Zero roll (P-axis) acceleration         |
C     =================================================
C
CD VTR370  Diff. in actual and demanded roll accel.
CR         N/A
C
      LPACC = HPACC * DEG_RAD
      DELP = (VPD - LPACC) * FACT
C
CD VTR380  Wheel set - drive heading
CR         N/A
C
      IF (VWHLSET) THEN
        DPRATE = AMIN1(LTGP*HTLP1, AMAX1(-LTGP*HTLP1,
     &                  (-LTGP*HTGP1*DELP)))
        VBETASET = VBETASET - DPRATE / HTGH
C
CD VTR390  Wheel not set - drive control or surface
C
CC         If wheel is not set then the wheel or aileron surface is
CC         backdriven to zero roll acceleration
C
      ELSE
        DELPL  = DELPL + (DELP-DELPL) * VKINT
        DELPD  = DELP - DELPL
        DPRATE = (-LTGP*HTGP*DELP - HTDP*DELPD) * VKINT
        IF (HCAMODE .EQ. 2) THEN
          HWHEEL = AMAX1(-WHLIM, AMIN1(WHLIM, (HWHEEL + DPRATE)))
        ELSE
          HAIL   = AMAX1(-AILIM, AMIN1(AILIM, (HAIL + DPRATE)))
          IF (HCATMODE .EQ. 3) then
            HCATRIM=AMAX1(ATBLIMN,AMIN1(ATBLIMP,HCATRIM+
     &              lafreeg*CIAFCFOR))
          ENDIF
        ENDIF
      ENDIF
C
C     -------------------------------------------------
C     |       Zero pitch (Q-axis) acceleration        |
C     =================================================
C
CD VTR400  Diff. in actual and demanded pitch accel.
CR         N/A
C
      LQACC  = HQACC * DEG_RAD
      DELQ   = (VQD - LQACC) * FACT
      DQRATE = AMIN1(LTGQ*HTLQ, AMAX1(-LTGQ*HTLQ, (LTGQ*HTGQ*DELQ)))
C
CD VTR410  Tab set - drive control or surface
C
CC         If the tab is set then the column or elevator surface is
CC         backdriven to zero the pitch acceleration
C
      IF (VSTABSET) THEN
        IF (HCEMODE .EQ. 0) THEN
          HCETRIM = CIETSPOS
        ELSE IF (HCEMODE .EQ. 2) THEN
           HCOL = AMAX1(COLIMN, AMIN1(COLIMP, (HCOL - DQRATE)))
        ELSE
          IF (HCEMODE .EQ. 3) VETRIM = VFETRIM
          HELV = AMAX1(ELVLIMN, AMIN1(ELVLIMP, (HELV + DQRATE)))
        ENDIF
        if (HCETMODE .EQ. 3) then
          lsp0 = AMAX1(RATLIMN,AMIN1(RATLIMP,lfreeg*vfstick))
          HCETRIM=AMAX1(TABLIMN,AMIN1(TABLIMP,HCETRIM+lsp0))
C          HCETRIM=AMAX1(TABLIMN,AMIN1(TABLIMP,HCETRIM+lfreeg*vfstick))
        endif
C
CD VTR420  Tab not set - drive tab
CC         Use the tab to zero pitch acceleration
C
      ELSE
        HCETRIM = AMAX1(TABLIMN, AMIN1(TABLIMP, (HCETRIM - DQRATE)))
        HELV  = HELVE
      ENDIF
C
C     -------------------------------------------------
C     |        Zero yaw (R-axis) acceleration         |
C     =================================================
C
CD VTR430  Diff. in actual and demanded yaw accel.
CC         If the rudder is set the heading is used to zero the
CC         yaw acceleration. This works as body axis velocities are
CC         transformed to the new heading. Thus a beta is generated
CC         that zeros yaw acceleration
C
      LRACC = HRACC * DEG_RAD
      DELR = (VRD - LRACC) * FACT
C
      IF (VDRSET .AND. (.NOT. VWHLSET)) THEN
        DRRATE=AMIN1(LTGR*HTLR1,AMAX1(-LTGR*HTLR1, (LTGR*HTGR1*DELR)))
        VBETASET = VBETASET - DRRATE / HTGH
C
CD VTR460  Rudder not set - drive surface or control
C
CC         If the rudder is not set it is used to zero the yaw acceleration
C
      ELSE
        DELRL  = DELRL + (DELR - DELRL) * VKINT
        DELRD  = DELR - DELRL
        DRRATE = (LTGR * HTGR * DELR + HTDR * DELRD) * VKINT
C
        IF (HCRMODE .EQ. 2) THEN
          HPEDAL = AMAX1(-PEDLIM, AMIN1(PEDLIM, (HPEDAL - DRRATE)))
        ELSE
          HRUD   = AMAX1(-RUDLIM, AMIN1(RUDLIM, (HRUD + DRRATE)))
          IF (HCRTMODE .EQ. 3) then
            HCRTRIM=AMAX1(RTRLIMN,AMIN1(RTRLIMP,HCRTRIM+
     &              lrfreeg*CIRCFOR))
          ENDIF
        ENDIF
C
CD VTR470  Rudder and wheel not set - drive heading
CC         Refernce heading is set if trimming to a
CC         a specific heading with a given wind (for ATG).
C
        IF (.NOT. VWHLSET) THEN
C
C   Trim to a commanded sideslip.
C
C
CD VTR475  For engine out reposition - drive rudder
CC         For engine out repositions zero wheel and drive heading
CC         to achieve a wings level, zero wheel and a certain
CC         sideslip angle.
C
          IF (VENGOUT) THEN
              LSP0   = HWHEEL * FACT * LGAIN1
              LSP1   = (LSP0 - LSP9) / VKINT
              LSP9   = LSP0
              LSP3   = LGAIN2 * LSP0 - LGAIN3 * LSP1
              VBETASET = AMAX1(-20. , AMIN1(20., (VBETASET + LSP3)))
              HWHEEL = 0.0
          ENDIF
C
        ENDIF
      ENDIF
C
C     set heading vs track to acheive desired beta
C
      IF (VDUMMYL(19))THEN ! Use beta to achieve desired VAYB
         VBETASET = VBETASET + HTGAY * (VAYB - VDUMMYR(19))
      ENDIF
      LSP0 = AMIN1(HTLH, AMAX1(-HTLH,(HTGH * (VBETA - VBETASET))))
      IF (HCWTRM)THEN
         LNEWREF = LNEWREF - LSP0
      ELSE
         VPSI0  = VPSI0 + LSP0
      ENDIF
C
CD VTR480  Decrement counter
CR         N/A
C
1000  VNINIT   = MAX0(0, (VNINIT-1))
C
C     ----------------------------------------
C     |     Test if aircraft is trimmed      |
C     ========================================
C
CD VTR490  Ensure thrust is backdriven
CC         Check throttle rates to ensure the engines have achieved
CC         the requested thrust
C
      IF(((HEMODE(1).NE.1).OR.(ABS(EFNT(1)-HECMD(1)).LT.100.)).AND.
     &((HEMODE(2).NE.1).OR.(ABS(EFNT(2)-HECMD(2)).LT.100.)))THEN
        LTIMER = LTIMER + 1
        IF (LTIMER .GT. LTRIM) THEN
          LTHRUST = .TRUE.
        ENDIF
      ELSE
        LTIMER  = 0
        LTHRUST = .FALSE.
      ENDIF
CD VTR500  Ensure surfaces are backdriven
CC         Check that all backdriven surfaces have come to their requested
CC         positions
C
C GC      IF ((HCHMODE .NE. 1) .OR. (ABS(HSTAB-CHSFRL) .LE. 0.06)) THEN
c      IF ((HCHMODE .NE. 1) )THEN
c        LSTAB = .TRUE.
c      ELSE
c        LSTAB = .FALSE.
c      ENDIF
C
      LSURF = .TRUE.
      IF (HCEMODE .EQ. 1) THEN
        LSP0 = ABS(HELV - (CELVL+CELVR)*.5)
        IF (LSP0 .GT. LSURFE) LSURF = .FALSE.
      ENDIF
      IF (HCETMODE .EQ. 3) THEN
        LSP0 = ABS(VFSTICK)
        IF (LSP0 .GT. LSURFET) LSURF = .FALSE.
      ENDIF
      IF (HCAMODE .EQ. 1) THEN
        LSP0 = ABS(HAIL - (CAILL-CAILR)*.5)
        IF (LSP0 .GT. LSURFA) LSURF = .FALSE.
      ENDIF
      IF (HCRMODE .EQ. 1) THEN
        LSP0 = ABS(HRUD - CRUD)
        IF (LSP0 .GT. LSURFR) LSURF = .FALSE.
      ENDIF
C
CD VTR510  Test for at least VNINIT number of iterations (usually 10).
CR         N/A
C
      IF (VNINIT .EQ. 0) THEN
C
CD VTR520  Sum of linear accelerations must be within trim torlerance.
CC         Trim until linear accelerations are within a trim tolerance
C
        IF (ABS(VTUGD) + ABS(VTVGD) + ABS(VTWGD) .LT. VACC1
C
CD VTR530  Sum of angular accelerations must be within trim torlerance.
CC         Trim until angular accelerations are within a trim tolerance
C
     &  .AND. ABS(VPD-LPACC)+ABS(VQD-LQACC)+ABS(VRD-LRACC) .LT. VACC2
C
CD VTR540  Thrust and stabilizer backdriven
CC         Trim until all backdriving is complete
C
     &  .AND. ((ABS(LEDRM(1)-VEFD(1)) .LT. 5. .AND.
     &         ABS(LEDRM(2)-VEFD(2)) .LT. 5.) .OR. VSITE)
C
     &  .AND. ((LTHRUST .AND. LSTAB .AND. LSURF).OR. (.NOT. VSITE))
C
     &  ) THEN
C
CD VTR550  Set VTRIM to 1.0 (aircraft in trim).
CR         N/A
C
          VTRIM    = 1.0
C
CD VTR560  Compute offset for VSA ramp
CR         N/A
C
          IF (HELVL)   HELVO   = HELV - HELVE
          IF (HRUDL)   HRUDO   = HRUD - HRUDE
          IF (HWHEELL) HWHEELO = HWHEEL - HWHEELE
          IF (HSTABL)  HSTABO  = HSTAB - HSTABE
          IF (HECMDL) THEN
            DO I = 1, NENG
              HECMDO(I) = HECMD(I) - HECMDE(I)
            ENDDO
          ENDIF
C
CD VTR570  Freeze flight.
CC         Freeze flight and return when trim is complete
C
          IF (VSITE .AND. (.NOT. RUFLT)) TCFFLPOS = .TRUE.
          RETURN
        ENDIF
      ENDIF
C
      IF (LCNT .GT. 10) THEN
        LEDRM(1) = VEFD(1)
        LEDRM(2) = VEFD(2)
        LCNT = 0
      ENDIF
      LCNT = LCNT + 1
C
C     --------------------------------------------
C     |       Compute aircraft velocities        |
C     ============================================
C
CD VTR580  Velocity of sound in air.
CR         N/A
C
      LAIR = SQRT(4329.0 * VTEMPK)
C
CD VTR590  True airspeed as a function of mach number.
CC         Calculate true airspeed as a function of requested mach number
C
      IF (VMSET) THEN
        VVT = VMS * LAIR
C
CD VTR600  True airspeed as a function of indicated airspeed.
CC         Calculate true airspeed as a function of requested indicated
CC         airspeed
C
      ELSEIF (VIASSET) THEN
        LSP0 = VIASS / PVA0
        LSP1 = PPAMB * ((1.0 + 0.2 * LSP0 * LSP0)**3.5 - 1.0)
        LSP2 = LSP1 + VPRESS
        LSP3 = 5.0 * ((LSP2 / VPRESS)**(2.0 / 7.0) - 1.0)
        VMS = SQRT(ABS(LSP3))
        VVT = VMS * LAIR
C
CD VTR610  True airspeed as a function of equivalent airspeed.
CC         Calculate true airspeed as a function of requested equivalent
CC         airspeed
      ELSE
        VVT = VIASS * LAIR / (14.379 * SQRT(VPRESS))
      ENDIF
C
CD VTR620  Speed in horizontal plane. (Total speed minus vertical speed)
CC         Compute component of the velocity in the horizontal plane
C
      LSP1 = VZDS + LZDS
      VHORIZ = SQRT(AMAX1(0.0, (VVT * VVT - LSP1 * LSP1)))
C
CD VTR630  A/C body axis speeds
CC         Resolve the requested airspeed into the body axis speeds
CC         and if not on ground correct for the wind speed
C
      LSP0 = VPSI0 - LNEWREF
      CSPSI = COS(LSP0)
      SNPSI = SIN(LSP0)
C
      VUG = VHORIZ * CSPSI * VLXB0 + LSP1 * VNXB
      VVG = VHORIZ * (CSPSI * VLYB0 - SNPSI * VCSPHI) + LSP1 * VNYB
      VWG = VHORIZ * (CSPSI * VLZB0 + SNPSI * VSNPHI) + LSP1 * VNZB
      IF (.NOT. VBOG) THEN
        VUG = VUG - VUW
        VVG = VVG - VVW
        VWG = VWG - VWW
      ENDIF
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00639 VTR010  On first pass do initializations
C$ 00703 VTR020  Adjustable gains for quicker trimming
C$ 00772 VTR030  Limit adjustable gains
C$ 00794 VTR040  Store previous value of accelerations
C$ 00804 VTR050  Store initial heading
C$ 00815 VTR060  Initialize counter for trim to a load factor
C$ 00820 VTR070  Temperature lapse rate
C$ 00831 VTR080  Compute wind maginitude and direction
C$ 00847 VTR090  Difference in wind direction and initial heading
C$ 00855 VTR100  Initial Y body axis velocity w.r.t. air
C$ 00860 VTR110  Sine of initial sideslip angle
C$ 00865 VTR120  Initial sideslip angle
C$ 00871 VTR130  Increment "reference" heading by initial beta
C$ 00881 VTR140  Correct ROC so A/C is still on glideslope.
C$ 00906 VTR150  Increment heading by beta
C$ 00916 VTR160  Reduce trim gains at higher speeds
C$ 00926 VTR170  Accel. needed for constant airspeed climb
C$ 00952 VTR180  Accel. needed for constant mach climb
C$ 00962 VTR190  No acceleration
C$ 00973 VTR200  Diff. in actual and demanded long. accel.
C$ 00978 VTR210  Modify diff. as a function of speed
C$ 00983 VTR220  X Acceleration left untrimmed
C$ 00991 VTR220  Thrust set - drive rate of climb
C$ 01000 VTR230  Thrust not set - drive thrust
C$ 01015 VTR250  On ground - do not drive controls
C$ 01025 VTR260  Diff. in actual and demanded lateral accel.
C$ 01032 VTR270  Bank angle set to desired load factor
C$ 01051 VTR280  Bank set - drive yaw rate
C$ 01064 VTR290  Set pitch rate.
C$ 01075 VTR300  Set roll rate.
C$ 01086 VTR301 Y acceleration untrimmed.
C$ 01097 VTR310  Bank not set - drive bank angle
C$ 01105 VTR320  If roll rate not set then it is zero
C$ 01115 VTR330  If yaw rate not set then it is zero
C$ 01124 VTR340  If pitch rate not set then it is zero
C$ 01138 VTR350  Diff. in actual and demanded heave accel.
C$ 01144 VTR360  Drive pitch to zero out heave accel.
C$ 01157 VTR370  Diff. in actual and demanded roll accel.
C$ 01163 VTR380  Wheel set - drive heading
C$ 01171 VTR390  Wheel not set - drive control or surface
C$ 01195 VTR400  Diff. in actual and demanded pitch accel.
C$ 01202 VTR410  Tab set - drive control or surface
C$ 01222 VTR420  Tab not set - drive tab
C$ 01234 VTR430  Diff. in actual and demanded yaw accel.
C$ 01247 VTR460  Rudder not set - drive surface or control
C$ 01266 VTR470  Rudder and wheel not set - drive heading
C$ 01275 VTR475  For engine out reposition - drive rudder
C$ 01304 VTR480  Decrement counter
C$ 01313 VTR490  Ensure thrust is backdriven
C$ 01327 VTR500  Ensure surfaces are backdriven
C$ 01356 VTR510  Test for at least VNINIT number of iterations (usually 10).
C$ 01361 VTR520  Sum of linear accelerations must be within trim torlerance.
C$ 01366 VTR530  Sum of angular accelerations must be within trim torlerance.
C$ 01371 VTR540  Thrust and stabilizer backdriven
C$ 01381 VTR550  Set VTRIM to 1.0 (aircraft in trim).
C$ 01386 VTR560  Compute offset for VSA ramp
C$ 01399 VTR570  Freeze flight.
C$ 01418 VTR580  Velocity of sound in air.
C$ 01423 VTR590  True airspeed as a function of mach number.
C$ 01429 VTR600  True airspeed as a function of indicated airspeed.
C$ 01441 VTR610  True airspeed as a function of equivalent airspeed.
C$ 01448 VTR620  Speed in horizontal plane. (Total speed minus vertical speed)
C$ 01454 VTR630  A/C body axis speeds
C
