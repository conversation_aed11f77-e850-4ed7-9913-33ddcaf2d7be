C'Title        De Havilland Dash 8
C'Module_ID    USD8UFI
C'Customer     Usair Dash 8 100/200
C'Application  Simulation of Flight Instruments
C'Author       <PERSON><PERSON><PERSON>'Date         July 91
C
C'Revision_history
C
C  usd8ufi.for.46 28Jan1998 04:51 usd8 JWM
C       < Modified to allow stby attitude indicator to quick align after
C         fault is removed. >
C
C  usd8ufi.for.45 27Aug1997 03:00 usd8 Tom
C       < Changed revision level to bring up to date after CAE mod >
C
C  usd8ufi.for.44 25Aug1997 11:04 ???? <PERSON><PERSON><PERSON>       < Added logic to avoid 0.0 value in BNR ARINC label 164 for RA
C         that conflicts with TCAS. Also move original assignment of BNR
C         label to end of RA section and assigned UGRA instead of LGRA. >
C
C  usd8ufi.for.43 15Aug1997 01:37 usd8 Tom M
C       < CAE change to correct TCAS problem. (RA's below 1k feet) >
C
C  usd8ufi.for.42  6Sep1993 04:11 usd8 W. Pin
<PERSON>       < Set scaling factor for adi slipballs to 1.0 (-300 series). >
C
C  usd8ufi.for.41  6Sep1993 03:01 usd8 W. Pin
C       < Use scaling factor for adi slipballs (-300 series). >
C
C  usd8ufi.for.40 23Oct1992 15:55 usd8 J.Nicol
C       < Added tail flag for SAT/FLX indicator code. >
C
C  usd8ufi.for.39 22Oct1992 13:23 demo J.Nicol
C       < Added CP statement for cb BILC08 used in FLXDISC logic. >
C
C  usd8ufi.for.38 22Oct1992 13:09 demo J.Nicol
C       < Added CP statement for engine instrument panel SAT/FLX lights.  >
C
C  usd8ufi.for.37 22Oct1992 11:37 demo J.Nicol
C       < Added SAT/FLX lights and modified FLXDISC logic.  >
C
C  usd8ufi.for.36 30Jul1992 21:19 usd8 paulv
C       < slow the overactive slipball >
C
C  usd8ufi.for.35 12Jun1992 16:24 usd8 Michal
C       < Corrected RA SSM-SDI >
C
C  usd8ufi.for.34 10Jun1992 09:30 usd8 M.WARD
C       < ADDED CODE FOR TCAS INPUT FROM RADIO ALTIMETER >
C
C  usd8ufi.for.33 30Apr1992 17:06 usd8 m.ward
C       < ivsi test successful, will now drive both instruments with high
C         speed arinc >
C
C  usd8ufi.for.32 29Apr1992 20:48 usd8 m.ward
C       < changed 3 labels from local to cdb >
C
C  usd8ufi.for.31 15Apr1992 15:13 usd8 GB
C       < Removed stby horizon flag when caging (snag 1148) >
C
C  usd8ufi.for.30 15Apr1992 11:01 usd8 m.ward
C       < testing ivsi info on high speed arinc bus >
C
C  usd8ufi.for.29  2Apr1992 18:59 usd8 GB
C       < Only use tracking flag to set rad alt valid when below 2500 ft. >
C
C  usd8ufi.for.28 27Mar1992 10:52 usd8 STEVE M
C       < ADDED THROUGHPUT CODE FOR ADI AND SLIPBALL >
C
C  usd8ufi.for.27 25Mar1992 19:14 usd8 GB
C       < Removed "auto cage" of stby gyro. Added SHRS ATT FAILS malf. >
C
C  usd8ufi.for.26  5Mar1992 10:14 usd8 GB
C       < Replaced UBALTV by UBVSIV in vsi logic (pin 34 is vsiv) >
C
C  usd8ufi.for.25  3Mar1992 14:21 usd8 GB
C       < Modified UGRAV logic to remain valid above 2500 ft when pwr
C         avail. >
C
C  usd8ufi.for.24 27Feb1992 13:29 usd8 GB
C       < Modified vg topple limits. >
C
C  usd8ufi.for.23 27Feb1992 09:02 usd8 GB
C       < Modified VMO equation to use IAS chart iso CAS chart. >
C
C  usd8ufi.for.22 19Feb1992 16:48 usd8 GB
C       < Set VAZB to -32.0 , the normal acceleration . >
C
C  usd8ufi.for.21 19Feb1992 14:30 usd8 GB
C       < Fixed stby hor malf logic. >
C
C  usd8ufi.for.20 13Feb1992 16:39 usd8 GB
C       < Limit UEAZI to -14 as in SAAB to stop slipball going awire. >
C
C  usd8ufi.for.17  6Feb1992 14:34 usd8 GB
C       < Added radtodeg conversion to rate of turn.  >
C
C  usd8ufi.for.16  6Feb1992 12:45 usd8 GB
C       < Convert angles in rad before calling up sin cos functions. >
C
C  usd8ufi.for.15  4Feb1992 12:54 usd8 M.WARD
C       < CHANGED UH$ALTF* TO MATCH NEW CDB AS PER G.BILODEAU'S REQUEST >
C
C  usd8ufi.for.14 30Jan1992 22:50 usd8 GB
C       < Added light test rly in alt alert lt logic. >
C
C  usd8ufi.for.13 30Jan1992 19:46 usd8 GB
C       < Convert fine alt into deg to send sin & cos to the instr. >
C
C  usd8ufi.for.12 22Jan1992 08:24 usd8 GB
C       < Altimeter fine input was changed from a sop to a acop.>
C
C  usd8ufi.for.11 16Jan1992 13:52 usd8 GB
C       < Added call to scalout at the end of module. >
C
C  usd8ufi.for.10 16Jan1992 12:34 usd8 GB
C       < Added missing malfunctions. >
C
C  usd8ufi.for.9 11Jan1992 10:36 usd8 enrique
C       < modify altimeter coarse equation >
C
C  usd8ufi.for.8  9Jan1992 15:26 usd8 GB
C       < Modified equation of altimeters coarse input to be a dc ratio. >
C
C  usd8ufi.for.7  9Jan1992 11:07 usd8 GB
C       < Added ATT FAIL lt logic and changed VG ERECT button logic to
C         momentary type of switch. >
C
C  usd8ufi.for.6 19Dec1991 12:34 usd8 GB
C       < Removed t&s ind slipballs out of power loop. >
C'
C
C'References
C
C
C        ATTITUDE DIRECTOR INDICATOR
C        HONEYWELL, PN 7001827
C        Rev F
C
C        PSCD Standby Altimeter
C        165118.25.5.607
C
C        De HAVILLAND MAINTENANCE MANUAL
C        Chapter 34
C
C        De HAVILLAND DASH 8 WIRING
C        Chapter 34
C
      SUBROUTINE USD8UFI
C
      IMPLICIT NONE
C
C'Purpose
C
C  The program simulates the flight instruments. It also simulates the
C  standby gyro and the radio altimeter system.
C
C'Include_files
C
          INCLUDE 'disp.com'  !NO FPC
C
C'Subroutine_called
C
C  Not applicable
C
C'Ident
      CHARACTER*55  REV/
     -  '$Source: usd8ufi.for.46 28Jan1998 04:51 usd8 JWM    $'/
C'
C
C'Data_base_variables
C
CPI   USD8
C
CPI  &      BILH06(2),BILD04(2),BIAE05(2),BILA06,BILH04,BIRL07,
CPI  &      BIAF05,BILA05(2),BIAE09,BIAC08,BIAL03,BILC08,
C
CPI  &      AMRLK1A,
C
CPI  &      TF31051(2),TF34A41(2),TF31071(2),TF31061(2),TF30051(2),
CPI  &      TF31141,TF31091,TF31081(2),TF34A11(2),TF34A51(2),
CPI  &      T031051(2),T034A41(2),T031071(2),T031061(2),T030051(2),
CPI  &      T031141,T031091,T031081(2),T034A11(2),T034A51(2),
CPI  &      TF34A61(2),T034A61(2),TCFFLPOS,TCFALT,TF34I031,T034I031,
C
CPI  &      MTHPUT,MLATINIT, MLATAXIS,
C
CPI  &      RNPCHVO,RNPCHO,RNROLO,
C
CPI  &      VTEMP,VALPHA,VUA,VSNTHE,VCSTHE,VTHETADG,VPHIDG,
CPI  &      VZD,VH,VCSPHI,VBETA,VHH,VM,VP,VQ,VCGDYN,VVT1INV,VW,
CPI  &      VFLAPS,VPHI,VPSIDG,VSNPSI,VAXB,VCSPSI,VDXX,VRD,
CPI  &      VAZB,VAYB,VPILTXCG,VPSI0D,VR,VQD,VZCG,VXCG,VWGD,VVGD,
C
CPI  &      YITAIL,YISHIP,YLGAUSN,
C
CPI  &      UAPT,UAPS,UAPSALT,UBADCV,UBALTV,UBALTC,UBPALRT,UBSAT,
CPI  &      UBVSI, UBTEST,UBVSIV,UDPLIM,UDRLIM,UDFALIGN,
C
CPI  &      IDUGTST(2),CWRATST,IDUCAGE,IDRCVG,IDRCGYR1(2),
CPI  &      IDUEDH,IDESTOP,IAUBFLX,IDUBSAT,IDUBFLX,
C
CPO  &      UBA213F,UBJ213F,UBA233F,UBJ233F,UGA165A,UGJ165A,
CPO  &      UGA164A,UGJ164A,UGA165B,UGJ165B,UGA164B,UGJ164B,
C
CPO  &      UJ$IAS(2),UJ$DRUM(2),UJ$VMO(2),UJDRUM,UJALT,UJIAS,
C
CPO  &      UH$DIM(2),UH$REF(2),UH$ALTV(2),UH$ALTC(2),
CPO  &      UH$ALTF(2),UH$ALTFC(2),
C
CPO  &      UCRS,UC$FLAG,UC$PITCH,UC$ROLL,UCROLL,UCPITCH,UCTP,UCTR,
CPO  &      UCPWR,UCRUNUP,UCRUNDN,UCPERCT,UCRERCT,UCSLOW,UCBATV,UCBTIM,
CPO  &      UCPLIM,UCRLIM,UCPTOPL,UCRTOPL,UCFALIGN,
C
CPO  &      UDPWR,UDVGV,UDRS,UDPERR,UDRERR,UDTP,UDTR,UDPITCH,UDROLL,
CPO  &      UDSLOW,UDRUNUP,UDRUNDN,UDPERCT,UDRERCT,UDPTOPL,UDRTOPL,
CPO  &      UDFAST,RC$FATT,
C
CPO  &      UE$SLIP(2),US$SLIP(2),US$TURN(2),UE$ATTV(2),UE$PITCH(2),
CPO  &      UE$ROLL(2),UETURN,UESLIP,UESSLIP,UEAZI,UEAYI,UE$DH,
CPO  &      UE$RA(2),UE$RAV(2),
C
CPO  &      UFREZ,UFIRST,
C
CPO  &      UGRA,UGPWR,UGRAV,UGEAR,UGLG2,UGTST,UGTRIP1,UGTRIP2,UGTRIP3,
CPO  &      UGTRIP4,UGLG1,
C
CPO  &      UF$CS0(2),UF$CS1(2),UF$CS3(2),UF$CS4(2),UF$CS5(2),
CPO  &      UF$CS6(2),UF$CS7(2),UF$ADCV(2),UBA212A(2),UBJ212A(2),
CPO  &      UBA232A(2),UBJ232A(2),
C
CPO  &      ED$SAT,ED$FLX,
C
CPO  &      SCSREQ,
C
CPO  &      UQ$ALTF,UQ$ALTC,UQCORR,UQALT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:00:08 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  IAUBFLX        !  SAT/FLEX temp potentiometer   [deg]  AI037
     &, RNPCHO(2)      ! AHRS PITCH                             [DEG]
     &, RNROLO(2)      ! AHRS ROLL                              [DEG]
     &, UAPS(10)       !  Sensed static pressure                [Hg]
     &, UAPSALT(10)    !  Alternate static pressure             [Hg]
     &, UAPT(10)       !  Pitot port pressure                   [Hg]
     &, UBALTC(3)      !  ADC Baro Corrected Altitude           [ft]
     &, UBSAT(3)       !  ADC Static Air Temperature         [deg C]
     &, UBVSI(3)       !  ADC Altitude Rate                 [ft/min]
     &, UDPLIM         !  Vertical Gyro pitch limit            [deg]
     &, UDRLIM         !  Vertical Gyro roll  limit            [deg]
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VAXB           ! BODY AXES TOTAL X ACC.             [ft/s**2]
     &, VAYB           ! BODY AXES TOTAL Y ACC.             [ft/s**2]
     &, VAZB           ! BODY AXES TOTAL Z ACC.             [ft/s**2]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VCGDYN         ! INST.A/C C.G.INCLUD.FUEL S
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSPSI         ! COSINE OF VPSI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VDXX           ! DISTANCE FROM REFERENCE C.G.  [fraction MAC]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VM             ! MACH NUMBER
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPILTXCG       ! X CG DIST TO PILOT                      [ft]
     &, VPSI0D         ! RATE OF CHANGE OF A/C HEADING        [rad/s]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
      REAL*4   
     &  VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VQD            ! BODY AXES PITCH ACCELERATION      [rad/s**2]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VRD            ! A/C YAW ACC. - BODY AXES          [rad/s**2]
     &, VSNPSI         ! SINE OF VPSI
     &, VSNTHE         ! SINE OF VTHETA
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
     &, VUA            ! BODY AXES X VELOCITY WRT AIR          [ft/s]
     &, VVGD           ! BODY AXES VAYB + GRAVITY           [ft/s**2]
     &, VVT1INV        ! INVERSE OF VVT1                       [s/ft]
     &, VW             ! TOTAL A/C WEIGHT                       [lbs]
     &, VWGD           ! BODY AXES VAZB + GRAVITY           [ft/s**2]
     &, VXCG           ! CENTRE OF GRAVITY                       [in]
     &, VZCG           ! Z-COORD OF C.G.                         [in]
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      INTEGER*4
     &  YISHIP         ! Ship name
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  MLATAXIS       ! TEST AXIS / AIRCRAFT CONFIGURATION
C$
      LOGICAL*1
     &  AMRLK1A        ! Avionic advisory lts relay
     &, BIAC08         ! AHRS 2                      34 PDAR   DI197B
     &, BIAE05(2)      ! ADC 1                       34 PDAL   DI195C
     &, BIAE09         ! DG 1  (100 ONLY)            34 PAAL   DI1986
     &, BIAF05         ! RAD ALT 1                   34 PDAL   DI195D
     &, BIAL03         ! VGH/CH SW  (100 ONLY)       34 PDAL   DI1947
     &, BILA05(2)      ! T & S IND 1                *34 PDLMN  DI2044
     &, BILA06         ! AC OSPD                     34 PDLMN  DI2055
     &, BILC08         ! SAT/FLX (300 ONLY)         *27 PDLMN  DI2079
     &, BILD04(2)      ! STAT PORT HTR 1             30 PDLMN  DI2036
     &, BILH04         ! STBY ATTD IND A             34 PDLES  DI203A
     &, BILH06(2)      ! PITOT HTR 1                 30 PDLES  DI205C
     &, BIRL07         ! STBY ATTD IND B             34 PDRES  DI2230
     &, CWRATST        ! STALL WARN TEST SIGNAL TO RADIO ALTIMETER
     &, IDESTOP        ! Engine ECU selector @ TOP             DI0090
     &, IDRCGYR1(2)    ! GYRO 1 SELECTED                       DI0066
     &, IDRCVG         ! VG SELECTED                           DI0068
     &, IDUBFLX        !  SAT/FLEX temp sw FLEX selected       DI0095
     &, IDUBSAT        !  SAT/FLEX temp sw SAT selected        DI0094
     &, IDUCAGE        !  Stby ADI caging knob                 DI028C
     &, IDUEDH         !  Capt ADI DH gnd output (pin D)       DI029E
     &, IDUGTST(2)     !  Pilot rad alt test                   DI029D
     &, MLATINIT       ! LATENCY INITIALIZATION FLAG
     &, MTHPUT         ! THROUGHPUT DELAY MODE FLAG
     &, RNPCHVO(2)     ! AHRS PITCH VALIDITY                      [-]
     &, T030051(2)     ! PROBE HEATING, PITOT FAIL 1
     &, T031051(2)     ! ADC FAIL 1
     &, T031061(2)     ! IAS UNRELIABLE CAPT
     &, T031071(2)     ! ALTIMETER FAIL CAPT
     &, T031081(2)     ! VERTICAL SPEED INDICATOR FAIL CAPT
     &, T031091        ! STANDBY HORIZON FAIL
      LOGICAL*1
     &  T031141        ! RADIO ALT FAIL
     &, T034A11(2)     ! STATIC SYSTEM BLOCKED CAPT
     &, T034A41(2)     ! ADC SELF TEST IN FLIGHT 1
     &, T034A51(2)     ! SAT DISPLAY FAILS 1
     &, T034A61(2)     ! ADI FROZEN CAPT
     &, T034I031       ! SHRS FAILS ATT
     &, TCFALT         ! FREEZE/ALTITUDE  (01)
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TF30051(2)     ! PROBE HEATING, PITOT FAIL 1
     &, TF31051(2)     ! ADC FAIL 1
     &, TF31061(2)     ! IAS UNRELIABLE CAPT
     &, TF31071(2)     ! ALTIMETER FAIL CAPT
     &, TF31081(2)     ! VERTICAL SPEED INDICATOR FAIL CAPT
     &, TF31091        ! STANDBY HORIZON FAIL
     &, TF31141        ! RADIO ALT FAIL
     &, TF34A11(2)     ! STATIC SYSTEM BLOCKED CAPT
     &, TF34A41(2)     ! ADC SELF TEST IN FLIGHT 1
     &, TF34A51(2)     ! SAT DISPLAY FAILS 1
     &, TF34A61(2)     ! ADI FROZEN CAPT
     &, TF34I031       ! SHRS FAILS ATT
     &, UBADCV(3)      !  ADC valid
     &, UBALTV(3)      !  ADC altitude valid
     &, UBPALRT(3)     !  ADC altiude alert light
     &, UBTEST(3)      !  ADC test enable flag
     &, UBVSIV(3)      !  ADC VSI valid
     &, UDFALIGN       !  Vertical Gyro fast align
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  UBA212A(2)     ! ALTITUDE RATE            0  25 ADC1  15  15 3
     &, UBA213F        ! STATIC AIR TEMP          0   2 ADCSF 13   9 2
     &, UC$PITCH       !  Stby ADI pitch               [deg]   TO116
     &, UC$ROLL        !  Stby ADI roll                [deg]   TO118
     &, UCBATV         !  Stby Horizon Battery voltage        [volt]
     &, UCBTIM         !  Battery charged left time            [sec]
     &, UCPERCT        !  Stby Horizon fast erection rate  [deg/sec]
     &, UCPITCH        !  Stby Horizon Display Pitch Angle     [deg]
     &, UCPLIM         !  Pitch topple limit                   [deg]
     &, UCPTOPL        !  Stby Horizon pitch topple rate   [deg/sec]
     &, UCRERCT        !  Stby Horizon fast erection rate  [deg/sec]
     &, UCRLIM         !  Roll topple limit                    [deg]
     &, UCROLL         !  Stby Horizon Display Roll  Angle     [deg]
     &, UCRS           !  Stby Horizon Rotor Speed             [0-1]
     &, UCRTOPL        !  Stby Horizon roll  topple rate   [deg/sec]
     &, UCRUNDN        !  Stby Horizon Gyro Run Down Speed   [%/min]
     &, UCRUNUP        !  Stby Horizon Gyro Run Up   Speed   [%/min]
     &, UCSLOW         !  Stby Horizon Slow Erection Rate  [deg/sec]
     &, UCTP           !  Stby Horizon Pitch Angle             [deg]
     &, UCTR           !  Stby Horizon Roll  Angle             [deg]
     &, UDFAST         !  Vertical Gyro Slow Erection Rate
     &, UDPERCT        !  Vertical Gyro fast erection rate [deg/sec]
     &, UDPERR(3)      !  Vertical Gyro Pitch error            [deg]
     &, UDPITCH(3)     !  Vertical Gyro Pitch Display          [deg]
     &, UDPTOPL        !  Vertical Gyro pitch topple rate  [deg/sec]
     &, UDRERCT        !  Vertical Gyro fast erection rate [deg/sec]
     &, UDRERR(3)      !  Vertical Gyro Roll  error            [deg]
     &, UDROLL(3)      !  Vertical Gyro Roll  Display          [deg]
     &, UDRS(3)        !  Vertical Gyro  Rotor Speed           [0-1]
     &, UDRTOPL        !  Vertical Gyro roll  topple rate  [deg/sec]
     &, UDRUNDN        !  Vertical Gyro Run Down Speed
      REAL*4   
     &  UDRUNUP        !  Vertical Gyro Run Up Speed
     &, UDSLOW         !  Vertical Gyro Slow Erection Rate [deg/sec]
     &, UDTP(3)        !  Vertical Gyro Pitch Angle            [deg]
     &, UDTR(3)        !  Vertical Gyro Roll  Angle            [deg]
     &, UE$PITCH(2)    !  Capt ADI pitch input         [deg]   SO016
     &, UE$RA(2)       !  Capt ADI rad alt              [ft]   AO105
     &, UE$ROLL(2)     !  Capt ADI roll input          [deg]   SO017
     &, UE$SLIP(2)     !  Capt ADI sideslip                    AO104
     &, UEAYI          !  Y-Axis Cockpit Acceleration    [ft/sec**2]
     &, UEAZI          !  Z-Axis Cockpit Acceleration    [ft/sec**2]
     &, UESLIP         !  Slipball ouput (lagged)              [deg]
     &, UESSLIP        !  Slip angle (not lagged)              [deg]
     &, UETURN         !  Rate of turn                         [deg]
     &, UGA164A        ! RADIO HEIGHT             0  25 LRRA1 16  13 1
     &, UGA164B        ! RADIO HEIGHT             0  25 LRRA2 16  13 1
     &, UGEAR          !  Gear offset compensation              [ft]
     &, UGLG1          !  RA antenna to CG dist (x-axis)        [ft]
     &, UGLG2          !  RA antenna to CG dist (z-axis)        [ft]
     &, UGRA(3)        !  Radio altitude                        [ft]
     &, UH$ALTC(2)     !  Capt coarse altitude          [ft]   AO115
     &, UH$ALTF(2)     !  Capt fine altitude sine       [ft]   CO006
     &, UH$ALTFC(2)    !  Capt fine altitude cos        [ft]   CO007
     &, UJ$DRUM(2)     !  Capt airspeed ind drum      [0-99]   TO102
     &, UJ$IAS(2)      !  Capt airspeed ind pointer    [kts]   TO100
     &, UJ$VMO(2)      !  Capt airspeed ind VMO        [kts]   TO098
     &, UJALT(2)       !  Airspeed ind pressure alt             [ft]
     &, UJDRUM(2)      !  Airspeed indicator drum             [0-99]
     &, UJIAS(2)       !  Airpseed                             [kts]
     &, UQ$ALTC        !  Stby altimeter coarse         [ft]   TO032
     &, UQ$ALTF        !  Stby altimeter fine                  TO034
     &, UQALT          !  Stby Altimeter Pressure Altitude      [ft]
      REAL*4   
     &  UQCORR         !  Stby Altimeter Baro Correction        [ft]
     &, US$SLIP(2)     !  Capt turn & sip ind slip             AO097
     &, US$TURN(2)     !  Capt turn & sip ind turn             AO096
C$
      INTEGER*4
     &  UBA232A(2)     ! ALTITUDE RATE                8 ADC1   4     2
     &, UBA233F        ! STATIC AIR TEMP              4 ADCSF  3     2
     &, UGA165A        ! RADIO HEIGHT                 4 LRRA1  4     1
     &, UGA165B        ! RADIO HEIGHT                 4 LRRA2  4     1
C$
      LOGICAL*1
     &  ED$FLX         ! Eng. 1&2 MAX/REDUCED power FLX lt     DO075F
     &, ED$SAT         ! Eng. 1&2 MAX/REDUCED power SAT lt     DO075E
     &, RC$FATT        ! ATTITUDE FAIL ON GYRO PANEL           DO0372
     &, SCSREQ(128)    ! IOCB REQUEST LABELS
     &, UC$FLAG        !  Stby ADI gyro flag                   DO020D
     &, UCFALIGN       !  Stby Horizon fast align
     &, UCPWR          !  Stby Horizon Power Validity
     &, UDPWR(3)       !  Vertical Gyro Power Validity
     &, UDVGV(3)       !  Vertical Gyro Attitude Validity
     &, UE$ATTV(2)     !  Capt ADI attitude valid              DO017A
     &, UE$DH          !  F/o  ADI dh input (pin D)            DO0245
     &, UE$RAV(2)      !  Capt ADI rad alt valid               DO017C
     &, UF$ADCV(2)     !  Capt VSI/TA/RA adc valid             DO019B
     &, UF$CS0(2)      !  Capt VSI/TA/RA config strap 0        DO019C
     &, UF$CS1(2)      !  Capt VSI/TA/RA config strap 1        DO019D
     &, UF$CS3(2)      !  Capt VSI/TA/RA config strap 3        DO019F
     &, UF$CS4(2)      !  Capt VSI/TA/RA config strap 4        DO0200
     &, UF$CS5(2)      !  Capt VSI/TA/RA config strap 5        DO0201
     &, UF$CS6(2)      !  Capt VSI/TA/RA config strap 6        DO0202
     &, UF$CS7(2)      !  Capt VSI/TA/RA config strap 7        DO0203
     &, UFIRST         !  UFI First Pass Flag
     &, UFREZ          !  UFI Freeze Flag
     &, UGPWR(3)       !  Radio altimeter power
     &, UGRAV(3)       !  Radio altimeter validity
     &, UGTRIP1(3)     !  Radio altimeter trip #1
     &, UGTRIP2(3)     !  Radio altimeter trip #2
     &, UGTRIP3(3)     !  Radio altimeter trip #3
     &, UGTRIP4(3)     !  Radio altimeter trip #4
     &, UGTST(3)       !  Radio altimeter o/p test
     &, UH$ALTV(2)     !  Capt altm alt valid                  DO0196
     &, UH$DIM(2)      !  Capt altm annunciator dim            DO0667
      LOGICAL*1
     &  UH$REF(2)      !  Capt altm +15V ref                   DO0195
C$
      INTEGER*1
     &  UBJ212A(2)     ! ALTITUDE RATE            0  25 ADC1  15  15 3
     &, UBJ213F        ! STATIC AIR TEMP          0   2 ADCSF 13   9 2
     &, UBJ232A(2)     ! ALTITUDE RATE                8 ADC1   4     2
     &, UBJ233F        ! STATIC AIR TEMP              4 ADCSF  3     2
     &, UGJ164A        ! RADIO HEIGHT             0  25 LRRA1 16  13 1
     &, UGJ164B        ! RADIO HEIGHT             0  25 LRRA2 16  13 1
     &, UGJ165A        ! RADIO HEIGHT                 4 LRRA1  4     1
     &, UGJ165B        ! RADIO HEIGHT                 4 LRRA2  4     1
C$
      LOGICAL*1
     &  DUM0000001(32),DUM0000002(1196),DUM0000003(3652)
     &, DUM0000004(8),DUM0000005(512),DUM0000006(60)
     &, DUM0000007(3170),DUM0000008(2),DUM0000009(2)
     &, DUM0000010(4),DUM0000011(2),DUM0000012(2),DUM0000013(4)
     &, DUM0000014(236),DUM0000015(76),DUM0000016(2451)
     &, DUM0000017(971),DUM0000018(2),DUM0000019(159)
     &, DUM0000020(84),DUM0000021(516),DUM0000022(58)
     &, DUM0000023(273),DUM0000024(1),DUM0000025(75)
     &, DUM0000026(12),DUM0000027(2),DUM0000028(1)
     &, DUM0000029(544),DUM0000030(2380),DUM0000031(104)
     &, DUM0000032(4),DUM0000033(16),DUM0000034(112)
     &, DUM0000035(8),DUM0000036(284),DUM0000037(12)
     &, DUM0000038(12),DUM0000039(8),DUM0000040(120)
     &, DUM0000041(84),DUM0000042(124),DUM0000043(4)
     &, DUM0000044(12),DUM0000045(4),DUM0000046(8)
     &, DUM0000047(4),DUM0000048(16),DUM0000049(4)
     &, DUM0000050(240),DUM0000051(32),DUM0000052(916)
     &, DUM0000053(916),DUM0000054(108),DUM0000055(4)
     &, DUM0000056(20),DUM0000057(56),DUM0000058(8)
     &, DUM0000059(2958),DUM0000060(336),DUM0000061(76)
     &, DUM0000062(15),DUM0000063(15),DUM0000064(31)
     &, DUM0000065(63),DUM0000066(60),DUM0000067(156)
     &, DUM0000068(96),DUM0000069(61),DUM0000070(8)
     &, DUM0000071(4),DUM0000072(20),DUM0000073(2)
     &, DUM0000074(3),DUM0000075(2),DUM0000076(7119)
     &, DUM0000077(64211),DUM0000078(104),DUM0000079(8896)
     &, DUM0000080(201274),DUM0000081(13663),DUM0000082(94)
     &, DUM0000083(2),DUM0000084(1),DUM0000085(2),DUM0000086(2)
     &, DUM0000087(12),DUM0000088(421),DUM0000089(94)
     &, DUM0000090(2),DUM0000091(1),DUM0000092(2),DUM0000093(2)
     &, DUM0000094(14),DUM0000095(320),DUM0000096(6651)
      LOGICAL*1
     &  DUM0000097(32),DUM0000098(24),DUM0000099(105)
     &, DUM0000100(8),DUM0000101(6),DUM0000102(36)
     &, DUM0000103(12),DUM0000104(40),DUM0000105(4)
     &, DUM0000106(3),DUM0000107(10)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YISHIP,YITAIL,DUM0000002,YLGAUSN,DUM0000003
     &, UE$RA,DUM0000004,UE$SLIP,US$SLIP,US$TURN,UH$ALTC,UH$ALTF
     &, UH$ALTFC,DUM0000005,UC$PITCH,UC$ROLL,UJ$VMO,UJ$DRUM,UJ$IAS
     &, UQ$ALTC,UQ$ALTF,DUM0000006,UE$PITCH,UE$ROLL,DUM0000007
     &, UC$FLAG,UH$REF,UH$ALTV,DUM0000008,UH$DIM,UE$DH,DUM0000009
     &, UE$RAV,DUM0000010,UE$ATTV,DUM0000011,UF$CS0,UF$CS1,DUM0000012
     &, UF$CS3,UF$CS4,UF$CS5,UF$CS6,UF$CS7,DUM0000013,UF$ADCV
     &, DUM0000014,RC$FATT,DUM0000015,ED$FLX,ED$SAT,DUM0000016
     &, IAUBFLX,DUM0000017,IDUCAGE,IDUEDH,IDUGTST,DUM0000018
     &, IDUBSAT,IDUBFLX,DUM0000019,IDRCGYR1,IDRCVG,DUM0000020
     &, IDESTOP,DUM0000021,BILC08,DUM0000022,BILA05,DUM0000023
     &, BILH06,DUM0000024,BILD04,DUM0000025,BILA06,DUM0000026
     &, BIAC08,DUM0000027,BIAF05,BIAL03,DUM0000028,BIAE05,BIAE09
     &, BILH04,BIRL07,DUM0000029,MLATINIT,MTHPUT,MLATAXIS,DUM0000030
     &, VFLAPS,DUM0000031,VAZB,DUM0000032,VWGD,DUM0000033,VALPHA
     &, DUM0000034,VAYB,VVGD,DUM0000035,VBETA,DUM0000036,VAXB
     &, DUM0000037,VUA,DUM0000038,VVT1INV,DUM0000039,VM,DUM0000040
     &, VP,DUM0000041,VQD,VQ,DUM0000042,VRD,DUM0000043,VR,DUM0000044
     &, VPHI,VPHIDG,DUM0000045,VCSPHI,DUM0000046,VTHETADG,VSNTHE
     &, VCSTHE,DUM0000047,VPSI0D,DUM0000048,VPSIDG,DUM0000049
     &, VSNPSI,VCSPSI,DUM0000050,VZD,VHH,DUM0000051,VH,DUM0000052
     &, VTEMP,DUM0000053,VW,DUM0000054,VDXX,DUM0000055,VXCG,DUM0000056
     &, VPILTXCG,DUM0000057,VCGDYN,DUM0000058,VZCG,DUM0000059
     &, UFREZ,UFIRST,DUM0000060,UAPSALT,UAPS,UAPT,DUM0000061
     &, UBADCV,DUM0000062,UBALTV,DUM0000063,UBVSIV,DUM0000064
     &, UBTEST,DUM0000065,UBALTC,DUM0000066,UBSAT,DUM0000067
     &, UBVSI,DUM0000068,UBPALRT,DUM0000069,UJIAS,DUM0000070
     &, UJDRUM,UJALT,DUM0000071,UQALT,UQCORR,DUM0000072,UCPWR
     &, UCFALIGN,DUM0000073,UCRS,UCTP,UCTR,UCPITCH,UCROLL,UCRUNUP
     &, UCRUNDN,UCPERCT,UCRERCT,UCPTOPL,UCRTOPL,UCSLOW,UCPLIM
      COMMON   /XRFTEST   /
     &  UCRLIM,UCBATV,UCBTIM,UGPWR,UGRAV,UGTST,UGTRIP1,UGTRIP2
     &, UGTRIP3,UGTRIP4,DUM0000074,UGRA,UGEAR,UGLG1,UGLG2,UEAYI
     &, UEAZI,UESLIP,UESSLIP,UETURN,UDPWR,UDVGV,DUM0000075,UDRS
     &, UDPERR,UDRERR,UDTP,UDTR,UDPITCH,UDROLL,UDSLOW,UDRUNUP
     &, UDRUNDN,UDPERCT,UDRERCT,UDPTOPL,UDRTOPL,UDFAST,UDPLIM
     &, UDRLIM,UDFALIGN,DUM0000076,CWRATST,DUM0000077,RNPCHO
     &, RNROLO,DUM0000078,RNPCHVO,DUM0000079,AMRLK1A,DUM0000080
     &, TCFFLPOS,TCFALT,DUM0000081,TF30051,DUM0000082,TF31051
     &, TF34A41,TF31071,DUM0000083,TF31061,TF31141,DUM0000084
     &, TF31091,TF31081,TF34A11,DUM0000085,TF34A51,DUM0000086
     &, TF34A61,DUM0000087,TF34I031,DUM0000088,T030051,DUM0000089
     &, T031051,T034A41,T031071,DUM0000090,T031061,T031141,DUM0000091
     &, T031091,T031081,T034A11,DUM0000092,T034A51,DUM0000093
     &, T034A61,DUM0000094,T034I031,DUM0000095,SCSREQ,DUM0000096
     &, UBA212A,DUM0000097,UBA213F,DUM0000098,UGA164A,UGA164B
     &, DUM0000099,UBJ212A,DUM0000100,UBJ213F,DUM0000101,UGJ164A
     &, UGJ164B,DUM0000102,UBA232A,DUM0000103,UBA233F,DUM0000104
     &, UGA165A,UGA165B,DUM0000105,UBJ232A,DUM0000106,UBJ233F
     &, DUM0000107,UGJ165A,UGJ165B   
C------------------------------------------------------------------------------
C
C
C------------------------------------------------------------------------------
C
C     This module should be run on the 33 msec band.
C     It may be frozen by setting UFREZ = .true.
C
C     INTRODUCTION
C     ------------
C
C     PURPOSE : To simulate the aircraft systems that drive the various
C               flight instuments, and to drive the flight instruments
C               on the simulator through the interface.
C
C     THEORY  : The aircraft attitude and environment from flight
C               are used to derive the signals normally received by the
C               flight instruments on a real aircraft under similar
C               circumstances. Instructor generated malfunctions are
C               included as well.
C
C     INPUTS  : The inputs come from standard atmosphere module, flight
C               instructor facilities, ECS, ancillaries, electrics,
C               the interface and the executive.
C
C     OUTPUTS : Most outputs are to the interface for various flight
C               instruments.
C
C'Local_Variables
C
C
C
C
      INTEGER*1  FAIL(2)        !  SDI/SSM messages
     &          ,NCD(2)         !  SDI/SSM messages
     &          ,NDS(2)         !     "       "
     &          ,NEG(2)         !     "       "
     &          ,NORM(2)        !     "       "
     &          ,POS(2)         !     "       "
     &          ,TEST(2)        !     "       "
 
      INTEGER*4   CTR              !  iteration counter
     &           ,CTR2             !  iteration counter
     &           ,I1(3)            !  pointer for interpolation
     &           ,I2(3)            !     "     "       "
     &           ,I3(3)            !     "     "       "
     &           ,I                ! indexing utility integers
     &           ,J/1/             !    "        "       "
     &           ,K                !    "        "       "
     &           ,JI1(2)           !  pointer for interpolation (UJ)
     &           ,JI3(2)           !  pointer for interpolation (UJ)
     &           ,QI1              !  pointer for interpolation (UQ)
     &           ,QI3              !     "     "        "         "
     &           ,SYS              !  SCALING SYSTEM
 
      REAL*4      A                !  temporary storage utility reals
     &           ,ALTF(2)          !  fine altitude (ft)
     &           ,ALTFRAD(2)       !  fine altitude (rad)
     &           ,B1(134)          !  Y1 intercept
     &           ,B2(46)           !  Y2 intercept
     &           ,B3(26)           !  Y3 intercept
     &           ,DELTHETA         !  Delta pitch angle
     &           ,DELPHI           !  Delta roll angle
     &           ,DELTA_ALT(30,3)  !  List of delta altitudes
     &           ,FPI_TO_FPM       !  ft per iter to ft per min conversion
     &           ,FLXTMP           !  flex temp from knob [deg C]
     &           ,FLXLAG           !  flex temp lag factor
     &           ,IASLIM(2)        !  IAS deviation limit
     &           ,ILAG             !  IAS malf lag
     &           ,L1(134)          !  inverse of slope M1
     &           ,L2(46)           !  inverse of slope M2
     &           ,LEAK             !  pitot leak factor
     &           ,LG1              !  r/a Xeiver x-axis offset (c.g.)
     &           ,LGRA             !  aircraft radio altitude
     &           ,M1(134)          !  slope of X1 Y1
     &           ,M2(46)           !  slope of X2 Y2
     &           ,M3(26)           !  slope of X3 Y3
     &           ,PRETHETA         !  previous value of pitch angle
     &           ,PREPHI           !  previous value of roll angle
     &           ,QALT             !  pressure altitude to instruments
     &           ,RABS             !  radio alt antenna body station [inches]
     &           ,RAWL             !  radio alt antenna water line   [inches]
     &           ,RUNTIME          !  run time
     &           ,SCRATCH          !  Scratch
     &           ,SLIPLAG          !  sideslip lag
     &           ,UDTHETA          !  modified flight pitch angle (deg)
     &           ,UDPHI            !  modified flight roll  angle (deg)
     &           ,UEAZILIM         !
     &           ,X1(135)          !  pressure altitude [ft]
     &           ,X2(47)           !  mach number
     &           ,X3(27)           !  dynamic pressure  [Hg]
     &           ,XM(6)            !  MACH number
     &           ,X                !  temporary storage utility reals
     &           ,Y1(135)          !  static pressure   [Hg]
     &           ,Y2(47)           !  pressure ratio (dynamic/static)
     &           ,Y3(27)           !  calibrated airspd [kts]
     &           ,SLIPSCAL /1.0/   !  scaling factor for slip balls (-300)
     &           ,Y                !  temporary storage utility reals
C
      LOGICAL*1   AWD8             !  AW dash 8/100 flag
     &           ,FLXDISC          !  flex discrete
     &           ,INHIB(3)         !  r/a test inhibit
     &           ,LSCRATCH         !  scratch pad
     &           ,RATEST(3)        !  r/a test requested flag
     &           ,TRACK            !  radio altimeter in track mode
     &           ,RASTART(3)       !  r/a test starts
     &           ,RTSTINIT(3)      !  r/a test init flag
     &           ,TSPWR(2)         !  turn & slip power
     &           ,USD1             !  USAIR dash 8/100 flag
     &           ,USD3             !  USAIR dash 8/300 flag
     &           ,VGERCT           !  vertical gyro erect in progress flag
     &           ,Y66              !  flag for 66 ms band
     &           ,Y132(4)          !  flags for 132 ms band
     &           ,Y264(8)          !  flag for 264 ms band
C
      COMMON /STDUFI/X1,Y1,M1,B1,X2,Y2,M2,B2,X3,Y3,M3,B3
C
C'Constants
C
      REAL*4 LATPIT/90./ , LATROLL/90./
      REAL*4 MB
      PARAMETER (MB = 33.86363636)       ! conversion for HG to MB
      REAL*4 HG
      PARAMETER (HG = 0.029530201)       ! conversion for MB to HG
      REAL*4 INTOFT
      PARAMETER (INTOFT = 0.08333333)    ! conversion for inches to ft
      REAL*4 RADTODEG
      PARAMETER (RADTODEG=57.296)        ! conversion for rad to deg
      REAL*4 DEGTORAD
      PARAMETER (DEGTORAD= 0.017453292)  ! conversion for deg to rad
      REAL*4 KG
      PARAMETER (KG=0.45359)             ! conversion for lbs to kg
      INTEGER*4 NO_ITEMS
      PARAMETER (NO_ITEMS=30)            ! no of items in altitude list
C
C
C     ########################
C     # INTERPOLATION TABLES #
C     ########################
C
C  Pressure (Hg) vs. altitude (ft)
C  -------------------------------
C
C   From UFISTD X1 and Y1
C
C  Pressure ratio (dyn/stat Hg) vs Mach number
C  -------------------------------------------
C
C   From UFISTD X2 and Y2
C
C  Calibrated airspeed (knots) vs. dynamic pressure (Hg)
C  -----------------------------------------------------
C
C   From UFISTD X3 and Y3
C
      DATA SYS /'04'X/
C
      DATA FAIL /X'08',X'10'/
      DATA POS  /X'08',X'10'/
      DATA NCD  /X'0A',X'12'/
      DATA TEST /X'0C',X'14'/
      DATA NORM /X'0E',X'16'/
      DATA NEG  /X'0E',X'16'/
C
      ENTRY UFI
C
      CALL SCALIN(SYS)
C
C         ##################
C         # INITIALIZATION #
C         ##################
C
      IF ( UFREZ ) THEN
        CALL SCALOUT(SYS)
        RETURN
      ENDIF
C
      IF (UFIRST) THEN
C
C -- Establish A/C type
C
C       AWD8 =
        USD1 = YITAIL.EQ.226       !USAIR dash 8/100
        USD3 = YITAIL.EQ.230       !USAIR dash 8/300
C
        FPI_TO_FPM = ((1./YITIM)/NO_ITEMS) * 60.
C
        ILAG = YITIM*2.0
C
C  -- Standby attitude constants
C
        UCRUNUP = 4.*YITIM * (1.0/180.) ! 100%/3 min
        UCRUNDN = 4.*YITIM * (.5/540.)  ! 50%/9 min
        UCPLIM  = 35.                   ! Pitch topple limit
        UCRLIM  = 135.                  ! roll topple limit
        UCPERCT = YITIM * (UCPLIM/1.5)  ! Pitch topple lim /1.5 sec to erect
        UCRERCT = YITIM * (UCRLIM/1.5) ! Roll  topple lim /1.5 sec to erect
        UCSLOW  = YITIM * (3.0/60.)     ! 3 deg/min slow erect
        UCPTOPL = YITIM * (UCPLIM/30.)  ! Pitch topple lim /30 sec to topple
        UCRTOPL = YITIM * (UCRLIM/30.)  ! Roll  topple lim /30 sec to topple
        UCPITCH = UCPLIM
        UCROLL  = UCRLIM
C
C  -- Vertical gyro constants
C
        UDFALIGN = .TRUE.
 
        UDRUNUP = 4.*YITIM * (1.0/180.) ! 100%/3 min
        UDRUNDN = 4.*YITIM * (.5/1800.) ! 50%/30 min
        UDPLIM  = 40.                   ! Pitch topple limit
        UDRLIM  = 40.                   ! roll topple limit
        UDPERCT = YITIM * (20./60.)     ! 20 deg/min to erect
        UDRERCT = YITIM * (20./60.)     ! 20 deg/min to erect
        UDSLOW  = YITIM * (3.0/60.)     ! 3 deg/min slow erect
        UDPTOPL = YITIM * (UCPLIM/30.)  ! Pitch topple lim /30 sec to topple
        UDRTOPL = YITIM * (UCRLIM/30.)  ! Roll  topple lim /30 sec to topple
        UDPITCH(1) = UDPLIM
        UDROLL(1)  = UDRLIM
C
C -- Sideslip lag constant
C
C !FM+
C !FM  30-Jul-92 21:15:54 paul van esbroeck
C !FM    < slip ball is a little to active according to USAir and Ron Smith
C !FM      of CAE so we are making it more lagged >
C !FM
c        SLIPLAG = 1.0 - EXP(-YITIM*3.0) ! about 1/3 second
        SLIPLAG = 1.0 - EXP(-YITIM*2.66) ! about 1/3 second
C !FM-
        UEAZILIM = -14.0                ! UEAZI limit
C
C -- FLX temp potentiometer lag constant
C
        FLXLAG = 1.0 - EXP(-YITIM*3.0) ! about 1/3 second
C
C -- Radio altimeter system constants
C
        UGEAR = 2.0               ! gear offset [ft]
C
        IF (USD3) THEN
          RABS = 104.4            ! body station [in]
        ELSE
          RABS = 177.4
        ENDIF
C
        RAWL = 81.0               ! water line (a/c belly) [in]
C
C  -- VSI/TRA configuration strap
C
        DO I = 1,2
C !FM+
C !FM  15-Apr-92 07:45:03 M.WARD
C !FM    < TRYING TO DRIVE VSI WITH HIGH SPEED ARINC 429 >
C !FM
CMW          UF$CS0(I) = .TRUE.      ! Arinc low speed 429 primary
CMW          UF$CS1(I) = .TRUE.
          UF$CS0(I) = .FALSE.
          UF$CS1(I) = .FALSE.
C !FM-
          UF$CS4(I) = .FALSE.     ! VSI/RA/TA
          UF$CS5(I) = .FALSE.
          UF$CS6(I) = .FALSE.
C
C  -- Interpolation pointer initialization
C
         JI1(I) = 2
         JI3(I) = 2
        ENDDO
C
        QI1 = 2
        QI3 = 2
C
        DO I = 1,3
          I1(I) = 2
          I2(I) = 2
          I3(I) = 2
        ENDDO
C
C
C  -- Reciprocal of slopes as required
C
        DO J=1,134
          L1(J)=1.0/M1(J)
        ENDDO
C
        DO J=1,44
          L2(J)=1.0/M2(J)
        ENDDO
C
        UFIRST = .FALSE.
C
C -- Malfunctions dark concept
C
        T031061(1) = .TRUE.
        T031061(2) = .TRUE.
        T031091    = .TRUE.
        T031141    = .TRUE.
        T034I031   = .TRUE.
C
      ENDIF
C
C  Flag for half speed band - 66 msecs
C
      Y66 = .NOT. Y66
C
C  Flags for quarter speed band - 132 msecs
C
      CTR = CTR + 1
      IF (CTR.GT.4) CTR=1
      Y132(1) = CTR.EQ.1
      Y132(2) = CTR.EQ.2
      Y132(3) = CTR.EQ.3
      Y132(4) = CTR.EQ.4
C
C  Flags for 1/8 speed band - 264 msecs
C
C
      CTR2 = CTR2 + 1
      IF (CTR2.GT.8) CTR2=1
      Y264(1) = CTR2.EQ.1
      Y264(2) = CTR2.EQ.2
      Y264(3) = CTR2.EQ.3
      Y264(4) = CTR2.EQ.4
      Y264(5) = CTR2.EQ.5
      Y264(6) = CTR2.EQ.6
      Y264(7) = CTR2.EQ.7
      Y264(8) = CTR2.EQ.8
C
C        #####################
C        # SAT/FLX indicator #
C        #####################
C
CD  UT010  SAT/FLX indicator
C          -----------------
C
      IF ( USD3 ) THEN        ! process only for 300 series
C
C --    FLX discrete to ADC
C
        FLXDISC = IDUBFLX .AND. IDESTOP .AND. BILC08 ! flx selected and ecu in
C                                                    ! top position
C
C --    SAT/FLX light on engine instrument panel
C
        ED$SAT = BILC08 .AND. .NOT. ( IDUBFLX .OR. IDESTOP )
        ED$FLX = BILC08 .AND. IDUBFLX .AND. IDESTOP
C
C --  determine which adc is valid
C
        IF (UBADCV(1)) THEN
          J = 1
        ELSE IF (UBADCV(2)) THEN
          J = 2
        ELSE
          J=1
        ENDIF
C
C -- establish whether FLX or SAT temp is selected
C
        IF (FLXDISC) THEN
C
          FLXTMP = FLXTMP + FLXLAG * (IAUBFLX - FLXTMP)  !get input of temp
C                                                       from potentiometer
C
          UBA213F = FLXTMP
          UBA233F = UBA213F
 
          UBJ213F = NORM(J)              !set ssm/sdi
C
          IF (FLXTMP.LT.0.) THEN
            UBJ233F = NEG(1)
          ELSE
            UBJ233F = POS(1)
          ENDIF
C
        ELSE
C
          UBA213F = UBSAT(J)
          IF (UBTEST(J)) THEN    !set ssm/sdi
            UBJ213F = TEST(J)
            UBJ233F = TEST(J)
          ELSE
            UBJ213F = NORM(J)
            IF (UBSAT(J).LT.0.) THEN
              UBJ233F = NEG(1)
            ELSE
              UBJ233F = POS(1)
            ENDIF
          ENDIF
        ENDIF
      ENDIF
C
      SCSREQ(11) = .NOT.UBADCV(J)    !ADCSF
C
      DO I = 1,2
C
C        #################################
C        # VSI/TRA vertical speed inputs #
C        #################################
C
CD  UF010  VSI/TRA vertical speed inputs
C          -----------------------------
C
        SCSREQ(3) = .NOT.UBADCV(1)     !ADC1
        SCSREQ(9) = .NOT.UBADCV(2)     !ADC2
C
C
        IF (UBVSIV(I).AND.UBADCV(I)) THEN
C
          UF$ADCV(I) = .TRUE.          !alt 1 valid
          UBA212A(I) = UBVSI(I)        !update vsi input
          UBA232A(I) = UBVSI(I)        !update vsi input
 
          IF (UBTEST(I)) THEN          !set ssm/sdi to test
            UBJ212A(I) = TEST(I)
            UBJ232A(I) = TEST(I)
          ELSE
            UBJ212A(I) = NORM(I)       !set ssm/sdi to normal
            IF (UBVSI(I).LT.0.0) THEN
              UBJ232A(I) = NEG(I)
            ELSE
              UBJ232A(I) = POS(I)
            ENDIF
          ENDIF
        ELSE
C
C -- Input invalid from ADC
C
          UF$ADCV(I) = .FALSE.         !alt 1 invalid
 
          UBJ212A(I) = NCD(I)          !set ssm/sdi to NCD
          UBJ232A(I) = NCD(I)
        ENDIF
C
C        ##############
C        # ALTIMETERS #
C        ##############
C
C
CD  UH010  Altimeters validities
C          ---------------------
C
        UH$REF(I) = UBADCV(I)      !15V reference voltage from ADC
        UH$ALTV(I)= UBALTV(I)      !altitude valid flag
C
C
CD  UH020  Altimeters altitude outputs
C          ---------------------------
C
C -- The coarse input is an analog input. Since the card supplies only 10 volts
C    to the instrument which requires 15 volts a voltage multiplier is used by
C    h/w. The calibration is done by dividing the expected voltage by 1.5.
C    (ref Honeywell 4016341 Engineering specifications)
C
        UH$ALTC(I)= UBALTC(I)
C
C --fine (360deg = 5000 ft)
C
        ALTF(I)= UBALTC(I)-5000.0*INT((UBALTC(I)+2500.0)*0.0002)
C
C -- converts fine altitude from feet to degrees (outputs in radians)
C
        ALTFRAD(I) = (0.072*ALTF(I)) * DEGTORAD
C
C
C -- send sine and cosine of the angle to the instrument (angle must be in
C    radians for sin cos functions)
C
        UH$ALTF(I)   = SIN(ALTFRAD(I))
        UH$ALTFC(I)  = COS(ALTFRAD(I))
C
CD  UH030  Altimeters altitude alert light
C          -------------------------------
C
C -- Pilot and copilot are connected to both ADC's.
C    The ADC or the light test provide a ground to allow the
C    light to be set. Power to the light is controlled by the FGC
C    and is there when in ALT SEL mode.
C
        UH$DIM(I) = UBPALRT(1).OR.UBPALRT(2).OR.AMRLK1A
C
C        #######################
C        # AIRSPEED INDICATORS #
C        #######################
C
CD UJ010  Indicated airspeed (kts) from line pressures (hg)
C         -------------------------------------------------
C
        X = (UAPT(I)-UAPS(I))
C
        DO WHILE (X.LT.X3(JI3(I)))
          JI3(I) = JI3(I) - 1
        ENDDO
        DO WHILE (X.GT.X3(JI3(I)+1))
          JI3(I) = JI3(I) + 1
        ENDDO
C
        SCRATCH = M3(JI3(I))*X + B3(JI3(I))
C
C
        IF (.NOT.TF31061(I)) THEN
          UJIAS(I) = SCRATCH
          IASLIM(I) = UJIAS(I)
        ELSE
C
C   Using GAUSSIAN Distribution random number (between -2 to +2),
C   factor=10.0 to keep and IAS oscillation of +-20.0 kts.
C
          IF (ABS(IASLIM(I)-UJIAS(I)).LT.0.5)
     &       IASLIM(I) = YLGAUSN(I) * 10.0 + SCRATCH
          UJIAS(I) = UJIAS(I)+ILAG *(IASLIM(I)-UJIAS(I))
        ENDIF
C
CD UJ020  Drive airspeed pointer
C         ----------------------
C
        UJ$IAS(I) = UJIAS(I)
C
CD UJ030  Drive Airspeed drum [0-99 ft]
C         -----------------------------
C
C -- Airspeed indicator drum does not read below 22kts
C    as seen on A/C photos.
C
        IF (UJIAS(I).LE.22.0) THEN
          UJDRUM(I) = 22.0
        ELSE
          UJDRUM(I) = UJIAS(I)
        ENDIF
C
C -- limits to 0-99 ft
C
        DO WHILE (UJDRUM(I).GE.100.)
          UJDRUM(I) = UJDRUM(I) - 100.
        ENDDO
C
        UJ$DRUM(I) = ABS(UJDRUM(I))
C
C
CD UJ040  Airspeed indicator VMO pointer
C         ------------------------------
C
C -- Compute uncorrected altitude from pilot and copilot static probes
C
        Y = UAPS(I)
        DO WHILE (Y.GT.Y1(JI1(I)))
          JI1(I) = JI1(I) - 1
        ENDDO
        DO WHILE (Y.LT.Y1(JI1(I)+1))
        JI1(I) = JI1(I) + 1
        ENDDO
C
        UJALT(I) = (Y-B1(JI1(I))) * L1(JI1(I))
C
C Drives VMO pointer according to computed altitude
C (Note: the VMO equation is derived from the IAS chart
C        as per AEROSONIC P/N: 21140-1134-2)
 
        IF (UJALT(I).LE.14000.0) THEN
          UJ$VMO(I)=242.0
        ELSE
          UJ$VMO(I) = -0.00318 * UJALT(I) + 286.5
        ENDIF
      ENDDO
C
C
C        #####################
C        # STANDBY ALTIMETER #
C        #####################
C
C
CD UQ010  Standby altimeter output
C         ------------------------
C
C --  The altitude is found from the pilot static pressure line
C
C -- coarse
C
      UQ$ALTC = UJALT(1)
C
C -- fine (0-4000 ft , 1:25 ratio)
C
      UQ$ALTF = UJALT(1)-4000.0*INT((UJALT(1)+2000.0)*0.00025)
C
C
C          ##############################
C          # STANDBY ATTITUDE INDICATOR #
C          ##############################
C
C
      IF (Y132(3)) THEN
C
CD UC010 Standby attitude validity
C        -------------------------
C
        UCPWR = (BILH04.OR.BIRL07).AND..NOT.TF31091
C
        IF (TF31091.AND..NOT.UCPWR) UCFALIGN = .TRUE.   ! JN 1574 -jwm-
C
C  This section for gyro power on. Store pitch & roll angles for
C  later use.
C
        IF (UCPWR) THEN
C
CD UC015 Standby attitude fast aligned request from I/F
C        ---------------------------------------------
C
         IF (UCFALIGN) THEN
           UCFALIGN = .FALSE.
           UCRS = 1.0
           UCPITCH = 0.0
           UCROLL  = 0.0
           UCTP = VTHETADG
           UCTR = VPHIDG
         ENDIF
C
C  Increase rotor speed gradually to 100%
C
CD UC020  Standby attitude gyro speed runup
C         --------------------------------
C
          SCRATCH = UCRS + UCRUNUP
          IF (SCRATCH.GE.1.0) THEN
            UCRS = 1.0
          ELSE
            UCRS = SCRATCH
          ENDIF
        ELSE
C
C  This section for gyro power off.
C  Decrease rotor speed gradually to 50%
C  then to zero once gyro toppled.
C
C
CD UC030  Standby attitude gyro speed rundown
C         ----------------------------------
C
          SCRATCH = UCRS - UCRUNDN
          IF (SCRATCH.LE.0.473) THEN
            UCRS = 0.0
          ELSE
            UCRS = SCRATCH
          ENDIF
        ENDIF
C
CD UC040  Standby attitude indicator flag
C         ------------------------------
C
C  Gyro flag appears when rpm < 18000 or when power is lost.
C
         UC$FLAG = UCPWR.AND.(UCRS.GT.0.78)
C
      ENDIF
C
CD UC050  Standby attitude fast erect mode
C         -------------------------------
C
C Test for fast cage knob pulled
C
      IF (IDUCAGE) THEN
 
        IF (UCPITCH.GE.UCPERCT) THEN
          UCPITCH=UCPITCH - UCPERCT
        ELSE IF (UCPITCH.LE.(-UCPERCT)) THEN
          UCPITCH=UCPITCH + UCPERCT
        ELSE
          UCPITCH=0.
        ENDIF
 
        IF (UCROLL.GE.UCRERCT) THEN
          UCROLL=UCROLL - UCRERCT
        ELSE IF (UCROLL.LE.(-UCRERCT)) THEN
          UCROLL=UCROLL + UCRERCT
        ELSE
          UCROLL=0.
        ENDIF
C
C  Store the difference between actual and displayed
C  attitude.
C
        UCTP = VTHETADG - UCPITCH
        UCTR = VPHIDG - UCROLL
C
CD UC060  Standby attitude slow erect mode
C         -------------------------------
C
C   This section for fast rotor speed gyro will slow erect if it
C   is within 10 degrees of apparent vertical and rotor speed is
C   .ge. 50%.
C
      ELSE IF (UCRS.GE.0.5) THEN
        IF ((ABS(UCTP).LT.10.0).AND.(ABS(UCTR).LT.10.0)) THEN
          IF (UCTP.GT.UCSLOW) THEN
            UCTP = UCTP -UCSLOW
          ELSE IF (UCTP.LT.(-UCSLOW)) THEN
            UCTP = UCTP +UCSLOW
          ELSE
            UCTP=0.
          ENDIF
 
          IF (UCTR.GT.UCSLOW) THEN
            UCTR = UCTR -UCSLOW
          ELSE IF (UCTR.LT.(-UCSLOW)) THEN
            UCTR = UCTR +UCSLOW
          ELSE
            UCTR=0.
          ENDIF
        ENDIF
 
        UCPITCH = VTHETADG - UCTP
        UCROLL  = VPHIDG - UCTR
      ELSE
C
CD UC070  Standby attitude topple mode
C         ----------------------------
C
C  When the gyro is not caged or locked and the rotor speed
C  is below 50% of full rating, the gyro will topple.
C
        IF (UCPITCH.LT.UCPLIM) THEN
          UCPITCH = UCPITCH + UCPTOPL + DELTHETA
        ELSE
          UCPITCH = UCPLIM
        ENDIF
 
        IF (UCROLL.LT.UCRLIM) THEN
          UCROLL = UCROLL + UCRTOPL + DELPHI
        ELSE
          UCROLL = UCRLIM
        ENDIF
 
        UCTP = VTHETADG - UCPITCH
        UCTR = VPHIDG   - UCROLL
C
        DELTHETA = VTHETADG - PRETHETA
        DELPHI   = VPHIDG   - PREPHI
        PRETHETA = VTHETADG
        PREPHI   = VPHIDG
      ENDIF
C
CD UC080  Standby attitude outputs
C         ------------------------
C
      UC$PITCH = UCPITCH
      UC$ROLL  = UCROLL
C
C         ##############################
C         # LOW RANGE RADIO ALTIMETERS #
C         ##############################
C
C
      IF ((VH .LE. 1000.0) .OR. Y66) THEN
C
C -- Above 1000 ft radio altitude calculations are done at
C    half speed to save cpu time.
C
C
CD UG010  Radio altitude computation
C         --------------------------
C
        UGLG1 = (VXCG - RABS) * INTOFT
        UGLG2 = (VZCG - RAWL) * INTOFT
C
        LGRA = VH + UGLG1*VSNTHE - UGLG2*VCSTHE*VCSPHI - UGEAR
        UGA165A = LGRA
        UGA165B = LGRA
C
Csk+ Move ARINC Label assignments for RA to end of section
C
C COA S81-1-xxx Following two lines added per CAE instructions to
C               fix the TCAS problem (giving RA's below 1000 feet
C               radio altitude).  Tom M. 8/15/97
C
Csk        UGA164A = LGRA
Csk        UGA164B = LGRA
C
Csk- S. Kalantzis 8/25/97
C
C
Csk+ Modify calibration to read -1 or +1 in the range of zero RA
Csk  S. Kalantzis TCAS fix 8/25/97
Csk
C -- Calibrated to read 0 on ground.
C
Csk        IF ( LGRA .LT. 0.0 ) LGRA = 0.0
Csk
        IF(ABS(LGRA) .LT. 1.0) THEN
          IF(LGRA .GE.  0.0) THEN
             LGRA =  1.0
          ELSE
             LGRA = -1.0
          ENDIF
        ENDIF
Csk
Csk- End of Modification
C
C
CD UG020  Radio altimeter power
C         ---------------------
C
        UGPWR(1) = BIAF05.AND..NOT.TF31141
C
        IF ( UGPWR(1) ) THEN
C
            SCSREQ(12) = .FALSE.         ! lrra1 bus on
            SCSREQ(13) = .FALSE.         ! lrra2 bus on
C
CD UG030  Radio altimeter outputs
C         -----------------------
C
C -- Check if in test and outputs test values
C
          IF (IDUGTST(1) .OR. IDUGTST(2)) THEN
            UGRA(1)  = 100.
            UGTST(1) = .TRUE.        !RA test flag
          ELSE
C
C -- Check if stall warning outputs test voltage to drive > 400 ft
C
            IF (CWRATST) THEN
              UGRA(1)  = 450.
            ELSE
C
C -- Normal output
C
              UGRA(1) = LGRA
              UGTST(1) = .FALSE.       !RA test flag
            ENDIF
          ENDIF
C
C
CD UG040  Radio altimeter validities
C         --------------------------
C
C -- RA is invalid during test or when not tracking.
C    Tracking operates only below 2500 ft.
C
C  Note: Beam is 50deg HPLANE, 60deg EPLANE.
C
C
        TRACK = (.NOT.( (VCSPHI.LT.0.5) .OR. (VCSTHE.LT.0.6428) ))
     &         .OR. (LGRA .GT.2500.)
C
          UGRAV(1) = TRACK .AND. .NOT. UGTST(1)
C
C !FM+
C !FM  10-Jun-92 09:28:10 M.WARD
C !FM    < ADDED CODE FOR TCAS UNIT INPUT FROM RA >
C !FM
          IF ( UGTST(1) ) THEN
            UGJ165A = 10              ! test (ncd)
            UGJ165B = 10              ! test (ncd)
            UGJ164A = 10              ! test (ncd)
            UGJ164B = 10              ! test (ncd)
          ELSEIF ( UGRAV(1) ) THEN
            UGJ165A = 08              ! normal
            UGJ165B = 16              ! normal
            UGJ164A = 14              ! normal
            UGJ164B = 22              ! normal
          ELSE
            SCSREQ(12) = .TRUE.          ! lrra1 bus fail
            SCSREQ(13) = .TRUE.          ! lrra2 bus fail
          ENDIF
C !FM-
C
C
CD UG050  R/A trips
C         ---------
C
C -- Trip #1 at 1200 ft
C
          UGTRIP1(1) = (LGRA .LE. 1200.)
C
C -- Trip #2 at 250 ft
C
          UGTRIP2(1) = (LGRA .LE. 500.)
C
C -- Trip #3 at 50 ft
C
          UGTRIP3(1) = (LGRA .LE. 50.)
C
C -- Trip #4 at 400 ft
C
          UGTRIP4(1) = (LGRA .LE. 400.)
C
C
CD UG060  Radio altimeter power off
C         -------------------------
C
C -- no outputs & all discretes are invalids
C
        ELSE
          UGRA(1)    =  0.0
          UGRAV(1)   = .FALSE.
          UGTRIP1(1) = .FALSE.
          UGTRIP2(1) = .FALSE.
          UGTRIP3(1) = .FALSE.
          UGTRIP4(1) = .FALSE.
C !FM+
C !FM  10-Jun-92 09:30:12 M.WARD
C !FM    < ADDED CODE FOR TCAS INPUT FROM RA >
C !FM
          SCSREQ(12) = .TRUE.
          SCSREQ(13) = .TRUE.
C !FM-
        ENDIF
C
Csk+ Moved Binary Label Assignment to end of RA section and
Csk  assign UGRA instead of LGRA which will reflect changes during the test
Csk  and Fail states.
C
C COA S81-1-xxx Following two lines added per CAE instructions to
C               fix the TCAS problem (giving RA's below 1000 feet
C               radio altitude).  Tom M. 8/15/97
C
        UGA164A = UGRA(1)
        UGA164B = UGRA(1)
C
Csk- S. Kalantzis  8/25/97
C
      ENDIF
 
C
C
C         #################
C         # VERTICAL GYRO #
C         #################
C
CD UD005  Pitch and roll angles for Vertical gyro computations
C         ----------------------------------------------------
C
C -- The flight pitch and roll angles are modified for the vg computations
C    to ensure that there is a slight difference between AHRS values.
C
      UDTHETA = VTHETADG + 1.5
      UDPHI   = VPHIDG + 1.5
C
      IF (Y132(4)) THEN
C
CD UD010 Vertical gyro power
C        -------------------
C
C DeHavilland Maintenance manual (34-20-50)
C VG14A  Vertical gyro. Time constants are given in
C data request no USA-DH8-058 response from Honeywell.
C
        UDPWR(1) = BIAE09.AND..NOT.TF34I031
C
C  This section for gyro power on. Store pitch & roll angles for
C  later use.
C
        IF (UDPWR(1)) THEN
C
CD UD015 Vertical gyro fast aligned request from I/F
C        -------------------------------------------
C
         IF (UDFALIGN) THEN
           UDRS(1) = 1.0
           UDPITCH(1) = 0.0
           UDROLL(1)  = 0.0
           UDTP(1) = 0.0
           UDTR(1) = 0.0
         ENDIF
C
C  Increase rotor speed gradually to 100%
C
CD UD020  Vertical gyro speed runup
C         -------------------------
C
          SCRATCH = UDRS(1) + UDRUNUP
          IF (SCRATCH.GE.1.0) THEN
            UDRS(1) = 1.0
          ELSE
             UDRS(1) = SCRATCH
          ENDIF
        ELSE
C
C  This section for vertical gyro power off.
C  Decrease rotor speed gradually to 50%
C  then to zero once gyro toppled.
C
C
CD UD030  Vertical gyro speed rundown
C         ---------------------------
C
          SCRATCH = UDRS(1) - UDRUNDN
          IF (SCRATCH.LE.0.492) THEN
            UDRS(1) = 0.0
          ELSE
            UDRS(1) = SCRATCH
          ENDIF
        ENDIF
C
CD UD040  Vertical gyro validity
C         ----------------------
C
C  Determine vertical gyro erect request via the VG ERECT button
C
         IF (IDRCVG.AND..NOT.VGERCT) VGERCT = .TRUE.
C
C  Vertical gyro is invalid while vertical gyro is erecting or rpm is 75%
C  or less or when power is lost.
C
         UDVGV(1) = UDPWR(1).AND..NOT.VGERCT.AND.(UDRS(1).GT.0.75)
C
C  Set ATT FAIL lt on Gyro Switching Control Panel
C
         RC$FATT = .NOT.UDVGV(1) .AND. BIAL03
C
      ENDIF
C
CD UD050  Vertical gyro fast erect mode
C         -----------------------------
C
C Test for vertical gyro erect request
C
      IF (VGERCT) THEN
        IF (UDPITCH(1).GE.UDPERCT) THEN
          UDPITCH(1)=UDPITCH(1) - UDPERCT
        ELSE IF (UDPITCH(1).LE.(-UDPERCT)) THEN
          UDPITCH(1)=UDPITCH(1) + UDPERCT
        ELSE
          UDPITCH(1)=0.
        ENDIF
 
        IF (UDROLL(1).GE.UDRERCT) THEN
          UDROLL(1)=UDROLL(1) - UDRERCT
        ELSE IF (UDROLL(1).LE.(-UDRERCT)) THEN
          UDROLL(1)=UDROLL(1) + UDRERCT
        ELSE
          UDROLL(1)=0.
          IF (UDPITCH(1).EQ.0.) VGERCT = .FALSE.
        ENDIF
C
C  Store the difference between actual and displayed
C  attitude.
C
        UDTP(1) = UDTHETA - UDPITCH(1)
        UDTR(1) = UDPHI   - UDROLL(1)
C
CD UD060  Vertical gyro slow erect mode
C         -----------------------------
C
C   This section for fast rotor speed gyro will slow erect if it
C   is within 10 degrees of apparent vertical and rotor speed is
C   .ge. 50%.
C
      ELSE IF (UDRS(1).GE.0.5) THEN
        IF ((ABS(UDTP(1)).LT.10.0).AND.(ABS(UDTR(1)).LT.10.0)) THEN
          IF (UDTP(1).GT.UDSLOW) THEN
            UDTP(1) = UDTP(1) -UDSLOW
          ELSE IF (UDTP(1).LT.(-UDSLOW)) THEN
            UDTP(1) = UDTP(1) +UDSLOW
          ELSE
            UDTP(1)=0.
          ENDIF
 
          IF (UDTR(1).GT.UDSLOW) THEN
            UDTR(1) = UDTR(1) -UDSLOW
          ELSE IF (UDTR(1).LT.(-UDSLOW)) THEN
            UDTR(1) = UDTR(1) +UDSLOW
          ELSE
            UDTR(1)=0.
          ENDIF
        ENDIF
 
        UDPITCH(1) = UDTHETA - UDTP(1)
        UDROLL(1)  = UDPHI   - UDTR(1)
      ELSE
C
CD UD070  Vertical gyro topple mode
C         -------------------------
C
C  When the gyro is not caged or locked and the rotor speed
C  is below 50% of full rating, the gyro will topple.
C
        IF (UDPITCH(1).LT.UCPLIM) THEN
          UDPITCH(1) = UDPITCH(1) + UDPTOPL + UDPERR(1)
        ELSE
          UDPITCH(1) = UDPLIM
        ENDIF
 
        IF (UDROLL(1).LT.UCRLIM) THEN
          UDROLL(1) = UDROLL(1) + UDRTOPL + UDRERR(1)
        ELSE
          UDROLL(1) = UDRLIM
        ENDIF
 
        UDTP(1) = UDTHETA - UDPITCH(1)
        UDTR(1) = UDPHI   - UDROLL(1)
C
        UDPERR(1) = UDTHETA - PRETHETA
        UDRERR(1) = UDPHI   - PREPHI
        PRETHETA = UDTHETA
        PREPHI   = UDPHI
      ENDIF
C
C
C         #########
C         # ADI'S #
C         #########
C
CD UE010 Radio altitude inputs
C        ---------------------
C
C ADI 1 DH GND O/P (pin D) is connected to ADI 2 DH I/P (pin D)
C
      UE$DH = IDUEDH
C
      DO I = 1,2
        UE$RAV(I) = UGRAV(1)    !RAD ALT VALID
        UE$RA(I)  = UGRA(1)     !RAD ALT DATA
C
C
CD UE020 Pitch and roll inputs
C        ---------------------
C
C -- Vertical gyro inputs
C
        IF (IDRCGYR1(I)) THEN
          UE$ATTV(I)  = UDVGV(1)
          IF (.NOT.TF34A61(I)) THEN
            UE$PITCH(I) = UDPITCH(1)
            UE$ROLL(I)  = UDROLL(1)
          ENDIF
        ELSE
C
C -- AHRS inputs
C
          UE$ATTV(I)  = RNPCHVO(I)
          IF (.NOT.TF34A61(I)) THEN
             UE$ROLL(I)  = RNROLO(I)
             UE$PITCH(I) = RNPCHO(I)
          ENDIF
        ENDIF
C !FM+
C !FM  27-Mar-92 10:50:10 STEVE MCTAVISH
C !FM    < ADDING THROUGHPUT CODE FOR ADI >
C !FM
        IF(MLATINIT.AND.(MLATAXIS.EQ.2.)) UE$PITCH(I)=UE$PITCH(I)+90.
        IF(MLATINIT.AND.(MLATAXIS.EQ.1.)) UE$ROLL(I)=UE$ROLL(I)+90.
C !FM-
      ENDDO
 
C         ####################
C         # ADI 'S SLIPBALLS #
C         ####################
C
CD UE030  Y-axis cockpit acceleration [ft/sec**2]
C         --------------------------------------
C
      UEAYI = VAYB + VPILTXCG * (VP*VQ + VRD)
C
C
CD UE040  Z-axis cockpit acceleration [ft/sec**2]
C         --------------------------------------
C
C -- Set to standard gravity acceleration
C
      UEAZI = -32.0  +  VPILTXCG * (VP*VR - VQD)
C
CD UE050  Sideslip angle [deg]
C         --------------------
C
      UESSLIP = (-UEAYI/UEAZI) * RADTODEG
C
C
C  -- Lagged sideslip angle [deg]
C
      UESLIP = UESLIP + SLIPLAG * (UESSLIP - UESLIP)
C
C
CD UE060  Sideslip output [deg]
C         ---------------------
C
      DO I = 1,2
C
C !FM+
C !FM  26-Mar-92 16:09:49 STEVE MCTAVISH
C !FM    < ADDING THROUGHPUT CODE FOR SLIPBALL >
C !FM
        IF((MLATAXIS.EQ.3.).AND.MLATINIT) THEN
         UE$SLIP(I)= UESLIP+10.
        ELSE
C !FM+
C !FM   6-Sep-93 02:55:18 W. Pin
C !FM    < Add scaling coefficient. >
C !FM
CWP         UE$SLIP(I)= UESLIP
CWP
         IF ( YITAIL.EQ.230 ) THEN
           UE$SLIP(I)= UESLIP * SLIPSCAL
         ELSE
           UE$SLIP(I)=UESLIP
         ENDIF
C !FM-
        ENDIF
C !FM-
C
C
C         #########################
C         # TURN & SIP INDICATORS #
C         #########################
C
C
CD  US010  Turn & slip power
C          -----------------
C
        TSPWR(I) = BILA05(I)
C
CD  US020  Turn Indication [deg]
C          ---------------------
C
        IF (TSPWR(I)) THEN
C
          UETURN = VPSI0D * RADTODEG
C
CD  US030  Turn & slip outputs
C          -------------------
C
C -- rate of turn
C
          US$TURN(I) = UETURN
        ENDIF
C
C -- sideslip (simulates a ball in a glass tube, power independant)
C
          US$SLIP(I) = UESLIP
      ENDDO
C
      CALL SCALOUT(SYS)
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00911 UT010  SAT/FLX indicator
C$ 00979 UF010  VSI/TRA vertical speed inputs
C$ 01018 UH010  Altimeters validities
C$ 01025 UH020  Altimeters altitude outputs
C$ 01050 UH030  Altimeters altitude alert light
C$ 01064 UJ010  Indicated airspeed (kts) from line pressures (hg)
C$ 01092 UJ020  Drive airspeed pointer
C$ 01097 UJ030  Drive Airspeed drum [0-99 ft]
C$ 01118 UJ040  Airspeed indicator VMO pointer
C$ 01150 UQ010  Standby altimeter output
C$ 01171 UC010 Standby attitude validity
C$ 01183 UC015 Standby attitude fast aligned request from I/F
C$ 01197 UC020  Standby attitude gyro speed runup
C$ 01213 UC030  Standby attitude gyro speed rundown
C$ 01224 UC040  Standby attitude indicator flag
C$ 01233 UC050  Standby attitude fast erect mode
C$ 01262 UC060  Standby attitude slow erect mode
C$ 01292 UC070  Standby attitude topple mode
C$ 01319 UC080  Standby attitude outputs
C$ 01336 UG010  Radio altitude computation
C$ 01376 UG020  Radio altimeter power
C$ 01386 UG030  Radio altimeter outputs
C$ 01410 UG040  Radio altimeter validities
C$ 01445 UG050  R/A trips
C$ 01465 UG060  Radio altimeter power off
C$ 01507 UD005  Pitch and roll angles for Vertical gyro computations
C$ 01518 UD010 Vertical gyro power
C$ 01532 UD015 Vertical gyro fast aligned request from I/F
C$ 01545 UD020  Vertical gyro speed runup
C$ 01561 UD030  Vertical gyro speed rundown
C$ 01572 UD040  Vertical gyro validity
C$ 01590 UD050  Vertical gyro fast erect mode
C$ 01619 UD060  Vertical gyro slow erect mode
C$ 01649 UD070  Vertical gyro topple mode
C$ 01681 UE010 Radio altitude inputs
C$ 01693 UE020 Pitch and roll inputs
C$ 01727 UE030  Y-axis cockpit acceleration [ft/sec**2]
C$ 01733 UE040  Z-axis cockpit acceleration [ft/sec**2]
C$ 01740 UE050  Sideslip angle [deg]
C$ 01751 UE060  Sideslip output [deg]
C$ 01784 US010  Turn & slip power
C$ 01789 US020  Turn Indication [deg]
C$ 01796 US030  Turn & slip outputs
