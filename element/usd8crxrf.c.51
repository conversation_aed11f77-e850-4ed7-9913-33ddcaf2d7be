# include  "cf_def.h"
/* # include  <math.h>             */

/*
C'Revision_History
*/
                                                                          
/*                                                                        
*    -------------------------------------------------------              
**   SYSTEM TIME RATES                                                    
*    -------------------------------------------------------              
*/                                                                        
float                                                                     
YITIM    = 0.00033333,  /* PROGRAM ITERATION RATE                 (SEC) */  
YTITRN   = 0.00033333,  /* CTS ITERATION RATE                     (SEC) */  
SYSITIMC = 0.00033333,  /* S<PERSON>U<PERSON>TO<PERSON> FORWARD AND AFT TIME CONSTANT(SEC) */  
SYSITIMP = 0.002,       /* SIMULATOR PCU MODEL TIME CONSTANT      (SEC) */  
YTSIMTM  = 0.0,         /* SIMULATOR TIME (SEC)                   */  
TESTIME  = 0.0          /* CTS TEST TIMER                         */  
;                                                                 
int                                                               
TESTCOUNT = 0,           /*  CTS ITERATION COUNT                  */  
YIFREZ    = 0,           /*  SIMULATOR FREEZE FLAG                */  
YTITRCNT  = 0            /*  SIMULATOR ITERATION COUNT            */  
;                                                                 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF020 ROLL CARD TRANSFERS LABELS
C -----------------------------------------------------------------------------
C
CC The following section contains the variables that are transfered between
CC the host computer CDB and the Roll C30 card. The labels used here
CC are similar to the actual model labels declared further in this present
CC file. Labels with CXA prefix are transfered to host CDB labels with CIA
CC prefix. In the same way, host CDB labels with CA$ prefix are transfered
CC to the XRF labels with the CAX prefix. The transfer variables in the host
CC CDB and in this XRF must be equally aligned and must be specified in the
CC transfer file usd8dn1.dfx.
*/
/*
-----------
HOST to DMC
-----------
*/
float
CACSPOS=0,
CACSVEL=0,
CAFSPOS=0,
CAFSVEL=0,

CACDPOS=0,   /* Demanded position                */
CACQPOS=0,   /* Equivalent position              */
CACFPOS=0,   /* Fokker position                  */
CACAFOR=0,   /* Actual force - Pilot units        */
CACCFOR=0,   /* Cable force                      */
CACTRIMP=0,  /* Trim Position actually used      */

CAFDPOS=0,   /* Demanded position                */
CAFQPOS=0,   /* Equivalent position              */
CAFFPOS=0,   /* Fokker position                  */
CAFAFOR=0,   /* Actual force - Pilot units        */
CAFCFOR=0,   /* Cable force                      */
CAFTRIMP=0,  /* Trim Position actually used      */

CSSPOS[8]={0,0,0,0,0,0,0,0},

CASPR0 = 0,
CASPR1 = 0,
CASPR2 = 0,
CASPR3 = 0,
CASPR4 = 0,
CASPR5 = 0,
CASPR6 = 0,
CASPR7 = 0,
CASPR8 = 0,
CASPR9 = 0,

CACDFOR=0,        /* Driving force                    */
CAFDFOR=0;        /* Driving force                    */

int
CSCLT1   =0,    /*         SPOILER CLUTCH 1 BREAKOUT          TRUE*/
CSCLT2   =0;    /*         SPOILER CLUTCH 2 BREAKOUT          TRUE*/

/*
-----------
HOST to DMC
-----------
*/
int 
CAFREZ   =0;  /*        AILERON MODEL FREEZE               I6*/

struct
BITMAP  CAMALF   ={0};  /*        AILERON MALFUNCTIONS               I6*/
 
#define  TF27021 CAMALF.bit_1  /* left roll control jam */
#define  TF27022 CAMALF.bit_2  /* Right roll control jam   */
#define  TF27011 CAMALF.bit_3  /* Aileron surface jam   */
#define  TF27231 CAMALF.bit_4  /* Aileron trim rwy  */
#define  TF27311 CAMALF.bit_5  /* Left aileron rwy immed left  */
#define  TF27312 CAMALF.bit_6  /* Right aileron rwy immed left  */
#define  TF27241 CAMALF.bit_7  /* Left outbd roll spoiler link jam   */
#define  TF27242 CAMALF.bit_8  /* Left inbd roll spoiler link jam   */
#define  TF27243 CAMALF.bit_9 /* Right outbd roll spoiler link jam   */
#define  TF27244 CAMALF.bit_10 /* Right inbd roll spoiler link jam   */
#define  TF27291 CAMALF.bit_11 /* Gnd spoiler sol fail open   */
#define  TF27292 CAMALF.bit_12 /* Gnd spoiler sol fail close   */
#define  TF27251 CAMALF.bit_13 /* Inbd lift dump fails open   */
#define  TF27252 CAMALF.bit_14 /* Outbd lift dump fails open   */
#define  TF27261 CAMALF.bit_15 /* Inbd lift dump fails close  */
#define  TF27262 CAMALF.bit_16 /* Outbd lift dump fails close  */

int
CAAPENG  =0,  /*        AILERON AUTOPILOT SERVO ENGAGED    I6*/
CAAPCH   =0,  /*        NUMBER OF AUTOPILOT CHANNELS ENGAGED I6*/
CAYTAIL  =0,  /*        AILERON TAIL NUMBER CONFIGUARTION OPTION I6*/

CACBON=0,    /* Host backdrive mode           */
CAFBON=0,    /* Host backdrive mode           */
CANOFRI  =0,  /*        AILERON FRICTION INHIBIT              TRUE*/
CANOHYS  =0,  /*        AILERON HYSTERESIS INHIBIT            TRUE*/
CASWCON  =0,  /*        SOFTWARE ROLL CONTROLS COUPLE COMMAND TRUE*/

CSFREZ   =0;  /*        SPOILER MODEL FREEZE                  I6*/

struct
BITMAP CSGCMD = {0};     
struct
BITMAP CAISPR = {0} ;   
#define   CSUNLD1    CAISPR.bit_1    
#define   CSUNLD2    CAISPR.bit_2    

float
CATRIM = 0,
CADYNPR=0,
CAMACH    =0,  /*          A/C MACH NUMBER                        F9.4*/
CAFLAPS   =0,  /*          FLAP ANGLE                      [DEG] F9.4*/
CAALPHA   =0,  /*          WING ANGLE OF ATTACK            [DEG] F9.4*/
CAGLOCK   =0,  /*          CONTROL LOCK LEVER POSITION     F9.4*/

CASRAIL   =0,  /*          A/P AILERON COMMAND             [DEG] F9.4*/

CACHTSTF=0,       /* Test force input from host       */
CACBPOS=0,   /* Host backdrive position       */

CAFHTSTF=0,       /* Test force input from host       */
CAFBPOS=0,   /* Host backdrive position       */

CSHP1 =0,  /*   SPOILER SYSTEM 1 HYDRAULIC PRESSURE    [PSI] F9.4*/
CSHP2 =0;  /*   SPOILER SYSTEM 2 HYDRAULIC PRESSURE    [PSI] F9.4*/

/*
C -----------------------------------------------------------------------------
CD CRXRF030 SYSTEM MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the system definition file usd8crsys.c
*/
 
/*
C -----------------------------------------------
CD CRXRF040 - Captains Aileron Mode Control Macro
C -----------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CACIALC=20;  /* Max. Current limit             */
/*
Inputs
*/
 
float
CACFSAFLIM=0, /* Force level for safety fai   */
CACVSAFLIM=0, /* Velocity for safety fail     */
CACPSAFLIM=0, /* Position Error for safety    */
CACBSAFLIM=0, /* Position Error for safety    */
CACMSAFLIM=0, /* Force * Vel for safety fai   */
CACNSAFLIM=0, /* Neg Force * Vel for safety fai */
CACNSAFUPR=0, /* Neg Force * Vel range upper lim*/
CACNSAFLWR=0, /* Neg Force * Vel range lower lim*/
CACPOSTRNS=0, /* Max. position transient        */
CACFORTRNS=0, /* Max. force transient           */
CACKA=0,      /* Servo value current acceler'n gain */
CACKV=0,      /* Servo value current velocity gain  */
CACKP=0;      /* Servo value current position gain  */
/*
Outputs
*/
 
float
CACIAL=0,     /* Current limit        */
CACFSAFMAX=0, /* Max Force Level since reset fail   */
CACVSAFMAX=0, /* Max Velocity Level since reset f   */
CACPSAFMAX=0, /* Max Force Position since reset f   */
CACBSAFMAX=0, /* Max Force Position since reset f   */
CACMSAFMAX=0, /* Max Force * Vel Level since reset  */
CACNSAFMAX=0, /* Max neg Force * Vel Level since rst*/
CACFSAFVAL=0, /* Present Force level          */
CACVSAFVAL=0, /* Present Velocity level       */
CACPSAFVAL=0, /* Present Position Error le    */
CACBSAFVAL=0, /* Present Position Error le    */
CACMSAFVAL=0, /* Present Force * Vel level    */
CACNSAFVAL=0, /* Present Neg force * Vel level*/
CACFSAFSAF=0, /* Maximum allowed force safe level   */
CACVSAFSAF=0, /* Maximum allowed Velocity safe level*/
CACPSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CACBSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CACMSAFSAF=0, /* Maximum allowed Force*Vel safe level*/
CACNSAFSAF=0, /* Maximum allowed neg Force*Vel safe  */
CACKANOR=0,   /* Normalized  current acceler'n gain */
CACKVNOR=0,   /* Normalized  current velocity gain  */
CACKPNOR=0,   /* Normalized  current position gain  */
CACGSCALE=0,  /* Force gearing scale               */
CACPSCALE=0;  /* Position gearing scale            */
/*
Integer Inputs
*/
 
int
CACSAFDSBL=0, /* Capt Elevator safety disabl  */
CACFLDSABL=0,/* Force max limit disbale      */
CACBSENABL=0,/* Bungee safety disable        */
CACLUTYPE=0,  /* Load unit type               */
CACSAFREC=0,  /* Safety limit recalculation flag    */
CACFSAFTST=0, /* Test Force safety fail       */
CACVSAFTST=0, /* Test Velocity safety fail    */
CACPSAFTST=0, /* Test Position Error safety   */
CACBSAFTST=0, /* Test Position Error safety   */
CACMSAFTST=0, /* Test Force * Vel safety fai  */
CACNSAFTST=0, /* Test neg force * Vel safety  */
CACFTRNTST=0, /* Force transient test        */
CACPTRNTST=0, /* Position transient test     */
CACBPWRTST=0, /* Test Buffer unit power fail */
CACDSCNTST=0; /* Test Buffer unit disconnect */
/*
Integer Outputs
*/
 
int
CACFSAFFL=0, /* Force safety fail           */
CACVSAFFL=0, /* Velocity safety fail        */
CACPSAFFL=0, /* Position Error safety       */
CACBSAFFL=0, /* Position Error safety       */
CACMSAFFL=0, /* Force * Vel safety fai      */
CACNSAFFL=0, /* Negative force * Vel failure */
CACBPWRFL=0,  /* Buffer unit power fail      */
CACDSCNFL=0,  /* Buffer unit disconnect      */
CACFTRNFL=0,  /* Force transient failure     */
CACPTRNFL=0,  /* Position transient failure     */
CAC_CMP_IT=0, /* Position Error enable          */
CAC_IN_STB=0, /* Buffer unit in standby mode  */
CAC_IN_NRM=0, /* Buffer unit in normal mode   */
CAC_HY_RDY=0, /* Hyd ready signal to B.U. in BUDOP */
CAC_STB_RQ=0; /* Stby req to B.U. through BUDOP    */
/*
*/
 
/*
*/
 
  
/*
C ------------------------------------------
CD CRXRF050 - F/O Aileron Mode Control Macro
C ------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CAFIALC=20;  /* Max. Current limit             */
/*
Inputs
*/
 
float
CAFFSAFLIM=0, /* Force level for safety fai   */
CAFVSAFLIM=0, /* Velocity for safety fail     */
CAFPSAFLIM=0, /* Position Error for safety    */
CAFBSAFLIM=0, /* Position Error for safety    */
CAFMSAFLIM=0, /* Force * Vel for safety fai   */
CAFNSAFLIM=0, /* Neg Force * Vel for safety fai */
CAFNSAFUPR=0, /* Neg Force * Vel range upper lim*/
CAFNSAFLWR=0, /* Neg Force * Vel range lower lim*/
CAFPOSTRNS=0, /* Max. position transient        */
CAFFORTRNS=0, /* Max. force transient           */
CAFKA=0,      /* Servo value current acceler'n gain */
CAFKV=0,      /* Servo value current velocity gain  */
CAFKP=0;      /* Servo value current position gain  */
/*
Outputs
*/
 
float
CAFIAL=0,     /* Current limit        */
CAFFSAFMAX=0, /* Max Force Level since reset fail   */
CAFVSAFMAX=0, /* Max Velocity Level since reset f   */
CAFPSAFMAX=0, /* Max Force Position since reset f   */
CAFBSAFMAX=0, /* Max Force Position since reset f   */
CAFMSAFMAX=0, /* Max Force * Vel Level since reset  */
CAFNSAFMAX=0, /* Max neg Force * Vel Level since rst*/
CAFFSAFVAL=0, /* Present Force level          */
CAFVSAFVAL=0, /* Present Velocity level       */
CAFPSAFVAL=0, /* Present Position Error le    */
CAFBSAFVAL=0, /* Present Position Error le    */
CAFMSAFVAL=0, /* Present Force * Vel level    */
CAFNSAFVAL=0, /* Present Neg force * Vel level*/
CAFFSAFSAF=0, /* Maximum allowed force safe level   */
CAFVSAFSAF=0, /* Maximum allowed Velocity safe level*/
CAFPSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CAFBSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CAFMSAFSAF=0, /* Maximum allowed Force*Vel safe level*/
CAFNSAFSAF=0, /* Maximum allowed neg Force*Vel safe  */
CAFKANOR=0,   /* Normalized  current acceler'n gain */
CAFKVNOR=0,   /* Normalized  current velocity gain  */
CAFKPNOR=0,   /* Normalized  current position gain  */
CAFGSCALE=0,  /* Force gearing scale               */
CAFPSCALE=0;  /* Position gearing scale            */
/*
Integer Inputs
*/
 
int
CAFSAFDSBL=0, /* Capt Elevator safety disabl  */
CAFFLDSABL=0,/* Force max limit disbale      */
CAFBSENABL=0,/* Bungee safety disable        */
CAFLUTYPE=0,  /* Load unit type               */
CAFSAFREC=0,  /* Safety limit recalculation flag    */
CAFFSAFTST=0, /* Test Force safety fail       */
CAFVSAFTST=0, /* Test Velocity safety fail    */
CAFPSAFTST=0, /* Test Position Error safety   */
CAFBSAFTST=0, /* Test Position Error safety   */
CAFMSAFTST=0, /* Test Force * Vel safety fai  */
CAFNSAFTST=0, /* Test neg force * Vel safety  */
CAFFTRNTST=0, /* Force transient test        */
CAFPTRNTST=0, /* Position transient test     */
CAFBPWRTST=0, /* Test Buffer unit power fail */
CAFDSCNTST=0; /* Test Buffer unit disconnect */
/*
Integer Outputs
*/
 
int
CAFFSAFFL=0, /* Force safety fail           */
CAFVSAFFL=0, /* Velocity safety fail        */
CAFPSAFFL=0, /* Position Error safety       */
CAFBSAFFL=0, /* Position Error safety       */
CAFMSAFFL=0, /* Force * Vel safety fai      */
CAFNSAFFL=0, /* Negative force * Vel failure */
CAFBPWRFL=0,  /* Buffer unit power fail      */
CAFDSCNFL=0,  /* Buffer unit disconnect      */
CAFFTRNFL=0,  /* Force transient failure     */
CAFPTRNFL=0,  /* Position transient failure     */
CAF_CMP_IT=0, /* Position Error enable          */
CAF_IN_STB=0, /* Buffer unit in standby mode  */
CAF_IN_NRM=0, /* Buffer unit in normal mode   */
CAF_HY_RDY=0, /* Hyd ready signal to B.U. in BUDOP */
CAF_STB_RQ=0; /* Stby req to B.U. through BUDOP    */
/*
*/
 
/*
*/
 
  
/*
C --------------------------------------------
CD CRXRF060 - Captains Aileron Backdrive Macro
C --------------------------------------------
*/
 
/*
Parameters
*/
 
float
CACBDLAG=0,  /* Backdrive lag constant        */
CACBDLIM=0,  /* Backdrive rate limit          */
CACBDGEAR=(-1), /* Surface gearing for backdrive */
CACBDFOR=0,  /* Backdrive force override level*/
CACBDOVRG=0; /* Force override rate gain      */
/*
Inputs
*/
 
float
CACMBPOS=0,  /* Utility backdrive position    */
CACBDFREQ=0, /* Sinewave backdrive frequency  */
CACBDAMP=0,  /* Sinewave backdrive amplitude  */
CACTRIM=0;   /* Trim pos'n to backdrive to    */
/*
*/
 
/*
*/
 
/*
Outputs
*/
 
float
CACBDRATE=0; /*  backdrive rate               */
/*
Integers
*/
 
int
CACMBMOD=0,  /* Utility backdrive mode        */
CACBDMODE=0; /*  backdrive mode               */
  
/*
C ---------------------------------------
CD CRXRF070 - F/O Aileron Backdrive Macro
C ---------------------------------------
*/
 
/*
Parameters
*/
 
float
CAFBDLAG=0,  /* Backdrive lag constant        */
CAFBDLIM=0,  /* Backdrive rate limit          */
CAFBDGEAR=(1), /* Surface gearing for backdrive */
CAFBDFOR=0,  /* Backdrive force override level*/
CAFBDOVRG=0; /* Force override rate gain      */
/*
Inputs
*/
 
float
CAFMBPOS=0,  /* Utility backdrive position    */
CAFBDFREQ=0, /* Sinewave backdrive frequency  */
CAFBDAMP=0,  /* Sinewave backdrive amplitude  */
CAFTRIM=0;   /* Trim pos'n to backdrive to    */
/*
*/
 
/*
*/
 
/*
Outputs
*/
 
float
CAFBDRATE=0; /*  backdrive rate               */
/*
Integers
*/
 
int
CAFMBMOD=0,  /* Utility backdrive mode        */
CAFBDMODE=0; /*  backdrive mode               */
  
/*
C ---------------------------------------------
CD CRXRF080 - Captains Aileron Throughput Macro
C ---------------------------------------------
*/
 
/*
Inputs:
*/
 
float
CACTHPTLVL=(-5);  /* Through-put force level   */
/*
Outputs:
*/
 
float
CACTHPTFOR=0;  /* Through-put force         */
  
/*
C ----------------------------------------
CD CRXRF090 - F/O Aileron Throughput Macro
C ----------------------------------------
*/
 
/*
Inputs:
*/
 
float
CAFTHPTLVL=0;  /* Through-put force level   */
/*
Outputs:
*/
 
float
CAFTHPTFOR=0;  /* Through-put force         */
  
/*
C --------------------------------------------------------
CD CRXRF100 - Captains Aileron Aip Input Calibration Macro
C --------------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CACPOS=0;       /* Position Offset                */
/*
*/
 
/*
Outputs
*/
 
float
CACXPU=0,     /* Control pos'n  - Actuator units   */
CACXP=0,      /* Control pos'n  - Pilot units      */
CACFOS=0,     /* Force offset - Actuator units     */
CACFPU=0,     /* Control force - Actuator units    */
CACKCUR=0,    /* Current normalisation gain        */
CACMF=0,      /* Mechanical friction - Pilot units */
CACFPMF=0;    /* Actuator force minus friction     */
/*
*/
 
  
/*
C ---------------------------------------------------
CD CRXRF110 - F/O Aileron Aip Input Calibration Macro
C ---------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CAFPOS=0;       /* Position Offset                */
/*
*/
 
/*
Outputs
*/
 
float
CAFXPU=0,     /* Control pos'n  - Actuator units   */
CAFXP=0,      /* Control pos'n  - Pilot units      */
CAFFOS=0,     /* Force offset - Actuator units     */
CAFFPU=0,     /* Control force - Actuator units    */
CAFKCUR=0,    /* Current normalisation gain        */
CAFMF=0,      /* Mechanical friction - Pilot units */
CAFFPMF=0;    /* Actuator force minus friction     */
/*
*/
 
  
/*
C ---------------------------------------------
CD CRXRF120 - Captains Aileron Servo Controller
C ---------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CACKI=0,        /* Overall current gain           */
CACIAOS=0;      /* Current Offset                 */
/*
*/
 
/*
Output
*/
 
float
CACPE=0,        /* Position Error                 */
CACIA=0;        /* Actual Current                 */
/*
Integer Input
*/
 
int
CACIPE=1;       /* Position Error enable          */
  
/*
C ----------------------------------------
CD CRXRF130 - F/O Aileron Servo Controller
C ----------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CAFKI=0,        /* Overall current gain           */
CAFIAOS=0;      /* Current Offset                 */
/*
*/
 
/*
Output
*/
 
float
CAFPE=0,        /* Position Error                 */
CAFIA=0;        /* Actual Current                 */
/*
Integer Input
*/
 
int
CAFIPE=1;       /* Position Error enable          */
  
 
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF150 MODEL MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the various band simulation models.
*/
 
/*
C -----------------------------------------------------
CD CRXRF160 - Captains Aileron Forward Mass Model Macro
C -----------------------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CACKFDMP=1.0,     /* Forward cable damping gain         */
CACFDMP=0,        /* Forward cable damping              */
CACFFRI=0,        /* Forward friction                   */
CACKIMF=1.0,      /* Inverse forward mass gain          */
CACIMF=30,        /* Inverse forward mass               */
CACFVLM=500,      /* Forward velocity limit             */
CACFNLM=(-100),   /* Forward neg. pos'n limit           */
CACFPLM=100,      /* Forward pos. pos'n limit           */
CACMVNVEL=0.5,    /* Forward stop moving velocity       */
CACZMPOS=0,       /* Control mech compliance pos dir    */
CACZMNEG=0,       /* Control mech compliance neg dir    */
CACCALDMP=0,      /* Calibration mode damping increment */
CACCALIMF=0,      /* Calibration mode IMF               */
CACCALKN=0,       /* Calibration mode 2 notch stiffness */
CACCALFOR=0,      /* Calibration mode 2 notch force     */
CACCFORLAG=3.0;   /* Cal For fade lag time constant (s) */
/*
Inputs:
*/
 
float
CACMTSTF=0,       /* Test force input from utility    */
CACBUNF=0,        /* Bungee force                     */
CACMUBF=0;        /* Mass unbalance force             */
/*
*/
 
/*
Outputs:
*/
 
float
CACDACC=0,        /* Forward acceleration             */
CACDVEL=0,        /* Forward velocity                 */
CACFFMF=0;        /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/
 
int
CACCALMOD=0,      /* Calibration mode                 */
CACFJAM=0;        /* Jammed forward quadrant flag     */
  
/*
C ------------------------------------------------
CD CRXRF170 - F/O Aileron Forward Mass Model Macro
C ------------------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CAFKFDMP=1.0,     /* Forward cable damping gain         */
CAFFDMP=0,        /* Forward cable damping              */
CAFFFRI=0,        /* Forward friction                   */
CAFKIMF=1.0,      /* Inverse forward mass gain          */
CAFIMF=30,        /* Inverse forward mass               */
CAFFVLM=500,      /* Forward velocity limit             */
CAFFNLM=(-100),   /* Forward neg. pos'n limit           */
CAFFPLM=100,      /* Forward pos. pos'n limit           */
CAFMVNVEL=0.5,    /* Forward stop moving velocity       */
CAFZMPOS=0,       /* Control mech compliance pos dir    */
CAFZMNEG=0,       /* Control mech compliance neg dir    */
CAFCALDMP=0,      /* Calibration mode damping increment */
CAFCALIMF=0,      /* Calibration mode IMF               */
CAFCALKN=0,       /* Calibration mode 2 notch stiffness */
CAFCALFOR=0,      /* Calibration mode 2 notch force     */
CAFCFORLAG=3.0;   /* Cal For fade lag time constant (s) */
/*
Inputs:
*/
 
float
CAFMTSTF=0,       /* Test force input from utility    */
CAFBUNF=0,        /* Bungee force                     */
CAFMUBF=0;        /* Mass unbalance force             */
/*
*/
 
/*
Outputs:
*/
 
float
CAFDACC=0,        /* Forward acceleration             */
CAFDVEL=0,        /* Forward velocity                 */
CAFFFMF=0;        /* Forward friction used (minus MF) */

/*
Integer Inputs:
*/
 
int
CAFCALMOD=0,      /* Calibration mode                 */
CAFFJAM=0;        /* Jammed forward quadrant flag     */

/*
C ---------------------------------------------------------------------------
CD CRXRF175 Bungee Macro
C ---------------------------------------------------------------------------
C
*/
/*
Bungee Macro
*/

/*
Parameters:
*/
float
CABNGDMP=0.1,      /* Bungee damping gain              */
CAKBUNG=20,       /* Bungee stiffness                 */
CABNGNL=0,       /* Negative force limit             */
CABNGPL=0,       /* Positive force limit             */
CABNGLAG=0;      /* Bungee reconnect gain multiplier */

/*
C ----------------------------------------
CD CRXRF180 - Captains Aileron Cable Macro
C ----------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CACCDBD=0,    /* Cable deadband                   */
CACKC=2.34,
CACCABLE=1.0;/* Cable on gain                    */
 
/*
*/
 
/*
Outputs:
*/
 
  
/*
C -----------------------------------
CD CRXRF190 - F/O Aileron Cable Macro
C -----------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CAFCDBD=0,    /* Cable deadband                   */
CAFKC=2.34,
CAFCABLE=1.0;/* Cable on gain                    */
 
/*
*/
 
/*
Outputs:
*/
  
/*
C -------------------------------------------------
CD CRXRF200 - Captains Aileron Aft Mass Model Macro
C -------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CACADMP=0,   /* Aft damping                      */
CACIMA=30,   /* Inverse aft mass                 */
CACAVLM=500, /* Aft velocity limit               */
CACAPGAIN=1, /* Autopilot Notch Gain             */
CACAPKN=0,   /* Autopilot Notch Stiffness        */
CACAPNNL=0,  /* Autopilot Neg. Notch Level       */
CACAPNPL=0,  /* Autopilot Pos. Notch Level       */
CACAPLM=100, /* Aft positive stop position       */
CACANLM=(-100);/* Aft negative stop position       */
/*
Inputs:
*/
 
float
CACAPRATE=0, /* Autopilot Rate                   */
CACMFOR=0,   /* Model force                      */
CACSFRI=0;   /* Total or spring friction         */
/*
*/
 
/*
Outputs:
*/
 
float
CACAFRI=0,   /* Aft friction                     */
CACAPUSD=0,  /* Autopilot Pos'n used at 3 kHz    */
CACQACC=0,   /* Aft acceleration                 */
CACQVEL=0;   /* Aft velocity                     */
/*
Integer Inputs:
*/
 
int
CACAJAM=0;   /* Aft jammed flag                  */
  
/*
C --------------------------------------------
CD CRXRF210 - F/O Aileron Aft Mass Model Macro
C --------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CAFADMP=0,   /* Aft damping                      */
CAFIMA=30,   /* Inverse aft mass                 */
CAFAVLM=500, /* Aft velocity limit               */
CAFAPGAIN=0, /* Autopilot Notch Gain             */
CAFAPKN=-5,   /* Autopilot Notch Stiffness        */
CAFAPNNL=-50,  /* Autopilot Neg. Notch Level       */
CAFAPNPL=50,  /* Autopilot Pos. Notch Level       */
CAFAPLM=22.5, /* Aft positive stop position       */
CAFANLM=(-22.5);/* Aft negative stop position       */
/*
Inputs:
*/
 
float
CAFAPRATE=0, /* Autopilot Rate                   */
CAFMFOR=0,   /* Model force                      */
CAFSFRI=0;   /* Total or spring friction         */
/*
*/
 
/*
Outputs:
*/
 
float
CAFAFRI=0,   /* Aft friction                     */
CAFAPUSD=0,  /* Autopilot Pos'n used at 3 kHz    */
CAFQACC=0,   /* Aft acceleration                 */
CAFQVEL=0;   /* Aft velocity                     */
/*
Integer Inputs:
*/
 
int
CAFAJAM=0;   /* Aft jammed flag                  */
  
/*
C ---------------------------------------------
CD CRXRF220 - Captains Aileron Feelspring Macro
C ---------------------------------------------
*/
 
/*
Inputs:
*/
 
float
CACTRIMV=0,  /* Trim Velocity                    */
CACKN=0,
CACNNL=0,
CACNPL=0;
/*
*/
 
/*
Outputs:
*/
 
  
/*
C ----------------------------------------
CD CRXRF230 - F/O Aileron Feelspring Macro
C ----------------------------------------
*/
 
/*
Inputs:
*/
 
float
CAFTRIMV=0,  /* Trim Velocity                    */
CAFKN=0,
CAFNNL=0,
CAFNPL=0;
/*
*/
 
/*
Outputs:
*/
 
/*
C -------------------------------------------
CD CRXRF240 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CS1PPLM=0,  /* Positive valve error limit           */
CS1PNLM=0,  /* Negative valve error limit           */
CS1PVPL=0,  /* Positive surface position rate limit */
CS1PVNL=0,  /* Negative surface position rate limit */
CS1PHG=0,   /* Flow gain                            */
CS1SPLM=0,  /* Positive surface position limit      */
CS1SNLM=0,  /* Negative surface position limit      */
CS1VREF=0;  /* Reference volume                     */
/*
Inputs:
*/
 
float
CS1CMD=0,   /* Control surface command          */
CS1HYDS=0,  /* Actuator hydraulic pressure      */
CS1QREF=0,  /* Dynamic pressure                 */
CS1XV=0,    /* Valve error                      */
CS1MAA=0,   /* 1/(piston area * moment arm)     */
CS1SHMC=0,  /* Slow hinge moment coefficients   */
CS1FHMC=0,  /* Fast hinge moment coefficients   */
CS1HMCON=0, /* constant hinge moment            */
CS1VL=0;    /* Valve leakage                    */
/*
Outputs:
*/
 
float
CS1SPOS=0,  /* Surface position                  */
CS1HM=0,    /* Surface hinge moment coefficients */
CS1HMC=0,   /* Surface hinge moment              */
CS1PL=0,    /* Surface load pressure             */
CS1FG=0;    /* Flow gain                         */
  
/*
C -------------------------------------------
CD CRXRF250 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CS2PPLM=0,  /* Positive valve error limit           */
CS2PNLM=0,  /* Negative valve error limit           */
CS2PVPL=0,  /* Positive surface position rate limit */
CS2PVNL=0,  /* Negative surface position rate limit */
CS2PHG=0,   /* Flow gain                            */
CS2SPLM=0,  /* Positive surface position limit      */
CS2SNLM=0,  /* Negative surface position limit      */
CS2VREF=0;  /* Reference volume                     */
/*
Inputs:
*/
 
float
CS2CMD=0,   /* Control surface command          */
CS2HYDS=0,  /* Actuator hydraulic pressure      */
CS2QREF=0,  /* Dynamic pressure                 */
CS2XV=0,    /* Valve error                      */
CS2MAA=0,   /* 1/(piston area * moment arm)     */
CS2SHMC=0,  /* Slow hinge moment coefficients   */
CS2FHMC=0,  /* Fast hinge moment coefficients   */
CS2HMCON=0, /* constant hinge moment            */
CS2VL=0;    /* Valve leakage                    */
/*
Outputs:
*/
 
float
CS2SPOS=0,  /* Surface position                  */
CS2HM=0,    /* Surface hinge moment coefficients */
CS2HMC=0,   /* Surface hinge moment              */
CS2PL=0,    /* Surface load pressure             */
CS2FG=0;    /* Flow gain                         */
  
/*
C -------------------------------------------
CD CRXRF260 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CS3PPLM=0,  /* Positive valve error limit           */
CS3PNLM=0,  /* Negative valve error limit           */
CS3PVPL=0,  /* Positive surface position rate limit */
CS3PVNL=0,  /* Negative surface position rate limit */
CS3PHG=0,   /* Flow gain                            */
CS3SPLM=0,  /* Positive surface position limit      */
CS3SNLM=0,  /* Negative surface position limit      */
CS3VREF=0;  /* Reference volume                     */
/*
Inputs:
*/
 
float
CS3CMD=0,   /* Control surface command          */
CS3HYDS=0,  /* Actuator hydraulic pressure      */
CS3QREF=0,  /* Dynamic pressure                 */
CS3XV=0,    /* Valve error                      */
CS3MAA=0,   /* 1/(piston area * moment arm)     */
CS3SHMC=0,  /* Slow hinge moment coefficients   */
CS3FHMC=0,  /* Fast hinge moment coefficients   */
CS3HMCON=0, /* constant hinge moment            */
CS3VL=0;    /* Valve leakage                    */
/*
Outputs:
*/
 
float
CS3SPOS=0,  /* Surface position                  */
CS3HM=0,    /* Surface hinge moment coefficients */
CS3HMC=0,   /* Surface hinge moment              */
CS3PL=0,    /* Surface load pressure             */
CS3FG=0;    /* Flow gain                         */
  
/*
C -------------------------------------------
CD CRXRF270 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CS4PPLM=0,  /* Positive valve error limit           */
CS4PNLM=0,  /* Negative valve error limit           */
CS4PVPL=0,  /* Positive surface position rate limit */
CS4PVNL=0,  /* Negative surface position rate limit */
CS4PHG=0,   /* Flow gain                            */
CS4SPLM=0,  /* Positive surface position limit      */
CS4SNLM=0,  /* Negative surface position limit      */
CS4VREF=0;  /* Reference volume                     */
/*
Inputs:
*/
 
float
CS4CMD=0,   /* Control surface command          */
CS4HYDS=0,  /* Actuator hydraulic pressure      */
CS4QREF=0,  /* Dynamic pressure                 */
CS4XV=0,    /* Valve error                      */
CS4MAA=0,   /* 1/(piston area * moment arm)     */
CS4SHMC=0,  /* Slow hinge moment coefficients   */
CS4FHMC=0,  /* Fast hinge moment coefficients   */
CS4HMCON=0, /* constant hinge moment            */
CS4VL=0;    /* Valve leakage                    */
/*
Outputs:
*/
 
float
CS4SPOS=0,  /* Surface position                  */
CS4HM=0,    /* Surface hinge moment coefficients */
CS4HMC=0,   /* Surface hinge moment              */
CS4PL=0,    /* Surface load pressure             */
CS4FG=0;    /* Flow gain                         */
  
/*
C --------------------------------------------------------------
CD CRXRF280 - Spoilers Aft Mass Model Macro
C --------------------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CS5PPLM=0,  /* Positive valve error limit           */
CS5PNLM=0,  /* Negative valve error limit           */
CS5PVPL=0,  /* Positive surface position rate limit */
CS5PVNL=0,  /* Negative surface position rate limit */
CS5PHG=0,   /* Flow gain                            */
CS5SPLM=0,  /* Positive surface position limit      */
CS5SNLM=0,  /* Negative surface position limit      */
CS5VREF=0;  /* Reference volume                     */
/*
Inputs:
*/
 
float
CS5CMD=0,   /* Control surface command          */
CS5HYDS=0,  /* Actuator hydraulic pressure      */
CS5QREF=0,  /* Dynamic pressure                 */
CS5XV=0,    /* Valve error                      */
CS5MAA=0,   /* 1/(piston area * moment arm)     */
CS5SHMC=0,  /* Slow hinge moment coefficients   */
CS5FHMC=0,  /* Fast hinge moment coefficients   */
CS5HMCON=0, /* constant hinge moment            */
CS5VL=0;    /* Valve leakage                    */
/*
Outputs:
*/
 
float
CS5SPOS=0,  /* Surface position                  */
CS5HM=0,    /* Surface hinge moment coefficients */
CS5HMC=0,   /* Surface hinge moment              */
CS5PL=0,    /* Surface load pressure             */
CS5FG=0;    /* Flow gain                         */
  
/*
C -------------------------------------------
CD CRXRF290 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CS6PPLM=0,  /* Positive valve error limit           */
CS6PNLM=0,  /* Negative valve error limit           */
CS6PVPL=0,  /* Positive surface position rate limit */
CS6PVNL=0,  /* Negative surface position rate limit */
CS6PHG=0,   /* Flow gain                            */
CS6SPLM=0,  /* Positive surface position limit      */
CS6SNLM=0,  /* Negative surface position limit      */
CS6VREF=0;  /* Reference volume                     */
/*
Inputs:
*/
 
float
CS6CMD=0,   /* Control surface command          */
CS6HYDS=0,  /* Actuator hydraulic pressure      */
CS6QREF=0,  /* Dynamic pressure                 */
CS6XV=0,    /* Valve error                      */
CS6MAA=0,   /* 1/(piston area * moment arm)     */
CS6SHMC=0,  /* Slow hinge moment coefficients   */
CS6FHMC=0,  /* Fast hinge moment coefficients   */
CS6HMCON=0, /* constant hinge moment            */
CS6VL=0;    /* Valve leakage                    */
/*
Outputs:
*/
 
float
CS6SPOS=0,  /* Surface position                  */
CS6HM=0,    /* Surface hinge moment coefficients */
CS6HMC=0,   /* Surface hinge moment              */
CS6PL=0,    /* Surface load pressure             */
CS6FG=0;    /* Flow gain                         */
  
/*
C -------------------------------------------
CD CRXRF300 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CS7PPLM=0,  /* Positive valve error limit           */
CS7PNLM=0,  /* Negative valve error limit           */
CS7PVPL=0,  /* Positive surface position rate limit */
CS7PVNL=0,  /* Negative surface position rate limit */
CS7PHG=0,   /* Flow gain                            */
CS7SPLM=0,  /* Positive surface position limit      */
CS7SNLM=0,  /* Negative surface position limit      */
CS7VREF=0;  /* Reference volume                     */
/*
Inputs:
*/
 
float
CS7CMD=0,   /* Control surface command          */
CS7HYDS=0,  /* Actuator hydraulic pressure      */
CS7QREF=0,  /* Dynamic pressure                 */
CS7XV=0,    /* Valve error                      */
CS7MAA=0,   /* 1/(piston area * moment arm)     */
CS7SHMC=0,  /* Slow hinge moment coefficients   */
CS7FHMC=0,  /* Fast hinge moment coefficients   */
CS7HMCON=0, /* constant hinge moment            */
CS7VL=0;    /* Valve leakage                    */
/*
Outputs:
*/
 
float
CS7SPOS=0,  /* Surface position                  */
CS7HM=0,    /* Surface hinge moment coefficients */
CS7HMC=0,   /* Surface hinge moment              */
CS7PL=0,    /* Surface load pressure             */
CS7FG=0;    /* Flow gain                         */
  
/*
C -------------------------------------------
CD CRXRF310 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CS8PPLM=0,  /* Positive valve error limit           */
CS8PNLM=0,  /* Negative valve error limit           */
CS8PVPL=0,  /* Positive surface position rate limit */
CS8PVNL=0,  /* Negative surface position rate limit */
CS8PHG=0,   /* Flow gain                            */
CS8SPLM=0,  /* Positive surface position limit      */
CS8SNLM=0,  /* Negative surface position limit      */
CS8VREF=0;  /* Reference volume                     */
/*
Inputs:
*/
 
float
CS8CMD=0,   /* Control surface command          */
CS8HYDS=0,  /* Actuator hydraulic pressure      */
CS8QREF=0,  /* Dynamic pressure                 */
CS8XV=0,    /* Valve error                      */
CS8MAA=0,   /* 1/(piston area * moment arm)     */
CS8SHMC=0,  /* Slow hinge moment coefficients   */
CS8FHMC=0,  /* Fast hinge moment coefficients   */
CS8HMCON=0, /* constant hinge moment            */
CS8VL=0;    /* Valve leakage                    */
/*
Outputs:
*/
 
float
CS8SPOS=0,  /* Surface position                  */
CS8HM=0,    /* Surface hinge moment coefficients */
CS8HMC=0,   /* Surface hinge moment              */
CS8PL=0,    /* Surface load pressure             */
CS8FG=0;    /* Flow gain                         */
  
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF320 EXTRA MODEL VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains all the extra variables not required by the macros
CC including symbol definitions, FGEN outputs and function labels. Each
CC function label must be declared as an integer and have a default value
CC of -1.
*/
 
/*
*     ---------------------
*     EXTRA MODEL VARIABLES
*     ---------------------
*/
int
CATRANS=0,   /* Transfer enable flag */
CATUNE=0,    /* Tune enable flag */
CAFGENI=0,   /* Fgen index        */
CASCALC=0,   /* Fgen slope calculate request */
CACPLOT=0,   /* Label to be sent to Chan 1 Test Point A */
CAFPLOT=0;   /* Label to be sent to Chan 1 Test Point B */
 
float
CACSCALE = 0, /* Scale factor for plot */
CAFSCALE = 0, /* Scale factor for plot */
 
CAQBKPT[4]  = {    0,    33,     48,     112},
CACQBKPT[4]  = {    0,    33,     48,     112},
CAFQBKPT[4]  = {    0,    33,     48,     112},

/* Values for 100 */
 
CACVIMF[4]  = { 1000,  1700,   1200,    1000},
CACVFF[4]   = {  .35,    .0,      1,      .9},/*{ .7,    .0,      2,    1.8},*/
CACVCFDMP[4] = {   0.,   .00,   0.01,    .005},
CACVFFDMP[4] = {   0.,   .00,      0,    .005},
CACVCIMA[4]  = { 1000,  1000,    400,    1000},
CACVFIMA[4]  = {  100,   400,    400,     100},
CACVCADMP[4] = { 0.01,  0.00,   0.03,    0.01},
CACVFADMP[4] = { 0.01,  0.01,   0.03,    0.03},

/* Values for 300 */

CAFVIMF[4]  = { 1000,  1700,    1200,    1000},
CAFVFF[4]   = {  .35,    .0,       1,      .9},
CAFVCFDMP[4] = {   0.,   .00,   0.01,    .005},
CAFVFFDMP[4] = {0.005,  .005,  0.005,    .005},
CAFVCIMA[4]  = { 1000,  1000,    400,    1000},
CAFVFIMA[4]  = {  100,   400,    400,     100},
CAFVCADMP[4] = { 0.01,  0.00,   0.03,    0.01},
CAFVFADMP[4] = { 0.01,  0.01,   0.03,    0.03},

CASIMF[4]   = {0,0,0,0},
CASFF[4]    = {0,0,0,0},
CACSFDMP[4] = {0,0,0,0},
CAFSFDMP[4] = {0,0,0,0},
CACSIMA[4]  = {0,0,0,0},
CAFSIMA[4]  = {0,0,0,0},
CACSADMP[4] = {0,0,0,0},
CAFSADMP[4] = {0,0,0,0},
 
CAOIMF   = 0,
CAOFF    = 0,
CACOFDMP = 0,
CAFOFDMP = 0,
CACOIMA  = 0,
CAFOIMA  = 0,
CACOADMP = 0,
CAFOADMP = 0,
 
CACIMFI=0,
CAFIMFI=0,
CACFDMPI=0,
CAFFDMPI=0,
 
CAB0=0,        /* Hinge moment coef constant */
CAB1=0,        /* Hinge moment coef alpha term */
CAB2=(-0.0811),  /* Hinge moment coef surface term*/
CAB3=(-0.37),  /* Hinge moment coef trim term */

/* 100 data */

CACB0=0,        /* Hinge moment coef constant */
CACB1=0,        /* Hinge moment coef alpha term */
CACB2=(-0.0811),  /* Hinge moment coef surface term*/
CACB3=(-0.37),  /* Hinge moment coef trim term */
CACM=0.35,
CAFANLM1=(-22.5),/* Aft negative stop position       */

/* 300 data */

CAFB0=0,        /* Hinge moment coef constant */
CAFB1=0,        /* Hinge moment coef alpha term */
CAFB2=(-0.0811),  /* Hinge moment coef surface term*/
CAFB3=(-0.37),  /* Hinge moment coef trim term */
CAFM=0.27,
CAFANLM3=(-17),/* Aft negative stop position       */

CAKC = 0,
 
CACHMC=0,      /* Hinge Moment Coef          */
CACHM=0,      /* Hinge Moment           */
CAFHMC=0,      /* Hinge Moment Coef          */
CAFHM=0,      /* Hinge Moment           */
 
CACHORD=1.45,
CASAREA=8.18,
CAASPLR=0,
CAWDIA=0.604,
CAM=0.35,
CANQPOS=0,
CAB2F0=(-0.0811),
CAB2F35=(-0.0811),
CACGLOCK=0.1,
CACSFOR=0,
CAFSFOR=0;
 
int
C3SFREZ=0,
C3AFREZ=0;
/*
*         -------
**        SPOILER
*         -------
*/
 
#define  CSGND   CSGCMD.bit_1  /* ground spoilers*/
#define  CSINBD  CSGCMD.bit_2  /* inbd ground spoilers*/
#define  CSOUTBD CSGCMD.bit_3  /* outbd ground spoilers*/

float
CSPPLM=2,  /* Positive valve error limit           */
CSPNLM=(-2),  /* Negative valve error limit           */
CSPVDB = 0.5, /* deadband in spoilers */
CSPVPL=200,  /* Positive surface position rate limit */
CSPVNL=(-200),  /* Negative surface position rate limit */
CSSPLM=70,  /* Positive surface position limit      */
CSSNLM=0,  /* Negative surface position limit      */
CSPHGS=0.12,   /* Flow gain                            */
CSPHGG=0.05,   /* Flow gain                            */
CSVRFS=1,  /* Reference volume                     */
CSVRFG=1,  /* Reference volume                     */
CSMAAS=1,   /* 1/(piston area * moment arm)     */
CSMAAG=1,   /* 1/(piston area * moment arm)     */
CSVL=0.01,    /* Valve leakage                    */
CSFHMC=.1,  /* Fast hinge moment coefficients   */
CSCONST = .5,/* Gain on constant hold down pressure */
CS3CMDF = 0, /* PCU input for fgen */
CS4CMDF = 0, /* PCU input for fgen */
CS5CMDF = 0, /* PCU input for fgen */
CS6CMDF = 0, /* PCU input for fgen */
CSJFORCE = 0, /* Force from spoiler jam clutches */
CS3JFORCE = 0, /* Force from spoiler jam clutches */
CS4JFORCE = 0, /* Force from spoiler jam clutches */
CS5JFORCE = 0, /* Force from spoiler jam clutches */
CS6JFORCE = 0, /* Force from spoiler jam clutches */
CSSZM = (-.003), /* Simulated Mechanical compliance of spoilers */
CSKNJ = 0,   /* Breakout slope on spoiler clutches */
CSJLT =  2, /* Jam lt thresh hold */
CSJBRK = 5,  /* breakout force for one spoiler clutch */
CSJDET = 5;  /* breakout detent width for one spoiler clutch */
 
/* The following are for tune gain calculations */
 
float
CACPECNT  = 0,
CACPESLOPE = 0,
CACSUMXP   = 0,
CACSUMXP2  = 0,
CACSUMP    = 0,
CACSUMXPP  = 0,
CAFPECNT  = 0,
CAFPESLOPE = 0,
CAFSUMXP   = 0,
CAFSUMXP2  = 0,
CAFSUMP    = 0,
CAFSUMXPP  = 0;
 
int
CACPERST   = 0,
CAFPERST   = 0;
 
/*   FGEN FUNCTIONS   */
int
CAGEAR   = -1,
CSGEAR   = -1,
CAGEAR3  = -1,
CSGEAR3  = -1
;
 
/* 
C -----------------------------------------------------------------------------
CD CRXRF330 ROLL CONTROLS THROUGHPUT PARAMETERS
C -----------------------------------------------------------------------------
C
CC The following variables are used by the throughput test macro to read
CC the different inputs from the logic request buffer.
*/
 
int
THPUT_ENBL = 0,
THPUT_TRIG = 0,
THPUT_AXIS = 0;
 
#define    C30_AXIS    2              /* C30 card axis, pitch =  1  */
                                      /*                roll  =  2  */
                                      /*                yaw   =  3  */
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF340 GENERAL SERVO CONTROLLER CONSTANTS
C -----------------------------------------------------------------------------
C
CC The following variables are used to normalize the acceleration, velocity
CC and position gains for the servo controller. They are used in the
CC computation of KANOR, KVNOR and KPNOR which is done in the controls
CC operation mode and safety macro.
*/
 
float
KACONST = 0.0013,        /* KA CONSTANT TO GIVE REASONABLE NORMALIZED KA */
KVCONST = 0.08925,       /* KV CONSTANT TO GIVE REASONABLE NORMALIZED KV */
KPCONST = 1.;            /* KP CONSTANT TO GIVE REASONABLE NORMALIZED KP */
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF350 ADIO CARD DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The ADIO has: - 8 analog inputs
CC               - 8 analog ouputs
CC               - 16 digital inputs  (1 word)
CC               - 16 digital outputs (1 word)
CC
CC The following buffers are used to store the values written to and read
CC from the ADIO card. The input and output variables must be organized
CC to form two blocks in memory. This is assured by the use of structures.
*/
 
#define ADIO_SLOT 13
 
int ADIO_ERROR = 0;
 
struct ADIO
{
  int A[8];
  int D;
};
struct ADIO ADIO_IP = {{0,0,0,0,0,0,0,0},0};
struct ADIO ADIO_OP = {{0,0,0,0,0,0,0,0},0};
 
#define ADIO_AIP ADIO_IP.A
#define ADIO_DIP ADIO_IP.D
#define ADIO_AOP ADIO_OP.A
#define ADIO_DOP ADIO_OP.D
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF360 CONTROL LOADING CHANNEL DEFINITIONS
C -----------------------------------------------------------------------------
C
CC Each channel on this C30 card must be given an integer identification
CC number, incrementing from 0.  Each ADIO has a maximium of 4 channels,
CC channel 0 connecting to Buffer Unit 1, channel 1 to Buffer Unit 2, etc.
*/
 
#define    NUM_CHANNEL       2    /* Total number of channels on this card */
 
#define    CAC_CHAN         0    /* Captains Aileron */
#define    CAF_CHAN         1    /* F/O Aileron */
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF370 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The next DIP/DOP definitions are used by the control mode operation and
CC safety routine to hold the buffer units input/output status.
*/
 
#define    CAC_PWR_DIP       0x0001     /*  BU #1 power failure      */
#define    CAF_PWR_DIP       0x0010     /*  BU #2 power failure      */
 
#define    CAC_STBY_DIP      0x0002     /*  BU #1 in standby mode    */
#define    CAF_STBY_DIP      0x0020     /*  BU #2 in standby mode    */
 
#define    CAC_NORM_DIP      0x0004     /*  BU #1 in normal mode     */
#define    CAF_NORM_DIP      0x0040     /*  BU #2 in normal mode     */
 
#define    CAC_NULL_MASK     0x000f     /*  BU #1 no signal mask     */
#define    CAF_NULL_MASK     0x00f0     /*  BU #2 no signal mask     */
 
#define    CAC_TOGGLE_DOP    0x0001     /*  BU #1 computer iterating */
#define    CAF_TOGGLE_DOP    0x0010     /*  BU #2 computer iterating */
 
#define    CAC_HYDR_DOP      0x0002     /*  BU #1 hydraulic ready    */
#define    CAF_HYDR_DOP      0x0020     /*  BU #2 hydraulic ready    */
 
#define    CAC_STBY_DOP      0x0004     /*  BU #1 standby request    */
#define    CAF_STBY_DOP      0x0040     /*  BU #2 standby request    */
 
int
BUDIP = 0;          /* Buffer unit digital input    */
 
int
BUDOP = 0;          /* Buffer unit digital output   */
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF380 LOGIC TO C30 CHANNEL REQUEST BUFFER
C -----------------------------------------------------------------------------
C
CC The next lines contains the structure of the buffer that is used by the
CC DN1 logic to send a request to the Roll control system.
*/
 
struct L2C_REQUEST                /* Logic to C30 buffer structure          */
{
  int toggle;                     /* Iteration toggle sent by logic         */
  int cl_request;                 /* Control loading operation mode request */
  int mot_request;                /* Motion operation mode request          */
  int thruput;                    /* Throughput request parameter           */
  int logic_options;              /* Logic options                          */
  int logic_state;                /* Logic status                           */
  int cab_state;                  /* Cabinet status                         */
  int fail_reset;                 /* Failure reset button request           */
};
 
struct L2C_REQUEST LOGIC_REQUEST; /* Logic to C30 buffer name declaration   */
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF390 C30 TO LOGIC CHANNEL STATUS BUFFER
C -----------------------------------------------------------------------------
C
CC The next buffer is sent to the DN1 logic to specify the current controls
CC mode of operation. It also sends back the iteration toggle.
*/
 
struct C2L_STATUS                 /* Channel status buffer structure        */
{
  int toggle;                     /* Iteration toggle sent back to logic    */
  int status;                     /* Channel status                         */
};
 
struct C2L_STATUS CHANNEL_STATUS[NUM_CHANNEL];   /* buffer name declaration */
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF400 C30 TO LOGIC CHANNEL DEFINITION BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer is used to specify the channel names to be displayed with the
CC DN1 messages. It also contains the number and type of channels defined.
*/
 
struct C2L_DEFINITION {           /* Channel definition buffer structure    */
  int number;                     /* Total number of channels defined       */
  int type;                       /* Channels type (1 for control loading)  */
  int name[NUM_CHANNEL][3];       /* Channels names in the first element [0]*/
  };
 
struct C2L_DEFINITION CHANDEF;    /* Channel definition buffer declaration  */
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF410 C30 TO LOGIC ERROR LOGGER BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer contains a list of error codes that are to be displayed on
CC the DN1 display window.
*/
 
#define MAX_ERROR 10              /* Maximum number of errors in buffer     */
 
struct C2L_ERROR                  /* Error logger buffer structure          */
{
  int number;                     /* Error number index                     */
  int code[MAX_ERROR];            /* Error type                             */
};
 
struct C2L_ERROR CHANERR;         /* Error logger buffer declaration        */
 
 
/*
C -----------------------------------------------------------------------------
CD CRXRF420 LOCAL ERROR BUFFER
C -----------------------------------------------------------------------------
C
CC The next flag is set to TRUE whenever the corresponding channel has been
CC failed and the hydraulics are turned off for the controls.
*/
 
int FAILED[NUM_CHANNEL];          /* Channel failed flag                    */
 
 
/*
C$
C$--- Section Summary
C$
C$ 00041 CRXRF010 SYSTEM TIME RATES LABELS                                     
C$ 00055 CRXRF020 ROLL CARD TRANSFERS LABELS                                   
C$ 00073 CRXRF030 SYSTEM MACRO VARIABLES                                       
C$ 00082 CRXRF040 - Captains Aileron Mode Control Macro                        
C$ 00191 CRXRF050 - F/O Aileron Mode Control Macro                             
C$ 00300 CRXRF060 - Captains Aileron Backdrive Macro                           
C$ 00347 CRXRF070 - F/O Aileron Backdrive Macro                                
C$ 00394 CRXRF080 - Captains Aileron Throughput Macro                          
C$ 00413 CRXRF090 - F/O Aileron Throughput Macro                               
C$ 00432 CRXRF100 - Captains Aileron Aip Input Calibration Macro               
C$ 00464 CRXRF110 - F/O Aileron Aip Input Calibration Macro                    
C$ 00496 CRXRF120 - Captains Aileron Servo Controller                          
C$ 00529 CRXRF130 - F/O Aileron Servo Controller                               
C$ 00564 CRXRF140 MODEL MACRO VARIABLES TO BE DECLARE AT ALL TIME              
C$ 00585 CRXRF150 MODEL MACRO VARIABLES                                        
C$ 00594 CRXRF160 - Captains Aileron Forward Mass Model Macro                  
C$ 00656 CRXRF170 - F/O Aileron Forward Mass Model Macro                       
C$ 00718 CRXRF180 - Captains Aileron Cable Macro                               
C$ 00745 CRXRF190 - F/O Aileron Cable Macro                                    
C$ 00772 CRXRF200 - Captains Aileron Aft Mass Model Macro                      
C$ 00820 CRXRF210 - F/O Aileron Aft Mass Model Macro                           
C$ 00868 CRXRF220 - Captains Aileron Feelspring Macro                          
C$ 00893 CRXRF230 - F/O Aileron Feelspring Macro                               
C$ 01264 CRXRF320 EXTRA MODEL VARIABLES                                        
C$ 01474 CRXRF330 ROLL CONTROLS THROUGHPUT PARAMETERS                          
C$ 01493 CRXRF340 GENERAL SERVO CONTROLLER CONSTANTS                           
C$ 01510 CRXRF350 ADIO CARD DEFINITIONS                                        
C$ 01543 CRXRF360 CONTROL LOADING CHANNEL DEFINITIONS                          
C$ 01559 CRXRF370 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS             
C$ 01596 CRXRF380 LOGIC TO C30 CHANNEL REQUEST BUFFER                          
C$ 01620 CRXRF390 C30 TO LOGIC CHANNEL STATUS BUFFER                           
C$ 01638 CRXRF400 C30 TO LOGIC CHANNEL DEFINITION BUFFER                       
C$ 01656 CRXRF410 C30 TO LOGIC ERROR LOGGER BUFFER                             
C$ 01676 CRXRF420 LOCAL ERROR BUFFER                                           
*/
