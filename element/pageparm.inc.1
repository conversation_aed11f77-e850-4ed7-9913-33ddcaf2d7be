C
C --- Include file PAGEPARM.INC
C
C --- This file contains parameter constant values for use by the
C     I/F compiler utilities
C
C
C -- Compiler release numbers and dates
C
      CHARACTER
     .          PGC_RLNM*(4),
     .          PGC_RLDT*(12),
     .          ASC_RLNM*(4),
     .          ASC_RLDT*(12),
     .          IPD_RLNM*(4),
     .          IPD_RLDT*(12),
     .          PAN_RLNM*(4),
     .          PAN_RLDT*(12),
     .          GRC_RLNM*(4),
     .          GRC_RLDT*(12)
C
      PARAMETER (
     .          PGC_RLNM = '3.1 ',       ! PGC utility release number
     .          PGC_RLDT = '05-OCT-1990',! PGC utility release date
     .          ASC_RLNM = '3.1 ',       ! ASC utility release number
     .          ASC_RLDT = '05-OCT-1990',! ASC utility release date
     .          IPD_RLNM = '2.0 ',       ! IPD utility release number
     .          IPD_RLDT = '21-SEP-1989',! IPD utility release date
     .          PAN_RLNM = '2.0 ',       ! PANEL utility release number
     .          PAN_RLDT = '21-SEP-1989',! PANEL utility release date
     .          GRC_RLNM = '3.01',       ! GRC utility release number
     .          GRC_RLDT = '20-SEP-1990' ! GRC utility release date
     .          )
C
C
C
      INTEGER*2
     .          WIN_XTL,                 ! X top left coord window
     .          WIN_YTL,                 ! Y top left coord window
     .          WIN_XBR,                 ! X bottom right coord window
     .          WIN_YBR                  ! Y bottom right coord window
C
C -- Compiler specifications and restrictions
C
CIBM
      INTEGER*4
     .          IBMMAX_ESLN
CIBMEND
      INTEGER*2
CJ     .          MAXPAGE,      ! NOW IN PAGECODE.INC AS MAX_PGNO
     .          MAX_ESLN,
     .          MAXRANGE,
     .          MAX_MULTS,      ! Same as AMAX_MULTS in PAGECODE.INC
     .          MAX_FLDLEN,
     .          MAX_VSLN,
     .          MAX_FILENM,
     .          MAX_PATHLN,
     .          MAX_PGSRLN,
     .          MAX_LABLN,
     .          MAX_COLRLN,
     .          MAX_UNITLN,
     .          MAX_BSLN,
     .          MAX_DSLN,
     .          MAX_DSTRS,
     .          AMAX_DSTRS,
     .          MAX_ORLN,
     .          MAX_BSCLN,
     .          MAX_ROTLN,
     .          MAX_LTLN,
     .          MAX_TXTLN,
     .          MAX_SYMLN,
     .          MAX_SRCLN,
     .          MAX_SPLEN,
     .          MAX_MODELN,
     .          MAX_SIDLEN,
     .          MAX_CRIT,
     .          MAX_COND,
     .          MAX_IFCOND,
     .          MAX_ITEMS,
     .          MAX_USERLN,
     .          MAX_OBJBUF,
     .          MAX_VALPG,
     .          MAX_PGDIM,
     .          MAX_AREAS,
     .          MAX_LINENM,
     .          MAX_VTBSZ,
     .          MAX_ILEN,
     .          MIN_PGCLR,
     .          MAX_PGCLR,
     .          MIN_IPDCLR,
     .          MAX_IPDCLR,
     .          MAX_NPDM,
CJ     .          MAX_PDMROW,
CJ     .          MAX_PDMCOL,
CJ     .          MAX_MENU,
CJ     .          MAX_GRPAR,
     .          MAX_VLTBSZ,
     .          MAX_PGTRNG,
     .          MAX_SVALP,
C
C_RB  These have be brought over from ASCPARM.INC for CHOICES.INC
C
     .          MODE_MAXNO,
     .          LTYPE_MAXNO,
     .          COLR_MINNO,
     .          COLR_MAXNO,
     .          ORIEN_MAXNO,
     .          HORI_INDEX,
     .          VERT_INDEX,
     .          DFLAGS_MAXNO,
     .          SIDE_MAXNO,
C
C_RB  These are new for CHOICES.INC
C
     .          BSCAL_MAXNO,
     .          LOG_INDEX,
     .          LIN_INDEX
C
C
      INTEGER*4
     .          MAX_ORECLEN,
     .          MAX_OMAXREC,
     .          MAX_OEXTEND,
     .          MAX_BASE,
     .          MIN_BASE,
     .          NO_BASE
C
      INTEGER*4
     .          INIT_PGSIZ,
     .          SCRBUF_SIZE
C
      PARAMETER (
CJ     .          MAXPAGE = 1100,    ! SEE PAGECODE.INC MAX_PGNO
     .          MAXRANGE = 25,     ! Maximum number of ranges
                                   !    allowed on command line
     .          MAX_MULTS=10,      ! maximum number of multiple DCBs
     .                             ! must be the same as AMAX_MULTS in
     .                             ! PAGECODE.INC
     .          MAX_PGSRLN=132,    ! max page src record len
     .          MAX_FLDLEN=20*MAX_PGSRLN,! maximum field length
     .          MAX_VSLN=79,   ! max validation string length
     .          MAX_ESLN=MAX_PGSRLN,     ! max expression  string length
CIBM
     .          IBMMAX_ESLN=MAX_ESLN,
CIBMEND
     .          MAX_FILENM=60,     ! max file name length (incl. path)
     .          MAX_PATHLN=40,     ! maximum directory path length
     .          MAX_LABLN=24,      ! maximum CDB label length
     .          MAX_COLRLN=12,     ! maximum colour specification length
     .          MAX_UNITLN=6,      ! maximum unit string length
     .          MAX_BSLN=10,       ! max boolean (true/false) str len
     .          MAX_DSLN=10,       ! max boolean display code str len
     .          MAX_DSTRS=45,      ! max number of display codes
     .          AMAX_DSTRS=100,    ! Absolute max, predefined disp codes
     .          MAX_ORLN=10,       ! max orientation string length
     .          MAX_BSCLN=11,      ! max barchart scale string length
     .          MAX_ROTLN=3,       ! max rotation string length
     .          MAX_LTLN=8,        ! max linetype string length
     .          MAX_TXTLN=100,     ! max text string length
     .          MAX_SRCLN=10,      ! max source name length
     .          MAX_SYMLN=25,      ! max symbol name length
     .          MAX_SPLEN=10,      ! max space specifier string length
     .          MAX_MODELN=8,      ! max schematics mode string length
     .          MAX_SIDLEN=5,      ! max arrow side string length
     .          MAX_CRIT=3,        ! max # criteria on set value
     .          MAX_COND=20,       ! max # conditions/expressions
     .          MAX_IFCOND=2,      ! max # conditions used in IF_THEN
     .          MAX_ITEMS=100,     ! maximum number of items for
                                   !  instructions with 'unlimited'
                                   !  number of a certain item
     .          MAX_USERLN=4,      ! maximum user name length
     .          MAX_OBJBUF = 256,  ! Maximum object buffer
     .          MAX_ORECLEN = 256, ! Object file record length
     .          MAX_OMAXREC = 20,  ! Object file maximum record
     .          MAX_OEXTEND = 32,  ! Object file extend quantity
     .          MAX_VALPG=25,      ! Max # validpage ranges
     .          MAX_PGDIM=100,     ! Max # pages dimension ranges
     .          MAX_AREAS=50,      ! Max # color area definitions
     .          MAX_LINENM=100,    ! Max input line number
     .          MAX_VTBSZ=600,     ! Max volatile table size(expandable)
     .          MAX_ILEN=12,       ! Maximum length of instruction
                                   !  keyword (actual length of
                                   !  keywords are defined elsewhere)
     .          INIT_PGSIZ=200,    ! Default new data file size (blocks)
     .          SCRBUF_SIZE=10240, ! Scratch buffer size (10K) bytes
     .          MIN_PGCLR=0,       ! Min page compiler color code
     .          MAX_PGCLR=15,      ! Max page compiler color code
     .          MIN_IPDCLR=0,      ! Min IPD compiler color code
     .          MAX_IPDCLR=0,      ! Max IPD compiler color code
     .          MAX_NPDM=50,       ! Max possible number of PDMs/menu
CJ     .          MAX_PDMROW=2,      ! Max PDM row dimension
CJ     .          MAX_PDMCOL=20,     ! Max PDM column dimension
CJ     .          MAX_MENU = 1000,   ! Maximum number of IPD menus
CJ     .          MAX_GRPAR = 999,   ! Maximum number of GR parameters
     .          MAX_VLTBSZ = 5120, ! Max value table array size (bytes)
     .          MAX_PGTRNG=25,     ! Max pagetype range table size
     .          MAX_SVALP=4 )      ! Max sv item alpha string fractions
C
      PARAMETER (
     .          HORI_INDEX = 1,             ! Horizontal orient. index
     .          VERT_INDEX = 2,             ! Vertical orient. index
     .          MODE_MAXNO = 20,            ! Max no. of modes
     .          LTYPE_MAXNO = 4,            ! Max no. of line types
     .          COLR_MINNO = 0,             ! Min color index
     .          COLR_MAXNO = 15,            ! Max no. of avail. colors
     .          ORIEN_MAXNO = 2,            ! Max no. of orientations
     .          DFLAGS_MAXNO = COLR_MAXNO,  ! Max no. of dummy flags
     .          SIDE_MAXNO = 2,             ! Max no. of arrow sides
     .          BSCAL_MAXNO = 2,            ! Max no. of barchart scales
     .          LIN_INDEX = 1,              ! Linear scale index
     .          LOG_INDEX = 2               ! Logarithmic scale index
     .          )
C
      PARAMETER (
CVAX
CVAX .          MAX_BASE =  8,
CVAXEND
CSGI
CSGI .          MAX_BASE =  99,
CSGIEND
CIBM
     .          MAX_BASE =  90,
CIBMEND
     .          MIN_BASE =  0,
     .          NO_BASE  = -1
     .          )
C
C
C -- Input/Ouput
C
C -- NOTE:   LUN 9 and 10 are used by BUFF I/O and can not be
C            used for FORTRAN I/O.
C
C -- NOTE:   Don't use unit number 11. It appears that this is being
C            used by library routine QBLKIO which performs NBLKOPEN,
C            NBLKWRITE, NBLKCLOSE etc.
C
      INTEGER*4 SRC, OBJ, LST, ERR, OT, IT, PDA, TMP, LKP , DEL, INF,
     .          VAL
CIBM
      INTEGER*4 SECTOR_BYTES
CIBMEND
C
      INTEGER*2 PDA_RECLN
C
CVAX
CVAX  CHARACTER*12 ERRLOGN
CVAXEND
CIBM
      CHARACTER*9 ERRLOGN
CIBMEND
CSGI
CSGI  CHARACTER*9 ERRLOGN
CSGIEND
C
      CHARACTER*10 ERRFILE
C
      PARAMETER (
CVAX
CVAX .          SRC = 1,                      ! Source file LUN
CVAX .          OBJ = 2,                      ! Object file LUN
CVAX .          LST = 3,                      ! Listing file LUN
CVAX .          ERR = 4,                      ! Error message file LUN
CVAX .          IT = 5,                       ! Terminal input LUN
CVAX .          OT = 6,                       ! Terminal output LUN
CVAX .          PDA = 7,                      ! Direct access file LUN
CVAX .          TMP = 8,                      ! Temporary file LUN
CVAX                                          ! don't use 11
CVAX .          INF = 12,                     ! INFILE file LUN
CVAX .          LKP = 13,                     ! LINKPAGE file LUN
CVAX .          VAL = 14,                     ! VALIDATION file LUN
CVAX .          DEL = 88,                     ! Delete object file LUN
CVAXEND
CIBM
     .          SRC = 1,                      ! Source file LUN
     .          OBJ = 2,                      ! Object file LUN
     .          LST = 3,                      ! Listing file LUN
     .          ERR = 4,                      ! Error message file LUN
     .          IT = 5,                       ! Terminal input LUN
     .          OT = 6,                       ! Terminal output LUN
     .          PDA = 7,                      ! Direct access file LUN
     .          TMP = 8,                      ! Temporary file LUN
     					      ! don't use 11
     .          INF = 12,                     ! INFILE file LUN
     .          LKP = 13,                     ! LINKPAGE file LUN
     .          VAL = 14,                     ! VALIDATION file LUN
     .          DEL = 88,                     ! Delete object file LUN
CIBMEND
CSGI
CSGI .          SRC = 15,                     ! Source file LUN
CSGI .          OBJ = 16,                     ! Object file LUN
CSGI .          LST = 17,                     ! Listing file LUN
CSGI .          ERR = 18,                     ! Error message file LUN
CSGI .          IT = 5,                       ! Terminal input LUN
CSGI .          OT = 6,                       ! Terminal output LUN
CSGI .          PDA = 19,                     ! Direct access file LUN
CSGI .          TMP = 20,                     ! Temporary file LUN
CSGI .          INF = 21,                     ! INFILE file LUN
CSGI .          LKP = 22,                     ! LINKPAGE file LUN
CSGI .          DEL = 23,                     ! Delete object file LUN
CSGI                                          ! Unit 24 used in COMPRESS
CSGI                                          ! Unit 25 used in DIRPROC
CSGI                                          ! Units 26/27 used in LINKPG
CSGI                                          ! Units 28/29 used in SGICOPYFILE
CSGI                                          ! Unit 30 used in SGIOBJIO
CSGI .          VAL = 31,                     ! VALIDATION file LUN
CSGIEND
CVAX
CVAX .          ERRLOGN = 'PGC$ERRPTH:',
CVAXEND
CIBM
     .          ERRLOGN = '/cae/bin/',
     .          SECTOR_BYTES = 768,           ! # bytes in a sector (SEL)
CIBMEND
CSGI
CSGI .          ERRLOGN = '/cae/bin/',
CSGIEND
     .          ERRFILE = 'errmsg.dac',       ! Error message filename
     .          PDA_RECLN=132                 ! PDA record length
     .          )
C
C --- Message prefix codes (for user message routine)
C
      INTEGER*2 MPFX_BLANK, MPFX_STARS, MPFX_DASHES
C
      PARAMETER (
     .          MPFX_BLANK=0,           ! prefix message with blanks
     .          MPFX_STARS=1,           ! prefix message with stars
     .          MPFX_DASHES=2           ! prefix message with dashes
     .          )
C
C --- Address to detect absent arguments in function/subroutine calls
C
      INTEGER*4 ARG_ABSENT
      PARAMETER (
CVAX
CVAX .          ARG_ABSENT = '00000000'X ! address of absent argument
CVAXEND
CSGI
CSGI .          ARG_ABSENT = $00000000   ! address of absent argument
CSGIEND
CIBM
     .          ARG_ABSENT = X'00000000' ! address of absent argument
CIBMEND
     .          )
C
C --- Integer*2 bounds (for checking if an I*4 variable can be stored
C                       as an I*2)
C
      INTEGER*4
     .          I2STORMAX,
     .          I2STORMIN
      PARAMETER (
     .          I2STORMAX = 32767,
     .          I2STORMIN = -32768
     .          )
C
C --- High values for various data types
C
      REAL*4    HIVL_R4
      INTEGER*4 HIVL_I4
      INTEGER*2 HIVL_I2
      INTEGER*1 HIVL_BYTE
      LOGICAL*1 HIVL_L1
C
      PARAMETER (
     .          HIVL_R4 = X'FFFFFFFF',  ! real*4 highvalues
     .          HIVL_I4 = -1,           ! integer*4 highvalues
     .          HIVL_I2 = -1,           ! integer*2 highvalues
     .          HIVL_BYTE = -1,         ! byte highvalues
     .          HIVL_L1 = .TRUE.        ! logical*1 highvalues
     .          )
C
C --- Display format specifier type codes for internal processing
C     between page compiler modules
C
      INTEGER*1 DF_DEC,  DF_PERC, DF_SDEC,
     .          DF_HEX,  DF_OCT,  DF_TACT,
     .          DF_LAT,  DF_LON,  DF_HLAT,
     .          DF_HLON, DF_SLAT, DF_SLON,
     .          DF_A360, DF_A180, DF_A360T,
     .          DF_A180T,DF_HMS,  DF_MS,
     .          DF_HM,   DF_ALPHA,DF_ATS,
     .          DF_TLAT, DF_TLON, DF_STLAT,
     .          DF_STLON,DF_SHLAT,DF_SHLON,
     .          DF_MLAT, DF_MLON, DF_SMLAT,
     .          DF_SMLON,DF_DPCODE, DF_TFSTR,
     .          DF_VMARM,DF_A18S, DF_A18ST,
     .          DF_SPERC,DF_EXPO,
     .          NUM_DFS
C
      PARAMETER (
     .          DF_DEC    = 1,  ! regular decimal display
     .          DF_PERC   = 2,  ! percent display
     .          DF_SDEC   = 3,  ! signed decimal display
     .          DF_HEX    = 4,  ! hex display
     .          DF_OCT    = 5,  ! octal display
     .          DF_TACT   = 6,  ! tactical grid display
     .          DF_LAT    = 7,  ! latitude display
     .          DF_LON    = 8,  ! longitude display
     .          DF_HLAT   = 9,  ! latitude in hundreds of seconds
     .          DF_HLON   = 10, ! longitude in hundreds of seconds
     .          DF_SLAT   = 11, ! latitude , special sign
     .          DF_SLON   = 12, ! longitude, special sign
     .          DF_A360   = 13, ! angle 0 to 360 display
     .          DF_A180   = 14, ! angle -180 to 180 display
     .          DF_A360T  = 15, ! angle 0 to 360, tenth of degree
     .          DF_A180T  = 16, ! angle -180 to 180 tenth of degree
     .          DF_HMS    = 17, ! time in hours, minutes, seconds
     .          DF_HM     = 18, ! time in hours, minutes
     .          DF_MS     = 19, ! time in minutes, seconds
     .          DF_ALPHA  = 20, ! alphanumeric
     .          DF_TLAT   = 21, ! lat with tenth of a minute
     .          DF_TLON   = 22, ! lon with tenth of a minute
     .          DF_STLAT  = 23, ! lat with tenth and spec sign
     .          DF_STLON  = 24, ! lon with tenth and spec sign
     .          DF_SHLAT  = 25, ! lat with hundredth and spec sign
     .          DF_SHLON  = 26, ! lon with hundredth and spec sign
     .          DF_MLAT   = 27, ! degrees and minutes latitude
     .          DF_MLON   = 28, ! degrees and minutes longitude
     .          DF_SMLAT  = 29, ! special sign latitude (deg,min)
     .          DF_SMLON  = 30, ! special sign longitude (deg,min)
     .          DF_ATS    = 31, ! '@'
     .          DF_DPCODE = 32, ! display code
     .          DF_TFSTR  = 33, ! true/false string
     .          DF_VMARM  = 34, ! variable malfunction armed string
     .          DF_A18S   = 35, ! signed angle -180 to 180 display
     .          DF_A18ST  = 36, ! signed angle -180 to 180 tenth of degree
     .          DF_SPERC  = 37, ! signed decimal with percent
     .          DF_EXPO   = 38, ! signed decimal with percent
     .          NUM_DFS   = 38  ! total number of display formats
     .          )
C
C --- Variable malfunction armed display string and length
C
      INTEGER*2 VM_ARMLEN
      CHARACTER VM_ARMSTR*(5)
      PARAMETER (
     .          VM_ARMLEN=5,      ! varmalf armed string length
     .          VM_ARMSTR='ARMED' ! varmalf armed display string
     .          )
C
C --- Area definition table codes
C
      INTEGER*1 ATBD_UNDF, ATBD_GLOB, ATBD_LOC
      PARAMETER (
     .          ATBD_UNDF = 0,  ! undefined area
     .          ATBD_GLOB = 1,  ! area defined globally
     .          ATBD_LOC  = 2   ! area defined locally
     .          )
C
C --- Storage type parameter values
C
      INTEGER*1
     .          DATA_NOTPRES,
     .          DATA_PRESENT,
     .          DATA_I4,
     .          DATA_R4,
     .          DATA_C4,
     .          DATA_TC4,
     .          DATA_LTR4,
     .          DATA_LNR4,
     .          DATA_TMR4,
     .          DATA_BCI4,
     .          DATA_LABEL
C
      PARAMETER (
     .          DATA_NOTPRES=0,     ! data not stored
     .          DATA_PRESENT=1,     ! data is stored (no type
                                    !    distinction is reqd)
     .          DATA_I4=2,          ! data is stored and is integer*4
     .          DATA_R4=3,          ! data is stored and is real*4
     .          DATA_C4=4,          ! data is stored and is character*4
     .          DATA_TC4=5,         ! data is stored and is character*4
                                    !   and was contained in text delims
     .          DATA_LTR4=6,        ! data stored real & reps a latitude
     .          DATA_LNR4=7,        ! data stored real & reps a longitude
     .          DATA_TMR4=8,        ! data stored real & reps time (secs)
     .          DATA_BCI4=9,        ! data is an I*4 boolean value code
                                    !   (see BC_xxx parameters)
     .          DATA_LABEL=10       ! data is stored in a CDB label
     .          )
C
C --- Various type codes
C
      INTEGER*1 FLD_INSTR, FLD_NONV,     ! field types (page input)
     .          FLD_COMM
      LOGICAL*1 OBJ_NEW, OBJ_OLD         ! Open access for OBJIO
C
      PARAMETER (
     .          FLD_NONV = 1,            ! nonvolatile field type
     .          FLD_INSTR = 2,           ! instruction field type
     .          FLD_COMM = 3,            ! comment field type
     .          OBJ_NEW = .TRUE.,        ! New object
     .          OBJ_OLD = .FALSE.        ! Old object
     .          )
C
C --- File type codes
C
      INTEGER*4 FTYP_PGC,  FTYP_ASC,  FTYP_PGIT, FTYP_ASIT,
     .          FTYP_PDAT, FTYP_PV45, FTYP_IPD,  FTYP_IPIT,
     .          FTYP_IDAT, FTYP_GRIT, FTYP_GRC,  FTYP_GDAT,
     .          FTYP_PVA45,FTYP_VDAT, FTYP_PSGI
C
      PARAMETER (
     .          FTYP_PGC = 1,                 ! PAGEXXXX.SRC file
     .          FTYP_ASC = 2,                 ! ASPGXXXX.SRC file
     .          FTYP_PGIT = 3,                ! PGCINIT.DAT file
     .          FTYP_ASIT = 4,                ! ASCINIT.DAT file
     .          FTYP_PDAT = 5,                ! PAGE.DAT type
     .          FTYP_PV45 = 6,                ! PAGE.V45 type
     .          FTYP_IPD  = 7,                ! IPDXXXX.SRC file
     .          FTYP_IPIT = 8,                ! IPDINIT.DAT file
     .          FTYP_IDAT = 9,                ! IPD.DAT file
     .          FTYP_GRIT = 10,               ! GRCINIT.DAT file
     .          FTYP_GRC = 11,                ! GRC.SRC file
     .          FTYP_GDAT = 12,               ! GRC.DAT file
     .          FTYP_PVA45 = 13,              ! PAGE.A45 file
     .          FTYP_VDAT = 14,               ! VALID.DAT file
     .          FTYP_PSGI = 15                ! PAGE.SGI file
     .          )
C
C
C -- Compiler linker parameters
C
      INTEGER*2
     .          MODE_PGC,                     ! Page
     .          MODE_ASC,                     ! Schematic
     .          MODE_IPD,                     ! IPD
     .          MODE_PPC                      ! Post Page Comp
C
      PARAMETER (
     .          MODE_PGC = 1,                 ! Page
     .          MODE_ASC = 2,                 ! Schematic
     .          MODE_IPD = 3,                 ! IPD
     .          MODE_PPC = 4                  ! Post Page Comp
     .          )
C
C
C -- Buff IO parameters
C
      INTEGER*4
     .          BFIO_SUCCESS
CIBM
      INTEGER*4
     .          BFIO_MINSECT
CIBMEND
C
      PARAMETER (
CVAX
CVAX .          BFIO_SUCCESS = 1           ! BUFFIO success status (VAX)
CVAXEND
CSGI
CSGI .          BFIO_SUCCESS = 1           ! BUFFIO success status (SGI)
CSGIEND
CIBM
     .          BFIO_SUCCESS = 0,          ! BUFFIO success status (SEL)
     .          BFIO_MINSECT = 16          ! Min # sectors for BUFF_OPEN (SEL)
CIBMEND
     .          )
C
C --- End of PAGEPARM.INC
C
C
       COMMON /WINBLK/ WIN_XTL, WIN_YTL, WIN_XBR, WIN_YBR
C
