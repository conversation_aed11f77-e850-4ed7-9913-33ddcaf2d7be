C'Name         Aircraft Position and Reposition
C'Module_ID    USD8RU
C'Customer     All
C'Author       <PERSON><PERSON>'Date         08/04/87
C'Revision_history
C
C  usd8ru.for.13 06May2013 17:13 usd8 mb
C       < New logic to drive TCMGSC (generic scene selected). >
C
C  usd8ru.for.5 26Apr2013 15:44 usd8 mb
C       < Gen scene opposite end misaligned:
C         - replaced RHDG = RUREPHDG by RHDG = VSAGHDG
C         - Added new calculation using WGS84_DIR
C
C  usd8ru.for.5 29Oct2012 10:16 usd8 plemay
C       < added hold and taxi reposition offsets for
C         generic airports>
C
C  usd8ru.for.4 28Feb1992 13:33 usd8 jd
C       < changing med and high airwork altitudes to 8,000 and 16,000
C         respectively and 2nm circle reposition to 600 ft alt. as
C         requested by cutomer. >
C
C  usd8ru.for.3 18Dec1991 08:44 usd8 jd
C       < put in 2 nm circle reposition (#201) >
C
C File: /cae1/ship/usd8ru.for.2
C       Modified by: jd
C       Thu Oct 24 15:32:16 1991
C       < changed ship name to USD8 >
C
C File: /cae1/ship/s742ru.for.24
C       Modified by: av
C       Tue Oct  8 19:21:37 1991
C       < changing alt for repos 71/179 to 2500 ft.correcting spc_gat code >
C
C File: /cae1/ship/s742ru.for.23
C       Modified by: av
C       Mon Oct  7 13:13:49 1991
C       < declaring internal label rate as real*4 iso i*4(error in
C         declaration) >
C
C File: /cae1/ship/s742ru.for.22
C       Modified by: av
C       Wed Oct  2 10:42:34 1991
C       < repos 38/39 hdg changed from +/-45 to +/-90 >
C
C File: /cae1/ship/s742ru.for.18
C       Modified by: av
C       Fri Sep 27 17:56:04 1991
C       < not allowing processing of taposn unless tcmrepos is set >
C
C File: /cae1/ship/s742ru.for.15
C       Modified by: av
C       Fri Sep 27 14:52:29 1991
C       < compiling errors >
C
C File: /cae1/ship/s742ru.for.13
C       Modified by: av
C       Fri Sep 27 14:39:19 1991
C       < writing marker bits in vor 429 word (must be driven at 33 ms) >
C
C File: /cae1/ship/s742ru.for.12
C       Modified by: AV
C       Fri Sep 27 14:23:03 1991
C       < not setting tcmrepos during repos >
C
C File: /cae1/ship/s742ru.for.11
C       Modified by: av
C       Thu Sep 26 18:50:46 1991
C       < temporarily setting tcmrepos until flight informed of change >
C
C File: /cae1/ship/s742ru.for.9
C       Modified by: av
C       Thu Sep 26 17:48:59 1991
C       < declaring tcmtrim >
C
C File: /cae1/ship/s742ru.for.7
C       Modified by: av
C       Thu Sep 26 17:33:44 1991
C       < i/f setting tcmrepos through i/f page.trim required on certain
C         over station repositions.correcting generic gate code >
C
C File: /cae1/ship/s742ru.for.2
C       Modified by: av
C       Thu Aug 15 13:03:21 1991
C       < changing da88 to s742 >
C
C File: /cae1/ship/da88ru.for.13
C       Modified by: av
C       Fri Jul 26 12:21:55 1991
C       < correcting 12 nm intercept atm rep heading >
C
C File: /cae1/ship/da88ru.for.2
C       Modified by: MS/AV
C       Mon Jul 22 22:12:33 1991
C       < changed 12 nm intercept repositions headings from 90 to 45
C         degrees >
C
C File: /cae1/ship/da88ru.for.2
C       Modified by: jd
C       Tue Jun 11 14:57:58 1991
C       < put alt for offset repositions to 14000 ft. >
C
C File: /cae1/ship/da88ru.for.11
C       Modified by: jd
C       Wed May 22 14:49:04 1991
C       < no execute repos button >
C
C File: /cae1/ship/da88ru.for.10
C       Modified by: JD
C       Wed May 22 14:13:06 1991
C       < COMP ERRORS >
C
C
C File: /cae1/ship/da88ru.for.4
C       Modified by: jd
C       Wed May 22 10:53:14 1991
C       <
C
C'
C'Purpose
C        To permit rapid repositioning of the a/c by the
C        instructor and to integrate a/c position.
C
C        The following tasks are performed:
C
C         -  positions the a/c at the station or for a runway
C            type station points offset from touchdown may be
C            used.  Heading and altitude may also be updated.
C            This is achieved through a table of setups
C            for each reposition.
C
C            The inputs are:
C
C            X offset (from runway touchdown)
C            Y offset (from runway touchdown)
C            Altitude (from runway elevation)
C            Heading offset (from runway heading)
C            Round off to closest flight level (in feet)
C
C            The following special cases are handled:
C
C            AS_IS: no change
C            ON_GS: Given an altitude, X is calculated to go on G/S
C                   Given a distance, the altitude is calculated
C                                     to go on G/S
C            D_WNDO: Down wind reposition (opp end)
C            D_WNDT: Down wind reposition (threshold)
C            ON_THR: Repositions on the threshold
C            ON_GND: Reposition to ground
C
C            gate repositions (max 3/rwy) - X,Y offsets and heading in
C                                           station data.
C                                           I/F label TAPOSN goes from
C                                           1-3 and internal label
C                                           GATEPOS = max # gate
C                                           repos/rwy customer requested
C            reposition to takeoff end  - TOENDPOS = T/O position index #
C            reposition to opposite end - OPENDPOS = OPP position index #
C            no position change         - NOLATLON = position index #
C                                                  = -1 if no such repos
C
C            Y = 0 will position a/c along the runway centerline
C            Y > 0 will position a/c right of the runway centerline
C            Y < 0 will position a/c left of the runway centerline
C
C         -  outputs range and bearing from the a/c to any station.
C
C         -  advances or retards a/c on track.
C
C         -  integrates the N/S and E/W velocity components to
C            give the new a/c position.
C
C         -  outputs sin(a/c lat) and cos(a/c lat) for use by other
C            systems.
C
C         -  provides a flag to indicate a reposition, advance on
C            track or position slew has occurred.  The flag remains
C            on for 20 iterations after the completion of the operation.
C
C'Inputs
C         reposition request
C         station index
C         advance/retard on track request
C         advance/retard distance
C         range and bearing request
C         position freeze
C         slew request
C         N/S, E/W velocity components
C
C'Outputs
C         position of a/c
C         range and bearing to station
C         sin and cos of a/c latitude
C         requested altitude and heading of a/c
C'
      Subroutine USD8RU
C
      Implicit None
C
C'Common_Database_Variables
C
CQ    USD8   XRFTEST(*)
C
CVAX+
CVAX CE    BYTE        REPTYN,            !station type #
CVAX-
CSEL+
CSEL CE    INTEGER*1   REPTYN,            !station type #
CSEL-
CIBM+
CE    INTEGER*1   REPTYN,            !station type #
CIBM-
C
CE    EQUIVALENCE (RUREPTYN,REPTYN),
CE    INTEGER*4   REFICAO,
CE    EQUIVALENCE (RXMISICA(1,3),REFICAO),
CE    INTEGER*4   IFICAO,
CE    EQUIVALENCE (TAICAO1,IFICAO),
CE    INTEGER*4   IFRWY,
CE    EQUIVALENCE (TARWY1,IFRWY),
CE    INTEGER*4   REFRWY,
CE    EQUIVALENCE (RXMISRWY(3,3),REFRWY)
C
CPI   USD8 RUTSTFLG,RXMISIDX,RXMISLON,RXMISLAT,RUREPREC,RLGATREC,
CPI  -     RUREPPAL,RUREPTYN,RUREPIDX,RUREPPHD,RUREPLAT,RUREPLON,
CPI  -     RUREPLCX,RUREPRLE,RUREPHDG,RUREPGPA,RUREPELE,RUREPELB,
CPI  -     RUREPRWE,RUREPGPX,RUREPPOH,RUREPPOS,RUREPTYP,RUREPVIS,
CPI  -     RUREPLCY,RUREPLCH,RUREPGPY,
CPI  -     RUREPVI1,RXGATREC,RXMISICA,RXMISRWY,
CPI  -     RUEW,RUEWINV,RUNS,RUNSINV,
CPI  -     RXMISVI1,RXMISVIS, MFREZ, MO$TIMER, MSKIP,
C
CPI  -     RUGSLW,RURC,RBCGGPX,RBCGGPY,
CPI  -     RURD,RUBRG,RFKAMS0C,RAMLP,
C
CPI  -     TAXRB,TAREFRUN,TCPOSN,TAICAO1,TARWY1,
CPI  -     TASPDUP,TATERM,
CPI  -     TCMTIMCP,TCFPOS,TCMPOSL,
CPI  -     TCMRAFRZ,TCMRAALT,
C
CPI  -     tarwylen,
CPI  -     UGEAR,UGLG2,UGRA,XZPOSN,
C
CPI  -     VBOG,VGRALT,VXX,VSAGHDG,VSAGRWYL,
C
CPI  -     TAHDGSET,TALTSET,
C
CP   -     RXACCESS,
CP   -     TCMREPOS,TAPOSN,TARAOT,TCFFLPOS,TAXPOS,
CP   -     VVEW,VVNS,VPILTXCG,VPSIDG,VHS,VPSI,VH,VPSI0,VPSIC,
C
CPO  -     RUPOSN,RURFLG,RUAFLG,RUALTF,RUPLAT,RUPLON,
CPO  -     RUCOSLAT,RUSINLAT,RUBSTN,RURSTN,RUFLT,RUAHDG,RUAALT,
C
CPO  -     RTHELE,RTSETVAR,RTSETELV,RATCINGT,
C
CPO  &     TCMGSC,
CPO  -     TCMACGND,TCMGATE,VHG
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  6-May-2013 21:13:10 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd8.xeq.81
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RUREPLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RUREPLON       !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLON(5)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
C$
      REAL*4   
     &  MO$TIMER       ! MOTION FREEZE TIMER
     &, RBCGGPX(3)     ! A/C C/G TO GP ANTENNA (X-AXIS)         [FT]
     &, RBCGGPY(3)     ! A/C C/G TO GP ANTENNA (Y-AXIS)         [FT]
     &, RUBRG          ! BEARING FROM STN   (DEG)              [DEG]
     &, RUEW           ! POLAR E/W RADIUS                       [FT]
     &, RUEWINV        ! INVERSE OF E/W RADIUS                [1/FT]
     &, RUNS           ! POLAR N/S RADIUS                       [FT]
     &, RUNSINV        ! INVERSE OF N/S RADIUS                [1/FT]
     &, RURD           ! RANGE FROM STATION (NM)                [NM]
     &, RUREPGPA       ! 53 G/P ANGLE (DEGREES)                [DEG]
     &, RUREPHDG       !  7 TRUE HEADING (DEGREES)             [DEG]
     &, RUREPLCH       ! 92 LOC HEADING (DEGREES)              [DEG]
     &, RUREPPHD       ! 56 PREPOSITION HEADING (DEGREES)      [DEG]
     &, RUREPPOH(5)    ! 87/91 REPOSITION HEADINGS             [DEG]
     &, TAHDGSET       ! A/C HEADING                         [Degs ]
     &, TALTSET        ! A/C ALTITUDE                        [Feet ]
     &, TARAOT         ! ADV/RET ON STN/POS INDEX
     &, TASPDUP        ! SPEED UP RATE (0-100%)
     &, TAXPOS         ! REPOSITION STN/POS INDEX
     &, TCMRAALT       ! RADIO ALTITUDE FREEZE VALUE            [ft]
     &, UGEAR          !  Gear offset compensation              [ft]
     &, UGLG2          !  RA antenna to CG dist (z-axis)        [ft]
     &, UGRA(3)        !  Radio altitude                        [ft]
     &, VGRALT         ! USUAL CG HEIGHT ABOVE GROUND            [ft]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VPILTXCG       ! X CG DIST TO PILOT                      [ft]
     &, VPSI           ! CORRECTED A/C HEADING                  [rad]
     &, VPSI0          ! AIRCRAFT HEADING (NO CORRECTION)       [rad]
     &, VPSIC          ! HEADING CORRECTION DUE TO LAT/LONG     [rad]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
      REAL*4   
     &  VSAGHDG        ! GENERIC RWY HEADING                   [DEG]
     &, VSAGRWYL       ! GENERIC RWY LENGTH                     [FT]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
     &, VXX            ! C.G.POSITION ALONG X-B.AX.    [fraction MAC]
C$
      INTEGER*4
     &  RLGATREC       ! GATE RECORD NUMBER
     &, RUREPIDX       ! 31 STATION INDEX NUMBER
     &, RUREPREC       !    RZ RECORD NUMBER
     &, RUREPTYP       ! 41 STATION TYPE (ASCII)
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXGATREC(20)   ! GATE RZ RECORD
     &, RXMISIDX(5)    ! 31 STATION INDEX NUMBER
     &, TAPOSN         ! REPOSITION INDEX
     &, TAREFRUN       ! REFERENCE RUNWAY
     &, TARWYLEN       ! GENERIC VISUAL SCENE RWY LENGTH
     &, TAXRB          ! RANGE AND BEARING STN SLCT
     &, TCPOSN         ! REPOSITION INDEX
C$
      INTEGER*2
     &  RUREPELB       ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RUREPELE       !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RUREPGPX       ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RUREPGPY       ! 46 G/S XMTR Y OFFSET (FT)              [FT]
     &, RUREPLCX       ! 61 LOCALIZER X-OFFSET (FEET)           [FT]
     &, RUREPLCY       ! 62 LOCALIZER Y-OFFSET (FEET)           [FT]
     &, RUREPPAL       ! 57 PREPOSITION ALTITUDE (FEET)         [FT]
     &, RUREPPOS(2,5)  ! 77/86 REPOSITION X/Y OFFSETS (FEET)    [FT]
     &, RUREPRLE       !  8 RUNWAY LENGTH (FEET)                [FT]
     &, RUREPRWE       ! 60 RUNWAY END TO THRESHOLD (FEET)      [FT]
     &, RUREPVI1       ! 73 VISUAL DATA 1
     &, RUREPVIS       ! 72 VISUAL SCENE NUMBER
     &, RXMISVI1(5)    ! 73 VISUAL DATA 1
     &, RXMISVIS(5)    ! 72 VISUAL SCENE NUMBER
     &, TATERM         ! TERMINAL BUILDING
     &, XZPOSN         ! REPOSITION INDEX
C$
      LOGICAL*1
     &  MFREZ          ! MOTION FREEZE REQUEST
     &, MSKIP          ! MOTION PROGRAM BYPASS
     &, RAMLP(3,2)     ! MKR LAMPS ON/OFF: 1=IN, 2=MID, 3=OUT
     &, RUGSLW         ! SLEW A/C ON G/S FLAG
     &, RURC           ! CIRCLE STATION FLAG
     &, RUTSTFLG       ! INITIATE TEST PROGRAM
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFPOS         ! FREEZE/POSITION
     &, TCMPOSL        ! POS SLEW IN PROGRESS
     &, TCMRAFRZ       ! RADIO ALTITUDE FREEZE FLAG
     &, TCMREPOS       ! REPOSITION A/C
     &, TCMTIMCP       ! TIME COMPRESS
     &, VBOG           ! ON GROUND FLAG
C$
      INTEGER*1
     &  RUREPTYN       !  4 TYPE NUMBER
     &, RXMISICA(4,5)  ! 68 AIRPORT ICAO IDENT (ASCII)
     &, RXMISRWY(6,5)  ! 67 RUNWAY/GATE (ASCII)
     &, TAICAO1(4)     ! DEPARTURE ICAO CODE
     &, TARWY1(4)      ! DEPARTURE RWY CODE
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229             
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RTHELE         ! GROUND ELEVATION                       [FT]
     &, RUAALT         ! REPOSITION A/C ALTITUDE                [FT]
     &, RUAHDG         ! REPOSITION A/C HEADING                [DEG]
     &, RUBSTN         ! BRG.  OF A/C TO SELECTED STN         [DEGS]
     &, RUCOSLAT       ! COS A/C LAT
     &, RURSTN         ! RANGE OF A/C TO SELECTED STN           [NM]
     &, RUSINLAT       ! SIN A/C LAT
     &, VHG            ! GROUND ELEVATION                        [ft]
C$
      INTEGER*4
     &  RUPOSN         ! REPOS INDEX (BY RECORD NUMBER)
C$
      LOGICAL*1
     &  TCMGSC         ! GENERIC RUNWAY ACTIVE
     &, RATCINGT       ! ATC INTER. FLAG
     &, RTSETELV       ! FREEZE ELEVATION
     &, RTSETVAR       ! FREEZE VARIATION
     &, RUAFLG         ! REPOS'N,ADV ON TRK,POS SLW
     &, RUALTF         ! ALTITUDE SET FLAG
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, RURFLG         ! GENERAL REPOSITION
     &, TCMACGND       ! A/C ON GROUND
     &, TCMGATE        ! A/C REPOS AT GATE
C$
      LOGICAL*1
     &  DUM0000001(14311),DUM0000002(799),DUM0000003(1280)
     &, DUM0000004(1319),DUM0000005(4),DUM0000006(84)
     &, DUM0000007(156),DUM0000008(28),DUM0000009(4)
     &, DUM0000010(1940),DUM0000011(36),DUM0000012(4268)
     &, DUM0000013(4),DUM0000014(14124),DUM0000015(1)
     &, DUM0000016(599),DUM0000017(20),DUM0000018(5494)
     &, DUM0000019(5),DUM0000020(10),DUM0000021(8)
     &, DUM0000022(8),DUM0000023(12),DUM0000024(4)
     &, DUM0000025(2),DUM0000026(2),DUM0000027(48)
     &, DUM0000028(10),DUM0000029(176),DUM0000030(20)
     &, DUM0000031(1),DUM0000032(46),DUM0000033(17)
     &, DUM0000034(262),DUM0000035(3408),DUM0000036(868)
     &, DUM0000037(25992),DUM0000038(120),DUM0000039(470)
     &, DUM0000040(30),DUM0000041(1430),DUM0000042(43762)
     &, DUM0000043(183153),DUM0000044(9),DUM0000045(796)
     &, DUM0000046(20),DUM0000047(1),DUM0000048(3324)
     &, DUM0000049(2716),DUM0000050(2),DUM0000051(372)
     &, DUM0000052(363),DUM0000053(4),DUM0000054(544)
     &, DUM0000055(652),DUM0000056(44),DUM0000057(4)
     &, DUM0000058(84),DUM0000059(682),DUM0000060(4)
     &, DUM0000061(468),DUM0000062(19534)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,MSKIP,MFREZ,DUM0000002,MO$TIMER,DUM0000003
     &, VBOG,DUM0000004,VPSI0,DUM0000005,VPSIC,VPSI,VPSIDG,DUM0000006
     &, VVNS,VVEW,DUM0000007,VHS,DUM0000008,VHG,VGRALT,DUM0000009
     &, VH,DUM0000010,VXX,DUM0000011,VPILTXCG,DUM0000012,UGRA
     &, UGEAR,DUM0000013,UGLG2,DUM0000014,RUPLAT,RUPLON,RUCOSLAT
     &, RUSINLAT,RUEW,RUNS,RUEWINV,RUNSINV,RURSTN,RUBSTN,RUAALT
     &, RUAHDG,RUPOSN,DUM0000015,RURFLG,RUAFLG,RUALTF,RUFLT,DUM0000016
     &, RTHELE,DUM0000017,RTSETELV,RTSETVAR,DUM0000018,RUREPLAT
     &, RUREPLON,RUREPELE,RUREPTYN,DUM0000019,RUREPHDG,RUREPRLE
     &, DUM0000020,RUREPIDX,DUM0000021,RUREPTYP,DUM0000022,RUREPGPX
     &, RUREPGPY,DUM0000023,RUREPGPA,DUM0000024,RUREPPHD,RUREPPAL
     &, DUM0000025,RUREPRWE,RUREPLCX,RUREPLCY,DUM0000026,RUREPELB
     &, DUM0000027,RUREPVIS,RUREPVI1,DUM0000028,RUREPPOS,RUREPPOH
     &, RUREPLCH,RUREPREC,DUM0000029,RURD,RUBRG,DUM0000030,RUTSTFLG
     &, RURC,DUM0000031,RUGSLW,DUM0000032,RATCINGT,DUM0000033
     &, RAMLP,DUM0000034,RBCGGPY,RBCGGPX,DUM0000035,RXACCESS
     &, DUM0000036,RXGATREC,DUM0000037,RXMISLAT,RXMISLON,DUM0000038
     &, RXMISIDX,DUM0000039,RXMISRWY,RXMISICA,DUM0000040,RXMISVIS
     &, RXMISVI1,DUM0000041,RLGATREC,DUM0000042,XZPOSN,DUM0000043
     &, TCFFLPOS,DUM0000044,TCFPOS,DUM0000045,TATERM,DUM0000046
     &, TCMRAFRZ,DUM0000047,TCMRAALT,DUM0000048,VSAGRWYL,VSAGHDG
     &, DUM0000049,TCMREPOS,DUM0000050,TCMGATE,DUM0000051,TCMACGND
     &, DUM0000052,TALTSET,DUM0000053,TAHDGSET,DUM0000054,TARWYLEN
     &, DUM0000055,TAXRB,TAREFRUN,TAPOSN,TAXPOS,TARAOT,DUM0000056
     &, TAICAO1,DUM0000057,TARWY1,DUM0000058,TCPOSN,DUM0000059
     &, TCMPOSL,DUM0000060,TCMTIMCP,DUM0000061,TASPDUP,DUM0000062
     &, TCMGSC    
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd8.xeq.81
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFKAMS0C       ! KEYER DISPLAY KEYING AMPL.STATUS #12  MI693D
C$
      LOGICAL*1
     &  DUM0100001(16060)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFKAMS0C  
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*4
     &  REFICAO     
     &, IFICAO     
     &, IFRWY     
     &, REFRWY     
C$
      INTEGER*1
     &  REPTYN     
C$
      EQUIVALENCE
     &  (REPTYN,RUREPTYN),(REFICAO,RXMISICA(1,3)),(IFICAO,TAICAO1)      
     &, (IFRWY,TARWY1),(REFRWY,RXMISRWY(3,3))                           
C------------------------------------------------------------------------------
C
C'Local_Variables
C
C     Include 'DISP.COM' !NOFPC
C
      REAL*4 YITIM
      COMMON /DISPCOM/YITIM
C
C
      Logical*1
     &       S_USE_GEN_POS /.TRUE./  !Use new way to repos to generic (for GATE,
C
C
C
      Real*4
     &       AS_IS                !No change in alt or hdg command
     &      ,DEG_FT               !Degrees to feet conversion
     &      ,DEG_RAD              !Degrees to radians conversion
     &      ,D_WNDO               !Down wind command (opp end)
     &      ,D_WNDT               !Down wind command (threshold)
     &      ,FT_DEG               !Feet to degrees conversion
     &      ,FT_NM                !Feet to nautical miles conversion
     &      ,FT_MT                !Feet to meters conversion
     &      ,GATE_DELAY           !Delay the gate reposition to avoid motion bum
     &      ,GATE_DELAY_PARAM
     &      ,MOT_WASH             !Washout motion to prevent motion bump
     &      ,NM_DEG               !Nautical miles to degrees conversion
     &      ,OF_REP               !Offset reposition command
     &      ,ON_GND               !On ground command
     &      ,ON_GS                !On glideslope command
     &      ,ON_THR               !On threshold command
     &      ,PI                   !3.1415...
     &      ,P_ALT                !Pressure altitude command
     &      ,RAD_DEG              !Radians to degrees conversion
     &      ,SINE_OF_5            !Sine of 5 degs (estimated pitch)
     &      ,TWO_PI               !Two times PI
     &      ,REQ                  !EQUATORIAL RADIUS
     &      ,ELPCT                !ELLIPTICITY
     &      ,SINSQR               !SIN OF LAT SQUARED
C
      Integer*4
     &       DEF_GATE             !Default gate reposition number
     &      ,GATE                 ! 'GATE'
     &      ,GEN_GATE             !Generic gate reposition number
     &      ,GEN_HOLD             !Generic hold reposition number
     &      ,GEN_RHLD             !Generic reciprocal hold reposition number
     &      ,GEN_TAXI             !Generic taxi reposition number
     &      ,IPOSNUM              !Number of repositions in table
     &      ,LEFT                 !Generic gate on left number
     &      ,RIGHT                !Generic gate on right number
     &      ,OFFSET_1             !First relative reposition number
     &      ,OFFSET_5             !Last relative reposition number
     &      ,SPC_GAT1             !Specific gate 1 repos number
     &      ,SPC_GAT5             !Specific gate 5 repos number
C
      Integer*2
     &       ONE_THOU             !Declared as I*2 to be used in MOD function
     &      ,VZ222L               !Vor SSM/SDI Internal
     &      ,DUMX1F               !'1F' in hex
     &      ,OL_XZPOSN            !Old value of xzposn
C
      Parameter
     &(
     &           AS_IS  = -99999.
     &          ,DEF_GATE = 33
     &          ,DEG_FT = 60.0 * 1852.0 / (12. * 0.0254)
     &          ,DEG_RAD = 3.1415926536 / 180.
     &          ,D_WNDO = -99992.
     &          ,D_WNDT = -99993.
     &          ,FT_DEG = (12. * 0.0254) / (60. * 1852.)
     &          ,FT_NM = (12. * 0.0254) / 1852.
     &          ,FT_MT = .3048
     &          ,GATE = X'47415445'
     &          ,GATE_DELAY_PARAM=100.0
     &          ,GEN_GATE = 99999
     &          ,GEN_HOLD = 127
     &          ,GEN_RHLD = 125
     &          ,GEN_TAXI = 128
     &          ,IPOSNUM = 201
     &          ,LEFT = 1
     &          ,RIGHT = 2
     &          ,NM_DEG = 1./60.
     &          ,OFFSET_1 = 125
     &          ,OFFSET_5 = 129
     &          ,OF_REP = -99995.
     &          ,ON_GND = -99998.
     &          ,ON_GS  = -99997.
     &          ,ON_THR = -99994.
     &          ,ONE_THOU = 1000
     &          ,PI = 3.1415926536
     &          ,P_ALT  = -99996.
     &          ,RAD_DEG = 180. / 3.1415926536
     &          ,SINE_OF_5 = 0.0871557
     &          ,SPC_GAT1 = 42
     &          ,SPC_GAT5 = 46
     &          ,TWO_PI = 2. * PI
     &          ,REQ = 20925639.8
     &          ,ELPCT = 1. / 298.2638
     &)
C
      Real*8
     &       MISLAT                !RXMISLAT(1) in normal memory
     &      ,MISLON                !RXMISLON(1) in normal memory
     &      ,PRELAT                !Previous latitude
     &      ,PRELON                !Previous longitude
     &      ,REPLAT                !Touchdown latitude
     &      ,REPLON                !Touchdown longitude
     &      ,TEMPLAT               !Temporary latitude
     &      ,TEMPLON               !Temporary longitude
     &      ,TXGPLAT               !Glideslope latitude
     &      ,TXGPLON               !Glidelope longitude
     &      ,DLAT                  !Change in latitude
     &      ,DLON                  !Change in longitude
     &      ,GENLAT                !Generic runway reference Lat
     &      ,GENLON                !Generic runway reference Lon
     &      ,RDIST                 !Position distance
     &      ,RHDG                  !Position bearing
C
      Real*4
     &       ALTOFF       (IPOSNUM)!Altitude
     &      ,ARRAY      (IPOSNUM,5)!Position setup data
     &      ,BSTN                  !Bearing to station
     &      ,HDGGENL               !Heading of generic gate
     &      ,HDGGENR               !Heading of generic gate
     &      ,HDGOFF       (IPOSNUM)!Heading offset
     &      ,PRESALT            (5)!Pressure altitude table
     &      ,RBCOS                 !Cos latitude
     &      ,RNDOFF       (IPOSNUM)!Round off value
     &      ,RSTN                  !Range to station
     &      ,RTEMP                 !Temporary storage
     &      ,RUTRK                 !Advance/retard track
     &      ,SINLAT                !SINE OF LATITUDE
     &      ,X                     !X offset
     &      ,X1                    !X offset
     &      ,XGATEL                !X coordinate of generic scene
     &      ,XGATER                !X coordinate of generic scene
     &      ,XHOLDL                !X coordinate of generic scene (HOLD)
     &      ,XHOLDR                !X coordinate of generic scene (HOLD)
     &      ,XOFF         (IPOSNUM)!X offset
     &      ,XTAXIL                !X coordinate of generic scene (taxi in)
     &      ,XTAXIR                !X coordinate of generic scene (taxi in)
     &      ,Y                     !Y offset
     &      ,Y1                    !Y offset
     &      ,YGATEL                !Y coordinate of generic scene
     &      ,YGATER                !Y coordinate of generic scene
     &      ,YHOLDL                !Y coordinate of generic scene (HOLD)
     &      ,YHOLDR                !Y coordinate of generic scene (HOLD)
     &      ,YOFF         (IPOSNUM)!Y offset
     &      ,RATE                  !Speed rate
     &      ,YTAXIL                !Y coordinate of generic scene (taxi in)
     &      ,YTAXIR                !Y coordinate of generic scene (taxi in)
     &      ,XDIF                  !Difference in X direction
     &      ,YDIF                  !Difference in Y direction
     &      ,CGPX                  !G/P ANTENNA X-OFFSET
     &      ,GPX                   !THRESHOLD TO T/D DISTANCE
     &      ,GPY                   !G/P TRANSMITTER Y-OFFSET
     &      ,DIST                  !DISTANCE FROM G/P TRANSMITTER
     &      ,COSP                  !COSINE OF PITCH
     &      ,SINP                  !SINE OF PITCH
     &      ,FIXED                 !DIST FROM T/D TO LOC HDG
     &      ,ANGSLOC               !ANGLE FROM LOC XMTR TO G/S XMTR
     &      ,TDCOS                 !COSINE OF G/P TRANSMITTER LAT
     &      ,TDRNG                 !RANGE TO G/P TRANSMITTER LAT
     &      ,TDBRG                 !BEARING TO G/P TRANSMITTER LAT
     &      ,HDG                   !RWY HDG
     &      ,COSGP                 !COSINE OF RWY HDG
     &      ,SINGP                 !SINE OF RWY HDG
     &      ,COSLAT                !COSINE OF RWY END LAT
     &      ,HDIFF                 !RWY HDG - LOC HDG
     &      ,TEMPDST               !TEMP STORAGE FOR DISTANCE
     &      ,DLE                   !LOC X-OFFSET
     &      ,DLY                   !LOC Y-OFFSET
     &      ,GGPX                  !G/P X-OFFSET
     &      ,GGPY                  !G/P Y-OFFSET
     &      ,MINDST                !
C
      Integer*4
     &       GATEPOS/3/            !Max gate position #
     &      ,I                     !DO loop counter
     &      ,IPOS                  !Position #
     &      ,J                     !DO loop counter
     &      ,KLOK1                 !Flag reset counter
     &      ,KLOK2                 !Flag reset counter
     &      ,KLOK3                 !Range/bearing update counter
     &      ,NOLATLON/-1/          !No position reposition #
     &      ,OPENDPOS (2) /12,131/ !Opposite end position #
     &      ,RULPOSN               !Local value for TAPOSN
     &      ,POSITION              !Local value for position
     &      ,RUSTNX                !Station index
     &      ,TOENDPOS(3)/10,11,130/!Takeoff position #
     &      ,TERMINAL              !Generic gate position on visual
C
      Logical*1
     &       GEN_SC                !Generic scene flag
     &      ,GNDPOS                !Ground reposition flag
     &      ,OLFFLPOS /.FALSE./    !Previous freeze/flight,position
     &      ,OLTSTFLG /.FALSE./    !Previous test facility on flag
     &      ,RBFRST                !First pass for RNGBRG
     &      ,REDO                  !Second repos flag for X=ON_G/S reps
     &      ,READY    /.FALSE./    !Reposition station data ready flag
     &      ,PREPOS                !Previous value of TCMREPOS
     &      ,RUINIT /.TRUE./       !First time pass flag
     &      ,RUPOLFLG              !A/C flew over pole flag
     &      ,STNREPOS              !Over station reposition
     &      ,TEMPPOS               !Reposition performed
     &      ,ILSREP                !RWY OR LOC REPOS
     &      ,TDFRST                !RNGBRG FIRST TIME FLAG
C
CDEBUG+
        Integer*4     l_rulposn
        Integer*4     l_position
        Integer*2     l_taterm
        Integer*4     l_tarwylen
        REAL*4        l_vsagrwyl
        REAL*4        l_vsaghdg
	REAL*4	      l_rurephdg
CDEBUG-
C
C
      Equivalence
     &       (ARRAY(1,1),XOFF)
     &      ,(ARRAY(1,2),YOFF)
     &      ,(ARRAY(1,3),ALTOFF)
     &      ,(ARRAY(1,4),HDGOFF)
     &      ,(ARRAY(1,5),RNDOFF)
C
      Data
     &       PRESALT/5000,10000,20000,30000,40000/
C
      DATA
     &       COSP/0.99971255/
C
      DATA
     &       SINP/0.0757690/
C
C
C -- X and Y coordinates of generic scene for gate reposition
C
      Data
     &      XGATEL/0./,YGATEL/0./,HDGGENL/0./,
     &      XGATER/0./,YGATER/0./,HDGGENR/0./
 
      Data
     &      DUMX1F/'1F'X/
C
      Data
C
     &     ((ARRAY (I,J),J = 1,5),I=1,50)/
C
C        X      Y     Altitude    Heading    Round off Repos  Description
C       (nm)   (nm)    (feet)   (0-360 deg)   (feet)   Number
C      -------------------------------------------------------------------
C
     &      0,     0,  ON_GND,       0,          0     ! 1   G
     &,     0,     0,  ON_GND,       0,          0     ! 2
     &,     0,     0,  ON_GND,       0,          0     ! 3      A
     &,     0,     0,  ON_GND,       0,          0     ! 4
     &,     0,     0,  ON_GND,       0,          0     ! 5          T
     &,     0,     0,  ON_GND,       0,          0     ! 6
     &,     0,     0,  ON_GND,       0,          0     ! 7              E
     &, ON_GS,     0,     100,       0,          0     ! 8   ON ILS 100FT AGL
     &, ON_GS,     0,     200,       0,          0     ! 9   ON ILS 200FT AGL
     &,     0,     0,  ON_GND,       0,          0     ! 10  T/O
     &,     0,     0,  ON_GND,       0,          0     ! 11  T/O
     &,     0,     0,  ON_GND,     180,          0     ! 12  OPP T/O
     &,     0,     0,  ON_GND,       0,          0     ! 13  TOUCHDOWN
     &, ON_GS,     0,      20,       0,          0     ! 14  ON ILS  20FT AGL
     &, ON_GS,     0,     100,       0,          0     ! 15  ON ILS 100FT AGL
     &, ON_GS,     0,     200,       0,          0     ! 16  ON ILS 200FT AGL
     &,     6,     0,    1500,       0,          0     ! 17  6  NM OUT 1500FT
     &,    12,    -2,    3000,     -45,          0     ! 18  12 NM OUT 3K FT
     &,     0,     0,  ON_GND,       0,          0     ! 19  R E S E R V E D
     &,    10,     0,    2000,       0,          0     ! 20  IN FLIGHT
     &,    10,     2,    5000,      45,          0     ! 21  R INT 10 NM 2K FT
     &,    20,     0,   10000,       0,          0     ! 22  AIR WORK FL 100
     &,    20,     0,   20000,       0,          0     ! 23  AIR WORK FL 200
     &,    30,     0,   37000,       0,          0     ! 24  AIR WORK FL 370
     &,    17,     4,    2000,      30,          0     ! 25  4NMR 17NMO 2000FT
     &,   -20,     0,   10000,       0,          0     ! 26  AIRWK 20NM 10K FT
     &, ON_GS,     0,     100,       0,          0     ! 27  ON ILS 100FT
     &,     6,     0,   ON_GS,       0,          0     ! 28  ON ILS 6NMO GP
     &,     7,     1,    2000,      30,          0     ! 29  1 NMR 7NMO 2K FT
     &,    10,    -3,    2000,     -30,          0     ! 30  3 NML 10NMO 2K FT
     &,    10,     0,   ON_GS,       0,          0     ! 31  ON ILS 10 NM OUT
     &,    12,     0,   ON_GS,       0,          0     ! 32  ON ILS 12 NM OUT
     &,     0,     0,  ON_GND,       0,          0     ! 33  DEFAULT GATE
     &,     3,     0,   ON_GS,       0,          0     ! 34  3NMO ON ILS
     &,     6,     0,   ON_GS,       0,          0     ! 35  6NMO ON ILS
     &,     8,     0,    1500,       0,          0     ! 36  8NMO ON ILS
     &,    12,     0,    2000,       0,          0     ! 37  12NMO ON ILS
     &,    12,    -3,    2000,     -90,          0     ! 38  L. H. BASE
     &,    12,     3,    2000,      90,          0     ! 39  R. H. BASE
     &,D_WNDO,    -3,    2000,     180,          0     ! 40  L DWIND,3NM OPP
     &,D_WNDO,     3,    2000,     180,          0     ! 41  R DWIND,3NM OPP
     &,     0,     0,  ON_GND,       0,          0     ! 42  GATE 1
     &,     0,     0,  ON_GND,       0,          0     ! 43  GATE 2
     &,     0,     0,  ON_GND,       0,          0     ! 44  GATE 3
     &,     0,     0,  ON_GND,       0,          0     ! 45  GATE 4
     &,     0,     0,  ON_GND,       0,          0     ! 46  GATE 5
     &,    40,     0,    8000,       0,          0     ! 47  MED AIR
     &,   100,     0,   16000,       0,          0     ! 48  HIGH AIR
     &,     3,     0,   ON_GS,       0,          0     ! 49  3NM FINAL
     &,     9,     0,    2000,       0,          0/    ! 50  9NM FINAL
      DATA
     &     ((ARRAY (I,J),J = 1,5),I=51,100)/
     &     12,     0,    2500,       0,          0     ! 51  12NM FINAL
     &,    15,     3,   AS_IS,      45,          0     ! 52  15NM R45
     &,    15,    -3,   AS_IS,     -45,          0     ! 53  15NM L45
     &,    20,     0,    5000,       0,          0     ! 54  20NM FINAL
     &,D_WNDO,    -3,    1500,     180,          0     ! 55  L DWIND,3NM OPP
     &,D_WNDO,     3,    1500,     180,          0     ! 56  R DWIND,3NM OPP
     &,0.1972,     0,   ON_GS,       0,          0     ! 57  SR 1200'
     &,0.8686,     0,   ON_GS,       0,          0     ! 58  SR 1SM
     &,1.7371,     0,   ON_GS,       0,          0     ! 59  SR 2SM
     &,4.3430,     0,   ON_GS,       0,          0     ! 60  SR 5SM
     &,8.6859,     0,   ON_GS,       0,          0     ! 61  SR 10SM
     &,     0,     0,    1000,   AS_IS,          0     ! 62  AGL 1000'
     &,     0,     0,   P_ALT,   AS_IS,          0     ! 63  PA 5000'
     &,     0,     0,   P_ALT,   AS_IS,          0     ! 64  PA 10000'
     &,     0,     0,   P_ALT,   AS_IS,          0     ! 65  PA 20000'
     &,     0,     0,   P_ALT,   AS_IS,          0     ! 66  PA 30000'
     &,     0,     0,   P_ALT,   AS_IS,          0     ! 67  PA 40000'
     &,    40,     0,   15000,     180,          0     ! 68  MED AIR
     &,   100,     0,   35000,     180,          0     ! 69  HIGH AIR
     &,     8,     0,    2000,       0,          0     ! 70  8 NMO ON ILS
     &,    12,     1,    2500,      30,          0     ! 71 12 1NM FROM CTL
     &,     6,    -3,    3000,      45,          0     ! 72  6 3 NM FROM CTL
     &,     6,     3,    3000,      45,          0     ! 73  6 3 NM FROM CTL
     &,D_WNDO,    -2,    1500,     180,          0     ! 74  L DWIND,2NM OPP
     &,D_WNDO,     2,    1500,     180,          0     ! 75  R DWIND,2NM OPP
     &,     5,     0,   ON_GS,       0,          0     ! 76  5 NM ON ILS
     &,    10,    -4,    2000,       0,          0     ! 77 10 NMO 4 L 2K FT
     &,    10,     4,    2000,       0,          0     ! 78 10 NMO 4 R 2K FT
     &,    10,     0,    2000,       0,          0     ! 79 10NMO 2K FT
     &,    20,     0,   25000,       0,          0     ! 80 FL 250
     &,    20,     0,   33000,       0,          0     ! 81 FL 330
     &,    20,     0,   41000,       0,          0     ! 82 FL 410
     &,    20,     0,   10000,       0,          0     ! 83 FL 100
     &,    15,   -15,   ON_GS,       0,          0     ! 84 DOG LEFT
     &,    15,    15,   ON_GS,       0,          0     ! 85 DOG RIGHT
     &,    20,     0,   31000,       0,          0     ! 86 FL 310
     &, ON_GS,     0,     750,       0,          0     ! 87 ON G/S, 750 FT
     &,     8,     0,    2900,       0,          0     ! 88 8 NMO 2900 FT
     &,     8,     0,   11500,       0,          0     ! 89 8 NMO 11500 FT
     &,0.9710,     0,   13500,       0,          0     ! 90 5900 FT, 13500 FT
     &,    20,     0,    8000,       0,          0     ! 91 FL 80
     &,    20,     0,   15000,       0,          0     ! 92 FL 150
     &,    20,     0,   20000,       0,          0     ! 93 FL 200
     &,    20,     0,   35000,       0,          0     ! 94 FL 350
     &,    70,     0,   35000,       0,          0     ! 95 70 NMO, FL 350
     &,0.0987,     0,     790,       0,          0     ! 96 FINAL, 790 FT
     &,0.1991,     0,    1400,       0,          0     ! 97 FINAL, 1400 FT
     &,0.4625,     0,    3000,       0,          0     ! 98 FINAL, 3000 FT
     &,     4,     0,    1000,       0,          0     ! 99 4 NMO, 1000 FT
     &,0.1827,     0,    1300,       0,          0/    !100 1110 FTO, 1300 FT
      DATA
     &     ((ARRAY (I,J),J = 1,5),I=101,150)/
     & 0.2469,     0,    1500,       0,          0     !101 1500 FTO, 1500 FT
     &,0.4937,     0,    3000,       0,          0     !102 3000 FTO, 3000 FT
     &,     4,     0,    5800,       0,          0     !103 4 NMO, 5800 FT
     &,    10,     0,    9500,       0,          0     !104 10 NMO, 9500 FT
     &,    16,     0,   11500,       0,          0     !105 16 NMO, 11500 FT
     &,    20,     0,   15000,       0,          0     !106 20 NMO, FL 150
     &,0.3456,     0,    2200,       0,          0     !107 2100 FTO, 2200 FT
     &,    12,     0,    3000,       0,          0     !108 12 NMO, 3K FT
     &,    15,     2,    5000,       0,          0     !109 15 NMO, 2 NMR, 5K
     &,    10,     0,    3000,       0,          0     !110 10 NMO, 3000 FT
     &, ON_GS,     0,    1000,       0,          0     !111 ON G/S, 1000FT
     &,     6,     0,    1500,       0,          0     !112 6 NMO, 1500 FT
     &,    10,     0,    2500,       0,          0     !113 10 NMO, 2500 FT
     &,    14,     0,    3500,       0,          0     !114 14 NMO, 3500 FT
     &,    12,     0,    3000,       0,          0     !115 12 NMO, 3000 FT
     &,    25,     0,   10000,       0,          0     !116 25 NMO, FL 100
     &,    25,     0,   25000,       0,          0     !117 25 NMO, FL 250
     &,    12,    -6,    2500,      45,          0     !118 12 NMO, 6NML
     &,    12,     6,    2500,     -45,          0     !119 12 NMO, 6NMR
     &,     8,     0,    2000,       0,          0     !120 8 NMO, 2000 FT
     &,     4,     0,   ON_GS,       0,          0     !121 4 NMO, ON G/S
     &,     0,    -3,   AS_IS,       0,          0     !122 3 NML
     &,     0,     3,   AS_IS,       0,          0     !123 3 NMR
     &,     5,     0,    5000,       0,          0     !124 5 NMO, 5K FT
     &,     0,     0,  ON_GND,       0,          0     !125 Relative 1
     &,     0,     0,  ON_GND,       0,          0     !126 Relative 2
     &,     0,     0,  ON_GND,       0,          0     !127 Relative 3
     &,     0,     0,  ON_GND,       0,          0     !128 Relative 4
     &,     0,     0,  ON_GND,       0,          0     !129 Relative 5
     &,     0,     0,  ON_GND,       0,          0     !130  T/O
     &,     0,     0,  ON_GND,     180,          0     !131  OPP T/O
     &, ON_GS,     0,     100,       0,          0     !132  ON ILS 100FT AGL
     &, ON_GS,     0,     200,       0,          0     !133  ON ILS 200FT AGL
     &, ON_GS,     0,      20,       0,          0     !134  ON ILS  20FT AGL
     &,     6,     0,    1500,       0,          0     !135  6  NM OUT 1500FT
     &,    12,    -2,    3000,      45,          0     !136  12 NM OUT 3K FT
     &,    10,     0,    2000,       0,          0     !137  IN FLIGHT
     &,    10,     2,    5000,      45,          0     !138  R INT 10 NM 2K FT
     &,    30,     0,   10000,       0,          0     !139  AIR WORK FL 100
     &,    20,     0,   20000,       0,          0     !140  AIR WORK FL 200
     &,    30,     0,   37000,       0,          0     !141  AIR WORK FL 370
     &,    17,     4,    2000,      30,          0     !142  4NMR 17NMO 2000FT
     &,   -20,     0,   10000,       0,          0     !143  AIRWK 20NM 10K FT
     &, ON_GS,     0,     100,       0,          0     !144  ON ILS 100FT
     &,     6,     0,   ON_GS,       0,          0     !145  ON ILS 6NMO GP
     &,     7,     1,    2000,      30,          0     !146  1 NMR 7NMO 2K FT
     &,     7,     0,    2000,       0,          0     !147  7 NM OUT, 2K FT
     &,    20,     0,    3000,       0,          0     !148  20 NM OUT, 3K FT
     &,     2,     0,     640,       0,          0     !149  2 NM OUT, 640 FT
     &,     2,     0,   ON_GS,       0,          0/    !150  ON ILS 2NMO GP
      DATA
     &     ((ARRAY (I,J),J = 1,5),I=151,200)/
     &      5,     0,   ON_GS,       0,          0     !151  ON ILS 5NMO GP
     &,    10,     0,    2000,       0,          0     !152  ON ILS 10NMO GP
     &,    17,    -4,    2000,     -30,          0     !153  4NML 17NMO 2000FT
     &, ON_GS,     0,      15,       0,          0     !154  ON ILS, 15 FT AGL
     &,     0,     0,   29000,     126,          0     !155  LOFT 1 REPOS
     &,     0,     0,   29500,     245,          0     !156  LOFT 2 REPOS
     &,    12,    -2,    2000,     -30,          0     !157  2 NML,12 NMO,2KFT
     &,    30,    10,   10000,      30,          0     !158  30 NMO,10NMR,10KFT
     &,    30,   -10,   35000,     -30,          0     !159  30 NMO,10NML,35KFT
     &,     0,     0,       0,       0,          0     !160  TOUCHDOWN
     &, ON_GS,     0,    1200,       0,          0     !161  ON G/S, 1200FT
     &,     7, 12.12,    2000,      45,          0     !162  RT BASE, 14NM
     &,    13,     0,    3000,       0,          0     !163  13NMO, 3KFT
     &,     7,     0,    1500,       0,          0     !164  7 NMO, 1500FT
     &, ON_GS,     0,     600,       0,          0     !165  ON GS, 600 FT
     &, ON_GS,     0,     300,       0,          0     !166  ON GS, 300 FT
     &, ON_GS,     0,     200,       0,          0     !167  ON GS, 200 FT
     &, ON_GS,     0,     100,       0,          0     !168  ON GS, 100 FT
     &, ON_GS,     0,      50,       0,          0     !169  ON GS,  50 FT
     &,     0,     0,      20,       0,          0     !170  FLARE
     &,    13,     3,    3000,      45,          0     !171  13NMR, 45 DEG
     &,    13,    -3,    3000,     -45,          0     !172  13NMR, 45 DEG
     &, ON_GS,   0.4,    1000,      45,          0     !173  INTERCP R ON GS
     &,OF_REP,     0,   14000,   AS_IS,          0     !174  OFFSET REPOS
     &,    15,     0,    9000,       0,          0     !175  FL 090
     &,    15,     0,   33000,       0,          0     !176  FL 330
     &,ON_THR,     0,  ON_GND,       0,          0     !177  OVER THR, ON GND
     &,     8,     0,   ON_GS,       0,          0     !178  8NMO ON ILS
     &,    12,    -1,    2500,     -30,          0     !179  12NM, 1L FROM CTL
     &,    12,     0,    1500,       0,          0     !180  12NM, 1500FT
     &,    12,     3,    1500,      30,          0     !181  12NM, 1R, 1500FT
     &,    12,    -3,    1500,     -30,          0     !182  12NM, 1L, 1500FT
     &,    15,     0,    3000,       0,          0     !183  10 NM 3000FT
     &,    15,    -2,    3000,     -30,          0     !184  L 3000
     &,    15,     2,    3000,      30,          0     !185  R 3000
     &,D_WNDT,  -1.3,     700,     180,          0     !186  L DWIND,1.5SM THR
     &,D_WNDT,   1.3,     700,     180,          0     !187  R DWIND,1.5SM THR
     &,D_WNDO,  -1.3,     700,       0,          0     !188  L DWIND,1.5SM OPP
     &,D_WNDO,   1.3,     700,       0,          0     !189  R DWIND,1.5SM OPP
     &,D_WNDT,    -2,    1500,     180,          0     !190  L DWIND,2NM THR
     &,D_WNDT,     2,    1500,     180,          0     !191  R DWIND,2NM THR
     &, AS_IS, AS_IS,   AS_IS,   AS_IS,          0     !192  AS IS REP
     &, ON_GS,     0,     500,       0,          0     !193  ON ILS, 500FT
     &, ON_GS,     0,    1500,       0,          0     !194  ON ILS, 1500FT
     &,    14,     0,    3000,       0,          0     !195  14NM OUT, 3000FT
     &,    11,    -1,    2000,     -30,          0     !196  11NM, 1L, 2000FT
     &,    15,     3,    3000,      60,          0     !197  15NM, 3R, 3000FT
     &,    30,     0,   35000,       0,          0     !198  AIR WORK FL 350
     &,D_WNDT,    -5,    5000,     180,          0     !199  L DWIND, 5NM THR
     &,D_WNDT,     5,    5000,     180,          0/    !200  R DWIND, 5NM THR
      DATA
     &     ((ARRAY (I,J),J = 1,5),I=201,IPOSNUM)/
     &     -4,     0,     600,     180,          0/    !201  2 NM CIRCLE
C
C
C
      Entry RUPOS
      IF (RUINIT) THEN
C
C -- X and Y coordinates of MAXVUE generic scene for gate repositions
C
           XGATEL  = 4960.0
           YGATEL  = 880.0   !This number can be changed to 1630.0 or others
           HDGGENL = 90.0
C
           XGATER  = 4950.0
           YGATER  = -880.0  !This number can be changed to -1590.0 or -...
           HDGGENR = -90.0
C
C -- X and Y coordinates of MAXVUE generic scene for hold reposition
C
C 2UJ4-PLEM
           XHOLDL  =  50.
           YHOLDL  =  420.0
C
           XHOLDR  =  50.
           YHOLDR  = -420.0
C
 
C -- X and Y coordinates of MAXVUE generic scene for taxi reposition
C NOTE:  The 'TAXI' icon on the IOS puts the aircraft 90 deg to the rwy
C        at the hold bar.
C
C 2UJ4-PLEM
           XTAXIL  =  450.
           YTAXIL  =  710.
C
           XTAXIR  =  450.
           YTAXIR  = -710.
COT-
 
        RUINIT = .FALSE.
      ENDIF
C
C GEN+
C This new code is set correct offset for gate, hold rec. hold position
C for new generic database. Rwy length can be extended and all positions
C should change accordingly.
C
C Gate position should depend on rwy length also
C
        If (TATERM.EQ.3 .OR. TATERM.EQ.4) Then
          XGATEL  = (VSAGRWYL * (3./4.))
C
        Elseif (TATERM.EQ.1 .OR. TATERM.EQ.2) Then
          XGATEL  = (VSAGRWYL * (1./2.))
C
        Elseif (TATERM.EQ.5 .OR. TATERM.EQ.6) Then
          XGATEL  = (VSAGRWYL * (1./4.))
C
        Endif
        XGATER  = XGATEL
C
CGEN-
C
C SET MARKER BITS IN VOR 429 WORD(MUST BE DRIVEN AT 33MS)
C
C      VZ222L = RVJ222AA
C      VZ222L = IAND(VZ222L,DUMX1F)
C      IF (RFKAMS0C.NE.0) THEN
C        IF (RAMLP(1)) VZ222L = VZ222L + 128
C        IF (RAMLP(2)) VZ222L = VZ222L + 64
C        IF (RAMLP(3)) VZ222L = VZ222L + 32
C      ENDIF
C      RVJ222AA = VZ222L
C
C -- Check for instructor input to lat/lon
C
      If (PRELAT.NE.RUPLAT .OR. PRELON.NE.RUPLON) Then
        RUAFLG = .TRUE.
        KLOK2 = 20
      Endif
C
C -- Check for Radio Altitude Freeze flag, feature added for 2ULD
C
      If (TCMRAFRZ) Then
        If (TCMRAALT.GE.(UGRA(1)-1) .AND. TCMRAALT.LE.(UGRA(1)+1)) Then
          TCFFLPOS = .TRUE.
          TCMRAFRZ = .FALSE.
        Endif
      Endif
 
C
C -- Setting generic scene flag
C
      If (RXMISVIS(3).EQ.-1 .OR. RXMISVIS(3).EQ.256 .OR.
     &    RXMISVIS(3).EQ.257) Then
        GEN_SC = .TRUE.
        TCMGSC = .TRUE.
      Else
        GEN_SC = .FALSE.
        TCMGSC = .FALSE.
      Endif
C
C -- Fix for reposition issue between two generic aiports - 2ULD-PLEM
C
      If(XZPOSN.NE.OL_XZPOSN .AND. XZPOSN.EQ.0) Then
        RTSETELV = .FALSE.
      Endif
      OL_XZPOSN = XZPOSN
 
      If (XZPOSN.NE.0 .AND. GEN_SC) Then
        RTSETELV = .TRUE.
      Endif
C
C -- RU030  Call Radio Aids test program
C  -----------------------------------
      If (RUTSTFLG .XOR. OLTSTFLG) Then
        OLTSTFLG = RUTSTFLG
        If (RUTSTFLG) Then
          OLFFLPOS = TCFFLPOS
        Else
C
C -- Reset NATF flags when test facility is deactivated
C
          TCFFLPOS = OLFFLPOS
          RURC     = .FALSE.
          RUGSLW   = .FALSE.
          RTSETELV = .FALSE.
          RTSETVAR = .FALSE.
          RATCINGT = .FALSE.
          TAPOSN = 130      !Repositioning to T/O
        Endif
      Endif
C
      If (RUTSTFLG .AND..NOT. TCMREPOS) Call RATEST
C
C -- Save previous test flag
C
      OLTSTFLG = RUTSTFLG
C
C    bdarvell - prevent motion bump for gate repos
        If (MSKIP) MOT_WASH = AMAX1(MOT_WASH - 1.0 , 0.0 )
        If (MSKIP .AND. MOT_WASH .LE. 1.0) MSKIP = .FALSE.
C
C -- Check for request to reposition to a reference runway
C    If R/A has not found the requested reference runway, then wait
C
      If (TAPOSN .GT. 0 .AND. REFICAO .EQ. IFICAO
     &              .AND. REFRWY  .EQ. IFRWY) Then
        RULPOSN = TAPOSN
C
C -- Check if request to reposition to gates
C
        If (TAPOSN .LE. 7 .OR. TAPOSN .EQ. DEF_GATE) Then
C
C -- Generic scene gate
C
          If (GEN_SC) Then
            RUSTNX = TAREFRUN
            RULPOSN = GEN_GATE
          Else
C
C -- Default gate
C
            If (TAPOSN .EQ. DEF_GATE) Then
              If (RLGATREC .EQ. 0) Then
                If (RXGATREC (1) .EQ. 0) Then
                  RUSTNX = TAREFRUN
                  RULPOSN = 10
                Else
                  RUSTNX = RXGATREC (1)
                Endif
              Else
                RUSTNX = RLGATREC
              Endif
            Else
C
C -- Use airport gates if not default
C
              If (RXGATREC (1) .EQ. 0) Then
                RUSTNX = TAREFRUN
                RULPOSN = 10
              Else
                RUSTNX = RXGATREC (1)
              Endif
            Endif
          Endif
        Elseif (TAPOSN .GE. SPC_GAT1
     &                   .AND. TAPOSN .LE. SPC_GAT5) Then
C
C -- Generic scene gate
C
C  bdarvell:  eliminate motion bump during gate repos
          IF (RUPOSN.NE.TAPOSN) THEN
            TCFFLPOS = .TRUE.
            GATE_DELAY = GATE_DELAY_PARAM
            MOT_WASH = 1000.0
            MSKIP = .TRUE.
          ENDIF
C
          If (GEN_SC) Then
            RUSTNX = TAREFRUN
            RULPOSN = GEN_GATE
          Else
C
C -- Specific gate
C
            If (RXGATREC (TAPOSN - (SPC_GAT1 - 1)) .EQ. 0) Then
              RUSTNX = TAREFRUN
              RULPOSN = 10
            Else
              RUSTNX = RXGATREC (TAPOSN - (SPC_GAT1 - 1))
            Endif
          Endif
        Else
C
C -- Normal reposition
C
          RUSTNX = TAREFRUN
        Endif
C
C -- Make sure the reposition number is valid before doing anything
C
        If (TAPOSN .GT. IPOSNUM) Then
          TAPOSN = -1
        Else
C
C -- Reset redo flag (second reposition for on G/S reps) if a
C    reposition has taken place.
C
          IF (RUPOSN.NE.TAPOSN) REDO = .FALSE.
          RUPOSN = TAPOSN
          TCPOSN = TAPOSN
          TAPOSN = -1
          TAXPOS = 0
          TCMREPOS = .TRUE.
          PREPOS   = TCMREPOS    !Here so it will not be forgotten
          TEMPPOS  = .FALSE.
          STNREPOS = .FALSE.
        Endif
C
      Elseif (TAXPOS .NE. 0) Then
C
C -- Obtain station index
C
        RUSTNX = -TAXPOS
        STNREPOS = .TRUE.
C
      Endif
C
C -- Check if data available
C
      If (RUSTNX.NE.RUREPREC .AND. -RUSTNX.NE.RUREPIDX) Then
        If (RXACCESS(11,1) .EQ. 0) RXACCESS(11,1) = RUSTNX
        READY = .FALSE.
C
C -- Check if spare station
C
      Elseif (RUREPREC .NE. 0) Then
        If (REPTYN .EQ. 0) Then
          READY = .FALSE.
          TCMREPOS = .FALSE.
          TAXPOS = 0.
        Elseif (RUSTNX .EQ. RUREPREC) Then
          GATE_DELAY = AMAX1(GATE_DELAY - 1.0 , 0.0 )
          If (GATE_DELAY .LE. 1.0) READY = .TRUE.
          TAXPOS = RUREPIDX
        Else
          GATE_DELAY = AMAX1(GATE_DELAY - 1.0 , 0.0 )
          If (GATE_DELAY .LE. 1.0) READY = .TRUE.
        Endif
      Endif
C
C -- Check for a/c reposition request
C
      If (READY .AND. ((TCMREPOS .AND. .NOT.TEMPPOS).OR.
     -    (REDO.AND..NOT.RUFLT))) THEN
C
C -- Initialize flags, clocks
C
        RTHELE = RUREPELE      !initilized here in critical band
        TEMPPOS = .TRUE.
C
C -- Do not set RURFLG on the second reposition so that RTAVAR is set
C    to the repos station's variation only once
C
        IF (.NOT.REDO.OR.RUFLT) RURFLG = .TRUE.
        RUAFLG = .TRUE.
        KLOK1 = 20
        KLOK2 = 20
C
C -- Check for runway type station
C
        If ((.NOT.STNREPOS.OR.REDO)
     &       .AND. (REPTYN.EQ.1 .OR. REPTYN.EQ.10)) Then
C
C -- Use a 3 degree glideslope if no glideslope
C
          If (RUREPGPA .LE. 0.) Then
            RUREPGPA = 3.0
          Endif
C
CD RU005  Move position to touchdown as reference for repositioning
C
          If (RUREPGPX.LT.0) Then
            GPX = 1000 + RUREPRWE
          Else
            GPX = RUREPGPX
          Endif
C
          RHDG   = (RUREPHDG - 180.0) * DEG_RAD
          RDIST  = (RUREPRLE - GPX) * FT_DEG
          REPLAT = RUREPLAT + RDIST * DCOS (RHDG)
          REPLON = RUREPLON + RDIST * DSIN (RHDG)
     &                         / DCOS (REPLAT * DEG_RAD)
C
C -- Store reposition #
C
          IPOS = RULPOSN
C
C -- Update position
C
C -- Check for no position change
C
          If (RULPOSN .NE. NOLATLON) Then
C
C -- Ground reposition flag
C
            GNDPOS = (RULPOSN.EQ.GEN_GATE) .OR.
     &               (RULPOSN.EQ.GEN_TAXI) .OR.
     &               (RULPOSN.EQ.GEN_HOLD) .OR.
     &               (RULPOSN.EQ.GEN_RHLD)
C
C -- RU190  Obtain x, y offsets
C  --------------------------
            If ((RULPOSN .LT. OFFSET_1
     &            .OR. RULPOSN .GT. OFFSET_5)
     &            .AND. (RULPOSN .NE. GEN_GATE)) Then
C
              X = XOFF (IPOS)
              Y = YOFF (IPOS)
              RHDG = RUREPHDG - 180.
C
C -- RU100  Obtain x, y offsets from generic position offset table
C  -------------------------------------------------------------
            Elseif ( GEN_SC .AND. GNDPOS ) Then
C
CGEN+ Set tatermpos depending on taterm
C
                IF ((TATERM.EQ.1) .OR. (TATERM.EQ.3) .OR.
     &              (TATERM.EQ.5)) THEN
                   TERMINAL = LEFT
                ELSEIF ((TATERM.EQ.2) .OR. (TATERM.EQ.4)
     &              .OR. (TATERM.EQ.6)) THEN
                   TERMINAL = RIGHT
                ELSE
                   TERMINAL = 0
                ENDIF
CGEN-
C
C -- Generic gate position
C
              If (RULPOSN.EQ.GEN_GATE) Then
                If (TERMINAL .EQ. LEFT) Then
                  X = (XGATEL + RUREPRWE) * FT_NM
                  Y = YGATEL * FT_NM
                Else
                  X = (XGATER + RUREPRWE) * FT_NM
                  Y = YGATER * FT_NM
                Endif
C
C -- Generic taxi position
C
              Elseif (RULPOSN.EQ.GEN_TAXI) Then
                If (TERMINAL .EQ. LEFT) Then
                  X = (XTAXIL + RUREPRWE) * FT_NM
                  Y = YTAXIL * FT_NM
                Else
                  X = (XTAXIR + RUREPRWE) * FT_NM
                  Y = YTAXIR * FT_NM
                Endif
C
C -- Generic hold position
C
              Elseif (RULPOSN.EQ.GEN_HOLD) Then
                If (TERMINAL .EQ. LEFT) Then
                  X = (XHOLDL + RUREPRWE) * FT_NM
                  Y = YHOLDL * FT_NM
                Else
                  X = (XHOLDR + RUREPRWE) * FT_NM
                  Y = YHOLDR * FT_NM
                Endif
C
              Endif
C
C -- Move position to end of rwy as reference for repositioning
C
              RHDG = (RUREPHDG-180.0) * DEG_RAD
              RDIST = RUREPRLE * FT_DEG
C
              REPLAT = RUREPLAT + RDIST * DCOS (RHDG)
              REPLON = RUREPLON + RDIST * DSIN(RHDG)
     &                             / DCOS (REPLAT * DEG_RAD)
              RHDG = RUREPHDG
            Else
C
C -- Pick up offsets from station data
C
              X = RUREPPOS (1,IPOS - (OFFSET_1 - 1)) * FT_NM
              Y = - RUREPPOS (2,IPOS - (OFFSET_1 - 1)) * FT_NM
C
C -- Move position to end of rwy as reference for repositioning
C
              RHDG = (RUREPHDG-180.0) * DEG_RAD
              RDIST = RUREPRLE * FT_DEG
C
              REPLAT = RUREPLAT + RDIST * DCOS (RHDG)
              REPLON = RUREPLON + RDIST * DSIN(RHDG)
     &                             / DCOS (REPLAT * DEG_RAD)
              RHDG = RUREPHDG
            Endif
C
CD RU010  Takeoff end position
C
            If (RULPOSN .EQ. TOENDPOS (1) .OR.
     &             RULPOSN .EQ. TOENDPOS (2) .OR.
     &             RULPOSN .EQ. TOENDPOS (3)) Then
              RDIST = (GPX-RUREPRWE) * FT_DEG
              RHDG = RUREPHDG - 180.
              ILSREP = .FALSE.
C
CD RU015  Opposite end position
C
            Elseif (RULPOSN .EQ. OPENDPOS (1) .OR.
     &                      RULPOSN .EQ. OPENDPOS (2)) Then
C
CGEN+ For generic database extended rwy length must be counted
C
              If (GEN_SC) Then
                RDIST = (VSAGRWYL - GPX) * FT_DEG
C2ULD+++                RHDG = RUREPHDG
                RHDG = VSAGHDG
              Else
                RDIST = (RUREPRLE-GPX) * FT_DEG
                RHDG = RUREPHDG
              Endif
CGEN-
              ILSREP = .FALSE.
C
C -- Over touchdown
C
            Elseif (X .EQ. 0. .AND. Y .EQ. 0.) Then
              RDIST = 0.
              RHDG = 0.
              ILSREP = .FALSE.
C
C -- A/C on ILS
C=
            Elseif (X .EQ. ON_GS) Then
              IF(TAN(RUREPGPA*DEG_RAD).NE.0)THEN
                DIST = ((ALTOFF(IPOS)+UGEAR+UGLG2+RBCGGPY(1)*COSP+
     &          (RBCGGPX(1)*SINP))*1/TAN(RUREPGPA*DEG_RAD))*FT_DEG
              ELSE
                DIST = 0.0
              ENDIF
C
              CGPX = RBCGGPX(1)*FT_DEG
C
C -- Determine if ILS heading is the same as runway heading
C
              If (RUREPLCH.EQ.RUREPHDG) Then
                GPY = RUREPGPY*FT_DEG
                IF (DIST*DIST.GT.GPY*GPY) THEN
                  RDIST = SQRT((DIST*DIST)-(GPY*GPY))
                ELSE
                  RDIST = 0.0
                ENDIF
                RDIST = RDIST + CGPX
                RHDG = RUREPHDG - 180.
                ILSREP = .FALSE.
              Else
C
C -- Calculate G/S transmitter latitude and longitude
C
                COSLAT = COS(SNGL(RUREPLAT)*DEG_RAD)
                HDG  = (RUREPHDG-180.0) * DEG_RAD
                COSGP = COS(HDG)
                SINGP = SIN(HDG)
                RTEMP = RUREPRLE - RUREPGPX
                DLAT  = (RTEMP*COSGP+RUREPGPY*SINGP) * FT_DEG
                DLON  = (RTEMP*SINGP-RUREPGPY*COSGP) * FT_DEG / COSLAT
                TXGPLAT = RUREPLAT + DLAT
                TXGPLON = RUREPLON + DLON
C
C -- Determine lat/lon to reposition a/c on loc,on g/s
C
                HDIFF = RUREPHDG - RUREPLCH
                DLE = RUREPLCX
                DLY = RUREPLCY
                GGPX = RUREPGPX
                GGPY = RUREPGPY
                TEMPDST = (SQRT ((DLE - GGPX) ** 2
     &                            + (GGPY - DLY) ** 2)) * FT_DEG
                IF(RUREPGPY.NE.RUREPLCY) THEN
                  IF(RUREPLCX.NE.RUREPGPX) THEN
                    XDIF = RUREPLCX-RUREPGPX
                    YDIF = RUREPGPY-RUREPLCY
                    ANGSLOC = ATAN2(YDIF,XDIF)
                  ELSE
                    ANGSLOC = 90. * DEG_RAD
                  ENDIF
                ELSE
                  ANGSLOC = 0.0
                ENDIF
C
                IF (DIST.NE.0.0) THEN
                  MINDST = TEMPDST*SIN(HDIFF*DEG_RAD-ANGSLOC)/DIST
                  IF (ABS(MINDST). LE. 1.) THEN
                    ILSREP = .TRUE.
                    RHDG = (RUREPLCH + 90)*DEG_RAD +
     &                    ACOS(MINDST)
                  ELSE
                    ILSREP = .FALSE.
                  ENDIF
                ELSE
                  RHDG = 0
                  ILSREP=.FALSE.
                ENDIF
C
                IF(ILSREP)THEN
                  TEMPLAT = TXGPLAT + DIST * COS (RHDG)
                  TEMPLON = TXGPLON + DIST * SIN (RHDG)
     &                         / COS (SNGL (TEMPLAT) * DEG_RAD)
                  TDFRST = .TRUE.
                  RUCOSLAT = COS(SNGL(REPLAT) * DEG_RAD)
                  CALL RNGBRG(TEMPLAT,TEMPLON,REPLAT,REPLON,
     &                      TDCOS,TDFRST,TDRNG,TDBRG)
C
C If reposition from t/d is less than the distance from t/d to
C the intersection of the loc hdg and the rwy hdg, reposition with
C reference to t/d,otherwise reposition with reference to
C g/s transmitter
C
                  IF ((RUREPLCX .LT. GPX).AND.(SIN(HDIFF).NE.0)) THEN
                    IF ( COS(HDIFF).EQ.0 ) THEN
                      FIXED = (GPX - RUREPLCX)*FT_NM
                    ELSE
                      FIXED = (GPX - RUREPLCX - RUREPLCY/
     -                     (TAN(HDIFF*DEG_RAD)))*FT_NM
                    ENDIF
                  ELSE
                    FIXED = 2
                  ENDIF
                ENDIF
 
C
                IF ( (TDRNG.GT.FIXED) .AND. ILSREP) THEN
                  RDIST = TDRNG*NM_DEG
                  RHDG = TDBRG
                  RDIST = SQRT((RDIST*RDIST)+(CGPX*CGPX)-(2*RDIST*
     &                    CGPX*COS((RUREPLCH-TDBRG)*DEG_RAD)))
                  IF(RDIST.NE.0.0)THEN
                  RHDG=RHDG-(ASIN(CGPX*SIN((RUREPLCH-TDBRG)*DEG_RAD)
     &                 /RDIST))*RAD_DEG
                  ILSREP = .TRUE.
                  ENDIF
                ELSE
                  IF(RUREPGPX.LT.0)THEN
                    RDIST = DIST + CGPX
                  ELSE
                    GPY = RUREPGPY*FT_DEG
                    IF (DIST*DIST.GT.GPY*GPY) THEN
                      RDIST = SQRT((DIST*DIST)-(GPY*GPY))
                    ELSE
                      RDIST = 0.0
                    ENDIF
                    RDIST = RDIST + CGPX
                  ENDIF
                  RHDG = RUREPHDG - 180.
                  ILSREP = .FALSE.
                ENDIF
              ENDIF
C
              If (RUFLT) THEN
                REDO = .TRUE.
              ELSE
                REDO = .NOT. REDO
              ENDIF
C
C -- Down Wind (opposite end)
C
            Elseif (X .EQ. D_WNDO) Then
CGEN+
              If (GEN_SC) Then
                X = -(VSAGRWYL - GPX) * FT_NM
              Else
                X = -(RUREPRLE-GPX) * FT_NM
              Endif
CGEN-
              RDIST = SQRT(X*X + Y*Y) * NM_DEG
              RHDG = RUREPHDG - 180. - ATAN2(Y,X)*RAD_DEG
              ILSREP = .FALSE.
C
C -- Down Wind (threshold)
C
            Elseif (X .EQ. D_WNDT) Then
              X = GPX * FT_NM
              RDIST = SQRT(X*X + Y*Y) * NM_DEG
              RHDG = RUREPHDG - 180. - ATAN2(Y,X)*RAD_DEG
              ILSREP = .FALSE.
C
C -- On threshold
C
            Elseif (X .EQ. ON_THR) Then
              RDIST = GPX * FT_DEG
              RHDG = RUREPHDG - 180.
              ILSREP = .FALSE.
C
C -- Same position
C
            Elseif (X .EQ. AS_IS .OR. Y .EQ. AS_IS) Then
              RBFRST = .TRUE.
              Call RNGBRG (REPLAT, REPLON, RUPLAT, RUPLON,
     &                     X1,RBFRST,RSTN,BSTN)
              X1 = RSTN * COS ((RUREPHDG - BSTN) * DEG_RAD)
              Y1 = RSTN * SIN ((RUREPHDG - BSTN) * DEG_RAD)
              If (X .EQ. AS_IS) X = X1
              If (Y .EQ. AS_IS) Y = Y1
              RDIST = SQRT (X**2 + Y**2) * NM_DEG
              If (X .NE. 0. .AND. Y .NE. 0.) Then
                RHDG = RHDG - ATAN2 (Y,X) * RAD_DEG
              Elseif (X .EQ. 0.) Then
                If (Y .LT. 0.) Then
                  RHDG = RHDG+90.
                Else
                  RHDG = RHDG-90.
                Endif
              Elseif (Y .EQ. 0.) Then
                If (X .LT. 0.) Then
                  RHDG = RHDG+180.
                Endif
              Endif
C
              ILSREP = .FALSE.
C
C -- Check for an offset reposition
C
            Elseif (XOFF (IPOS) .EQ. OF_REP) Then
              RDIST = RURD * NM_DEG
              RHDG  = RUBRG
              ILSREP = .FALSE.
C
CD RU020  Offset from touchdown
C
            Else
              IF(SIN((RUREPHDG-RUREPLCH)*DEG_RAD).NE.0) THEN
                IF(COS((RUREPHDG-RUREPLCH)*DEG_RAD).NE.0) THEN
                  FIXED=(GPX-RUREPLCX-(RUREPLCY/TAN((RUREPHDG-RUREPLCH)
     &                  *DEG_RAD)))*FT_NM
                ELSE
                  FIXED=(GPX-RUREPLCY)*FT_NM
                ENDIF
              ELSE
                FIXED = 2
              ENDIF
C
              IF (RUREPLCX.LT.GPX) THEN
                RTEMP = FIXED
              ELSE
                RTEMP = 2
              ENDIF
C
              IF ((X.LT.RTEMP).OR.ALTOFF(IPOS).GE.10000) THEN
                RDIST = SQRT(X*X + Y*Y) * NM_DEG
                RHDG = RHDG - ATAN2 (Y,X) * RAD_DEG
                ILSREP = .FALSE.
              ELSE
C
C---Set x-offset
C
                RDIST = X
                IF(RDIST.NE.0)THEN
                  MINDST=FIXED*SIN((RUREPHDG-RUREPLCH)*DEG_RAD)/RDIST
                  IF(ABS(MINDST).LE.1.) THEN
                    RHDG = RHDG + RUREPLCH - RUREPHDG +(ASIN(MINDST))
     &                     *RAD_DEG
                  ENDIF
                ELSE
                  RHDG = 0
                ENDIF
C
C---Set y-offset
C
                IF(Y.NE.0.0) THEN
                  RDIST = SQRT((RDIST*RDIST)+(Y*Y)-(2*RDIST*Y*SIN
     &                    ((RUREPLCH-RHDG)*DEG_RAD)))
                  IF(RDIST.NE.0.0)THEN
                    RHDG=RHDG-(ASIN(-Y*COS((RUREPLCH-RHDG)*DEG_RAD)
     &                   /RDIST))*RAD_DEG
                  ENDIF
                ENDIF
C
                RDIST = RDIST*NM_DEG
                ILSREP = .TRUE.
              ENDIF
            Endif
C
CD RU025  New position
C
            If (RHDG .LT. -180.) Then
              RHDG = RHDG + 360.
            Elseif (RHDG .GT. 180.) Then
              RHDG = RHDG - 360.
            Endif
            RHDG = RHDG * DEG_RAD
C
            IF(GEN_SC .AND. GNDPOS
     &           .AND.
     &         S_USE_GEN_POS)THEN
              POSITION = RULPOSN - 124
C2UDT+
              IF(RULPOSN .EQ. GEN_GATE)THEN
                POSITION=5
              ELSEIF(RULPOSN .EQ. GEN_HOLD)THEN
                POSITION=3
              ELSEIF(RULPOSN .EQ. GEN_TAXI)THEN
                POSITION=1
              ENDIF
CDEBUG+
              l_rulposn=rulposn
              l_position=position
              l_taterm=taterm
              l_tarwylen=tarwylen
              l_vsagrwyl=vsagrwyl
              l_vsaghdg=vsaghdg
              l_rurephdg=rurephdg
CDEBUG-
			
              CALL GEN_POS(POSITION,RUPLAT,RUPLON)
C2ULD++++  Reposition to opposite end in generic airport or Maxvue(Tropos)
            ELSEIF(GEN_SC
     &            .AND.
     &            (RULPOSN.EQ.OPENDPOS(1).OR.RULPOSN.EQ.OPENDPOS(2)))
     &            THEN
              GENLAT = RUREPLAT - (RUREPRLE -
     &                 RUREPRWE) * FT_DEG *
     &                 COS (RUREPHDG * DEG_RAD)
              GENLON = RUREPLON - (RUREPRLE -
     &                 RUREPRWE) * FT_DEG *
     &                 SIN (RUREPHDG * DEG_RAD)
     &                 / COS (SNGL (GENLAT) * DEG_RAD)
              CALL WGS84_DIR(GENLAT,GENLON,RUPLAT,RUPLON,
     &                       VSAGRWYL*FT_MT,RUREPHDG)
C2ULD----
            ELSE
              RUPLAT = REPLAT + RDIST * DCOS (RHDG)
              RUPLON = REPLON + RDIST * DSIN (RHDG)
     &                         / DCOS (RUPLAT * DEG_RAD)
            Endif
C
          Endif
C
CD RU030  Update heading
C
CGEN+
          If (RULPOSN .EQ. GEN_GATE) Then
            If (TERMINAL . EQ. LEFT) Then
CGEN+
              RUAHDG=RUREPHDG-HDGGENL
C              RUAHDG=VSAGHDG-HDGGENL
            Else
              RUAHDG=RUREPHDG-HDGGENR
C              RUAHDG=VSAGHDG-HDGGENR
CGEN-
            Endif
C
C -- Generic hold and reciprocal hold heading
C
          Elseif (GEN_SC .AND. ( (RULPOSN.EQ.GEN_HOLD) .OR.
     &           (RULPOSN.EQ.GEN_RHLD) ) ) Then
            If (TERMINAL . EQ. LEFT) Then
CGEN+
              RUAHDG = RUREPHDG + 90
C              RUAHDG = VSAGHDG + 90
            Else
              RUAHDG = RUREPHDG - 90
C              RUAHDG = VSAGHDG - 90
CGEN-
            Endif
C
C -- Generic taxi heading
C
          Elseif (GEN_SC .AND. RULPOSN.EQ.GEN_TAXI) Then
            If (TERMINAL . EQ. LEFT) Then
              IF(VSAGRWYL .LT. (1999./FT_MT)) THEN
                RUAHDG = RUREPHDG + 90
              Else
                RUAHDG = RUREPHDG - 180
              ENDIF
            ELSE
              IF(VSAGRWYL .LT. (1999./FT_MT)) THEN
                RUAHDG = RUREPHDG - 90
              ELSE
                RUAHDG = RUREPHDG + 180
              ENDIF
            Endif
C
C -- Gate heading
C
          Elseif (RULPOSN .GE. OFFSET_1
     &          .AND. RULPOSN .LE. OFFSET_5) Then
            RUAHDG = RUREPPOH (IPOS - (OFFSET_1 - 1))
C
C -- Preposition heading
C
          Elseif (RUREPPHD .NE. 0) Then
            RUAHDG = RUREPPHD
C
C -- No change in heading
C
          Elseif (HDGOFF (IPOS) .EQ. AS_IS) Then
            RUAHDG = VPSIDG
C
          Else
C
C -- Obtain heading offset
C
            IF(ILSREP) THEN
              RUAHDG = RUREPLCH - HDGOFF(IPOS)
            ELSE
              RUAHDG = RUREPHDG - HDGOFF(IPOS)
            ENDIF
          Endif
C
          If (RUAHDG .GT. 180.) Then
            RUAHDG = RUAHDG - 360.
          Elseif (RUAHDG .LT. -180.) Then
            RUAHDG = RUAHDG + 360.
          Endif
C
C -- Update heading
C
C -- Set repos heading only for repositions not on gs or for the first
C    time through on gs repositions (please note that at this point in
C    program, redo is false for repos not on gs, it is true for first
C    time through x on_gs, it is false for first time through alt on_gs)
C
          IF ( (.NOT.REDO.AND.X.NE.ON_GS).OR.
     &          (REDO.AND.ALTOFF(IPOS).NE.ON_GS))
     &          VPSI0 = RUAHDG * DEG_RAD
C
C -- RU220  Update the altitude
C  --------------------------
          If (GEN_SC .AND. GNDPOS) Then
C
C -- Altitude for generic gate position
C
            If (RULPOSN .EQ. GEN_GATE) Then
              RUAALT  = RUREPELB
              TCMACGND = .TRUE.
              TCMGATE = .TRUE.
C
C -- Altitude for generic taxi and hold positions
C
            Elseif ( (RULPOSN.EQ.GEN_TAXI) .OR.
     &             (RULPOSN.EQ.GEN_HOLD) ) Then
              RUAALT   = RUREPELB
              TCMACGND = .TRUE.
C
            Endif
C
C -- Return to ground
C
          Elseif (ALTOFF (IPOS) .EQ. ON_GND) Then
            If (RULPOSN.EQ.OPENDPOS(1).OR.RULPOSN.EQ.OPENDPOS(2)) Then
              RUAALT = RUREPELE
            Else
              RUAALT = RUREPELB
            Endif
            TCMACGND = .TRUE.
          Else
C
C -- Preposition altitude
C
            If (RUREPPAL .NE. 0) Then
              RUAALT = RUREPPAL * 2.
C
C -- No change in altitude
C
            Elseif (ALTOFF (IPOS) .EQ. AS_IS) Then
              RUAALT = VHS
C
C -- Put a/c on glideslope
C
            Elseif (ALTOFF (IPOS) .EQ. ON_GS) Then
C
C -- Calculate G/S transmitter latitude and longitude
C
              COSLAT = COS(SNGL(RUREPLAT)*DEG_RAD)
              HDG  = (RUREPHDG-180.0) * DEG_RAD
              COSGP = COS(HDG)
              SINGP = SIN(HDG)
              RTEMP = RUREPRLE - RUREPGPX
              DLAT  = (RTEMP*COSGP+RUREPGPY*SINGP) * FT_DEG
              DLON  = (RTEMP*SINGP-RUREPGPY*COSGP) * FT_DEG / COSLAT
              TXGPLAT = RUREPLAT + DLAT
              TXGPLON = RUREPLON + DLON
              TDFRST = .TRUE.
              RUCOSLAT = COS( SNGL(TXGPLAT) * DEG_RAD )
              CALL RNGBRG(RUPLAT,RUPLON,TXGPLAT,TXGPLON,
     &                    TDCOS,TDFRST,TDRNG,TDBRG)
              RUAALT =RUREPELB+(TDRNG/FT_NM)*TAN(RUREPGPA*DEG_RAD)
C Redo the reposition after flight trims
C
              If (RUFLT) THEN
                REDO = .TRUE.
              ELSE
                REDO = .NOT. REDO
              ENDIF
C
C -- Pressure altitude
C
            Elseif (ALTOFF(IPOS) .EQ. P_ALT) Then
              RUAALT = PRESALT(IPOS-62) - RUREPELB
C
C -- Flight level
C
            Elseif (ALTOFF(IPOS) .GE. 10000) Then
              RUAALT = ALTOFF(IPOS)
C
C -- Obtain altitude offset
C
            Else
              RUAALT = RUREPELB + ALTOFF(IPOS) + UGEAR + UGLG2
            Endif
C
C -- Round off if required and update altitude
C
            If (RNDOFF (IPOS) .NE. 0.) Then
              RUAALT = RUAALT + RNDOFF (IPOS) * 0.5
              RUAALT = RUAALT - AMOD (RUAALT, RNDOFF (IPOS))
            Endif
            VHS = RUAALT
          Endif
C
CD RU055  Set trim request to flight
C  ---------------------------------
C         Set trim request to flight only for repos not on gs
C         or for first time through on gs repos (please note
C         that at this point in the program, redo is false for
C         repos not on gs, and it is true for first time through
C         x on_gs and alt on_gs)
C
          If ( (X.NE.ON_GS.AND.ALTOFF(IPOS).NE.ON_GS)
     &       .OR.(REDO) )RUFLT = .NOT. RUTSTFLG
C
        Else
C
C -- Not a runway type station
C
          If (XOFF (IPOS) .EQ. OF_REP) Then
            RUPLAT = RUREPLAT + RURD * NM_DEG * COS (RUBRG * DEG_RAD)
            RUPLON = RUREPLON + RURD * NM_DEG * SIN (RUBRG * DEG_RAD)
     &                          / COS (SNGL (RUPLAT) * DEG_RAD)
          Else
            RUPLAT = RUREPLAT
            RUPLON = RUREPLON
          Endif
C
C -- Gate reposition
C
          If (RUREPTYP .EQ. GATE) Then
            RUFLT = .NOT.RUTSTFLG
            TCMGATE = .TRUE.
            RUAHDG = RUREPHDG
            If (RUAHDG .GT. 180.) Then
              RUAHDG = RUAHDG - 360.
            Elseif (RUAHDG .LT. -180.) Then
              RUAHDG = RUAHDG + 360.
            Endif
            RUPLAT = RUPLAT - VPILTXCG * FT_DEG
     &                                   * COS (RUAHDG * DEG_RAD)
            RUPLON = RUPLON - VPILTXCG * FT_DEG
     &                                   * SIN (RUAHDG * DEG_RAD)
     &                                   / COS (SNGL (RUPLAT) * DEG_RAD)
C
C -- Update heading
C
            VPSI0 = RUAHDG * DEG_RAD
C
C -- Update altitude
C
            RUAALT = RUREPELE + UGEAR + UGLG2
            VHS = RUAALT
            VHG = RUREPELE
            TCMACGND = .TRUE.
 
          Else
C Repos in air over a radio station
C
            RUPLAT = RUREPLAT
            RUPLON = RUREPLON
C
C Maintain the same alt above gnd on over stn repos
C and set height above ground
C
            RUAALT = VH + RUREPELE
            VHS = RUAALT
            VHG = RUREPELE
          Endif
C
C -- Preposition altitude
C
          If (RUREPPAL .NE. 0) THEN
            RUAALT = RUREPPAL*2.
            VHS = RUAALT
          Endif
C
C -- Preposition heading
C
          If (RUREPPHD. NE. 0) Then
            If (RUREPPHD .GT. 180) Then
              RUAHDG = RUREPPHD - 360
            Elseif (RUREPPHD .LT. -180) Then
              RUAHDG = RUREPPHD + 360
            Else
              RUAHDG = RUREPPHD
            Endif
            VPSI0 = RUAHDG * DEG_RAD
          Endif
        Endif
      Endif
C
CD RU060  Range and bearing request
C
      If (TAXRB .NE. 0) Then
C
C -- Obtain data for selected station
C
        If (TAXRB .EQ. RXMISIDX(1)) Then
          KLOK3 = KLOK3 - 1
          If (KLOK3 .LE. 0) Then
            KLOK3 = 60
            MISLAT = RXMISLAT (1)
            MISLON = RXMISLON (1)
            CALL RNGBRG(MISLAT,MISLON,RUPLAT,RUPLON,
     &                  RBCOS,RBFRST,RSTN,BSTN)
            RURSTN = RSTN
            If (BSTN .LT. 0.) BSTN = BSTN + 360.
            RUBSTN = BSTN
          Endif
        Else
          If (RXACCESS(14,1) .EQ. 0) RXACCESS(14,1) = -TAXRB
          RBFRST = .TRUE.
          KLOK3 = 1
        Endif
      Endif
C
CD RU065  Advance or retard on track
C
      If (TARAOT .NE. 0.) Then
C
C -- If a/c on ground calculate track angle
C
        If (VBOG .OR. RUTSTFLG) Then
          RUTRK = VPSI
        Else
          RUTRK = ATAN2(VVEW,VVNS)
        Endif
C
        If (RUTRK .GT. PI) Then
          RUTRK = RUTRK - TWO_PI
        Elseif (RUTRK .LT. -PI) Then
          RUTRK = RUTRK + TWO_PI
        Endif
C
C -- Advance/retard in desired direction
C
        RTEMP = NM_DEG * TARAOT
        RUPLAT = RUPLAT + RTEMP * COS (RUTRK)
        RUPLON = RUPLON + RTEMP * SIN (RUTRK)
     &                     / COS (SNGL (RUPLAT) * DEG_RAD)
        TARAOT = 0
C
C -- Set fast align flag and clock
C
        RUAFLG = .TRUE.
        KLOK2 = 20
      Endif
C
C -- Check for position slew on
C
      If (TCMPOSL) Then
C
C -- Set fast align flag and clock
C
        RUAFLG = .TRUE.
        KLOK2 = 20
C
C -- Check for freezes, R/A test, trim on
C
      Elseif (.NOT. (TCFPOS .OR. TCFFLPOS .OR. RUFLT .OR. TEMPPOS)
     &          .OR. RUTSTFLG) Then
C
CD RU070  Integrate velocities and compute new A/C position
C         using ground speed rate factor
C
C CJ+ SEP 19-1991 ADDING CODE FOR POLAR NAV
C
        RTEMP = SNGL(RUPLAT) * DEG_RAD
C
C COMPUTE SIN SQUARED TO BE USED FOR RADIUS CALCULATIONS
C
        SINLAT = SIN(RTEMP)
        SINSQR = SINLAT * SINLAT
C
C COMPUTE E/W RADIUIS
C
        RUEW = REQ * (1 + ELPCT * SINSQR)
C
        RUEWINV = 1./ RUEW
C
C COMPUTE N/S RADIUIS
C
        RUNS = REQ * (1 - ELPCT * (2 - 3*SINSQR))
C
        IF(RUNS .GT. 0.0)RUNSINV = 1. / RUNS
C
        If (TCMTIMCP) Then
          RATE = RAD_DEG * YITIM * 2.5
        ELSEIF (TASPDUP .NE. 0.0) THEN
          RATE = RAD_DEG * YITIM * TASPDUP
        ELSE
          RATE = RAD_DEG * YITIM
        ENDIF
C
C UPDATING LAT, LON
C LAT = LAT + VVNS*(180/PI)*(1/R)*YITIM   !R IS THE RADIUS
C
          RUPLAT = RUPLAT + VVNS * RUNSINV * RATE
          RUPLON = RUPLON + VVEW * RUEWINV * RATE
     &                              / COS(RTEMP)
C CJ-
C
C -- Check for E/W change
C
        If (RUPLON .LT. -180.) Then
          RUPLON = RUPLON + 360.
        Elseif (RUPLON .GT. 180.) Then
          RUPLON = RUPLON - 360.
        END If
C
C -- Check for N/S poles
C
        If (RUPLAT .GE. 89.99972) Then
          RUPOLFLG = .TRUE.
          RUPLAT = 180. - RUPLAT
          If (RUPLAT .GE. 89.99972) RUPLAT = 89.99970
          If (RUPLON .GE. 0.) Then
            RUPLON = RUPLON - 180.
          Else
            RUPLON = RUPLON + 180.
          Endif
        Elseif (RUPLAT .LE. -89.99972) Then
          RUPOLFLG = .TRUE.
          RUPLAT = -180. - RUPLAT
          If (RUPLAT .LE. -89.99972) RUPLAT = -89.99970
          If (RUPLON .GE. 0.) Then
            RUPLON = RUPLON - 180.
          Else
            RUPLON = RUPLON + 180.
          Endif
        Endif
C
C -- Change a/c heading
C
        If (RUPOLFLG) Then
C
C CJ+ SEP 19-1991 POLAR NAV CODE
C
          RUAHDG = VPSIDG + 180
          IF(RUAHDG .GT. 360.) RUAHDG = RUAHDG - 360.0
          VPSI0 = RUAHDG * DEG_RAD
          VPSIC = 0.
          VVNS = -VVNS
          RUPOLFLG = .FALSE.
        Endif
      Endif
C CJ-
C
CD RU075  Compute cos and sin of a/c lat
C
      RTEMP = SNGL (RUPLAT) * DEG_RAD
      RUCOSLAT = COS (RTEMP)
      RUSINLAT = SIN (RTEMP)
C
C -- Check status of clocks and flags
C
250   Continue
      If (KLOK1 .GT. 0) Then
        KLOK1 = KLOK1 - 1
        If (KLOK1 .LE. 0) Then
          TCMREPOS = .FALSE.
          If (.NOT. RUFLT) TCFFLPOS = .TRUE.
          TEMPPOS = .FALSE.
          RURFLG = .FALSE.
          RUALTF = .FALSE.
        Endif
      Endif
C
      If (KLOK2 .GT. 0) Then
        KLOK2 = KLOK2 - 1
        If (KLOK2 .LE. 0) RUAFLG = .FALSE.
      Endif
C
C -- Save lat/lon
C
      PRELAT = RUPLAT
      PRELON = RUPLON
C
      Return
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00697 EBUG+
C$ 00705 EBUG-
C$ 01251 RU005  Move position to touchdown as reference for repositioning
C$ 01371 RU010  Takeoff end position
C$ 01380 RU015  Opposite end position
C$ 01601 RU020  Offset from touchdown
C$ 01656 RU025  New position
C$ 01677 EBUG+
C$ 01685 EBUG-
C$ 01711 RU030  Update heading
C$ 01894 RU055  Set trim request to flight
C$ 01982 RU060  Range and bearing request
C$ 02007 RU065  Advance or retard on track
C$ 02053 RU070  Integrate velocities and compute new A/C position
C$ 02139 RU075  Compute cos and sin of a/c lat
C
C
C
C'Name         Navigational Aids Test Facility
C'Module_ID    RATEST
C'PDD_#        TBD
C'Customer     All
C'Author       B. Isaac
C'Date         08/04/87
C
C
      Subroutine RATEST
C     -----------------
C
      Implicit None
C
C
C'Purpose
C        To aid in the integration of R/A system programs.
C        The R/A ATM uses this facility.  The flight program
C        is frozen when this program is running.  All the
C        inputs come from the NATF I/F crt page.  The following
C        tasks may be performed;
C
C        1. fly a circular path around a station at a given radius
C        2. reposition at any angle and bearing to a station
C        3. fly backwards up along the glidepath
C        4. change altitude
C        5. change heading
C        6. move a/c at any speed
C        7. ATM setup requests
C
C'Inputs
C        station number
C        reposition request
C        reposition angle and distance
C        heading request flag and value
C        altitude request flag and value
C        a/c speed
C        fly glideslope request
C        circle station request flag and radius
C        setup request flag and number
C        increment/decrement setup request
C
C'Outputs
C        flight freeze
C        N/S, E/W velocities
C        altitude
C        heading
C        reposition request
C        advance on track request
C        magnetic variation request
C
C'Common_Database_Variables
C
CQ    USD8   XRFTEST(*)
C
CP    USD8 RUPOSTN,RUGSLW,RUHDFLG,RUALTFLG,RTSETVAR,RUSETACT,
CP   -     RUSETNXT,RUSETPRV,
CP   -     RUVEL,RURC,RURD,RUBRG,RUHDGT,RUALTST,RTHELE,RUSET,
CP   -     RUREPIDX,RUREPLAT,RUREPLON,RUREPHDG,RUREPGPX,RUREPELB,
CP   -     RUREPRWE,RUREPGPA,RUREPRLE,
C
CP   -     RUPLAT,RUPLON,RXACCESS,RTAVAR,
C
CP   -     TCFFLPOS,TAXPOS,TCMREPOS,TARAOT,
C
CP   -     VHS,VPSI,VH,VHH,VCSPSI,VSNPSI,VVNS,VVEW,VPSIDG,VGRALT,
C
CP   -     VTHETA,VTHETADG,VSNTHE,VCSTHE,VPHI,VPHIDG,VSNPHI,VCSPHI
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  6-May-2013 21:13:16 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
     &, RUREPLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RUREPLON       !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
C$
      REAL*4   
     &  RTAVAR         ! MAGNETIC VARIATION                    [DEG]
     &, RTHELE         ! GROUND ELEVATION                       [FT]
     &, RUALTST        ! SPECIFIED ALTITUDE (TRUE)
     &, RUBRG          ! BEARING FROM STN   (DEG)              [DEG]
     &, RUHDGT         ! SPECIFIED HEADING  (DEG)              [DEG]
     &, RURD           ! RANGE FROM STATION (NM)                [NM]
     &, RUREPGPA       ! 53 G/P ANGLE (DEGREES)                [DEG]
     &, RUREPHDG       !  7 TRUE HEADING (DEGREES)             [DEG]
     &, RUVEL          ! VELOCITY FOR TEST                    [KNTS]
     &, TARAOT         ! ADV/RET ON STN/POS INDEX
     &, TAXPOS         ! REPOSITION STN/POS INDEX
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSPSI         ! COSINE OF VPSI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VGRALT         ! USUAL CG HEIGHT ABOVE GROUND            [ft]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPSI           ! CORRECTED A/C HEADING                  [rad]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VSNPHI         ! SINE OF VPHI
     &, VSNPSI         ! SINE OF VPSI
     &, VSNTHE         ! SINE OF VTHETA
     &, VTHETA         ! A/C PITCH ANGLE                        [rad]
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
C$
      INTEGER*4
     &  RUREPIDX       ! 31 STATION INDEX NUMBER
     &, RUSET          ! SET UP NUMBER
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
C$
      INTEGER*2
     &  RUREPELB       ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RUREPGPX       ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RUREPRLE       !  8 RUNWAY LENGTH (FEET)                [FT]
     &, RUREPRWE       ! 60 RUNWAY END TO THRESHOLD (FEET)      [FT]
C$
      LOGICAL*1
     &  RTSETVAR       ! FREEZE VARIATION
     &, RUALTFLG       ! SET ALTITUDE FLAG
     &, RUGSLW         ! SLEW A/C ON G/S FLAG
     &, RUHDFLG        ! SET HEADING FLAG
     &, RUPOSTN        ! REPOS'N AT ANGLE TO STN
     &, RURC           ! CIRCLE STATION FLAG
     &, RUSETACT       ! R/A SET UP SELECT/ACTIVATE
     &, RUSETNXT       ! INCREMENT SET UP NO.
     &, RUSETPRV       ! DECREMENT SET UP NO.
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCMREPOS       ! REPOSITION A/C
C$
      LOGICAL*1
     &  DUM0000001(17672),DUM0000002(4),DUM0000003(20)
     &, DUM0000004(4),DUM0000005(72),DUM0000006(156)
     &, DUM0000007(4),DUM0000008(24),DUM0000009(4)
     &, DUM0000010(20400),DUM0000011(648),DUM0000012(17)
     &, DUM0000013(5494),DUM0000014(8),DUM0000015(10)
     &, DUM0000016(20),DUM0000017(14),DUM0000018(12)
     &, DUM0000019(6),DUM0000020(286),DUM0000021(2)
     &, DUM0000022(3762),DUM0000023(256081),DUM0000024(6882)
     &, DUM0000025(1963)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VPHI,VPHIDG,VSNPHI,VCSPHI,DUM0000002,VTHETA
     &, VTHETADG,VSNTHE,VCSTHE,DUM0000003,VPSI,VPSIDG,DUM0000004
     &, VSNPSI,VCSPSI,DUM0000005,VVNS,VVEW,DUM0000006,VHS,DUM0000007
     &, VHH,DUM0000008,VGRALT,DUM0000009,VH,DUM0000010,RUPLAT
     &, RUPLON,DUM0000011,RTHELE,RTAVAR,DUM0000012,RTSETVAR,DUM0000013
     &, RUREPLAT,RUREPLON,DUM0000014,RUREPHDG,RUREPRLE,DUM0000015
     &, RUREPIDX,DUM0000016,RUREPGPX,DUM0000017,RUREPGPA,DUM0000018
     &, RUREPRWE,DUM0000019,RUREPELB,DUM0000020,RURD,RUBRG,RUHDGT
     &, RUALTST,RUVEL,RUSET,RUSETACT,RUSETNXT,RUSETPRV,DUM0000021
     &, RURC,RUPOSTN,RUGSLW,RUHDFLG,RUALTFLG,DUM0000022,RXACCESS
     &, DUM0000023,TCFFLPOS,DUM0000024,TCMREPOS,DUM0000025,TAXPOS
     &, TARAOT    
C------------------------------------------------------------------------------
C
C'Local_Variables
C
C      INCLUDE 'DISP.COM' !NOFPC
C
      REAL*4 YITIM
      COMMON /DISPCOM/YITIM
C
      Real*4 DEG_RAD,
     &       FT_DEG,
     &       KT_FPS,
     &       NM_FT
C
      Integer*4 NUMSETUP
C
      Parameter (DEG_RAD = 0.0174533,
     &           FT_DEG = 2.74298E-6,
     &           KT_FPS = 1.6878056,
     &           NM_FT = 6076.1,
     &           NUMSETUP = 139)         !max # of setups
C
      Real*8 RUGPLN,                   !glideslope latitude
     &       RUGPLT                    !glideslope longitude
C
      Real*4 RURDS,                    !circle station radius
     &       COSLAT,                   !cos latitude
     &       RURNG,                    !range to glideslope
     &       RHDG,                     !heading
     &       RUHDGRT,                  !heading rate
     &       TEMP,                     !temporary storage
     &       ITHOUR
C
      Integer*4 I,                     !DO loop counter
     &          J                      !DO loop counter
C
      Real*4    ARRAY(NUMSETUP,6),
     &          ADV_TRK(NUMSETUP),     !advance on track value
     &          AIRSPEED(NUMSETUP),    !airspeed
     &          ALTITUDE(NUMSETUP),    !altitude
     &          HEADING(NUMSETUP),     !heading
     &          MAG_VAR(NUMSETUP),     !magnetic variation
     &          POSITION(NUMSETUP)     !position index
C
      Logical*1 RUINIT,                !first pass flag for glideslope
     &          FRST,                  !first pass flag for RNGBRG
     &          REPOS/.TRUE./          !repos performed via setup
C
      Equivalence (ARRAY(1,1),POSITION),
     &            (ARRAY(1,2),HEADING),
     &            (ARRAY(1,3),ALTITUDE),
     &            (ARRAY(1,4),AIRSPEED),
     &            (ARRAY(1,5),ADV_TRK),
     &            (ARRAY(1,6),MAG_VAR)
C
      Data
C
     &     ((ARRAY (I,J),J = 1,6),I=1,50)/
C
C        Pos   Heading   Altitude   Airspeed   Advtrack   Mag var   Set up #
C        --------------------------------------------------------------------
C
     &    12,    270,         0,         0,         0,       0,     !  1
     &     3,     45,         0,         0,         0,       0,     !  2
     &     3,     45,         0,         0,         0,     -10,     !  3
     &     3,     45,         0,         0,         0,       5,     !  4
     &     3,    135,         0,         0,         0,       0,     !  5
     &     3,    135,         0,         0,         0,     -10,     !  6
     &     3,    135,         0,         0,         0,       5,     !  7
     &     3,    225,         0,         0,         0,       0,     !  8
     &     3,    225,         0,         0,         0,     -10,     !  9
     &     3,    225,         0,         0,         0,       5,     !  10
     &     3,    315,         0,         0,         0,       0,     !  11
     &     3,    315,         0,         0,         0,     -10,     !  12
     &     3,    315,         0,         0,         0,       5,     !  13
     &     3,    315,         0,         0,         0,       0,     !  14
     &    19,      0,         0,         0,         0,       0,     !  15
     &    21,      0,         0,         0,         0,       0,     !  16
     &    21,      0,         0,         0,         0,     -10,     !  17
     &    21,      0,         0,         0,         0,       5,     !  18
     &    24,      0,         0,         0,         0,       0,     !  19
     &    24,      0,         0,         0,         0,     -10,     !  20
     &    24,      0,         0,         0,         0,       5,     !  21
     &    27,      0,         0,         0,         0,       0,     !  22
     &    27,      0,         0,         0,         0,     -10,     !  23
     &    27,      0,         0,         0,         0,       5,     !  24
     &    30,      0,         0,         0,         0,       0,     !  25
     &    30,      0,         0,         0,         0,     -10,     !  26
     &    30,      0,         0,         0,         0,       5,     !  27
     &    30,      0,         0,         0,         0,       0,     !  28
     &    21,      0,         0,         0,         0,       0,     !  29
     &    19,      0,         0,         0,         0,       0,     !  30
     &    30,      0,      5000,         0,         0,       0,     !  31
     &    33,    270,      3628,         0,         0,       0,     !  32
     &    35,    270,      5917,         0,         0,       0,     !  33
     &    35,    270,         0,         0,         0,       0,     !  34
     &    11,    270,         0,         0,         0,       0,     !  35
     &    12,    270,         0,         0,         0,       0,     !  36
     &    13,    270,         0,         0,         0,       0,     !  37
     &    27,     90,     20000,         0,         0,       0,     !  38
     &    27,     90,     20000,       200,         0,       0,     !  39
     &    38,     90,     20000,       200,        20,       0,     !  40
     &    41,     90,     20000,       200,         0,       0,     !  41
     &    41,     90,         0,         0,         0,       0,     !  42
     &     3,     90,      5000,         0,         0,       0,     !  43
     &     3,     90,     10000,         0,         0,       0,     !  44
     &     3,     90,     20000,         0,         0,       0,     !  45
     &     3,     90,     30000,         0,         0,       0,     !  46
     &     3,     90,     40000,         0,         0,       0,     !  47
     &    21,     90,      5000,         0,         0,       0,     !  48
     &    21,     90,     10000,         0,         0,       0,     !  49
     &    21,     90,     20000,         0,         0,       0/    !  50
      DATA
     &     ((ARRAY (I,J),J = 1,6),I=51,100)/
     &    21,     90,     30000,         0,         0,       0,     !  51
     &    21,     90,     40000,         0,         0,       0,     !  52
     &    34,     90,      5000,         0,         0,       0,     !  53
     &    34,     90,     10000,         0,         0,       0,     !  54
     &    34,     90,     20000,         0,         0,       0,     !  55
     &    34,     90,     30000,         0,         0,       0,     !  56
     &    34,     90,     40000,         0,         0,       0,     !  57
     &    38,     90,      5000,         0,         0,       0,     !  58
     &    38,     90,     10000,         0,         0,       0,     !  59
     &    38,     90,     20000,         0,         0,       0,     !  60
     &    38,     90,     30000,         0,         0,       0,     !  61
     &    38,     90,     40000,         0,         0,       0,     !  62
     &    39,     90,      5000,         0,         0,       0,     !  63
     &    39,     90,     10000,         0,         0,       0,     !  64
     &    39,     90,     20000,         0,         0,       0,     !  65
     &    39,     90,     30000,         0,         0,       0,     !  66
     &    39,     90,     40000,         0,         0,       0,     !  67
     &    40,     90,      5000,         0,         0,       0,     !  68
     &    40,     90,     10000,         0,         0,       0,     !  69
     &    40,     90,     20000,         0,         0,       0,     !  70
     &    40,     90,     30000,         0,         0,       0,     !  71
     &    40,     90,     40000,         0,         0,       0,     !  72
     &    17,     90,      5000,         0,         0,       0,     !  73
     &    17,     90,     10000,         0,         0,       0,     !  74
     &    17,     90,     20000,         0,         0,       0,     !  75
     &    17,     90,     30000,         0,         0,       0,     !  76
     &    17,     90,     40000,         0,         0,       0,     !  77
     &    41,     90,     30000,       200,        15,       0,     !  78
     &    21,     90,         0,         0,         0,       0,     !  79
     &    47,     90,         0,         0,         0,       0,     !  80
     &    38,     90,       600,         0,         0,       0,     !  81
     &    38,     90,       250,         0,         0,       0,     !  82
     &    40,     90,      4500,         0,         0,       0,     !  83
     &    40,     90,      2500,         0,         0,       0,     !  84
     &    42,     90,     18000,         0,         0,       0,     !  85
     &    42,     90,     12000,         0,         0,       0,     !  86
     &    43,     90,     40000,         0,         0,       0,     !  87
     &    43,     90,     30000,         0,         0,       0,     !  88
     &    32,    270,     10000,         0,         0,       0,     !  89
     &    34,    270,     10000,         0,         0,       0,     !  90
     &    36,    270,     10000,         0,         0,       0,     !  91
     &    44,    270,     10000,       200,         0,       0,     !  92
     &    44,    270,      7500,       200,         0,       0,     !  93
     &    17,     90,      8000,         0,         0,       0,     !  94
     &    17,     90,      5500,         0,         0,       0,     !  95
     &    41,     90,     12000,         0,         0,       0,     !  96
     &    41,     90,      9000,         0,         0,       0,     !  97
     &    34,    270,      4772,         0,         0,       0,     !  98
     &    34,    270,      3648,         0,         0,       0,     !  99
     &    34,    270,      5917,         0,         0,       0/     !  100
      DATA
     &     ((ARRAY (I,J),J = 1,6),I=101,NUMSETUP)/
     &    47,    270,      3181,         0,         0,       0,     !  101
     &    47,    270,      2418,         0,         0,       0,     !  102
     &    47,    270,      3945,         0,         0,       0,     !  103
     &    46,    270,      2545,         0,         0,       0,     !  104
     &    46,    270,      1934,         0,         0,       0,     !  105
     &    46,    270,      3156,         0,         0,       0,     !  106
     &    45,    270,      1909,         0,         0,       0,     !  107
     &    45,    270,      1451,         0,         0,       0,     !  108
     &    45,    270,      2367,         0,         0,       0,     !  109
     &    44,    270,      1272,         0,         0,       0,     !  110
     &    44,    270,       967,         0,         0,       0,     !  111
     &    44,    270,      1578,         0,         0,       0,     !  112
     &    21,    270,       636,         0,         0,       0,     !  113
     &    21,    270,       484,         0,         0,       0,     !  114
     &    21,    270,       789,         0,         0,       0,     !  115
     &    33,    270,      4772,         0,         0,       0,     !  116
     &    35,    270,      4772,         0,         0,       0,     !  117
     &    34,    270,      4772,         0,         0,       0,     !  118
     &    34,     90,      7500,       200,         8,       0,     !  119
     &    39,     90,      7500,       200,         5,       0,     !  120
     &    39,     90,      2000,         0,         0,       0,     !  121
     &    39,     90,      1300,         0,         0,       0,     !  122
     &     2,     90,      5000,         0,         0,       0,     !  123
     &     2,     90,      5000,       200,   -0.1239,       0,     !  124
     &    21,    270,       500,         0,         0,       0,     !  125
     &    21,    270,       800,         0,         0,       0,     !  126
     &    21,    270,      1277,         0,         0,       0,     !  127
     &    21,    270,      1500,         0,         0,       0,     !  128
     &    21,    270,      2000,         0,         0,       0,     !  129
     &     3,     45,      5000,       200,         5,       0,     !  130
     &     3,     45,     13000,       200,         5,       0,     !  131
     &    44,     90,      1033,       200,         0,       0,     !  132
     &    21,     90,       153,       200,         0,       0,     !  133
     &     3,     90,        75,       200,         0,       0,     !  134
     &     3,    135,      3200,       200,         5,       0,     !  135
     &     3,    135,      6400,       200,         5,       0,     !  136
     &     3,    225,      5000,       200,         5,       0,     !  137
     &     3,    225,     13000,       200,         5,       0,     !  138
     &    13,    270,         0,         0,         0,       0/     !  139
C
C -------------------------------------------------------------------------
      If (ITHOUR .EQ. 0) ITHOUR = YITIM/3600
C
C -- Freeze flight
C
      TCFFLPOS = .TRUE.
C
CD RU100  Determine if a radio aids set up has been activated
C
      If (RUSETNXT) Then
        RUSET = RUSET + 1
        RUSETACT = .TRUE.
        RUSETNXT = .FALSE.
      Endif
C
      If (RUSETPRV) Then
        RUSET = RUSET - 1
        RUSETACT = .TRUE.
        RUSETPRV = .FALSE.
      Endif
C
      If (RUSETACT) Then
C
C -- Check for valid entry
C
        If (RUSET.LT.1 .OR. RUSET.GT.NUMSETUP) Then
          If (RUSET .LT. 1) Then
            RUSET = 1
          Elseif (RUSET .GT. NUMSETUP) Then
            RUSET = NUMSETUP
          Endif
          RUSETACT = .FALSE.
          Return
        Endif
C
C -- Set up new position index and ground speed
C
        If (REPOS) Then
          TAXPOS = POSITION(RUSET)
          VVNS = 0.
          VVEW = 0.
          TCMREPOS = .TRUE.
          REPOS = .FALSE.
          Return
        Endif
C
C -- Set up altitude
C
        RUALTST = ALTITUDE(RUSET)
        RUALTFLG = .TRUE.
C
C -- Set up heading
C
        RUHDGT = HEADING(RUSET)
        RUHDFLG = .TRUE.
C
C -- Set up ground speed
C
        RUVEL = AIRSPEED(RUSET)
C
C -- Set up magnetic variation
C
        RTSETVAR = .TRUE.
        If (MAG_VAR(RUSET) .GT. 180.) Then
          RTAVAR = MAG_VAR(RUSET) - 360.
        Else
          RTAVAR = MAG_VAR(RUSET)
        Endif
C
C -- Advance on track
C
        TARAOT = ADV_TRK (RUSET)
C
C -- Reset setup request
C
        RUSETACT = .FALSE.
        REPOS = .TRUE.
C
      Endif
C
CD RU105  Circle station request
C
      If (RURC) Then
C
C -- Check if radius different
C
        If (RURD .NE. RURDS) Then
C
C -- For new radius reposition a/c north of stn, heading east
C
          If (TAXPOS .NE. RUREPIDX) Then
            If (RXACCESS(11,1) .EQ. 0) RXACCESS(11,1) = -TAXPOS
            Return
          Endif
C
          If (RURD .LT. 2.) RURD = 2.
          RUPLAT = RUREPLAT + RURD/60.
          RUPLON = RUREPLON
          VPSI = 90. * DEG_RAD
          RURDS = RURD
        Endif
C
C -- Calculate heading rate
C
        If (RURD .NE. 0) Then
          RUHDGRT = (RUVEL/RURD) * ITHOUR
        Else
          RURD = 2.
          RUHDGRT = (RUVEL/RURD) * ITHOUR
        Endif
C
C -- Update heading
C
        VPSI = VPSI + RUHDGRT
      Else
        RURDS = 0.
      Endif
C
CD RU110  Reposition at angle to station request
C
      If (RUPOSTN) Then
        If (TAXPOS .NE. RUREPIDX) Then
          If (RXACCESS(11,1) .EQ. 0) RXACCESS(11,1) = -TAXPOS
          Return
        Endif
        VPSI = RUHDGT * DEG_RAD
        RUPLAT = RUREPLAT + (RURD/60.)*COS(RUBRG*DEG_RAD)
        RUPLON = RUREPLON + (RURD/60.)*SIN(RUBRG*DEG_RAD)/
     &                      COS(RUREPLAT*DEG_RAD)
        RUPOSTN = .FALSE.
      Endif
C
CD RU115  Slew on glideslope request
C
      If (RUGSLW .AND. (.NOT.RUINIT)) Then
        If (TAXPOS .NE. RUREPIDX) Then
          If (RXACCESS(11,1) .EQ. 0) RXACCESS(11,1) = -TAXPOS
          Return
        Endif
C
C -- Reposition to glideslope ,store lat/lon
C
        RHDG = (RUREPHDG-180.) * DEG_RAD
        TEMP = (RUREPRLE-RUREPGPX) * FT_DEG
        RUPLAT = RUREPLAT + TEMP*COS(RHDG)
        RUPLON = RUREPLON + TEMP*SIN(RHDG)/COS(RUREPLAT*DEG_RAD)
        RUGPLT = RUPLAT
        RUGPLN = RUPLON
C
C -- Set heading and altitude
C
        VH = 0.
        VPSI = RUREPHDG * DEG_RAD
        RUINIT = .TRUE.
        FRST = .TRUE.
C
C -- Compute altitude
C
      Elseif (RUGSLW) Then
        CALL RANGE(RUGPLT,RUGPLN,RUPLAT,RUPLON,COSLAT,FRST,RURNG)
        VH = RURNG*ATAN(RUREPGPA*DEG_RAD)*NM_FT + RUREPELB
      Else
        RUINIT = .FALSE.
      Endif
C
CD RU120  Set heading request
C
      If (RUHDFLG) Then
        VPSI = RUHDGT * DEG_RAD
        RUHDFLG = .FALSE.
      Endif
C
CD RU125  Set altitude request
C
      If (RUALTFLG) Then
C
C -- Limit altitude to lowest possible value
C
        If (RUALTST .GT. VGRALT) Then
          VH = RUALTST
        Else
          VH = VGRALT
        Endif
        RUALTFLG = .FALSE.
      Endif
C
CD RU130  Output heading
C
      RHDG = VPSI/DEG_RAD
      If (RHDG .GT. 180.) Then
        VPSI = VPSI - 360.*DEG_RAD
      Elseif (RHDG .LT. -180.) Then
        VPSI = VPSI + 360.*DEG_RAD
      Endif
      VPSIDG = VPSI / DEG_RAD
      VCSPSI = COS(VPSI)
      VSNPSI = SIN(VPSI)
C
C -- Zero A/C pitch & roll
C
      VTHETA = 0.
      VTHETADG = 0.
      VSNTHE = 0.
      VCSTHE = 1.
      VPHI = 0.
      VPHIDG = 0.
      VSNPHI = 0.
      VCSPHI = 1.
C
CD RU135  output N/S, E/W velocities
C
      VVNS = RUVEL * VCSPSI * KT_FPS
      VVEW = RUVEL * VSNPSI * KT_FPS
C
C for flying on glideslope reverse components
C
      If (RUGSLW) Then
        VVNS = -VVNS
        VVEW = -VVEW
      Endif
C
CD RU140  output altitudes
C
      VHS = VH + RTHELE
      VHH = VHS
C
      Return
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 02570 RU100  Determine if a radio aids set up has been activated
C$ 02643 RU105  Circle station request
C$ 02681 RU110  Reposition at angle to station request
C$ 02695 RU115  Slew on glideslope request
C$ 02728 RU120  Set heading request
C$ 02735 RU125  Set altitude request
C$ 02749 RU130  Output heading
C$ 02772 RU135  output N/S, E/W velocities
C$ 02784 RU140  output altitudes
 
      SUBROUTINE GEN_POS(POSNUM,PLAT,PLON)
C
C This subroutine determines the latitude and longitude of an on ground
C reposition for the generic scene. This subroutine is used for two different
C visual systems (Maxvue and E&S) depending on vsatyp value.
C
 
      implicit none
C
CP    USD8
CP   -     RUREPLAT,
CP   -     RUREPLON,
CP   -     RUREPHDG,
CP   -     RUREPRLE,
CP   -     RUREPRWE,
C2ULD+++CP   -     VSATYP,
CP   -     VSAGRWYL,
C FLIGHT
CP   -     VPILTXCG,
C IOS
CP   -     TATERM,
CP   -     TARWIDT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  6-May-2013 21:13:18 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RUREPLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RUREPLON       !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
C$
      REAL*4   
     &  RUREPHDG       !  7 TRUE HEADING (DEGREES)             [DEG]
     &, VPILTXCG       ! X CG DIST TO PILOT                      [ft]
     &, VSAGRWYL       ! GENERIC RWY LENGTH                     [FT]
C$
      INTEGER*2
     &  RUREPRLE       !  8 RUNWAY LENGTH (FEET)                [FT]
     &, RUREPRWE       ! 60 RUNWAY END TO THRESHOLD (FEET)      [FT]
     &, TARWIDT        ! GENERIC RUNWAY WIDTH
     &, TATERM         ! TERMINAL BUILDING
C$
      LOGICAL*1
     &  DUM0000001(20012),DUM0000002(24600),DUM0000003(8)
     &, DUM0000004(66),DUM0000005(262178),DUM0000006(3348)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VPILTXCG,DUM0000002,RUREPLAT,RUREPLON,DUM0000003
     &, RUREPHDG,RUREPRLE,DUM0000004,RUREPRWE,DUM0000005,TATERM
     &, TARWIDT,DUM0000006,VSAGRWYL  
C------------------------------------------------------------------------------
C2ULD+++CPI  -     CI99_VIS_ADDITIONALRUNWAY_I2,
C2ULD+++CPI  -     CI99_VIS_GENERICDBTYPE_I2,
C2ULD+++CPI  -     CI99_VIS_LEFTAPRONPOSITION_I2,
C2ULD+++CPI  -     CI99_VIS_RIGHTAPRONPOSITION_I2
C
      Integer*2
     &       VSATYP /10/  !Put as local (1=Maxvue;10=Tropos)
C
      REAL*8 PLON,PLAT
C
      Real*8
     &       GENLAT                !Generic runway reference Lat
     &      ,GENLON                !Generic runway reference Lon
     &      ,DEG_FT
     &      ,DEG_RAD
     &      ,FT_DEG
     &      ,FT_MT
     &      ,FT_NM
     &      ,MT_DEG
C
      Real*4
     &       POS_OFF        (10,5,4)!Position setup data
     &      ,XOF                     ! X offset from the thrshold
     &      ,YOF                     ! Y offset from the thrshold
     &      ,TESTX/0.0/
     &      ,TESTY/0.0/
     &      ,PI
     &      ,GENDIS
     &      ,GENHDG
C
C
CGEN0+
C Small rwy ( 800m to 2000m)
C Long rwy  (2100m to 4500m)
      Real*4     l_gen0_rhold_xl    / 15.0/
      Real*4     l_gen0_hold_xl     / 15.0/
      Real*4     l_gen0_hold_yl     / 98.0/  !long rwy
      Real*4     l_gen0_hold_yls    / 98.0/  !small rwy
      Real*4     l_gen0_taxi_xl     /150.0/
      Real*4     l_gen0_taxi_yl     /217.0/
      Real*4     l_gen0_taxi_yls    /120.0/  !small rwy
      Real*4     l_gen0_gate_xl     /  0.0/
      Real*4     l_gen0_gate_xls    /  0.0/  !small rwy
      Real*4     l_gen0_gate_yl     /315.0/
      Real*4     l_gen0_gate_yls    /302.0/  !small rwy
CGEN0-
C
CWIG0+
C Small rwy ( 800m to 2000m)
C Long rwy  (2100m to 4500m)
      Real*4     l_wig0_rhold_xl    / 15.0/
      Real*4     l_wig0_hold_xl     / 15.0/
      Real*4     l_wig0_hold_yl     / 98.0/  !long rwy
      Real*4     l_wig0_hold_yls    / 98.0/  !small rwy
      Real*4     l_wig0_taxi_xl     /150.0/
      Real*4     l_wig0_taxi_yl     /217.0/
      Real*4     l_wig0_taxi_yls    /120.0/  !small rwy
      Real*4     l_wig0_gate_xl     /  0.0/
      Real*4     l_wig0_gate_xls    /  0.0/  !small rwy
      Real*4     l_wig0_gate_yl     /334.0/
      Real*4     l_wig0_gate_yls    /315.0/  !small rwy
      Real*4     l_wig0_max_gate_yl  /282.5/
CWIG0-
C
      Logical*1
     &       TEST_ACTIVE/.FALSE./
C
      Integer*4
     &       I
     &      ,J
     &      ,POSNUM
     &      ,XR
     &      ,XL
     &      ,YR
     &      ,YL
     &      ,LEFT
     &      ,RIGHT
     &      ,NOTERM
     &      ,TERMINAL              !Generic gate position on visual
     &      ,GATE
     &      ,RHOLD
     &      ,HOLD
     &      ,RAMP
     &      ,TAXI
C
C
C
      Parameter
     &(
     &           DEG_FT = 60.0 * 1852.0 / (12. * 0.0254)
     &          ,DEG_RAD = 3.1415926536 / 180.
     &          ,FT_DEG = (12. * 0.0254) / (60. * 1852.)
     &          ,FT_NM = (12. * 0.0254) / 1852.
     &          ,FT_MT = .3048
     &          ,MT_DEG = 1./(60. * 1852.)
     &          ,PI = 3.1415926536
     &          ,LEFT = 1
     &          ,RIGHT = 2
     &          ,NOTERM = 0
     &          ,GATE = 5
     &          ,RHOLD = 4
     &          ,HOLD = 3
     &          ,RAMP = 2
     &          ,TAXI = 1
     &          ,XL = 1
     &          ,YL = 2
     &          ,XR = 3
     &          ,YR = 4)
C
C Data for MAXVUE visual system generic scene.
C
      Data
     &     ((POS_OFF (1,I,J),J = 1,4),I=1,5)/
C
C             XL      YL      XR      YR        Repos  Desc.
C            (m)      (m)     (m)     (m)       Number
C      -------------------------------------------------------------------
C
     &    785.47,  240.0, 715.37, -240.0      !   1  TAXI
     &,    350.0,    250,  350.0,   -250      !   2  RAMP
     &,     15.0,   90.0,   15.0,  -90.0      !   3  HOLD
     &,     15.0,   90.0,   15.0,  -90.0      !   4  RHOLD
C2UDT+
C     &,   785.47,  282.5, 715.37, -282.5   /  !   5  GATE
     &,   1501.83,  270.7, 715.37, -282.5   /  !   5  GATE
C
C Data for TROPOS visual system generic scene.
C
      Data
     &     ((POS_OFF (10,I,J),J = 1,4),I=1,5)/
C
C             XL      YL      XR      YR        Repos  Desc.
C            (m)      (m)     (m)     (m)       Number
C      -------------------------------------------------------------------
C
     &     150.0,   217.0,  150.0,  -217.0      !   1  TAXI
     &,    350.0,   250,    350.0,  -250        !   2  RAMP
     &,     15.0,    90.0,   15.0,  -90.0       !   3  HOLD
     &,     15.0,    90.0,   15.0,  -90.0       !   4  RHOLD
     &,   1150.0,   300.0, 1725.0,  -300.0   /  !   5  GATE
C2UDT-
C
C Data for E&S visual system generic scene.
C
      Data
     &     ((POS_OFF (2,I,J),J = 1,4),I=1,5)/
C
C             XL      YL      XR      YR        Repos  Desc.
C            (m)      (m)     (m)     (m)       Number
C      -------------------------------------------------------------------
C
C2UDT+
     &       350,  291.5,    350,  -291.5      !   1  TAXI
     &,        0,      0,      0,       0      !   2  RAMP
     &,     12.5,   96.5,   12.5,   -96.5      !   3  HOLD
     &,        0,      0,      0,       0      !   4  RHOLD
     &,    759.5,  448.5,    689,  -448.5   /  !   5  GATE
C2UDT-
C
C    Calculate reference lat, lon for the generic repositions
C
C2UDT+
       GENLAT = RUREPLAT - (RUREPRLE -
     &                      RUREPRWE) * FT_DEG *
     &                      COS (RUREPHDG * DEG_RAD)
       GENLON = RUREPLON - (RUREPRLE -
     &                      RUREPRWE) * FT_DEG *
     &                      SIN (RUREPHDG * DEG_RAD)
     &                    / COS (SNGL (GENLAT) * DEG_RAD)
 
C
C ************************  M  A  X  V  U  E  ************************
C
      IF (VSATYP .EQ. 1) THEN
 
C
C    Set terminal depending on taterm
C
       IF ((TATERM.EQ.1) .OR.
     &     (TATERM.EQ.3) .OR.
     &     (TATERM.EQ.5)) THEN
           TERMINAL = LEFT
       ELSEIF ((TATERM.EQ.2) .OR.
     &         (TATERM.EQ.4) .OR.
     &         (TATERM.EQ.6)) THEN
           TERMINAL = RIGHT
       ELSE
           TERMINAL = NOTERM
       ENDIF
C
        If (TATERM.EQ.3 .OR.
     &      TATERM.EQ.4) Then
                POS_OFF(VSATYP,POSNUM,XL) =
     &          (VSAGRWYL * (3./4.))*FT_MT
C
        Elseif (TATERM.EQ.1 .OR.
     &      TATERM.EQ.2) Then
                POS_OFF(VSATYP,POSNUM,XL) =
     &          (VSAGRWYL * (1./2.))*FT_MT
C
        Elseif (TATERM.EQ.5 .OR.
     &      TATERM.EQ.6) Then
                POS_OFF(VSATYP,POSNUM,XL) =
     &          (VSAGRWYL * (1./4.))*FT_MT
C
        Endif
C       POS_OFF(VSATYP,RAMP,XL) =
C     & POS_OFF(VSATYP,POSNUM,XL)-110
        POS_OFF(VSATYP,GATE,YL) = 282.5 -
     &          VPILTXCG * FT_MT
C
C -- X and Y coordinates of MAXVUE generic scene for reciprocal hold reposition
C
C if runway length is less than 2000 m, there will be one hold position in
C the gen db. It will be located at the center of the runway.
C
       If ((POSNUM.EQ.HOLD)  .OR.
     &     (POSNUM.EQ.RHOLD) .OR.
     &     (POSNUM.EQ.TAXI)) Then
         IF(VSAGRWYL.LT.(1999./FT_MT))THEN
           POS_OFF(VSATYP,POSNUM,XL) =
     &          POS_OFF(VSATYP,POSNUM,XL)
           POS_OFF(VSATYP,POSNUM,YL) =
     &          50.0 + VPILTXCG * FT_MT
           POS_OFF(VSATYP,TAXI,YL) =
     &          120.0 + VPILTXCG * FT_MT
        ELSE
           POS_OFF(VSATYP,RHOLD,XL)  =
     &          (VSAGRWYL*FT_MT - 15.0)
           POS_OFF(VSATYP,HOLD,XL)  =  15.0
           POS_OFF(VSATYP,TAXI,XL) = 150.0
           POS_OFF(VSATYP,POSNUM,YL) =
     &          90.0 + VPILTXCG * FT_MT
           POS_OFF(VSATYP,TAXI,YL) = 201.0
 
         ENDIF
       Endif
C
       POS_OFF(VSATYP,POSNUM,XR) =
     &         POS_OFF(VSATYP,POSNUM,XL)
       POS_OFF(VSATYP,POSNUM,YR) =
     &        -POS_OFF(VSATYP,POSNUM,YL)
C
C -- Generic position offset ( X and Y)
C
C
C  ******************** E N D  O F  M A X V U E ********************
C *********************   T  R  O  P  O  S  ************************
C
      ELSEIF(VSATYP .EQ. 10) THEN
C
C    Set terminal depending on taterm
C
       IF ((TATERM.EQ.1) .OR.
     &     (TATERM.EQ.3) .OR.
     &     (TATERM.EQ.5)) THEN
           TERMINAL = LEFT
       ELSEIF ((TATERM.EQ.2) .OR.
     &        (TATERM.EQ.4)
     &         .OR. (TATERM.EQ.6)) THEN
           TERMINAL = RIGHT
       ELSE
           TERMINAL = NOTERM
       ENDIF
C
C -- GEN0 IS ACTIVE --
        If (TATERM.EQ.3 .OR.
     &      TATERM.EQ.4) Then
                POS_OFF(VSATYP,POSNUM,XL) =
     &          (VSAGRWYL * (3./4.))*FT_MT
C
        Elseif (TATERM.EQ.1 .OR.
     &      TATERM.EQ.2) Then
                POS_OFF(VSATYP,POSNUM,XL) =
     &          (VSAGRWYL * (1./2.))*FT_MT
C
        Elseif (TATERM.EQ.5 .OR.
     &      TATERM.EQ.6) Then
                POS_OFF(VSATYP,POSNUM,XL) =
     &          (VSAGRWYL * (1./4.))*FT_MT
C
        Endif
C
C       POS_OFF(VSATYP,RAMP,XL) =
C     & POS_OFF(VSATYP,POSNUM,XL)-110
C
 
C --  GEN0 --
 
        IF (VSAGRWYL.LT.(1999./FT_MT)) THEN
          POS_OFF(VSATYP,GATE,YL) = l_gen0_gate_yls -
     &          VPILTXCG * FT_MT
        ELSE
          POS_OFF(VSATYP,GATE,YL) = l_gen0_gate_yl -
     &          VPILTXCG * FT_MT
        ENDIF
 
 
C -- X and Y coordinates of MAXVUE generic scene for reciprocal hold reposition
C
C if runway length is less than 2000 m, there will be one hold position in
C the gen db. It will be located at the center of the runway.
C
C -- GEN0 --
 
       If (POSNUM.EQ.HOLD .OR. POSNUM .EQ. RHOLD
     &                    .OR. POSNUM .EQ. TAXI)Then
 
         IF(VSAGRWYL.LT.(1999./FT_MT))THEN
           POS_OFF(VSATYP,POSNUM,XL) =
     &          POS_OFF(VSATYP,POSNUM,XL)
           POS_OFF(VSATYP,POSNUM,YL) =
     &          l_gen0_hold_yls + VPILTXCG * FT_MT
           POS_OFF(VSATYP,TAXI,YL) =
     &          l_gen0_taxi_yls + VPILTXCG * FT_MT
         ELSE
           POS_OFF(VSATYP,RHOLD,XL)  =
     &          (VSAGRWYL*FT_MT - l_gen0_rhold_xl)
           POS_OFF(VSATYP,HOLD,XL) = l_gen0_hold_xl
           POS_OFF(VSATYP,TAXI,XL) = l_gen0_taxi_xl
           POS_OFF(VSATYP,POSNUM,YL) =
     &       l_gen0_hold_yl + VPILTXCG * FT_MT
           POS_OFF(VSATYP,TAXI,YL) = l_gen0_taxi_yl
         ENDIF
       Endif
C
       POS_OFF(VSATYP,POSNUM,XR) =
     &         POS_OFF(VSATYP,POSNUM,XL)
       POS_OFF(VSATYP,POSNUM,YR) =
     &        -POS_OFF(VSATYP,POSNUM,YL)
C
       ENDIF
C
C ******************** E N D  O F  T R O P O S ***********
C
 
       IF (TERMINAL .EQ. LEFT)THEN
 
        XOF = POS_OFF(VSATYP,POSNUM,XL)
        YOF = POS_OFF(VSATYP,POSNUM,YL)
       ELSEIF(TERMINAL .EQ. RIGHT) THEN
        XOF = POS_OFF(VSATYP,POSNUM,XR)
        YOF = POS_OFF(VSATYP,POSNUM,YR)
       ELSE    ! If there is no terminal  do not move the A/C
        XOF = 0.01  !SINCE WE USE Y/X, X SHOULD NOT = 0
        YOF = 0.0
       ENDIF
 
       IF(TEST_ACTIVE)THEN         !to debug offsets
        XOF = TESTX
        YOF = TESTY
       ENDIF
C
C    Update latitude and longitude
C
       IF(VSATYP.EQ.1 .OR. VSATYP.EQ.10)THEN
        GENDIS=(XOF**2+YOF**2)**0.5
        IF(XOF.NE.0.0) GENHDG=RUREPHDG-ATAN(YOF/XOF)*180/PI
       CALL WGS84_DIR(GENLAT,GENLON,PLAT,PLON,GENDIS,GENHDG)
 
      ENDIF
      RETURN
      END
C
C
C
C
 
      SUBROUTINE WGS84_DIR(RLAT,RLON,OLAT,OLON,RNG,BRG)
C
C This subroutine determines the latitude and longitude of a point
C given its range and bearing from a reference.
C (Direct Solution)
C
C E.M. Sodano
C General Non-iterative Solution of the Inverse and Direct Geodetic Problems
C
      implicit none
      real*8 RLON,OLON,RLAT,OLAT,L,a1,m1,lambda
      real*8 pi,B0,f,degtorad,p5Esq,p5Fsq,math
      real*4 G,Beta,sintheta,costheta,sinbeta,cosbeta,RNG,
     &        theta,brgr,cosbrgr,sinbrgr,phi,sinphi,cosphi,J,phi0,
     &        alpha,sinSqbeta,tanbrg2,tanlambda,sinphi0,cosphi0,
     &        tanbeta,sinbeta2,cosbeta2,cosbeta0,BRG
 
 
      PARAMETER
C     *********
C
     &  (PI = 3.141592654
     &,  F = 3.352810664E-03    ! WGS84
     &,  B0 = 6356752.3142      ! WGS84
c     &,  F = 3.367e-03          ! International
c     &,  B0 = 3432.457854*1852. ! International
     &,  p5Fsq = .5*f*f
c     &,  p5Esq = 0.5*.00676817
     &,  p5Esq = 0.5*(2.*f-f**2)
     &,  G = 3.38408e-03
     &,  DEGTORAD = PI/180.
     & )
C
      BRGr = BRG*degtorad
      cosbrgr = cos(brgr)
      sinbrgr = sin(brgr)
C
      phi = rng/B0
      sinphi = sin(phi)
      cosphi = cos(phi)
C
      Beta = atan((1.-f)*tan(rlat*degtorad))
      sinbeta = sin(Beta)
      sinSqbeta = sinbeta*sinbeta
      cosbeta = cos(Beta)
C
      J = cosbeta*cosbrgr
      cosbeta0 = cosbeta*sinbrgr
      m1 = (1.+p5Esq*sinSqbeta)*(1.-cosbeta0**2)
      a1 = (1.+p5Esq*sinSqbeta)*(sinSqbeta*cosphi+J*sinbeta*sinphi)
C
      phi0 = phi + a1*(-p5Esq*sinphi)
     &  + m1*(-0.5*p5Esq*phi + 0.5*p5Esq*sinphi*cosphi)
     &  + a1*a1*(p5Esq*p5esq*(5./2.)*sinphi*cosphi)
C
      sinphi0 = sin(phi0)
      cosphi0 = cos(phi0)
C
      math = J*cosphi-sinbeta*sinphi
      if(math.ne.0.) tanbrg2 = cosbeta0/math
C
      tanlambda = ( sinphi0*sinbrgr/(cosbeta*cosphi0
     &        -sinbeta*sinphi0*cosbrgr))
      lambda = atan(tanlambda)
      if(phi0.gt.0. and. phi0.le.pi) then
        if(BRG.gt.0. and.BRG.le.180. .and.tanlambda.lt.0.)
     &        lambda=pi+lambda
      else if(BRG.gt.180. and.BRG.lt.360.) then
        lambda=lambda-2.*pi
      endif
C
      if(phi0.gt.pi. and. phi0.le.2.*pi) then
        if(BRG.gt.0. and.BRG.le.180.) then
          if( tanlambda.lt.0.) lambda=2*pi+lambda
        else if(BRG.gt.180. and.BRG.lt.360.) then
          lambda=lambda-2.*pi
        endif
      endif
C
      L = lambda + cosbeta0*(-f*phi  + a1*(3.*p5Fsq*sinphi)
     &    + m1*((3./2.)*p5Fsq*phi - (3./2.)*p5Fsq*sinphi*cosphi))
C
      OLON = RLON + L/degtorad
C
      cosbeta2 = sqrt(cosbeta0**2 + (J*cosphi0 - sinbeta*sinphi0)**2)
      if(cosbeta2.ne.0.) then
        sinbeta2 = (sinbeta*cosphi0 + J*sinphi0)
        tanbeta = sinbeta2/cosbeta2
        OLAT = (atan(tanbeta/(1.-f)))/degtorad
      else
        OLAT = 90.
      endif
C
      return
      end
 
