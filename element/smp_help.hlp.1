1 ASSIGN
2 KEY

        The ASSIGN KEY command assigns a command line to a keyboard
special key. The PF1 to PF4 special keys are reserved for predefined
assignment by the program and cannot be reassigned. The other keys (F5
to F20) can be reassigned as often as required and are usually assigned
during program invocation. Depending on the terminal and the computer
being used, some of the special keys may not be available. The command
line must be empty before the special keys can be used in line mode. Use
of double quotes is required for the command line when it includes
spaces and/or slashes.

Format:
  ASSIGN KEY key_name [command_line]

3 key_name

        The ``key_name'' indicates the key being modified.

3 command_line

        The ``command_line'' indicates the command line being assigned
to the key. The assignment is created when a value is provided and
erased when it is omitted. Symbols may be used as part of the command
line.

2 SYMBOL

        The ASSIGN SYMBOL command assigns a full or partial command line
to a symbol or erases a previously assigned symbol. A symbol is assigned
when a command line is specified or is erased when the command line is
omitted. Use of double quotes is required for the symbol name when it
has more than one keyword and for the command line when it includes
spaces and/or slashes.

        These symbols are used to perform the substitution of a command
specification when a command is typed in or is activated by one of the
special keyboard keys. The substitution process is not recursive which
means that it is performed only once per command processing.

Format:
  ASSIGN SYMBOL symbol_name [command_line]

3 symbol_name

        The ``symbol_name'' indicates the symbol being modified.
Assigning an empty value will erase the symbol.

3 command_line

        The ``command_line'' indicates the command line being assigned
to the symbol. The assignment is created when a value is provided and
erased when it is omitted.

1 BACKUP

        The BACKUP command copies all the files related to the current
configuration, including the configuration database, to the specified
directory. All the files in the directory will have the same versions as
those in the configuration. The command will fail if different files with
the same version exist in the specified directory.

Format:
  BACKUP directory_path

2 directory_path

        The ``directory_path'' is a valid directory specification and
indicates where the configuration database and its associated files are
to be written.

1 BUILD

        The BUILD command builds the specified element and its children
that are not currently built. The dependency chains on the children side
are searched for the elements that are not built. The build file of each
of those children is invoked starting from the end of the dependency
chains.

        A valid alteration password for the current configuration must be
effective (can be set using the SESSION command) to allow the use of this
command.

Format:
  BUILD [/qualifier...] element_name

2 element_name

        The ``element_name'' indicates the starting element of the
subtree of the configuration hierarchy to be built.

2 /PURGE[=minimum]

        The PURGE qualifier indicates that after a specified minimum
amount of free disk space is surpassed, all files (within the protected
directory) that are no longer part of a configuration are deleted. The
default minimum is four megabyte.

2 /CONTINUE

        The CONTINUE qualifier indicates that SIMex-PLUS must continue
the build process after failing to build an element. The command will
only process those elements whose children were successfully built. This
allows the command to build as much as possible of the subtree before
terminating. This qualifier is normally used with an active journal file
to allow reviewing of the encountered errors.

1 CHECK

        The CHECK command checks the integrity of the files in the
configurations. For each selected configuration, the existence of the
files associated with the elements is checked, followed by a comparison
(when requested) of the checksum value of each file with the checksum
value of that file at the time of its entry, thus verifying the files'
contents.

Format:
  CHECK [/qualifier] [pattern_config]

2 pattern_config

        The ``pattern_config'' value determines on which configuration
the check is to be performed. If no configuration is specified, the
currently selected configuration in memory will be checked. If no
configuration is selected, all configurations are checked. Wildcard
characters such as `*' can be used to select more than one configuration.

2 /CHECKSUM

        The CHECKSUM qualifier indicates that SIMex-PLUS must compute
the checksum of all the files in the selected configurations and compare
them with the values initially computed when the files were entered. By
default, SIMex-PLUS will not compute the checksum because of the amount
of time that would be required to do so.

1 COMPARE

        The COMPARE command compares elements of the current configuration
with elements of the specified configuration. The elements to be compared
are selected in the same manner as elements are selected for display by
the LIST command. The elements that do not have the same version in the
two configurations and the elements of one configuration that do not
appear in the other configuration are displayed.

        The output is identical to the enumeration section of the LIST
command, except that it is shifted to allow space for the configuration
name.

Format:
  COMPARE [/qualifier...] config_name [pattern_name] [scope_name]

2 config_name

        The ``config_name'' value determines on which configuration the
comparison is to be performed.

2 pattern_name

        The elements compared are a subset of the configuration
restricted to those elements matching the ``pattern_name''. The second
parameter is used as a pattern when there are three parameters or when
the second parameter contains wildcard characters. All of the elements
are compared when the pattern is not specified.

2 scope_name

        The ``scope_name'' indicates the starting point within the
configuration hierarchy of the list of possible elements. The scope
element must appear in both configurations. The second parameter is used
as the scope when there are only two parameters and it does not contain
wildcard characters. The default scope is used when the value is not
specified.

2 /CHILDREN

        The CHILDREN qualifier indicates that the list of possible
elements is taken from the children's side of the scope. The children
of an element are the elements that are required to build the scope
element. This qualifier cannot be used with the PARENTS qualifier and
is the default when neither of them is specified.

2 /PARENTS

        The PARENTS qualifier indicates that the list of possible
elements is taken from the parent's side of the scope. The parents of an
element are the elements that require the scope to be built before they
can be built. This qualifier cannot be used with the CHILDREN qualifier.
The CHILDREN qualifier is the default when neither of them is specified.

2 /IMMEDIATE

        The IMMEDIATE qualifier indicates that the list of possible
elements is composed of only the immediate dependents. This qualifier
cannot be used with the ALL_ELEMENTS or the STRUCTURE qualifiers and
is the default when none of them are specified.

2 /ALL_ELEMENTS

        The ALL_ELEMENTS qualifier indicates that the list of
possible elements is composed of all the elements that are part of
all the dependency chains from the scope (recursive traversal of the
configuration). This option only verifies whether or not the elements of
the configurations are the same. It does not verify that the structures
are the same. This qualifier cannot be used with the IMMEDIATE or the
STRUCTURE qualifiers. The IMMEDIATE qualifier is the default when none
of them are specified.

2 /STRUCTURE

        The STRUCTURE qualifier indicates that the comparison performed
by the IMMEDIATE qualifier must be repeated for all the elements that
are part of the dependency chains in the current configuration and that
also exist in the other configuration. This option verifies that both
the elements and the structures of the configurations are the same.
This qualifier cannot be used with the IMMEDIATE or the ALL_ELEMENTS
qualifiers. The IMMEDIATE qualifier is the default when none of them
are specified.

2 /FULL_INFO

        The FULL_INFO qualifier indicates that all the available
information about the selected elements is to be displayed.

2 /DIFFERENCE_PATH[=directory_path]

        The DIFFERENCE_PATH qualifier indicates that a difference of the
files' contents must be made whenever appropriate. The files that have
been selected must both be text files (as indicated by the Specific
Support Environment) and be of different version numbers. The difference
is displayed on the screen when the directory path is not specified.

1 CONFIGURATION

        The CONFIGURATION command is used to select, modify, rename,
copy or create a configuration. Modifications are made to the resulting
configuration when modification qualifiers are specified.

  o A new configuration is selected when the SET qualifier is used and a
    configuration name is specified. If enabled, the management password
    is requested before any modifications are performed on the current or
    newly selected configuration.

  o A new configuration is created with the specified name when the
    CREATE qualifier is used. The passwords of the new configuration are
    cleared and the requested modifications are made.

  o The currently selected configuration is renamed to the specified
    name when the RENAME qualifier is used. If enabled, the management
    password is requested before the configuration is renamed and the
    modifications are made.

  o The currently selected configuration is copied to the specified name
    when the COPY qualifier is specified. A new configuration is thus
    created. The passwords of the new configuration are cleared and the
    requested modifications are made.

        A list of available configurations is displayed when the
CONFIGURATION command is issued with only the SET qualifier (no other) and
no parameter, or a parameter with wildcard characters. Each item on the
configuration list begins with the configuration name on the left side,
followed by the contract name and the environment type. An optional second
line is displayed when a title is assigned to the configuration. Only the
configuration name is displayed when SIMex-PLUS can not get access to the
configuration database.

Format:
  CONFIGURATION [/qualifier...] [config_name]

Symbols:
  CREATE CONFIGURATION      "CONFIGURATION/CREATE"
  RENAME CONFIGURATION      "CONFIGURATION/RENAME"
  COPY CONFIGURATION        "CONFIGURATION/COPY"
  SET CONFIGURATION         "CONFIGURATION/SET"

2 config_name

        The ``config_name'' value indicates the name of the
configuration being selected (SET) or created (COPY, CREATE), or it
indicates the new name of the current configuration (RENAME). A pattern
(name with wildcard characters) is accepted when only the SET qualifier
is used.

2 /SET

        The SET qualifier indicates that the current configuration is
being modified, a new configuration is being selected and modified, or
a list of the available configurations is to be displayed. If enabled,
the management password will be requested before any modifications are
made. This qualifier cannot be used with the CREATE, COPY, or RENAME
qualifiers and is the default when none of them are specified.

2 /CREATE

        The CREATE qualifier indicates that a new configuration is being
created. The ``config_name'' value must be given when this qualifier is
used. The command will fail if the configuration already exists. The
created configuration will also become the current one. This qualifier
cannot be used with SET, COPY, or RENAME qualifiers. The SET qualifier
is the default when none of them are specified.

2 /RENAME

        The RENAME qualifier renames the current configuration. The
``config_name'' value must be given when this qualifier is used. The
command will fail if a configuration already exists with the specified
name. If enabled, the management password will be requested. This
qualifier cannot be used with SET, COPY, or RENAME qualifiers. The SET
qualifier is the default when none of them are specified.

2 /COPY

        The COPY qualifier creates a duplicate of the current
configuration. The ``config_name'' value must be given when this
qualifier is used. The command will fail if a configuration already
exists with the specified name. The passwords are cleared in the new
configuration. The new configuration also becomes the current one. This
qualifier cannot be used with SET, COPY, or RENAME qualifiers. The SET
qualifier is the default when none of them are specified.

2 /MANAGEMENT

        The MANAGEMENT qualifier indicates that the management password
is being changed. SIMex-PLUS will ask for the new password. A null
password clears the existing password. The management password is
used to prevent unauthorized users from modifying the configuration
parameters and from using commands which have an important impact on
a configuration.

2 /ALTERATION

        The ALTERATION qualifier indicates that the alteration password
is being changed. SIMex-PLUS will ask for the new password. A null
password clears the existing password. The alteration password is used
to prevent unauthorized users from updating elements in a configuration.

2 /OPERATION

        The OPERATION qualifier indicates that the operation password is
being changed. SIMex-PLUS will ask for the new password. A null password
clears the existing password. The operation password is used to prevent
unauthorized users from operating the simulator.

2 /CONTRACT[=contract_name]

        The CONTRACT qualifier indicates that the contract name is being
changed. The value given to the qualifier is assigned to the selected
configuration. The contract name of the configuration is cleared when no
value is given. The contract name is used as substitution value for `$'
in element name specification.

2 /TITLE[=title_line]

        The TITLE qualifier indicates that the title of the configuration
is being changed. The value given to the qualifier is assigned to the
selected configuration. The title line of the configuration is cleared
when no value is given. The title is displayed when the configurations
are listed.

2 /ENVIRONMENT[=environment_type]

        The ENVIRONMENT qualifier indicates that the environment type
is being changed. The value given to the qualifier is assigned to the
selected configuration. The environment type of the configuration is
cleared when no value is given. The environment type is used in special
environments (SSE) that require work to be done each time a
configuration is manipulated as a whole.

2 /DIFFERENCE_PATH[=directory_path]

        The DIFFERENCE_PATH qualifier indicates that the difference
option and path of the configuration are being changed. The
``directory_path'' value is saved and the option enabled when a value
is given to the qualifier. The previous path is cleared and the option
disabled when no value is given to the qualifier.

1 COPY
2 CONFIGURATION

See CONFIGURATION/COPY

2 ELEMENT

        The COPY ELEMENT command copies an element and optionally its
children from a configuration to the current one. All the information
relating to an element except its entries in the configuration history
are copied to the current configuration. However, the newly assigned
enter, build and load files will refer to the elements of the current
configuration which may not have the the same content as those in the
other configuration. If enabled, the management password will be
requested. When the ALL_ELEMENTS qualifier is used, the management
password will be requested, providing that it is enabled. When the
ALL_ELEMENTS qualifier is not used, the alteration password will be
requested, providing that it is enabled.

        Some verifications are made before the copy is performed. The
command always ensures that the referenced enter, build and load files
are present in the current configuration. When the copy of a single
element is requested, the command will also ensure that the children
of the elements are built and that they have the same version numbers
in both configurations. However, when the copy of an element and its
children is requested, the command will only ensure that the subtree of
the current configuration does not include elements and/or dependencies
that are not present in the other configuration. Missing children and
dependencies are automatically added before the elements are copied.

Format:
  COPY ELEMENT [/qualifier] config_name element_name

3 config_name

        The ``config_name'' indicates from which configuration the
element is being copied.

3 element_name

        The ``element_name'' value determines which element is to be
copied to the current configuration. The element must already exist in
both configurations.

3 /ALL_ELEMENTS

        The ALL_ELEMENTS qualifier indicates that the children must
also be copied.

1 CREATE
2 CONFIGURATION

See CONFIGURATION/CREATE

1 DEFINE

        The DEFINE command creates a new element, modifies the SSE
(Specific Support Environment) component assigned to an existing one,
and/or adds a new dependency to the configuration. A new dependency is
added to the configuration when the parent value is specified. A valid
alteration password for the current configuration must be effective (can
be set using the SESSION command) to allow the use of this command.

Format:
  DEFINE [/qualifier...] element_name [parent_name]

2 element_name

        The ``element_name'' indicates the element that is to be created
or modified. A new element is created if the element specified does not
currently appear in the configuration, in which case a parent value is
required.

2 parent_name

        The ``parent_name'' indicates the new parent of the element when
a new dependency is being added to the configuration. The parent must
exist in the configuration and is invalidated (marked as not built).
This value is required when an element is to be created because an
element cannot exist in a configuration without having at least one
parent (except for ROOT).

2 /ENTER_FILE[=element_name]

        The ENTER_FILE qualifier indicates that the SSE (Specific
Support Environment) component defined as the enter file for the element
is being changed. The value specified to the qualifier must be an
existing element of the configuration. The enter file becomes undefined
when no value is specified to the qualifier.

        The file of an element that does not have an enter file cannot
be directly entered or extracted using the simple form of the ENTER or
GET commands.

2 /BUILD_FILE[=element_name]

        The BUILD_FILE qualifier indicates that the SSE (Specific
Support Environment) component defined as the build file for the element
is being changed. The value specified to the qualifier must be an
existing element of the configuration. The build file becomes undefined
when no value is specified to the qualifier.

        During a ``BUILD'' operation, an element that does not have a
build file is automatically updated (marked as built) by SIMex-PLUS
without being processed. It might still be updated later by another
related element's build file.

2 /LOAD_FILE[=element_name]

        The LOAD_FILE qualifier indicates that the SSE (Specific Support
Environment) component defined as the load file for the element is
being changed. The value specified to the qualifier must be an existing
element of the configuration. The load file becomes undefined when no
value is specified to the qualifier.

        The file of an element having no load file is either unloadable
or is loaded at the same time as another element by the load file of
that element.

2 /SUPPORT_CODE[=code_value]

        The SUPPORT_CODE qualifier indicates that the SSE (Specific
Support Environment) code kept in the configuration is being changed.
The new value replaces the old one and spaces are used when no value is
specified. This is a temporary modification and is in effect until a
new file is assigned to the element. The ``code_value'' is the SSE two
character string value to be assigned to the element. The meaning of
this string depends on the environment being used.

1 DELETE

        The DELETE command deletes the current configuration. The
configuration database and the history file are, in fact, temporarly
renamed to allow for possible recovery. They will be deleted along with
the data files stored outside the database by the PURGE command. If
enabled, the management password will be requested.

Format:
  DELETE [/CONFIRM]
  DELETE /NOCONFIRM

2 /CONFIRM

        The CONFIRM qualifier indicates that the user must be prompted
for confirmation before the operation is performed.

1 DIFFERENCE

        The DIFFERENCE command displays the differences between the file
associated with an element and a user file. The output of this command is
operating system-dependent.

Format:
  DIFFERENCE [/qualifier] element_name [file_name]

2 element_name

        The ``element_name'' indicates the element being manipulated.

2 file_name

        The ``file_name'' indicates the user file that must be used
for comparison. A file with the same name as the element name and from
the default directory is used when this parameter is not specified. A
partial file specification will be automatically completed as much as
possible using the element name and the default directory.

2 /CHECK_TYPE

        The CHECK_TYPE qualifier guarantees that only text files (as
indicated by the Specific Support Environment) will be examined. The
NOCHECK_TYPE form must be specified to override the normal check.

1 DIRECTORY

        The DIRECTORY command changes the default directory of the current
process. On some operating systems, the change remains effective when
SIMex-PLUS is exited.

Format:
  DIRECTORY directory_path

Symbols:
  SET DIRECTORY             "DIRECTORY"

2 directory_path

        The ``directory_path'' value is a valid directory specification.

1 ENTER

        The ENTER command enters one or more files into the configuration.
The elements are created if they do not exist or are updated if they do. A
valid alteration password for the current configuration must be effective
(can be set using the SESSION command) to allow the use of this command.

Format:
  ENTER [/qualifier] file_name [parent_name]

2 file_name

        The ``file_name'' indicates which file is being be entered. The
enter file that is used may request other files to be entered at the
same time.

2 parent_name

        The ``parent_name'' indicates the element to use as parent when
new elements must be added to the configuration. It is required when the
element represented by the file name is not part of the configuration but
is illegal when the element is. This parameter is not used when existing
elements are just being modified. The current scope is used when new
elements are added and this parameter is not specified.

2 /ENTER_FILE[=element_name]

        The ENTER_FILE qualifier indicates which enter file to use to
enter the specified ``file_name'' value. The specified enter file is
used when a value is given to the qualifier. A predefined minimal enter
file is used when the qualifier is specified without a value.

        The files to be established and their dependency relations are
defined by an SSE component called an enter file. The selection of the
enter file to use depends on the ``ENTER_FILE'' qualifier and the
existence of the element represented by the filename. SIMex-PLUS does
the following steps in selecting an enter file:

        As a first step, SIMex-PLUS checks if the ``ENTER_FILE''
qualifier is specified. If an element is assigned to the qualifier,
it is used. Otherwise, a predefined minimal enter file is used. Next,
SIMex-PLUS checks the existence of the element represented by the file.
If the element has an enter file assigned to it, it is used. Otherwise,
the command fails.

        If the ``ENTER_FILE'' qualifier is not used and the element
represented by the file does not exist, then SIMex-PLUS will try
to build an element name from the extension of the filename and
``_ENT.COM''. If this fails, SIMex-PLUS will try to build an element
name with the configuration's environment type and ``_ENT.COM''. If even
this fails, SIMex-PLUS will try to build an element name with SSE and
``_ENT.COM''. Otherwise the command fails.

1 EXECUTE

        The EXECUTE command invokes a SIMex-PLUS command file. The user
name and any required passwords must be set using the SESSION command
before any alteration or operation command can appear in the file.

Format:
  EXECUTE execute_file

2 execute_file

        The ``execute_file'' value must be a valid file specification.

1 EXIT

        The EXIT command terminates the execution of SIMex-PLUS.

Format:
  EXIT

1 EXPORT

        The EXPORT command creates an external build description file.
This file describes the information and the sequence of operations
required to allow building of the specified element and its children,
without the use of SIMex-PLUS.

        The external build description file is created in the default
directory, and has a filename created from the element name and a
system-dependent extension. In this file, there is a group of information
for each element that has a build file. Each one of these group starts
with the file specification of the build file and the identification of
the element being built. It is followed by the result of the build file
header parsing operation and is terminated by an empty line.

Format:
  EXPORT [element_name]

2 element_name

        The ``element_name'' indicates the starting element of the
subtree of the configuration hierarchy to be exported. The default scope
is used when the parameter is not specified.

1 FREEZE

        The FREEZE command invokes the load file associated with the
specified element to perform a FREEZE operation. The load file of the ROOT
element is invoked when the parameter is not specified. A valid operation
password for the current configuration must be effective (can be set using
the SESSION command) to allow the use of this command.

Format:
  FREEZE [element_name]

2 element_name

        The ``element_name'' parameter indicates the specified element.
The load file of the ROOT element is invoked when this parameter is not
specified.

1 GET

        The GET command copies one or more files from the configuration to
the default directory.

Format:
  GET [/qualifier] element_name

2 element_name

        The ``element_name'' indicates which file is to be extracted.
The enter file that is used may request other files to be extracted at
the same time.

2 /ENTER_FILE[=element_name]

        The ENTER_FILE qualifier indicates which enter file to use to
extract the file of the specified element. The specified enter file is
used when a value is given to the qualifier. A predefined minimal enter
file is used when the qualifier is specified without a value. The enter
file associated with the element is used when the qualifier is not
specified.

1 HELP

        The HELP command displays some information on the purpose, syntax,
and use of a SIMex-PLUS command. Additional information can be obtained by
indicating a command name, parameter name, or qualifier name to the
``Topics ?'' prompt of the HELP command.

Format:
  HELP [command_name]

2 command_name

        The ``command_name'' value indicates the command on which
information is requested.

1 HISTORY

        The HISTORY command displays some information from the history
file of the current configuration, or from the global history file.
Specific information can be selected from the file using the qualifiers
and the parameter.

        The information from the current configuration is displayed as
a series of user sessions, each consisting of a user identification, a
group of changes and finally a user comment line. The sessions are
displayed in the reverse order in which they occurred.

        The information from the global history file is displayed in
the reverse order in which it was added. It shows when configurations
were manipulated as a whole.

Format:
  HISTORY [/qualifier...] [pattern_name]

Symbols:
  SHOW HISTORY              "HISTORY"

2 pattern_name

        The ``pattern_name'' indicates the element for which history
information will be selected. The pattern value is matched against
the element name portion of the file names in the history file. This
parameter is illegal when the MANAGEMENT qualifier is specified.

2 /ALTERATION

        The ALTERATION qualifier indicates that the information related
to the alteration of the configuration will be selected. This qualifier
is selected by default but is ignored when the MANAGEMENT qualifier is
specified.

2 /OPERATION

        The OPERATION qualifier indicates that the information related
to the operation of the simulator will be selected. This qualifier is
not selected by default and is ignored when the MANAGEMENT qualifier is
specified.

2 /MANAGEMENT

        The MANAGEMENT qualifier indicates that the information
from the global history file will be selected. When specified, this
qualifier overrides the ALTERATION and OPERATION qualifiers.

2 /SINCE[=date_value]

        The SINCE qualifier indicates that the information must be
selected according to the date on which it was written to the history
file. The current date is used when no value is specified. The
``date_value'' must be specified according to the international format
``YYYY-MMM-DD''.

        The following shows acceptable incomplete forms for a date.
The `#' character represents a digit and the `@' character represents
an upper or lower case letter.

"####"          The first day of the specified year.
"####-"         The current month and day of the specified year.
"####-@@@"      The first day of the specified year and month.
"####-@@@-"     The current day of the specified year and month.
"####-@@@-##"   The specified day, month and year.
"@@@"           The current year and the first day of the month.
"@@@-"          The current day and year of the specified month.
"@@@-##"        The current year and the specified month and day.
"##"            The current month and year of the specified day.

2 /USER_NAME[=user_name]

        The USER_NAME qualifier indicates that the information must
be selected according to the user name that produced the history. The
current user name is used when no value is given to the qualifier.

1 INVALIDATE

        The INVALIDATE command invalidates (marks as `not built') the
specified element and its parents. The dependency chains on the parent
side are traversed during the invalidation process. The BUILD or ENTER
command must be used to update all the invalidated elements. If enabled,
the management password will be requested. When the ALL_ELEMENTS
qualifier is used, the management password will be requested, providing
that it is enabled. When the ALL_ELEMENTS qualifier is not used, the
alteration password will be requested, providing that it is enabled.

Format:
INVALIDATE [/qualifier] [element_name]

2 element_name

        The ``element_name'' indicates the starting element of the
subtree of the configuration hierarchy to be invalidated. It and all
of its parents will be invalidated. The default scope is used when the
parameter is not specified.

2 /ALL_ELEMENTS

        The ALL_ELEMENTS qualifier indicates that the list of elements
to invalidate is composed of all children of the starting element and
their parents. Only the starting element and its parents are invalidated
when the qualifier is not specified.

1 INVOKE

        The INVOKE command invokes an SSE component that is part of
the configuration. This command allows direct activation of programs or
utilities from SIMex-PLUS. Depending on the component being invoked, the
first value can be an element name which is converted to its associated
file name.

Format:
  INVOKE [/qualifier] element_name [value [value [value [value] ] ] ]

2 element_name

        The ``element_name'' indicates the element whose associated file
(an SSE component) is being invoked.

2 value

        The ``value'' indicates a user information to be passed to the
component being invoked. Up to four values can be specified. The first
value is converted to filename when it is recognized as an existing
element with an associated file.

2 /BATCH

        The BATCH qualifier indicates that the component does not
require user interaction. Its output is displayed on the screen the same
way that the output of internal commands are. This qualifier cannot be
used with the INTERACTIVE qualifier and is the default when neither of
them is specified.

2 /INTERACTIVE

        The INTERACTIVE qualifier indicates that the component is
associated with an interactive utility. The screen is cleared before the
component is invoked and is refreshed when the execution terminates.
This qualifier cannot be used with the BATCH qualifier. The BATCH
qualifier is the default when neither of them is specified.

1 JOURNAL

        The JOURNAL command controls the recording in a file of all output
being produced by SIMex-PLUS. The recording can be started, terminated,
suspended, resumed, or displayed. Only one qualifier can be specified on
the command line.

Format:
  JOURNAL /qualifier [journal_file]

Symbols:
  SET JOURNAL               "JOURNAL"
  SHOW JOURNAL              "JOURNAL/SHOW"

2 journal_file

        The ``journal_file'' value is a valid file specification which
gives the name of the file to be used for journaling. The journaling is
automatically resumed when the file is opened. A previous journal file
is automatically closed when the new file is opened. This parameter is
required for the OPEN and the APPEND qualifiers and is forbidden with
the others.

2 /OPEN

        The OPEN qualifier specifies that a new journal file be created
with the given name. The journaling is automatically resumed.

2 /APPEND

        The APPEND qualifier specifies that new journal information
be written at the end of an old journal file. The journaling is
automatically resumed.

2 /CLOSE

        The CLOSE qualifier specifies that the journaling be suspended
and the file closed.

2 /RESUME

        The RESUME qualifier specifies that the journaling be resumed.
New information will be written to the file.

2 /SUSPEND

        The SUSPEND qualifier specifies that the journaling be
suspended. It can be resumed with the RESUME, OPEN, or APPEND
qualifiers.

2 /SHOW

        The SHOW qualifier specifies that the journal be displayed on
the screen. The journaling, if active, is suspended for the duration of
the JOURNAL command.

1 LINE

        The LINE command selects the line driven command input mode. It is
applicable only when in menu-driven command input mode.

Format:
  LINE

1 LIST

        The LIST command displays information about the elements of a
configuration. The output is subdivided in two sections. The first one is
the title section describing the selected scope element and the other one
is the enumeration section describing its parents or children. The format
of the title section is the same as the enumeration section when the
``/ALL_ELEMENTS'' qualifier is specified.

        The first line of the title section shows the element name with a
version number when there is an associated file, the time and user name
of the last update, and an indication of the element state (built or not).
The next lines show the comment associated with the element when it exists
and its origin file name (the source of the file before its entry into the
configuration) when it exists and the ``/FULL_INFO'' qualifier is used.
Finally comes an optional line for each of the element's enter file, build
file, load file, and support code when it exists and the ``/FULL_INFO''
qualifier is used.

        Each description of elements in the enumeration section is
composed of up to four lines. The first line provides the same information
as the first line in the title section. The other lines are displayed when
the ``/FULL_INFO'' qualifier is used. The second line shows the element's
enter file, build file, load file, and support code. The next line shows
the origin file name (the source of the file before its entry into the
configuration) when it exists. And the last line shows the comment
associated with the element when it exists.

Format:
  LIST [/qualifier...] [pattern_name] [scope_name]

2 pattern_name

        The elements displayed are a subset of the configuration
restricted to those elements matching the ``pattern_name''. The first
parameter is used as a pattern when there are two parameters or when the
first parameter contains wildcard characters. All of the elements are
listed when the pattern is not specified.

2 scope_name

        The ``scope_name'' indicates the starting point within the
configuration hierarchy of the list of possible elements. The first
parameter is used as the scope when there is only one parameter and it
does not contain wildcard characters. The default scope is used when the
value is not specified.

2 /CONFIGURATION[=config_name]

        The CONFIGURATION qualifier indicates from which configuration
the elements should be listed. The current configuration is used when
the qualifier is not specified or when no value is assigned to the
qualifier.

2 /CHILDREN

        The CHILDREN qualifier indicates that the list of possible
elements is taken from the children side of the scope. The children of
a scope element are the elements that are required to build the scope
element. This qualifier cannot be used with the PARENTS qualifier and is
the default when neither of them is specified.

2 /PARENTS

        The PARENTS qualifier indicates that the list of possible
elements is taken from the parent side of the scope. The parents of a
scope element are the elements that require the scope to be built before
they can be built. This qualifier cannot be used with the CHILDREN
qualifier. The CHILDREN qualifier is the default when neither of them
is specified.

2 /ALL_ELEMENTS

        The ALL_ELEMENTS qualifier indicates that the list of possible
elements is composed of all the elements that are part of all the
dependency chains from the scope (a recursive traversal of the
configuration). Only the immediate dependents are possible when the
qualifier is not specified.

2 /FULL_INFO

        The FULL_INFO qualifier indicates that all the available
information about the selected elements is to be displayed.

1 LOAD

        The LOAD command invokes the load file associated with the
specified element to perform a LOAD operation. The load file of the ROOT
element is invoked when the parameter is not specified. A valid operation
password for the current configuration must be effective (can be set using
the SESSION command) to allow the use of this command.

Format:
  LOAD [element_name]

2 element_name

        The ``element_name'' parameter indicates the specified element.
The load file of the ROOT element is invoked when this parameter is not
specified.

1 MENU

        The MENU command selects the menu driven command input mode. This
command is only applicable when in line-driven command input mode and
SIMex-PLUS is operating in full screen mode. Otherwise, the command has no
effect.

Format:
  MENU

1 PURGE

        The PURGE command deletes all the files (within the protected
directory) that are no longer part of a configuration. The files that
are associated with elements that are not built are considered part of
a configuration. As well, the global file from the support subdirectory
is invoked, if present, to perform some environment related cleanup.

Format:
  PURGE [/qualifier]

2 /DELETE

        The DELETE qualifier indicates that the files selected by the
PURGE command must be deleted. This qualifier cannot be used with the
CONFIRM or the DISPLAY qualifiers and is the default when none of them
are specified.

2 /CONFIRM

        The CONFIRM qualifier indicates that the user should be prompted
for confirmation before each selected file is deleted. However,
answering ``ALL'' to the confirmation prompt will make the command act
as if the DELETE qualifier was used. This qualifier cannot be used with
the DELETE or the DISPLAY qualifiers. The DELETE qualifier is the
default when none of them are specified.

2 /DISPLAY

        The DISPLAY qualifier indicates that the files selected by the
PURGE command must be displayed instead of deleted. This qualifier
cannot be used with the DELETE or the CONFIRM qualifiers. The DELETE
qualifier is the default when none of them are specified.

1 QUIT

        The QUIT command terminates the execution of SIMex-PLUS.

Format:
  QUIT

1 REMOVE

        The REMOVE command removes one or more elements, or a single
dependency from the configuration. A valid alteration password for the
current configuration must be effective (and can be set using the SESSION
command) to allow the use of this command.

Format:
  REMOVE [/DEPENDENCY] element_name [parent_name]
  REMOVE /ELEMENT element_name

2 element_name

        The ``element_name'' indicates the element being removed or
modified. A modified element is deleted from the configuration when
the dependency to its last parent is removed and it does not have any
children.

2 parent_name

        The ``parent_name'' indicates the parent element that is no
longer a parent of the specified element when a single dependency
is being removed. The default scope is used when the value is not
specified. The parent is invalidated (marked as not built).

2 /DEPENDENCY

        The DEPENDENCY qualifier indicates that a single dependency is
being removed from the configuration. The modified element is deleted
from the configuration when the dependency to its last parent is removed
and it does not have any children. The parent is invalidated (marked as
not built). This qualifier cannot be used with the ELEMENT qualifier and
is the default when neither of them is specified.

2 /ELEMENT

        The ELEMENT qualifier indicates that one or more elements are
being removed from the configuration. The remaining parents of the
removed element are invalidated (marked as not built). This qualifier
cannot be used with the DEPENDENCY qualifier. The DEPENDENCY qualifier
is the default when neither of them is specified.

1 RENAME
2 CONFIGURATION

See CONFIGURATION/RENAME

1 RESTORE

        The RESTORE command restores the specified configuration that was
saved using the BACKUP command. The configuration database is copied from
the specified directory to the protected one along with the files that are
part of that configuration.

Format:
  RESTORE directory_path config_name

2 directory_path

        The ``directory_path'' is a valid directory specification and
indicates where the configuration database and its associated files are.

2 config_name

        The ``config_name'' value indicates which configuration is to be
restored.

1 RUN

        The RUN command invokes the load file associated with the
specified element to perform a RUN operation. The load file of the ROOT
element is invoked when the parameter is not specified. A valid operation
password for the current configuration must be effective (can be set using
the SESSION command) to allow the use of this command.

Format:
  RUN [element_name]

2 element_name

        The ``element_name'' parameter indicates the specified element.
The load file of the ROOT element is invoked when this parameter is not
specified.

1 SCOPE

        The SCOPE command changes the default value for the scope_name or
element_name parameter used by most commands requiring a scope.

Format:
  SCOPE scope_name

Symbols:
  SET SCOPE                 "SCOPE"

2 scope_name

        The ``scope_name'' value indicates the element to be used as the
default scope for the current configuration.

1 SEARCH

        The SEARCH command searches a file or set of files for a pattern
and displays the lines containing that pattern.

Format:
  SEARCH [/qualifier...] pattern_text [pattern_name] [scope_name]

2 pattern_text

        The ``pattern_text'' indicates the pattern to search for in the
selected files. Simple wild card characters such as `*' and `?' can be
used in the pattern and the search is not case sensitive.

2 pattern_name

        The elements searched are a subset of the configuration
restricted to those elements matching the ``pattern_name''. The second
parameter is used as a pattern when there are three parameters or when
the second parameter contains wildcard characters. All of the elements
are searched when the pattern is not specified. This parameter cannot be
specified when the SCOPE qualifier is present.

2 scope_name

        The ``scope_name'' indicates the starting point within the
configuration hierarchy of the list of possible elements. The second
parameter is used as the scope when there are only two parameters and it
does not contain wildcard characters. The default scope is used when the
value is not specified.

2 /CHILDREN

        The CHILDREN qualifier indicates that the list of possible
elements is taken from the children side of the scope. The children of
a scope element are the elements that are required to build the scope
element. This qualifier cannot be used with the PARENTS or the SCOPE
qualifiers and is the default when none of them are specified.

2 /PARENTS

        The PARENTS qualifier indicates that the list of possible
elements is taken from the parent side of the scope. The parents of a
scope element are the elements that require the scope to be built before
they can be built. This qualifier cannot be used with the CHILDREN or
the SCOPE qualifiers. The CHILDREN qualifier is the default when none of
them are specified.

2 /SCOPE

        The SCOPE qualifier indicates that the list of possible elements
is limited to the scope itself. This qualifier cannot be used with the
CHILDREN or the PARENTS qualifiers. The CHILDREN qualifier is the
default when none of them are specified.

2 /ALL_ELEMENTS

        The ALL_ELEMENTS qualifier indicates that the list of possible
elements is composed of all the elements that are part of all the
dependency chains from the scope (a recursive traversal of the
configuration). Only the immediate dependents are possible when the
qualifier is not specified and there is no effect when the SCOPE
qualifier is used.

2 /CONFIRM

        The CONFIRM qualifier indicates that the user must be prompted
for confirmation before the file search is performed.

2 /CHECK_TYPE

        The CHECK_TYPE qualifier guarantees that only text files (as
indicated by the Specific Support Environment) will be searched. The
NOCHECK_TYPE form must be specified to override the normal check.

1 SESSION

        The SESSION command allows the user to define some characteristics
of the current session. This includes the user identification, the
passwords, and a comment for the history file when in batch mode. All
session information is cleared and the history is completed when the
SESSION command is used without any qualifiers or parameter.

Format:
  SESSION [/qualifier...] [user_name]

Symbols:
  SET SESSION               "SESSION"

2 user_name

        The ``user_name'' value is the identification of the current
user of SIMex-PLUS.

2 /ALTERATION

        The ALTERATION qualifier indicates that the alteration password
is being assigned for this session. SIMex-PLUS will ask for a password
which must match the one assigned to the current configuration. A null
password always clears the previously assigned value, which is also
cleared when the configuration is changed.

2 /OPERATION

        The OPERATION qualifier indicates that the operation password is
being assigned for this session. SIMex-PLUS will ask for a password
which must match the one assigned to the current configuration. A null
password always clears the previously assigned value, which is also
cleared when the configuration is changed.

2 /COMMENT_LINE[=comment_value]

        The COMMENT_LINE qualifier indicates that the internal comment
value is being changed. It is cleared or replaced by the given value.
The comment value is used to complete the history produced during the
invocation of a SIMex-PLUS command file by the EXECUTE command. Note
that a legal comment value must be at least 15 characters in length
and must contain at least 3 words.

1 SET
2 CONFIGURATION

See CONFIGURATION/SET

2 DIRECTORY

See DIRECTORY

2 JOURNAL

See JOURNAL

2 SCOPE

See SCOPE

2 SESSION

See SESSION

2 VERIFY

See VERIFY

1 SHOW
2 COMMAND

        The SHOW COMMAND command displays the available commands with a
brief description for each. Double quotes must be used when the command
name has more than one keyword. All matching commands are displayed when
an abbreviation is detected.

Format:
  SHOW COMMAND [command_name]

3 command_name

        The ``command_name'' value indicates the command on which
information is requested. Abbreviations can be used, in which case,
all matching commands are displayed. Double quotes must be used when
more than one keyword is specified.

2 CONFIGURATION

        A list of available configurations is displayed when the SHOW
CONFIGURATION command is issued. A specific configuration can be
referred to by giving a parameter value.

        Each item on the configuration list begins with the
configuration name on the left side, followed by the contract name and
the environment type. An optional second line is displayed when a title
is assigned to the configuration. Only the configuration name is
displayed when SIMex-PLUS can not get access to the configuration
database.

Format:
  SHOW CONFIGURATION [pattern_config]

3 pattern_config

        The ``pattern_config'' value indicates which of the available
configurations should be displayed. All configurations are displayed
when this parameter is omitted. The special `*' wildcard character can
be used to display a subset of the available configuration.

2 ELEMENT

        The SHOW ELEMENT command displays the elements of the
configuration in alphabetical order.

Format:
  SHOW ELEMENT [/qualifier...] [pattern_name]

3 pattern_name

        The elements displayed are a subset of the configuration
restricted to those elements matching the ``pattern_name''. All of the
elements are listed when the pattern is not specified.

3 /FULL_INFO

        The FULL_INFO qualifier indicates that all the available
information about the selected elements is to be displayed.

3 /FILES_ONLY

        The FILES_ONLY qualifier indicates that only elements with
associated files are to be displayed.

2 HISTORY

See HISTORY

2 JOURNAL

See JOURNAL/SHOW

2 KEY

        The SHOW KEY command displays the value currently assigned to
one or all special keyboard keys. The keys are named PF1 to PF4, and F5
to F20.

Format:
  SHOW KEY [key_name]

3 key_name

        The ``key_name'' parameter is optional. When omitted, the
values of all the symbols are displayed. When entered, only the value
of the typed symbol is displayed. The keys are named PF1 to PF4, and
F5 to F20.

2 STATUS

See STATUS

2 STRUCTURE

        The SHOW STRUCTURE command displays the dependency structure
of the configuration. In the output, a `*' in front of an element name
means that the dependency structure of this element has already been
displayed. Duplication of display only occurs when the /REPEAT qualifier
is used.

Format:
  SHOW STRUCTURE [/qualifier...] [element_name]

3 element_name

        The ``element_name'' parameter indicates the starting point
for displaying the dependency structure of the configuration. The
default scope is used when this parameter is not specified.

3 /CHILDREN

        The CHILDREN qualifier indicates that the list of possible
elements is taken from the children side of the scope. The children of
a scope element are the elements that are required to build the scope
element. This qualifier cannot be used with the PARENTS qualifier and
is the default when neither of them is specified.

3 /PARENTS

        The PARENTS qualifier indicates that the list of possible
elements is taken from the parent side of the scope. The parents of
a scope element are the elements that require the scope to be built
before they can be built. This qualifier cannot be used with the
CHILDREN qualifier. The CHILDREN qualifier is the default when neither
of them is specified.

3 /REPEAT

        The REPEAT qualifier indicates that all duplicated structures
are to be displayed fully.

2 SYMBOL

        The SHOW SYMBOL command displays the value currently assigned
to a symbol or all symbols. Symbols are used as aliases for SIMex-PLUS
commands and are checked by the user interface. Each symbol can be
specified using multiple keywords, in which case, double quotes must be
used. The command will display all matching symbols when an abbreviation
is detected.

Format:
  SHOW SYMBOL [symbol_name]

3 symbol_name

        The ``symbol_name'' parameter is optional. When omitted, the
values of all the symbols are displayed. When entered, only the value
of the typed symbol is displayed.

1 SPAWN

        The SPAWN command creates a subprocess to execute a specific
system command or to provide access to a system command interpreter.
Double quotes must be used when the system line includes spaces and/or
slashes. The method of returning from an interactive subprocess is
system dependent.

Format:
  SPAWN [/qualifier] [system_line]

2 system_line

        The ``system_line'' indicates a command line to be executed by
the operating system command interpreter. The available qualifiers have
no effect when this parameter is omitted.

2 /BATCH

        The BATCH qualifier indicates that the specified system
command does not require user interaction. The output of the program
is displayed on the screen the same way as internal commands are. This
qualifier cannot be used with the INTERACTIVE qualifier and is the
default when neither of them is specified.

2 /INTERACTIVE

        The INTERACTIVE qualifier indicates that the specified system
command requires user interaction. The screen is cleared before the
subprocess is created and is refreshed when the execution terminates.
This qualifier cannot be used with the BATCH qualifier. The BATCH
qualifier is the default when neither of them is specified.

1 STATUS

        The STATUS command displays information about the execution of
SIMex-PLUS or the support environment. This includes the default values
that are used and the working options that are set for the program.

Format:
  STATUS [/qualifier]

Symbols:
  SHOW STATUS               "STATUS"

2 /ENVIRONMENT

        The ENVIRONMENT qualifier indicates that the status of the
environment must be displayed on the screen. The global file which
supports the STATUS action must be present in the support subdirectory.
This qualifier cannot be used with the PROGRAM qualifier and is the
default when neither of them is specified.

2 /PROGRAM

        The PROGRAM qualifier indicates that the status of the program
be displayed on the screen. This qualifier cannot be used with the
ENVIRONMENT qualifier. The ENVIRONMENT qualifier is the default when
neither of them is specified.

1 SUSPEND

        The SUSPEND command invokes the load file associated with the
specified element to perform a SUSPEND operation. The load file of the
ROOT element is invoked when the parameter is not specified. A valid
operation password for the current configuration must be effective (can
be set using the SESSION command) to allow the use of this command.

Format:
  SUSPEND [element_name]

2 element_name

        The ``element_name'' parameter indicates the specified element.
The load file of the ROOT element is invoked when this parameter is not
specified.

1 TYPE

        The TYPE command displays on the screen the contents of a text
file associated with an element.

Format:
  TYPE [/CHECK_TYPE] element_name
  TYPE /NOCHECK_TYPE element_name

2 element_name

        The ``element_name'' indicates the element that must be
manipulated. Its associated file is displayed on the screen.

2 /CHECK_TYPE

        The CHECK_TYPE qualifier guarantees that only text file (as
indicated by the Specific Support Environment) will be displayed on the
screen. The NOCHECK_TYPE form must be specified to override the normal
check.

1 UNFREEZE

        The UNFREEZE command invokes the load file associated with the
specified element to perform a UNFREEZE operation. The load file of the
ROOT element is invoked when the parameter is not specified. A valid
operation password for the current configuration must be effective (can
be set using the SESSION command) to allow the use of this command.

Format:
  UNFREEZE [element_name]

2 element_name

        The ``element_name'' parameter indicates the specified element.
The load file of the ROOT element is invoked when this parameter is not
specified.

1 UNLOAD

        The UNLOAD command invokes the load file associated with the
specified element to perform a UNLOAD operation. The load file of the ROOT
element is invoked when the parameter is not specified. A valid operation
password for the current configuration must be effective (can be set using
the SESSION command) to allow the use of this command.

Format:
  UNLOAD [element_name]

2 element_name

        The ``element_name'' parameter indicates the specified element.
The load file of the ROOT element is invoked when this parameter is not
specified.

1 VALIDATE

        The VALIDATE command validates an element and its children. This
validation is normally done using the ENTER or BUILD commands. The use of
this command must be reserved for special occasions and used with caution.
If enabled, the management password will be requested.

Format:
  VALIDATE [element_name]

2 element_name

        The ``element_name'' indicates the starting element of the
subtree of the configuration hierarchy to be validated. It and all of
its children will be validated. The default scope is used when the
parameter is not specified.

1 VERIFY

        The VERIFY command sets or resets the options for checking the
behavior of the SSE components invoked by the alteration, operation, and
some special commands. All three options are set or reset when none of the
option qualifiers (/HEADER, /EXECUTION, /INSTRUCTION) are specified.

Format:
  VERIFY [/qualifier...] state_value

Symbols:
  SET VERIFY                "VERIFY"

2 state_value

        The ``state_value'' indicates the state of the verification
flags. Its only possible values are ON and OFF.

2 /ALTERATION

        The ALTERATION qualifier indicates that only the options related
to alteration commands are being modified. This qualifier cannot be used
with the OPERATION or the SPECIAL qualifiers and is the default when
none of them are specified.

2 /OPERATION

        The OPERATION qualifier indicates that only the options related
to operation commands are being modified. This qualifier cannot be used
with the ALTERATION or the SPECIAL qualifiers. The ALTERATION qualifier
is the default when none of them are specified.

2 /SPECIAL

        The SPECIAL qualifier indicates that only the options related to
special commands are being modified. This qualifier cannot be used with
the ALTERATION or the OPERATION qualifiers. The ALTERATION qualifier is
the default when none of them are specified.

2 /HEADER

        The HEADER qualifier indicates that the header option is being
modified. When this option is set, the configuration files selected
during the SSE (Specific Support Environment) header parsing are
displayed.

2 /EXECUTION

        The EXECUTION qualifier indicates that the execution option is
being modified. When this option is set, an execution trace is displayed
by the SSE (Specific Support Environment) component.

2 /INSTRUCTION

        The INSTRUCTION qualifier indicates that the instruction option
is being modified. When this option is set, the instruction file
produced by the SSE (Specific Support Environment) component is
displayed.

1 User_Interface

        This section provides some useful information about the user
interface of SIMex-PLUS. It describes the global parameters and the
special function keys that are available.

2 vt200_vt300

        The vt200 and vt300 terminals must be set in a specific way
to allow for proper use of the full screen mode.

DISPLAY             80 Columns
                    Interpret Controls

GENERAL             VT200/VT300 Mode, 7 Bit Controls
                    Normal Cursor Keys

COMMUNICATIONS      XOFF at 128
                    No Local Echo

2 Logical_Names

        The logical names are used to inform SIMex-PLUS of system
dependent initialization values. They can be defined on a system wide
basis, or independently by each user.

3 CAE$SIMEX_PLUS

        This mandatory logical name indicates the root directory of
SIMex-PLUS's required directory structure.

3 CAE$SMP_INIT

        This optional logical name indicates a file containing some
SIMex-PLUS commands to be executed during the program initialization.

3 CAE$SMP_WORK

        This optional logical name indicates the directory to set as
the default when SIMex-PLUS is started. The default directory is not
changed if this logical name is not defined or the specified directory
does not exist.

3 CAE$SMP_CONF

        This optional logical name indicates the configuration to use
when the SIMex-PLUS program is started. The "WORK" configuration is
used, if it exists, when the logical name is not defined or when the
specified configuration does not exist. This logical name is also used
by the SMP_FIND_FILE program.

3 CAE$SMP_ELEM

        This optional logical name indicates the initial value of the
scope element. The "ROOT" element is used when the logical name is not
defined or when the specified element does not exist.

3 CAE$SMP_HELP

        This optional logical name indicates the name and path of the
SIMex-PLUS help file. The help information is not available when this
logical name or the indicated file does not exist.

3 CAE$SMP_TERM

        This optional logical name indicates the terminal type being
used. When specified, its value must appear in the terminal definition
file indicated by the CAE$SOIL_CAP logical name. SIMex-PLUS will
operate in sequential mode when this logical name does not exist, the
specified terminal type is unknown or the terminal does not support
the required features.

3 CAE$SMP_MODE

        This optional logical name indicates the default mode of
operation for the user interface. The possible values are "line" and
"menu". The default is "menu" when the logical name does not exist or
the value is illegal.

3 CAE$SOIL_CAP

        This optional logical name indicates the name and path to the
terminal definition file. SIMex-PLUS will operate in sequential mode
when this logical name does not exist, the terminal specified by
CAE$SMP_TERM is unknown or the terminal does not support the required
features.

2 Invocation_Mode

        The program can be invoked in three different ways depending
on how the program is started and the terminal capabilities.

3 Immediate_Mode

        In immediate mode, a command line is given when SIMex-PLUS is
called by the user. The command is immediately executed and SIMex-PLUS
terminates as soon as the execution of that command is completed. The
information normally displayed on the screen is not shown and the
execution does not allow any interaction with the user. The syntax for
the commands follows the one specified for the Line Driven Command
Input without the editing capability.

3 Sequential_Mode

        In sequential mode, the command lines are read from the
standard system input file and the results are written to the standard
system output file. This mode is automatically selected when the user
terminal does not support all of the SIMex-PLUS features or when
SIMex-PLUS is invoked from a batch file. All the input of commands are
performed by the operating system, which does not necessarily support
the SIMex-PLUS command editing features provided in full screen
invocation mode. The syntax for the commands follows the one specified
for the Line Driven Command Input.

3 Full_Screen_Mode

        In full screen mode, SIMex-PLUS takes control of the user
terminal. Information is permanently displayed at the top of
the screen and commands are read by SIMex-PLUS which allow Line
Driven Command input and Menu Driven Command Input. This mode is
automatically selected when the user is directly interacting with
SIMex-PLUS and the terminal supports the SIMex-PLUS features.

2 Interaction

        The special interaction features of SIMex-PLUS are available
when the program is operating in full screen mode. In this mode,
the command recall and editing features are implemented by the user
interface of SIMex-PLUS which supports two ways of entering commands.
Some special functions are also available for convenience:

CTRL-W : This key is used to refresh the screen. The screen is
        completely erased and the information is redisplayed.
PREV PAGE,CTRL-? : These keys are used to scroll backward in the
        output region of the screen. Previously displayed information
        is visible again for reference. These keys can be used until
        the beginning of the internal output buffer has been reached.
        The region is automatically scrolled to the end of the buffer
        before new output is displayed.
NEXT PAGE,CTRL-L : These keys are used to scroll forward in the output
        region of the screen. Previously displayed information is
        visible again for reference. These keys can be used until the
        end of the internal output buffer has been reached.

3 Line_Driven

        Command names and parameters must be separated by at least
one space character. The qualifiers, which select the options, can
be typed anywhere on the line after the command names but must be
preceeded by the `/' character. When a value is assigned to a
qualifier, the value must be joined to the qualifier name by a `='
character. Double quotes can be used to allow spaces, slashes and
lower case letters to appear inside values. To appear in the value,
a double quote must be preceded by another double quote.

        Command symbols which are aliases for commands can be used in
places of command names to activate commands while using other names
and/or preselecting options. Abbreviation of these command symbols,
names and qualifier is allowed as long as they are not ambiguous. The
special keyboard keys (PF1 to PF4 and F5 to F20) can also be used to
activate commands when the command line is empty.

        The special /MENU qualifier can be used to convert the line
driven input to menu driven input for the duration of the specified
command. Any specified parameters and qualifiers are used to fill the
menu when it is displayed. The input mode is converted back to line
driven input when the command terminates or is canceled.

        A number of special keys can be used to reduce typing and
allow editing of the command line:

RETURN,CTRL-J,CTRL-M : These keys are used to execute the command
        line.
DO,CTRL-Z : These keys are used to execute the command line.
UP ARROW,CTRL-B : These keys are used to recall previously typed
        commands. They can be used until the first command appearing
        in the internal command history buffer has been reached.
DOWN ARROW,CTRL-G : These keys are used to recall previously typed
        commands. They can be used as long as the last command in
        the internal command history buffer has not been passed.
LEFT ARROW,CTRL-D : These keys are used to move the cursor one
        character to the left, and can be used until the first
        character of the command line has been reached.
RIGHT ARROW,CTRL-F : These keys are used to move the cursor one
        character to the right, and can be used until the last
        character of the command line has been reached.
CTRL-T : This key is used to move the cursor to the beginning of
        the command line.
CTRL-E : This key is used to move the cursor to the end of the
        command line.
DELETE,CTRL-H : These keys are used to delete the character left of
        the cursor position. The remaining characters on the line
        are moved to the left to fill the gap. This action can be
        performed until the beginning of the command line has been
        reached.
CTRL-N : This key is used to delete the current character on the
        command line. The remaining characters on the command line
        are moved to the left to fill the gap. This action can be
        performed until the end of the command line has been reached.
CTRL-U,CTRL-X : These keys are used to delete all characters to the
        left of the cursor. The current character becomes the first
        character of the command line.
CTRL-P : This key is used to delete the characters from the current
        character to the last character of the command line.
CTRL-V : This key is used to delete the entire command line.
        The cursor is placed at the beginning of the command line.
REMOVE,CTRL-R : These keys are used to cancel the command being
        entered. They delete the entire command line and the cursor
        is placed at the beginning of the command line.
CTRL-A : This key is used to toggle the INSERT/OVERSTRIKE switch for
        entering characters on the command line.

3 Menu_Driven

        In the ``menu'' mode there are two stages used to issue
a command: In the selection stage, the user scrolls through the
available commands using the DOWN ARROW and UP ARROW keys. Once the
desired command is reached, the corresponding function key (PF1, PF2,
PF3, or PF4) is typed, putting the user in the command stage. The
other special function keys can also be used but only if commands
has been assigned to them. In the command stage, the user can enter
parameter and qualifier value, and select options using the menu of
the command. The following special keys are provided to manipulate
the menu:

DO,CTRL-Z : These keys execute the selected command. The user interface
        will remain in the command stage and the menu will remain
        on the screen until the parameters and qualifiers have been
        validated. If an error is detected, the cursor is positioned
        on the error line.
REMOVE,CTRL-R : These keys return the user to the selection stage
        without executing the selected command.
DOWN ARROW,CTRL-G : These keys are used to move from one line of the
        menu to the next. The cursor is moved back to the first line
        when the last one has been passed.
RETURN,CTRL-J,CTRL-M : These keys are used to move from one line of
        the menu to the next. The cursor is moved back to the first
        line when the last one has been passed.
UP ARROW,CTRL-B : These keys are used in conjunction with the DOWN
        ARROW, CTRL-G keys to select the menu line, moving in the
        opposite direction. The cursor is moved back to the last line
        when the first one has been passed.
SELECT,CTRL-K : These keys are used to toggle the state of a boolean
        option.
LEFT ARROW,CTRL-D : These keys are used to move the cursor one
        character to the left in a value field, and can be used until
        the first character of the field has been reached. It can also
        be used to toggle the state of a boolean option.
RIGHT ARROW,CTRL-F : These keys are used to move the cursor one
        character to the right in a value field, and can be used
        until the last character of the field has been reached. They
        can also be used to toggle the state of a boolean option.
CTRL-T : This key is used to move the cursor to the beginning of
        a value field.
CTRL-E : This key is used to move the cursor to the end of a value
        field.
DELETE,CTRL-H : These keys are used to delete the character left of the
        cursor position in a value field. The remaining characters in
        the field are moved to the left to fill the gap. This action can
        be performed until the beginning of the line has been reached.
CTRL-N : This key is used to delete the current character in a value
        field. The remaining characters in the field are moved to the
        left to fill the gap. This action can be performed until the
        end of the value has been reached.
CTRL-U,CTRL-X : These keys are used to delete all characters to the
        left of the cursor. The current character becomes the first
        character of the field.
CTRL-P : This key is used to delete the characters from the current
        character to the last character of the value field.
CTRL-V : This key is used to delete a field. The cursor is placed at
        the beginning of the empty field.
CTRL-A : This key is used to toggle the INSERT/OVERSTRIKE switch for
        entering characters in a value field.

        The value field of a parameter must be entered unless the
parameter is indicated to be optional in the menu. The optional value
field of a boolean option can be entered if the field is provided, but
only when the YES state has been selected.

1 Error_Messages

        This section lists the most common error messages that can be
reported by SIMex-PLUS. They are divided into two groups: the ones that
are reported by the SIMex-PLUS program itself and the ones that are
reported by the user interface.

2 SIMex-PLUS

        This section describes the error messages that are reported by
SIMex-PLUS itself. This list does not include the messages that are
defined in the SOIL (Standard Operating-System Interface Layer) which
are described in the SOIL External Document.

3 Command

Command aborted by user

        This message is displayed when a command is aborted because
the user typed a CTRL-C or CTRL-Y while the command was being
executed. Those characters are checked whenever a safe command abort
can be performed.


Command file execution aborted

        This message is displayed when an error is displayed for a
command which appears in a SIMex-PLUS command file. The execution of
the command file is terminated.

3 Component

Component failure; no instruction file made

        This message is displayed when an SSE component does not
produce an instruction file and does not set an error status when
terminating. A component will indicate a failure by setting an error
status or by not producing an instruction file. In both cases, the
component should have produced output files describing the error. This
message is normally the last resort when an error status could not be
set.

3 Configuration

Configuration already being used

        This message is displayed when the current configuration is
specified but a different configuration is required. This can occur
with commands such as COPY ELEMENT and COMPARE which manipulate two
different configurations at the same time.


Configuration already exists

        This message is displayed when an attempt is made to create a
configuration that already exists. The SHOW CONFIGURATION command can
be used to display a list of the available configurations.


Configuration does not exist

        This message is displayed when the specified configuration
does not exist. The SHOW CONFIGURATION command can be used to display
a list of the available configurations.


Configuration is not currently selected

        This message is displayed when a command that requires a
configuration to be selected, is issued. The SHOW CONFIGURATION
command must be used to select a configuration.

3 Cycle

Cycle would be formed by a new dependency

        This message is displayed when an attempt is made to add a
dependency that would create a cycle of one or more elements. Aside
from violating the rules of the dependency hierarchy, cycles would
cause infinite loops in the execution of some commands.

3 Dependency

Dependency already exist between elements

        This message is displayed when an attempt is made to create a
dependency that already exists.


Dependency non-existent between elements

        This message is displayed when an attempt is made to remove an
non-existing dependency. This error is sometimes the result of having
specified the two elements in the wrong order to the REMOVE command. A
dependency defines a child-parent relation between two elements.

3 Element

Element already exists in the configuration

        This message should never be displayed on the screen unless
there is an internal problem, which should be reported to the
utility's maintainers. It means that an attempt has been made to
create an element that already exists in the database.


Element does not exist in the configuration

        This message is displayed when the specified element is not
part of the current configuration. This error is often the result of
misspelling an element name.


Element does not have a load file

        This message is displayed when the operation file resulting
from the invocation of a LOAD file contains an element name that does
not have an associated LOAD file.


Element does not have an enter file

        This message is displayed when an ENTER or a GET command is
issued for an element that does not have an ENTER file or the ENTER
file cannot be deduced from the name of the element.


Element has different children

        This message is displayed if, when copying an element from one
configuration to another, the element does not have identical children
in the two configurations.

3 Error

Error reported during command execution

        This message is displayed when a command terminates to
indicate that other errors were reported during the command execution.
Some commands continue their processing even when errors are
encountered (e.g. BUILD/CONTINUE and CHECK).

3 File

File does not contain text

        This message is displayed when a TYPE command is issued for an
element with a file that does not contain text. The internal indicator
that tells SIMex-PLUS about the type of the file is updated by the
element's ENTER file when the file is entered. This indicator cannot
be changed by issuing a SIMex-PLUS command. However, a NOCHECK_TYPE
qualifier can usually be used to override the check.


File version already used for an element

        This message is displayed when a file exists in the specified
directory with the same version but different content as one in the
configuration being copied using the BACKUP command.


File version has changed since used

        This message is displayed when an alteration file contains
an instruction that requests a file verification and the specified
file (including directory path, element name and file version)
is not part the current configuration. This usually indicates an
external manipulation of a file that was never part of the current
configuration or that has been updated since the manipulation occurred.

3 Illegal

Illegal use of reserved directory

        This message is displayed when an alteration file contains
an instruction that requests the manipulation of a file from the
protected directory. A protected file cannot be renamed or deleted
from the protected directory.

3 Incorrect

Incorrect database description version

        This message is displayed when the database belongs to another
version of SIMex-PLUS (i.e. other than Version 2.x).


Incorrect password for this configuration

        This message is displayed when the password entered by the
user does not match the one assigned to the configuration. This
message can also be displayed if SIMex-PLUS tries to execute a command
file when the user has not entered a required password. A password can
be entered using the SESSION command.

3 Interactive

Interactive spawn not allowed in batch mode

        This message is displayed when an interactive SPAWN command is
encountered in a batch job.

3 Invalid

Invalid comment specification

        This message is displayed when the value given to a
history comment parameter does not follow the syntax for a comment
specification. A valid comment is at least 15 characters long,
composed of 3 words or more, and does not have the same character
three times consecutively (except for digits).


Invalid data value specification

        This message is displayed when the value given to a data value
parameter (usually passwords, user name, etc.) does not follow the
syntax for a data value specification.


Invalid directory specification

        This message is displayed when the value given to a directory
path parameter does not follow the syntax for a directory path
specification.


Invalid element name specification

        This message is displayed when the value given to an element
name parameter does not follow the syntax for an element name
specification.


Invalid file name specification

        This message is displayed when the given file name
specification is in an incorrect format for a proper file
specification of the operating system.

3 Maximum

Maximum file version number exceeded

        This message is displayed when the maximum version number
allowed by SIMex-PLUS for a particular file has been exceeded.


Maximum number of symbols reached

        This message is displayed when there is no more space
available for new symbols in the symbol table.

3 No

No file associated with that element

        This message is displayed when an attempt is made to use the
file of an element that does not have one.

3 Not

Not enough parameters on the command line

        This message is displayed when there are not enough parameters
specified by the user for the current context of the command. Some
commands require the presence of optional parameters depending on the
qualifier used or the state of the configuration.

3 Only

Only ON and OFF values are allowed

        This message is displayed when an invalid value is given to a
boolean parameter.

3 Operation

Operation in progress, try again later

        This message is displayed when the user tries to execute an
operation command (LOAD, UNLOAD, FREEZE, UNFREEZE, RUN, or SUSPEND)
while another user is executing a command of the same type. Wait until
the other user's command is completed.

3 Other

Other users are working, try again later

        This message is displayed when an attempt is made to purge
the elements' directory while other users are working in the same
protected directory. The PURGE command cannot be used while other
users are using SIMex-PLUS.

3 Parameter

Parameter specification is empty

        This message is displayed when there are empty parameters
specified by the user when there should be parameters specified. The
parameter that needs to be filled is displayed in parentheses.

3 Password

Password confirmation failed

        This message is displayed when the user incorrectly retypes a
changed password for confirmation of the change.

3 Purge

Purge in progress, try again later

        This message is displayed when the user tries to execute a
command while another user is purging the elements' directory. Not
all commands are allowed while a PURGE command is being executed by
another user.

3 Removal

Removal would result in illegal hierarchy

        This message is displayed when an attempt is made to remove
the last dependency of a child which has children of its own.
Automatic deletion of a configuration sub-tree is not currently
supported.

3 The

The database is locked by another user

        This message is displayed when the database cannot be locked
because someone else has locked the database. Another attempt should
be made later when the database is no longer locked by the other user.


The history file does not exist

        This message is displayed when SIMex-PLUS is unable to access
the history file. The history file has not been created yet.


The history file is locked by another user

        This message is displayed when the history file is being used
and manipulated by another user.

3 Too

Too many parameters on the command line

        This message is displayed when there are too many parameters
specified by the user for the current context of the command. Some
commands require less parameters depending on the qualifier used or
the state of the configuration.

3 Unknown

Unknown command specification

        This message is displayed when the command sought for in SHOW
COMMAND does not exist.


Unknown key specification

        This message is displayed when the specified key is not
assignable (i.e. not within F5 to F20). PF1 to PF4 are reserved to
the program and cannot be reassigned.


Unknown symbol specification

        This message is displayed when the user is trying to deassign
an already deassigned symbol.

3 Use

Use of an obsolete file

        This message is displayed when an attempt is made to access
the file of an element that is not currently built. The specified
element should be reentered or rebuilt.

3 User

User name required

        This message is displayed if SIMex-PLUS tries to execute a
command file when the user has not entered the user name. The user
name can be entered using the SESSION command.

2 User_Interface

        This section describes the error messages that are reported by
the user interface.

3 Ambiguous

Ambiguous command specification

        This message is displayed when the characters supplied for
the command name are ambiguous. More characters should be supplied to
identify the desired command. A list of the commands an be obtained
using the SHOW COMMAND command.


Ambiguous qualifier specification

        This message is displayed when the characters supplied for
a qualifier name are ambiguous. The command name should be checked
to make sure that the expected command is being analyzed when an
abbreviation is used to specify the command, or more characters should
be supplied to identify the desired qualifier.


Ambiguous symbol specification

        This message is displayed when the characters supplied for the
symbol are ambiguous. More characters should be supplied to identify
the desired symbol. A list of the currently defined symbols can be
obtained using the SHOW SYMBOL command.

3 Command

Command too complex for a menu

        This messages is displayed when a command is too complex to
allow the user interface to generate a menu on screen. This is usually
caused by the fact that a command has too many parameters and options
for the available screen space.

3 Invalid

Invalid command line syntax

        This message is displayed when the next token to be analyzed
on the command line should be but is not a qualifier name, is not a
qualifier value, or has a qualifier value but missing the
corresponding qualifier. The syntax of the command line should be
checked.

3 Parameter

Parameter is missing from command

        This message is displayed when the number of parameters is
less than the required number for the command being analyzed. The
command name should be checked to make sure that the expected command
is being analyzed when an abbreviation is used to specify the command,
or the supplied parameters should be checked to make sure that the
requirements are being met.


Parameter value is too long

        This message is displayed when the length of a parameter value
exceeds the maximum required length of that particular parameter.

3 Partial

Partial command specification

        This message is displayed when there is a missing keyword in
matching the command name. A list of the commands an be obtained using
the SHOW COMMAND command.


Partial symbol specification

        This message is displayed when there is a missing keyword in
matching the symbol. A list of the currently defined symbols can be
obtained using the SHOW SYMBOL command.

3 Qualifier

Qualifier already appears on the line

        This message is displayed when a qualifier appears at least
twice on the command line. A second appearance of a qualifier can not
be used to complement or override its first appearance.


Qualifier selection already made

        This message is displayed when a selection qualifier appears
after another selection qualifier associated with the same selection
option. A selection qualifier can not be used to override another
selection qualifier.


Qualifier value is too long

        This message is displayed when the length of a qualifier value
exceeds the maximum required length of that particular qualifier.

3 Specified

Specified command is unknown

        This message is displayed when the characters supplied for
the command name do not identify any command. The supplied characters
should be checked for a spelling error. A list of the commands an be
obtained using the SHOW COMMAND command.


Specified qualifier is unknown

        This message is displayed when the characters supplied for a
qualifier name do not identify any qualifier for the command being
analyzed. The command name should be checked to make sure that the
expected command is being analyzed when an abbreviation is used to
specify the command, or the supplied characters should be checked for
a spelling error.


Specified symbol is unknown

        This message is displayed only when there is an internal
problem. The message results from discovery that the given keyword(s)
are not symbols, and so they will be treated as command(s). A list of
the currently defined symbols can be obtained using the SHOW SYMBOL
command.

3 Symbol

Symbol does not contain a command

        This message is displayed when the symbol does not contain
anything that can be interpreted as a command.

3 Too

Too many parameters for the command

        This message is displayed when the number of parameters
exceeds the requirements for the command being analyzed. The command
name should be checked to make sure that the expected command is being
analyzed when an abbreviation is used to specify the command, or the
supplied parameters should be checked to make sure that the syntax of
the command is being followed.

