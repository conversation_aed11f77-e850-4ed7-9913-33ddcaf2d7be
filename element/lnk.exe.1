#! /bin/csh -f
# $Revision: LNK.EXE V2.2 06-Feb-1992 | RBE new feature from CF $
#
# Added C library explicitly before the Fortran library for this AIX 3.1.2.15
#
# <PERSON><PERSON>(25 Jan 1991) :
#      - Put C, Fortran libs.
# Version 1.2 <PERSON> (26-Jul-91) :
#      - add the option -bI:/cae/lib/cae_syscall.exp in the link
# Version 1.3 <PERSON> (7-Aug-91) :
#      - add the option -bI:/cae/lib/caeioctl.exp in the link
# Version 2.2 <PERSON> (6-Feb-92) :
#      - this utility had been improve a lot bu G. FUNNEL dept 74 
#      - add the option -r (revision) -n (no revision), fix -o option
#      - version increase to 2.2 because on screen it was 2.1, could not find
#        any explanation why 
#
#
onintr inter
#
set system_name=`uname -s`
#
echo "*** (LNK V2.2) ***"
#
set LIB = '-lcae'
if ("$system_name" == "AIX") then
  set CMD = 'xlf -L/cae/lib -lc -bI:/cae/lib/cae_syscall.exp -bI:/cae/lib/caeioctl.exp'
else
  set CMD = 'f77 -static -L/cae/lib'
endif
set SOFT = ' '
set ODEF="false"
if ($#argv == 0) then
   echo 'Usage: lnk objectfile [objectfile ...] [-L librarypath] [-l library]'
   echo '                      [-o executable] [-g]'
else
 set NSRC = `revl $argv[1] %mod`
 set IEV = $status
 if ($IEV == 0 || $IEV == 6) then
      set argv = `cat $NSRC`
 endif
#
 while ($#argv != 0)
#
   if ("$argv[1]" == "-o") then
      shift
      set EXEC = `revl $argv[1] + %exe` 
#
   else if ("$argv[1]" == "-r") then
      set REV="on"
#
   else if ("$argv[1]" == "-n") then
      set REV="off"
#
   else if ("$argv[1]" == "-l") then
      shift
      set CMD = "$CMD -l$argv[1]"
#
   else if ("$argv[1]" == "-L") then
      shift
      set CMD = "$CMD -L$argv[1]"
#
   else if ("$argv[1]:e" == "a") then
      set NSRC = `revl $argv[1] %a`
      set IEV = $status
      if ($IEV == 0) then
         set LSRC = `unik $NSRC`
         set LSRC = "$LSRC:t"
         ln -s $NSRC $LSRC.a
         set SOFT = "$SOFT $LSRC.a"
         set CMD = "$CMD $LSRC.a"
      else 
         echo "$argv[1] : `reverr $IEV`"
         rm $SOFT
         exit
      endif
#
   else if ("$argv[1]:e" == "mod") then
      echo "mod"
      set NSRC = `revl $argv[1] %mod`
      set MOD=`cat $NSRC`
      set argv="`glob $argv[1] $MOD $argv[2-]`"
#
   else if ("$argv[1]" == "-g") then
      set CMD = "$CMD -g"
#
   else if ("$argv[1]:e" == "o") then
      set NSRC = `revl $argv[1] %o`
      set IEV = $status
      if ($IEV == 0) then
         set LSRC = `unik $NSRC`
         set LSRC = "$LSRC:t"
         ln -s $NSRC $LSRC.o
         set SOFT = "$SOFT $LSRC.o"
         set CMD = "$CMD $LSRC.o"
      else 
         echo "$argv[1] : `reverr $IEV`"
         rm $SOFT
         exit
      endif
#
   else
      set NSRC = `revl $argv[1] %obj`
      set IEV = $status
      if ($IEV == 0) then
         set LSRC = `unik $NSRC`
         set LSRC = "$LSRC:t"
         ln -s $NSRC $LSRC.o
         set SOFT = "$SOFT $LSRC.o"
         set CMD = "$CMD $LSRC.o"
      else 
         set NSRC = `revl $argv[1] %a`
         set IEV = $status
         if ($IEV == 0) then
            set LSRC = `unik $NSRC`
            set LSRC = "$LSRC:t"
            ln -s $NSRC $LSRC.a
            set SOFT = "$SOFT $LSRC.a"
            set CMD = "$CMD $LSRC.a"
         else 
            echo "$argv[1] : `reverr $IEV`"
            rm $SOFT
            exit
         endif
      endif
      if ($?EXEC == 0) then
         set EXEC = `revl $argv[1] + %exe` 
      endif
   endif
#
   shift
 end
#
 set CMD = "$CMD $LIB"
 if ($?EXEC) then
    if ($?REV == 0) then
      echo -n "Executable with revision number? (y/n) : "
      set ANS = ($<)
    else
      if ("$REV" == "on") then
        set ANS = "y"
      else
        set ANS = "n"
      endif
    endif
    if ($ANS != y) then
       set EXEC = `norev $EXEC`
       set EXEC = $EXEC:r
    endif
    set CMD = "$CMD -o $EXEC"
 endif
 echo $CMD
 $CMD
 unalias rm
 rm $SOFT
endif
exit
# interrupt handling
inter:
 rm $SOFT
exit
