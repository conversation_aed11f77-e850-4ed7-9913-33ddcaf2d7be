      SUBROUTINE SCALALL
C'Title 	Scaling system
C'Module_ID	
C'PDD_#		
C'Customer 	
C'Application	Scales of all systems on tcftot=t for morning readiness.
C'Author	<PERSON>
C'Date		SEP 1991
C
C'System	Morning readiness
C'Itrn_rate	critical band
C
C'Revision_history
C
C  usd8scal.for.1  9Jan1992 10:51 usd8 marcb
C       < initial edit to put the ship name. >
C
CP USD8 TCFTOT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:53:00 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      LOGICAL*1
     &  TCFTOT         ! FREEZE/TOTAL
C$
      LOGICAL*1
     &  DUM0000001(306084)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,TCFTOT    
C------------------------------------------------------------------------------
C------------------------------------------------------------------------------
C
         integer*4 I
         if (tcftot) then
            do I = 0,100,4
               call scalin(i)
               call scalout(i)
            enddo
         endif
         return
         end
