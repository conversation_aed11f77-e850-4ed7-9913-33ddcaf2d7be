C~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C~~~~ Name:           <PERSON><PERSON><PERSON><PERSON> EXECUTIVE ( TL )             ~~~~
C~~~~ Module_ID:      USD8TL                              ~~~~
C~~~~ Customer:       DELTA FTD MD88                      ~~~~
C~~~~ Author:         TERRY SIVILLA                       ~~~~
C~~~~ Date:           August,1991                         ~~~~
C~~~~                                                     ~~~~
C~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C
C
      SUBROUTINE USD8TL
C     ------------------
C'Revision_History
C
C  usd8tl.for.58 25Jul1992 00:07 usd8 Baon
C       < Auto Scrolling logic for long lesson page >
C
C  usd8tl.for.57 23Jul1992 04:19 usd8 BAON
C       < Put in logic for automatic page change if the new step is not in
C         the current page >
C
C  da88tl.for.56 29May1992 16:28 dat1 nt
C       < bytecnt for page header = max_hdr*2  (was*2), added LPMANINP
C         logic see lpmaninp & improved *dcb pointer logic @70 >
C
C  da88tl.for.55 26May1992 17:06 dat1 Changed
C       < now tlselect=3 for current step & 1 for preselect to match with
C         lp editor >
C
C  da88tl.for.54 25May1992 23:39 dat1 exit
C       < removed lp preselects in reverse order >
C
C  da88tl.for.53 25May1992 23:12 dat1 nt
C       < reset old values & improved next dcb pointer >
C
C  da88tl.for.52 25May1992 22:15 dat1 nt
C       < use tlstep for xstable(start+2) >
C
C  da88tl.for.51 25May1992 20:31 dat1 nt
C       < Added LP "reset" logic in ttlstep >
C
C  da88tl.for.50 25May1992 18:40 dat1 nt
C       < Removed TLSELECT=3 magenta & xstabl(start+2) contains the num of
C         preselect step see xsrt >
C
C  da88tl.for.49 22May1992 12:19 dat1 nt
C       < Fixed criteria offset for auto LP see TL_START/END >
C
C  da88tl.for.48 22May1992 11:09 dat1 nt
C       < TLACTN in base 1 >
C
C  da88tl.for.47 20May1992 11:28 dat1 nt
C       < TLSELECT in CDB >
C
C  da88tl.for.46 14May1992 12:24 dat1 nt
C       < Added fix for AUTO LP logic from md88 >
C
C  dat1tl.for.45 13May1992 17:43 dat1 nt
C       < Modified s312 for da88: CP statement. Temp declare TLSELECT local >
C
C  s312tl.for.44 14Apr1992 09:36 s312 s.gould
C       < added ident code to module >
C
C  s312tl.for.43 27Feb1992 13:37 s312 ts
C       < Latest version with lesson profile logic from c737 >
C
C  c757tl.for.42 16Jan1992 19:59 c757 TS
C       < Added logic for handling lesson profiles >
C
C  c757tl.for.41 16Jan1992 18:41 c757 TS
C       < Commented out occurances of TLSELECT until CDB update >
C
C  c757tl.for.40 16Jan1992 18:33 c757 TERRY S
C       < Brought over from SIA A310 #2 >
C
C File: /cae1/ship/s312tl.for.39
C       Modified by: TS
C       Wed Dec  4 11:10:09 1991
C       < fixing up pointers for preselect steps >
C
C File: /cae1/ship/s312tl.for.33
C       Modified by: TERRY
C       Tue Dec  3 16:55:33 1991
C       < Added one more color for currently selected step ( yellow ) >
C
C     File: /cae/if/ship/s312tl.for.9
C     Modified by: ts
C     Tue Nov 26 12:16:47 1991
C     < added lesson end/reset logic >
C
C     File: /cae1/ship/s742tl.for.151
C     Modified by: MB
C     Mon Nov 11 20:32:07 1991
C     < cae_io_open was using wrong label name. >
C
C     File: /cae1/ship/s742tl.for.149
C     Modified by: TS
C     Mon Nov 11 19:10:58 1991
C     < put null byte at end of file name string >
C
C     File: /cae1/ship/s742tl.for.148
C     Modified by: Terry Sivilla
C     Mon Nov 11 11:07:52 1991
C     < increased length of file name string >
C
C     File: /cae/if/ship/s742tl.for.164
C     Modified by: M.BREK
C     Fri Oct 25 17:08:41 1991
C     < Removed rev_curr logic for PAGE_DAT logical name >
C
C     File: /cae/if/ship/s742tl.for.164
C     Modified by: TERRY SIVILLA
C     Wed Sep 25 14:08:41 1991
C     < First Release >
C
      IMPLICIT NONE
C
CQ    USD8 XRFTEST(*)
CP    USD8 TL(*),            ! Lesson Executive labels.
CP   -     TAPAGE,XSOFFST,XSPRUPD,XSNXTOFF,XSCRITVAL,XSINDEX,
CP   -     XSARM,
CP   -     YXSTRTXRF1,
CP   -     XSOFFBLK,XSDCB,XSDCBSET,TTLSTEP,
CP   -     XISPBYTE, XISPWORD
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:12:23 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  TLALT          ! LESSON ALTITUDE                     [FL   ]
     &, TLELTIME       ! LESSON ELAPSED TIME                 [Secs ]
     &, TLNST          ! LESSON/STEP INPUT
     &, TLSPEED        ! LESSON SPEED                        [Knots]
     &, TLTDELAY       ! LESSON TIME DELAY                   [Secs ]
     &, TLTIME         ! LESSON TIME                         [Secs ]
C$
      INTEGER*4
     &  TLACTLPG       ! SPECIFIES ACTIVE LESSON PHASE PAGE
     &, TLESSON        ! LESSON NUMBER
     &, TLEXARM        ! ARM EXERCISE
     &, TLEXREP        ! REPEAT EXERCISE
     &, TLKILL         ! KILL STATION FROM LESSON PLAN
     &, TLMODE         ! LESSON MODE
     &, TLPAGE         ! LESSON PLAN PAGE NUMBER
     &, TLSTEP         ! LESSON STEP NUMBER
     &, TLUNKILL       ! RESTORE STATION FROM LESS PLAN
C$
      INTEGER*2
     &  TAPAGE(4)      ! PAGE REQUEST TO SGI
     &, TLSELECT(100)  ! LESSON PLAN COLOR TABLE
C$
      LOGICAL*1
     &  TLABTO         ! ABORTED TAKEOFF DETECT
     &, TLABTORQ       ! ABORTED TAKEOFF REQUEST
     &, TLACTAV(15)    ! ACTIVATE TRIGGER DEL/UNDEL
     &, TLACTN         ! LESSON PLAN STEP ACTION
     &, TLACTNAV       ! LESSON ACTION AVAILABLE
     &, TLAMSLCT       ! AUTO/MANUAL TOGGLE
     &, TLAUTO         ! AUTO MODE
     &, TLAUTOAV       ! AUTO MODE AVAILABLE
     &, TLAUTOINT      ! AUTO INIT ACTIVE FLAG
     &, TLAUTOPG       ! AUTO PAGE CHANGE TRIGGER
     &, TLAUTOPR       ! AUTO PAGE CHANGE PERFORMED
     &, TLAUTOPS       ! AUTOMATIC POSITION HOLD
     &, TLAUTOR        ! AUTO RESET TRIGGER
     &, TLCNIA         ! CNIA/LESSON SYNC
     &, TLCOMPLT(15)   ! EXERCISE COMPLETE FLAG
     &, TLDETECT(20)   ! DETECT SPECIAL FEATURES/MANEUVRES FLGS
     &, TLDUMGEN       ! GEN. PURPOSE LESSON TOOL DUMMY
     &, TLEND          ! LESSON END BUTTON
     &, TLENDAV        ! LESSON END AVAIL
     &, TLESSUPD       ! LESSON PLAN UPDATE
     &, TLEXACT(20)    ! EXERCISE ACTIVE FLAG
     &, TLEXCOMP       ! ALL EXERCISE COMPLETE
     &, TLFREEZE       ! FREEZE LESSON PLAN
     &, TLICPIN        ! INITIAL CONDITIONS INSERTED
     &, TLINDEX        ! LESSON INDEX BUTTON
     &, TLINE(15)      ! SPECIFIES EXECISE MANUALLY SELECTED
     &, TLINPINH       ! LESSON INPUT INHIBIT
     &, TLJUSTON       ! UPDATE LESSON PAGE--XO
     &, TLLAND         ! A/C JUST LANDED
     &, TLMANUAL       ! MANUAL MODE
     &, TLMISCB(30)    ! LESSON MISCELLANEOUS
      LOGICAL*1
     &  TLMULTSET(20,4)! MULTIPLE SET VALUE FLAGS
     &, TLOUTAV        ! LESSON PLAN OUT AVAILABLE
     &, TLOUTLP        ! LESSON PLAN OUT
     &, TLPGBK(2)      ! LESSON PAGE BACK
     &, TLPGBKAV(2)    ! PAGE BACK AVAILABLE
     &, TLPGFWAV(2)    ! PAGE FORWARD AVAILABLE
     &, TLPGFWD(2)     ! LESSON PAGE FORWARD
     &, TLPGREDY       ! LESSON PAGE READY FLAG
     &, TLPPSLCT       ! LESSON PHASE INSERT
     &, TLPUPD         ! LESSON PLAN UPDATE
     &, TLRESET(15)    ! CB RESETTABLE MESSAGE
     &, TLRESPER       ! AUTO RESET INPUT INHIBIT
     &, TLRTPHAV       ! RETURN TO PHASE AVAILABLE
     &, TLRTPHS        ! RETURN TO PHASE
     &, TLRTPLAV       ! RETURN TO PLAN AVAILABLE
     &, TLRTPLN        ! RETURN TO PLAN
     &, TLSTARAV       ! LESSON START AVAIL
     &, TLSTART        ! LESSON START BUTTON
     &, TLSTDNAV       ! LESSON STEP DOWN AVAILABLE
     &, TLSTEPAV       ! LESSON PLAN STEP AVAILABLE
     &, TLSTEPDO       ! LESSON PLAN STEP DOWN
     &, TLSTEPSK       ! LESSON PLAN STEP SKIP
     &, TLSTEPSL(100)  ! LESSON PLAN STEP ACTIVATED
     &, TLSTEPUP       ! LESSON PLAN STEP UP
     &, TLSTUPAV       ! LESSON STEP UP AVAILABLE
     &, TLTAKOFF       ! A/C JUST TOOK OFF
C$
      LOGICAL*1
     &  DUM0000001(105976),DUM0000002(1),DUM0000003(1)
     &, DUM0000004(206300)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,TLOUTLP,TLOUTAV,TLSTEPAV,TLSTEPDO,TLSTEPUP
     &, TLSTEPSK,TLACTN,TLSTEPSL,DUM0000002,TLNST,TLESSON,TLSTEP
     &, TLPAGE,TLMODE,TLALT,TLSPEED,TLELTIME,TLSELECT,TLSTUPAV
     &, TLSTDNAV,TLACTNAV,DUM0000003,TLKILL,TLUNKILL,TLACTLPG
     &, TLINE,TLDUMGEN,TLPGREDY,TLCOMPLT,TLDETECT,TLRESET,TLACTAV
     &, TLJUSTON,TLMISCB,TLFREEZE,TLAUTOINT,TLMULTSET,TLEXACT
     &, TLINDEX,TLENDAV,TLSTART,TLSTARAV,TLEND,TLICPIN,TLAUTO
     &, TLAUTOAV,TLMANUAL,TLAMSLCT,TLPGBK,TLPGFWD,TLPGBKAV,TLPGFWAV
     &, TLRTPLN,TLRTPLAV,TLRTPHS,TLRTPHAV,TLEXCOMP,TLAUTOPG,TLAUTOPR
     &, TLAUTOR,TLCNIA,TLINPINH,TLRESPER,TLPUPD,TLPPSLCT,TLLAND
     &, TLTAKOFF,TLEXREP,TLEXARM,TLTIME,TLTDELAY,TLABTORQ,TLABTO
     &, TLAUTOPS,TLESSUPD,DUM0000004,TAPAGE    
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      REAL*4   
     &  XSCRITVAL(50,20)
C$                     ! VALUE TABLE FOR PRESELECT CRITERIA
C$
      INTEGER*4
     &  XSINDEX(50)    ! OFFSET LOCATION OF EACH PRESELECT TABLE
     &, XSNXTOFF       ! NEXT AVAILABLE LOCATON IN THE PRE' TABLE
     &, XSOFFBLK(4,63,2)
C$                     ! PRESELECT OFFSET BLOCK
     &, XSOFFST(50)    ! CONTAINS THE OFFSET OF THE ARMES MALF'
C$
      INTEGER*2
     &  TTLSTEP        ! LESSON PLAN STEP
     &, XISPWORD(50)   ! SPARE WORD
     &, XSDCB(550,2)   ! CONTAINS THE PRESELECT DCB
C$
      LOGICAL*1
     &  XISPBYTE(100)  ! SPARE BYTE
     &, XSARM(50)      ! ARMED MALFUNCTION FLAGS
     &, XSDCBSET(2)    ! PRESELECT DCB FLAG
     &, XSPRUPD(2)     ! UPDATE PRESELECT FLAG
C$
      LOGICAL*1
     &  DUM0200001(10476),DUM0200002(4624),DUM0200003(86)
     &, DUM0200004(32),DUM0200005(2),DUM0200006(12200)
     &, DUM0200007(7082)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,XISPBYTE,XISPWORD,DUM0200002,XSPRUPD,DUM0200003
     &, XSOFFST,XSDCB,DUM0200004,XSDCBSET,DUM0200005,XSOFFBLK
     &, XSINDEX,XSNXTOFF,XSCRITVAL,DUM0200006,XSARM,DUM0200007
     &, TTLSTEP   
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      LOGICAL*1
     &  YXSTRTXRF1     ! Start of Base 1
C$
C$
      COMMON   /XRFTEST1  /
     &  YXSTRTXRF1
C------------------------------------------------------------------------------
C
C
C
C==============================
C       INCLUDE FILES
C==============================
C
      INCLUDE 'pagecode.inc'     !NOFPC
      INCLUDE 'cae_io.inc'       !NOFPC
      INCLUDE 'stdpres.inc'      !NOFPC
C
C==============================
C        DIRECTIVE LABELS
C==============================
C
      REAL*4
     -           R4_DCB(750)  ! Active step dcb (R*4 representation)
     -,          R4_TDCB(750) ! Active step dcb (I*4 representation)
C
      INTEGER*2
     -           dum1,dum2,dum3,dum4
     -,          NEXTDCBPTR    ! Pointer to next dcb.
     -,          TEMPDCBPTR/1/ ! Pointer to TEMP DCB_BLK
     -,          XTEMPDCBPTR/1/ ! Pointer to TEMP DCB_BLK
     -,          CURRPTR       ! Pointer to current dcb.
     -,          DCB_PTR       ! Present dcb pointer.
     -,          TDCB_PTR      ! Present dcb pointer.
     -,          DCBTYPE       ! Current DCB type
     -,          OPTION        ! DCB option
     -,          NEXT_DCB_OPTION ! Next DCB option
     -,          IPTR          ! Counter
     -,          OLD_TTLSTEP/0/
C
      INTEGER*4
     -           I4_DCB(750)  ! Active step dcb (I*4 representation)
     -,          I4_TDCB(750) ! Active step dcb (I*4 representation)
     -,          DCBSTART      ! Pointer to start of next section in DCB.
     -,          OFF_BLOCK(4,-249:250,2) ! offset block
     -,          ERROR_CODE    ! Error code
     -,          DCBOFFSET     ! CDB label offset in DCB block
     -,          CDB_LAB_OFFSET! CDB label offset in offset block
     -,          PRES_INDX     ! preselect index
C
      LOGICAL*1  HOST_UPD      ! Host update flag
     -,          PRES_FOUND    ! FM+
C
      BYTE       VAL_TABLE(100)! Value table
C
C
C
C
C==============================
C PROGRAM CONTROL VARIABLES
C==============================
C
      LOGICAL*1  FIRSTPASS/.TRUE./      ! First pass flag.
     -,          INIT_FIRSTPASS/.TRUE./ ! Initial states First pass flag
     -,          NEWPAGE                ! New lesson page is requested.
     -,          PAGE_DAT_UPDATE        ! New page.dat
C
      INTEGER*2
     -,          FIRSTLESPG/400/         ! First lesson plan page number
     -,          LASTLESPG/799/          ! Last lesson plan page number
C
     -,          INACTIVE/0/             ! Lesson step inactive
     -,          CURRENT/3/              ! Lesson step is current step
     -,          ACTIVE/2/               ! Lesson step has been activated
     -,          PRESELECT/1/            ! Lesson step preselected
C
      INTEGER*4  STATE,                  ! Current processing state
     -           INITSTATE               ! Current initial state
     -,          NUM_OF_REMOTE           ! Number of local labels on page
     -,          OFF_BLK_READ_POSN       ! Offset block starting position
C
      LOGICAL*1  FOUND                   ! Lesson page search flag
     -,          TLVARMLF                ! Current step actions variable
                                         ! malfunction
C
C !FM+ --- Auto Scrolling
C
     -,          SCROLL_UP
     -,          SCROLL_DOWN
     -,          SCROLLMODE/.FALSE./     ! Set to true if num steps > 5
     -,          SCROLL_CHECK/.FALSE./   !
C !FM-
C
      INTEGER*2
     -           STEP                    ! Lesson step number
     -,          PRESPAGE/-1/            ! Present lesson crt page.
     -,          FIRST_LINE              ! First line in page.
     -,          LAST_LINE               ! Last line in page.
     -,          CURR_LINE               ! Current input line number
C
C !FM+ --- Auto Scrolling
C
     -,          ROW_TO_PIXEL            ! Number of pixels per lesson line
     -,          LESSON_START_PIXEL      ! Pixel start of scroll bar
     -,          LESSONSTPG              ! Lesson start page range
     -,          LESSONENDPG             ! Lesson end page range
     -,          UPPER                   ! Upper SGI number
     -,          LOWER                   ! LOWER SGI number
     -,          SGI_DEV                 ! Current SGI that lesson is on
     -,          EAS_WAIT/0/             ! Waiting for data to be sent to SGI
C
C !FM-
C
      INTEGER*4  I,J,K                   ! Counters.
     -,          INT                     ! Cast to truncated_integer function.
     -,          DIR_POS                 ! Lesson directory step position.
     -,          FLOAT
C
C !FM+ --- Auto Scrolling
C
     -,          NUMSTEP_PG(20)
     -,          NUMSTEP_PREVPG/0/
     -,          PAGE_CNT/1/
C
C !FM-
C
C
C
C==============================
C MISCELLANEOUS PARAMETERS
C==============================
C
      INTEGER*2
     -           MAX_PGENTRY,    ! LES_DIR entry length for each page (bytes)
     -           MAX_PLENTRY     ! LES_DIR entry length for each plan (bytes)
      INTEGER*4  MAX_NPAGES      ! Maximum number of CRT pages
C
      PARAMETER  ( MAX_PGENTRY = 4)
      PARAMETER  ( MAX_PLENTRY = MAX_LPG*MAX_PGENTRY )
      PARAMETER  ( MAX_NPAGES  = 1100)
C
C !FM+ --- Auto Scrolling
C
      PARAMETER
     -           ( ROW_TO_PIXEL=136
     -,            LESSONSTPG=400
     -,            LESSONENDPG=799
     -,            LESSON_START_PIXEL=650
     -,            UPPER=1
     -,            LOWER=3 )
C
C !FM-
C
C
C==============================
C CAE_IO ROUTINE DATA STRUCTURES
C==============================
C
      INTEGER*2  LES_DIR(2*MAX_LPG)      ! Lesson page directory.
     -,          IP_DICT(2,MAX_IPDICT)   ! Lesson Page input dictionary
     -,          DCB_BLK(1500)           ! Lesson Page DCB blocks
     -,          TEMPDCB_BLK(1500)       ! Lesson PROFILE Page DCB blocks
     -,          TDCB_BLK(1500)          ! Lesson Page variable
                                         ! malfunction DCB block
     -,          PAGE_DIR(MAX_NPAGES)    ! Page.dat directory
     -,          PGHEADER(MAX_HDR)       ! Page.dat header
C
      INTEGER*4  LPPGHEADER(64)          ! Lesson Page header
C
C==============================
C    DATA STRUCTURE OFFSETS
C==============================
C
      INTEGER*4  LPOFFSET      ! Lesson offset in pages header.
     -,          LPHDOFFSET    ! Lesson Plan header offset in pages dir
     -,          PDIROFFSET    ! Page Dir offset in pages header.
     -,          IPDIROFFSET   ! Input Dictionary offset in pages header.
     -,          DCBLKOFFSET   ! DCB Blocks offset in pages header.
     -,          OFF_BLOCK_OFFSET   ! Offset Block offset in pages header.
C
C==============================
C PREVIOUS STATUS LABELS
C==============================
C
      REAL*4     OLDLNST/-1/   ! Old tlnst (lesson_#.step_#)
      INTEGER*4  OLD_STEP      ! Old step value.
      INTEGER*4  OLD_COLOR(100)! Previous color of selected step
      INTEGER*2  OLDLESSON/-1/ ! Old lesson number.
C
C
C
C
C
C==============================
C   AUTO - SEQUENCING LABELS
C==============================
C
      INTEGER*2
     &          SUBDCBSTRT,    ! sub_dcb position in DCB
     &          SUBDCBPTR ,    ! sub_dcb position in DCB
     &          TEMP_DCB_PTR,  ! Temporary pointer into DCB block
     &          NEXT_TEMP_DCB_PTR,  ! Temporary pointer into DCB block
     &          AUTOSEQ_OFF/0/,
     &          XSIND,
     &          CRIT_DCB_NUM   ! Criteria DCB number on page
 
      INTEGER*4
     &          NEXT_PRES,     ! Next preselect index
     &          PRES_INDEX,    ! Preselect index
     &          ADDR           ! CDB address function
C
      LOGICAL*1
     &          OLD_LPAUTO,       ! old value of TLAUTO
     &          AUTO_STEP,        ! Current step is an automatic step flag
     &          NEXT_AUTO_STEP,   ! Next step is an automatic step flag
     &          ASTPFND/.FALSE./, ! auto lp step found
     &          LPMANINP/.FALSE./ ! LP manual input
C
C
C==============================
C     LESSON PROFILE LABELS
C==============================
C
      LOGICAL*1
     -          LESSON_PROFILES/.FALSE./! Ship has lesson profiles
     -,         OLD_TLSTART
C
      INTEGER*2
     -          LESSON_PROFILE_PG    ! Lesson Profile page displayed
     -,         OLD_PROFILE          ! Old Lesson Profile page displayed
     -,         NUM_AUTO_STEPS
     -,         XSENTRY
     -,         NUM_XSENTRIES
C
C==============================
C     PRESELECT LABELS
C==============================
C
      INTEGER*4
     &          XSDCBSTART             ! Sub DCB starting offset
     &         ,CRITSTART              ! Criteria starting offset
     &         ,CRITINDX               ! Index in criteria block
     &         ,INDX                   ! Index in XSTABLE
     &         ,NUM_STV                ! Number of Values to set
     &         ,NUM_CRITERIA           ! Number of criteria
     &         ,II                     ! Loop counter
     &         ,OFF_IND
     &         ,BASE
     &         ,ICNT
C
      INTEGER*2
     &          I2ICNT(2)
     &         ,OFF_INDX(0:100)
     &         ,DCB(0:100)
     &         ,I2OFF_IND(2)
C
      INTEGER*4
     -          HOST_DATA,
     -          LESSON_DATA,
     -          OFFSET_EAS,
     -          BASE_EAS,
     -          BYTE_EAS,
     -          TYPE_EAS
C
      PARAMETER (
     -          HOST_DATA  = 1,
     -          LESSON_DATA= 2,
     -          BASE_EAS   = 1,
     -          OFFSET_EAS = 2,
     -          BYTE_EAS   = 3,
     -          TYPE_EAS   = 4  )
C
 
C
C==============================
C   CAE_IO ROUTINE PARAMETERS
C==============================
C
      INTEGER*4
     -          BYTENUM/0/     ! Byte number
     -,         BYTECNT        ! Byte count
     -,         RECNUM         ! Record number
     -,         RECSIZE/256/   ! Record size
     -,         FLEN           ! PAGE.DAT path name length
     -,         FD             ! PAGE.DAT file descriptor
     -,         REVSTATUS      ! Return status from REV_CURR routine
     -,         READ_STATUS    ! Return status from CAE_IO routines
     -,         OPEN_STATUS    ! Return status from CAE_IO routines
     -,         READ_RSTAT     ! Return status from CAE_IO routines
     -,         OPEN_RSTAT     ! Return status from CAE_IO routines
     -,         O_FLAG         ! Open status for CAE_IO routine
     -,         XOERRCOD(20)   ! Error codes from bad returns
     -,         OFFSET         ! Offset to be used by CAE_IO routines
     -,         CAE_TRNL       ! Translate logical name function
     -,         ID             ! Used in REV_CURR call
C
      CHARACTER
     -          FNAME*64,      ! PAGE.DAT file name string
     -          FULLNAME*64    ! PAGE.DAT file name string with revision
C
      LOGICAL  READ_IN_PROGRESS/.FALSE./, IO_END
C
C --- LABELS TO BE ADDED IN CDB
C
C
C==============================
C          EQUIVALENCES
C==============================
C
      EQUIVALENCE
     -           (DCB_BLK, R4_DCB)
     -,          (DCB_BLK, I4_DCB)
     -,          (TDCB_BLK, I4_TDCB)
     -,          (TDCB_BLK, R4_TDCB)
     -,          (I2OFF_IND, OFF_IND )
     -,          (I2ICNT   , ICNT    )
C
C
C' Ident
      CHARACTER*55 RVL
     &              /
     -  '$Source: usd8tl.for.58 25Jul1992 00:07 usd8 Baon   $'/
C
C
C
C
C
C
C==============================
C     START OF SUBROUTINE
C==============================
C
C
      ENTRY TLESSEX
C
C
      IF (READ_IN_PROGRESS) THEN
          IF (READ_STATUS.NE.1) THEN
             RETURN
             XOERRCOD(8) = READ_STATUS
          ELSE IF (READ_RSTAT.NE.1) THEN
             RETURN
             XOERRCOD(9) = READ_RSTAT
          ELSE
             READ_IN_PROGRESS = .FALSE.
             READ_STATUS = 0
             READ_RSTAT = 0
          ENDIF
      ENDIF
C
C
C
C==============================
C     FIRST PASS SECTION
C==============================
C
C
      IF (FIRSTPASS) THEN
        FIRSTPASS  = .FALSE.
        XSNXTOFF = 1             ! should be done in cdb init
        INIT_FIRSTPASS = .TRUE.
        INITSTATE = 1
        IF ( LESSON_PROFILES ) STATE = 8
      ENDIF
C
C
C
C
C==============================
C     UPDATE LESSON DIRECTORY
C==============================
C
       IF (TLESSUPD) THEN
         TLESSUPD = .FALSE.
         PAGE_DAT_UPDATE = .TRUE.
         FIRSTPASS = .TRUE.
         CALL CAE_IO_CLOSE(  READ_STATUS,
     -                       READ_RSTAT,
     -                       FD     )
         RETURN
       ENDIF
C
C
C------------------------------------------------
C --- If variable malfunction flag is set on,
C     then activate preprogrammed variable malf
C------------------------------------------------
C
      IF (TLVARMLF) THEN
         TLVARMLF = .FALSE.
         CALL XDVMLFIN (TDCB_BLK(DCB_PTR),
     +                  I4_TDCB(DCB_PTR/2+1),
     +                  R4_TDCB(DCB_PTR/2+1),
     +                  ERROR_CODE,
     +                  OFF_BLOCK,
     +                  VAL_TABLE,
     +                  HOST_UPD      )
      ENDIF
C
C
C
C
C=================================================
C !FM+ --- Auto Scrolling
C=================================================
C
      IF ( .NOT. SCROLL_CHECK) THEN
        IF ( SCROLLMODE ) THEN
          IF (XISPBYTE(1)) THEN
            IF (EAS_WAIT .LT. 0)  THEN
              XISPWORD(1) = 0
              XISPBYTE(1) = .FALSE.
              EAS_WAIT    = 0
            ELSE
              EAS_WAIT    = EAS_WAIT - 1
              GO TO 9999                 ! Sending data to SGI
            ENDIF
          ENDIF
C
C --- Page change in progess scrolling to current step
C
          IF ( NEWPAGE ) THEN
            XISPWORD(1) = LESSON_START_PIXEL-((TLSTEP-NUMSTEP_PREVPG)
     &                                       * ROW_TO_PIXEL)
            XISPBYTE(1) = .TRUE.
            EAS_WAIT    = 5
            NEWPAGE     = .FALSE.
          ENDIF
        ELSE
          NEWPAGE     = .FALSE.
          XISPWORD(1) = 0
          XISPBYTE(1) = .FALSE.
          EAS_WAIT    = 0
        ENDIF
      ENDIF
C
C !FM-
C
C==============================
C   RETURN TO LESSON PLAN PAGE
C==============================
C
      IF (TLRTPLN.AND..NOT.LESSON_PROFILES) THEN
C'usd8        TAPAGE(1) = TLPAGE
        TAPAGE(SGI_DEV) = TLPAGE
C
C !FM+ --- Auto Scrolling
C
        IF ( SCROLLMODE ) THEN
          XISPWORD(1) = LESSON_START_PIXEL - ((TLSTEP-NUMSTEP_PREVPG)
     &                                       * ROW_TO_PIXEL)
          XISPBYTE(1) = .TRUE.
          EAS_WAIT    = 5
        ENDIF
C !FM-
        TLRTPLN  = .FALSE.
      ENDIF
C
C==============================
C   LESSON PLAN AVAILABLE
C==============================
C
C'usd8      IF (TAPAGE(1).EQ.TLPAGE) THEN
      IF (TAPAGE(SGI_DEV).EQ.TLPAGE) THEN
        TLSTEPAV = .TRUE.
      ENDIF
C
C==============================
C   STEP/SKIP THRU LESSON STEPS
C==============================
C
      IF (TLSTEPDO) THEN
        TLSTEPDO = .FALSE.
C
C -- Check not to go beyond step
C
        IF( TLNST .GT. 1.0) THEN
          TLNST = TLNST + 0.01
          IF (STEP .GE. LAST_LINE .AND. (PAGE_DIR
     .        (LES_DIR(DIR_POS+1)) .EQ. -1 .OR. LES_DIR(DIR_POS+1)
     .                                               .EQ. -1 )) THEN
            TLNST = TLNST - 0.01
          ENDIF
        ENDIF
C
      ELSE IF (TLSTEPUP) THEN
        IF (TLSTEP.GT.1) TLNST = TLNST - 0.01
        TLSTEPUP   = .FALSE.
      ELSE IF (TLSTEPSK) THEN
        TLNST      = TLNST + 0.02
        TLSTEPSK = .FALSE.
      ENDIF
C
C==============================
C  LESSON AUTO MODE AVAILABLE
C==============================
C
      IF ( TLNST.GT.1.0 ) THEN
        TLAUTOAV = .TRUE.
      ELSE
        TLAUTOAV = .FALSE.
      ENDIF
C
C==============================
C  RETURN TO LESSON AVAILABLE
C==============================
C
      IF ( TLNST.GT.1.0.AND.
C'usd8     -    (TAPAGE(1).LT.FIRSTLESPG.OR.
C'usd8     -     TAPAGE(1).GT.LASTLESPG ) ) THEN
     -    (TAPAGE(SGI_DEV).LT.FIRSTLESPG.OR.
     -     TAPAGE(SGI_DEV).GT.LASTLESPG ) ) THEN
        TLRTPLAV = .TRUE.
      ELSE
        TLRTPLAV = .FALSE.
      ENDIF
C
C
C==============================
C   END/RESET LESSON PLAN
C==============================
C
      IF (TLEND.AND..NOT.LESSON_PROFILES) THEN
        TLEND = .FALSE.
C'usd8        TAPAGE(1) = 1
        TAPAGE(SGI_DEV) = 1
        TLNST = 0.00
        TLESSON =0
        TLSTEP =0
         DO I=1,100
          TLSELECT(I) = INACTIVE
          OLD_COLOR(I) = INACTIVE
        ENDDO
        TLAUTO = .FALSE.
        OLDLNST = 0.00
        OLD_TTLSTEP = 0
        OLDLESSON = 0
        PRESPAGE = -1
      ENDIF
C
C
C
C==============================
C CHECK FOR INPUT ON LESSON PAGE
C==============================
C
       IF (TTLSTEP .GT. 0) THEN
C
          LPMANINP = .TRUE.                     ! LP manual input detected
          TLNST = TLESSON + ((TTLSTEP)*0.01)
C
          TLSELECT(TLSTEP)  = OLD_COLOR(TLSTEP) ! restore the prev color
          TLSELECT(TTLSTEP) = ACTIVE
C
          TLACTN  = .TRUE.
          TLAUTOR = .TRUE.
C
          TLAUTO  = .FALSE.
C
C -- reset LP steps from the newly input step to the last selected step
C
         IF (TTLSTEP .LT. TLSTEP) THEN
C !FM          DO II=TTLSTEP+1, TLSTEP
          DO II=TTLSTEP+1, 100
            TLSELECT(II)  = INACTIVE
            OLD_COLOR(II) = INACTIVE
          ENDDO
C
          II = MAX_PRES
          DO WHILE (II.GE.1)
            IF (XSARM(II)) THEN
              IF (XSTABLE(XSINDEX(II)+2) .GT. (TTLSTEP) .AND.
     +            XSTABLE(XSINDEX(II)+2) .LE. (TLSTEP ) ) THEN
                PRES_FOUND = .TRUE.
              ELSE
                PRES_FOUND = .FALSE.
              ENDIF
              IF ( PRES_FOUND ) THEN
                PRES_INDEX     = XSINDEX(II)
                XSINDEX(II) = 0
                NEXT_PRES      = PRES_INDEX + XSTABLE(PRES_INDEX)
C
C -- Perform compress on XSTABLE for the deleted preselect item
C
                DO I = NEXT_PRES, XSNXTOFF - 1
                  DO J = 1, MAX_PRES
                    IF (XSINDEX(J).EQ.I) THEN
                      XSINDEX(J) = PRES_INDEX
                    ENDIF
                  ENDDO
                  XSTABLE(PRES_INDEX) = XSTABLE(I)
                  PRES_INDEX = PRES_INDEX + 1
                ENDDO
C
                XSNXTOFF = PRES_INDEX
                XSTABLE(XSNXTOFF) = -1
                XSARM(II)         = .FALSE.
                XSOFFST(II)       = 0
C !FM+
                DO J = 1, MAX_CRIT
                  XSCRITVAL(II,J) = NULL_CRIT
                ENDDO
C !FM-
              ENDIF ! auto lesson plan found
            ENDIF   ! xsarm
            II = II - 1
          ENDDO
         ENDIF
         TTLSTEP = 0
       ENDIF
C
C
C
C=======================================================
C                   INITIAL STATES
C=======================================================
C
C Execution of these 3 states is carried out only once
C a lesson has been started.
C
C
      IF (INIT_FIRSTPASS) THEN
C
         GOTO  ( 1,          ! Open PAGE.DAT
     +           2,          ! Read in page.dat header
     +           3           ! Read in page.dat directory
     +             ) INITSTATE
         GOTO 9999
C
C
C=================
C INITIAL STATE 1
C=================
C
C=======================================================
C                  OPEN PAGE.DAT FILE
C=======================================================
C
 1      CONTINUE
C
C Uncomment this code when the module is to be entered in SIMEX
C
CFG+++
        I = CAE_TRNL('PAGE_DAT',FLEN,FNAME,1)
        IF (I .NE. 1 ) THEN   ! default to current diretory
           FNAME(1:8) = 'page.dat'
           FLEN  = 9
           FNAME(FLEN:FLEN) = '\0'
        ELSE
           FNAME(FLEN+1:FLEN+1) = '\0'
        ENDIF
C !FM+
C !FM        O_FLAG = 7
        O_FLAG = 5
C !FM-
CFG+++
C
        OPEN_STATUS = 0
        OPEN_RSTAT = 0
 
C Uncomment this code when the module is running in background
C ( a PAGE.DAT file with no revision is required in the pg directory)
C
CBG+++
C        O_FLAG = 5
C        FNAME(1:8) = 'page.dat'
C        FNAME(9:9) = '\0'
CBG+++
        CALL CAE_IO_OPEN( OPEN_STATUS, OPEN_RSTAT, FD,
     .                    %VAL(RECSIZE), FNAME, %VAL(O_FLAG) )
C
C
        INITSTATE = 2
        GOTO 9999
C
C
C=================
C INITIAL STATE 2
C=================
C
C=======================================================
C                  READ PAGE.DAT FILE HEADER
C=======================================================
C
C     -- Check if page data file is opened
C
 2      IF ( OPEN_STATUS .NE. 1) THEN
          XOERRCOD(1) = OPEN_STATUS
          RETURN
        ELSE IF (OPEN_RSTAT .NE.1 ) THEN
          XOERRCOD(2) = OPEN_RSTAT
          RETURN
        ELSE
C
C     -- Read page file header
C
          RECNUM  = 0
          OFFSET = 0
          BYTECNT = MAX_HDR*2            ! was 4 see pagecode.inc
          READ_STATUS = 0
          READ_RSTAT = 0
          CALL CAE_IO_READ( READ_STATUS,
     .                    READ_RSTAT,
     .                    %VAL(FD),
     .                    %VAL(RECSIZE),
     .                    PGHEADER,
     .                    RECNUM,
     .                    BYTECNT,
     .                    OFFSET )
           READ_IN_PROGRESS = .TRUE.
           INITSTATE = 3
        ENDIF
        GOTO 9999
C
C
C
C=================
C INITIAL STATE 3
C=================
C
C=======================================================
C                 READ CRT PAGE DIRECTORY
C=======================================================
C
C     -- Check if pages file header is read
C
 3        PDIROFFSET  = PGHEADER(FIL_CRTOFF)
C
C     -- Read pages directory
C
          BYTECNT  = MAX_NPAGES*2
          RECNUM  = 0
          OFFSET = PDIROFFSET - 1
          READ_STATUS = 0
          READ_RSTAT = 0
          CALL CAE_IO_READ( READ_STATUS,
     .                    READ_RSTAT,
     .                    %VAL(FD),
     .                    %VAL(RECSIZE),
     .                    PAGE_DIR,
     .                    RECNUM,
     .                    BYTECNT,
     .                    OFFSET )
           READ_IN_PROGRESS = .TRUE.
C
        IF (.NOT.LESSON_PROFILES) STATE = 1
        INIT_FIRSTPASS = .FALSE.
        GOTO 9999
C
        ENDIF
            !< ===== END OF INITIAL STATES PROCESSING =====>
C
C
C=======================================================
C                 PROCESSING STATES
C=======================================================
C
C
C For ships with LESSON PROFILES rather than the old style
C lesson plan pages , states 1,2,4,6,7 need not be executed
C
C
       GOTO  ( 10,         ! Monitor lesson plan activation
     +         20,         ! Read in lesson plan directory
     +         30,         ! Read in lesson plan page header
     +         40,         ! Read in input dictionary
     +         50,         ! Read in lesson page DCB blocks
     +         60,         ! Read in lesson page offset block
     +         70,         ! Monitor for automatically triggered step
     +         80,         ! Lesson Profiles handling
     +         90,         ! Read in lesson profile page header
     +         100,        ! Read in lesson profile page dcbs
     +         110,        ! Extract lesson profile auto steps
     +         120,        ! Get lesson profile offset block
     +         130         ! Monitor lesson start
     +           ) STATE
       GOTO  9999
C
C===================
C PROCESSING STATE 1
C===================
C
C=======================================================
C       ACTIVATION OF LESSON/STEP  MONITORING
C=======================================================
C
 10   IF (TLNST .EQ. OLDLNST) THEN
 
        IF (TLACTN .AND. TLNST .GT. 1.0) THEN
C
          IF (LPMANINP) THEN            ! if manual input should not
            LPMANINP = .FALSE.          ! reset astpfnd
          ELSE
            ASTPFND = .FALSE.           ! Reset auto step
          ENDIF
          TLNST = TLNST + 0.01          ! Get next Step...
C
          IF (CURR_LINE .LT. LPPGHEADER(PAG_IPLEN)/4) THEN
            IF (.NOT.NEXT_AUTO_STEP) THEN
              NEXTDCBPTR = IP_DICT(1,CURR_LINE+1)
            ELSE
              NEXTDCBPTR = NEXT_TEMP_DCB_PTR
            ENDIF
          ELSE
             NEXTDCBPTR = IP_DICT(1,CURR_LINE) + 2 +
     +                    DCB_BLK(IP_DICT(1,CURR_LINE)+DCB_SIZE)
          ENDIF
C
          IF ( TLAUTOR ) THEN  ! associated boolean that
                               ! should be coded with the TLACTN
                               ! control to identify the action as
                               ! one set by the instructor and not
                               ! as a preselect becomming active
             IF (TLAUTO.AND.AUTO_STEP) THEN
                TLAUTO = .FALSE.
             ENDIF
             DCB_PTR = IP_DICT(1,CURR_LINE)
             TLAUTOR = .FALSE.
          ELSE
             DCB_PTR = TEMP_DCB_PTR
          ENDIF
C
          DO WHILE ((DCB_PTR + DCB_BLK(DCB_PTR + DCB_SIZE)
     &                                   .LT.NEXTDCBPTR)
     &             .AND. DCB_BLK(DCB_PTR + DCB_SIZE) .GT. 0)
             DCBTYPE =  DCB_BLK(DCB_PTR + DCB_TYPE )
C
C -- If setvalue DCB
C
         IF (DCBTYPE .EQ. SET_VALUE) THEN
C
C
C -- If display code is other than 0 then no true/false strings are
C    being used and sub dcb information will begin at positon 21 of the
C    setvale dcb , otherwise sub dcb information will begin at position 31
C
C
          IF (DCB_BLK(DCB_PTR+STV_DP_CODE).NE.0) THEN
              SUBDCBSTRT = STV_DSP_STRT
          ELSE
              SUBDCBSTRT = STV_SUB_STRT
          ENDIF
C
          OPTION   = DCB_BLK(DCB_PTR+DCB_OPTIONS)
          NUM_CRITERIA = DCB_BLK(DCB_PTR + STV_CRIT_NUM)
C
C -- If criteria exist prepare the XSDCB for XSRT so that the
C    preselect can be entered into the XSTABLE table
C
          IF (NUM_CRITERIA.NE.0) THEN
              NUM_STV = DCB_BLK(DCB_PTR + STV_VALS_NUM)
              CRITSTART = SUBDCBSTRT
              XSDCBSTART  = CRITSTART + NUM_CRITERIA * STV_SBL_SIZ
C
              ICNT = 0
              CRITINDX = CRITSTART
C
C --        Process criteria data
C
              DO I = 1, NUM_CRITERIA
               I2OFF_IND(1) = DCB_BLK(DCB_PTR +CRITINDX+STV_OFFSET)
               I2OFF_IND(2) = DCB_BLK(DCB_PTR +CRITINDX+STV_OFFSET+1)
C
               ICNT = ICNT + 1
C
               OFF_INDX(CRITINDX+STV_OFFSET)   = I2ICNT(1)
               OFF_INDX(CRITINDX+STV_OFFSET+1) = I2ICNT(2)
C
               XSOFFBLK(BASE_EAS,ICNT,LESSON_DATA)
     &                      = OFF_BLOCK(BASE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(OFFSET_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(OFFSET_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(BYTE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(BYTE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(TYPE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(TYPE_EAS,OFF_IND,HOST_DATA)
C
               CRITINDX = CRITINDX + STV_SBL_SIZ
              ENDDO
C
              DO II = 1, NUM_STV
               I2OFF_IND(1) = DCB_BLK(DCB_PTR +XSDCBSTART+STV_OFFSET)
               I2OFF_IND(2) = DCB_BLK(DCB_PTR +XSDCBSTART+STV_OFFSET+1)
C
               ICNT = ICNT + 1
C
               OFF_INDX(XSDCBSTART+STV_OFFSET)   = I2ICNT(1)
               OFF_INDX(XSDCBSTART+STV_OFFSET+1) = I2ICNT(2)
C
               XSOFFBLK(BASE_EAS,ICNT,LESSON_DATA)
     &                      = OFF_BLOCK(BASE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(OFFSET_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(OFFSET_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(BYTE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(BYTE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(TYPE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(TYPE_EAS,OFF_IND,HOST_DATA)
C
               XSDCBSTART = XSDCBSTART + STV_SBL_SIZ
              ENDDO
C
              DO II = 1, DCB_BLK(DCB_PTR +DCB_SIZE)
               XSDCB(II,LESSON_DATA) = DCB_BLK(DCB_PTR + II-1)
               IF ( OFF_INDX(II-1).NE.0 ) THEN
                  XSDCB(II,LESSON_DATA)    = OFF_INDX(II-1)
                  OFF_INDX(II-1) = 0
               ENDIF
              ENDDO
C
              XSDCBSET(LESSON_DATA) = .TRUE.
CNT           TLSELECT(TLSTEP) = PRESELECT
C
             ELSE
 
              DCBSTART = DCB_PTR
C
C --- Call directives to deposit value into the CDB
C
              CALL XDSETIN ( DCB_BLK(DCB_PTR),
     +                  I4_DCB(DCB_PTR/2+1),
     +                  R4_DCB(DCB_PTR/2+1),
     +                  ERROR_CODE,
     +                  OFF_BLOCK,
     +                  VAL_TABLE,
     +                  HOST_UPD      )
             ENDIF
C
C --- If dcbtype is boolean, call boolean directive input
C
            ELSE IF (DCBTYPE .EQ. BOOLEAN) THEN
C
                 CALL XDBOOLIN ( DCB_BLK(DCB_PTR),
     +                           I4_DCB(DCB_PTR/2+1),
     +                           R4_DCB(DCB_PTR/2+1),
     +                           ERROR_CODE,
     +                           OFF_BLOCK,
     +                           VAL_TABLE,
     +                           HOST_UPD      )
C
C --- If dcbtype is var malf, copy into temporary arrary
C
            ELSE IF (DCBTYPE .EQ. VAR_MALF) THEN
C
                 DO IPTR = DCB_PTR, 250
                   TDCB_BLK(IPTR) = DCB_BLK(IPTR)
                 ENDDO
                 TDCB_PTR = DCB_PTR
C
            ENDIF
C
            DCB_PTR = DCB_PTR + DCB_BLK(DCB_PTR + DCB_SIZE) + 2
          ENDDO
C
C
CNT       IF (NUM_CRITERIA.EQ.0) THEN
          TLSELECT(TLSTEP) = ACTIVE ! Put color on active line
CNT       ENDIF
C
C !FM+ --- Auto Scrolling
C
          DO II=TLSTEP+1, 100
            TLSELECT(II)  = INACTIVE
            OLD_COLOR(II) = INACTIVE
          ENDDO
C !FM-
C
         ENDIF
         TLACTN = .FALSE.                  ! End of Action_Step
C
      ENDIF                                ! End of Activation
C
C
C-----------------------------------------------
C       MONITOR LESSON # / STEP # CHANGES
C-----------------------------------------------
C
C
      IF (TLNST .NE. OLDLNST) THEN
C
C
        IF (ASTPFND) THEN ! Auto step found
C !FM+
          ASTPFND = .FALSE.
C !FM-
          II = 1
          PRES_FOUND = .FALSE.
          DO WHILE (II .LE. MAX_PRES .AND. .NOT. PRES_FOUND)
            IF (XSARM(II).NE.0) THEN
CNT           IF ( XSOFFST(II) .GE. TL_START .AND.
CNT  +             XSOFFST(II) .LT. TL_END ) PRES_FOUND = .TRUE.
              IF (XSTABLE(XSINDEX(II)+2) .EQ. (TLSTEP))
     +          PRES_FOUND = .TRUE.
              IF ( PRES_FOUND ) THEN
                PRES_INDEX     = XSINDEX(II)
                XSINDEX(II) = 0
                NEXT_PRES      = PRES_INDEX + XSTABLE(PRES_INDEX)
C
C -- Perform compress on XSTABLE for the deleted preselect item
C
                DO I = NEXT_PRES, XSNXTOFF - 1
                  DO J = 1, MAX_PRES
                    IF (XSINDEX(J).EQ.I) THEN
                      XSINDEX(J) = PRES_INDEX
                    ENDIF
                  ENDDO
                  XSTABLE(PRES_INDEX) = XSTABLE(I)
                  PRES_INDEX = PRES_INDEX + 1
                ENDDO
C
                XSNXTOFF = PRES_INDEX
                XSTABLE(XSNXTOFF) = -1
                XSARM(II)         = .FALSE.
                XSOFFST(II)       = 0
C !FM+
                DO J = 1, MAX_CRIT
                  XSCRITVAL(II,J) = NULL_CRIT
                ENDDO
C !FM-
C !FM           TLSELECT(TLSTEP) = OLD_COLOR(TLSTEP)   !!!??? check old_color
              ENDIF ! auto lesson plan found
            ENDIF   ! xsarm
            II = II + 1
          ENDDO
        ENDIF
C
         IF (TLNST.LT.1.0) THEN
             IF (TLESSON .GT. 0) THEN
                TLNST  = FLOAT(TLESSON)
             ELSE
                TLPAGE = -1
                OLDLESSON = TLESSON
                OLDLNST = TLNST
                RETURN
             ENDIF
         ENDIF
C
         TLESSON   = INT(TLNST)
         OLD_STEP  = TLSTEP
C
         STEP    = INT((TLNST+0.004) * 100.0 - TLESSON * 100)
C
C --- Color the line as preselected but save its old color in case
C     that we are just stepping through
C
                     OLD_COLOR(STEP) = TLSELECT(STEP)
                     TLSELECT(STEP) = CURRENT
C
                     IF ((TLSELECT(OLD_STEP).EQ.CURRENT).AND.
     -                   (TLSELECT(OLD_STEP).NE.ACTIVE) )
     -                    TLSELECT(OLD_STEP) = OLD_COLOR(OLD_STEP)
                   ! Old step may have been actionned so keep the
                   ! active color otherwise put back the old color
C
         IF (TLESSON.GT.0 .AND. STEP.EQ.0) THEN
            TLSTEP = 1
            TLNST  = TLNST + 0.01
C         ELSE IF (TLESSON.EQ.OLDLESSON .AND. STEP.EQ.0) THEN
C            TLSTEP = OLD_STEP
C            TLNST  = OLDLESSON + TLSTEP/100.0
         ENDIF
C
         IF (TLESSON.GT.0 .AND. TLESSON.LT.100) THEN
            IF (TLESSON.NE.OLDLESSON .OR. OLDLNST.EQ.0) THEN
               DO I = 1, 100
                  TLSELECT(I) = INACTIVE
                  OLD_COLOR(I) = INACTIVE
               ENDDO
               TLSELECT(1) = CURRENT
C'usd8+ ---------------------------------------------------- 12-Jul-1992
               IF (TLESSON.NE.OLDLESSON) THEN
                 TLTIME = 0.0
               ENDIF
C'usd8- -----------------------------------------------------------------
               STATE = 2
            ELSE
               STATE = 3
            ENDIF
         ENDIF
C
       ENDIF
       OLDLESSON = TLESSON
       OLDLNST = TLNST
C
       IF (PAGE_DAT_UPDATE) THEN
           PAGE_DAT_UPDATE = .FALSE.
           STATE = 2
       ENDIF
C
       GO TO 9999
C
C
C
C===================
C PROCESSING STATE 2
C===================
C
C=======================================================
C              READ IN LESSON PLAN DIRECTORY
C=======================================================
C
C
C     -- Check if pages directory is read
C
 20       LPOFFSET  = PGHEADER(FIL_LESOFF) +
     +            MAX_PLENTRY * (TLESSON - 1)
C
C     -- Read Lesson Plan directory
C
          BYTECNT  = MAX_LPG*4
          RECNUM  = 0
          OFFSET = LPOFFSET - 1
          READ_STATUS = 0
          READ_RSTAT = 0
          CALL CAE_IO_READ( READ_STATUS,
     .                    READ_RSTAT,
     .                    %VAL(FD),
     .                    %VAL(RECSIZE),
     .                    LES_DIR,
     .                    RECNUM,
     .                    BYTECNT,
     .                    OFFSET )
           READ_IN_PROGRESS = .TRUE.
C
C
          STATE = 3
C
      GOTO 9999
C
C
C
C
C===================
C PROCESSING STATE 3
C===================
C
C=======================================================
C                 READ LESSON PAGE HEADER
C=======================================================
C
 30    FOUND = .FALSE.
C
       IF (( STEP .GE. 1 .AND. LES_DIR(2) .GE. 1) .AND.
     .      PAGE_DIR(LES_DIR(1)) .GT. 0) THEN
C
         DO I = 2,78,2
           IF (    STEP .GE. LES_DIR(I)   .AND.
     .          ( (STEP .LT.LES_DIR(I+2) .OR.
     .            (LES_DIR(I+2) .EQ. -1) .OR.
     .            (PAGE_DIR(LES_DIR(I+1)).EQ.1.AND.
     .             LES_DIR(I+1).GT. 0 )    )).AND.
     .          .NOT. FOUND )     THEN
C
              DIR_POS = I
              FOUND   = .TRUE.
           ENDIF
         ENDDO
       ENDIF
C
       IF (FOUND .AND. LES_DIR(DIR_POS-1) .NE. -1) THEN
           TLPAGE    = LES_DIR(DIR_POS - 1)
C
C !FM+ --- Auto Scrolling
C
           IF (TAPAGE(UPPER) .EQ. TLPAGE) THEN
             SGI_DEV = UPPER
           ELSE
             SGI_DEV = LOWER
           ENDIF
C !FM-
           TLSTEP    = STEP
           CURR_LINE = STEP - LES_DIR(DIR_POS) + 1
C
 
           IF (TLPAGE .NE. PRESPAGE) THEN
C
C !FM+ --- Auto Scrolling
C
              SCROLLMODE = .FALSE.  ! By default no scrolling
              IF ( TLSTEP .EQ. 1  ) THEN  ! Start of lesson
                PAGE_CNT         = 1      ! Init page counter
                NUMSTEP_PG(PAGE_CNT) = 0  ! Init num steps on prev page
              ELSE
                IF ( TLPAGE .GT. PRESPAGE ) THEN ! Page Forward
                  PAGE_CNT = PAGE_CNT + 1
                  NUMSTEP_PG(PAGE_CNT) = TLSTEP - 1
                ELSE ! Page backward
                  PAGE_CNT = PAGE_CNT - 1
                ENDIF
              ENDIF
              NUMSTEP_PREVPG = NUMSTEP_PG(PAGE_CNT)
C !FM-
              PRESPAGE   = TLPAGE
C
C !FM+ --- Automatic page change if the new step is not
C          in the current page
C
              IF (TAPAGE(SGI_DEV) .NE. TLPAGE) THEN
                TAPAGE(SGI_DEV) = TLPAGE
                NEWPAGE    = .TRUE.
              ENDIF
C !FM- ------------------------------------------------
C
              LPHDOFFSET = 1
              BYTECNT  = MAX_PGHDR*4
              RECNUM   = PAGE_DIR(TLPAGE) - 1
              OFFSET = LPHDOFFSET - 1
              READ_STATUS = 0
              READ_RSTAT = 0
              CALL CAE_IO_READ( READ_STATUS,
     .                          READ_RSTAT,
     .                          %VAL(FD),
     .                          %VAL(RECSIZE),
     .                          LPPGHEADER,
     .                          RECNUM,
     .                          BYTECNT,
     .                          OFFSET )
             READ_IN_PROGRESS = .TRUE.
C
             SCROLL_CHECK = .TRUE.
             STATE = 4
             GOTO 9999
           ELSE
             STATE = 1
                         ! New step is on the same page therefore do not
                         ! need to read header,input dict and dcb's again
           ENDIF
C
C !FM+ --- Auto Scrolling
C
           IF ( SCROLLMODE ) THEN
            XISPWORD(1) = LESSON_START_PIXEL-((TLSTEP-NUMSTEP_PREVPG)
     &                                       * ROW_TO_PIXEL)
            XISPBYTE(1) = .TRUE.
            EAS_WAIT    = 5
           ENDIF
C !FM-
 
       ELSE
           TLNST      = 0.0
           TLESSON    = 0
           OLDLESSON  = TLESSON
           OLDLNST    = 0.0
           STATE  = 1
           GOTO 9999
       ENDIF
C
C Check this newly preselected step to see if it is an automatically
C triggered step
C
C If the new step is on a new page , this section will be executed
C only after all of the new page information has been read, that is,
C after it has gone through states 4,5, and 6
C
 70    CONTINUE
                       ! If in AUTO mode , check if the current step
                       ! is an auto step
 
       TEMP_DCB_PTR = IP_DICT(1, CURR_LINE)
       NEXT_TEMP_DCB_PTR = IP_DICT(1,CURR_LINE+1)
 
       OPTION  = DCB_BLK(TEMP_DCB_PTR+DCB_OPTIONS)
       NEXT_DCB_OPTION = DCB_BLK(NEXT_TEMP_DCB_PTR+DCB_OPTIONS)
 
       AUTO_STEP = ( IAND(OPTION,'0010'X) .NE.0 )
       NEXT_AUTO_STEP = ( IAND(NEXT_DCB_OPTION,'0010'X) .NE.0 )
 
       IF ( NEXT_AUTO_STEP) THEN
CNT      NEXT_TEMP_DCB_PTR = IP_DICT(1,CURR_LINE) +
CNT  .                       DCB_BLK(IP_DICT(1,CURR_LINE)+DCB_SIZE)+2
CNT      CRIT_DCB_NUM = IP_DICT(2,CURR_LINE+1) - 1   ! Previous DCB
CNT      NEXT_TEMP_DCB_PTR = 1
CNT      DO K=1,CRIT_DCB_NUM-1
CNT        NEXT_TEMP_DCB_PTR = NEXT_TEMP_DCB_PTR +
CNT  .                      DCB_BLK(NEXT_TEMP_DCB_PTR+DCB_SIZE) + 2
CNT      ENDDO
         NEXT_TEMP_DCB_PTR = IP_DICT(1,CURR_LINE)
         DO WHILE ((NEXT_TEMP_DCB_PTR+
     -              DCB_BLK(NEXT_TEMP_DCB_PTR+DCB_SIZE)+2).LT.
     -              IP_DICT(1,CURR_LINE+1))      ! loop until the dcb
C                                                ! next to the current step
C                                                ! is found
            NEXT_TEMP_DCB_PTR = NEXT_TEMP_DCB_PTR +
     -                          DCB_BLK(NEXT_TEMP_DCB_PTR+DCB_SIZE)+2
         ENDDO
       ENDIF
 
C      Set the triggers for the auto step by calling XDSETIN.
C      The triggers will be entered into the XSTABLE and when all
C      of them are met, the TLACTN flag will be activated and the
C      dcb following this auto step will be processed
C
       IF (AUTO_STEP.AND.TLAUTO) THEN
C
C      The current step is an auto step . The previous dcb will
C      be the criteria dcb which will set TLACTN. Extract this dcb.
C
C         temp_dcb_ptr = previous DCB offset + previos DCB size      !FM+
C
CNT          TEMP_DCB_PTR = DCB_BLK(IP_DICT(1,CURR_LINE-1)+DCB_SIZE)    !FM+
CNT          TEMP_DCB_PTR =
CNT     .         IP_DICT(1,CURR_LINE-1) + TEMP_DCB_PTR+DCB_SIZE+2      !FM+
CNT       CRIT_DCB_NUM = IP_DICT(2,CURR_LINE) - 1   ! Previous DCB
CNT       TEMP_DCB_PTR = 1
CNT       DO K=1,CRIT_DCB_NUM-1
CNT         TEMP_DCB_PTR = TEMP_DCB_PTR +
CNT  .                     DCB_BLK(TEMP_DCB_PTR+DCB_SIZE) + 2
CNT       ENDDO
          TEMP_DCB_PTR = IP_DICT(1,CURR_LINE-1)  ! previous step
          DO WHILE ((TEMP_DCB_PTR+DCB_BLK(TEMP_DCB_PTR+DCB_SIZE)+2).LT.
     -              IP_DICT(1,CURR_LINE))        ! loop until the dcb
C                                                ! previous to the current step
C                                                ! is found
            TEMP_DCB_PTR = TEMP_DCB_PTR+DCB_BLK(TEMP_DCB_PTR+DCB_SIZE)+2
          ENDDO
          IF (DCB_BLK(TEMP_DCB_PTR+STV_DP_CODE).NE.0) THEN
              SUBDCBSTRT = STV_DSP_STRT
          ELSE
              SUBDCBSTRT = STV_SUB_STRT
          ENDIF
C
          OPTION   = DCB_BLK(TEMP_DCB_PTR+DCB_OPTIONS)
          NUM_CRITERIA = DCB_BLK(TEMP_DCB_PTR + STV_CRIT_NUM)
C
C -- If criteria exist prepare the XSDCB for XSRT so that the
C    preselect can be entered into the XSTABLE table
C
          IF (NUM_CRITERIA.NE.0) THEN
CNT           TLSELECT(CURR_LINE) = PRESELECT
              NUM_STV = DCB_BLK(TEMP_DCB_PTR + STV_VALS_NUM)
              CRITSTART = SUBDCBSTRT
              XSDCBSTART  = CRITSTART + NUM_CRITERIA * STV_SBL_SIZ
C
              ICNT = 0
              CRITINDX = CRITSTART
C
C --        Process criteria data
C
            DO I = 1, NUM_CRITERIA
               I2OFF_IND(1) = DCB_BLK(TEMP_DCB_PTR +CRITINDX
     &           +STV_OFFSET)
               I2OFF_IND(2) = DCB_BLK(TEMP_DCB_PTR +CRITINDX
     &           +STV_OFFSET+1)
C
               ICNT = ICNT + 1
C
               OFF_INDX(CRITINDX+STV_OFFSET)   = I2ICNT(1)
               OFF_INDX(CRITINDX+STV_OFFSET+1) = I2ICNT(2)
C
               XSOFFBLK(BASE_EAS,ICNT,LESSON_DATA)
     &                      = OFF_BLOCK(BASE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(OFFSET_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(OFFSET_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(BYTE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(BYTE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(TYPE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(TYPE_EAS,OFF_IND,HOST_DATA)
C
               CRITINDX = CRITINDX + STV_SBL_SIZ
              ENDDO
C
            DO II = 1, NUM_STV
             I2OFF_IND(1) = DCB_BLK(TEMP_DCB_PTR +XSDCBSTART
     &                      +STV_OFFSET)
             I2OFF_IND(2) = DCB_BLK(TEMP_DCB_PTR +XSDCBSTART
     &                      +STV_OFFSET+1)
C
             ICNT = ICNT + 1
C
             OFF_INDX(XSDCBSTART+STV_OFFSET)   = I2ICNT(1)
             OFF_INDX(XSDCBSTART+STV_OFFSET+1) = I2ICNT(2)
C
             XSOFFBLK(BASE_EAS,ICNT,LESSON_DATA)
     &                      = OFF_BLOCK(BASE_EAS,OFF_IND,HOST_DATA)
             XSOFFBLK(OFFSET_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(OFFSET_EAS,OFF_IND,HOST_DATA)
             XSOFFBLK(BYTE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(BYTE_EAS,OFF_IND,HOST_DATA)
             XSOFFBLK(TYPE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(TYPE_EAS,OFF_IND,HOST_DATA)
C
             XSDCBSTART = XSDCBSTART + STV_SBL_SIZ
            ENDDO
C
              DO II = 1, DCB_BLK(TEMP_DCB_PTR +DCB_SIZE)
               XSDCB(II,LESSON_DATA) = DCB_BLK(TEMP_DCB_PTR + II-1)
               IF ( OFF_INDX(II-1).NE.0 ) THEN
                  XSDCB(II,LESSON_DATA)    = OFF_INDX(II-1)
                  OFF_INDX(II-1) = 0
               ENDIF
              ENDDO
C
              XSDCBSET(LESSON_DATA) = .TRUE.
              ASTPFND = .TRUE.                 !FM+ LP step found
           ENDIF
C
C
C      Skip over the auto criteria dcb and point to the beginning
C      of the setvalue dcb which will automatically be triggered.
C
         TEMP_DCB_PTR = DCB_BLK(TEMP_DCB_PTR+DCB_SIZE)+2 + TEMP_DCB_PTR
 
       ELSE IF (AUTO_STEP) THEN
         TEMP_DCB_PTR = IP_DICT(1, CURR_LINE)
       ENDIF
       STATE = 1
C
       GOTO 9999
C
C
C
C===================
C PROCESSING STATE 4
C===================
C
C=======================================================
C                READ PAGE INPUT DIRECTORY
C=======================================================
C
 40   FIRST_LINE  =  LPPGHEADER(PAG_FIRLIN)
      LAST_LINE   =  LPPGHEADER(PAG_LASLIN)
C
C !FM+ --- Auto Scrolling
C
      IF ( (LAST_LINE - FIRST_LINE) .GT. 5 ) THEN
        SCROLLMODE = .TRUE.
      ELSE
        SCROLLMODE = .FALSE.
      ENDIF
      SCROLL_CHECK = .FALSE.
C !FM-
C
      IPDIROFFSET =  LPPGHEADER(PAG_IPOFF)
      BYTECNT  = LPPGHEADER(PAG_IPLEN)
      RECNUM   = PAGE_DIR(TLPAGE) - 1
      OFFSET = IPDIROFFSET - 1
      READ_STATUS = 0
      READ_RSTAT = 0
      CALL CAE_IO_READ( READ_STATUS,
     .                          READ_RSTAT,
     .                          %VAL(FD),
     .                          %VAL(RECSIZE),
     .                          IP_DICT,
     .                          RECNUM,
     .                          BYTECNT,
     .                          OFFSET )
           READ_IN_PROGRESS = .TRUE.
C
      STATE = 5
C
C
C
C
C===================
C PROCESSING STATE 5
C===================
C
C=======================================================
C  READ DCB BLOCKS AREA FOR THE CURRENT LESSON PLAN PAGE
C=======================================================
C
 50   IF ( (((TLSTEP .GE. FIRST_LINE)  .AND.
     +       (TLSTEP .LE. LAST_LINE) ) .AND.
     +       (IP_DICT(1,CURR_LINE) .NE. -1) ) ) THEN
 
         RECNUM   = PAGE_DIR(TLPAGE) - 1
         DCBLKOFFSET =  LPPGHEADER(PAG_DCBOFF)
         BYTECNT  = LPPGHEADER(PAG_DCBLEN)
         OFFSET = DCBLKOFFSET - 1
         READ_STATUS = 0
         READ_RSTAT = 0
         CALL CAE_IO_READ( READ_STATUS,
     .                          READ_RSTAT,
     .                          %VAL(FD),
     .                          %VAL(RECSIZE),
     .                          DCB_BLK,
     .                          RECNUM,
     .                          BYTECNT,
     .                          OFFSET )
           READ_IN_PROGRESS = .TRUE.
C
           STATE = 6
C
      ELSE
        STATE      = 1
        TLNST      = 0.0
        TLESSON    = 0
      ENDIF
      GO TO 9999
C
C
C
C===================
C PROCESSING STATE 6
C===================
C
C=======================================================
C  READ OFFSET BLOCK AREA FOR THE CURRENT LESSON PLAN PAGE
C=======================================================
C
 60      RECNUM   = PAGE_DIR(TLPAGE) - 1
         BYTECNT  = LPPGHEADER(PAG_OFFLEN)
         OFF_BLOCK_OFFSET =  LPPGHEADER(PAG_OFFOFF)
         OFFSET = OFF_BLOCK_OFFSET - 1
         READ_STATUS = 0
         READ_RSTAT = 0
         NUM_OF_REMOTE = LPPGHEADER(PAG_RENTRY)
         OFF_BLK_READ_POSN = 1 - NUM_OF_REMOTE
         CALL CAE_IO_READ(      READ_STATUS,
     .                          READ_RSTAT,
     .                          %VAL(FD),
     .                          %VAL(RECSIZE),
     .                          OFF_BLOCK(1,OFF_BLK_READ_POSN,1),
     .                          RECNUM,
     .                          BYTECNT,
     .                          OFFSET )
          READ_IN_PROGRESS = .TRUE.
C
          STATE = 7
C
      GO TO 9999
C
C=======================================================
C=======================================================
C
C              LESSON PROFILE HANDLING
C
C
C=======================================================
C=======================================================
C
C
C
C===================
C PROCESSING STATE 8
C===================
C
C=======================================================
C  MONITOR LESSON PROFILE PAGE DISPLAY
C=======================================================
C
C
 80      CONTINUE
          IF ( ( TAPAGE(1) .GE. 400 )
     .      .AND. ( TAPAGE(1) .LE. 500 )) THEN
               LESSON_PROFILE_PG = TAPAGE(1)
C'usd8          ELSE IF ( ( TAPAGE(16) .GE. 400 )
C'usd8     .      .AND. ( TAPAGE(16) .LE. 500 )) THEN
C'usd8               LESSON_PROFILE_PG = TAPAGE(16)
          ELSE IF ( ( TAPAGE(3) .GE. 400 )
     .      .AND. ( TAPAGE(3) .LE. 500 )) THEN
               LESSON_PROFILE_PG = TAPAGE(3)
          ENDIF
 
C   If lesson profile is newly displayed, go through all dcbs
C   and extract all automatic steps. Place them into temporary
C   dcb blocks until the lesson start flag is selected
C
              IF ( LESSON_PROFILE_PG .NE. OLD_PROFILE ) THEN
C
C          ! READ IN PAGE DCBS
C
                 IF (PAGE_DIR(LESSON_PROFILE_PG).NE.-1) THEN
                    STATE = 9
                    OLD_PROFILE = LESSON_PROFILE_PG
                    DO I = 1,TEMPDCBPTR
                      TEMPDCB_BLK(I) = 0
                      TEMPDCBPTR = 1
                    ENDDO
                    GOTO 9999
                  ENDIF
              ENDIF
C
           OLD_PROFILE = LESSON_PROFILE_PG
C
           IF (TLSTART) THEN           ! Lesson Profile elapsed time
              TLELTIME = TLELTIME + 0.266
           ENDIF
C
           IF (TLSTART.NEQV.OLD_TLSTART) THEN
             IF (TLSTART) STATE = 13  ! Lesson Profile activated
             IF (TLELTIME.GT.0) TLELTIME = 0
             XTEMPDCBPTR = 1
           ELSE
               STATE = 8
           ENDIF
C
           OLD_TLSTART = TLSTART
C
           IF (TLEND) THEN
              TLSTART = .FALSE.
              TLEND = .FALSE.
              OLD_PROFILE = 0
           ENDIF
C
          IF (TLSTART) THEN
             TLRTPLAV = .TRUE.
          ENDIF
C
          IF (TLRTPLN) THEN
            TAPAGE(1) = LESSON_PROFILE_PG
            TLRTPLN  = .FALSE.
          ENDIF
 
           GOTO 9999
C
C
C===================
C PROCESSING STATE 9
C===================
C
C=======================================================
C  READ IN LESSON PROFILE PAGE HEADER
C=======================================================
C
 90      CONTINUE
         LPHDOFFSET = 1
         BYTECNT  = MAX_PGHDR*4
         RECNUM   = PAGE_DIR(LESSON_PROFILE_PG) - 1
         OFFSET = LPHDOFFSET - 1
         READ_STATUS = 0
         READ_RSTAT = 0
         CALL CAE_IO_READ( READ_STATUS,
     .                          READ_RSTAT,
     .                          %VAL(FD),
     .                          %VAL(RECSIZE),
     .                          LPPGHEADER,
     .                          RECNUM,
     .                          BYTECNT,
     .                          OFFSET )
         READ_IN_PROGRESS = .TRUE.
         STATE = 10
         GOTO 9999
C
C
C===================
C PROCESSING STATE 10
C===================
C
C=======================================================
C  READ IN LESSON PROFILE PAGE DCBS
C=======================================================
C
 100     CONTINUE
         RECNUM   = PAGE_DIR(LESSON_PROFILE_PG) - 1
         DCBLKOFFSET =  LPPGHEADER(PAG_DCBOFF)
         BYTECNT  = LPPGHEADER(PAG_DCBLEN)
         OFFSET = DCBLKOFFSET - 1
         READ_STATUS = 0
         READ_RSTAT = 0
         CALL CAE_IO_READ( READ_STATUS,
     .                          READ_RSTAT,
     .                          %VAL(FD),
     .                          %VAL(RECSIZE),
     .                          DCB_BLK,
     .                          RECNUM,
     .                          BYTECNT,
     .                          OFFSET )
           READ_IN_PROGRESS = .TRUE.
           STATE = 11
           GOTO 9999
C
C
C===================
C PROCESSING STATE 11
C===================
C
C=======================================================
C  EXTRACT AUTO STEPS FROM LESSON PROFILE PAGE
C=======================================================
C
 110       CONTINUE
           DCB_PTR = 1
           NEXTDCBPTR = DCB_BLK(DCB_PTR + DCB_SIZE) + 2 + DCB_PTR
C
           NUM_AUTO_STEPS = 0
C
           DO WHILE ((DCB_PTR + DCB_BLK(DCB_PTR + DCB_SIZE)
     &                                  .LT.NEXTDCBPTR)
     &             .AND. DCB_BLK(DCB_PTR + DCB_SIZE) .GT. 0)
             DCBTYPE =  DCB_BLK(DCB_PTR + DCB_TYPE )
C
C -- If setvalue DCB
C
             IF (DCBTYPE .EQ. SET_VALUE) THEN
C
C
C -- If display code is other than 0 then no true/false strings are
C    being used and sub dcb information will begin at positon 21 of the
C    setvale dcb , otherwise sub dcb information will begin at position 31
C
C
            IF (DCB_BLK(DCB_PTR+STV_DP_CODE).NE.0) THEN
                SUBDCBSTRT = STV_DSP_STRT
            ELSE
                SUBDCBSTRT = STV_SUB_STRT
            ENDIF
C
            OPTION   = DCB_BLK(DCB_PTR+DCB_OPTIONS)
            AUTO_STEP = ( IAND(OPTION,'0010'X) .NE.0 )
C
            IF (AUTO_STEP) THEN
              NUM_AUTO_STEPS = NUM_AUTO_STEPS + 1
              CURRPTR = DCB_PTR
              DO WHILE (CURRPTR .LT.(DCB_PTR+DCB_BLK
     &                                  (DCB_PTR+DCB_SIZE)))
                   TEMPDCB_BLK(TEMPDCBPTR) = DCB_BLK(CURRPTR)
                   CURRPTR = CURRPTR + 1
                   TEMPDCBPTR = TEMPDCBPTR + 1
              ENDDO
            ENDIF
C
           ENDIF
           DCB_PTR = NEXTDCBPTR
           NEXTDCBPTR = DCB_BLK(DCB_PTR + DCB_SIZE) + 2 + DCB_PTR
           ENDDO
C
           IF (NUM_AUTO_STEPS .GT. 0) THEN
              STATE = 12
           ELSE
              STATE = 8
           ENDIF
           GOTO 9999
C
C
C
C
C====================
C PROCESSING STATE 12
C====================
C
C=======================================================
C  GET LESSON PROFILE PAGE OFFSET BLOCK
C=======================================================
C
 120     RECNUM   = PAGE_DIR(LESSON_PROFILE_PG) - 1
         BYTECNT  = LPPGHEADER(PAG_OFFLEN)
         OFF_BLOCK_OFFSET =  LPPGHEADER(PAG_OFFOFF)
         OFFSET = OFF_BLOCK_OFFSET - 1
         READ_STATUS = 0
         READ_RSTAT = 0
         NUM_OF_REMOTE = LPPGHEADER(PAG_RENTRY)
         OFF_BLK_READ_POSN = 1 - NUM_OF_REMOTE
         CALL CAE_IO_READ(      READ_STATUS,
     .                          READ_RSTAT,
     .                          %VAL(FD),
     .                          %VAL(RECSIZE),
     .                          OFF_BLOCK(1,OFF_BLK_READ_POSN,1),
     .                          RECNUM,
     .                          BYTECNT,
     .                          OFFSET )
          READ_IN_PROGRESS = .TRUE.
C
          STATE = 8
          GOTO 9999
C
C===================
C PROCESSING STATE 13
C===================
C
C===========================================================
C  ENTER AUTOMATIC LESSON PROFILE STEPS IN PRESELECT TABLE
C===========================================================
C
C
C
C -- If criteria exist prepare the XSDCB for XSRT so that the
C    preselect can be entered into the XSTABLE table
C
 130      CONTINUE
C
          NUM_XSENTRIES = NUM_XSENTRIES + 1
          NUM_CRITERIA = TEMPDCB_BLK(XTEMPDCBPTR + STV_CRIT_NUM)
          IF (NUM_CRITERIA.NE.0) THEN
              dum1 = dum1 + 1
              NUM_STV = TEMPDCB_BLK(XTEMPDCBPTR + STV_VALS_NUM)
C
              IF (TEMPDCB_BLK(XTEMPDCBPTR+STV_DP_CODE).NE.0) THEN
                  SUBDCBSTRT = STV_DSP_STRT
              ELSE
                  SUBDCBSTRT = STV_SUB_STRT
              ENDIF
C
              CRITSTART = SUBDCBSTRT
              XSDCBSTART  = CRITSTART + NUM_CRITERIA * STV_SBL_SIZ
C
              ICNT = 0
              CRITINDX = CRITSTART
C
C --        Process criteria data
C
              dum2 = 0
              DO I = 1, NUM_CRITERIA
               dum2 = dum2 + 1
               if (dum2.gt.10) goto 9999
               I2OFF_IND(1) = TEMPDCB_BLK(XTEMPDCBPTR +CRITINDX
     &                                       +STV_OFFSET)
               I2OFF_IND(2) = TEMPDCB_BLK(XTEMPDCBPTR +CRITINDX
     &                                      +STV_OFFSET+1)
C
               ICNT = ICNT + 1
 
               OFF_INDX(CRITINDX+STV_OFFSET)   = I2ICNT(1)
               OFF_INDX(CRITINDX+STV_OFFSET+1) = I2ICNT(2)
 
               XSOFFBLK(BASE_EAS,ICNT,LESSON_DATA)
     &                      = OFF_BLOCK(BASE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(OFFSET_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(OFFSET_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(BYTE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(BYTE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(TYPE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(TYPE_EAS,OFF_IND,HOST_DATA)
C
               CRITINDX = CRITINDX + STV_SBL_SIZ
              ENDDO
C
              dum3 = 0
              DO II = 1, NUM_STV
               dum3 = dum3 + 1
               if (dum3.gt.40) goto 9999
               I2OFF_IND(1) = TEMPDCB_BLK(XTEMPDCBPTR +XSDCBSTART
     &                                        +STV_OFFSET)
               I2OFF_IND(2) = TEMPDCB_BLK(XTEMPDCBPTR +XSDCBSTART
     &                                        +STV_OFFSET+1)
C
               ICNT = ICNT + 1
C
               OFF_INDX(XSDCBSTART+STV_OFFSET)   = I2ICNT(1)
               OFF_INDX(XSDCBSTART+STV_OFFSET+1) = I2ICNT(2)
C
               XSOFFBLK(BASE_EAS,ICNT,LESSON_DATA)
     &                      = OFF_BLOCK(BASE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(OFFSET_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(OFFSET_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(BYTE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(BYTE_EAS,OFF_IND,HOST_DATA)
               XSOFFBLK(TYPE_EAS,ICNT,LESSON_DATA)
     &                     = OFF_BLOCK(TYPE_EAS,OFF_IND,HOST_DATA)
C
               XSDCBSTART = XSDCBSTART + STV_SBL_SIZ
              ENDDO
C
              dum4 = 0
              DO II = 1, TEMPDCB_BLK(XTEMPDCBPTR +DCB_SIZE)
               if (dum4.gt.500) goto 9999
               XSDCB(II,LESSON_DATA) = TEMPDCB_BLK(XTEMPDCBPTR + II-1)
               IF ( OFF_INDX(II-1).NE.0 ) THEN
                  XSDCB(II,LESSON_DATA)    = OFF_INDX(II-1)
                  OFF_INDX(II-1) = 0
               ENDIF
              ENDDO
C
              XSDCBSET(LESSON_DATA) = .TRUE.
              XTEMPDCBPTR = TEMPDCB_BLK(XTEMPDCBPTR + DCB_SIZE)
     &                             + XTEMPDCBPTR
             ENDIF
C
             IF (NUM_XSENTRIES.LT.NUM_AUTO_STEPS) THEN
                  STATE = 13
             ELSE
                  STATE = 8
                  NUM_XSENTRIES = 0
             ENDIF
             GOTO 9999
C
C
C -- Exit
C
C
 9999  RETURN
       END
