/********************************************************************/
/* rvlstr: $Revision: FPMC.CMD, Version 1.5 (KU) 1992-NOV-04$       */
/********************************************************************/
-q                                      /* QUIET MODE               */
-c                                      /* ROM AUTO-INITIALIZATION  */

/* SPECIFY THE SYSTEM MEMORY MAP */

MEMORY
{
   VECS:   org = 0        len = 0x0040    /* INTERRUPT VECTORS      */
   BOOT:   org = 0x40     len = 0x7fc0    /* BOOT MEMORY            */
   SHARE:  org = 0x008000 len = 0x7fff    /* SHARED MEMORY          */
   RAM:    org = 0x809800 len = 0x67ff    /* RAM BLOCK 0,1          */
   MEM:    org = 0x810000 len = 0x9fff    /* LOCAL MEMORY           */
}

/* SPECIFY THE SECTIONS ALLOCATION INTO MEMORY */

SECTIONS
{
   .vectors:{} > VECS               /* INTERRUPT VECTORS            */
   .stack:  {} > RAM                /* SYSTEM STACK                 */
   .bss:    {} > RAM                /* GLOBAL & STATIC VARIABLES    */
   .share:  {} > SHARE	            /* SHARED MEMORY                */
   .text:   {} > MEM	            /* CODE                         */
   .data:   {} > MEM	            /* 32 BIT CONSTANTS             */
   .sysmem: {} > BOOT               /* SPACE FOR MEMORY MANAGER     */
   .cinit:  {} > MEM                /* INITIALIZATION TABLES        */
}