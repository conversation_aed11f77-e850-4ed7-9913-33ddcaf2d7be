C'Module_ID             USD8DV
C'SDD_#                 ?
C'Customer              USAIR
C
C'Application           Simulation of the DASH8 Pressurization
C
C'Author                F. Nacca<PERSON>
C                       (<PERSON><PERSON>)
C'Date                  July 1991
C
C'System                ECS (Environmental Control Systems)
C'Iteration_Rate        266 msec
C'Process               Synchronous
C
C ----------------
C'Revision_History
C
C  usd8dv.for.18 28Jan2004 01:50 usd8 Tom
C       < Fix for FAULT Lt not working 1st power-up after reload >
C
C  usd8dv.for.17 29Apr1992 16:21 usd8 jgb
C       < corrected constant dvcf300 from 1.5 to 1.0 to clear snag 1059 >
C
C  usd8dv.for.16 12Apr1992 12:50 usd8 JGB
C       < ADDED EQ. N280 TO SET DVGS TO FALSE WHEN REPOSITIONING  >
C
C  usd8dv.for.15 12Apr1992 08:52 usd8 jgb
C       < corrected constant dvcf300 from 3 to 1.5 sec to clear snag ,
C         costumer wants the light to extinguish in half time. >
C
C  usd8dv.for.14 27Mar1992 16:02 usd8 JGB
C       < ADDED I/F MAINT RESET DARK CONCEPT  >
C
C  usd8dv.for.13 18Mar1992 09:24 usd8 jgb
C       < corrected compilation error >
C
C  usd8dv.for.12 18Mar1992 09:22 usd8 JGB
C       < PUT BACK UACABALT AND UAPSALT LABELS >
C
C  usd8dv.for.11  2Mar1992 21:09 usd8 JGB
C       < CORRECTED COMPILATION ERROR >
C
C  usd8dv.for.10  2Mar1992 20:47 usd8 JGB
C       < PUT IN COMMENT UAPSALT MOMENTARILY >
C
C  usd8dv.for.9  2Mar1992 16:48 usd8 JGB
C       < CORRECTED COMPILING ERROR >
C
C  usd8dv.for.8  2Mar1992 16:43 usd8 JGB
C       < CHANGE LOGIC OF MALFUNCTION ZFO SEE EQ.D2002 AND D2500 TO D3000  >
C
C  usd8dv.for.7 24Feb1992 06:56 usd8 JGB
C       < CORRECTED COMPILATION ERROR >
C
C  usd8dv.for.6 24Feb1992 06:55 usd8 jgb
C       < corrected compilation error >
C
C  usd8dv.for.5 24Feb1992 06:53 usd8 JGB
C       < CHANGE UACABALT FOR UBALT  >
C
C  usd8dv.for.4  1Feb1992 15:34 usd8 JGB
C       < CHANGE DVSS FOR .NOT. DVSS  >
C
C  usd8dv.for.3  1Feb1992 14:04 usd8 j.bilod
C       < change dvcc100 for in Hg conversion >
C
C ----------------
C
C'
C
C ----------------------
C'Compilation_Directives
C ----------------------
C
C
C'
C ------------------------
C'Include_Files_Directives
C ------------------------
C
C
C'
C -----------
C'Description
C -----------
C
C
C
C ----------
C'References
C ----------
C
C     The list of references is given in the PPS and
C     PDS documents relative to this project.
C
      SUBROUTINE USD8DV
C
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C'
C -------------
C'Include_Files
C -------------
C
      INCLUDE 'disp.com'  !NOFPC
C
C
C'
C ------------------
C'Subroutines_Called
C ------------------
C
C
C --------------------------
C'Common_Data_Base_Variables
C --------------------------
C
C
C     ---------------------------------------------------------
C
C                 C O M M O N    D A T A    B A S E
C
C     ---------------------------------------------------------
C
C
C
C     ---------
C      QMR bus
C     ---------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ         XRFTEST5,XRFTEST6
C
C
C
CE    REAL*4  DVHAS           ,!E- AIRCRAFT ALTITUDE     (ubalt)       UACABALT
CE    REAL*4  DVPAS           ,!E- ATMOS. PRESSURE                    UAPSALT
C
CE    EQUIVALENCE ( DVHAS     , UACABALT       ),
CE    EQUIVALENCE ( DVPAS     , UAPSALT(1)     ),
CcE    EQUIVALENCE ( DVPAS     , UBPS(1)        ),
C
C
C
CE    LOGICAL*1 DVBA          ,!E- CAB PRESS. CONT                      BILC04
CE    LOGICAL*1 DVFF          ,!E- FLIGHT FREEZE                        TCFFLPOS
CE    LOGICAL*1 DVFT          ,!E- THROTTLE ADV RELAY                   ESPLS1
CE    LOGICAL*1 DVGR          ,!E- GROUNG RELAY                         AGRK1
CE    LOGICAL*1 DVKF          ,!E- CAB PRES FAULT LIGHT                 DV$KF
CE    LOGICAL*1 DVKM          ,!E- I/F MAINT RESET DARK CONCEPT       TCR0CBPRE
CE    LOGICAL*1 DVSA          ,!E- CONT. IN AUTO MODE SWITCH            IDDVSA
CE    LOGICAL*1 DVSD          ,!E- EMER. DUMP SWITCH                    IDDVSD
CE    LOGICAL*1 DVSS          ,!E- AUTOMATIC FUNCTION SWITCH            IDDVSS
CE    LOGICAL*1 DVZFC         ,!E- AUTO CABIN PRESSURE CONTROLLER F.    TF21071
CE    LOGICAL*1 DVZFO         ,!E- LOSS OF CABIN PRESSURE               TF21111
C
CE    EQUIVALENCE ( DVBA      , BILC04          ),
CE    EQUIVALENCE ( DVFF      , TCFFLPOS        ),
CE    EQUIVALENCE ( DVFT      , ESPLS1          ),
CE    EQUIVALENCE ( DVGR      , AGRK1           ),
CE    EQUIVALENCE ( DVKF      , DV$KF           ),
CE    EQUIVALENCE ( DVKM      , TCR0CBPRE       ),
CE    EQUIVALENCE ( DVSA      , IDDVSA          ),
CE    EQUIVALENCE ( DVSD      , IDDVSD          ),
CE    EQUIVALENCE ( DVSS      , IDDVSS          ),
CE    EQUIVALENCE ( DVZFC     , TF21071         ),
CE    EQUIVALENCE ( DVZFO     , TF21111         )
C
C
CP USD8
C
C
C  Real CDB input variables
C
CP   * DTPCI                  ,! CABIN PRESSURE                      [psi]
CP   * DTXB                   ,! PRESS/ALT CONVERS FACT                -
CP   * ESPLS1                 ,!
CP   * UAPSALT                ,! LEFT PITOT STATIC PRESSURE          [mb]
Cp   * UBPS                   ,!
CP   * UBALT                  ,!
CP   * UACABALT               ,!
CP   * IADVPB                 ,! BAROMETRIC CORRECTION
CP   * IADVPL                 ,! ALTITUDE SELECTED
CP   * IADVQL                 ,! CABIN RATE SELECTED
CP   * VHH                    ,! DESCENT DET ALT HOLD                [psia]
C
C
C  Logical CDB input variables
C
CP   * AGRK1                  ,!
CP   * BILC04                 ,! CAB PRESS. CONT
CP   * IDDVSA                 ,! CONT. IN AUTO MODE SWITCH
CP   * IDDVSD                 ,! EMER. DUMP SWITCH
CP   * IDDVSS                 ,! AUTOMATIC FUNCTION SWITCH
CP   * TCFFLPOS               ,! FLIGHT/FREEZE
CP   * TCR0CBPRE              ,! I/F MAINT DARK CONCEPT
CP   * TCRCBPRE               ,!-
CP   * TCRMAINT               ,!-
CP   * TF21071                ,! AUTO CABIN PRESSURE CONTROLLER FAILS
CP   * TF21111                ,! LOSS OF CABIN PRESSURE
C
C 'Outputs
C
C  Real CDB outputs
C
CP   * DVDC                   ,! CAB PRESS CONT TIMER-4        [sec]
CP   * DVDD                   ,! CAB PRESS CONT TIMER-3        [sec]
CP   * DVDF                   ,! FAULT DELAY                   [sec]
CP   * DVHAC                  ,! CAB CLAMP ALT                 [psia]
CP   * DVHAL                  ,! A/C ALT LAST                  [psia]
CP   * DVHD                   ,! DESCENT DET ALT CHANGE        [feet]
CP   * DVHH                   ,! DESCENT DET ALT HOLD          [psia]
CP   * DVNM                   ,! MODE NUMBER                     -
CP   * DVPLD                  ,! LANDING PRESS                 [psia]
CP   * DVPN                   ,! CAB PRESS COMMAND             [psia]
CP   * DVPS                   ,! CABIN SCHEDULE PRESS          [psia]
CP   * DVPT                   ,! CABIN TARGET PRESS            [psia]
CP   * DVRD                   ,! CAB PRESS DOWN RATE LIMIT     [psi/min]
CP   * DVRU                   ,! CAB PRESS UP RATE LIMIT       [psi/min]
C
C  Logical CDB outputs
C
CP   * DVF                    ,! DPRES3 FREEZE FLAG
CP   * DVFB                   ,! ABORT FLAG
CP   * DVFC                   ,! CAB EVER CLAMP FLAG
CP   * DVFD                   ,! CAB PRES CONT DESCENT FLAG
CP   * DVFFC                  ,! O/V CLOSE COMMAND FLAG
CP   * DVFFO                  ,! O/V OPEN COMMAND FLAG
CP   * DVFN                   ,! NUMODE FLAG
CP   * DVFR                   ,! RESET FLAG
CP   * DVGC                   ,! CAB CLAMP FLAG
CP   * DVGF                   ,! FAULT FLAG
CP   * DV$KF                   ! CAB PRES FAULT LIGHT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:40:58 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DTPCI(2)       ! COMPT PRESS                            [psi]
     &, DTXB           ! PRESS/ALT CONVERS FACT                   [-]
     &, DVDC           ! CAB PRESS CONT TIMER-4                 [SEC]
     &, DVDD           ! CAB PRESS CONT TIMER-3                 [SEC]
     &, DVDF           ! FAULT DELAY                            [SEC]
     &, DVHAC          ! CAB CLAMP ALT                         [PSIA]
     &, DVHAL          ! A/C ALT LAST                          [PSIA]
     &, DVHD           ! DESCENT DET ALT CHANGE                [FEET]
     &, DVHH           ! DESCENT DET ALT HOLD                  [PSIA]
     &, DVPLD          ! LANDING PRESS                         [PSIA]
     &, DVPN           ! CAB PRESS COMMAND                     [PSIA]
     &, DVPS           ! CABIN SCHEDULE PRESS                  [PSIA]
     &, DVPT           ! CABIN TARGET PRESS                    [PSIA]
     &, DVRD           ! CAB PRESS DOWN RATE LIMIT          [PSI/MIN]
     &, DVRU           ! CAB PRESS UP RATE LIMIT            [PSI/MIN]
     &, IADVPB         ! BAROMETRIC CORRECTION          [inHg] AI048
     &, IADVPL         ! ALTITUDE SELECTED               [psi] AI050
     &, IADVQL         ! CABIN RATE SELECTED         [psi/min] AI049
     &, UACABALT       !  Cabin altitude                        [ft]
     &, UAPSALT(10)    !  Alternate static pressure             [Hg]
     &, UBALT(3)       !  ADC Pressure Altitude                 [ft]
     &, UBPS(3)        !  ADC Static Pressure                   [mb]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
C$
      INTEGER*4
     &  DVNM           ! MODE NUMBER                              [-]
C$
      LOGICAL*1
     &  AGRK1          ! Gear aux lndg relay K1
     &, BILC04         ! CABIN PRESS CONT            21 PDLMN  DI2035
     &, DV$KF          ! CAB PRESS FAULT LIGHT                 DO013A
     &, DVF            ! DPRES3 FREEZE FLAG
     &, DVFB           ! ABORT FLAG                       -
     &, DVFC           ! CAB EVER CLAMP FLAG              -
     &, DVFD           ! CAB PRES CONT DESCENT FLAG       -
     &, DVFFC          ! O/V CLOSE COMMAND FLAG           -
     &, DVFFO          ! O/V OPEN COMMAND FLAG            -
     &, DVFN           ! NUMODE FLAG                      -
     &, DVFR           ! RESET FLAG                       -
     &, DVGC           ! CAB CLAMP FLAG                   -
     &, DVGF           ! FAULT FLAG                       -
     &, ESPLS1         ! ENG 1 PL MSW [ T= PLA > FLT IDLE + 12 ]  [-]
     &, IDDVSA         ! CONT. IN AUTO MODE SWITCH             DI018B
     &, IDDVSD         ! EMER. DUMP SWITCH                     DI018C
     &, IDDVSS         ! AUTOMATIC FUNCTION SWITCH             DI018A
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCR0CBPRE      ! CABIN PRESS RESET DARK CON
     &, TCRCBPRE       ! CABIN PRESSURE RESET
     &, TCRMAINT       ! MAINTENANCE
     &, TF21071        ! AUTO CABIN PRESSURE CONTROLLER FAI
     &, TF21111        ! LOSS OF CABIN PRESSURE
C$
      LOGICAL*1
     &  DUM0000001(9106),DUM0000002(2649),DUM0000003(1010)
     &, DUM0000004(746),DUM0000005(4464),DUM0000006(5388)
     &, DUM0000007(152),DUM0000008(76),DUM0000009(36)
     &, DUM0000010(73832),DUM0000011(48),DUM0000012(72)
     &, DUM0000013(122),DUM0000014(2712),DUM0000015(3621)
     &, DUM0000016(201882),DUM0000017(51),DUM0000018(33)
     &, DUM0000019(125),DUM0000020(13253),DUM0000021(6)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,DV$KF,DUM0000002,IADVPB,IADVPL,IADVQL,DUM0000003
     &, IDDVSA,IDDVSD,IDDVSS,DUM0000004,BILC04,DUM0000005,VHH
     &, DUM0000006,UAPSALT,DUM0000007,UACABALT,DUM0000008,UBPS
     &, DUM0000009,UBALT,DUM0000010,DTPCI,DUM0000011,DTXB,DUM0000012
     &, DVDC,DVDD,DVDF,DVHAC,DVHAL,DVHD,DVHH,DVNM,DVPLD,DVPN
     &, DVPS,DVPT,DVRD,DVRU,DVFB,DVFC,DVFD,DVFFC,DVFFO,DVFN,DVFR
     &, DVGC,DVGF,DUM0000013,DVF,DUM0000014,ESPLS1,DUM0000015
     &, AGRK1,DUM0000016,TCFFLPOS,DUM0000017,TCRMAINT,DUM0000018
     &, TCRCBPRE,DUM0000019,TCR0CBPRE,DUM0000020,TF21071,DUM0000021
     &, TF21111   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DVHAS     
     &, DVPAS     
C$
      LOGICAL*1
     &  DVBA     
     &, DVFF     
     &, DVFT     
     &, DVGR     
     &, DVKF     
     &, DVKM     
     &, DVSA     
     &, DVSD     
     &, DVSS     
     &, DVZFC     
     &, DVZFO     
C$
      EQUIVALENCE
     &  (DVHAS,UACABALT),(DVPAS,UAPSALT(1)),(DVBA,BILC04)               
     &, (DVFF,TCFFLPOS),(DVFT,ESPLS1),(DVGR,AGRK1),(DVKF,DV$KF)         
     &, (DVKM,TCR0CBPRE),(DVSA,IDDVSA),(DVSD,IDDVSD),(DVSS,IDDVSS)      
     &, (DVZFC,TF21071),(DVZFO,TF21111)                                 
C------------------------------------------------------------------------------
C
C
C
C
C
C
C ---------------
C'Local_Variables
C ---------------
C
C
C
C    ----------------------------------------------------------------------
C                             M O D U L E
C
C               V A R I A B L E S    C O N V E N T I O N
C    ----------------------------------------------------------------------
C
C                L - local variable
C
C                X - common data base variable (read/write)
C
C                E - common data base variable (read only)
C
C                M - ECAM
C
C
C  ----------------------
C  ECS  Integer_Variables
C  ----------------------
C
C
      INTEGER*4
C
     & I             !L- LOOP INDEX
     &,DVK           !L- INDEX
C
C
C  -------------------
C  ECS  Real_Variables
C  -------------------
C
C
      REAL*4
C
C
C       Label       Description                 Units        Equival
C
C
     &  DVDA      !- POWER INTERRUPT DELAY
     &, DVHA      !- AIRCRAFT ALTITUDE
     &, DVHAA     !- CAB CLAMP ALT ABSOL DIFF
     &, DVHF      !- DESCENT DET ALT INCR
     &, DVHR      !- DESCENT DET ALT DIFF
     &, DVPA      !- ATMOSPHERIC PRESSURE
     &, DVPC      !- CABIN PRESSURE
     &, DVPCX     !- CABIN MAX PRESSURE
     &, DVPD      !- SCHEDULE DIFF PRESS
     &, DVPH      !- LO ALT SCHEDULE DIFF PRESS
     &, DVQLU     !- CAB ALT UP RATE SELECT
     &, DVRDL     !- DOWN RATE LO LIMIT
     &, DVRLD     !- CAB PRESS DOWN RATE SELECTED
     &, DVRLU     !- CAB PRESS UP RATE SELECTED
     &, T         !- ITERATION TIME
     &, DVX       !- TEMPORARY BUFFER
C
C
C
C
C
C
C  ----------------------
C  ECS  Logical_Variables
C  ----------------------
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
C
C
     &  DVFA      !- CAB PRESS CONT POWER FLAG
     &, DVFAL     !- CONTROL POWER FLAG LAST
     &, DVFS      !- DESC DET FIRST PASS FLAG
     &, DVGA      !- AIRBORN FLAG
     &, DVGE      !- MUX FAULT
     &, DVGS      !- SLEW FLAG
     &, DVZFOL    !- LOSS OF CABIN PRESSURE LAST MALF.
     &, DVZFCL    !- AUTO CABIN PRESSURE CONTROLLER F.LAST MALF.
     &, FIRST     !- FIRST PASS FLAG
     &                            / .TRUE. /
C
C
C
C
C  --------------
C  ECS  Constants
C  --------------
C
C
      REAL*4
C
C       Label      Value           Description              Units
C
C
     &  DVCA20   / 0.5     /       !-                       [sec]
     &, DVCA40   / -.2655  /       !-                       [psi/min]
     &, DVCA41   / .1593   /       !-                       [psi/min]
     &, DVCC20   / 0.50    /       !-                       [n/a]
     &, DVCC40   / 8.294   /       !-                       [psi]
     &, DVCC100  / 0.491   /       !-                       [psi/Mb]
     &, DVCC140  / 3.458   /       !-                       [psi]
     &, DVCC200  / 15000   /       !-                       [ft]
     &, DVCC400  / 2500.   /       !-                       [ft/min]
     &, DVCC401  / 150.    /       !-                       [ft/min]
     &, DVCC420  / -.600   /       !-                       [n/a]
     &, DVCD40   / 5.50    /       !-                       [psi]
     &, DVCD200  / 50.02   /       !-                       [1/min]
     &, DVCD220  / -3.1854 /       !-                       [psi/min]
     &, DVCD221  / 3.1854  /       !-                       [psi/min]
     &, DVCF20   / 0.2456  /       !-                       [psi]
     &, DVCF100  / 15.51   /       !-                       [psi]
     &, DVCF101  / 8.462   /       !-                       [psi]
     &, DVCF120  / 0.7761  /       !-                       [psi]
     &, DVCF121  / -1.189  /       !-                       [psi]
     &, DVCF140  / 2800.   /       !-                       [ft/min]
     &, DVCF141  / 50.0    /       !-                       [ft/min]
     &, DVCF300  / 1.0     /       !-                       [sec]
     &, DVCL20   / 10.0    /       !-                       [sec]
     &, DVCM20   / 0.074326/       !-                       [psi]
     &, DVCM60   / -300.0  /       !-                       [ft/min]
     &, DVCN20   / 10.0    /       !-                       [sec]
     &, DVCN60   / 33.4    /       !-                       [ft]
     &, DVCN61   / 166.7   /       !-                       [ft]
     &, DVCN100  / 1000.0  /       !-                       [ft]
     &, DVCN200  / 100.0   /       !-                       [ft]
     &, DVCN201  / 500.0   /       !-                       [ft]
     &, DVCN260  / 100.0   /       !-                       [ft]
     &, DVCN300  / 60.0    /       !-                       [sec]
     &, DVCN820  / 0.0     /       !-                       [n/a]
     &, DVCP20   / 60.0    /       !-                       [sec]
     &, DVCP21   / 20.0    /       !-                       [sec]
     &, DVCP60   / 7.432   /       !-                       [psi]
     &, DVCS1    / 0.523   /       !-                       [psi]
C
C
      REAL*4
C
C       Label         Description                          Units
C
     &  DVCB(35)   !- INTERCEPT VECTOR                      psi
     &, DVCM(35)   !- SLOPE VECTOR                          [-]
     &, DVPAT(36)  !- ATM PRESS REF TABLE                   psi
     &, DVPST(36)  !- CABIN SCHEDULE PRES REF TABLE         psi
C
      DATA DVPAT            !AIR PRESS REF TABLE
C
     & / 15.790   ,   15.235   ,  14.963    ,   14.696   ,  14.432    ,
     &   14.173   ,   13.664   ,  13.171    ,   12.692   ,  12.228    ,
     &   11.777   ,   11.340   ,  10.916    ,   10.505   ,  10.106    ,
     &    9.720   ,    9.346   ,   9.164    ,    8.984   ,   8.633    ,
     &    8.294   ,    7.965   ,   7.647    ,    7.339   ,   7.041    ,
     &    6.753   ,    6.475   ,   6.206    ,    5.947   ,   5.696    ,
     &    5.454   ,    5.220   ,   4.994    ,    4.776   ,   4.566    ,
     &    4.364   /
C
      DATA DVPST            !CABIN SCHEDULE PRES REF TABLE
C
     & / 14.997   ,   14.913   ,  14.851    ,   14.829   ,  14.788    ,
     &   14.746   ,   14.662   ,  14.581    ,   14.498   ,  14.417    ,
     &   14.335   ,   14.254   ,  14.174    ,   14.093   ,  14.013    ,
     &   13.839   ,   13.665   ,  13.580    ,   13.477   ,  13.274    ,
     &   13.074   ,   12.876   ,  12.686    ,   12.495   ,  12.305    ,
     &   12.114   ,   11.862   ,  11.620    ,   11.381   ,  11.147    ,
     &   10.916   ,   10.689   ,  10.466    ,   10.247   ,  10.031    ,
     &   9.820    /
C
C
         ENTRY DPRES3
C
C
C        *************************
C        FIRST PASS INITIALIZATION
C        *************************
C
          IF ( FIRST ) THEN
C !FM+
C !FM  28-Jan-04 01:44:26 Tom
C !FM    < Ref: Gripe number 90931 >
C !FM    < fix for FAULT light not working first time after reload>
C
          DVDF = DVCF300
C !FM-
          FIRST = .FALSE.
C
          DO I = 1, 35
C
          DVX = DVPAT( I + 1 ) - DVPAT( I )
C
          DVCM(I) = ( DVPST( I + 1 ) - DVPST( I )) / DVX
C
          DVCB(I) = DVPST( I ) - DVCM( I ) * DVPAT( I )
C
          DVK = 1
C
          ENDDO
C
          ENDIF
C
C
C
C        **************
C        INITIALIZATION
C        **************
C
C
         T = YITIM
C
C
         IF (DVF) THEN
C          Module freeze flag
         ELSE
C
         TCRCBPRE = TCRMAINT
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  A   ##                               ##
C        #                ##                               ##
C        ####################################################
C
C
CD A200  UPDATE [DVFA] CAB PRESS CONT POWER FLAG
C        ---------------------------------------
C
         DVFA   =  DVBA .AND. DVSA
C
CD A220  IF CAB PRESS CONT POWER FLAG IS TRUE THEN
C        -----------------------------------------
C
         IF ( DVFA ) THEN
C
CD       ELSE
C        ----
C
         ELSE
C
CD A240  UPDATE [DVDA] POWER INTERRUPT DELAY
C        -----------------------------------
C
         DVDA   = DVDA - T
C
CD A260  CONTROL POWER FLAG LAST
C        -----------------------
C
         DVFAL = .FALSE.
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
C
C
C          WAKE UP & FAULT ( NM = 0 )
C
C
CD A1000  IF CAB PRESS CONT POWER FLAG IS TRUE OR POWER INTERRUPT DELAY IS
C                                                      GREATER THAN 0 THEN
C         ----------------------------------------------------------------
C
          IF ( DVFA .OR. ( DVDA .GT. 0.0 ) ) THEN
C
CD A1020  IF POWER INTERRUPT DELAY IS LOWER THAN 0 THEN
C         ---------------------------------------------
C
          IF ( DVDA .LT. 0.0 ) THEN
C
CD A1200  UPDATE [DVDA] POWER INTERRUPT DELAY
C         -----------------------------------
C
          DVDA  = DVCA20
C
CD A1220  UPDATE [DVNM] MODE NUMBER
C         -------------------------
C
          DVNM = 0
C
CD A1240  UPDATE [DVRU] CAB PRESS UP RATE LIMIT
C         -------------------------------------
C
          DVRU = DVCA40
C
CD A1260  UPDATE [DVRD] CAB PRESS DOWN RATE LIMIT
C         ---------------------------------------
C
          DVRD = DVCA41
C
CD A1280  UPDATE [DVPN] CAB PRESS COMMAND
C         -------------------------------
C
          DVPN = DTPCI(1)
C
CD        ELSE
C         ----
C
          ELSE
C
CD        ENDIF
C         -----
C
          ENDIF
C
C A1400
C
          IF (DVFA .AND. .NOT.DVFAL) THEN
C
CD A1420  CONTROL POWER FLAG LAST
C         -----------------------
C
          DVFAL = .TRUE.
C
CD A1440  POWER INTERRUPT DELAY
C         --------------------
C
          DVDA = DVCA20
C
          ENDIF
C
CD A2000  IF FAULT FLAG IS TRUE THEN
C         --------------------------
C
          IF ( DVGF ) THEN
C
CD A2020  UPDATE [DVNM] MODE NUMBER
C         -------------------------
C
          DVNM = 0
C
CD A2040  UPDATE [DVKF] CAB PRESS FAULT LIGHT
C         -----------------------------------
C
          DVKF = .TRUE.
C
CD        ELSE
C         ----
C
          ELSE
C
CD        ENDIF
C         ----
C
          ENDIF
C
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  C   ##                               ##
C        #                ##                               ##
C        ####################################################
C
C
CD C200  IF NOT FAULT FLAG AND MODE NUMBER IS DIFFERENT THEN 0 THEN
C        ----------------------------------------------------------
C
         IF ( .NOT. DVGF .AND. DVNM .NE. 0 ) THEN
C
CD C220  UPDATE [DVKF] CAB PRES FAULT LIGHT
C        ----------------------------------
C
         DVKF = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C1000 UPDATE [DVPC] CABIN PRESSURE
C        ----------------------------
C
          DVPC = DTPCI(1) + DVCC20 * ( DTPCI(2) - DTPCI(1) )
C
CD C1020 IF CABIN PRESSURE IS LOWER THAN DVCC40 THEN
C        -------------------------------------------
C
         IF ( DVPC .LT. DVCC40 ) THEN
C
CD C1040 UPDATE [DVPC] CABIN PRESSURE
C        ----------------------------
C
         DVPC = DVCC40
C
CD       ELSE
C        -----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C2000 UPDATE [DVPA] ATMOSPHERIC PRESSURE
C        ----------------------------------
C
         DVPA = DVCC100 * DVPAS
C
CD C2020 IF ATMOS. PRES. IS LOWER THAN DVCC140 THEN
C        ------------------------------------------
C
         IF ( DVPA .LT. DVCC140 ) THEN
C
CD C2040 UPDATE [DVPA] ATMOSPHERIC PRESSURE
C        ----------------------------------
C
         DVPA = DVCC140
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C3000 UPDATE [DVHA] AIRCRAFT ALTITUDE
C        -------------------------------
C
         DVHA = DVHAS
C
CD C3020 UPDATE [DVGA] AIRBORN FLAG
C        ---------------------------
C
         DVGA = ( DVHA .GT. DVCC200)
C
CD C4000 IF CABIN ALT UP RATE IS GREATER THEN HIGH LIMIT THEN
C        ----------------------------------------------------
C
         IF ( IADVQL .GT. DVCC400 ) THEN
C
CD C4021 UPDATE [DVQLU] CAB ALT UP RATE SELECT
C        -------------------------------------
C
         DVQLU = DVCC400
C
CD       ELSE
C        ----
C
         ELSE
C
CD C4020 IF CABIN ALT UP RATE IS LOWER THEN LOWER LIMIT THEN
C        ---------------------------------------------------
C
         IF ( IADVQL .LT. DVCC401 ) THEN
C
CD
CD C4042 UPDATE [DVQLU] CAB ALT UP RATE SELECT
C        -------------------------------------
C
         DVQLU = DVCC401
C
CD       ELSE
C        ----
C
         ELSE
C
CD C4040 UPDATE [DVQLU] CAB ALT UP RATE SELECT
C        -------------------------------------
C
         DVQLU = IADVQL
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C4060 UPDATE [DVRLU] CAB PRESS UP RATE SELECTED
C        ------------------------------------------
C
         DVRLU =  DTXB * DVQLU
C
CD C4080 UPDATE [DVRLD] CAB PRESS DOWN RATE SELECTED
C        -------------------------------------------
C
         DVRLD = DVCC420 * DVRLU
C
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  D   ##                               ##
C        #                ##                               ##
C        ####################################################
C
C
CD D200  CABIN MAX PRESSURE
C        ------------------
C
         DVPCX = DVPA+DVCD40
C
CD D1000 IF CABIN PRESSURE COMMAND IS GREATER THAN CABIN MAX PRESSURE THEN
C        -----------------------------------------------------------------
C
         IF ( DVPN .GT. DVPCX ) THEN
C
CD D1020 UPDATE [DVPN] CABIN PRESSURE COMMAND
C        ------------------------------------
C
         DVPN  = DVPCX
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD D2000 IF MALF. ON CONTROLLER THEN
C        ---------------------------
C
         IF ( DVZFC .OR. DVZFO ) THEN
C
CD D2020 UPDATE [DVGF] FAULT FLAG
C        ------------------------
C
         DVGF = .TRUE.
C
CD D2040 IF MALF. O/V COMMANDED CLOSED THEN
C        ----------------------------------
C
         IF ( DVZFC ) THEN
C
CD D2061 UPDATE [DVFFC] O/V CLOSE COMMAND FLAG
C        -------------------------------------
C
         DVFFC = .TRUE.
C
CD D2091 UPDATE [DVFFO] O/V OPEN COMMAND FLAG
C        ------------------------------------
C
         DVFFO = .FALSE.
C
CD D2101 UPDATE [DVZFCL] AUTO CABIN PRESSURE CONTROLLER F.LAST MALF.
C        -----------------------------------------------------------
C
         DVZFCL = .TRUE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD D2060 UPDATE [DVFFC] O/V CLOSE COMMAND FLAG
C        -------------------------------------
C
         DVFFC = .FALSE.
C
CD D2080 UPDATE [DVFFO] O/V OPEN COMMAND FLAG
C        ------------------------------------
C
         DVFFO = .TRUE.
C
CD D2100 UPDATE [DVZFOL] LOSS OF CABIN PRESSURE LAST MALF.
C        -------------------------------------------------
C
         DVZFOL = .TRUE.
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD D2022 UPDATE [DVFFC] O/V CLOSE COMMAND FLAG
C        -------------------------------------
C
         DVFFC = .FALSE.
C
CD D2092 UPDATE [DVFFO] O/V OPEN COMMAND FLAG
C        ------------------------------------
C
         DVFFO =  DVGS
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD D2500 IF LOSS OF CABIN PRESSURE MALF. DE-SELECTED
C        -------------------------------------------
C
         IF ( .NOT.DVZFO .AND. DVZFOL ) THEN
C
CD D2520  UPDATE [DVGF] FAULT FLAG
C         ------------------------
C
          DVGF = .FALSE.
C
CD D2540  UPDATE [DVZFOL] UPDATE [DVZFOL] LOSS OF CABIN PRESSURE LAST MALF.
C         -----------------------------------------------------------------
C
          DVZFOL = .FALSE.
C
CD        ELSE
C         ----
C
          ELSE
C
CD        ENDIF
C         -----
C
          ENDIF
C
CD D2600  IF AUTO CABIN PRESSURE CONTROLLER F. MALF. DE-SELECTED THEN
C         -----------------------------------------------------------
C
          IF ( .NOT. DVZFC .AND. DVZFCL ) THEN
C
CD D2620  UPDATE [DVGF] FAULT FLAG
C         ------------------------
C
          DVGF = .FALSE.
C
CD D2640  UPDATE [DVZFCL] AUTO CABIN PRESSURE CONTROLLER F.LAST MALF.
C         -----------------------------------------------------------------
C
          DVZFCL = .FALSE.
C
CD        ELSE
C         ----
C
          ELSE
C
CD        ENDIF
C         -----
C
          ENDIF
C
CD D3000  IF FLIGHT FREEZE THEN
C         ---------------------
C
          IF ( DVFF ) THEN
C
CD        ELSE
C         -----
C
           ELSE
C
CD D3000  UPDATE [DVDD] CAB PRESS CONT TIMER-3
C         ------------------------------------
C
          DVDD = DVDD - T
C
CD D3020  UPDATE [DVDC] CAB PRESS CONT TIMER-4
C         ------------------------------------
C
          DVDC = DVDC - T
C
CD        ENDIF
C         -----
C
          ENDIF
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  F   ##                               ##
C        #                ##                               ##
C        ####################################################
C
C
CD F200  IF MODE FLAG IS 0 THEN
C        ----------------------
C
         IF ( DVNM .EQ. 0 ) THEN
C
CD F220  IF AIRCRAFT ON GROUND THEN
C        --------------------------
C
         IF ( DVGR ) THEN
C
CD F240  IF THROTTLE ADV. FLAG THEN
C        --------------------------
C
         IF ( DVFT ) THEN
C
CD H1000 UPDATE [DVDF] FAULT DELAY
C        -------------------------
C
         DVDF = DVCF300
C
CD H1020 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD H1040 UPDATE [DVNM] FLAG MODE TO TAKE OFF MODE
C        ----------------------------------------
C
         DVNM = 2
C
CD       ELSE
C        ----
C
         ELSE
C
CD F260  UPDATE [DVGF] FAULT FLAG
C        ------------------------
C
         DVGF = .FALSE.
C
CD F1000 IF PRESS. CONT. IN AUTO MODE THEN
C        ---------------------------------
C
         IF ( .NOT.DVSS ) THEN
C
CD F1020 IF ATMOS. PRESS. - CABIN PRESSURE IS GREATER THAN DVCF20 THEN
C        -------------------------------------------------------------
C
         IF ( ABS(DVPA - DVPC) .GT. DVCF20 )  THEN
C
CD F1040 UPDATE [DVGF] FAULT FLAG
C        ------------------------
C
         DVGF = .TRUE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD F2000 UPDATE [DVGE] MUX FAULT
C        -----------------------
C
         DVGE = .FALSE.
C
CD F2020 IF ALTITUDE SELECTED IS NOT BETWEEN -1500 AND 14500 FT THEN
C        -----------------------------------------------------------
C
         IF ( ( IADVPL .GT. DVCF100 ) .AND. ( IADVPL .LT. DVCF101 )
     &                                                      ) THEN
C
CD F2040 UPDATE [DVGE] MUX FAULT
C        -----------------------
C
         DVGE = .TRUE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD F2060 IF BAROMETRIC CORRECTION SELECTED IS NOT
C                                    BETWEEN 27.5 AND 31.5 in Hg THEN
C        ------------------------------------------------------------
C
         IF ( ( IADVPB .GT. DVCF120 ) .AND. ( IADVPB .LT. DVCF121 ) )
     &         THEN
C
CD F2080 UPDATE [DVGE] MUX FAULT
C        -----------------------
C
         DVGE = .TRUE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD F3000 IF CABIN RATE SELECTED IS NOT BETWEEN 50 AND 2800 ft THEN
C        ---------------------------------------------------------
C
         IF ( ( IADVQL .GT. DVCF140 ) .AND. ( IADVQL .LT. DVCF141
     &                                                        ) ) THEN
C
CD F3020 UPDATE [DVGE] MUX FAULT
C        -----------------------
C
         DVGE = .TRUE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD F4000 IF MUX FAULT IS TRUE THEN
C        -------------------------
C
         IF ( DVGE ) THEN
C
CD F4021 UPDATE [DVDF] FAULT TIME DELAY
C        ------------------------------
C
         DVDF = DVCF300
C
CD F4041 UPDATE [DVGF] FAULT FLAG
C        ------------------------
C
         DVGF = .TRUE.
C
C
CD       ELSE
C        ----
C
         ELSE
C
CD F4020 UPDATE [DVDF] FAULT TIME DELAY
C        ------------------------------
C
         DVDF = DVDF - T
C
CD F4040 IF FAULT TIME DELAY IS GREATER THAN 0 THEN
C        ------------------------------------------
C
         IF ( DVDF .GT. 0.0 ) THEN
C
CD F4060 UPDATE [DVGF] FAULT FLAG
C        ------------------------
C
         DVGF = .TRUE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        ----
C
         ENDIF
C
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  H   ##                               ##
C        #                ##                               ##
C        ####################################################
C
C
CD H200  UPDATE [DVFN] NIMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD H220  UPDATE [DVNM] SET GROUND MODE
C        -----------------------------
C
         DVNM = 1
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD H1001 UPDATE [DVDF] FAULT DELAY
C        -------------------------
C
         DVDF = DVCF300
C
CD H1021 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD H1041 UPDATE [DVNM] FLAG MODE TO FLIGHT  MODE
C        ---------------------------------------
C
         DVNM = 3
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD H1060 UPDATE [DVDF] FAULT DELAY
C        -------------------------
C
         DVDF = DVCF300
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
CD H2000 IF RESET FLAG THEN
C        ------------------
C
         IF ( DVFR ) THEN
C
CD H2020 UPDATE [DVFS] DESC DET FIRST PASS FLAG
C        --------------------------------------
C
         DVFS = .FALSE.
C
CD H2040 UPDATE [DVFD,DVGC,DVGS,DVFR,DVFC,DVGA,DVFB]
C        -------------------------------------------
C
         DVFD = .FALSE.
         DVGC = .FALSE.
         DVGS = .FALSE.
         DVFR = .FALSE.
         DVFC = .FALSE.
         DVGA = .FALSE.
         DVFB = .FALSE.
C
CD H2060 UPDATE [DVDD] TIMER - 3
C        -----------------------
C
         DVDD = -1
C
CD H2080 UPDATE [DVDC] TIMER - 4
C        -----------------------
C
         DVDC = -1
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  K   ##                               ##
C        #                ##                               ##
C        ####################################################
C
CD K200  CONTROLLER MODE SELECT
C        ----------------------
C
      GOTO ( 1200,2200,3200,4200 ) DVNM
C
C
C
CD L200     GROUND MODE ( NM=1 )
C
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  L   ##                               ##
C        #                ##                               ##
C        ####################################################
C
1200  CONTINUE
C
CD L200  IF AIRBORN FLAG THEN
C        --------------------
C
         IF ( DVGA ) THEN
C
CD L220  UPDATE [DVDD] TIMER - 3
C        -----------------------
C
         DVDD = - 1
C
CD L240  UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD L260  UPDATE [DVNM] SET FLIGHT MODE
C        -----------------------------
C
         DVNM = 3
C
CD L280  UPDATE [DVGS] SLEW FLAG
C        -----------------------
C
         DVGS = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD L1000 IF NUMODE FLAG IS TRUE THEN
C        ---------------------------
C
         IF ( DVFN ) THEN
C
CD L1020 UPDATE [DVDD] TIMER - 3
C        -----------------------
C
         DVDD = DVCL20
C
CD L1040 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD L1060 UPDATE [DVGS] SLEW FLAG
C        -----------------------
C
         DVGS = .TRUE.
C
CD L1080 UPDATE [DVPT] CABIN TARGET PRESS
C        --------------------------------
C
         DVPT = DVPC
C
CD L2000 UPDATE [DVRU] CABIN PRESS UP RATE LIMIT
C        ---------------------------------------
C
         DVRU = DVRLU
C
CD L2020 UPDATE [DVRD] CABIN PRESS DOWN RATE LIMIT
C        -----------------------------------------
C
         DVRD = DVRLD
C
CD L3000 IF TIMER-3 IS LOWER THAN 0 THEN
C        -------------------------------
C
         IF ( DVDD .LT. 0.0 ) THEN
C
CD L3020 IF THROTTLE ADV. FLAG IS ON THEN
C        --------------------------------
C
         IF ( DVFT ) THEN
C
CD L3041 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD L3061 UPDATE [DVNM] SET TAKE OFF MODE
C        -------------------------------
C
         DVNM = 2
C
CD L3081 UPDATE [DVGS] SLEW FLAG
C        -----------------------
C
         DVGS = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD L3040 IF AIRCRAFT ON GROUND THEN
C        --------------------------
C
         IF ( DVGR ) THEN
C
CD       ELSE
C        ----
C
         ELSE
C
CD L3042 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD L3062 UPDATE [DVNM] SET FLIGHT  MODE
C        ------------------------------
C
         DVNM = 3
C
CD L3082 UPDATE [DVGS] SLEW FLAG
C        -----------------------
C
         DVGS = .FALSE.
C
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
      GOTO 6000
C
C
C
CD M200     TAKE OFF  MODE ( NM=2 )
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  M   ##                               ##
C        #                ##                               ##
C        ####################################################
C
2200  CONTINUE
C
CD M200  IF AIRBORN FLAG THEN
C        --------------------
C
         IF ( DVGA ) THEN
C
CD M220  UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD M240  UPDATE [DVNM] SET FLIGHT MODE
C        -----------------------------
C
         DVNM = 3
C
CD       ELSE
C        ----
C
         ELSE
C
CD M1000 IF NUMODE FLAG IS TRUE THEN
C        ---------------------------
C
         IF ( DVFN ) THEN
C
CD M1020 UPDATE [DVPT] CABIN TARGET PRESS
C        --------------------------------
C
         DVPT = DVPC + DVCM20
C
CD M1040 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD M2000 UPDATE [DVRU] CAB PRESS UP RATE LIMIT
C        -------------------------------------
C
         DVRU = DVRLU
C
CD M2020 UPDATE [DVRDL] DOWN RATE LO LIMIT
C        ---------------------------------
C
         DVRDL = DVCM60 * DTXB
C
CD M2040 UPDATE [DVRD] CAB PRESS DOWN RATE LIMIT
C        ---------------------------------------
C
         DVRD = DVRLD
         IF ( DVRD .LT. DVRDL ) THEN
         DVRD = DVRDL
         ENDIF
C
CD M3000 IF AIRCRAFT ON GROUND THEN
C        --------------------------
C
         IF ( DVGR ) THEN
C
CD M3020 IF THROTTLE ADV. FLAG IS ON THEN
C        --------------------------------
C
         IF ( DVFT ) THEN
C
CD       ELSE
C        ----
C
         ELSE
C
CD M3021 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD M3041 UPDATE [DVNM] SET LANDING MODE
C        ------------------------------
C
         DVNM = 4
C
CD M3061 UPDATE [DVFB] ABORT FLAG
C        ------------------------
C
         DVFB = .TRUE.
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD M3022 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD M3042 UPDATE [DVNM] SET FLIGHT MODE
C        -----------------------------
C
         DVNM = 3
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
      GOTO 6000
C
C
CD N200     FLIGHT MODE ( NM=3 )
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  N   ##                               ##
C        #                ##                               ##
C        ####################################################
C
3200  CONTINUE
C
CD N200  IF NUMODE FLAG THEN
C        --------------------
C
         IF ( DVFN ) THEN
C
CD N220  UPDATE [DVFS] DESC DET FIRST PASS FLAG
C        --------------------------------------
C
         DVFS = .FALSE.
C
CD N240  UPDATE [DVDD] TIMER -3
C        ----------------------
C
         DVDD = -1
C
CD N260  UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .FALSE.
C
CD N280  UPDATE [DVGS] SLEW FLAG
C        -----------------------
C
         DVGS = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD N1000 IF CAB PRESS CONT DESCENT FLAG IS ON THEN
C        -----------------------------------------
C
         IF ( DVFD ) THEN
C
CD       ELSE
C        ----
C
         ELSE
C
CD N1020 IF TIMER -3 IS LOWER THAN 0 THEN
C        --------------------------------
C
         IF ( DVDD .LT. 0.0 ) THEN
C
CD N1040 UPDATE [DVDD] TIMER -3
C        ----------------------
C
         DVDD = DVCN20
C
CD N1060 IF DESC DET FIRST PASS FLAG  IS TRUE THEN
C        -----------------------------------------
C
         IF ( DVFS ) THEN
C
CD N1080 UPDATE [DVHR] DESCENT DET ALT DIFF
C        ----------------------------------
C
         DVHR = DVHH - DVHA
C
CD N1600 IF DESCENT DET ALT DIFF IS GREATER THAN DVCN60 THEN
C        ---------------------------------------------------
C
         IF ( DVHR .GT. DVCN60 ) THEN
C
CD N1620 UPDATE [DVHF] DESCENT DET ALT INCR
C        ----------------------------------
C
         DVHF = DVHR
         IF ( DVHR .GT. DVCN61 ) THEN
         DVHF = DVCN61
         ENDIF
C
CD N1640 UPDATE [DVHD] DESCENT DET ALT CHANGE
C        ------------------------------------
C
         DVHD = DVHD + DVHF
C
CD N2000 IF DESCENT DET ALT CHANGE IS GREATER THAN DVCN100 THEN
C        ------------------------------------------------------
C
         IF ( DVHD .GT. DVCN100 ) THEN
C
CD N2020 UPDATE [DVFD] CAB PRESS CONT DESCENT FLAG
C        -----------------------------------------
C
         DVFD = .TRUE.
C
CD N2040 UPDATE [DVDD] TIMER -3
C        ----------------------
C
         DVDD = -1
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD N1621 UPDATE [DVHD] DESCENT DET ALT INCR
C        ----------------------------------
C
         DVHD = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD N1602 UPDATE [DVFS] DESC DET FIRST PASS FLAG
C        --------------------------------------
C
         DVFS = .TRUE.
C
CD N1680 UPDATE [DVHD] DESCENT DET ALT INCR
C        ----------------------------------
C
         DVHD = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD N2060 UPDATE [DVHH] DESCENT DET ALT HOLD
C        ----------------------------------
C
         DVHH = DVHA
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C        ----------------------------
C
         DVPLD = IADVPL + IADVPB
C
CD N3020 IF CAB CLAMP FLAG IS ON THEN
C        ----------------------------
C
         IF ( DVGC ) THEN
C
CD N3040 IF AIRCRAFT ALTITUDE AND AIRCRAFT ALTITUDE CLAMP DIFF.
C                                      GREATER THAN 100 ft THEN
C        ------------------------------------------------------
C
         IF ( ( DVHA - DVHAC ) .GT. DVCN200 ) THEN
C
CD N3061 UPDATE [DVGC] CAB CLAMP FLAG
C        ----------------------------
C
         DVGC = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD N3060 IF AIRCRAFT ALTITUDE AND AIRCRAFT ALTITUDE CLAMP DIFF.
C                                        LOWER THAN 500 ft THEN
C        ------------------------------------------------------
C
         IF ( ( DVHA - DVHAC ) .LT. DVCN201 ) THEN
C
CD N3062 UPDATE [DVGC] CAB CLAMP FLAG
C        ----------------------------
C
         DVGC = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        -----
C
         ELSE
C
CD N3600 IF TIMER -4 GREATER OR EQUAL THAN 0 THEN
C        ----------------------------------------
C
         IF ( DVDC .GE. 0.0 ) THEN
C
CD N3620 UPDATE [DVHAA] CAB CLAMP ALT ABSOL DIFF
C        ---------------------------------------
C
         DVHAA = ABS ( DVHA - DVHAL )
C
CD N3660 IF CAB CLAMP ALT ABSOL DIFF IS LOWER THAN 100 ft
C        ------------------------------------------------
C
         IF ( DVHAA .LT. DVCN260 ) THEN
C
CD N3800 IF TIMER -4 LOWER OR EQUAL THAN YITIM  THEN
C        -------------------------------------------
C
         IF ( DVDC .LE. T ) THEN
C
CD N3820 UPDATE [DVGC] CAB CLAMP FLAG
C        ----------------------------
C
         DVGC = .TRUE.
C
CD N3840 UPDATE [DVFC] CAB EVER CLAMP FLAG
C        ---------------------------------
C
         DVFC = .TRUE.
C
CD N3880 UPDATE [DVDC] TIMER -4
C        ----------------------
C
         DVDC = -1
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD N4001 UPDATE [DVDC] TIMER -4
C        ----------------------
C
         DVDC = DVCN300
C
CD N4021 UPDATE [DVHAL] A/C ALT LAST
C        ---------------------------
C
         DVHAL = DVHA
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD N4000 UPDATE [DVDC] TIMER -4
C        ----------------------
C
         DVDC = DVCN300
C
CD N4020 UPDATE [DVHAL] A/C ALT LAST
C        ---------------------------
C
         DVHAL = DVHA
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD N4040 UPDATE [DVHAC] CAB CLAMP ALT
C        ----------------------------
C
         DVHAC = DVHA
C
CD       CABIN CSHEDULE PRESSURE SETTING
C        -------------------------------
C
         IF ( DVPA .GT. DVPAT(1) ) THEN
C
         DVK = 1
C
         ELSE
C
         IF ( DVPA .LT. DVPAT(35) ) THEN
C
         DVK = 35
C
         ELSE
C
          DO WHILE ( DVPA .LT. DVPAT( DVK + 1))
C
          DVK = DVK + 1
C
          ENDDO
C
          DO WHILE ( DVPA .GT. DVPAT( DVK ))
C
          DVK = DVK - 1
C
          ENDDO
C
         ENDIF
C
         ENDIF
C
CD N5000 UPDATE [DVPS] CABIN SCHEDULE PRESS
C        ----------------------------------
C
         DVPS = DVCM(DVK) * DVPA + DVCB(DVK)
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD N6000 IF CONT. IN AUTO MODE THEN
C        --------------------------
C
         IF ( .NOT.DVSS ) THEN
C
CD N6020 IF CAB EVER CLAMP FLAG OR CAB PRESS CONT DESCENT FLAG ARE ON THEN
C        -----------------------------------------------------------------
C
         IF ( DVFD .OR. DVFC ) THEN
C
CD N6040 IF CABIN SCHEDULE PRESS IS GREATER THAN LANDING PRESS THEN
C        ----------------------------------------------------------
C
         IF ( DVPS .GT. DVPLD ) THEN
C
CD N6060 UPDATE [DVPT] CABIN TARGET PRESS
C        --------------------------------
C
         DVPT = DVPLD
C
CD       ELSE
C        ----
C
         ELSE
C
CD N6061 UPDATE [DVPT] CABIN TARGET PRESS
C        --------------------------------
C
         DVPT = DVPS
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD N6042 UPDATE [DVPT] CABIN TARGET PRESS
C        --------------------------------
C
         DVPT = DVPS
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD N6021 UPDATE [DVPT] CABIN TARGET PRESS
C        --------------------------------
C
         DVPT = DVPLD
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD N6600 UPDATE [DVRU] CAB PRESS UP RATE LIMIT
C        -------------------------------------
C
         DVRU = DVRLU
C
CD N6660 UPDATE [DVRD] CAB PRESS DOWN RATE LIMIT
C        ---------------------------------------
C
         DVRD = DVRLD
C
CD N7000 IF CAB EVER CLAMP FLAG OR CAB PRESS CONT DESCENT FLAG ARE ON THEN
C        -----------------------------------------------------------------
C
         IF ( DVFD .OR. DVFC ) THEN
C
CD N7020
C
C
         ELSEIF ( .NOT.DVSS .AND. DVPC.LT.DVPT ) THEN
C
CD N7040 CAB PRESS DOWN RATE LIM
C        -----------------------
C
         DVRD = DVCN820 * DVRD
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD N8000 IF AIRCRAFT ON GROUND FLAG AND NOT AIRBORN FLAG ON THEN
C        -------------------------------------------------------
C
         IF ( DVGR .AND. .NOT. DVGA ) THEN
C
CD N8020 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD N8040 UPDATE [DVNM] SET LANDING MODE
C        ------------------------------
C
         DVNM = 4
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
C
      GOTO 6000
C
C
C
CD P200     LANDING  MODE ( NM=4 )
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function  P   ##                               ##
C        #                ##                               ##
C        ####################################################
C
4200  CONTINUE
C
CD P200  IF AIRBORN FLAG THEN
C        --------------------
C
         IF ( DVGA ) THEN
C
CD P220  UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD P240  UPDATE [DVNM] SET FLIGHT MODE
C        -----------------------------
C
         DVNM = 3
C
CD P260  UPDATE [DVDD] TIMER - 3
C        -----------------------
C
         DVDD = - 1
C
CD P280  UPDATE [DVFR] RESET FLAG
C        -----------------------
C
         DVFR = .TRUE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD P1000 IF NUMODE FLAG ON THEN
C        ----------------------
C
         IF ( DVFN ) THEN
C
CD P1020 IF ABORT FLAG THEN
C        ------------------
C
         IF ( DVFB ) THEN
C
CD P1041 UPDATE [DVDD] TIMER -3
C        ----------------------
C
         DVDD = DVCP21
C
CD       ELSE
C        ----
C
         ELSE
C
CD P1040 UPDATE [DVDD] TIMER -3
C        ----------------------
C
         DVDD = DVCP20
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD P1200 UPDATE [DVFB] ABORT FLAG
C        ------------------------
C
          DVFB = .FALSE.
C
CD P1220 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .FALSE.
C
CD P1240 UPDATE [DVPT] CABIN TARGET PRESS
C        --------------------------------
C
         DVPT = DVPLD - DVCP60
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD P2000 UPDATE [DVRU] CABIN PRESS UP RATE LIM
C        -------------------------------------
C
         DVRU = DVRLU
C
CD P2020 UPDATE [DVRD] CABIN PRESS DOWN RATE LIM
C        ---------------------------------------
C
         DVRD = DVRLD
C
CD P3000 IF THROTTLE ADV. FLAG ON THEN
C        -----------------------------
C
         IF ( DVFT ) THEN
C
CD P4001 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD P4021 UPDATE [DVNM] SET TAKE OFF MODE
C        -------------------------------
C
         DVNM = 2
C
CD P4041 UPDATE [DVDD] TIMER -3
C        ----------------------
C
         DVDD = -1
C
CD P4061 UPDATE [DVFR] RESET FLAG
C        ------------------------
C
         DVFR = .TRUE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD P3020 IF AIRCRAFT ON GROUND THEN
C        --------------------------
C
         IF ( DVGR ) THEN
C
CD P3040 IF TIMER -3 IS LOWER THAN 0 THEN
C        --------------------------------
C
         IF ( DVDD .LT. 0.0 ) THEN
C
CD P3200 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD P3220 UPDATE [DVNM] SET GROUNG MODE
C        -----------------------------
C
         DVNM = 1
C
CD P3240 UPDATE [DVDD] TIMER -3
C        ----------------------
C
         DVDD = -1
C
CD P3260 UPDATE [DVFR] RESET FLAG
C        ------------------------
C
         DVFR = .TRUE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD P4000 UPDATE [DVFN] NUMODE FLAG
C        -------------------------
C
         DVFN = .TRUE.
C
CD P4020 UPDATE [DVNM] SET FLIGHT MODE
C        -----------------------------
C
         DVNM = 3
C
CD P4040 UPDATE [DVDD] TIMER -3
C        ----------------------
C
         DVDD = -1
C
CD P4060 UPDATE [DVFR] RESET FLAG
C        ------------------------
C
         DVFR = .TRUE.
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
      GOTO 6000
C
C
6000  CONTINUE
C
CD 6000 UPDATE [DVPN] CABIN PRESS COMMAND
C       ---------------------------------
C
        DVPN = DVPT
C
CD      ELSE
C       ----
C
        ELSE
C
CD C202  UPDATE [DVKF] CAB PRES FAULT LIGHT
C        ----------------------------------
C
         DVKF = .FALSE.
C
CD D2002 UPDATE [DVFFC] O/V CLOSE COMMAND FLAG
C        ---------------------------------------
C
         DVFFC = DVBA .AND. .NOT.(DVSD .OR. DVZFO)
C
CD D2082 UPDATE [DVFFO] O/V OPEN COMMAND FLAG
C        ------------------------------------
C
         DVFFO = .NOT. DVFFC
C
CD D3002 UPDATE [DVDD] CAB PRESS CONT TIMER-3
C         ------------------------------------
C
          DVDD = -1
C
CD D3022  UPDATE [DVDC] CAB PRESS CONT TIMER-4
C         ------------------------------------
C
          DVDC = -1
C
CD F1002  UPDATE [DVGF] FAULT FLAG
C         ------------------------
C
          DVGF = .FALSE.
C
CD        ENDIF
C         -----
C
          ENDIF
C
CD S200 IF CPCS IN AUTO MODE AND IN FLIGHT THEN
C       ----------------------------------------
C
        IF ( DVFA .AND. ( DVNM .EQ.3 )) THEN
C
CD S220 IF OFF SCHEDULE THEN
C       --------------------
C
        IF ( ABS ( DVPN -DVPC ) .GT. DVCS1 ) THEN
C
CD S240 SET [DVKM] I/F MAINT RESET DARK CONCEPT
C       ---------------------------------------
C
        DVKM = .TRUE.
C
CD     ELSE
C      ----
C
       ELSE
C
CD S241 SET [DVKM] I/F MAINT RESET DARK CONCEPT
C       ---------------------------------------
C
        DVKM = .FALSE.
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD      ELSE
C       ----
C
        ELSE
C
CD S221 SET [DVKM] I/F MAINT RESET DARK CONCEPT
C       ---------------------------------------
C
        DVKM = .FALSE.
C
C
CD      ENDIF
C       -----
C
        ENDIF
C
C
CD       FREEZE FLAG ENDIF
C        -----------------
C
         ENDIF
C
CD       END
C        ---
C
         END
C$
C$--- EQUATION SUMMARY
C$
C$ 00577 ## Function  A   ##                               ##
C$ 00582 A200  UPDATE [DVFA] CAB PRESS CONT POWER FLAG
C$ 00587 A220  IF CAB PRESS CONT POWER FLAG IS TRUE THEN
C$ 00592 ELSE
C$ 00597 A240  UPDATE [DVDA] POWER INTERRUPT DELAY
C$ 00602 A260  CONTROL POWER FLAG LAST
C$ 00607 ENDIF
C$ 00618 A1000  IF CAB PRESS CONT POWER FLAG IS TRUE OR POWER INTERRUPT DELAY IS
C$ 00624 A1020  IF POWER INTERRUPT DELAY IS LOWER THAN 0 THEN
C$ 00629 A1200  UPDATE [DVDA] POWER INTERRUPT DELAY
C$ 00634 A1220  UPDATE [DVNM] MODE NUMBER
C$ 00639 A1240  UPDATE [DVRU] CAB PRESS UP RATE LIMIT
C$ 00644 A1260  UPDATE [DVRD] CAB PRESS DOWN RATE LIMIT
C$ 00649 A1280  UPDATE [DVPN] CAB PRESS COMMAND
C$ 00654 ELSE
C$ 00659 ENDIF
C$ 00668 A1420  CONTROL POWER FLAG LAST
C$ 00673 A1440  POWER INTERRUPT DELAY
C$ 00680 A2000  IF FAULT FLAG IS TRUE THEN
C$ 00685 A2020  UPDATE [DVNM] MODE NUMBER
C$ 00690 A2040  UPDATE [DVKF] CAB PRESS FAULT LIGHT
C$ 00695 ELSE
C$ 00700 ENDIF
C$ 00709 ## Function  C   ##                               ##
C$ 00714 C200  IF NOT FAULT FLAG AND MODE NUMBER IS DIFFERENT THEN 0 THEN
C$ 00719 C220  UPDATE [DVKF] CAB PRES FAULT LIGHT
C$ 00724 ELSE
C$ 00729 ENDIF
C$ 00734 C1000 UPDATE [DVPC] CABIN PRESSURE
C$ 00739 C1020 IF CABIN PRESSURE IS LOWER THAN DVCC40 THEN
C$ 00744 C1040 UPDATE [DVPC] CABIN PRESSURE
C$ 00749 ELSE
C$ 00754 ENDIF
C$ 00759 C2000 UPDATE [DVPA] ATMOSPHERIC PRESSURE
C$ 00764 C2020 IF ATMOS. PRES. IS LOWER THAN DVCC140 THEN
C$ 00769 C2040 UPDATE [DVPA] ATMOSPHERIC PRESSURE
C$ 00774 ELSE
C$ 00779 ENDIF
C$ 00784 C3000 UPDATE [DVHA] AIRCRAFT ALTITUDE
C$ 00789 C3020 UPDATE [DVGA] AIRBORN FLAG
C$ 00794 C4000 IF CABIN ALT UP RATE IS GREATER THEN HIGH LIMIT THEN
C$ 00799 C4021 UPDATE [DVQLU] CAB ALT UP RATE SELECT
C$ 00804 ELSE
C$ 00809 C4020 IF CABIN ALT UP RATE IS LOWER THEN LOWER LIMIT THEN
C$ 00814
C$ 00815 C4042 UPDATE [DVQLU] CAB ALT UP RATE SELECT
C$ 00820 ELSE
C$ 00825 C4040 UPDATE [DVQLU] CAB ALT UP RATE SELECT
C$ 00830 ENDIF
C$ 00835 ENDIF
C$ 00840 C4060 UPDATE [DVRLU] CAB PRESS UP RATE SELECTED
C$ 00845 C4080 UPDATE [DVRLD] CAB PRESS DOWN RATE SELECTED
C$ 00854 ## Function  D   ##                               ##
C$ 00859 D200  CABIN MAX PRESSURE
C$ 00864 D1000 IF CABIN PRESSURE COMMAND IS GREATER THAN CABIN MAX PRESSURE THEN
C$ 00869 D1020 UPDATE [DVPN] CABIN PRESSURE COMMAND
C$ 00874 ELSE
C$ 00879 ENDIF
C$ 00884 D2000 IF MALF. ON CONTROLLER THEN
C$ 00889 D2020 UPDATE [DVGF] FAULT FLAG
C$ 00894 D2040 IF MALF. O/V COMMANDED CLOSED THEN
C$ 00899 D2061 UPDATE [DVFFC] O/V CLOSE COMMAND FLAG
C$ 00904 D2091 UPDATE [DVFFO] O/V OPEN COMMAND FLAG
C$ 00909 D2101 UPDATE [DVZFCL] AUTO CABIN PRESSURE CONTROLLER F.LAST MALF.
C$ 00914 ELSE
C$ 00919 D2060 UPDATE [DVFFC] O/V CLOSE COMMAND FLAG
C$ 00924 D2080 UPDATE [DVFFO] O/V OPEN COMMAND FLAG
C$ 00929 D2100 UPDATE [DVZFOL] LOSS OF CABIN PRESSURE LAST MALF.
C$ 00934 ENDIF
C$ 00939 ELSE
C$ 00944 D2022 UPDATE [DVFFC] O/V CLOSE COMMAND FLAG
C$ 00949 D2092 UPDATE [DVFFO] O/V OPEN COMMAND FLAG
C$ 00954 ENDIF
C$ 00959 D2500 IF LOSS OF CABIN PRESSURE MALF. DE-SELECTED
C$ 00964 D2520  UPDATE [DVGF] FAULT FLAG
C$ 00969 D2540  UPDATE [DVZFOL] UPDATE [DVZFOL] LOSS OF CABIN PRESSURE LAST MALF
C$ 00974 ELSE
C$ 00979 ENDIF
C$ 00984 D2600  IF AUTO CABIN PRESSURE CONTROLLER F. MALF. DE-SELECTED THEN
C$ 00989 D2620  UPDATE [DVGF] FAULT FLAG
C$ 00994 D2640  UPDATE [DVZFCL] AUTO CABIN PRESSURE CONTROLLER F.LAST MALF.
C$ 00999 ELSE
C$ 01004 ENDIF
C$ 01009 D3000  IF FLIGHT FREEZE THEN
C$ 01014 ELSE
C$ 01019 D3000  UPDATE [DVDD] CAB PRESS CONT TIMER-3
C$ 01024 D3020  UPDATE [DVDC] CAB PRESS CONT TIMER-4
C$ 01029 ENDIF
C$ 01036 ## Function  F   ##                               ##
C$ 01041 F200  IF MODE FLAG IS 0 THEN
C$ 01046 F220  IF AIRCRAFT ON GROUND THEN
C$ 01051 F240  IF THROTTLE ADV. FLAG THEN
C$ 01056 H1000 UPDATE [DVDF] FAULT DELAY
C$ 01061 H1020 UPDATE [DVFN] NUMODE FLAG
C$ 01066 H1040 UPDATE [DVNM] FLAG MODE TO TAKE OFF MODE
C$ 01071 ELSE
C$ 01076 F260  UPDATE [DVGF] FAULT FLAG
C$ 01081 F1000 IF PRESS. CONT. IN AUTO MODE THEN
C$ 01086 F1020 IF ATMOS. PRESS. - CABIN PRESSURE IS GREATER THAN DVCF20 THEN
C$ 01091 F1040 UPDATE [DVGF] FAULT FLAG
C$ 01096 ELSE
C$ 01101 ENDIF
C$ 01106 ELSE
C$ 01111 ENDIF
C$ 01116 F2000 UPDATE [DVGE] MUX FAULT
C$ 01121 F2020 IF ALTITUDE SELECTED IS NOT BETWEEN -1500 AND 14500 FT THEN
C$ 01127 F2040 UPDATE [DVGE] MUX FAULT
C$ 01132 ELSE
C$ 01137 ENDIF
C$ 01142 F2060 IF BAROMETRIC CORRECTION SELECTED IS NOT
C$ 01149 F2080 UPDATE [DVGE] MUX FAULT
C$ 01154 ELSE
C$ 01159 ENDIF
C$ 01164 F3000 IF CABIN RATE SELECTED IS NOT BETWEEN 50 AND 2800 ft THEN
C$ 01170 F3020 UPDATE [DVGE] MUX FAULT
C$ 01175 ELSE
C$ 01180 ENDIF
C$ 01185 F4000 IF MUX FAULT IS TRUE THEN
C$ 01190 F4021 UPDATE [DVDF] FAULT TIME DELAY
C$ 01195 F4041 UPDATE [DVGF] FAULT FLAG
C$ 01201 ELSE
C$ 01206 F4020 UPDATE [DVDF] FAULT TIME DELAY
C$ 01211 F4040 IF FAULT TIME DELAY IS GREATER THAN 0 THEN
C$ 01216 F4060 UPDATE [DVGF] FAULT FLAG
C$ 01221 ELSE
C$ 01226 ENDIF
C$ 01231 ENDIF
C$ 01240 ## Function  H   ##                               ##
C$ 01245 H200  UPDATE [DVFN] NIMODE FLAG
C$ 01250 H220  UPDATE [DVNM] SET GROUND MODE
C$ 01255 ENDIF
C$ 01260 ELSE
C$ 01265 H1001 UPDATE [DVDF] FAULT DELAY
C$ 01270 H1021 UPDATE [DVFN] NUMODE FLAG
C$ 01275 H1041 UPDATE [DVNM] FLAG MODE TO FLIGHT  MODE
C$ 01280 ENDIF
C$ 01285 ELSE
C$ 01290 H1060 UPDATE [DVDF] FAULT DELAY
C$ 01295 ENDIF
C$ 01301 H2000 IF RESET FLAG THEN
C$ 01306 H2020 UPDATE [DVFS] DESC DET FIRST PASS FLAG
C$ 01311 H2040 UPDATE [DVFD,DVGC,DVGS,DVFR,DVFC,DVGA,DVFB]
C$ 01322 H2060 UPDATE [DVDD] TIMER - 3
C$ 01327 H2080 UPDATE [DVDC] TIMER - 4
C$ 01332 ELSE
C$ 01337 ENDIF
C$ 01345 ## Function  K   ##                               ##
C$ 01349 K200  CONTROLLER MODE SELECT
C$ 01356 L200     GROUND MODE ( NM=1 )
C$ 01362 ## Function  L   ##                               ##
C$ 01368 L200  IF AIRBORN FLAG THEN
C$ 01373 L220  UPDATE [DVDD] TIMER - 3
C$ 01378 L240  UPDATE [DVFN] NUMODE FLAG
C$ 01383 L260  UPDATE [DVNM] SET FLIGHT MODE
C$ 01388 L280  UPDATE [DVGS] SLEW FLAG
C$ 01393 ELSE
C$ 01398 L1000 IF NUMODE FLAG IS TRUE THEN
C$ 01403 L1020 UPDATE [DVDD] TIMER - 3
C$ 01408 L1040 UPDATE [DVFN] NUMODE FLAG
C$ 01413 ELSE
C$ 01418 ENDIF
C$ 01423 L1060 UPDATE [DVGS] SLEW FLAG
C$ 01428 L1080 UPDATE [DVPT] CABIN TARGET PRESS
C$ 01433 L2000 UPDATE [DVRU] CABIN PRESS UP RATE LIMIT
C$ 01438 L2020 UPDATE [DVRD] CABIN PRESS DOWN RATE LIMIT
C$ 01443 L3000 IF TIMER-3 IS LOWER THAN 0 THEN
C$ 01448 L3020 IF THROTTLE ADV. FLAG IS ON THEN
C$ 01453 L3041 UPDATE [DVFN] NUMODE FLAG
C$ 01458 L3061 UPDATE [DVNM] SET TAKE OFF MODE
C$ 01463 L3081 UPDATE [DVGS] SLEW FLAG
C$ 01468 ELSE
C$ 01473 L3040 IF AIRCRAFT ON GROUND THEN
C$ 01478 ELSE
C$ 01483 L3042 UPDATE [DVFN] NUMODE FLAG
C$ 01488 L3062 UPDATE [DVNM] SET FLIGHT  MODE
C$ 01493 L3082 UPDATE [DVGS] SLEW FLAG
C$ 01499 ENDIF
C$ 01504 ENDIF
C$ 01509 ENDIF
C$ 01514 ENDIF
C$ 01524 M200     TAKE OFF  MODE ( NM=2 )
C$ 01529 ## Function  M   ##                               ##
C$ 01535 M200  IF AIRBORN FLAG THEN
C$ 01540 M220  UPDATE [DVFN] NUMODE FLAG
C$ 01545 M240  UPDATE [DVNM] SET FLIGHT MODE
C$ 01550 ELSE
C$ 01555 M1000 IF NUMODE FLAG IS TRUE THEN
C$ 01560 M1020 UPDATE [DVPT] CABIN TARGET PRESS
C$ 01565 M1040 UPDATE [DVFN] NUMODE FLAG
C$ 01570 ELSE
C$ 01575 ENDIF
C$ 01580 M2000 UPDATE [DVRU] CAB PRESS UP RATE LIMIT
C$ 01585 M2020 UPDATE [DVRDL] DOWN RATE LO LIMIT
C$ 01590 M2040 UPDATE [DVRD] CAB PRESS DOWN RATE LIMIT
C$ 01598 M3000 IF AIRCRAFT ON GROUND THEN
C$ 01603 M3020 IF THROTTLE ADV. FLAG IS ON THEN
C$ 01608 ELSE
C$ 01613 M3021 UPDATE [DVFN] NUMODE FLAG
C$ 01618 M3041 UPDATE [DVNM] SET LANDING MODE
C$ 01623 M3061 UPDATE [DVFB] ABORT FLAG
C$ 01628 ENDIF
C$ 01633 ELSE
C$ 01638 M3022 UPDATE [DVFN] NUMODE FLAG
C$ 01643 M3042 UPDATE [DVNM] SET FLIGHT MODE
C$ 01648 ENDIF
C$ 01653 ENDIF
C$ 01661 N200     FLIGHT MODE ( NM=3 )
C$ 01666 ## Function  N   ##                               ##
C$ 01672 N200  IF NUMODE FLAG THEN
C$ 01677 N220  UPDATE [DVFS] DESC DET FIRST PASS FLAG
C$ 01682 N240  UPDATE [DVDD] TIMER -3
C$ 01687 N260  UPDATE [DVFN] NUMODE FLAG
C$ 01692 N280  UPDATE [DVGS] SLEW FLAG
C$ 01697 ELSE
C$ 01702 ENDIF
C$ 01707 N1000 IF CAB PRESS CONT DESCENT FLAG IS ON THEN
C$ 01712 ELSE
C$ 01717 N1020 IF TIMER -3 IS LOWER THAN 0 THEN
C$ 01722 N1040 UPDATE [DVDD] TIMER -3
C$ 01727 N1060 IF DESC DET FIRST PASS FLAG  IS TRUE THEN
C$ 01732 N1080 UPDATE [DVHR] DESCENT DET ALT DIFF
C$ 01737 N1600 IF DESCENT DET ALT DIFF IS GREATER THAN DVCN60 THEN
C$ 01742 N1620 UPDATE [DVHF] DESCENT DET ALT INCR
C$ 01750 N1640 UPDATE [DVHD] DESCENT DET ALT CHANGE
C$ 01755 N2000 IF DESCENT DET ALT CHANGE IS GREATER THAN DVCN100 THEN
C$ 01760 N2020 UPDATE [DVFD] CAB PRESS CONT DESCENT FLAG
C$ 01765 N2040 UPDATE [DVDD] TIMER -3
C$ 01770 ELSE
C$ 01775 ENDIF
C$ 01780 ELSE
C$ 01785 N1621 UPDATE [DVHD] DESCENT DET ALT INCR
C$ 01790 ENDIF
C$ 01795 ELSE
C$ 01800 N1602 UPDATE [DVFS] DESC DET FIRST PASS FLAG
C$ 01805 N1680 UPDATE [DVHD] DESCENT DET ALT INCR
C$ 01810 ENDIF
C$ 01815 N2060 UPDATE [DVHH] DESCENT DET ALT HOLD
C$ 01820 ELSE
C$ 01825 ENDIF
C$ 01830 ENDIF
C$ 01838 N3020 IF CAB CLAMP FLAG IS ON THEN
C$ 01843 N3040 IF AIRCRAFT ALTITUDE AND AIRCRAFT ALTITUDE CLAMP DIFF.
C$ 01849 N3061 UPDATE [DVGC] CAB CLAMP FLAG
C$ 01854 ELSE
C$ 01859 N3060 IF AIRCRAFT ALTITUDE AND AIRCRAFT ALTITUDE CLAMP DIFF.
C$ 01865 N3062 UPDATE [DVGC] CAB CLAMP FLAG
C$ 01870 ELSE
C$ 01875 ENDIF
C$ 01880 ENDIF
C$ 01885 ELSE
C$ 01890 N3600 IF TIMER -4 GREATER OR EQUAL THAN 0 THEN
C$ 01895 N3620 UPDATE [DVHAA] CAB CLAMP ALT ABSOL DIFF
C$ 01900 N3660 IF CAB CLAMP ALT ABSOL DIFF IS LOWER THAN 100 ft
C$ 01905 N3800 IF TIMER -4 LOWER OR EQUAL THAN YITIM  THEN
C$ 01910 N3820 UPDATE [DVGC] CAB CLAMP FLAG
C$ 01915 N3840 UPDATE [DVFC] CAB EVER CLAMP FLAG
C$ 01920 N3880 UPDATE [DVDC] TIMER -4
C$ 01925 ELSE
C$ 01930 ENDIF
C$ 01935 ELSE
C$ 01940 N4001 UPDATE [DVDC] TIMER -4
C$ 01945 N4021 UPDATE [DVHAL] A/C ALT LAST
C$ 01950 ENDIF
C$ 01955 ELSE
C$ 01960 N4000 UPDATE [DVDC] TIMER -4
C$ 01965 N4020 UPDATE [DVHAL] A/C ALT LAST
C$ 01970 ENDIF
C$ 01975 N4040 UPDATE [DVHAC] CAB CLAMP ALT
C$ 01980 CABIN CSHEDULE PRESSURE SETTING
C$ 02011 N5000 UPDATE [DVPS] CABIN SCHEDULE PRESS
C$ 02016 ENDIF
C$ 02021 N6000 IF CONT. IN AUTO MODE THEN
C$ 02026 N6020 IF CAB EVER CLAMP FLAG OR CAB PRESS CONT DESCENT FLAG ARE ON THEN
C$ 02031 N6040 IF CABIN SCHEDULE PRESS IS GREATER THAN LANDING PRESS THEN
C$ 02036 N6060 UPDATE [DVPT] CABIN TARGET PRESS
C$ 02041 ELSE
C$ 02046 N6061 UPDATE [DVPT] CABIN TARGET PRESS
C$ 02051 ENDIF
C$ 02056 ELSE
C$ 02061 N6042 UPDATE [DVPT] CABIN TARGET PRESS
C$ 02066 ENDIF
C$ 02071 ELSE
C$ 02076 N6021 UPDATE [DVPT] CABIN TARGET PRESS
C$ 02081 ENDIF
C$ 02086 N6600 UPDATE [DVRU] CAB PRESS UP RATE LIMIT
C$ 02091 N6660 UPDATE [DVRD] CAB PRESS DOWN RATE LIMIT
C$ 02096 N7000 IF CAB EVER CLAMP FLAG OR CAB PRESS CONT DESCENT FLAG ARE ON THEN
C$ 02101 N7020
C$ 02106 N7040 CAB PRESS DOWN RATE LIM
C$ 02111 ELSE
C$ 02116 ENDIF
C$ 02121 N8000 IF AIRCRAFT ON GROUND FLAG AND NOT AIRBORN FLAG ON THEN
C$ 02126 N8020 UPDATE [DVFN] NUMODE FLAG
C$ 02131 N8040 UPDATE [DVNM] SET LANDING MODE
C$ 02136 ELSE
C$ 02141 ENDIF
C$ 02152 P200     LANDING  MODE ( NM=4 )
C$ 02157 ## Function  P   ##                               ##
C$ 02163 P200  IF AIRBORN FLAG THEN
C$ 02168 P220  UPDATE [DVFN] NUMODE FLAG
C$ 02173 P240  UPDATE [DVNM] SET FLIGHT MODE
C$ 02178 P260  UPDATE [DVDD] TIMER - 3
C$ 02183 P280  UPDATE [DVFR] RESET FLAG
C$ 02188 ELSE
C$ 02193 P1000 IF NUMODE FLAG ON THEN
C$ 02198 P1020 IF ABORT FLAG THEN
C$ 02203 P1041 UPDATE [DVDD] TIMER -3
C$ 02208 ELSE
C$ 02213 P1040 UPDATE [DVDD] TIMER -3
C$ 02218 ENDIF
C$ 02223 P1200 UPDATE [DVFB] ABORT FLAG
C$ 02228 P1220 UPDATE [DVFN] NUMODE FLAG
C$ 02233 P1240 UPDATE [DVPT] CABIN TARGET PRESS
C$ 02238 ELSE
C$ 02243 ENDIF
C$ 02248 P2000 UPDATE [DVRU] CABIN PRESS UP RATE LIM
C$ 02253 P2020 UPDATE [DVRD] CABIN PRESS DOWN RATE LIM
C$ 02258 P3000 IF THROTTLE ADV. FLAG ON THEN
C$ 02263 P4001 UPDATE [DVFN] NUMODE FLAG
C$ 02268 P4021 UPDATE [DVNM] SET TAKE OFF MODE
C$ 02273 P4041 UPDATE [DVDD] TIMER -3
C$ 02278 P4061 UPDATE [DVFR] RESET FLAG
C$ 02283 ELSE
C$ 02288 P3020 IF AIRCRAFT ON GROUND THEN
C$ 02293 P3040 IF TIMER -3 IS LOWER THAN 0 THEN
C$ 02298 P3200 UPDATE [DVFN] NUMODE FLAG
C$ 02303 P3220 UPDATE [DVNM] SET GROUNG MODE
C$ 02308 P3240 UPDATE [DVDD] TIMER -3
C$ 02313 P3260 UPDATE [DVFR] RESET FLAG
C$ 02318 ELSE
C$ 02323 ENDIF
C$ 02328 ELSE
C$ 02333 P4000 UPDATE [DVFN] NUMODE FLAG
C$ 02338 P4020 UPDATE [DVNM] SET FLIGHT MODE
C$ 02343 P4040 UPDATE [DVDD] TIMER -3
C$ 02348 P4060 UPDATE [DVFR] RESET FLAG
C$ 02353 ENDIF
C$ 02358 ENDIF
C$ 02363 ENDIF
C$ 02374 6000 UPDATE [DVPN] CABIN PRESS COMMAND
C$ 02379 ELSE
C$ 02384 C202  UPDATE [DVKF] CAB PRES FAULT LIGHT
C$ 02389 D2002 UPDATE [DVFFC] O/V CLOSE COMMAND FLAG
C$ 02394 D2082 UPDATE [DVFFO] O/V OPEN COMMAND FLAG
C$ 02399 D3002 UPDATE [DVDD] CAB PRESS CONT TIMER-3
C$ 02404 D3022  UPDATE [DVDC] CAB PRESS CONT TIMER-4
C$ 02409 F1002  UPDATE [DVGF] FAULT FLAG
C$ 02414 ENDIF
C$ 02419 S200 IF CPCS IN AUTO MODE AND IN FLIGHT THEN
C$ 02424 S220 IF OFF SCHEDULE THEN
C$ 02429 S240 SET [DVKM] I/F MAINT RESET DARK CONCEPT
C$ 02434 ELSE
C$ 02439 S241 SET [DVKM] I/F MAINT RESET DARK CONCEPT
C$ 02444 ENDIF
C$ 02449 ELSE
C$ 02454 S221 SET [DVKM] I/F MAINT RESET DARK CONCEPT
C$ 02460 ENDIF
C$ 02466 FREEZE FLAG ENDIF
C$ 02471 END
