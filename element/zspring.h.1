/*********************************************************************
C fspring.h   V2.00
C
C'Revision_History
C
C  fspring.h.1 11Jun1992 11:47 ja34 mzahn  
C       < deleted fspring array size definition (now in cf_def.h) >:
C
C 09-Jun-92     M Zahn
C         Added feelspring array size definitions.
C         Made declaration of external structures and functions
C          conditional on non-definition of FSPRING_SRC.
*********************************************************************/

struct FEEL_INT{
   float oldvari;          /* previous value of interp variable  */
   int   nx;               /* number of position breakpoints     */
   int   ix;               /* interpolated curve position index  */
   float pos[MAX_FEEL];    /* interpolated curve position bkpts  */
   float slfor[MAX_FEEL];  /* interpolated curve force slopes    */
   float slfri[MAX_FEEL];  /* interpolated curve friction slopes */
   float force[MAX_FEEL];  /* interpolated curve force intercepts*/
   float fric[MAX_FEEL];   /* interpolated curve friction intcpts*/
   };

struct FEEL_PTR{
   int   valid;            /* data valid flag                    */
   int   use;              /* data buffer to use                 */
   int   *err;             /* pointer to feelspring error index  */
   int   *aft;             /* pointer to aft feelspring flag     */
   int   *nx;              /* pointer to number of breakpoints   */
   int   *ncurve;          /* pointer to number of curves        */
   float *curve;           /* pointer to curve breakpoints       */
   float *pos;             /* pointer to position breakpoints    */
   float *force;           /* pointer to force data              */
   float *fric;            /* pointer to friction data           */
   float *kn;              /* pointer to notch stiffness         */
   float *npl;             /* pointer to positive notch level    */
   float *nnl;             /* pointer to negative notch level    */
   float *kc;              /* pointer to cable stiffness         */
   float *xmin;            /* pointer to minimum position bkpt   */
   float *xmax;            /* pointer to maximum position bkpt   */
   int   *data0;           /* pointer to data buffer 0           */
   int   *data1;           /* pointer to data buffer 1           */
   int   *datain;          /* pointer to interpolated curve data */
   };

#ifndef FSPRING_SRC

extern struct FEEL_PTR feel_table[MAX_SERVO];

extern int feel_init();
extern int feel_mod();
extern float feel_interp_for();
extern float feel_interp_fri();
void   feel_check();
void   feel_vari();

#endif
