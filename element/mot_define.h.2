/*----------------------------------------------------------------------------

  'Title                MOTION DEFINITIONS
  'Module_ID            define.h
  'Entry_point
  'Documentation
  'Customer             QANTAS
  'Application          all motion programs
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       n/a
  'Process              n/a

------------------------------------------------------------------------------

  'Revision_history

  'References

*/

#define TRUE  1              /* logical TRUE */
#define FALSE 0              /* logical FALSE */

#define MAX(x,y)     (((x) > (y)) ? (x) : (y))
#define MIN(x,y)     (((x) < (y)) ? (x) : (y))
#define LIMIT(x,y,z) MIN(z,MAX(x,y))
/*
* 	added by k.unger
*/
#define max(A,B)     (((A) > (B)) ? (A) : (B))
#define min(A,B)     (((A) < (B)) ? (A) : (B))
#define	limit(A,B,C) (((A) < (B)) ? (B) : (((A) > (C)) ? (C) : (A)))
#define abs(A)       (((A) > 0.) ? (A) : -(A))
/*
*    	site identifier
*/
#define CTS 1
#define HTF 2
#define QA74 3
#define AW37 4

/*
*	---------------------------------------------------
*                   OPTIONS
*	---------------------------------------------------
*/

/*
*	Motion type  ( MOTYPE)
*/
#define MOT300 1
#define MOT500 2
#define MOT550 3
#define MOT750 4
#define MOT600 5
#define MOT800 6

/*
*	pressure transducer type (MPRESTYPE)
*/
#define P300 0
#define P600 1
/*
*	accelerometer package  ( MTACCEL)
*/
#define NOBOX 0			/* NO accelerator boxes */
#define SINGLE_BOX_1 1	   	/* single box installed at J5+J6 BEARING */
#define SINGLE_BOX_2 2		/* single box installed at J3+J4 BEARING */
#define FULL_PLAT 3		/* full platform accelerometer */

/*
*	force scaling used on buffer UNIT (MFORCESCAL)
*/
#define F11250 0       /* 11250 lbs/10 volts (upper BU R1=R2=R11=R12=100Kohms)*/
#define F20000 1       /* 20000 lbs/10 volts (upper BU R1=R2=R11=R12=55Kohms)*/
/*
*	position transducer scaling on temposonic circuit (MPOSSCAL)
*/
#define P9 0        /* 9.0 volts for full extension */
#define P10 1       /* 10.0 volts for full extension */
/*
*	motion valve type (MVALVETYPE)
*/
#define PEGASUS 0	/* PEGASUS motion valves */
#define MOOG 1   	/* MOOG motion valves */
/*
* definitions used by motion
*/
#define JACK1 0 /* motion system jack 1 */
#define JACK2 1 /* motion system jack 2 */
#define JACK3 2 /* motion system jack 3 */
#define JACK4 3 /* motion system jack 4 */
#define JACK5 4 /* motion system jack 5 */
#define JACK6 5 /* motion system jack 6 */
#define JACKX 6 /* motion system jack 6 */

#define LONG 0         /* motion axis used as index in array */
#define LAT 1
#define HEAV 2
#define ROLL 3
#define PICH 4
#define YAW 5

#define AXISLONG 1         /* motion axis used by MTAXISREQ,MTAXIS */
#define AXISLAT 2
#define AXISHEAV 3
#define AXISROLL 4
#define AXISPICH 5
#define AXISYAW 6
#define AXISJ1 7
#define AXISJ2 8
#define AXISJ3 9
#define AXISJ4 10
#define AXISJ5 11
#define AXISJ6 12

/*
*	---------------------------------------------------
*                MISCELLENEOUS MOTION DEFINITIONS
*	---------------------------------------------------
*/
/*
*	--------------------
*	motion output states ( MOUTSTATE )
*	--------------------
*/
#define ACTIVE 1
#define FROZEN 2
#define WASHED 3

/*
*   ------------------------
*   failure test for dn1 tip
*   ------------------------
*/
#define ENABLED 0
#define TEST 1

/*
*  --------------
*  failure levels ( MFAILEVEL )
*  --------------
*/
#define ABORT 10
#define STANDBY 20
#define OFFREQ 30
#define FREEZE 40
#define WARNING 50
#define NOTICE 60
#define NOFAIL 99

/*
*	---------------------------------------------------
*                   MOTION TEST
*	---------------------------------------------------
* 	automated tests for mtfaa, morning readiness, manual
*	and auto tuning are combined in MTTEST_KEY
*/
/*
*	mtfaa tests ( MTMTFAAKEY and MTTEST_KEY )
*/
#define TABUMP 1 	/* turn around bump */
#define LEGBAL 2        /* leg balance */
#define FRQRESP 3       /* freq response */
#define POSFRQRESP 4    /* position freq response */
#define POSLINEAR1 5     /* position linearity, freq 1 , for CAA */
#define POSLINEAR2 6     /* position linearity, freq 2 , for CAA */
#define ACCELPERF1 7     /* acceleration perlormance freq 1 , for CAA */
#define ACCELPERF2 8    /* acceleration perlormance freq 2 , for CAA */
/*
*	morning readiness ( MTMORNKEY and MTTEST_KEY )
*/
#define MORN1 11	/* jack velocity vs valve current graph */
#define MORN2 12        /* velocity error shown on screen */
#define MORN3 13        /* velocity gain and phase shown on screen */
#define MORN4 14        /* friction test, shown on screen */
/*
*	motion tuning test ( MTTUNE_KEY, MTAUTOTUNE_KEY and MTTEST_KEY )
*/
#define T_KV 21
#define T_KL 22
#define T_KBET 23
#define T_KMID 24
#define T_KCO 25
/*
*	Automated scaling   ( MTSCALEKEY and MTTEST_KEY )
*/
#define NOTEST 0
#define WARM_UP 31
#define POS_OFS 32
#define CURR_OFS 33
#define PRES_OFS 34
#define FRIC_OFS 35
#define ACCEL_OFS 36
/*
*	MT program
*/
#define ATTITUDE 1 	/* MT in attitude mode ( MTMODE ) */
#define ACTUATOR 2      /* MT in actuator mode ( MTMODE ) */

#define DISPL 1		/* amplitude selection mode : */
#define ACCEL 2         /* displacement or accel ( MTAMPSEL ) */

#define SINE 1  	/* waves ( MTWAVE ) */
#define TRIANGLE 2
#define SQUARE 3


#define OFF 1            /* motion test program status ( MTSTATUS ) */
#define STOP 2
#define WASHOUT 3
#define DRIVE 4
#define ANALYS 5

#define NOANAL 0         /* no analysis flag */
#define FRA 1            /* code for analysis to be done ( MTANALYS ) */
#define RMS 2

#define CONTINU 1         /* ( MTDRIVMODE ) continuous or table drive */
#define TABLE 2

#define NONE 1
#define SINGLE 2          /* ( MTCHANANAL ) : channels analysed */
#define ALL 3
                          /* MTRSLTMODE */
#define REPORT 1   	  /* report single result generated at end of test*/
#define SCROLL_UP 2       /* scroll up results on screen */
#define SCROLL_MAX 3      /* scroll up maximum result on screen */
#define BODEPLOT 4        /* bode plot: scroll up results and plot package */
#define GRAPH 5           /* only graph generated */
#define MAPOUT 6          /* map signals to patch panel */

/* key for signal selection MTINPUT_KEY, MTOUTPUT_KEY */

#define KAA 1	 /* accelerometers */
#define KAP 2    /* commanded plaform accelerations */
#define KAC 3    /* commanded jack acceleration */
#define KAD 4    /* demanded acceleration */
#define KVAC 5   /* actual jack velocity */
#define KVR 6    /* requested velocity */
#define KVC 7   /* commanded velocity */
#define KVE 8    /* velocity error */
#define KXAC 9   /* actual position */
#define KXC 10    /* commanded position */
#define KXE 11    /* position error */
#define KIAC 12   /* actual current */
#define KIC 13   /* commanded current */
#define KIE 14   /* current error */
#define KFRIC 15   /* friction force */


/*
*	Math constants
*/
#define PI 3.141592654
#define TWOPI 6.283185308
#define RADTODEG 57.2957795
#define DEGTORAD .017453293
#define GINCH 386.4
#define GINCHINV .002587992

/*
* 	Defines for buffets
*/
#define MAX_IN 30
#define MAX_OUT 128

/*
*	--------------------------------
*		BIT MASKS
*	--------------------------------
*/
#define EXBUS_BASE_ADDRESS  0x805000
#define SERIAL_PORT0_CNTREG 0x808042
#define SUCCESS 1
/*
*	Bit masks for Buffer Unit
*/
#define TOGGLEDOP 0x1
#define NORMALDOP 0x2
#define STANDBYDOP 0x4

#define POWERFAILDIP 0x1
#define STANDBYDIP 0x2
#define NORMALDIP 0x4

#define ENERGIZED 0x1
#define DEENERGIZED 0x0

/*
* 	ADIO DEFINITIONS
*/
#define ADIO_SLOT1 2 	/* slot number for motion ADIO #1: xa2 */
#define ADIO_SLOT2 3    /* slot number for motion ADIO #2: xa3 */
#define ADIO_SLOT3 4   /* slot number for motion ADIO #3: xa4 */
#define ADIO_SLOT4 5   /* slot number for motion ADIO #4: xa5 */
#define ADIO_SLOT5 6   /* slot number for motion ADIO #5: xa6 */


/*
*	---------------------------------------------------
*                   DEFINITIONS USED BY LOGIC
*	---------------------------------------------------
*/
#define LOGIC_HEAV 0 /* axis heave in motion test mode */
#define LOGIC_LONG 1 /* axis longitudinal in motion test mode */
#define LOGIC_LAT  2 /* axis lateral in motion test mode */
#define LOGIC_YAW  3 /* axis YAW in motion test mode */
#define LOGIC_PICH 4 /* axis PICH in motion test mode */
#define LOGIC_ROLL 5 /* axis ROLL in motion test mode */

/*
* 	cabinet states ( L2M_CABSTATE )
*/
#define CAB_OFF                   1 /* cabinet in OFF mode */
#define CAB_READY                 2 /* cabinet in READY mode */
#define CAB_ON_NORMAL             3 /* cabinet in ON mode */
#define CAB_HYD_MAINT             4 /* cabinet in mode */
#define CAB_CL_ON_HYD_MAINT       5 /* cabinet in mode */
#define CAB_MOT_ON_HYD_MAINT      6 /* cabinet in mode */
#define CAB_CL_MAINT              7 /* cabinet in mode */
#define CAB_MOT_TEST_ATTITUDE     8 /* cabinet in mode */
#define CAB_MOT_TEST_ACTUATOR     9 /* cabinet in mode */
#define CAB_MOT_TEST_COMPUTER    10 /* cabinet in mode */

/*
*            +==================================================+
*            |                                                  |
*            |           I/O MAILBOX DEFINITIONS                |
*            |                                                  |
*            +==================================================+
*/
#define SCALE_32767 32767

#define   LREQ_MBX       0x1111           /* logic request mailbox       */
#define   ERROR_MBX      0x3333           /* error logger mailbox        */
#define   CHANDEF_MBX    0x4444           /* channel definition mailbox */
#define   LMTEST_MBX     0x6666           /* motion test mailbox      */
#define   MOTION_MBX     0x7777           /* channel status mailbox      */
#define   OPTION_MBX     0x8888           /* option mailbox      */
#define   JACK_MBX       0x9999           /* jack info to logic mailbox */
/*
*            +==================================================+
*            |                                                  |
*            |      C30 to LOGIC FAILURE code and CHANNEL code  |
* 	     | 			DEFINITIONS       	        |
*            |                                                  |
*            +==================================================+
*/
#define  MAX_ERROR  20	/* MAXIMUM NUMBER OF FAILURE MESSAGES */

#define C_MOTION 99	/* MOTION GENERIC CHANNEL */
#define C_NOCHAN 98     /* NO CHANNEL NAME REQUIRED WITH DESCRIPTION */
#define C_JACK1 0 	/* FAILED JACK */
#define C_JACK2 1
#define C_JACK3 2
#define C_JACK4 3
#define C_JACK5 4
#define C_JACK6 5

#define C_MF_INVSW    10
#define C_MF_HCHKSUM  11
#define C_MF_UBUNOTC 14

#define C_MF_POSER   21
#define C_MF_VELER   42
#define C_MF_CURER   22


#define C_MF_TRAVL   15
#define C_MF_EXVEL   39
#define C_MF_EXFOR   23
#define C_MF_JFRIC    16

#define C_MF_BUPFAIL  17
#define C_MF_NOTC     18

#define C_MF_BUSTDBY  19
#define C_MF_BUNORM   20

#define C_MF_VLVPROB  40
#define C_MF_STNDBY   30

#define C_MF_POSDC  24
#define C_MF_POSRNG   25
#define C_MF_CAPRNG   27
#define C_MF_RODRNG   28
#define C_MF_FORRNG   29

#define C_MF_CONREST  26

#define C_MF_UPTIMOUT 6
#define C_MF_DWTIMOUT 7

#define C_MF_ADIO1 52
#define C_MF_ADIO2 53
#define C_MF_ADIO3 54
#define C_MF_ADIO4 55
#define C_MF_ADIO5 56

#define C_MF_FADIO1 69
#define C_MF_FADIO2 70
#define C_MF_FADIO3 71
#define C_MF_FADIO4 72
#define C_MF_FADIO5 73

#define C_MF_COMM 50

#define C_MF_OVERRUN 58

#define C_MF_NOOPT 59

/*    warning */

#define C_MW_POSER   33
#define C_MW_VELER   31
#define C_MW_CURER   34

#define C_MW_TRAVL   35
#define C_MW_EXVEL   32
#define C_MW_EXFOR   37
#define C_MW_JFRIC    36

#define C_MW_POSDC  38

/*
*	SAFETY disable messages
*/

#define C_MW_NOSAFE 57

