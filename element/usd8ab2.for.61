C'Title                 DASH-8 100,300 Brakes System
C'Module_ID             USD8AB2
C'Entry_Point           ABRAK2
C'Documentation         Brake SDD
C'Application           Simulation of the DASH-8 Brakes System
C'Author                <PERSON> (3540)
C'Date                  January 1992
C
C'System                Ancillaries (Dept 21)
C'Itrn_rate             33 msec
C'Process               Synchronous process
C
C
C'Compilation_directives
C
C       Compile and add in SHIPDIR library.
C       It must be FPC'd, compiled and put again in SHIPDIR library after each
C       CDB update.
C
C
C'Include_files_directives
C
C       DISP.COM       - iteration time and frequency declaration
C                      - need not to be FPC'd
C
C       ANCMASK.INC    - defines masks required for BYTE || operation
C                      - need not to be FPC'd
C
C       SHIPINFO.INC:  - declarations for using YISHIP on IBM computers
C                      - need not be FPC'd
C
C
C'Revision_History
C
C  usd8ab2.for.7 22Feb1993 15:55 usd8 pve
C       < fix fortran error >
C
C  usd8ab2.for.6 20Feb1993 23:23 usd8 pve
C       < increase max torque to match stopping time and distance test >
C
C  usd8ab2.for.5  9Jul1992 07:23 usd8 PLAM
C       < Tuned B3CVSR to 0.1067 to match ATG case 1D1 >
C
C  usd8ab2.for.4 26Apr1992 01:51 usd8 PLam
C       < Tuned B3CVSR to 0.095 to match ATG case 1D1 >
C
C   #(033) 30-Mar-92 SERGEB
C         SET MAX TORQUE TO 6000; TARGET SLIP RATIO TO 0.105;
C         KEEP A/S PRESS CTE WHEN FLIGHT FREEZE IS ON
C
C
C'Description
C
C     See AB1 module for the brake system description.
C
C
C       PROGRAM DESCRIPTION :
C       =====================
C
C     	The module is separated into three main sections beginning with
C     an initialization section :
C
C
C     SECTION 0 /  INITIALIZATION & SPECIAL FUNCTIONS
C
C
C     	The initialization section include a first pass portion where ship
C     dependencies are evaluated as well as customer options. All
C     initializations for time constant parameters are also computed here.
C     The second part of the section performs the equivalence for CDB
C     variables.
C
C
C     SECTION 1 /  CONTROL
C
C       This section computed the locked wheel protection of the skid
C     control unit (SCU).
C
C
C     SECTION 2 /  LOGIC & INDICATIONS
C
C     	N/A
C
C
C     SECTION 3 /  PERFORMANCES
C
C       This section computes the brake plate pressure according to
C     metered pressure, SCU modulation and parking brake output pressure.
C     These values are used to computed the brake torque.
C
C
C     Wheels order within a loop :
C
C
C           1      2                        3      4
C
C          00     00           W           00     00
C         0000   0000        DFWDF        0000   0000
C         0000XXX0000       WDFWDFW       0000XXX0000
C         0000   0000         FWD         0000   0000
C          00     00          FWD          00     00
C                             FWD
C
C          LH GEAR                           RH GEAR
C
C
C'References
C
C 	[ 1 ]   DHC-8 Operation Manual / Operating Data Manual, Aug 1990
C
C 	[ 2 ]   DHC-8 Maintenance Manual Chapter 32  ( 100A ), Feb 1989
C
C 	[ 3 ]   DHC-8 Maintenance Manual Chapter 32  ( 300A ), Sep 1990
C
C 	[ 4 ]   DHC-8 Wiring Diagrams Manual Chapter 32, Oct 1988
C
C 	[ 5 ]   AEROC 8.6.U.1, Feb 1988
C
C       [ 6 ]   CAE VIDEO
C
C       [ 7 ]   AEROC 8.6.HY.1, May 1984
C
C
      SUBROUTINE USD8AB2
C
C
      IMPLICIT NONE
C
C
C'Include_files
C
      INCLUDE 'disp.com'!NOFPC
      INCLUDE 'ancmask.inc'!NOFPC
CIBM+
          INCLUDE 'shipinfo.inc' !NOFPC
CIBM-
C
C
C'Ident
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8ab2.for.7 22Feb1993 15:55 usd8 pve    $'/
C
C
C'Common_Data_Base_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *              C O M M O N    D A T A    B A S E                     *
C     *                                                                    *
C     **********************************************************************
C
C
CQ    USD8 XRFTEST(*)
C
C
C     **********************************************************************
C     *			        EQUIVALENCES                               *
C     **********************************************************************
C
C
C     **********************************************************************
C     *			           INPUTS                                  *
C     **********************************************************************
C
C
CPI   USD8
C
C
C     ----------------------------------------------------------------------
C     -                            OTHER SYSTEMS                           -
C     ----------------------------------------------------------------------
C
C     < GEAR >
C     ========
C
CPI   AGFPS82(2)   , AGFCL(2)     ,
C
C     < FLIGHT >
C     ==========
C
CPI   VOMEGA       , VSR          ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
CPI   YITAIL       ,
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
CPI   ABPMN(2)     , ABPMPB       , ABXFADE(4)   ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
CPI   ABFREL(4)    , ABFAS(4)     ,
C
C     ----------------------------------------------------------------------
C     -                            INSTRUCTOR FACILITY                     -
C     ----------------------------------------------------------------------
C
CPI   TCFBRAKE     , TCFFLPOS     ,
C
C
C     **********************************************************************
C     *			           OUTPUTS                                 *
C     **********************************************************************
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
C
CPO   ABPASV1(4)   , ABPBRK(4)    , ABU(4)       , ABQ(4)       ,
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
CPO   ABFSKID(4)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:29:07 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  ABPMN(2)       ! LH normal vlv metered press            [psi]
     &, ABPMPB         ! Park Brk  vlv metered press            [psi]
     &, ABXFADE(4)     ! Brk fade factor LO                       [-]
     &, VOMEGA(3,2)    ! Wheel speed                        [rad/sec]
     &, VSR(6)         ! Tire slip ratio                          [-]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  ABFAS(4)       ! A/S protection ON wheel 1 LO
     &, ABFREL(4)      ! A/S release flag wheel 1 LO
     &, AGFCL(2)       ! Left  wheel gear collapsing flag
     &, AGFPS82(2)     ! PSEU eq82  [C24] A/S - INB  PWR ON
     &, TCFBRAKE       ! FREEZE/BRAKES
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  ABPASV1(4)     ! A/S sys 1 vlv press 1 LO               [psi]
     &, ABPBRK(4)      ! Brake pressure 1 LO                    [psi]
     &, ABQ(4)         ! Actual brk torque wheel 1 LO        [ft*lbs]
     &, ABU(4)         ! Speed wheel 1 LO                       [kts]
C$
      LOGICAL*1
     &  ABFSKID(4)     ! Wheel 1 skidding flag LO
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(20656),DUM0000003(83353)
     &, DUM0000004(99),DUM0000005(32),DUM0000006(8)
     &, DUM0000007(44),DUM0000008(14),DUM0000009(201683)
     &, DUM0000010(29)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VSR,VOMEGA,DUM0000003,AGFCL
     &, DUM0000004,AGFPS82,DUM0000005,ABQ,ABPMN,ABPMPB,DUM0000006
     &, ABPASV1,ABPBRK,DUM0000007,ABXFADE,ABU,DUM0000008,ABFSKID
     &, ABFREL,ABFAS,DUM0000009,TCFFLPOS,DUM0000010,TCFBRAKE  
C------------------------------------------------------------------------------
C
C
C     ----------------------------------------------------------------------
C
C
C
C'Local_Variables
C
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C
C     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
C     =============================================
C
C     Variables Second Number Function
C     --------------------------------
C
C     0 : Miscellaneous
C     1 : Controller
C     2 : Logic & Indications
C     3 : Performance
C
C
C     REAL Variables Names Third Letter Function
C     ------------------------------------------
C
C     A : Admittance, Angle, Acceleration
C     B : CB, X intercept, Admittance ( Y delta Transformation )
C     C : Constants
C     D : Time delay
C     E : Energy, Source pressure
C     F : Force, "Forcing function", Volumetric flow rate
C     G : Gain, Admittance ( Summ of 3 admittances in Y )
C     H : Frequency, Altitude
C     I : Current
C     J : ........
C     K : ........
C     L : Admittance ( Summ of 2 admittances in parallel )
C     M : Admittance ( Summ of 2 admittances in series )
C     N : RPM, Node capacitor admittance
C     O : Negative flow
C     P : Pressure, Phase
C     Q : Quantity, Battery charge
C     R : Resistance
C     S : Scratchpad
C     T : Temperature, Torque
C     U : Rate, Velocity
C     V : Valve position, Voltage
C     W : Positive flow, Electrical load
C     X : Coefficient, Time factor ( Tau )
C     Y : Total heat transfer flow
C     Z : "Malfunctions"
C
C     LOGICAL Variables Names Third Letter Function
C     ---------------------------------------------
C
C     F : Logical flag
C     G : ........
C     M : Option, mod #, Service bulletin #
C     R : Relay status
C     S : Switch status
C     Z : Malfunction
C
C
C     INTEGER Variables Names Third Letter Function
C     ---------------------------------------------
C
C     J : All integer variables
C
C
C     LOGICAL/REAL Variables Names last Letter Function
C     -------------------------------------------------
C
C     I : Integer*4 for bite //
C     J : Logical*4 for bite //
C     L : Left, Latched
C     Q : Previous buffer
C     R : Right
C     T : Time dependant
C
C
C     ----------------------------------------------------------------------
C     -                           PARAMETER                                -
C     ----------------------------------------------------------------------
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
      INTEGER*4
C
     &   I,K,J                  ! Index
     &,  K1(2)    /    2, 1  /  ! Index
     &,  K2(2)    /    3, 4  /  ! Index
     &,  K3(4)    / 2,1,1,2  /  ! Index
     &,  K4(4)    / 1,1,2,2  /  ! Index
C
C
C     ----------------------------------------------------------------------
C     -                             REAL                                   -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &   B3DTD(15)           ! Timers used with B3CTD               [Sec]
     &,  B3UERR(4)           ! Brake release error signal           [psi]
     &,  B3XPEAK(4)          ! Brake torque peaking factor           [--]
     &,  B3XRATIO(4)         ! Brake torque ratio                    [--]
     &,  B3XVSR(4)           ! Slip Ratio                            [--]
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
      LOGICAL*1
C
     &   B0FIRST  /.TRUE. /  ! First pass initialization flag
     &,  B3FASVC(4)          ! A/S valve closed flag
C
C     ---
C     SCU
C     ---
C
      LOGICAL*1
C
     &   B1FSLWP(4)          ! SCU locked wheel protection ON (LO,LI,RI,RO)
C
C     -------
C     OPTIONS
C     -------
C
      LOGICAL*1
C
     &   B0MAWD8   /.FALSE./ ! America West Flag
     &,  B0MSR300            ! SERIES 300 FLAG
     &,  B0MUSD8   /.FALSE./ ! US AIR  Flag
C
C
C     ------------
C     MALFUNCTIONS
C     ------------
C
C
C     ----------------------------------------------------------------------
C     -                            CONSTANTS                               -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &   B0CDIV0   /    1.0E-6 /  ! Factor to prevent div by 0       [--]
     &,  B1CUADS   /   10.0    /  ! Antiskid dropout speed        [knots]
     &,  B1CULDS   /   23.0    /  ! Locked wheel dropout speed    [knots]
     &,  B1CULSR   /    0.5    /  ! Locked wheel speed ratio      [knots]
     &,  B3CHMAX   / 3000.0    /  ! Hydraulic max pressure          [psi]
     &,  B3CPMAX                  ! Max. hyd press flow for A/S [psi/sec]
     &,  B3CPRELF  /  450.0    /  ! Gain for A/S releasing brake press[-]
     &,  B3CR2KT                  ! Wheel speed conv rad->kts    [kt/rad]
     &,  B3CTD(15)                ! Delay times used with B3DTD     [Sec]
     &             /    0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0    /  ! NOT USED
C !FM+
C !FM  20-Feb-93 23:22:17 PVE
C !FM    < Increase max torque to match braking time for given brake
C !FM      pressure >
C !FM
     &,  B3CTMAX                  ! Torque maximum ( at 3000 psi )[Lb*ft]
     &,  B3CTMAX1  / 6000.0    /  ! Torque maximum ( at 3000 psi )[Lb*ft]
     &,  B3CTMAX3  / 9000.0    /  ! Torque maximum ( at 3000 psi )[Lb*ft]
C !FM-
     &,  B3CTPFB   /  100.0    /  ! Torque peak factor brkpoint   [Knots]
     &,  B3CTPFI   /    1.2    /  ! Torque peak factor intercept  [Coeff]
     &,  B3CTPFM   /    0.9    /  ! Torque peak factor minimum    [Coeff]
     &,  B3CTPFS   /   -0.003  /  ! Torque peak factor slope[Coeff/knots]
     &,  B3CTRES   /  100.0    /  ! Constant for brake temp reset  [DegC]
     &,  B3CTRMI   /  250.0    /  ! Minimum metered for torque      [Psi]
     &,  B3CTRMX   / 3000.0    /  ! Maximum metered for torque      [Psi]
     &,  B3CTRMVI                 ! Torque ratio intercept            [-]
     &,  B3CTRMVS                 ! Torque ratio slope             [/Deg]
     &,  B3CVSR    /    0.1067 /  ! Target slip ratio               [ - ]
C
C
C     ----------------------------------------------------------------------
C     -                        LOCAL EQUIVALENCES                          -
C     ----------------------------------------------------------------------
C
C
C     ----------------------------------------------------------------------
C     -                        FUTURE CDB LABELS                           -
C     ----------------------------------------------------------------------
C
C
C
      ENTRY ABRAK2
C
C
      IF ( TCFBRAKE )  RETURN
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 0 :  INITIALIZATION & SPECIAL FUNCTIONS           #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.1 :  FIRST PASS                                 |
CD    ----------------------------------------------------------------------
C
CT    This section is executed once on the first pass of the module.
CT    Initialization of variables, timers, constants and A/C option flags
CT    depending on the tail number are set.
C
C
      IF ( B0FIRST )  THEN
C
C       =================================================================
C       |                                                               |
C       |                SHIPS                  TAIL #                  |
C       |                ------                 ------                  |
C       |                USAIR 100A              226                    |
C       |                USAIR 300A              230                    |
C       |                AWEST 100              ?????                   |
C       |                                                               |
C       =================================================================
C
C
CD    B01000  SHIP SELECTION AND OPTIONS                          (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        IF ( YITAIL .EQ. 000 )  THEN
          B0MAWD8   = .TRUE.
          B0MSR300  = .FALSE.
C !FM+
C !FM  20-Feb-93 23:22:17 PVE
C !FM    < Increase max torque to match braking time for given brake
C !FM      pressure >
C !FM
          B3CTMAX   = B3CTMAX1
        ELSEIF ( YITAIL .EQ. 226 )  THEN
          B0MUSD8   = .TRUE.
          B0MSR300  = .FALSE.
          B3CTMAX   = B3CTMAX1
        ELSEIF ( YITAIL .EQ. 230 )  THEN
          B0MUSD8   = .TRUE.
          B0MSR300  = .TRUE.
          B3CTMAX   = B3CTMAX3
C !FM-
        ENDIF                          ! OF SHIP OPTION
C
C
CD    B01010  VARIABLES INITIALIZATION                            (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        DO I = 1, 15
          B3DTD(I)    = B3CTD(I)
        ENDDO
C
        B3CTRMVS =                1
C                  -------------------------------
     &/            ( B3CTRMX - B3CTRMI + B0CDIV0 )
C
        B3CTRMVI =  -B3CTRMI * B3CTRMVS
        B3CPMAX  = 3000 * YITIM
C
        IF ( B0MSR300 ) THEN
          B3CR2KT = 0.6915             ! 14 / [ 12 * 1.467 * 1.15 ]
        ELSE
          B3CR2KT = 0.6421             ! 13 / [ 12 * 1.467 * 1.15 ]
        ENDIF                          ! OF SERIES 300 OPTION
C
        B0FIRST = .FALSE.
C
      ENDIF                            ! OF FIRST PASS
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.2 :  GENERAL                                    |
CD    ----------------------------------------------------------------------
C
C
CD    B02000  CDB EQUIVALENCE                                     (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation makes the equivalence between future CDB variables and
CT    temporary local variables. It also makes correspondance between CDB
CT    label and local array for indexing purpose.
C
C
      B3XVSR(1) = VSR(3)
      B3XVSR(2) = VSR(4)
      B3XVSR(3) = VSR(5)
      B3XVSR(4) = VSR(6)
C
C
CD    B02010  WHEEL SPEED CORRECTION                              (ABU     )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation calculates the wheel speed relative to the ground.
CT    It takes the wheel speed calculated by flight, which is in rad/sec,
CT    and makes the convertion to an absolute value in knots.
C
C
      ABU(1) = VOMEGA(2,1) * B3CR2KT
      ABU(2) = VOMEGA(2,2) * B3CR2KT
      ABU(3) = VOMEGA(3,1) * B3CR2KT
      ABU(4) = VOMEGA(3,2) * B3CR2KT
C
      DO I = 1, 4
        IF ( ABU(I) .LT. 0.0 ) ABU(I) = -ABU(I)
      ENDDO
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 1 :  CONTROL                                      #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 1.1 :  SKID CONTROL UNIT's logic                  |
CD    ----------------------------------------------------------------------
C
C
CD    B11000  SCU LOCKED WHEEL CROSSOVER PROTECTION               (B1FSLWP )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec 32-46-00 p 5
CR                         [ 5 ] sec 2.6 p 11
C
CT    If one of the paired wheel speeds is seen to differ by approximately
CT    50 % or more, the slow wheel brake is released to preclude tire
CT    scuffing until less than 50 % wheel speed difference is achieved.
CT    Inboard wheel speeds are compared and outboard speeds are compared.
CT    In order to preclude brake pressure release during low speed, high
CT    turn rate manoeuvering, this locked wheel protection circuit includes
CT    a locked wheel dropout speed.
C
C
      DO I = 1, 4
C
        IF ( ABFAS(I) .AND. ABU(I) .LT. ( ABU(5-I) * B1CULSR ) .AND.
     &       ABU(5-I) .GT. B1CULDS ) THEN
          B1FSLWP(I) = .TRUE.
        ELSE
          B1FSLWP(I) = .FALSE.
        ENDIF                      ! OF LOCKED WHEEL PROTECTION
C
      ENDDO
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 2 :  LOGIC & INDICATIONS                          #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.1 :  LOGIC                                      |
CD    ----------------------------------------------------------------------
C
C
CD                           Not applicable.
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.2 :  INDICATIONS                                |
CD    ----------------------------------------------------------------------
C
C
CD                           Not applicable.
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 3 :  PERFORMANCES                                 #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.1 : BRAKE TORQUE COMPUTATION                    |
CD    ----------------------------------------------------------------------
C
C
CD    B31000  A/S VALVE FULLY CLOSED FLAG                         (B3FASVC )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec 2.5, 2.6
C
CT    If there is a full release signal from the SCU, anti-skid valve will
CT    close completely and no pressure is applied to the brake. Since locked
CT    wheel protection is computed in this module, valve will close if
CT    a locked wheel condition is detected. Valve will also close completely
CT    if there is a malfunction on the valve.
C
C
      DO I = 1, 4
C
        B3FASVC(I) = ABFREL(I) .OR. B1FSLWP(I)
C
C
CD    B31010  A/S LOGIC - RELEASE ERROR                           (B3UERR  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec 2.5, 2.6
C
CT    This equation computes the error between the brake plate target
CT    pressure and the actual pressure. Target pressure is modulated when
CT    antiskid protection is ON, inhibiting a locked wheel condition. If
CT    there is no protection, target pressure is equal to metered pressure.
CT    If anti-skid valve is closed completely, no pressure is applied
CT    to the brake. The rate of change of hydraulic pressure through the
CT    anti-skid valves is limited.
C
C
        IF ( B3FASVC(I) ) THEN
          B3UERR(I) = - ABPASV1(I)
        ELSEIF ( ABFAS(I) ) THEN
C !FM+
C !FM  23-Mar-92 02:01:58 pve
C !FM    < if flight is frozen slip ratio is not updated so keep pressure >
C !FM    < constant until flight freeze is released >
C
          IF ( TCFFLPOS )THEN
            B3UERR(I) = 0.0
          ELSE
            B3UERR(I) = B3CPRELF * ( B3CVSR - B3XVSR(I) )
          ENDIF                        ! OF FLIGHT FREEZE
C !FM-
        ELSE
          B3UERR(I) = ABPMN(K4(I)) - ABPASV1(I)
        ENDIF                          ! OF RELEASE PRESSURE
C
        IF ( B3UERR(I) .GT. B3CPMAX ) THEN
          B3UERR(I) = B3CPMAX
        ELSEIF ( B3UERR(I) .LT. -B3CPMAX ) THEN
          B3UERR(I) = -B3CPMAX
        ENDIF                          ! OF RELEASE LIMITATION
C
C
CD    B31020  A/S SYSTEM PRESSURE                                 (ABPASV1 )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec 2.5, 2.6
C
CT    This equation computes the pressure downsteam anti-skid valves.
CT    Ensure brake pressures do not exceed demanded pressure.
C
C
        ABPASV1(I) = ABPASV1(I) + B3UERR(I)
C
        IF ( ABPASV1(I) .LT. 0.0 ) THEN
          ABPASV1(I) = 0.0
        ELSEIF ( ABPASV1(I) .GT. ABPMN(K4(I)) ) THEN
          ABPASV1(I) = ABPMN(K4(I))
        ENDIF                          ! OF A/S PRESSURE
C
      ENDDO
C
C
CD    B31030  ACTUAL BRAKE PRESSURE                               (ABPBRK  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec 2.5
C
CT    Downstream of each of the quantity valves is a shuttle valve enabling
CT    the standby/parking brake to operate the brake actuators when its
CT    pressure exceeds that provided from the skid control. No skid
CT    protection is provided for stand-by/parking brake operation that's
CT    why the parking brake pressure has priority on skid pressure.
CT    Brake plate actual pressure is egal to the parking brake pressure
CT    if this one is higher than skid pressure due to shuttle valve
CT    operation. Pressure to the brake is inhibited when a gear collapse
CT    occurs.
C
C
      DO I = 1, 4
C
        IF ( AGFCL(K4(I)) ) THEN
          ABPBRK(I) = 0.0
        ELSEIF ( ABPASV1(I) .LT. ABPMPB ) THEN
          ABPBRK(I) = ABPMPB
        ELSE
          ABPBRK(I) = ABPASV1(I)
        ENDIF                          ! OF BRAKE PLATE PRESSURE
C
C
CD    B31040  BRAKE TORQUE RATIO                                  (B3XRATIO)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The torque ratio is function of the brake valve output pressure.
CT    It will be used to calculate the actual torque value, according to
CT    the maximum torque ( at 3000 psi ). There is no data for torque
CT    ratio neither maximun torque so they have been estimated.
C
C
        IF ( ABPBRK(I) .LE. B3CTRMI ) THEN
          B3XRATIO(I) = 0.0
        ELSE
          B3XRATIO(I) = ABPBRK(I) * B3CTRMVS + B3CTRMVI
          IF ( B3XRATIO(I) .GT. 1.0 ) B3XRATIO(I) = 1.0
          IF ( B3XRATIO(I) .LT. 0.0 ) B3XRATIO(I) = 0.0
        ENDIF                          ! OF TORQUE RATIO FACTOR
C
C
CD    B31050  TORQUE PEAKING FACTOR                               (B3XPEAK )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The torque peaking factor is dependent of the wheel speed. There
CT    is no data for peaking factor so it has been estimated.
C
C
        IF ( ABU(I) .LT. B3CTPFB ) THEN
          B3XPEAK(I) = B3CTPFS * ABU(I) + B3CTPFI
        ELSE
          B3XPEAK(I) = B3CTPFM
        ENDIF                          ! OF TORQUE PEAKING FACTOR
C
      ENDDO
C
C
CD    B31060  BRAKE TORQUE                                        (ABQ     )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The demanded brake torque is computed according to the following
CT    factors :
CT
CT       Brake torque ratio, Torque at 3000 psi, Torque peaking factor,
CT       and Fade factor.
C
C
      DO I = 1, 4
        ABQ(I) = B3CTMAX * ABXFADE(I) * B3XPEAK(I) * B3XRATIO(I)
      ENDDO
C
C
      RETURN
C
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00512 ###                                                                ###
C$ 00513 ######################################################################
C$ 00514 #                                                                    #
C$ 00515 #          SECTION 0 :  INITIALIZATION & SPECIAL FUNCTIONS           #
C$ 00516 #                                                                    #
C$ 00517 ######################################################################
C$ 00518 ###                                                                ###
C$ 00521 ----------------------------------------------------------------------
C$ 00522 |          Section 0.1 :  FIRST PASS                                 |
C$ 00523 ----------------------------------------------------------------------
C$ 00543 B01000  SHIP SELECTION AND OPTIONS                          (--------)
C$ 00569 B01010  VARIABLES INITIALIZATION                            (--------)
C$ 00596 ----------------------------------------------------------------------
C$ 00597 |          Section 0.2 :  GENERAL                                    |
C$ 00598 ----------------------------------------------------------------------
C$ 00601 B02000  CDB EQUIVALENCE                                     (--------)
C$ 00616 B02010  WHEEL SPEED CORRECTION                              (ABU     )
C$ 00635 ###                                                                ###
C$ 00636 ######################################################################
C$ 00637 #                                                                    #
C$ 00638 #          SECTION 1 :  CONTROL                                      #
C$ 00639 #                                                                    #
C$ 00640 ######################################################################
C$ 00641 ###                                                                ###
C$ 00644 ----------------------------------------------------------------------
C$ 00645 |          Section 1.1 :  SKID CONTROL UNIT's logic                  |
C$ 00646 ----------------------------------------------------------------------
C$ 00649 B11000  SCU LOCKED WHEEL CROSSOVER PROTECTION               (B1FSLWP )
C$ 00675 ###                                                                ###
C$ 00676 ######################################################################
C$ 00677 #                                                                    #
C$ 00678 #          SECTION 2 :  LOGIC & INDICATIONS                          #
C$ 00679 #                                                                    #
C$ 00680 ######################################################################
C$ 00681 ###                                                                ###
C$ 00684 ----------------------------------------------------------------------
C$ 00685 |          Section 2.1 :  LOGIC                                      |
C$ 00686 ----------------------------------------------------------------------
C$ 00689 Not applicable.
C$ 00692 ----------------------------------------------------------------------
C$ 00693 |          Section 2.2 :  INDICATIONS                                |
C$ 00694 ----------------------------------------------------------------------
C$ 00697 Not applicable.
C$ 00700 ###                                                                ###
C$ 00701 ######################################################################
C$ 00702 #                                                                    #
C$ 00703 #          SECTION 3 :  PERFORMANCES                                 #
C$ 00704 #                                                                    #
C$ 00705 ######################################################################
C$ 00706 ###                                                                ###
C$ 00709 ----------------------------------------------------------------------
C$ 00710 |          Section 3.1 : BRAKE TORQUE COMPUTATION                    |
C$ 00711 ----------------------------------------------------------------------
C$ 00714 B31000  A/S VALVE FULLY CLOSED FLAG                         (B3FASVC )
C$ 00730 B31010  A/S LOGIC - RELEASE ERROR                           (B3UERR  )
C$ 00768 B31020  A/S SYSTEM PRESSURE                                 (ABPASV1 )
C$ 00787 B31030  ACTUAL BRAKE PRESSURE                               (ABPBRK  )
C$ 00813 B31040  BRAKE TORQUE RATIO                                  (B3XRATIO)
C$ 00832 B31050  TORQUE PEAKING FACTOR                               (B3XPEAK )
C$ 00849 B31060  BRAKE TORQUE                                        (ABQ     )
