#!  /bin/csh -f
#!  $Revision: FSE configuration support script V1.5 Feb-92$
#!
#! ^
#!  Version 1.0: <PERSON> (18-Jul-91)
#!     - initial version
#!
#!  Version 1.1: <PERSON> (20-Aug-91)
#!     - added double quotes to most of the character strings.
#!     - check status after calling logicals
#!
#!  Version 1.2: <PERSON> (25-Sep-91)
#!     - translate the logical name CAE_CAELIB_PATH in the file that is
#!       transfered to the remote machine
#!  
#!  Version 1.3: <PERSON> (03-Dec-91)
#!     - update call to fse_operate for MOM 4.0
#!
#!  Version 1.4: <PERSON> (04-Dec-91)
#!     - check if remote nodes were found before calling fse_operate
#!     - remove file contained in FSE_TEMP before ending
#!
#!  Version 1.5: <PERSON> (24-Feb-92)
#!     - initialized FSE_REMO to "" instead of " "
#!
set FSE_TEMP = ""
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ! ( ("$argv[2]" == "COPY")   || \
       ("$argv[2]" == "RENAME") || \
       ("$argv[2]" == "DELETE")    \
     ) exit
#
if ( "`logicals -t CAE_MOM_MAX`" == "0" ) exit
#
#    identify actual computer
#
@ FSE_ACTUAL = "`logicals -t CAE_MOM_CPU`"
if ( $status != 0 ) then
   echo "*** ERROR : Logical name CAE_MOM_CPU cannot be translated."
   exit
endif
#
#    find the number of bits that are set in CAE_SIMTAB logical name
#
set SIMTAB = "`logicals -t CAE_SIMTAB`"
if ( $status != 0 ) then
   echo "*** ERROR : Logical name CAE_SIMTAB cannot be translated."
   exit
endif
@ FSE_LEN = 0
@ FSE_COUNT = 8
while ( $FSE_COUNT > 0 )
    set FSE_BIT = `echo "$SIMTAB" | cut -c"$FSE_COUNT"`
    if ( "$FSE_BIT" != "" ) then
       @ FSE_LEN++
    endif
    @ FSE_COUNT--
end
#
#    check if CAE_SIMTAB sees a different computer than the actual one
#
@ FSE_COUNT = $FSE_LEN
set FSE_REMO  = ""
set SIMEX_DIR = "`logicals -t CAE_SIMEX_PLUS`"
if ( $status != 0 ) then
   echo "*** ERROR : Logical name CAE_SIMEX_PLUS cannot be translated."
   exit
endif
set FSE_TEMP = "$SIMEX_DIR/work/coml_`pid`.tmp.1"
while ( $FSE_COUNT > 0 )
    set FSE_BIT = `echo "$SIMTAB" | cut -c"$FSE_COUNT"`
    if ( "$FSE_BIT" == "1" ) then
       @ FSE_NUMB = $FSE_LEN - $FSE_COUNT
       if ( "$FSE_NUMB" != "$FSE_ACTUAL" ) then
          set FSE_REMO = ($FSE_REMO `logicals -t CAE_MOM$FSE_NUMB`)
          if ( $status != 0 ) then
              echo "*** ERROR : Logical name CAE_MOM$FSE_NUMB is not defined"
              exit
          endif
          echo "   SYSC$FSE_NUMB" >> $FSE_TEMP
       endif
    endif
    @ FSE_COUNT--
end
#
#    initiate files transfering if remote node(s) were found
#
if ( "$FSE_REMO" != "" ) then
#
#  Build the remote computer SIMex-PLUS input file
#
   set FSE_TMP1 = "$SIMEX_DIR/work/coml_`pid`.tmp.1"
   echo "set session REMOTE"              > $FSE_TMP1
   echo "set configuration $argv[4]"     >> $FSE_TMP1
   if ( "$argv[2]" == "DELETE" ) then
      echo "DELETE /NOCONFIRM"           >> $FSE_TMP1
   else
      echo "conf /$argv[2] $argv[5]"     >> $FSE_TMP1
   endif
   echo "exit"                           >> $FSE_TMP1
#
#  Build the remote computer SIMex-PLUS invocation file
#
   set FSE_EXEC = "coml_`pid`"
   set FSE_TMP2 = "$SIMEX_DIR/work/$FSE_EXEC.tmp.1"
   echo '#!  /bin/csh -f'                                  > $FSE_TMP2
   echo 'set BIN = "`/cae/logicals -t cae_caelib_path`"'  >> $FSE_TMP2
   echo  setenv PATH '`echo $BIN`:`echo $PATH`'           >> $FSE_TMP2
   echo  simex execute \\\"/tmp/$FSE_TMP1:t\\\"           >> $FSE_TMP2
   echo  rm /tmp/$FSE_TMP1:t                              >> $FSE_TMP2 
   echo  rm /tmp/$FSE_TMP2:t                              >> $FSE_TMP2
   chmod +x $FSE_TMP2
#
#  Initiate files transfer
#
   set I = 1
   while ( ($I <= $#FSE_REMO) && ("$FSE_REMO" != "") )
       @ DEC_I = $I
       rcp -p "$FSE_TMP1" "$FSE_REMO[$DEC_I]":/tmp/"$FSE_TMP1:t"
       rcp -p "$FSE_TMP2" "$FSE_REMO[$DEC_I]":/tmp/"$FSE_TMP2:t"
       @ I++
   end
#
#  Tell remote computer to execute the COPY/RENAME/DELETE command
#
   set FSE_TMP3 = "$SIMEX_DIR/work/coml_`pid`.tmp.1"
   echo "EL /tmp/$FSE_TMP2:t" > $FSE_TMP3
   cat  $FSE_TEMP            >> $FSE_TMP3
#
#  Invoke FSE_OPERATE
#
   set FSE_PREVIOUS = "`logicals -t CAE_LD_CDB`"
   logicals -c "CAE_LD_CDB" "$SIMTAB"
   fse_operate LOAD START_REPORT $FSE_TMP3
   if ( $status != 0 ) exit $status
   logicals -c "CAE_LD_CDB" "$FSE_PREVIOUS"
   logicals -d "cae_ld_$FSE_EXEC"
#
   rm $FSE_TMP1
   rm $FSE_TMP2
   rm $FSE_TMP3
#
endif
if ("$FSE_TEMP" != "") rm $FSE_TEMP
#
exit
