#!  /bin/csh -f
#   $Revision: RTMS_SERVER invocation file V1.1 (MT) May-91$
#
#   Version 1.1: <PERSON> (24-May-91)
#      - removed reference to /cae/simex_plus/support
#
set BIN=`logicals -t cae_caelib_path`
set SIMEX_DIR="`$BIN/logicals -t CAE_SIMEX_PLUS`"
set FSE_UNIK="`pid`.tmp.1"
set FSE_TEMP=$SIMEX_DIR/work/rtmst_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/rtmsl_$FSE_UNIK
#
echo '@$.'          >$FSE_TEMP
echo '&$*.XSL'     >>$FSE_TEMP
echo '@CDB_SPARE.' >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
set LOG=`logicals -t cae_log`
$BIN/rtms_server >> $LOG/rtms_server_error.log 
unsetenv SOURCE
rm $FSE_LIST
exit
