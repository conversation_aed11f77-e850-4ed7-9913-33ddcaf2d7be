#! /bin/csh -f
echo "*** (REN V1.0) ***"
echo " "
if ($#argv > 2 || $#argv == 0) then
   echo 'Usage: ren source target'
else
   if ($#argv == 1) then
      set TSRC = `norev $argv[1]`
   else
      set TSRC = $argv[2]
   endif
   if ($argv[1]:h == $argv[1]:t) then
      set SSRC = `revl $argv[1] `
      set IEV = $status
   else
      set SSRC = `revl -$argv[1] `
      set IEV = $status
   endif
   if ($IEV == 0 || $IEV == 6 || $IEV == 1) then
      if ($TSRC:h == $TSRC:t) then
         set TSRC = `revl $TSRC +`
         set IEV = $status
      else
         set TSRC = `revl -$TSRC +`
         set IEV = $status
      endif
      if ($IEV == 0) then
         mv $SSRC $TSRC
         if ($status == 0) then
            echo '"'$SSRC'" renamed to "'$TSRC'"'
            echo ' '
         endif
      else if ($IEV == 1 || $IEV == 6 || $IEV == 9 ) then
         echo -n 'File ' $TSRC ' already exists. Overwrite (y/n)? '
         set ANS = ($<)
         if ($ANS == y || $ANS == Y) then
            mv $SSRC $TSRC
            if ($status == 0) then
               echo '"'$SSRC'" renamed to "'$TSRC'"'
               echo ' '
            endif
         endif
      else
         echo 'Invalid target file'
      endif
   else
      echo 'Invalid source file'
   endif
endif
