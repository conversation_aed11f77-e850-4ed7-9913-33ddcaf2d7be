#! /bin/csh -f
#
# $Revision: CAE C compiler include processor  Version 2.0 (MT) Jul-1991$
#
# Version 1.0: [<PERSON><PERSON>] (29/08/90)
#       Create a new file with the include file name converted to
#       have version numbers.
# Version 1.1: [<PERSON>] (19/12/90)
#       Added code to support commentaries in include files.
#
# Version 1.2: <PERSON> (26/03/91)
#       FSE_MAKE is set with the '.lst' file
#
# Version 1.3: <PERSON> (01/05/91)
#       FSE_MAKE file generation is moved from c4l to cupinc.  Brought IBM
#       and SGI version into agreement.
#
# Version 2.0: <PERSON> (19/07/91)
#       Modified to support nested include files.  The way it works is that
#       all the includes files are added at the right place in the program.
#
onintr FSE_STOP
#
# Get the arguments and do the initialization
set FSE_DATA=$argv[1]   # Original file name
set FSE_WORK=$argv[2]   # Modified file name
set FSE_DONE=$argv[3]   # Selected file name
set FSE_LIST=$argv[4]   # Messages file name
set FSE_FAIL=0          # Number of errors
set FSE_LOOP=1          # Current line number
#
# generate a list of include files in FSE_MAKE
#
########### IBM code, beginning of commented part on SGI ###############
#
set FSE_DUMM="`unik dummy`.c"
set FSE_MAKE="$FSE_DUMM:r.lst"
cp $FSE_DATA $FSE_DUMM
cc -qstat -c $FSE_DUMM >>&/dev/null
if ( -e "$FSE_DUMM:r.o" ) rm "$FSE_DUMM:r.o"
if ( -e $FSE_DUMM ) rm $FSE_DUMM
#
################  end of commented part on SGI  ########################
#
########### SGI code, beginning of commented part on IBM ###############
#
#set FSE_MAKE="`unik $FSE_DATA`"
#/usr/lib/cpp -M $FSE_DATA >&$FSE_MAKE
#
################  end of commented part on IBM  ########################
#
# Strip out the comments
set FSE_GREP='^#[ 	]*include[ 	]+"'
set FSE_STRIP="`unik strip`" 
egrep -n "$FSE_GREP" "$FSE_DATA" | cut -f1-2 -d'"' > $FSE_STRIP
#
# Loop on each user include statement
foreach FSE_LINE ("`cat "$FSE_STRIP"`")
#
# Isolate the information on the line
  set FSE_NUMS=`echo $FSE_LINE | cut -f1 -d:`
  set FSE_REST=`echo $FSE_LINE | sed '1 s/"/ /g' | sed "1 s/$FSE_NUMS":"/ /p"`
  set FSE_LINE=($FSE_NUMS $FSE_REST)
  if ("$FSE_LINE[2]" == "#") then
    set FSE_LINE[3]="$FSE_LINE[4]"
  endif
#
# Check if it is used by the preprocessor
  if (-e $FSE_MAKE) then
      set FSE_FIND="`grep $FSE_LINE[3] $FSE_MAKE`"
      if ($status != 0) continue
  endif
#
# Find the full file name of the user include file
  set FSE_FILE="`revl -$FSE_LINE[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "$FSE_LINE[3] => `reverr $stat`" >>$FSE_LIST
    @ FSE_FAIL = $FSE_FAIL + 1
  endif
  if ($FSE_FAIL != 0) continue
#
# Add to the list if not there
  set FSE_FIND="`grep $FSE_FILE $FSE_DONE`"
  if ($status != 0) echo "$FSE_FILE" >>$FSE_DONE
#
# Copy the file until the line
  @ FSE_COPY = $FSE_NUMS - 1
  if ($FSE_COPY >= $FSE_LOOP) then
    sed -n "$FSE_LOOP,$FSE_COPY p" $FSE_DATA >>$FSE_WORK
  endif
#
# Call cupinc recursively to support nested include files
  cupinc $FSE_FILE $FSE_WORK $FSE_DONE $FSE_LIST
#
  @ FSE_LOOP = $FSE_NUMS + 1
#
end
#
# Copy the rest of the file
if ($FSE_FAIL == 0) then
  if ($FSE_LOOP == 1) then
    cat $FSE_DATA >> $FSE_WORK
  else
    sed -n "$FSE_LOOP,$ p" $FSE_DATA >>$FSE_WORK
  endif
endif
#
if (-e $FSE_MAKE) rm $FSE_MAKE
rm $FSE_STRIP
exit $FSE_FAIL
#
FSE_STOP:
if (-e $FSE_MAKE) rm $FSE_MAKE
if (-e $FSE_STRIP) rm $FSE_STRIP
if (-e $FSE_DUMM) rm $FSE_DUMM
exit 1
