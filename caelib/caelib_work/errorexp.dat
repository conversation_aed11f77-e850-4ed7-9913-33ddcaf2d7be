INT:4000
Explanation: The card specified failed a tolerance test. 
 
Action:      Card must be replaced. 
INT:9002
Explanation: The specified DMC is not responding.

Action:      Check to see if chassis is powered down. Check the 
             transceiver to the DMC for proper connection.
INT:9001
Explanation: The specified DMC was not responding at an earlier time 
             but has now began responding.


Action:      If this situation persists, check the transceiver for
             proper connection to the DMC. The load on the Ethernet may
             also be causing the problem.
INT:9000
Explanation: The specified DMC did not respond at the proper time but
             responded soon after.

Action:      If this situation persists, check the transceiver for
             proper connection to the DMC. The load on the Ethernet may
             also be causing the problem.
INT:5FF0
Explanation: The system service used to perform I/O on the host returned
             an error condition.

Action:      Use the off-line diagnostic ETHERTST to help locate the 
             problem. The Ethernet controller may be malfunctioning.
INT:5FF3
Explanation: The host received a bad control word from a DMC.

Action:      A faulty DMC is on the network. Check for errors detected
             on each DMC card (slot 1A hex). If no DMC is singled out,
             use the off-line diagnostic ETHERTST. The faulty DMC card
             must be replaced.
INT:5FF6
Explanation: No frames were received in response to the host's input 
             request.

Action:      Use the off-line diagnostic ETHERTST to help locate the 
             problem. The transceivers on the host side may be the 
             problem.
INT:5FF1
Explanation: The Interface is not responding at all to the Host's request 
             for input data. 

Action:      Check if the power to the Interface has been turned off.
             If the power is on, check the transceiver on the Host side
             if it is connected properly and has power (see lights). 
             Finally, check the cable to the Interface and transceivers
             on the Interface side, their may be a disconnection.
INT:5FFB
Explanation: The host expected standard input from the DMCs but 
             one or more standard frames did not arrive.

Action:      Use the off-line diagnostic ETHERTST to help locate the 
             problem. Check the hardware (transceivers, cables, etc.)
INT:5FFC
Explanation: The host expected ARINC input from the DMCs but 
             one or more ARINC frames did not arrive.

Action:      Use the off-line diagnostic ETHERTST to help locate the 
             problem. Check the hardware (transceivers, cables, etc.)
             connecting the ARINC chassis to the host computer.
INT:5FFE
Explanation: No DMC has responded to the host.

Action:      Use the off-line diagnostic ETHERTST to help locate the 
             problem. Check the hardware (transceivers, cables, etc.).
             The interface may be powered down.
INT:6000
Explanation: ARINC card is malfunctioning.

Action:      Card must be replaced.
INT:6001
Explanation: A DMA operation on an ARINC channel did not complete
             properly.

Action:      Card must be replaced.
INT:6002
Explanation: The ARINC card tried to output data on a channel and an
             error condition was detected after the DMA operation 
             completed.

Action:      Card must be replaced.
INT:6003
Explanation: An equipment failed and its corresponding input ARINC 
             label is no longer received by the ARINC card.

Action:      Check the equipment associated to the label.
INT:6004
Explanation: A parity error was detected when reading an ARINC bus.

Action:      Card must be replaced.
INT:6005
Explanation: An ARINC label was received by the chassis, but no 
             information for the label was provided by the download
             data. This message is a warning and will only appear
             once for each unrecognized label.

Action:      To avoid reception of this warning, use the SINK subsection
             in the bus information file to sink the input labels on the
             appropriate buses.
INT:6006
Explanation: An unexpected interrupt occurred on the ARINC card.

Action:      Card must be replaced.
INT:6007
Explanation: An arithmetic exception due to an incorrect division 
             operation occurred.
            
Action:      Card must be replaced.
INT:6008
Explanation: An underflow error condition was detected by the ARINC 
             card while reading the labels from an input bus.

Action:      Card must be replaced.
INT:6009
Explanation: Too many output labels were declared in the CDB for a
             particular bus.

Action:      The ARINC section of the CDB must be configured to reduce 
             the load on the bus in question (use CCU HISTORY command to
             determine bus). SCSGEN should have issued warning messages
             when the CDB is set up to overflow output buses.
INT:600A
Explanation: Arinc card is not running.

Action:      Check if card is correctly inserted in its slot. If it is,
             card must be replaced.
INT:600C
Explanation: One or more Ethernet packets containing ARINC output
             data were not received by the DMC from the host.

Action:      Use the off-line diagnostic ETHERTST to help locate the 
             problem. Check the hardware (transceivers, cables, etc.)
             on the DMC side.
INT:600D
Explanation: The ARINC frame received from the host is corrupted.

Action:      Use the off-line diagnostic ETHERTST to help locate the 
             problem. Check the hardware (transceivers, cables, etc.)
             on the DMC side.
INT:600E
Explanation: ARINC card is malfunctioning.

Action:      Card must be replaced.
INT:7002
Explanation: The chassis did not receive a frame from the host. The
             loss of a frame can be caused by a heavy load on the 
             network (collisions) or an abnormal completion of the
             host transmission on the network.

Action:      This message is not severe since the host  transmits the
             same region of the CDB on the network at the next iteration.
             However, if the message begins to arrive at a regular 
             frequency, it may be an indication that the host's Ethernet
             controller is starting to malfunction. Use the off-line 
             diagnostic ETHERTST to locate the problem.
INT:7102
Explanation: The network is either overloaded or malfunctioning. Output
             frames sent by the host cannot be output to the chassis 
             because the chassis has not completed the processing 
             of the output frames of the previous request.

Action:      Try reducing the load on the network.
INT:7202
Explanation: The network is malfunctioning. The concentrator DMC is
             receiving an input request packet from the host while 
             still waiting for local chassis to respond.

Action:      Use the off-line diagnostic ETHERTST to locate the problem.
             Also check the host iteration frequency. It may be too high
             for the interface system.

INT:8001 
Explanation: The card was not addressable at an earlier time but it
             has however responded successfully at a later time.

Action:      Make sure the card is seated properly in the slot.
INT:800B
Explanation: The card did not respond.

Action:      If the card is in the slot, check if it is inserted
             properly. If it is, the card must be replaced. 
INT:800C
Explanation: The DMC card tried to do an operation on an address
             but the data cycle was either lost or incomplete
             at the end of the address cycle.

Action:      The card must be replaced.
INT:800D
Explanation: This is a hardware problem with the chassis itself. The
             DMC card tried to address the card specified by the 
             C_Bus address, but more than one card responded.

Action:      Check the wiring on the backplane for correctness.
INT:4800
Explanation: The card failed a special type of host diagnostic test
             for intelligent cards. 

Action:      Use CCU HISTORY to view the exact failure.
INT:4801
Explanation: The power voltage to the DMC chassis is either too high
             or too low (+/- 10% deviation).

Action:      Check too see if the chassis actually requires the power 
             supply in question. If it does not, then one must ground
             the INH pin to the DMC card to prevent the error message.
             Otherwise, check the level of the power supply to the chassis.
Action:      None.
VIS:0002
Explanation: The visual system is not responding.

Action:      Check to see if the visual system is powered down.
VIS:0003
Explanation: The visual YY do not work properly.

Action:      Check if the visual YY is well configurated.