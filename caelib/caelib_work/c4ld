#! /bin/csh -f
#
# $Revision: CAE C compiler frontend  DEBUG Version 3.3 (MT) Jul-1991$
#
# Version 1.0: [<PERSON><PERSON>] (29/08/90)
#       One level of user include files is currently supported but an
#       unlimited number of levels for system include files.
#
# Version 2.0: <PERSON> (12/01/91)
#       -logfile call <filename.err> instead of c4l<filename.err>
#       -logfile deleted if compilation successfull
#       -one logfile per file to compile instead of one for all.
#
# Version 3.0: <PERSON> (26/03/91)
#       -Return a status value of 1 if any error occured, 0 otherwise.
#       -compile with -qstat option before calling `cupinc`.  This
#        generates a '.lst' file (FSE_STAT) which contains the name of
#        the include files necessary to the compilation.
#       -the FSE_LIST variable is now declared before its first use in
#        checking the specified source file.
#
# Version 3.1: <PERSON> (30/04/91)
#       - Empty source files are filtered
#       - .o is moved to .obj after the compilation
#       - IBM and SGI versions are now into agreement
#       - moved FSE_STAT file generation to cupinc
#
# Version 3.2: <PERSON> (22/05/91)
#       - Changed name of `.err' file  to `.lst.xxx'
#       - `.lst.xxx' file is not removed if it contains warnings. <warning/(W)>
#
# Version 3.3: <PERSON> (17-Jul-91)
#       - `.lst.xxx' file is not removed if it contains errors. <error/(E)>
#
#
onintr FSE_STOP
echo
echo "***  C4LD Version 3.3 - Jul 1991  ***"
echo
set FSE_FLAG=""
if (($#argv > 0) && ("$argv[1]" == "-S")) then
  set FSE_FLAG="-S"
  shift
endif
if ($#argv == 0) then
  echo 'Usage: c4ld [-S] filename [ filename... ]'
  exit 1
endif
#
# Prepare the environment
unalias rm
set FSE_OPTS="`printenv c4loptions`"
set ERRFLAG = 0
#
while ($#argv > 0)
#
# Prepare the list file
  set FSE_LIST="`norev $argv[1]`"
  set FSE_LIST="$FSE_LIST:t"
  set FSE_LIST="`revl $FSE_LIST:r.lst +`"
#
# Check the specified source file
  set FSE_DATA="`revl '-$argv[1]' %c`"
  set stat=$status
  if ($stat != 0) then
    echo "$argv[1] => `reverr $stat`" >>$FSE_LIST
    set ERRFLAG = 1
# Check if there are errors in the list file
    if (-e $FSE_LIST) then
      echo "%c4ld: Error/Warning messages in logfile $FSE_LIST"
    endif
    shift
    continue
  endif
#
# Make sure the specified file is not empty
  set FSE_LEN = (`ls -l $FSE_DATA`)
  if ( $FSE_LEN[5] == "0" ) then
    echo "%c4ld: $argv[1] => The file is empty." >>$FSE_LIST
    set ERRFLAG = 1
# Check if there are errors in the list file
    if (-e $FSE_LIST) then
      echo "%c4ld: Error/Warning messages in logfile $FSE_LIST"
    endif
    shift
    continue
  endif
#
# Define the files to manipulate
  set FSE_SHOW="`revl -$FSE_DATA +%obj`"
  set FSE_NAME="`norev $FSE_DATA:t`"
  set FSE_WORK="$FSE_NAME:r_deb.c"
  set FSE_CODE="$FSE_NAME:r_deb.s"
  set FSE_DONE="`unik $FSE_DATA`"
  touch $FSE_DONE
#
  if (-e $FSE_WORK) then
    echo "Deleting   $FSE_WORK"
    rm $FSE_WORK
  endif
  if ("$FSE_FLAG" == "-S") then
    if (-e $FSE_CODE) then
      echo "Deleting   $FSE_CODE"
      rm $FSE_CODE
    endif
  else
    if (-e $FSE_SHOW) then
      echo "Deleting   $FSE_SHOW"
      rm $FSE_SHOW
    endif
  endif
#
  echo " "
  echo "Generating $FSE_WORK"
  cupinc $FSE_DATA $FSE_WORK $FSE_DONE $FSE_LIST
  if (($status != 0) || (! -e $FSE_WORK)) then
    echo "%c4ld: Processing failed: no source file created"
    if (-e $FSE_WORK) rm $FSE_WORK
    rm $FSE_DONE
# Check if there are errors in the list file
    if (-e $FSE_LIST) then
      echo "%c4ld: Error/Warning messages in logfile $FSE_LIST"
    endif
    set ERRFLAG = 1
    shift
    continue
  endif
#
  if ("$FSE_FLAG" == "-S") then
    echo "Generating $FSE_CODE"
    cc -c -g -S $FSE_OPTS $FSE_WORK >>&$FSE_LIST
    set stat=$status
    set FSE_BUG="`basename $FSE_WORK .c`.o"
    if (-e $FSE_BUG) mv $FSE_BUG $FSE_SHOW
    if ($stat != 0) then
      echo "%c4ld: Compilation failed: no code file created"
      if (-e $FSE_CODE) rm $FSE_CODE
      set ERRFLAG = 1
    else
#      grep -i 'warning' $FSE_LIST > & /dev/null
      grep -i '(w)' $FSE_LIST > & /dev/null
      set STAT1 = $status
#      grep -i 'error' $FSE_LIST > & /dev/null
      grep -i '(e)' $FSE_LIST > & /dev/null
      set STAT2 = $status
      if ( ($STAT1 != 0) && ($STAT2 != 0) ) rm $FSE_LIST
    endif
    rm $FSE_DONE
# Check if there are errors in the list file
    if (-e $FSE_LIST) then
      echo "%c4ld: Error/Warning messages in logfile $FSE_LIST"
      set ERRFLAG = 1
    endif
    shift
    continue
  endif
#
  echo "Compiling  $FSE_WORK"
  cc -c -g $FSE_OPTS -o $FSE_SHOW $FSE_WORK >>&$FSE_LIST
  set stat=$status
  set FSE_BUG="`basename $FSE_WORK .c`.o"
  if (-e $FSE_BUG) mv $FSE_BUG $FSE_SHOW
  if ($stat != 0) then
    echo "%c4ld: Compilation failed: no object file created"
    rm $FSE_DONE
# Check if there are errors in the list file
    if (-e $FSE_LIST) then
      echo "%c4ld: Error/Warning messages in logfile $FSE_LIST"
    endif
    set ERRFLAG = 1
    shift
    continue
  else
#    grep -i 'warning' $FSE_LIST > & /dev/null
    grep -i '(w)' $FSE_LIST > & /dev/null
    set STAT1 = $status
#    grep -i 'error' $FSE_LIST > & /dev/null
    grep -i '(e)' $FSE_LIST > & /dev/null
    set STAT2 = $status
    if ( ($STAT1 != 0) && ($STAT2 != 0) ) rm $FSE_LIST
  endif
#
# Check if there are errors in the list file
  if (-e $FSE_LIST) then
    echo "%c4ld: Error/Warning messages in logfile $FSE_LIST"
    set ERRFLAG = 1
  endif
  if (-e $FSE_DONE) rm $FSE_DONE
  shift
end
#
exit $ERRFLAG
#
# Interrupt handling
FSE_STOP:
if (-e $FSE_SHOW) rm $FSE_SHOW
if (-e $FSE_WORK) rm $FSE_WORK
if (-e $FSE_DONE) rm $FSE_DONE
if (-e $FSE_WARN) rm $FSE_WARN
exit 1
