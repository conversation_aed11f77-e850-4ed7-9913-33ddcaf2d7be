#! /bin/csh -f
# 
# This is a CSH script to do full or incremental backup 
# of a Unix computer system.
#
# REQUIREMENTS :
# ------------
# 
# We use the logical name "cae_log" to find the location to write our 
# backup reports. 
# We use the logicals name "cae_backlog_delay to determine the amount 
# of backup reports to keep.
#
# SYNTAX:
# ------
#
# caebackup { help | all | inc dir days | dir |  restore file [ file ] |
#             rebuild }
#
#     Where:
#        "HELP" or "help": Get help message.
#        "ALL" or "all": Full backup all file systems.
#        "INC" or "inc": Do an incremental backup of an "efs", ie. a
#                        file system whose files have been modified since
#                        a specified number of "days".
#                        These above 3 items are the required arguments
#                        for the incremental backup.
#        "DIR": is the full path of a directoy or a file system to do full
#         backup. Only one directory can be specified at one time.
#        "RESTORE" or "restore": To restore file(s).
#        "REBUILD" or "rebuild": rebuild cae1 disk from formatted disk.
#        "SHOW" or "show" : List files saved on tape.
#
# This script provides a list of files backuped to make restoring file
# later on easily. Errors on a tape are also kept on the file.
#
# Written by <PERSON><PERSON>
# Version 5.0
# Oct., 1990
# CAE Electronics, Dept. 73
#
# Version 5.2 (Mar. 07th, 91):  Tuan D.
#    - Modified to backup files from inside each file system.
#
# Version 5.3 (November 91): M. Lafrance
#    - We translate the "cae_log" logical name to find the directory to 
#      write the backup report.
#
# Version 5.4 (December 91): M. Bernuy
#     - Brought from sgi to ibm.
#
# Version 5.5 (Febuary 92): M. Bernuy
#     - use logical name cae_backlog_delay
#
# Version 6.0 (October 92): Tuan D. 
#     - Modified to use the 8mm tape drive to backup entire system.
#
# Version 6.1 (January 92): M. Lafrance
#    - Fixed bug in the 8mm code.
#
# Version 7.0 (February 92): M. Lafrance
#     - Merged the SCTB, SGI and IBM version for 8mm tape.
#     - No more file systems involved, we use as many tapes as we need.
#     - No more direct backup of a file system, only full backups or
#       incrementals.
#     - Merged the brufull and bruinc scripts.
#     - Complete rewrite and cleanup.
#     - Rename brufull to caebackup
#
# Version 7.1 (April 92): M. BERNUY
#     - Added rebuild option.
#     - Added show option.
#
# $Revision: Caebackup backup system Version 7.1 (MB) 4/92$
#
onintr EXIT
# 
# If it an SGI or an IBM ?
#
if ( -e /usr/sbin/sgikopt ) then
  set SGI=1
else
  set SGI=0
endif
clear
echo '#################################'
echo '#                               #'
echo '#   Backup/Restore program      #'
echo '#         Version 7.1           #'
echo '#         Apr. - 1992           #'
echo '#                               #'
echo '#################################'
echo " "
set TAPEDEV="`printenv TAPEDEVICE`"
if ( "$TAPEDEV" == "" ) then
  if ($SGI) then
    set TAPEDEV=/dev/tape
  else
    set TAPEDEV=/dev/rmt0.4
  endif
else
  echo "**** Using device $TAPEDEV ****"
  echo " "
endif
set USAGE="Usage: caebackup { help | inc dir days | all | dir |"
set USAGE2="                   restore file(s) | rebuild | show }"
set SAVEDIR=`pwd`
set LOGDIR="`/cae/logicals -t cae_log`"
set TYPE = "FULL"
set NAME = "FULL"
unalias find
unalias ls
unalias rm
unalias backup
unalias bru
unalias cat
if ( $#argv == 0 ) goto HELP
#
# Select operation:
#
switch ("$argv[1]")
case "all":
case "ALL":
  set argv[1] = "/"
  goto BACKUP
case "help":
case "HELP":
  goto HELP
case "INC":
case "inc":
  set TYPE = "INC"
  set NAME = "INCREMENTAL"
  shift
  switch ($#argv)
  case 0:
    set argv=( / 1 )
  case 1:
    set argv=( $argv  1 )
  endsw
  goto BACKUP
case "restore":
case "RESTORE":
  shift
  goto RESTORE
case "REBUILD":
case "rebuild":
  shift
  if ($SGI) then
     echo " To rebuild a disk on the SGI computer, please use power off backup"
     echo " tapes and follow the procedure described in the general user manual."
     exit
  endif
  set DSKNAME=""
  set DSKSIZE=""
  set DSKCHASSIS=""
     echo -n " Which disk do you want to build? (/cae1) "
     set DSKNAME=($<)
     if ("$DSKNAME" == "") set DSKNAME="/cae1"
     if ("$DSKNAME" != "/cae1") then
        echo " Procedure to rebuild $DSKNAME not implemented yet. "
        exit
     else
        set DSKVLG=`echo $DSKNAME|cut -f2 -d'/'`"vg"
        echo -n " On which chassis do you want to rebuild $DSKNAME ? (10): "
        set DSKCHASSIS=($<)
        if ("$DSKCHASSIS" == "" ) set DSKCHASSIS="10"
        lsdev -C -c disk |grep "\-$DSKCHASSIS " > /dev/null
        if ($status != 0) then
           echo -n " What is the size of the disk? (355/670): "
           set DSKSIZE=($<)
           if ("$DSKSIZE" != "355" && "$DSKSIZE" != "670") then
              echo " Invalid disk size. "
              exit
           else
              mkdev -c disk -t $DSKSIZE"mb" -s scsi -p scsi0 -w $DSKCHASSIS -l tmp >& /dev/null
              if ($status != 0) then
                 echo " Can not assign chassis $DSKCHASSIS . Check power."
                 exit
              endif
              rmdev -ltmp -d >& /dev/null
           endif
        endif
     endif
     goto REBUILD
case "SHOW":
case "show":
  shift
  goto LIST
endsw
#
BACKUP:
# 
# Initialise LISTFILE
#
  if ( "$argv[1]" == "/" ) then
    set FILEBASE="$LOGDIR/`hostname`" 
  else
    set FILEBASE="$LOGDIR/$argv[1]:t"
  endif
  set LISTFILE="$FILEBASE`date +%m%d`.$TYPE"
  set BRUFILE="$LOGDIR/tempfile"
  set WORKFILE="$LOGDIR/workfile"
  rm $LISTFILE >& /dev/null
  touch $LISTFILE
  touch $BRUFILE
  touch $WORKFILE
  echo " Please label this tape as :"
  echo "    **** $NAME BACKUP OF $argv[1] at `date` ****" | tee -a $LISTFILE
  if ($TYPE == "INC") then
    echo "    **   Modified time: $2 day(s) **" | tee -a $LISTFILE
  endif
  echo " " | tee -a $LISTFILE
#
# Make sure we backup only directories
#
  if ( ! -d "$argv[1]" ) then
    echo " ERROR: $argv[1] is not a directory" | tee -a $LISTFILE
    echo " " | tee -a $LISTFILE
    goto EXIT
  endif
#
# Keep lattest lists of files backuped as defined by "cae_backlog_delay" :
#
  set DELAY=`/cae/logicals -t cae_backlog_delay`
  if $TYPE == "INC" then
    if ("$DELAY" == "") then
      set DELAY=7
    else
      @ DELAY = $DELAY * 7
    endif
  else
    if ("$DELAY" == "") set DELAY=2
  endif
  if ($SGI) then
    if (`ls -t $FILEBASE*.$TYPE | wc -l` > 1) rm `ls -t $FILEBASE*.$TYPE | tail +$DELAY` > & /dev/null
  else
    if (`ls -t1 $FILEBASE*.$TYPE | wc -l` > 1) rm `ls -t1 $FILEBASE*.$TYPE | tail +$DELAY` > & /dev/null
  endif
#
# We prompt the questions to the operator
#
  echo -n " Do you want the log file to be printed at the end? (Y/N): "
  set x=($<)
  if ( "$x" == "y" || "$x" == "Y" ) then
    set PRI=1
    echo "    Log file will be printed at end of backup. "
  else
    set PRI=0
    echo "    Log file will NOT be printed at end of backup."
  endif
  echo " "
  if ($SGI) then
    echo  "    Retensioning tape, please wait ..."
    echo " "
    mt -t $TAPEDEV ret
  endif
#
# We start from root
#
  cd "$argv[1]"
# 
# Select the files to backup
#
  if ($TYPE == "INC") then
    find . -type f -mtime -"$2" -print >>& $WORKFILE
    cat $WORKFILE | sed '/tmp/d' | sed '/debug/d' | sed '/lost+found/d' | \
      sed '/vads/d' | sed '/usr\/preserve/d' | sort >>& $BRUFILE
  else
    find . -type f -print >>& $WORKFILE
    cat $WORKFILE | sed '/tmp/d' | sed '/debug/d' | sed '/lost+found/d' | \
      sed '/vads/d' | sed '/usr\/preserve/d' | sort >>& $BRUFILE
  endif
  if ($status != 0) then
    echo " Cannot log directories. Status = $status" | tee -a $LISTFILE
    echo  " " | tee -a $LISTFILE
    goto EXIT
  endif
#
# Make sure the specified file is not empty
  set FIND_LEN = (`ls -l $BRUFILE`)
  if ( $FIND_LEN[5] == "0" ) then
    echo " Directory $argv[1] No file needs backups ..." | tee -a $LISTFILE
    echo  " " | tee -a $LISTFILE
    goto EXIT
  endif
  echo  "    $NAME backup of $argv[1] in progress ..." | tee -a $LISTFILE
  echo  " " | tee -a $LISTFILE
#
# Perform the actual backup
#
  if ($SGI) then
    cat $BRUFILE | bru -cvdf $TAPEDEV - | tee -a $LISTFILE
  else
    cat $BRUFILE | backup -iqvf $TAPEDEV | tee -a $LISTFILE
  endif
  if ($status != 0) then
    echo "*** $NAME Backup failed ..." | tee -a $LISTFILE
    echo " " | tee -a $LISTFILE
  else
    echo "    $NAME Backup successfull." | tee -a $LISTFILE
    echo " " | tee -a $LISTFILE
  endif
# 
# End of backup: 
# 
  echo "    $NAME BACKUP COMPLETED `date` " | tee -a $LISTFILE
  echo  " " | tee -a $LISTFILE
#
# Print the log file if required.
#
  if ($PRI) then
    `/cae/logicals -t cae_caelib_path`/print $LISTFILE
  endif
goto EXIT
# 
# Restoring files: 
# 
RESTORE:
  set FN=$#argv
  echo " "
  date 
  echo " "
#
# Checking tape label:
#
  set sf=0
  while ( $sf < $FN )
    @ sf = $sf + 1
    set argv[$sf]="$argv[$sf]"
# 
RESTOREF:
#
    echo "    Restoring file: $argv[$sf] ... "
    if ($SGI) then
      mt -t $TAPEDEV rew
      bru -xjf $TAPEDEV $argv[$sf]
    else
      restore -xdvqf $TAPEDEV "$argv[$sf]"
    endif
    if ( $status != 0 ) then
      echo " "
      echo " Cannot restore $argv[$sf]"
      echo " "
    else
      echo " Done "
    endif
#
  readnext:
  end
#
# Restore completes:
#
  echo " "
  echo " Restore completed. "
  date
exit 
#
# Rebuild of cae1 disk from formatted disk.
#
REBUILD:
  echo -n " Is the disk in chassis $DSKCHASSIS formatted? (Y/N): "
  set x=($<)
  if ( "$x" == "y" || "$x" == "Y" ) then
       if ("$DSKSIZE" == "") then
         echo -n " What is the size of the disk? (355/670): "
         set DSKSIZE=($<)
       endif
       if ( "$DSKSIZE" == "355" || "$DSKSIZE" == "670" ) then
          set DISK = "`lsdev -c disk -C -S available|grep '\-$DSKCHASSIS '|cut -f1 -d' '`"
          echo -n " Next step will rebuild $DSKNAME on chassis $DSKCHASSIS ($DSKSIZE"mb" disk) Ready? (Y/N): "
          set x=($<)
          if ( "$x" != "y" && "$x" != "Y" ) exit
          rmdev -l"$DISK" -d >& /dev/null
          mkdev -c disk -t $DSKSIZE"mb" -s scsi -p scsi0 -w $DSKCHASSIS >&/dev/null
          if ($status != 0) goto FORMAT
          exportvg $DSKVLG >& /dev/null
          set DISK = "`lsdev -c disk -C -S available|grep '\-$DSKCHASSIS '|cut -f1 -d' '`"
          mkvg -y $DSKVLG $DISK
          if ($status != 0) goto FORMAT
          varyonvg $DSKVLG
          rmfs $DSKNAME >& /dev/null
          crfs -v jfs -g $DSKVLG -a size=901120 -m $DSKNAME -A yes -p rw
          if ($status != 0) goto FORMAT
          mount $DSKNAME
          mkps -s32 -n -a $DSKVLG 
          if ($status != 0) echo " Can not create paging space on $DSKNAME .Please do it thru smit."
       else
          echo " Invalid disk size. Procedure exiting."
          exit
       endif
#     endif
     echo -n " Please put tape containing $DSKNAME in drive. Ready? (Y/N): "
     set x=($<)
     if ( "$x" == "y" || "$x" == "Y" ) then
        cd $DSKNAME
        restore -xdvqf $TAPEDEV ./cae1
        chown simex.cae *
        chmod ug+rw *
        echo " "
        echo " Disk $DSKNAME rebuilt."
     endif
     exit
  else
     goto NOERROR
FORMAT:
     echo " "
     echo " Can not assign chassis $DSKCHASSIS , or disk is not formatted,"
     echo " or $DSKNAME is already mounted."
     echo -n " Do you want to format the disk? (Y/N):  "
     set x=($<)
     if ( "$x" != "y" && "$x" != "Y" ) exit
     umount $DSKNAME >& /dev/null
     varyoffvg $DSKVLG >& /dev/null
     exportvg $DSKVLG >& /dev/null
NOERROR:
     foreach I ("`lsdev -c disk -C|grep '\-$DSKCHASSIS '`")
        set DISK="`echo $I|cut -f1 -d' '`"
        rmdev -l"$DISK" -d > /dev/null
     end
     if ("$DSKSIZE" == "" )then
       echo -n " What is the size of the disk to format? (355/670): "
       set DSKSIZE=($<)
     endif
     if ( "$DSKSIZE" == "355" || "$DSKSIZE" == "670" ) then
        mkdev -c disk -t $DSKSIZE"mb" -s scsi -p scsi0 -w $DSKCHASSIS >& /dev/null
        if ($status != 0) then
           echo " Can not assign chassis $DSKCHASSIS . Check power."
           exit
        else
          echo " Disk is ready to be formatted. Please use smit to do so."
        endif
     else
        echo " Invalid disk size. Procedure exiting."
        exit
     endif
  endif
exit
#
# List what's on the tape
#
LIST:
  if ($SGI) then
     bru -tvf $TAPEDEV
  else 
     restore -Tqf $TAPEDEV
  endif
exit
#
# Help messages:
#
HELP:
  echo " ******************************************************************* "
  echo " *                                                                 * "
  echo " * Backup/Restore Utility (v7.1, Apr 1992)                         * "
  echo " *                                                                 * "
  echo " *   Available options, select one:                                * "
  echo " *                                                                 * "
  echo " *     INC or inc: Incremental backup. Followed by the name of a   * "
  echo " *                 file system, and the number days during which   * "
  echo " *                 files to be backuped, were modified.            * "
  echo " *                 Ex. caebackup  inc  /  2                        * "
  echo " *                                                                 * "
  echo " *     ALL or all: Backup all file systems on tapes.               * "
  echo " *                 Ex. caebackup all                               * "
  echo " *                                                                 * "
  echo " *     File sytem name: Back up this file system or directory.     * "
  echo " *                      Only one file system or directory can be   * "
  echo " *                      can be specified at one time.              * "
  echo " *                      Ex. caebackup /cae1                        * "
  echo " *                                                                 * "
  echo " *     RESTORE or restore: Restore one or more files whose full    * "
  echo " *                         path names follows.                     * "
  echo " *                         Ex. caebackup restore /.login /usr/log  * "
  echo " *                                                                 * "
  echo " *     REBUILD or rebuild: Rebuild a disk                          * "
  echo " *                         Ex. caebackup rebuild /cae1             * "
  echo " *                                                                 * "
  echo " *     SHOW or show: List content of tape.                         * "
  echo " *                                                                 * "
  echo " *     HELP or help: Show this message.                            * "
  echo " *                                                                 * "
  echo " ******************************************************************* "
  echo " "
  echo $USAGE
  echo $USAGE2
  echo "Please try caebackup again."
  echo " "
exit
#
# Interrupt path:
#
EXIT: 
  cd $SAVEDIR 
  rm $BRUFILE >& /dev/null
  rm $WORKFILE >& /dev/null
exit
