                      CAETAPE file transfer utility


        CAETAPE utility is used to transport text files from a VAX
      computer or a GOULD computer to an IBM computer and vice versa.

1 RESTORE

         This mode restores files from the tape to your system.
   For each file read from the tape, caeta<PERSON> displays the full original
   file name and the default target name.  If the target name already exists,
   a warning message also appears on the screen caeta<PERSON> then asks what
   operation is to be done.  The following prompt is displayed:

             Restoring  : <input_file_name>
             Target file: <output_file_name>

             Enter  c(CONTINUE) or b(BYPASS) or n(RENAME) or
                    r(REWIND)   or h(HELP)   or x(EXIT)   or
                    q(QUIT)     or a(ALL)    :

    As it can be seen, eight different commands are available.

2 Example

         In the RESTORE mode, the following prompt is displayed:

             Restoring  : STF10:[CAE.EMPLOYEES]EXAMPLES.FOR;5
             Target file: /cae1/ship/receive.for.1

             Enter  c(CONTINUE) or b(BYPASS) or n(RENAME) or
                    r(REWIND)   or h(HELP)   or x(EXIT)   or
                    q(QUIT)     or a(ALL)    :


         This indicates that the current file on tape was originally called
    'EXAMPLES.FOR' and that CAETAPE intends to restore it as 'receive.for.1'.
         
         If you type  C or CONTINUE, the current file will be restored as
    specified by the target file name.

         If you type  A or ALL, all files left on the tape starting with
    the current one will be restored.  For each file processed, the default
    target file name will be used and a status message will be displayed.
         
2 CONTINUE

         This option restores the current file from the tape using the
    displayed output_file_name which already shows the next available
    revision level.  The utility displays the status of the restore
    operation along with the full name of the target file including
    revision level.

2 BYPASS

         This option bypasses the current file on tape.  The utility
    searches for the next file on the tape and prompts for the next
    operation.

2 RENAME

         This command allows to enter a new output file name and
    attempts to restore the file under the new target name.

2 REWIND

         This command rewinds the tape and restart the RESTORE
    command beginning with the first file on the tape.

2 HELP

         This command access the HELP facility.  Note that the
    "cae_help" logical name must be properly defined in order for
    caetape to be able to locate the help file.

2 EXIT

         This command is used to exit the caetape utility.  It
    has the same function as the QUIT command.

2 QUIT

         This command is used to exit the caetape utility.  It
    has the same function as the EXIT command.

2 ALL

         This command will restore all the files left on the tape
    beginning with the current file.  As each file is processed, a
    status message is displayed indicating the full file name of the
    target file.  When caetape reaches the end of the tape, the
    following prompt appears:

             Enter  r(REWIND) or q(QUIT) or x(EXIT):

    At this point, you have the choice of restarting the RESTORE process
    by rewinding the tape or simply exiting the utility.

1 SAVE

         When you  enter this mode, you can SAVE text files that
   are accessible from your account.  You enter the file names by
   answering the SAVE prompt :

             Enter file to save :

   Three formats are available for this mode.

             1) <Input_file_name>  [[,] <Output_file_name>]

             2) &<Input_file_name>

             3) <CR>

         With the first format above, you specify the file name(s)
   to SAVE and an optional output file name to be used during the
   RESTORE operation on the target computer. An input name can also
   contain wildcard characters.  If wildcard characters are specified,
   a target name cannot be specified.

         The second format is used to specify the name of a file
   which contains the Input and Output file names to be saved.  The
   format for this file is the same as the previous prompt driven
   format. 

         The third format is used to indicate that no more files
   are to be saved.

         Revision level is fully supported on the three computers.
   This means that if no revision level is entered, the highest
   revision of the input file is saved on the tape.

         If no output file name is specified, caetape defaults to an
   output name which is the same as the input name without the path and
   version level fields.

         When all the files names are entered, caetape performs the actual
   copy of the specified files.  As each file is processed, a message is
   displayed on the screen showing each file name and the status of the
   operation.  A log file called caetape.log is also created in the current
   directory and provides information each performed operation.

2 Example

         The following example attempts to write all the files starting
   with "test" in the current directory to the tape:

             Enter s(SAVE) or r(RESTORE) or h(HELP) :  s
             Overwrite tape ? (y/n)  y

              Enter file to save :  test*
              Enter file to save :  <cr>

             /cae/ship/test.for.4 saved
             /cae/ship/test.c.2 saved
             /cae/ship/test.inc.6 saved
 