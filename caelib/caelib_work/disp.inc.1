C      CHARACTER*80 RVLSTR_DISP_INC 
C     . /'$Revision: disp.inc V1.0 26-Feb-92 | start s/w rev descr $'/
        CHARACTER*8  TASKNAME      ! Process name
        INTEGER*4    
     ^               MAXOVR,       ! maximum of overrun tolerate
     ^               CLOCK_INT,    ! iteration clock interrupt
     ^               PROC_PTR,     ! Process pointer
     ^               CPU_NUM,      ! CPU id 
     ^               TASKNUM,      ! Task number 
     ^               MAX_LEG,      ! Maximum number of leg
     ^               WAKECNT,      ! local wake up counter
     ^               SLOW_FACTOR,  ! slow down factor
     ^               SLOW_LEN,     ! # of slow loading iteration
     ^               TOP_CPU,      ! TOP CPU flag
     ^               MAXCPU   ,    ! Maximum number of CPU
     ^               MAX_TASK ,    ! Maximum sync task per Box 
     ^               SYNCINT ,     ! Local synchronous interrupt counter
     ^               REFLEX_MEM ,
     ^               MASTER_CPU ,
     ^               RUNON_CPU ,
     ^               EXTERNAL_SYNC,
     ^               EXTERNAL_CLOCK
        REAL         
     ^               SIM_RATE,     ! simulation rate  
     ^               SIM_TIME,
     ^               RATETBL(20)
        INTEGER      MON_COUNTER
        REAL         MON_ACCU
        INTEGER      MON_BUFF(8)
C
        INTEGER      CRIT_DEPTH
        INTEGER      MAXCDB
        INTEGER*4       DSP$FRZFLG,! freeze flag
     ^                  DSP$ABORT, ! Abort flag
     ^                  DSP$SPECREQ, ! special request flag
     ^                  DSP$ITRCNT,! Iteration counter
     ^                  DSP$CLKINT,! Iteration length ( in 100 nanosec )
     ^                  DSP$YTITRN,! Iteration rate
     ^                  DSP$FRECHNG,! frequency change flag
     ^                  DSP$SUSPEND,! suspend flag
     ^                  DSP$SIMTIME,! simulation time
     ^                  DSP$DSIMTIME,! presision simulation time
     ^                  DSP$YITIM, !
     ^                  DSP$YIFRE, !
     ^                  DSP$MAXOVR,! Max overrun tolerate
     ^                  DSP$SPARE, ! instantenous Spare time
     ^                  DSP$SPAMIN,! min spare time
     ^                  DSP$SPAMAX,! max spare time
     ^                  DSP$SPACNT,!
     ^                  DSP$SPAVG, ! average spare time over the cycle
     ^                  DSP$OVRCNT,     ! over run counter
     ^                  OLDCNT/-1/      ! old counter
        INTEGER*4       DSP$RESET_AVG,
     ^                  DSP$RESET_MIN,
     ^                  DSP$RESET_MAX,
     ^                  DSP$RESET_CNT
C
        REAL*4          ACC_RESET_TIME
C
        INTEGER*4       DSP$MONTIM,
     ^                  DSP$MONMIN,
     ^                  DSP$MONMAX,
     ^                  DSP$MONCNT
C
        INTEGER*4       CONOVR_CNT
C
        COMMON /DISPBLK/ TASKNAME,
     ^                   MAXOVR,
     ^                   CLOCK_INT ,
     ^                   PROC_PTR,
     ^                   CPU_NUM,
     ^                   MAXCPU,
     ^                   MAX_LEG,
     ^                   WAKECNT,
     ^                   SLOW_FACTOR,
     ^                   SLOW_LEN,
     ^                   SIM_RATE ,
     ^                   SIM_TIME ,
     ^                   TASKNUM ,
     ^                   MAX_TASK,
     ^                   CRIT_DEPTH,
     ^                   RATETBL,
     ^                   TOP_CPU,
     ^                   MAXCDB,
     ^                   MON_COUNTER,
     ^                   MON_ACCU,
     ^                   MON_BUFF,
     ^                  DSP$FRZFLG,
     ^                  DSP$ABORT,
     ^                  DSP$SPECREQ,
     ^                  DSP$ITRCNT,
     ^                  DSP$YITIM,
     ^                  DSP$YIFRE,
     ^                  DSP$FRECHNG,
     ^                  DSP$SUSPEND,
     ^                  DSP$OVRCNT,
     ^                  DSP$DSIMTIME,
     ^                  DSP$SIMTIME,
     ^                  DSP$YTITRN,
     ^                  DSP$MONTIM,
     ^                  DSP$MONMIN,
     ^                  DSP$MONMAX,
     ^                  DSP$MONCNT,
     ^                  DSP$SPARE,
     ^                  DSP$SPAVG, 
     ^                  DSP$SPAMAX,
     ^                  DSP$SPAMIN,
     ^                  DSP$RESET_AVG,
     ^                  DSP$RESET_MIN,
     ^                  DSP$RESET_MAX,
     ^                  DSP$RESET_CNT,
     ^                  ACC_RESET_TIME,
     ^                  CONOVR_CNT,
     ^                  REFLEX_MEM,
     ^                  MASTER_CPU,
     ^                  RUNON_CPU,
     ^                  EXTERNAL_SYNC,
     ^                  EXTERNAL_CLOCK
