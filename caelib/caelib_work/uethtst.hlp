
  This help file contains a description on the various commands that the 
user can use to test the Ethernet link.

  Before accessing the commands available for the ETHERNET TEST, the user
is prompt to allocate a device. Allocatable devices are enp0, enp1 etc. 

  Please specify the entire path to the device as /dev/ etc.
The device can be deallocated with the DEALLOCATE command.

1 AUDIT
  This command provides the user with a list of all the configured 
DMCs on the system. However, should there be a problem with the 
controller, this command may not execute. 

  COMMAND FORMAT: AUDIT

1 STATISTICS
  This command displays various statistics maintained by 
the controller.

  COMMAND FORMAT: STATISTICS

1 DEALLOCATE
  This command deallocates a previously allocated device.

  COMMAND FORMAT: DEALLOCATE

1 QUIT
  This command allows the user to quit (terminate the session).

  COMMAND FORMAT: QUIT

1 READ
  This command allows the user to read data resident in DMC 
RAM memory. 
  
  All memory (00000 - FFFFF) is available for reading. A maximum of 200 
Hex Intel Words can be read at one time (1024 Bytes).

  COMMAND FORMAT: READ

  The user will be requested to provide the start address and 
the number of 16 bit words to read.

2 EXAMPLE
  ETHERTST> READ
  
  DMC NUMBER IS         : 2
  DMC START ADDRESS     : 8008
  NUMBER OF INTEL WORDS : 1A

1 REVREAD
  This command allows the user to read the DMC EPROM revision level.
For EPROMS prior to release K, no revision level is available and
the DMC will fail to respond to the request.

1 WRITE
  This command allows the user to write to DMC ram memory.The user is 
recommended to write only to the addresses 8000 to 85C3 to assure that
the DMC memory information does not get overwritten. If the user
overwrites information in DMC memory, the following steps are suggested:
      
   1. If the DMC command still operates past Phase 3, the DMC memory
      will have been refreshed.

   2. If step 1 above is not the case, a manual reset of the DMC is
      suggested.

  COMMAND FORMAT: WRITE

  The user will be required to provide the start address and DATA (Intel
Words) for transmission. Enter 99999 to begin transmission.

2 EXAMPLE
  ETHERTST> WRITE
      
  DMC NUMBER               : 1       (User entered 1   )  
  DMC START ADDRESS        : 8000    (User entered 8000)
  DATA FOR ADDRESS :08000  : AAAA    (User entered AAAA)
                   :08002  : 122C    (User entered 122C)
                   :08004  : 99999   (to transmit data)


1 DMC
  This command performs various exercises on the Ethernet Link.

  Stage 1: Initialize Ethernet Controller

  Stage 2: Read DMC memory

  Stage 3: Reset the DMC
         - Check all CME memory
         - Check all DMC memory

  Stage 4: Read & Write tests to the DMCs 
         - Data is written to location 8000 - 85C3 HEX.

  COMMAND FORMAT: DMC


  A maximum of 60 DMCs can be tested at the same time. Simply enter the
number of DMCs you wish to test (the DMC test will request it) then you
will be prompt to enter the DMC numbers one at a time. 

Ex.   Enter number of DMCs   : 2
      Enter DMC number 1     : 4     ! test this DMC first
                number 2     : 8     ! test this DMC second

2 Interface
  The user will be asked if the DMC(s) to test are part of Standard/Arinc.
If they are, then there are no further questions since ethertst knows the DMC
addresses it can write to. However, other systems like DN1, Military
special chassis etc. may use some of the Ram memory for code, stacks etc..
If the user is testing such a DMC, ethertst will prompt the user to
enter a start address and number of bytes to transfer. A quick way to 
determine a safe area to write to for the DMC command is to do a READ
command at different locations (start addresses) to located some
free space (addresses with zero content). 



