#
#  $Revision: MACRO - awk pattern & scanner command files V1.1 (LN) Mar-91$
#
#  awk patter scanner & parser command files to generate
#  the dispatcher data structure
#
#  Version 1.0: <PERSON><PERSON> (01-Aug-89)
#     - initial version
#
#  Version 1.1: <PERSON><PERSON> (01-Mar-91)
#     - Modified to support new disp table format
#
BEGIN {
  for ( i=1; i<=8; i++ )
    blanks = blanks " "

  i = index(FILENAME,".")
  if ( substr(FILENAME,i+1,3) == "def" ) {
    printf "#include \"disp.h\" \n"
    print "#include \"" substr(FILENAME,0,i-1) ".h\" "
    printf "disptbl_struct disp_table = { \n" 
    print " sizeof(disptbl_struct)/4 , "
  }
}

{ 
  while ((NF == 0) || ($1 == ";")) {
    getline
  }
  if ( $1 == "crit_table:" ) { # Begin generatin critical table

    getline                    # Skip to new line
    while ((NF == 0) || ($1 == ";")) {
      getline
    }

    print "sizeof(legtbl_struct)/4 , 0 , /* Leg Table size & Leg pointer */ "

    cleg_count = 0       # Critical leg counter
    while ($1 != "end_crittable:"){
      if ( $1 == "leg" ) {
        cleg_count++     # increment critical leg counter
        print "0 ,/* Leg Pfu status word */ "
        n = NF 
        print n-1 " , /* Leg size */"

        for ( i=2;i<=n;i++) {
            printf " band%s_ ," , $i
        }
        for (i=n-1;i<5;i++){
          printf " -1 ,"
        }
        printf " \n "
      }

      # Get next line

      getline 
      while ((NF == 0) || ($1 == ";")) {
        getline
      }
    }

  }                           # End generating Critical table
  if ($1 == "end_crittable:"){
    for (i=cleg_count;i<32;i++){
      print "-1,-1,-1,-1,-1,-1,-1,"
    }
  }

  if ( $1 == "sync_table:" ) { # Begin generating synchronous table

    sleg_count = 0             # synchronous leg counter
    getline                    # Skip to new line
    while ((NF == 0) || ($1 == ";")) {
      getline
    }

    print "sizeof(legtbl_struct)/4 , /* Leg Table size */ "
    print "0 , /* Leg Table pointer */ "

    while ($1 != "end_synctable:"){
      if ( $1 == "leg" ) {
        sleg_count++           # increment synchronous leg counter
        print "0 ,/* Leg Pfu status word */ "
        n = NF 
        print n-1 " , /* Leg size */"

          for ( i=2;i<=n;i++) {
            printf " band%s_ ," , $i 
          }
        for (i=n-1;i<5;i++){
          printf " -1 ,"
        }
        printf " \n "
      }

      # Get next line

      getline 
      while ((NF == 0) || ($1 == ";")) {
        getline
      }
    }
  }                           # End generating synchronous table

  if ($1 == "end_synctable:"){
    for (i=sleg_count;i<32;i++){
      print "-1,-1,-1,-1,-1,-1,-1,"
    }
  }

  # Generate the Band Dir structure
  if (substr($1,0,4) == "band") {
    print " sizeof(bandir_struct)/4 , "
    for (i=0;i<=31;i++) {
      printf "0,"
    }
    printf " \n "
  }

  # Generating band table
  while ( substr($1,0,4) == "band" ) {
      
     print "0 , /* Band PFU status word */"
     i = index($1,":")
     print substr($1,0,i-1) " , /* Number of modules */ "
     print "0, \" \" ,"
     # Get next line
     getline 
     while ((NF == 0) || ($1 == ";")) {
       getline
     }
     count = 0
     while ((substr($1,0,4) != "band") && (substr($1,0,3) != "end")){
       if ( $1 == "prog" ) { # Must be PROG macro
         count++
         i = index($2,".")
         if (substr($2,i+1,1) == "u")
           print "64 ,/* Unfreezable module */ "
         else
           print "0 ,/* Freezable module */ "

         print "0 ,"
         print "&" substr($2,0,i-1) ","
         if ( i > 9 )
           print "\"" substr($2,0,8)"\" ,"
         else 
           print "\"" substr($2,0,i-1) substr(blanks,0,9-i)"\" ," 
       }
       if ( $1 == "aprog" ) { # Must be APROG macro
         count++
         i = index($2,".")

         if ( i == 0 ) i = length($2) + 1

         if (substr($2,i+1,1) == "r")
           print "16 ,/* special request module */ "
         else
           print "0 ,/* normal module */ "

         print "0 ,"
         print "&" substr($2,0,i-1) ","
         if ( i > 9 )
           print "\"" substr($2,0,8)"\" ,"
         else 
           print "\"" substr($2,0,i-1) substr(blanks,0,9-i)"\" ," 
       }

       # Get next line
       getline 
       while ((NF == 0) || ($1 == ";")) {
         getline
       }
     } # end while PROG
     if ( count > 48 ) 
       print " %macro-err: maximum modules in band is 48 "
  } # end while BAND

}
END { printf "}; \n " 
      print  "int max_critleg = ", cleg_count , ";"
      print  "int max_syncleg = ", sleg_count , ";" 
      }
