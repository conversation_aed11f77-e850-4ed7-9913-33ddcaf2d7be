******************************************************
*         HCONFIG.DAT                                *
* EYE Hardware Configuration data base               *
******************************************************
* 'Projet EYE V4.0                                   *
* 'Date April 23 92                                  *
* 'System OSU (73)                                   *
*                                                    *
* Consult OSU or the I.S. before modifying this file *
*                                                    *
******************************************************
*                                                    
* You can include comments if you put a star '*' in  the first colunm
* of a line.                        
*                                                    
* All the applications start with ':' and a 3 letters
* name for the application.
* 
* Application INTerface                          
:INT:
*                                                    
* In the application INTerface the ":" should not change of position 
* Always use 2 digit number Ex use 02 for 2           
*                                                    
* You put the number of ethernet systems after the ':' in decimal
*
NUMBER_OF_ETHERNET_SYSTEMS:02
*                                                    
* You write to which system a DMC belongs         
*
DMC#_SYS_A:01 02 03 04 05 06 07 0A 0B 0E 10 
DMC#_SYS_B:0C 
*                                                    
* You put in the number of cabinet you have      
* do not forget always 2 digit                   
*                                
NUMBER_OF_CABINETS:05                                      
*                                                    
* For each cabinet:                              
* you specify his name up to 8 characters long         
*
*CABINET_NAME:L1
*                                                    
* All the DMCs are in 2 digit format and the SLOT number is in decimal
* written with a capital 'A' in front of the 2 digit number. The same
* thing applies for the 'B' which stands for 'Begin'          
*                                                    
*     LOCATION is made of a name : 3 characters Maximun  
*                         a type : A,B,C,D,          
*                         a dmc  : 2 digit hexadecimal      
*                         a slot : 2 digit decimal start 'A' 
*                         a begin: 2 digit decimal start 'A' 
*                                                    
*     name: You can have up to 4 different location  
*           name on one cabinet                      
*                                                    
*     Make sure you do this for all cabinets         
*
*     type: 'A' single Dmc chassis
*               in slot 26 and the interface card    
*               begin in slot 1                      
*
*LOCATION:A1 T:A DMC:01
*                                                    
*     type: 'B' single Dmc chassis
*               in slot 1 to 27 
*               interface card begin in slot 1       
*
*LOCATION:A4 T:B DMC:08 SLOT:A25
*
*                                                    
*     type: 'C' single Dmc chassis            
*               in slot 1 to 27              
*               interface card begin in slot B:
*
*LOCATION:A2 T:C DMC:0F SLOT:A25 B:A10
*                                                    
*     type: 'D' multiple DMC chassis          
*               in slot 1 to 27              
*               interface card begin in slot B:  
*     up to 4 DMC on one chassis                     
*
*LOCATION:A2 T:D DMC:0A SLOT:A07 B:A01
*
CABINET_NAME:L1
*
LOCATION:A1 T:A DMC:01
LOCATION:A2 T:A DMC:02
LOCATION:A3 T:A DMC:03
LOCATION:A4 T:A DMC:04
*
CABINET_NAME:L2
*
LOCATION:A1 T:A DMC:05
LOCATION:A2 T:A DMC:06
LOCATION:A3 T:A DMC:07
*
CABINET_NAME:FC-A601
*
LOCATION:A1 T:A DMC:0D
LOCATION:A2 T:D DMC:0A SLOT:A07 B:A01
LOCATION:A2 T:D DMC:0B SLOT:A16 B:A10
LOCATION:A2 T:D DMC:10 SLOT:A25 B:A19
*
CABINET_NAME:F2
LOCATION:A1 T:A DMC:0E
*
CABINET_NAME:F2A
*
LOCATION:B11T:A DMC:0C
*
******************************************************
*                        END                         *
******************************************************
