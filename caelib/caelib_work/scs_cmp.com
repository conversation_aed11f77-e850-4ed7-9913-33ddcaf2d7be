#!  /bin/csh -f
#   $Revision: SCS_CMP - Apply SCSGEN to a Data File V1.4 (RBE) Apr-92$
#
#   Syntax is : scsgen address_file cdb_file
#
#   Version 1.1 : <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#
#   Version 1.2 : <PERSON> Jul-91
#      - removed request to 429 file in configuration
#
#   Version 1.3 : <PERSON> Apr-92 
#      - will generate an output file SCSGEN.LOG in ship directory and 
#        enter it in configuration. 
#
#   Version 1.4 : <PERSON> Apr-92 
#      - modify the FSE_MAKE instead of FSE_SAVE also chnage in SCS_BLD.COM
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SHIP="`logicals -t CAE_CDBNAME`"
set SHIP_DIR="`logicals -t CAE_SHIP`"
set FSE_BASE=""
set FSE_FILE=""
#
if ($#argv > 0) then
  set FSE_FILE="$argv[1]"
endif
#
if ($#argv > 1) then
  set FSE_BASE="$argv[2]"
endif
#
if ("$FSE_BASE" == "") then
  echo -n "_Enter cdb source name [$SHIP.cdb] ? "
  set FSE_BASE="$<"
  if ("$FSE_BASE" == "") set FSE_BASE="$SHIP.cdb"
endif
#
if ("$FSE_BASE:e" == "") set FSE_BASE="$FSE_BASE".cdb
#
if ("$FSE_FILE" == "") set FSE_FILE="$FSE_BASE:r"dmc0.429
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_TEMP=$SIMEX_DIR/work/scst_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/scsl_$FSE_UNIK
set FSE_MAKE=$SIMEX_DIR/work/scsm_$FSE_UNIK
set FSE_LOG="$SHIP_DIR/`revl  'scsgen.log' +`"
#
echo "&$FSE_BASE"  >$FSE_TEMP
echo '@$.'         >>$FSE_TEMP
echo '&$*.xsl'     >>$FSE_TEMP
echo '@cdb_spare.' >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
unalias scsgen
scsgen $FSE_FILE $FSE_BASE | tee "$FSE_LOG" 
set stat=$status
#
unsetenv SOURCE
unsetenv TARGET
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
set FSE_SAVE=$SIMEX_DIR/enter/$FSE_FILE:t
if ( -e "$FSE_SAVE") rm $FSE_SAVE
echo " Generated  $FSE_LOG " >> $FSE_MAKE
setenv fse_source "$FSE_MAKE"
setenv fse_target "$FSE_SAVE"
setenv fse_action "S"
fse_compile
rm $FSE_MAKE
exit
