#! /bin/csh -f
#! $Revision: SET_ETH_LOGNAMES - eth logical names setting V2.0 May-92$
#!
#! Version 1.0: <PERSON> (19 Nov 1991)
#!    - initial version
#!
#! Version 1.1: <PERSON> (10-Dec-91)
#!    - removed creation of CAE_VISUAL_ADDR logical name
#!
#! Version 1.2: <PERSON> (18-Dec-91)
#!    - loop only on keywords that were validated and are in DATA_VAL.
#!
#! Version 1.3: <PERSON> (28-Jan-92)
#!    - added following logical names:
#!            - cae_dmc_etfile_1
#!            - cae_input_ec_1
#!            - cae_dmc0_etfile
#!            - cae_dmc1_etfile
#!            - cae_dfc_eth
#!
#! Version 2.0: <PERSON> (07-May-92)
#!    - replaced DMC1IO & DMC2IO by DMC_IO & DMC1IO
#!    - added DMC2IO & DMC3IO and all the logical names associated to them.
#!    - removed creation of CAE_DMC0_ETFILE, CAE_DMC1_ETFILE, CAE_DMC0_ADDR1
#!
#! Description:
#!
#!  This script sets the logical names listed below according to the
#!  content of "ethernet_location.dat" file.  The latter describes the
#!  usage of each ethernet port using predefined keywords ( dn1_io, dmc_io,
#!  if__io, decnet, dmc1io, vis_io , dmc2io, dmc3io ).
#!
#!
#!       Port   Ethernet    Port     \
#!      Number   driver     Usage    |    Default setting of
#!                type               |
#!                                   |    "ethernet_location.dat"
#!         1      cae       dn1_io    \
#!         2      cae       dmc_io    /   on May 08th 1992
#!         3      ibm       if__io   |
#!         4      ibm       decnet   |
#!         5      cae       vis_io   |
#!                                   /
#!
#!  From this data file, we find the name of the ethernet driver and/or
#!  the hardware address of the ethernet adapter associated to the keyword
#!  and we set the following logical names:
#!
#!         cae_dmc_addr1
#!         cae_dmc1_addr1
#!         cae_dmc2_addr1
#!         cae_dmc3_addr1
#!         cae_dmc_etfile
#!         cae_dmc_etfile_1
#!         cae_dmc_etfile_2
#!         cae_dmc_etfile_3
#!         cae_dmc_port1
#!         cae_dmc1_port1
#!         cae_dmc2_port1
#!         cae_dmc3_port1
#!         cae_dfc_eth
#!         cae_dn1
#!         cae_if_etfile
#!         cae_inp
#!         cae_input_ec
#!         cae_input_ec_1
#!         cae_input_ec_2
#!         cae_input_ec_3
#!         cae_scs
#!         cae_std
#!         cae_visual
#!
#############################################################################
#        Start of program
#############################################################################
#
set CRASH    = 1
set DATA     = ""
set DATA_TMP = ""
set DATA_VAL = ""
set JUNK     = ""
set TMP_DEST = ""
onintr FINISH
#
#############################################################################
#        Open log file
#############################################################################
#
set LOG = "`/cae/logicals -t cae_log`"
if ($status != 0) then
   set LOG = "/ethernet.log"
else
   set LOG = "`echo $LOG`/ethernet.log"
endif
if ( -e "$LOG" ) mv "$LOG" "$LOG.last"
echo "BOOT TIME ETHERNET CONFIGURATION (`date`)."  >$LOG
echo " "                                          >>$LOG
#
#############################################################################
#        Locate "ethernet_location.dat" file
#############################################################################
#
set BIN = "`/cae/logicals -t cae_caelib_path`"
if ($status != 0) then
   set DATA = "/cae/ethernet_location.dat"
else
   set DATA = "`echo $BIN`/ethernet_location.dat"
endif
if ( ! -e "$DATA" ) then
   echo "SET_ETH_LOGNAMES: Cannot find $DATA file."
   echo 'SET_ETH_LOGNAMES: ** PROGRAM MAY NEED TO BE CALLED AGAIN **'
   echo "ERROR: Cannot find $DATA file."          >>$LOG
   goto BYPASS_MAIN
endif
#
#############################################################################
#        Create temporary data file into which comments are removed
#############################################################################
#
set DATA_TMP = "`$BIN/unik /tmp/eth`"
grep -v '^#' "$DATA" >"$DATA_TMP"
#
# create another file that will contain only valid data
set DATA_VAL = "`$BIN/unik /tmp/et2`"
#
#############################################################################
#        Validate information in data file
#############################################################################
#
set VALID = 0
set PRINT = 0
foreach LINE ("`cat $DATA_TMP`")
#
   set LINE = ($LINE)
   if ( $#LINE != 3 ) then
      echo 'ERROR: Incorrect number of columns in line.' >>$LOG
      set VALID = 1
      set PRINT = 1
   else
#
      switch ("$LINE[1]")
      case "0":
      case "1":
      case "2":
      case "3":
      case "4":
      case "5":
      case "6":
      case "7":
         breaksw
      default:
         echo "ERROR: ..$LINE[1].. should rather range from 0 to 7." >>$LOG
         set VALID = 1
         set PRINT = 1
      endsw
#
      switch ("$LINE[2]")
      case "cae":
      case "CAE":
      case "ibm":
      case "IBM":
         breaksw
      default:
         echo "ERROR: ..$LINE[2].. should be one of cae,ibm". >>$LOG
         set VALID = 1
         set PRINT = 1
      endsw
#
      switch ("$LINE[3]")
      case "dn1_io":
      case "dmc_io":
      case "dmc1io":
      case "dmc2io":
      case "dmc3io":
      case "vis_io":
      case "if__io":
      case "decnet":
      case "DN1_IO":
      case "DMC_IO":
      case "DMC1IO":
      case "DMC2IO":
      case "DMC3IO":
      case "VIS_IO":
      case "IF__IO":
      case "DECNET":
         breaksw
      default:
         echo "ERROR: ..$LINE[3].. should be one of dn1_io,dmc_io,dmc1io,dmc2io,dmc3io,vis_io,if__io". >>$LOG
         set VALID = 1
         set PRINT = 1
      endsw
#
   endif
#
   if ( "$PRINT" == "1" ) then
      echo  "Bad line in $DATA is : $LINE" >>$LOG
      set PRINT = 0
   else
      echo "$LINE" >>$DATA_VAL
   endif
#
end
#
if ( "$VALID" != "0" ) then
   echo "SET_ETH_LOGNAMES: Invalid information in $DATA file."
   echo "SET_ETH_LOGNAMES: See log file $LOG."
   if (! -e "$DATA_VAL") then
      echo "SET_ETH_LOGNAMES: Program exiting..."
      set CRASH = 0
      goto FINISH
   endif
endif
#
############################################################################
#        MAIN LOOP: 
#           - device driver name is found for each keyword.
#           - ethernet address is replaced with a symbol for each keyword.
############################################################################
#
set CAE_ID = 0
set KEY_ID = 1
set KEYWORD = (`awk '{ print $3 }' $DATA_VAL`)
#
while ( $KEY_ID <= $#KEYWORD )
#
   set DRIVER  = ""
   set ADDRESS = ""
   set SYMBOL  = ""
   set PARAM   = (`grep -i $KEYWORD[$KEY_ID] $DATA_VAL`)
   if ($status == 0) then
#
#  Set symbol that will be used to replace ethernet address
#  --------------------------------------------------------
#
         if ( ("$KEYWORD[$KEY_ID]" != "if__io") && \
              ("$KEYWORD[$KEY_ID]" != "IF__IO") && \
              ("$KEYWORD[$KEY_ID]" != "vis_io") && \
              ("$KEYWORD[$KEY_ID]" != "VIS_IO") && \
              ("$KEYWORD[$KEY_ID]" != "decnet") && \
              ("$KEYWORD[$KEY_ID]" != "DECNET") ) then
            set SYMBOL = "$KEYWORD[$KEY_ID]"
         endif
#
#  Find device driver name and assign symbol to replace the ethernet address
#  -------------------------------------------------------------------------
#
#  Handle case of a CAE driver
#  ---------------------------
#
         if (("$PARAM[2]" == "cae") || ("$PARAM[2]" == "CAE")) then
            set DRIVER  = "/dev/cae_ent$CAE_ID"
            if ( -e "$DRIVER" ) then
               set ADDRESS = "`$BIN/net_addr.exe $DRIVER $SYMBOL`"
               @ CAE_ID++
            else
               echo "WARNING: No CAE device driver found for $KEYWORD[$KEY_ID] interface." >>$LOG
               set DRIVER = ""
            endif
         else
#
#  Handle case of an IBM driver
#  ----------------------------
#
            set JUNK = "`$BIN/unik /tmp/junk`"
            lsparent -C -k ent -H  >&$JUNK
            set ENT = "`grep 00-0$PARAM[1] $JUNK | cut -f1 -d' '`"
            if (($status == 0) && ("$ENT" != "")) then
               set DRIVER = "/dev/`echo $ENT`"
               set COMPARE = ""
#
#  Replace the ethernet address with the new symbol
#  ------------------------------------------------
#
               switch("$KEYWORD[$KEY_ID]")

               case "dn1_io":
               case "DN1_IO":
                 chdev -l "$ENT" -a use_alt_addr='yes' \
				 -a alt_addr='0x646E315F696F'
                 set COMPARE = '64:6E:31:5F:69:6F'
                 breaksw
   
               case "dmc_io":
               case "DMC_IO":
                 chdev -l "$ENT" -a use_alt_addr='yes' \
				 -a alt_addr='0x646D635F696F'
                 set COMPARE = '64:6D:63:5F:69:6F'
                 breaksw

               case "dmc1io":
               case "DMC1IO":
                 chdev -l "$ENT" -a use_alt_addr='yes' \
				 -a alt_addr='0x646D6331696F'
                 set COMPARE = '64:6D:63:31:69:6F'
                 breaksw

               case "dmc2io":
               case "DMC2IO":
                 chdev -l "$ENT" -a use_alt_addr='yes' \
				 -a alt_addr='0x646D6332696F'
                 set COMPARE = '64:6D:63:32:69:6F'
                 breaksw

               case "dmc3io":
               case "DMC3IO":
                 chdev -l "$ENT" -a use_alt_addr='yes' \
				 -a alt_addr='0x646D6333696F'
                 set COMPARE = '64:6D:63:33:69:6F'
                 breaksw

               default:
                 set COMPARE = "NOT APPLICABLE"

               endsw
#
#  Get back the address in hexadecimal
#  -----------------------------------
#
               lscfg -l $ENT -v    >&$JUNK
               set B = `awk '{FS="." ; if ("        Network Address"==$1) print $14 }' $JUNK`
               @ i = 11
               set A = ""
               while ( "$i" > "0" )
                  @ j = $i + 1
                  set A = ("`echo $B | cut -c$i,$j`" $A)
                  @ i = $i - 2
               end
               set ADDRESS = "${A[1]}:${A[2]}:${A[3]}:${A[4]}:${A[5]}:${A[6]}"
#
#  Validate SYMBOL vs ADDRESS
#  --------------------------
#
               if ("$COMPARE" != "NOT APPLICABLE") then
                  if ("$ADDRESS" != "$COMPARE") then
                     echo "WARNING: Cannot set new symbol for $DRIVER." >>$LOG
                  endif
               endif
#
            else
               echo "WARNING: No ethernet adapter in port #$PARAM[1]." >>$LOG
            endif
         endif
      endif
#
      if ("$ADDRESS" != "") echo "$KEYWORD[$KEY_ID] ethernet address .. $ADDRESS" >>$LOG
      if ("$DRIVER"  != "") echo "$KEYWORD[$KEY_ID] device driver ..... $DRIVER"  >>$LOG
#
   endif   
#
#  Replace/Delete logical names
#  ----------------------------
#
   switch ("$KEYWORD[$KEY_ID]")
#
   case "dn1_io":
   case "DN1_IO":
      if ( "$DRIVER" == "" ) then
         /cae/logicals -d "cae_dn1"
         /cae/logicals -d "cae_dfc_eth"
      else
         /cae/logicals -c "cae_dn1" "$DRIVER"
         /cae/logicals -c "cae_dfc_eth" "$DRIVER"
      endif
      breaksw
#
   case "dmc_io":
   case "DMC_IO":
      if ( "$DRIVER" == "" ) then
         /cae/logicals -d "cae_dmc_etfile"
         /cae/logicals -d "cae_dmc_port1"
         /cae/logicals -d "cae_inp"
         /cae/logicals -d "cae_scs"
         /cae/logicals -d "cae_std"
      else
         /cae/logicals -c "cae_dmc_etfile"  "$DRIVER"
         /cae/logicals -c "cae_dmc_port1"   "$DRIVER"
         /cae/logicals -c "cae_inp"         "$DRIVER"
         /cae/logicals -c "cae_scs"         "$DRIVER"
         /cae/logicals -c "cae_std"         "$DRIVER"
      endif
      if ( "$ADDRESS" == "" ) then
         /cae/logicals -d "cae_dmc_addr1"
         /cae/logicals -d "cae_input_ec"
      else
         /cae/logicals -c "cae_dmc_addr1"  "$ADDRESS"
         /cae/logicals -c "cae_input_ec"   "$ADDRESS"
      endif
      breaksw
#
   case "dmc1io":
   case "DMC1IO":
      if ( "$DRIVER" == "" ) then
         /cae/logicals -d "cae_dmc1_port1"
         /cae/logicals -d "cae_dmc_etfile_1"
      else
         /cae/logicals -c "cae_dmc1_port1"   "$DRIVER"
         /cae/logicals -c "cae_dmc_etfile_1" "$DRIVER"
      endif
      if ( "$ADDRESS" == "" ) then
         /cae/logicals -d "cae_dmc1_addr1"
         /cae/logicals -d "cae_input_ec_1"
      else
         /cae/logicals -c "cae_dmc1_addr1" "$ADDRESS"
         /cae/logicals -c "cae_input_ec_1" "$ADDRESS"
      endif
      breaksw
#
   case "dmc2io":
   case "DMC2IO":
      if ( "$DRIVER" == "" ) then
         /cae/logicals -d "cae_dmc2_port1"
         /cae/logicals -d "cae_dmc_etfile_2"
      else
         /cae/logicals -c "cae_dmc2_port1"   "$DRIVER"
         /cae/logicals -c "cae_dmc_etfile_2" "$DRIVER"
      endif
      if ( "$ADDRESS" == "" ) then
         /cae/logicals -d "cae_dmc2_addr1"
         /cae/logicals -d "cae_input_ec_2"
      else
         /cae/logicals -c "cae_dmc2_addr1" "$ADDRESS"
         /cae/logicals -c "cae_input_ec_2" "$ADDRESS"
      endif
      breaksw
#
   case "dmc3io":
   case "DMC3IO":
      if ( "$DRIVER" == "" ) then
         /cae/logicals -d "cae_dmc3_port1"
         /cae/logicals -d "cae_dmc_etfile_3"
      else
         /cae/logicals -c "cae_dmc3_port1"   "$DRIVER"
         /cae/logicals -c "cae_dmc_etfile_3" "$DRIVER"
      endif
      if ( "$ADDRESS" == "" ) then
         /cae/logicals -d "cae_dmc3_addr1"
         /cae/logicals -d "cae_input_ec_3"
      else
         /cae/logicals -c "cae_dmc3_addr1" "$ADDRESS"
         /cae/logicals -c "cae_input_ec_3" "$ADDRESS"
      endif
      breaksw
#
   case "vis_io":
   case "VIS_IO":
      if ( "$DRIVER" == "" ) then
         /cae/logicals -d "cae_visual"
      else
         /cae/logicals -c "cae_visual" "$DRIVER"
      endif
      breaksw
#
   case "if__io":
   case "IF__IO":
      if ( "$DRIVER" == "" ) then
         /cae/logicals -d "cae_if_etfile"
      else
         /cae/logicals -c "cae_if_etfile" "$DRIVER"
      endif
      breaksw
   endsw
#
   @ KEY_ID++
#
end
#
BYPASS_MAIN:
#
############################################################################
#        Terminate this execution
############################################################################
#
set CRASH = 0
#
FINISH:
#
if ("$DATA" != "" ) then
   if ( -e "$DATA" ) then
      echo " "                    >>$LOG
      echo "Content of $DATA is:" >>$LOG
      cat $DATA                   >>$LOG
   endif
endif
#
if ( "$DATA_TMP" != "" ) rm $DATA_TMP >&/dev/null
if ( "$DATA_VAL" != "" ) rm $DATA_VAL >&/dev/null
if ( "$JUNK"     != "" ) rm $JUNK     >&/dev/null
if ( "$TMP_DEST" != "" ) rm $TMP_DEST >&/dev/null
#
if ( "$CRASH" == "1" ) then
   if ( "$LOG" != "" ) then
      echo "Execution of eth_lognames.exe aborted by user..."        >>$LOG
      echo "WARNING: Some logicals names may not be set properly..." >>$LOG
   endif
   echo "SET_ETH_LOGNAMES: Execution aborted by user..."
   echo "SET_ETH_LOGNAMES: Some logicals names may not be set properly..."
   exit 1
endif
#
exit 0
