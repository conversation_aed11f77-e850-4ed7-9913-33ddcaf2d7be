#!/bin/csh -f
if ($#argv == 0) then
   echo 'Usage: ty filename'
else
 while ($#argv > 0)
   set NSRC = `revl -$argv[1]`
   set IEV = $status
   if ($IEV == 0 | $IEV == 1 | $IEV == 6) then
      echo "[2J[1;1H[7m *** (TY V1.5) ***     FILE TYPED: $NSRC [0m"
      echo '----+----1----+----2----+----3----+----4----+----5----+----6----+----7----+----8'
   else
      echo "[2J[1;1H[7m *** (TY V1.5) ***     FILE TYPED: $argv[1][0m"
      echo '----+----1----+----2----+----3----+----4----+----5----+----6----+----7----+----8'
   endif
   echo "[3;24r"
   echo "[2;1H"
   if ($IEV == 0 | $IEV == 1 | $IEV == 6) then
      pg -21 -f -n -p "(page %d):" $NSRC
   else
      pg -21 -f -n -p "(page %d):" $argv[1]
   endif
   echo "[1;24r"
   echo "[23;1H"
   shift
 end
endif
