#!  /bin/csh -f
#   $Revision: SCL_CMP - Apply SCALDATA to a Calibration File V1.1 (MT) May-91$
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#
if ($#argv == 0) then
  set FSE_FILE="`logicals -t CAE_CALFILE`"
else
  set FSE_FILE="$argv[1]"
endif
set FSE_FILE="`revl -'$FSE_FILE' @.cal`"
set stat=$status
if ! ($stat == 0 || $stat == 1 || $stat == 6) then
  reverr $stat
  exit
endif
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set FSE_UNIK="`pid`.tmp.1"
set FSE_TEMP=$SIMEX_DIR/work/sclt_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/scll_$FSE_UNIK
set FSE_MAKE=$SIMEX_DIR/work/sclm_$FSE_UNIK
#
echo '&$dmc.cld' >$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if  ! ( -e "$FSE_LIST" ) exit
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
unalias scaldata
scaldata $FSE_FILE
#
set stat=$status
unsetenv TARGET
unsetenv SOURCE
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit $stat
#
set FSE_SAVE=$SIMEX_DIR/enter/$FSE_FILE:t
if (-e "$FSE_SAVE") rm $FSE_SAVE
setenv fse_source "$FSE_MAKE"
setenv fse_target "$FSE_SAVE"
setenv fse_action "R"
fse_compile
rm $FSE_MAKE
exit
