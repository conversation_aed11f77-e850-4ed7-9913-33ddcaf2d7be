#!  /bin/csh -f
#!
#!  $Revision: CAELIB configuration support script V1.4 Mar-92$ 
#!
#!  Version 1.0: <PERSON>, May-91
#!     - initial version
#!
#!  Version 1.1: <PERSON> (21-Jun-91)
#!     - copy the soft link instead of the file pointed by the soft link
#!
#!  Version 1.2: <PERSON>, Feb-92
#!     - fix rename command
#!     - fix copy command when too many soft links for the foreach
#!
#!  Version 1.3: <PERSON>, Feb-92
#!     - add support for _site
#!
#!  Version 1.4: <PERSON>, Mar-92
#!     - fix copy command again when too many soft links
#!       and add . files support
#!
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ! ("$argv[2]" == "CREATE" || "$argv[2]" == "DELETE"  || \
      "$argv[2]" == "COPY"   || "$argv[2]" == "RENAME"  || \
      "$argv[2]" == "BACKUP" || "$argv[2]" == "RESTORE" || \
      "$argv[2]" == "COPIED" || "$argv[2]" == "REMOVED") exit
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
cd $SIMEX_DIR/caelib
set CAELIB_CONF="`cvfs '$argv[4]'`"
#
# Note : Does not create ${CAELIB_CONF}_site to keep it common
#
if ("$argv[2]" == "CREATE") then
  mkdir $CAELIB_CONF
  exit
endif
#
if ("$argv[2]" == "DELETE") then
  rm -rf $CAELIB_CONF
  if (-e ${CAELIB_CONF}_site) rm -rf {$CAELIB_CONF}_site
  exit
endif
#
if ("$argv[2]" == "COPY") then
  set CAELIB_NEW_CONF="`cvfs '$argv[5]'`"
  mkdir $CAELIB_NEW_CONF
  foreach FILE ( "`ls -lA $CAELIB_CONF | head -400 | cut -c55-`" )
    set FILE = ($FILE)
    if ( $#FILE == 3 ) then
      ln -s $FILE[3] $SIMEX_DIR/caelib/$CAELIB_NEW_CONF/$FILE[1]
    endif
  end
  foreach FILE ( "`ls -lA $CAELIB_CONF | tail +401 | cut -c55-`" )
    set FILE = ($FILE)
    if ( $#FILE == 3 ) then
      ln -s $FILE[3] $SIMEX_DIR/caelib/$CAELIB_NEW_CONF/$FILE[1]
    endif
  end
#  tar cBf - $CAELIB_CONF |(cd $CAELIB_NEW_CONF;tar xBf -)
#  cp -r $CAELIB_CONF $CAELIB_NEW_CONF
  if (-e ${CAELIB_CONF}_site) then
    mkdir ${CAELIB_NEW_CONF}_site
    foreach FILE ( "`ls -lA ${CAELIB_CONF}_site | head -400 | cut -c55-`" )
      set FILE = ($FILE)
      if ( $#FILE == 3 ) then
        ln -s $FILE[3] $SIMEX_DIR/caelib/${CAELIB_NEW_CONF}_site/$FILE[1]
      endif
    end
    foreach FILE ( "`ls -lA ${CAELIB_CONF}_site | tail +401 | cut -c55-`" )
      set FILE = ($FILE)
      if ( $#FILE == 3 ) then
        ln -s $FILE[3] $SIMEX_DIR/caelib/${CAELIB_NEW_CONF}_site/$FILE[1]
      endif
    end
  endif
  exit
endif
#
if ("$argv[2]" == "RENAME") then
  set CAELIB_NEW_CONF="`cvfs '$argv[5]'`"
  mv $CAELIB_CONF $CAELIB_NEW_CONF
  if (-e ${CAELIB_CONF}_site) mv ${CAELIB_CONF}_site ${CAELIB_NEW_CONF}_site
  exit
endif
#
if ("$argv[2]" == "BACKUP") then
  exit
endif
#
echo "Command $argv[2] for soft links not implemented yet."
exit
#
set argv[3]="`revl '-$argv[3]'`"
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
#
if ("$argv[2]" == "RESTORE") then
# mkdir + create soft links *** TBD
  exit
endif
#
if ("$argv[2]" == "COPIED") then
# ln on soft links or `ls -lA` *** TBD
  exit
endif
#
if ("$argv[2]" == "REMOVED") then
#                         *** TBD
  foreach i in ("`cat $argv[3]`")
    rm -rf $i
  end
  exit
endif
