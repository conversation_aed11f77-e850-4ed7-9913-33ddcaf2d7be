#! /bin/csh -f
#  $Revision: F4LLIB V1.1 FEB-1991 | <PERSON>$
#
#     This script is used to create a FORTRAN library or to
#     add a new member to it on UNIX based systems.  It allows
#     operations involving file names showing revision number.
#
#  Version 1.0 (28-Jan-1991): <PERSON><PERSON>
#
#     - Creation
#
#  Version 1.1 (22-Feb-1991): <PERSON><PERSON>
#
#     - validate the archive file before compiling
#
onintr inter
echo " "
echo "*** F4LLIB Version 1.1 - February 1991 ***"
echo " "
#
if ( $#argv < 2 ) then
    echo "USAGE: f4llib filename [ filename ] libname"
    exit 1
endif
#
#        determine the library name
#
set LIBPOS = $#argv
set NLIB = `revl -$argv[$LIBPOS] @a`
set stat = $status
if !( $stat == 0 || $stat == 5 || $stat == 1 || $stat == 6 ) then
    echo "Library file '$argv[$LIBPOS]' =>" `reverr $stat`
    exit 1
endif
#
#        validate the archive file
#
if ( $stat == 0 || $stat == 1 || $stat == 6 ) then
    if !( -e "/tmp/XXXX.tmp" ) then
        ar -t $argv[$LIBPOS] > &"/tmp/XXXX.tmp"
        set IEV = $status
        rm "/tmp/XXXX.tmp"
        if ( $IEV != 0 ) then
            echo $argv[$LIBPOS] "--> not a valid archive file."
            exit 1
        endif
    endif
endif
#
#        create new version of library to keep older version intact
#
set NEWLIB = `revl -$argv[$LIBPOS] +@a`
if ( $NLIB != "" ) then
    if ( -e $NLIB ) cp $NLIB $NEWLIB
endif
#
# loop without considering the last argument which is 'libname'
#
set ERRFLAG = 0
while ( $#argv > 1 )
#
    set NSRC = `revl -$argv[1] @for`
    set stat = $status
    if ( $stat != 0 ) then
        echo "Source file '$argv[1]' =>" `reverr $stat`
        set ERRFLAG = 1
        goto next
    endif
#
#        compile and check if an obj was created
#
    f4l $NSRC
    set stat = $status
    if ( $stat != 0 ) then
        echo $argv[1] '=> object file not created, library not updated'
        set ERRFLAG = 1
        goto next
    endif
#
#        change '.obj' extension for '.o' extension
#
    set TEMP = `norev $NSRC`
    set NOBJ = `revl $TEMP:r.obj`
    if ( $status != 0 ) then
        echo "internal error"
    endif
    set NO = "$TEMP:r.o"
    cp $NOBJ $NO
#
#        try to update the library
#
    ar ru $NEWLIB $NO
    if ( $status != 0 ) then
        set ERRFLAG = 1
        echo $argv[1] '=> not added to' $NEWLIB:t
    else
        echo $argv[1] '=> added to' $NEWLIB:t
    endif
    rm $NO
#
next:
#
    shift
end
#
exit $ERRFLAG
#
#        interrupt handling
#
inter:
if (-e $NOBJ ) then rm $NOBJ
if (-e $NEWLIB ) then rm $NEWLIB
if (-e $NO ) then rm $NO
if (-e "/tmp/XXXX.tmp") then `rm /tmp/XXXX.tmp`
exit 1
