#!  /bin/csh -f
#!  $Revision: SIMEX_INIT - Initialize Flight Simulator Env.  V9.0 Jul-92$
#!
#!  Version 7.0: <PERSON> (06-Feb-92)
#!     - Merge all configuration installation scripts and make a unique
#!       script that will install WORK & CAELIB_WORK on both IBM & SGI.
#!
#!         MACHINE_NAME  values: IBM,SGI
#!         MACHINE_USAGE values:
#!            HOST_FS   ( flight simulator )
#!            HOST_IF   ( flight simulator & instructor station )
#!            HOST_RG   ( regular sites, all the others )
#!            IF_MAST   ( stand alone instructor station )
#!            IF_SLAV   ( no WORK configuration, CAELIB_WORK only )
#!
#!  Version 7.1: <PERSON> (27-May-92)
#!     - WORK & TCP/IP are not always installed anymore
#!     - Replaced /cae & /cae1 by relative path
#!
#!  Version 8.0: <PERSON> (15-Jul-92)
#!     - automatic installation for CAELIB v13 on SGI is supported.
#!     - system administration is done automatically in SGI case.
#!     - installation of ROSE TEMPLATE added.
#!     - SITE PARAMETERS are asked in specific cases, not all the time.
#!     - /etc/mfg directory is created on IBM if it does not exist.
#!     - setting of ownership is done all the time to support SGI case.
#!     - sitelogs is copied from install directory inside INSTAL_WORK
#!       instead of in READ_TAPE section.
#!     - hosts is copied from install directory and updated only from
#!       inside INSTAL_TCP section.
#!     - chown on simex.exe in the element directory is done on
#!       simex.exe.* instead of simex.exe.1.
#!     - changed some user messages.
#!
#!  Version 9.0: Martin Talbot (30-Jul-92)
#!     - automatic installation for CAELIB v13 on IBM is supported.
#!     - compatible with new set_eth_lognames
#!     - added moving of files in progen directory
#!     - added moving of libcae.h in lib directory
#!     - added moving of liblv.a in lib directory
#!     - mktcpip is compatible with AIX 3.2 and added in caeconfig file.
#!     - cts_atg.exe belongs to root.sys and has the +s bit
#!     - dmcrlog is now simrlog
#!     - added fpc + f4l on cae_report_err.for
#!     - /etc/utmp on SGI has a+w bit
#!
#!===========================================================================
#!===========================================================================
#!  PROMPT THE USER FOR SITE INFORMATION
#!===========================================================================
#!===========================================================================

clear
unalias mv
unalias ls
unalias cd
unalias cp
unalias rm

echo "#1                (0)0lqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqk"
echo "#1                x(B)B                                            [0m(0)0x"
echo "#1                x(B)B        CAELIB INSTALLATION PROGRAM         [0m(0)0x"
echo "#1                x(B)B                version 8.0                 [0m(0)0x"
echo "#1                x(B)B           For CAELIB Release 13            [0m(0)0x"
echo "#1                x(B)B                JULY - 1992                 [0m(0)0x"
echo "#1                x(B)B                                            [0m(0)0x"
echo "#1                (0)0mqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqj(B)B"

##############################################
####### Get type of WORK configuration #######
##############################################

echo " "
echo " "
echo "    Please choose one of the following configurations:"
echo " "
echo "           1 - HOST for Flight Simulator Environment (FSE)."
echo "           2 - HOST (FSE) including Instructor Facility (I/F) structure."
echo "           3 - HOST for general purposes (development station)."
echo "           4 - MASTER station for Instructor Facility (I/F)."
echo "           5 - SLAVE station for Instructor Facility (I/F)."
echo " "
echo -n "    (1-5)? "
set CONF = ($<)
echo " "
switch ("$CONF")
   case 1:
      set USAGE = "HOST_FS"
      set MESS1 = " "
      set MESS2 = "          It will be made to load any remote computer."
      breaksw
   case 2:
      set USAGE = "HOST_IF"
      set MESS1 = " "
      set MESS2 = "          It will include structure for I/F software."
      breaksw
   case 3:
      set USAGE = "HOST_RG"
      set MESS1 = " "
      set MESS2 = "          It will work in stand alone mode."
      breaksw
   case 4:
      set USAGE = "IF_MAST"
      set MESS1 = "          It will be reduced for disk space requirements."
      set MESS2 = "          It will not contain any synchronous executable."
      breaksw
   case 5:
      set USAGE = "IF_SLAV"
      set MESS1 = "          It will be reduced for disk space requirements."
      set MESS2 = " "
      breaksw
   default:
      echo "    Program aborted by user..."
      echo " "
      exit 1
      breaksw
endsw
clear

#####################################
####### Get brand of computer #######
#####################################

echo "                             TYPE OF INSTALLATION"
echo "                             ===================="
echo " "
echo " "
echo -n "    Installation will be done on IBM or SGI? <IBM> "
TRY_AGAIN_1:
set NAME = ($<)
if ("$NAME" == "") set NAME = "IBM"
switch ("$NAME")
   case IBM:
   case ibm:
      set MACHINE = "IBM"
      breaksw
   case SGI:
   case sgi:
      set MACHINE = "SGI"
      breaksw
   default:
      echo -n "    Choose one of IBM, SGI : "
      goto TRY_AGAIN_1
      breaksw
endsw

###########################################################################
####### Get what we need to install (directories, configuration(s)) #######
###########################################################################

echo " "
echo -n "    Does CAELIB directory structure have to be extracted from tape ? <y> "
TRY_AGAIN_2:
set READ_TAPE = ($<)
if ("$READ_TAPE" == "") set READ_TAPE = "y"
switch ("$READ_TAPE")
   case Y:
   case y:
      set READ_TAPE = "YES"
      set INSTAL_CAELIB = "YES"
      breaksw
   case N:
   case n:
      set READ_TAPE = "NO"
      breaksw
   default:
      echo -n "    Answer y or n : "
      goto TRY_AGAIN_2
      breaksw
endsw
#
echo -n "    Does CAELIB_WORK configuration have to be created ? <y> "
TRY_AGAIN_3:
set INSTAL_CAELIB = ($<) 
if ("$INSTAL_CAELIB" == "") set INSTAL_CAELIB = "y"
switch ("$INSTAL_CAELIB")
   case Y:
   case y:
      set INSTAL_CAELIB = "YES"
      breaksw
   case N:
   case n:
      set INSTAL_CAELIB = "NO"
      breaksw
   default:
      echo -n "    Answer y or n : "
      goto TRY_AGAIN_3
      breaksw
endsw
#
echo -n "    Does SITE configuration have to be created ? <y> "
TRY_AGAIN_4:
set INSTAL_WORK = ($<) 
if ("$INSTAL_WORK" == "") set INSTAL_WORK = "y"
switch ("$INSTAL_WORK")
   case Y:
   case y:
      set INSTAL_WORK = "YES"
      breaksw
   case N:
   case n:
      set INSTAL_WORK = "NO"
      breaksw
   default:
      echo -n "    Answer y or n : "
      goto TRY_AGAIN_4
      breaksw
endsw
#
echo -n "    Does ROSE TEMPLATE have to be created ? <n> "
TRY_AGAIN_5:
set INSTAL_ROSE = ($<)
if ("$INSTAL_ROSE" == "") set INSTAL_ROSE = "n"
switch ("$INSTAL_ROSE")
   case Y:
   case y:
      set INSTAL_ROSE = "YES"
      breaksw
   case N:
   case n:
      set INSTAL_ROSE = "NO"
      breaksw
   default:
      echo -n "    Answer y or n : "
      goto TRY_AGAIN_5
      breaksw
endsw
#
echo -n "    Does TCP/IP have to be configured ? <y> "
TRY_AGAIN_6:
set INSTAL_TCP = ($<) 
if ("$INSTAL_TCP" == "") set INSTAL_TCP = "y"
switch ("$INSTAL_TCP")
   case Y:
   case y:
      set INSTAL_TCP = "YES"
      breaksw
   case N:
   case n:
      set INSTAL_TCP = "NO"
      breaksw
   default:
      echo -n "    Answer y or n : "
      goto TRY_AGAIN_6
      breaksw
endsw
#
echo " "
echo -n "    Enter first directory (root) ?  < /cae > "
set BASEDIR = ($<)
if ("$BASEDIR" == "")  set BASEDIR  = '/cae'
test -d "$BASEDIR"
if ( $status != 0 ) then
   if ( "$MACHINE" == "IBM" ) then
      echo " "
      echo ">>> Directory $BASEDIR does not exist."
      echo ">>> Create it and restart the installation procedure."
      echo " "
      exit 1
   else if ( "$MACHINE" == "SGI" ) then
      set INSTAL_DIR1 = "YES"
   endif
else
   set INSTAL_DIR1 = "NO"
endif
echo -n "    Enter second directory ?  < /cae1 > "
set BASEDIR2 = ($<)
if ("$BASEDIR2" == "") set BASEDIR2 = '/cae1'
if ("$BASEDIR" == "$BASEDIR2") then
   set INSTAL_DIR2 = "NO"
else
   test -d "$BASEDIR2"
   if ( $status != 0 ) then
      if ( "$MACHINE" == "IBM" ) then
         echo " "
         echo ">>> Directory $BASEDIR2 does not exist."
         echo ">>> Create it and restart the installation procedure."
         echo " "
         exit 1
      else if ( "$MACHINE" == "SGI" ) then
         set INSTAL_DIR2 = "YES"
      endif
   else
      set INSTAL_DIR2 = "NO"
   endif
endif
#
echo " "
if ("$READ_TAPE"     == "YES") then
   echo "      (1) CAELIB directory structure will be created for $NAME."
endif
if ("$INSTAL_CAELIB" == "YES") then
   echo "      (2) CAELIB_WORK configuration will be created for $NAME."
   if ("$MESS1" != " ") echo "$MESS1"
endif
if ("$INSTAL_WORK"   == "YES") then
   echo "      (3) WORK configuration will be created for $NAME."
   if ("$MESS2" != " ") echo "$MESS2"
endif
if ("$INSTAL_ROSE"   == "YES") then
   echo "      (3) ROSE TEMPLATE will be created for $NAME."
endif
if ("$INSTAL_TCP"    == "YES") then
   echo "      (4) TCP/IP will be configured for $NAME."
endif
echo " "
echo -n "    Is it O.K. (y/n)? <y> "
TRY_AGAIN_7:
set VALID = ($<) 
if ("$VALID" == "") set VALID = "y"
switch ("$VALID")
   case Y:
   case y:
      breaksw
   case N:
   case n:
      echo "    Program aborted by user..."
      exit 1
      breaksw
   default:
      echo -n "    Answer y or n : "
      goto TRY_AGAIN_7
      breaksw
endsw

###################################
####### Get site parameters #######
###################################

if ( ("$INSTAL_WORK" == "YES") || \
     ("$INSTAL_ROSE" == "YES") || \
     ("$INSTAL_TCP"  == "YES") || \
     ("$USAGE" == "IF_SLAV") ) then

   clear
   echo "                                SITE PARAMETERS"
   echo "                                ==============="
   echo " "
   if ( ("$INSTAL_WORK" == "YES") || \
        ("$INSTAL_ROSE" == "YES") || \
        ("$USAGE" == "IF_SLAV") ) then
#
      echo -n "     Enter the name of SIMex-PLUS configuration <WORK>   : "
      set CONF = ($<)
      echo -n "     Enter the name of the CDB (max. 6 char.)            : "
      set CDB  = ($<)
      echo -n "     Enter the contract (SHIP) name (max. 6 char.)       : "
      set SHIP = ($<)
      echo -n "     Enter the name of the HOST computer (max. 6 char.)  : "
      set HOST = ($<)
      echo -n "     Enter the CPU identification number (0-11)          : "
      set CPU  = ($<)
      echo -n "     Enter the MAXimum number of cpus - 1 (0-11)         : "
      set MAX  = ($<)
      echo -n "     Enter the value of CAE_SIMTAB (max. 12 digits)      : "
      set TAB  = ($<)
#
      if ("$CONF" == "") set CONF = "WORK"
      if ("$CPU" == "0") then
         set NODE = "$HOST"
      else
         @ ID = $CPU - 1
         set NODE = "${HOST}${ID}"
      endif
#
   endif
   if ("$INSTAL_TCP" == "YES") then
      echo -n "     Enter the host internet address (xxx.xxx.xxx.xxx)   : "
      set NET  = ($<)
   endif
#
   echo " "
   if ( ("$INSTAL_WORK" == "YES") || \
        ("$INSTAL_ROSE" == "YES") || \
        ("$USAGE" == "IF_SLAV") ) then
      echo "                 SIMex-PLUS configuration   : $CONF"
      echo "                 common data base name      : $CDB"
      echo "                 ship name (contract name)  : $SHIP"
      echo "                 computer name              : $NODE"
      echo "                 host name                  : $HOST"
      echo "                 cpu identification number  : $CPU"
      echo "                 number of cpus (computers) : $MAX"
      echo "                 value of SIMTAB            : $TAB"
   endif
   if ("$INSTAL_TCP" == "YES") then
      echo "                 host internet address      : $NET"
   endif
   echo " "
   echo -n "    Is it O.K. (y/n)? <y> "
TRY_AGAIN_8:
   set VALID = ($<) 
   if ("$VALID" == "") set VALID = "y"
   switch ("$VALID")
      case Y:
      case y:
         breaksw
      case N:
      case n:
         echo "    Program aborted by user..."
         exit 1
         breaksw
      default:
         echo -n "    Answer y or n : "
         goto TRY_AGAIN_8
         breaksw
   endsw
#
endif

####################################
###### Ethernet configuration ######
####################################

if ("$INSTAL_TCP" == "YES") then
   if ("$MACHINE" == "IBM") then
      clear
      echo "                             ETHERNET CONFIGURATION"
      echo "                             ======================"
      echo " "
      set DEVICE_DRIVER_CAE = ""
      set DEVICE_DRIVER_IF  = ""
      set FIRST_PASS        = 1
      set SYMBOL            = ( DN1_IO DMC_IO DMC1IO DMC2IO DMC3IO \
                                IF__IO DECNET VIS_IO )
                                
      foreach ADAPTER ( "`lsparent -C -k ent -H | grep -i available`")
#
         if ("$ADAPTER" != "") then
            set ADAPTER = ( $ADAPTER )
            if ("$FIRST_PASS" == "1") then
               echo "  For each of the following ports, please choose one of :"
               echo " "
               echo "     (1)  Communication with Control Force (DN1) cabinet."
               echo "     (2)  Communication with DMC interface #1."
               echo "     (3)  Communication with DMC interface #2."
               echo "     (4)  Communication with DMC interface #3."
               echo "     (5)  Communication with DMC interface #4."
               echo "     (6)  Communication with Instructor Facility (I/F)."
               echo "     (7)  Communication with CAE network."
               echo "     (8)  Communication with VISUAL system."
               echo " "
               echo "  Standard ethernet configuration for a flight simulator is:"
               echo "  ( port #1: DN1  #2: DMC#1  #3: I/F  #4: CAE  #5: AUDIO  #6: VISUAL )"
               echo " "
            endif
#
            switch ("$ADAPTER[3]")
               case "00-01":
                  set PORT    = "1"
                  set DEFAULT = "1"
                  breaksw
               case "00-02":
                  set PORT    = "2"
                  set DEFAULT = "2"
                  breaksw
               case "00-03":
                  set PORT    = "3"
                  set DEFAULT = "6"
                  breaksw
               case "00-04":
                  set PORT    = "4"
                  set DEFAULT = "7"
                  breaksw
               case "00-05":
                  set PORT    = "5"
                  set DEFAULT = "8"
                  breaksw
               case "00-06":
                  set PORT    = "6"
                  set DEFAULT = "3"
                  breaksw
               case "00-07":
                  set PORT    = "7"
                  set DEFAULT = "4"
                  breaksw
               case "00-08":
                  set PORT    = "8"
                  set DEFAULT = "5"
                  breaksw
            endsw
#
            echo -n "       port #${PORT} : (1..8)  <${DEFAULT}> "
TRY_AGAIN_9:
            set VALID = ($<) 
            if ("$VALID" == "") set VALID = "$DEFAULT"
            @ I_VALID = $VALID
            if ( ($I_VALID > 0) && ($I_VALID < 9) ) then
               set PORT_USAGE = "$SYMBOL[$I_VALID]"
            else
               echo -n "  Choose a number from 1 to 8, default is $DEFAULT : "
               goto TRY_AGAIN_9
            endif
#
            switch ("$PORT_USAGE")
               case "DN1_IO":
               case "DMC_IO":
               case "DMC1IO":
               case "DMC2IO":
               case "DMC3IO":
               case "VIS_IO":
                  set DRIVER_TYPE   = "CAE"
                  breaksw
               case "IF__IO":
                  set DRIVER_TYPE       = "IBM"
                  set DEVICE_DRIVER_IF  = "`echo $ADAPTER[1] | cut -c4`" 
                  breaksw
               case "DECNET":
                  set DRIVER_TYPE       = "IBM"
                  set DEVICE_DRIVER_CAE = "`echo $ADAPTER[1] | cut -c4`" 
                  breaksw
            endsw
#
#  Build temporary ethernet_location.dat file
#
            if ("$FIRST_PASS" == "1") then
               echo "$PORT $DRIVER_TYPE $PORT_USAGE"  > /tmp/ethernet_location.dat
            else
               echo "$PORT $DRIVER_TYPE $PORT_USAGE" >> /tmp/ethernet_location.dat
            endif
#
         else
            echo -n "  No ethernet adapters were detected.  Press any key... "
            set DUMMY = ($<)
         endif
#
         set FIRST_PASS = 0
      end
   endif

endif

##########################################
###### Start installation procedure ######
##########################################

clear
echo "                               START INSTALLATION"
echo "                               =================="
echo " "
echo " "

#############################################
####### Make sure the tape is mounted #######
#############################################

if ( "$READ_TAPE" == "YES" ) then

   echo " "
   echo -n "    Please mount a tape in the tape drive. Ready ? <y> "
TRY_AGAIN_10:
   set VALID = ($<)
   if ("$VALID" == "") set VALID = "y"
   switch ("$VALID")
      case Y:
      case y:
         breaksw
      case N:
      case n:
         echo "    Program aborted by user..."
         exit 1
         breaksw
      default:
         echo -n "    Answer y or n : "
         goto TRY_AGAIN_10
         breaksw
   endsw
   if ("$MACHINE" == "IBM") then
      lsdev -c tape -C | grep -i available >&/dev/null
   else
      ls -l /dev/*tape* >&/dev/null
   endif
   if ($status == 0) then
      echo " "
      echo "        The following tape drive(s) are available:"
      echo " "
      set DEF_TAPE = ""
      if ("$MACHINE" == "IBM") then
         foreach DEV ( "`lsdev -c tape -C | grep -i available`" )
            echo "$DEV" | grep 150 >&/dev/null
            if ($status == 0) set DEF_TAPE = "$DEV" 
            echo "            $DEV"
         end
      else
         foreach DEV ( "`ls /dev/*tape*`")
            if ("$DEV" == "/dev/tape") set DEF_TAPE = "$DEV"
            echo "            $DEV"
         end
      endif
      if ("$DEF_TAPE" == "") set DEF_TAPE = "$DEV"
      set DEF_TAPE = ($DEF_TAPE)
      if ("$MACHINE" == "IBM") then
         set DEF_TAPE = "/dev/${DEF_TAPE[1]}.4"
      endif
      echo " "
      echo -n "    Enter tape drive name: <${DEF_TAPE}> "
   else
      echo " "
      echo "        No tape drive available on this machine."
      echo " "
      echo -n "    Enter remote tape drive name: "
   endif
   set tapename=($<)
   if ( "$tapename" == "" ) then
      set TAPEDEVICE = "$DEF_TAPE"
   else
      set TAPEDEVICE = "$tapename"
   endif
   if ( "$TAPEDEVICE" == "" ) then
      echo "    Program aborted by user..."
      echo " "
      exit 1
   endif

endif

echo " "
echo "    Installation started, please wait ..."
echo " "

############################################
###### Validate accounts & group cae #######
############################################

echo " "
echo " "
echo ">>> Checking simex & save accounts and group cae..."
#
cd /etc
set simex = "y"
set save  = "y"
set cae   = "y"
#
grep cae group > &/dev/null
if ( $status != 0 ) then
   if ("$MACHINE" == "IBM") then
      set cae = "n"
   else if ("$MACHINE" == "SGI") then
      echo ">>> Saving /etc/group to /etc/group.save ..."
      cp group group.save
      chmod a+w group
      echo 'cae::500:' >> group
      chmod a-w group
   endif
endif
#
grep simex /etc/passwd > &/dev/null
if ( $status != 0 ) then
   if ("$MACHINE" == "IBM") then
      set simex = "n"
   else if ("$MACHINE" == "SGI") then
      echo ">>> Saving /etc/passwd to /etc/passwd.save ..."
      cp passwd passwd.save
      chmod a+w passwd
      echo 'simex::201:500:SIMex-PLUS:'$BASEDIR/ship':/bin/csh' >> passwd
      chmod a-w passwd      
   endif
endif
#
grep save /etc/passwd > &/dev/null
if ( $status != 0 ) then
   if ("$MACHINE" == "IBM") then
      set save = "n"
   else if ("$MACHINE" == "SGI") then
      echo ">>> Saving /etc/passwd to /etc/passwd.save..."
      if ! ( -e passwd.save ) cp passwd passwd.save
      chmod a+w passwd
      echo 'save::200:500:save::' >> passwd
      chmod a-w passwd      
   endif
endif
#
if ( "$simex" == "n" || "$save" == "n" || "$cae" == "n" ) then
   echo " "   
   echo ">>> The following accounts and/or group are missing: ***"
   if ( "$simex" == "n" ) echo "      - Account: simex"
   if ( "$save" == "n" ) echo  "      - Account: save"
   if ( "$cae" == "n" ) echo   "      - Group: cae"
   echo ">>> Please create them, and retry the installation afterwards."
   exit 1
endif

#!===========================================================================
#!===========================================================================
#!  SET SYSTEM PARAMETERS ON SGI FILES
#!===========================================================================
#!===========================================================================

if ( "$MACHINE" == "SGI" ) then

   echo ">>> Checking the system parameters ..."

#####################################################
###### Check the /usr/sysgen/master.d/sem file ######
#####################################################

   cd /usr/sysgen/master.d
   set SEM_CHANGED = "NO"
   set SEMMAP_LINE = (`grep '#define SEMMAP' sem`)
   set SEMMNI_LINE = (`grep '#define SEMMNI' sem`)
   set SEMMNS_LINE = (`grep '#define SEMMNS' sem`)
   set SEMMNU_LINE = (`grep '#define SEMMNU' sem`)
   set SEMMSL_LINE = (`grep '#define SEMMSL' sem`)
   if ! ( ("$SEMMAP_LINE[3]" == "250") && \
          ("$SEMMNI_LINE[3]" == "500") && \
          ("$SEMMNS_LINE[3]" == "500") && \
          ("$SEMMNU_LINE[3]" == "500") && \
          ("$SEMMSL_LINE[3]" == "500") ) then
      echo " >> Increase the number of semaphores ..."
      echo "  > Save /usr/sysgen/master.d/sem in /usr/sysgen/master.d/sem.save ..."
      cp sem sem.save
      sed s/"$SEMMAP_LINE"/"$SEMMAP_LINE[1] $SEMMAP_LINE[2] 250"/ sem   > TEMP1
      sed s/"$SEMMNI_LINE"/"$SEMMNI_LINE[1] $SEMMNI_LINE[2] 500"/ TEMP1 > TEMP2
      sed s/"$SEMMNS_LINE"/"$SEMMNS_LINE[1] $SEMMNS_LINE[2] 500"/ TEMP2 > TEMP3
      sed s/"$SEMMNU_LINE"/"$SEMMNU_LINE[1] $SEMMNU_LINE[2] 500"/ TEMP3 > TEMP4
      sed s/"$SEMMSL_LINE"/"$SEMMSL_LINE[1] $SEMMSL_LINE[2] 500"/ TEMP4 > TEMP5
      chmod a+w sem
      mv TEMP5 sem
      set SEM_CHANGED = "YES"
      chmod a-w sem
      rm -f TEMP1 TEMP2 TEMP3 TEMP4 TEMP5 >&/dev/null
   endif

#####################################################
###### Check the /usr/sysgen/master.d/mem file ######
#####################################################

   set MEM_CHANGED = "NO"
   grep 8192 mem >&/dev/null
   if ($status != 0) then
      echo " >> Set the address for the real time clock ..."
      echo "  > Save /usr/sysgen/master.d/mem in /usr/sysgen/master.d/mem.save ..."
      cp mem mem.save
      set CPU_IP = "`uname -m`"
      switch ("$CPU_IP")
         case "IP4" :
            set PT_CLOCK_LINE = "        { 8192, 0xbfb40000, },"
            breaksw
         case "IP5" :
         case "IP7" :
         case "IP17":
            set PT_CLOCK_LINE = "        { 8192, 0xbf600000, },"
            breaksw
         case "IP6" :
            set PT_CLOCK_LINE = "        { 8192, PHYS_TO_K1(0x1fb40000), },"
            breaksw
         case "IP12" :
            set PT_CLOCK_LINE = "        { 8192, PHYS_TO_K1(0x1fb80000), },"
            breaksw
         default:
            echo "  > New type of cpu on this machine."
            echo "  > Please contact OSU representative to set the address of"
            echo "  > the real time clock."
      endsw
      set DEST_LINE = "`grep -n '{ 0, 0 }' mem | cut -d':' -f1`"
      if ("$DEST_LINE" != "") then
         @ DEST_LINE = $DEST_LINE
         @ DEST_LINE --
         sed -n "1,$DEST_LINE p" mem  > TMP
         echo "$PT_CLOCK_LINE"       >> TMP
         @ DEST_LINE ++
         sed -n "$DEST_LINE,$ p" mem >> TMP
         chmod a+w mem
         mv TMP mem
         set MEM_CHANGED = "YES"
         chmod a-w mem
      else
         echo "  > Cannot locate insertion point."
         echo "  > Please contact OSU representative to set the address of"
         echo "  > the real time clock."
      endif
   endif

#####################################
###### Regenerating the kernel ######
#####################################

   cd /
   if ( ("$SEM_CHANGED" == "YES") || ("$MEM_CHANGED" == "YES") ) then
      if ! ( -e /unix.install ) then
         echo " >> Regenarating new kernel ..."
         echo "  > Save /unix in /unix.save ..."
         cp /unix /unix.save
         lboot -u /unix.install
      endif
   endif

####################################
###### Check /etc/sys_id file ######
####################################

   cd /etc
   if ("$NODE" != "") then
      grep -i "$NODE" sys_id >&/dev/null
      if ($status != 0) then
         echo " >> Set the hostname ..."
         echo "  > Save /etc/sys_id in /etc/sys_id.save"
         cp sys_id sys_id.save
         echo "$NODE" > sys_id
      endif
   endif

###################################
###### Check /etc/cshrc file ######
###################################

   grep TRAP_FPE cshrc >&/dev/null
   if ($status != 0) then
      echo " >> Set the floating point exception handler ..."
      echo "  > Save /etc/cshrc in /etc/cshrc.save"
      cp cshrc cshrc.save
      echo " "                                           >> cshrc
      echo "#  Set the floating point exception handler" >> cshrc
      echo 'setenv TRAP_FPE "ALL=DEFAULT"'               >> cshrc
      echo " "                                           >> cshrc
   endif

################################################
###### Check /etc/rc2.d/S90caeconfig file ######
################################################

   cd rc2.d
   if ! ( -e S90caeconfig ) then
      echo " >> Set the automatic starting of CAE processes at boot time ..."
      echo "  > Link $BASEDIR/caeconfig to /etc/rc2.d/S90caeconfig ..."
      ln -s "$BASEDIR/caeconfig" S90caeconfig
   endif

########################################
###### Check swap space partition ######
########################################

   if ! ( -d /debug ) then
      echo " >> Initialize swap space partition ..."
      echo "  > Creating /debug directory ..."
      mkdir /debug
   endif

##################################
###### Check base directory ######
##################################

   if ("$INSTAL_DIR1" == "YES") then
      cd /usr
      echo ">>> Creating /usr/$BASEDIR:t directory ..."
      mkdir $BASEDIR:t
      echo ">>> Linking /usr/$BASEDIR:t to /$BASEDIR:t ..."
      ln -s /usr/$BASEDIR:t  /$BASEDIR:t 
   endif
   if ("$INSTAL_DIR2" == "YES") then
      cd /usr
      echo ">>> Creating /usr/$BASEDIR2:t directory ..."
      mkdir $BASEDIR2:t
      echo ">>> Linking /usr/$BASEDIR2:t to /$BASEDIR2:t ..."
      ln -s /usr/$BASEDIR2:t  /$BASEDIR2:t
   endif
#
endif

#!===========================================================================
#!===========================================================================
#!  CREATE DIRECTORY STRUCTURE AND RESTORE FILES
#!===========================================================================
#!===========================================================================

###################################################
####### Creating CAELIB directory structure #######
###################################################

if ( ("$READ_TAPE" == "YES") && ("$MACHINE" == "IBM") ) then

   echo ">>> Creating CAELIB directory structures ..."

   cd $BASEDIR
   chown -f simex.cae .
   foreach dir ( simex_plus install lib snag caetest atg atm cfutil if \
                 lp master mtp ovp pg progen rautil sound voice wxr lock )
      if ( ! -d $dir ) mkdir $dir
      chmod 775 $dir > &/dev/null
      chmod go-s $dir > &/dev/null
      chown -Rf simex.cae $dir
   end
   chown -Rf save.cae simex_plus

   cd simex_plus
   foreach dir ( caelib database element enter support work )
      if ( ! -d $dir ) mkdir $dir
      chmod 775 $dir > &/dev/null
      chmod go-s $dir > &/dev/null
      chown -Rf simex.cae $dir
   end
   chown -Rf save.cae caelib database element
   chown -Rf root.system support
   chmod g+w caelib
   chmod g-w support
   chmod a+w enter work
   switch ("$USAGE")
      case "HOST_FS":
      case "HOST_RG":
      case "HOST_FS":
      case "IF_MAST":
         chmod g-w database element
         breaksw
      case "IF_SLAV":
         chmod g+w database element
         breaksw
   endsw

   cd ../lock
   foreach dir ( efs nfs )
      if ( ! -d $dir ) mkdir $dir
      chmod 775 $dir > &/dev/null
      chmod go-s $dir > &/dev/null
      chown -Rf simex.cae $dir
   end

   cd $BASEDIR2
   chown -f simex.cae .
   foreach dir ( log rap ship )
      if ( ! -d $dir ) mkdir $dir
      chmod 775 $dir > &/dev/null
      chmod go-s $dir > &/dev/null
      chown -Rf simex.cae $dir
   end

########################################
####### Restore all CAELIB files #######
########################################

   echo ">>> Changing directory to $BASEDIR/install ..."
   cd $BASEDIR/install
   echo ">>> Restoring files from tape ..."
   tar -xpvf "$TAPEDEVICE"

##########################################################
####### Save already existing configuration files ########
##########################################################

   echo ">>> Check for existing files ..."
   if ( -e $BASEDIR/caeconfig ) then
      echo "  > Saved $BASEDIR/caeconfig             in $BASEDIR/caeconfig.save"
      cp              $BASEDIR/caeconfig                $BASEDIR/caeconfig.save
   endif
   if ( -e $BASEDIR2/ship/.cshrc ) then
      echo "  > Saved $BASEDIR2/ship/.cshrc          in $BASEDIR2/ship/.cshrc.save"
      cp              $BASEDIR2/ship/.cshrc             $BASEDIR2/ship/.cshrc.save
   endif
   if ( -e $BASEDIR2/ship/.login ) then
      echo "  > Saved $BASEDIR2/ship/.login          in $BASEDIR2/ship/.login.save"
      cp              $BASEDIR2/ship/.login             $BASEDIR2/ship/.login.save
   endif
   if ( -e $BASEDIR2/ship/.emacs ) then
      echo "  > Saved $BASEDIR2/ship/.emacs          in $BASEDIR2/ship/.emacs.save"
      cp              $BASEDIR2/ship/.emacs             $BASEDIR2/ship/.emacs.save
   endif
   if ( -e $BASEDIR2/ship/.rhosts ) then
      echo "  > Saved $BASEDIR2/ship/.rhosts          in $BASEDIR2/ship/.rhosts.save"
      cp              $BASEDIR2/ship/.rhosts             $BASEDIR2/ship/.rhosts.save
   endif
   if ( -e /.cshrc ) then
      echo "  > Saved /.cshrc                    in /.cshrc.save"
      cp              /.cshrc                       /.cshrc.save
   endif
   if ( -e /.login ) then
      echo "  > Saved /.login                    in /.login.save"
      cp              /.login                       /.login.save
   endif

endif

##########################################################
####### Install files outside of INSTALL directory #######
##########################################################

if ( ("$READ_TAPE" == "YES") && ("$MACHINE" == "IBM") ) then

   echo ">>> Install files in CAELIB directory structure ..."

   if ( -e /tmp/ethernet_location.dat ) then
      echo "  > Created $BASEDIR/install/ethernet_location.dat"
      mv /tmp/ethernet_location.dat $BASEDIR/install
   endif

   cd /usr
   if ( ! -d local ) mkdir local
   cd local
   if ( ! -d bin   ) mkdir bin
   cd $BASEDIR/lib
   if ( ! -d lisp  ) mkdir lisp
   cd lisp
   if ( ! -d term  ) mkdir term
   cd /etc
   if ( ! -d mfg   ) mkdir mfg
   cd $BASEDIR/install

   if ( ! -e /usr/local/net_config.dat ) then
      echo "  > Moving net_config.dat      to /usr/local"
      cp             ./net_config.dat         /usr/local
   endif

   echo "  > Moving ticker              to /usr/local/bin"
   cp             ./ticker.exe             /usr/local/bin/ticker
   echo "  > Moving cae_entdd           to /etc/drivers"
   cp             ./cae_entdd              /etc/drivers
   echo "  > Moving cae_sadd            to /etc/drivers"
   cp             ./cae_sadd               /etc/drivers
   echo "  > Moving rc.preload          to /etc/mfg"
   cp             ./rc.preload             /etc/mfg

   echo "  > Moving caeconfig           to $BASEDIR"
   cp             ./caeconfig              $BASEDIR
   echo "  > Moving logicals            to $BASEDIR"
   cp             ./logicals.exe           $BASEDIR/logicals

   echo "  > Moving ata_group.dat.1     to $BASEDIR/snag"
   cp             ./ata_group.dat.1        $BASEDIR/snag
   echo "  > Moving long_format.dat.1   to $BASEDIR/snag"
   cp             ./long_format.dat.1      $BASEDIR/snag
   echo "  > Moving short_format.dat.1  to $BASEDIR/snag"
   cp             ./short_format.dat.1     $BASEDIR/snag
   echo "  > Moving snag_print.com.1    to $BASEDIR/snag"
   cp             ./snag_print.com.1       $BASEDIR/snag
   echo "  > Moving std_log.sng         to $BASEDIR/snag"
   cp             ./std_log.sng            $BASEDIR/snag

   echo "  > Moving pra1.hlp            to $BASEDIR/progen"
   cp             ./pra1.hlp               $BASEDIR/progen
   echo "  > Moving pra2.hlp            to $BASEDIR/progen"
   cp             ./pra2.hlp               $BASEDIR/progen
   echo "  > Moving prb1.hlp            to $BASEDIR/progen"
   cp             ./prb1.hlp               $BASEDIR/progen
   echo "  > Moving prb2.hlp            to $BASEDIR/progen"
   cp             ./prb2.hlp               $BASEDIR/progen
   echo "  > Moving prc1.hlp            to $BASEDIR/progen"
   cp             ./prc1.hlp               $BASEDIR/progen
   echo "  > Moving prc2.hlp            to $BASEDIR/progen"
   cp             ./prc2.hlp               $BASEDIR/progen
   echo "  > Moving prd1.hlp            to $BASEDIR/progen"
   cp             ./prd1.hlp               $BASEDIR/progen
   echo "  > Moving prd2.hlp            to $BASEDIR/progen"
   cp             ./prd2.hlp               $BASEDIR/progen
   echo "  > Moving pro.edx             to $BASEDIR/progen"
   cp             ./pro.edx                $BASEDIR/progen

   echo "  > Moving adisp.a             to $BASEDIR/lib"
   cp             ./adisp.a                $BASEDIR/lib
   echo "  > Moving cae_entx.exp        to $BASEDIR/lib"
   cp             ./cae_entx.exp           $BASEDIR/lib
   echo "  > Moving cae_sax.exp         to $BASEDIR/lib"
   cp             ./cae_sax.exp            $BASEDIR/lib
   echo "  > Moving cae_syscall.exp     to $BASEDIR/lib"
   cp             ./cae_syscall.exp        $BASEDIR/lib
   echo "  > Moving caeioctl.exp        to $BASEDIR/lib"
   cp             ./caeioctl.exp           $BASEDIR/lib
   echo "  > Moving cdbmap_cdbsrv.o     to $BASEDIR/lib"
   cp             ./cdbmap_cdbsrv.o        $BASEDIR/lib
   echo "  > Moving common.a            to $BASEDIR/lib"
   cp             ./common.a               $BASEDIR/lib
   echo "  > Moving cts_server.a        to $BASEDIR/lib"
   cp             ./cts_server.a           $BASEDIR/lib
   echo "  > Moving ctsdisp.a           to $BASEDIR/lib"
   cp             ./ctsdisp.a              $BASEDIR/lib
   echo "  > Moving disp.a              to $BASEDIR/lib"
   cp             ./disp.a                 $BASEDIR/lib
   echo "  > Moving fakentry.a          to $BASEDIR/lib"
   cp             ./fakentry.a             $BASEDIR/lib
   echo "  > Moving libcae.a            to $BASEDIR/lib"
   cp             ./libcae.a               $BASEDIR/lib
   echo "  > Moving libcae.h            to $BASEDIR/lib"
   cp             ./libcae.h               $BASEDIR/lib
   echo "  > Moving libcts.a            to $BASEDIR/lib"
   cp             ./libcts.a               $BASEDIR/lib
   echo "  > Moving libdmc.a            to $BASEDIR/lib"
   cp             ./libdmc.a               $BASEDIR/lib
   echo "  > Moving libeas.a            to $BASEDIR/lib"
   cp             ./libeas.a               $BASEDIR/lib
   echo "  > Moving liblv.a             to $BASEDIR/lib"
   cp             ./liblv.a                $BASEDIR/lib
   echo "  > Moving libvt100.a          to $BASEDIR/lib"
   cp             ./libvt100.a             $BASEDIR/lib
   echo "  > Moving pfudbg.a            to $BASEDIR/lib"
   cp             ./pfudbg.a               $BASEDIR/lib
   echo "  > Moving pfuopt.o            to $BASEDIR/lib"
   cp             ./pfuopt.o               $BASEDIR/lib
   echo "  > Moving system.a            to $BASEDIR/lib"
   cp             ./system.a               $BASEDIR/lib
   echo "  > Moving users.a             to $BASEDIR/lib"
   cp             ./users.a                $BASEDIR/lib

   echo "  > Moving COPYING             to $BASEDIR/lib/lisp"
   cp             ./COPYING                $BASEDIR/lib/lisp
   echo "  > Moving DOC-18.57aix.hlp    to $BASEDIR/lib/lisp"
   cp             ./DOC-18.57aix.hlp       $BASEDIR/lib/lisp
   echo "  > Moving bytecomp.elc        to $BASEDIR/lib/lisp"
   cp             ./bytecomp.elc           $BASEDIR/lib/lisp
   echo "  > Moving c-fill.elc          to $BASEDIR/lib/lisp"
   cp             ./c-fill.elc             $BASEDIR/lib/lisp
   echo "  > Moving dired.elc           to $BASEDIR/lib/lisp"
   cp             ./dired.elc              $BASEDIR/lib/lisp
   echo "  > Moving keypad.elc          to $BASEDIR/lib/lisp"
   cp             ./keypad.elc             $BASEDIR/lib/lisp
   echo "  > Moving rect.elc            to $BASEDIR/lib/lisp"
   cp             ./rect.elc               $BASEDIR/lib/lisp
   echo "  > Moving server.elc          to $BASEDIR/lib/lisp"
   cp             ./server.elc             $BASEDIR/lib/lisp
   echo "  > Moving shell.elc           to $BASEDIR/lib/lisp"
   cp             ./shell.elc              $BASEDIR/lib/lisp
   echo "  > Moving time.elc            to $BASEDIR/lib/lisp"
   cp             ./time.elc               $BASEDIR/lib/lisp

   echo "  > Moving vt100.el            to $BASEDIR/lib/lisp/term"
   cp             ./vt100.el               $BASEDIR/lib/lisp/term
   echo "  > Moving vt125.el            to $BASEDIR/lib/lisp/term"
   cp             ./vt125.el               $BASEDIR/lib/lisp/term
   echo "  > Moving vt200.el            to $BASEDIR/lib/lisp/term"
   cp             ./vt200.el               $BASEDIR/lib/lisp/term
   echo "  > Moving vt220.el            to $BASEDIR/lib/lisp/term"
   cp             ./vt220.el               $BASEDIR/lib/lisp/term
   echo "  > Moving vt240.el            to $BASEDIR/lib/lisp/term"
   cp             ./vt240.el               $BASEDIR/lib/lisp/term

   echo "  > Moving caelib_sup.com      to $BASEDIR/simex_plus/support"
   cp             ./caelib_sup.com         $BASEDIR/simex_plus/support
   echo "  > Moving fse_sup.com         to $BASEDIR/simex_plus/support"
   cp             ./fse_sup.com            $BASEDIR/simex_plus/support
   echo "  > Moving sse_gbl.com         to $BASEDIR/simex_plus/support"
   cp             ./sse_gbl.com            $BASEDIR/simex_plus/support

   echo "  > Moving .celdbxinit         to $BASEDIR2/log"
   cp             ./.celdbxinit            $BASEDIR2/log

   echo "  > Moving .cshrc              to /"
   cp             ./.cshrc_root            /.cshrc
   echo "  > Moving .login              to /"
   cp             ./.login_root            /.login

   echo "  > Moving .cshrc              to $BASEDIR2/ship"
   cp             ./.cshrc                 $BASEDIR2/ship/.cshrc
   echo "  > Moving .login              to $BASEDIR2/ship"
   cp             ./.login                 $BASEDIR2/ship/.login
   echo "  > Moving .emacs              to $BASEDIR2/ship"
   cp             ./.emacs                 $BASEDIR2/ship
   echo "  > Moving .rhosts             to $BASEDIR2/ship"
   cp             ./.rhosts                $BASEDIR2/ship

else if ( ("$READ_TAPE" == "YES") && ("$MACHINE" == "SGI") ) then

########################################
####### Restore all CAELIB files #######
########################################

   echo ">>> Configuring device driver for tape drive ..."
   cd /etc
   MAKEDEV tape >&/dev/null
   echo ">>> Changing directory to $BASEDIR ..."
   cd $BASEDIR

   echo ">>> Creating CAELIB directory structures ..."
   echo ">>> Restoring files from tape ..."
   tar -xpvf "$TAPEDEVICE"

endif

if ( "$MACHINE" == "SGI" ) then

   cd /usr
   if ! ( -d local ) mkdir local
   cd local
   if ! ( -d bin   ) mkdir bin

   cd $BASEDIR/install
   if ( ! -e /usr/local/net_config.dat ) then
      echo "  > Moving net_config.dat      to /usr/local"
      cp             ./net_config.dat         /usr/local
   endif

   if ( ! -e /usr/local/bin/ticker ) then
      echo "  > Moving ticker              to /usr/local/bin"
      cp             ./ticker.exe             /usr/local/bin/ticker
   endif

endif

#!===========================================================================
#!===========================================================================
#!  SETUP OWNERSHIPS
#!===========================================================================
#!===========================================================================

test -d $BASEDIR/install
if ( $status != 0 ) then
   echo " "
   echo ">>> Directory $BASEDIR/install does not exist..."
   echo ">>> CAELIB directory structure needs to be created to go further."
   echo ">>> Restart the installation procedure by answering YES to: "
   echo ">>>    Does CAELIB directory structure have to be created ? <y>"
   echo " "
   exit 1
endif

echo ">>> Checking ownership in CAELIB directory structures ..."

chown root.sys $BASEDIR/caeconfig
chown root.sys /usr/local/net_config.dat
chown root.sys /usr/local/bin/ticker
chown root.sys /.cshrc
chown root.sys /.login

if ( "$MACHINE" == "IBM" ) then
   chown root.sys /etc/drivers/cae_entdd
   chown root.sys /etc/mfg/rc.preload
   chmod a+x         /etc/mfg/rc.preload
else if ( "$MACHINE" == "SGI" ) then
   chmod a+w /etc/utmp >&/dev/null
endif

cd $BASEDIR
chown -f simex.cae .
if ( "$MACHINE" == "IBM" ) then
   foreach dir ( simex_plus install lib snag caetest atg atm cfutil if \
                 lp master mtp ovp pg progen rautil sound voice wxr )
      chmod 775 $dir > &/dev/null
      chmod go-s $dir > &/dev/null
      chown -Rf simex.cae $dir
   end
else if ( "$MACHINE" == "SGI" ) then
   foreach dir ( simex_plus ship install lib snag ifutil lp pg log )
      chmod 775 $dir > &/dev/null
      chmod go-s $dir > &/dev/null
      chown -Rf simex.cae $dir
   end
endif
chown save.cae install/simex.exe
chmod ug+s install/simex.exe
chown -Rf save.cae simex_plus
#
cd simex_plus
foreach dir ( caelib database element enter support work )
   chmod 775 $dir > &/dev/null
   chmod go-s $dir > &/dev/null
   chown -Rf simex.cae $dir
end
chown -Rf save.cae database element
chown -f  save.cae caelib
chown -Rf root.sys support
chmod g+w caelib
chmod g-w support
chmod a+w enter work
switch ("$USAGE")
   case "HOST_FS":
   case "HOST_RG":
   case "HOST_FS":
   case "IF_MAST":
      chmod g-w database element
      breaksw
   case "IF_SLAV":
      chmod g+w database element
      breaksw
endsw
#
cd ../lock
foreach dir ( efs nfs )
   chmod 775 $dir > &/dev/null
   chmod go-s $dir > &/dev/null
   chown -Rf simex.cae $dir
end
#
cd $BASEDIR2
chown -f simex.cae .
foreach dir ( log rap ship )
   chmod 775 $dir > &/dev/null
   chmod go-s $dir > &/dev/null
   chown -Rf simex.cae $dir
end

#!===========================================================================
#!===========================================================================
#!  Replace predefined strings in given files
#!===========================================================================
#!===========================================================================

cd $BASEDIR/install

if ( ("$INSTAL_WORK" == "YES") || \
     ("$INSTAL_ROSE" == "YES") || \
     ("$USAGE" == "IF_SLAV") ) then

# Replace "demo" in the name of the given files

   foreach FILE ( `ls demo*` )
      set FILE2 = "${CDB}`echo $FILE | cut -c5-`"
      echo ">>> Renaming $FILE to $FILE2 ..."
      cp "$FILE" "$FILE2" >&/dev/null
      chown simex.cae "$FILE2"
   end

# Replace <ship> by the name of the CDB

   echo ">>> Searching all files for <ship> string... Please wait... "
   foreach FILE ( `ls ${CDB}*.cdb.* ${CDB}*.hdr.* ${CDB}*.scb.* ${CDB}*.sex.*                        ${CDB}cfio.for.* ${CDB}scal.for.* *vsio.for.*                                  *p*c*init.for.* cae_report_err.for.* dfcsafe.for.*                             rand.for.* *.inc.* init1.cts* pgcinit.dat* `)

      set NEWFILE = "`./revl.exe $FILE +`"
      echo "  > Replacing <ship> by $CDB in $NEWFILE ..."
      sed s/"<ship>"/"$CDB"/ "$FILE" > TEMP
      mv TEMP "$NEWFILE"
      chown simex.cae "$NEWFILE"
   end

# Case of sitelogs file

   echo ">>> Updating sitelogs file ..."
   if ( -e $BASEDIR/sitelogs ) then
      echo "  > Saved $BASEDIR/sitelogs in $BASEDIR/sitelogs.save"
      cp              $BASEDIR/sitelogs    $BASEDIR/sitelogs.save
   endif
   echo "  > Moving sitelogs to $BASEDIR"
   cp             ./sitelogs    $BASEDIR
   chown root.sys $BASEDIR/sitelogs

   cd $BASEDIR
   sed s/"<cdb>"/"$CDB"/   sitelogs > TEMP
   mv TEMP sitelogs
   sed s/"<host>"/"$HOST"/ sitelogs > TEMP
   mv TEMP sitelogs
   sed s/"<ship>"/"$SHIP"/ sitelogs > TEMP
   mv TEMP sitelogs
   sed s/"<cpu>"/"$CPU"/   sitelogs > TEMP
   mv TEMP sitelogs
   sed s/"<max>"/"$MAX"/   sitelogs > TEMP
   mv TEMP sitelogs
   sed s/"<tab>"/"$TAB"/   sitelogs > TEMP
   mv TEMP sitelogs
   sed s/"<node>"/"$NODE"/ sitelogs > TEMP
   mv TEMP sitelogs
   sed s/"<usg>"/"$USAGE"/ sitelogs > TEMP
   mv TEMP sitelogs
   if ("$BASEDIR2" != "/cae1") then
      sed s/"\/cae1"/"\$BASEDIR2"/ sitelogs > TEMP
      mv TEMP sitelogs
   endif
   if ("$BASEDIR" != "/cae") then
      sed s/"\/cae"/"\$BASEDIR"/ sitelogs > TEMP
      mv TEMP sitelogs
   endif

endif

if ("$INSTAL_TCP" == "YES") then

# Case of hosts file

   echo ">>> Updating hosts file ..."
   cd /etc
   if ( -e /etc/hosts ) then
      echo "  > Saved /etc/hosts in /etc/hosts.save"
      cp              /etc/hosts    /etc/hosts.save
   endif
   echo "  > Moving hosts to /etc"
   cp $BASEDIR/install/hosts .
   chown root.sys hosts
   chmod ug+w     hosts
   switch ("$USAGE")
      case "HOST_FS":
      case "HOST_IF":
         sed s/"# internet0"/"$NET   $HOST"/ hosts > TEMP
         mv TEMP hosts
      case "IF_MAST":
      case "IF_SLAV":
         set FIELD_3 = "`echo $NET | cut -f3 -d'.'`"
         set FIELD_4 = "`echo $NET | cut -f4 -d'.'`"
         sed s/"# internet1"/"192.${FIELD_3}.${FIELD_4}.1   ${HOST}"/ hosts > TEMP
         mv TEMP hosts
         @ J     = 1
         @ I_MAX = $MAX
         while ( $J <= $MAX )
            @ K = $J - 1
            @ L = $J + 1
            sed s/"# internet${L}"/"192.${FIELD_3}.${FIELD_4}.${L}   ${HOST}${K}"/ hosts > TEMP
            mv TEMP hosts
            @ J++
         end
         breaksw
      case "HOST_RG":
         sed s/"# internet0"/"$NET   $HOST"/ hosts > TEMP
         mv TEMP hosts
         breaksw
   endsw

endif

#!===========================================================================
#!===========================================================================
#!  START TCP/IP
#!===========================================================================
#!===========================================================================

if ("$INSTAL_TCP" == "YES") then

   echo ">>> Starting TCP/IP ..."
   cd $BASEDIR
   if ("$MACHINE" == "IBM") then
      switch ("$USAGE")
         case "HOST_IF"
         case "HOST_FS"
            if ("$DEVICE_DRIVER_IF" != "") then

               mktcpip -h"$HOST" -a"192.${FIELD_3}.${FIELD_4}.1" \
                                 -i"en${DEVICE_DRIVER_IF}" -t'bnc'

               sed s/"#mktcpip"/"      mktcpip -h$HOST -a192.${FIELD_3}.${FIELD_4}.1 -ien${DEVICE_DRIVER_IF} -tbnc"/ caeconfig > TEMP
               mv TEMP caeconfig
               chmod ug+x caeconfig
            endif
         case "HOST_RG"
            if ("$DEVICE_DRIVER_CAE" != "") then

               mktcpip -h"$HOST" -a"$NET" \
                                 -i"en${DEVICE_DRIVER_CAE}" -t'dix'

               sed s/"#mktcpip"/"      mktcpip -h$HOST -a$NET -ien${DEVICE_DRIVER_CAE} -tdix"/ caeconfig > TEMP
               mv TEMP caeconfig
               chmod ug+x caeconfig
            endif
            breaksw
         case "IF_MAST"
         case "IF_SLAV"
            if ("$DEVICE_DRIVER_IF" != "") then

               @ ZZ = $CPU + 1
               mktcpip -h"$NODE" -a"192.${FIELD_3}.${FIELD_4}.${ZZ}" \
                                 -i"en${DEVICE_DRIVER_IF}" -t'bnc'

               sed s/"#mktcpip"/"      mktcpip -h$NODE -a192.${FIELD_3}.${FIELD_4}.${ZZ} -ien${DEVICE_DRIVER_IF} -tbnc"/ caeconfig > TEMP
               mv TEMP caeconfig
               chmod ug+x caeconfig
            endif
            breaksw
      endsw

   endif

endif

#!===========================================================================
#!===========================================================================
#!  INITIALIZE SITE VARIABLES
#!===========================================================================
#!===========================================================================

###########################################
###### Reset logical names partition ######
###########################################

echo ">>> Reset logical names partition ..."

set SIMEX_BIN  = "`$BASEDIR/logicals -t cae_caelib_path`"
echo "reset" > /tmp/dummy
echo "q"    >> /tmp/dummy
$SIMEX_BIN/lognam       < /tmp/dummy >&/dev/null
$BASEDIR/install/lognam.exe < /tmp/dummy >&/dev/null
rm /tmp/dummy >&/dev/null

###########################################
###### Read back logical names value ######
###########################################

set SIMEX_CDB  = "`$BASEDIR/logicals -t cae_cdbname`"
set SIMEX_DIR  = "`$BASEDIR/logicals -t cae_simex_plus`"
set SIMEX_HOST = "$MACHINE"
set SIMEX_SHIP = "`$BASEDIR/logicals -t cae_ship`"
set SIMEX_USAG = "$USAGE"

set SIMEX_UNIK = "`$BASEDIR/install/pid.exe`"
set SIMEX_TEMP = "$SIMEX_DIR/work/init_${SIMEX_UNIK}.tmp.1"
set SIMEX_LINK = "bootlnks${SIMEX_UNIK}"

cd $BASEDIR/install

#!===========================================================================
#!===========================================================================
#!  BUILD CAELIB_WORK CONFIGURATION
#!===========================================================================
#!===========================================================================

if ("$INSTAL_CAELIB" == "YES") then

   echo ">>> Installing CAELIB_WORK configuration..."
   mkdir "$SIMEX_LINK"
   onintr ABORT
   cd "${BASEDIR}/install/${SIMEX_LINK}"
   foreach EXE (`ls $BASEDIR/install/*.exe`)
     set BASE = "$EXE:t"
     ln -s $EXE $BASE:r
   end
   setenv PATH "`echo ${BASEDIR}/install/${SIMEX_LINK}`:`echo ${PATH}`"
   setenv cae_smp_init "`echo $BASEDIR`/install/std_log.smp"
   chown save.cae "$BASEDIR/install/simex.exe"
   chmod ug+s "$BASEDIR/install/simex.exe"

   echo 'set session CAELIB_INIT'                              >$SIMEX_TEMP
   echo 'create configuration/environment=CAELIB caelib_work' >>$SIMEX_TEMP
   switch ("$SIMEX_USAG")
      case HOST_FS:
      case HOST_IF:
      case HOST_RG:
         echo 'execute cae_install:caelho_ins.smp'            >>$SIMEX_TEMP
         breaksw
      case IF_MAST:
      case IF_SLAV:
         echo 'execute cae_install:caelif_ins.smp'            >>$SIMEX_TEMP
         breaksw
   endsw
   echo 'set session/comment="Prepared the CAELIB"'           >>$SIMEX_TEMP
   echo 'build root'                                          >>$SIMEX_TEMP
   echo 'exit'                                                >>$SIMEX_TEMP
   simex < "$SIMEX_TEMP"
   rm "$SIMEX_TEMP"
   chown root.sys ${SIMEX_DIR}/element/cts_atg.exe.*  >&/dev/null
   chmod ug+s ${SIMEX_DIR}/element/cts_atg.exe.*      >&/dev/null
   chmod ug+s ${SIMEX_DIR}/element/simex.exe.*        >&/dev/null
   rm -r "$SIMEX_LINK" >&/dev/null

endif

#!===========================================================================
#!===========================================================================
#!  BUILD WORK CONFIGURATION
#!===========================================================================
#!===========================================================================

if ( "$INSTAL_WORK" == "YES" ) then

   echo ">>> Installing WORK configuration..."
   source "`echo $SIMEX_BIN`/std_log.com"
   setenv PATH ".:`echo $SIMEX_BIN`:`echo $PATH`"
   $BASEDIR/logicals -c "cae_cdb" "$BASEDIR/install"
   cd "$BASEDIR/install"
   if ("$MACHINE" == "SGI") cp $BASEDIR/lib/* . >&/dev/null

###############################################
########## run CDBP on cdb file(s) ############
###############################################

   rm ${SIMEX_CDB}*.n*    >&/dev/null
   rm ${SIMEX_CDB}*.s*    >&/dev/null
   rm ${SIMEX_CDB}*.x*    >&/dev/null
   rm ${SIMEX_CDB}*.dat.* >&/dev/null
   rm ${SIMEX_CDB}*.log.* >&/dev/null
   rm ${SIMEX_CDB}*.inf.* >&/dev/null 

   if ("$SIMEX_HOST" == "SGI") then
      cdbp "${SIMEX_CDB}.cdb"
      cdbp "${SIMEX_CDB}1.cdb"
      xmerge "${SIMEX_CDB}" "${SIMEX_CDB}1"
   else if ("$SIMEX_HOST" == "IBM") then
      switch ("$SIMEX_USAG")
         case HOST_IF:
         case HOST_FS:
         case HOST_RG:
            cdbp "${SIMEX_CDB}.cdb"
            cdbp "${SIMEX_CDB}1.cdb"
            cdbp "${SIMEX_CDB}2.cdb"
            xmerge "${SIMEX_CDB}" "${SIMEX_CDB}1" "${SIMEX_CDB}2"
            breaksw
         case IF_MAST:
            cdbp "${SIMEX_CDB}.cdb"
            breaksw
      endsw
   endif

######################################################
########## compile fake1.for for CTS_FAKE ############
######################################################

   if ( "$SIMEX_USAG" != "IF_MAST" ) then
#! ******* HOST_FS, HOST_IF & HOST_RG   (IBM & SGI)
      f4l fake1.for
   endif

###############################################################################
########## define basic structure in SIMex-PLUS and enter CDB files ###########
###############################################################################

   echo 'set session SIMEX_INIT'                                   >$SIMEX_TEMP
   echo "create conf /environment=FSE/contract=$SIMEX_CDB $CONF"  >>$SIMEX_TEMP
   switch ("$SIMEX_USAG")
      case HOST_FS:
         echo 'execute cae_install:fse_ins.smp'                   >>$SIMEX_TEMP
         breaksw
      case HOST_IF:
         echo 'execute cae_install:hoi_ins.smp'                   >>$SIMEX_TEMP
         breaksw
      case HOST_RG:
         echo 'execute cae_install:reg_ins.smp'                   >>$SIMEX_TEMP
         breaksw
      case IF_MAST:
         echo 'execute cae_install:ifs_ins.smp'                   >>$SIMEX_TEMP
         breaksw
   endsw
   echo 'exit'                                                    >>$SIMEX_TEMP
   simex < "$SIMEX_TEMP"
   rm "$SIMEX_TEMP"

############################################################
########## enter the files depending on the CDB ############
############################################################

   switch ("$SIMEX_USAG")
      case HOST_IF:
         fpc sp0c1init.for
         f4l sp0c1init.for
         fpc ap0c1init.for
         f4l ap0c1init.for
         fpc sp0c0init.for
         f4l sp0c0init.for
         fpc ap0c0init.for
         f4l ap0c0init.for
         if ( "$MACHINE" == "IBM" ) then
            fpc ccu_diag.inc
            fpc -noinclude ccubg.for
            f4l ccubg.for
            fpc -noinclude ccufg.for
            f4l ccufg.for
            fpc -noinclude ccufpc.for
            f4l ccufpc.for
            fpc -noinclude simrlog.for
            f4l simrlog.for
            fpc -noinclude cae_report_err.for
            f4l cae_report_err.for
         endif
         breaksw
      case HOST_FS:
         fpc sp0c0init.for
         f4l sp0c0init.for
         fpc ap0c0init.for
         f4l ap0c0init.for
         if ( "$MACHINE" == "IBM" ) then
            fpc ccu_diag.inc
            fpc -noinclude ccubg.for
            f4l ccubg.for
            fpc -noinclude ccufg.for
            f4l ccufg.for
            fpc -noinclude ccufpc.for
            f4l ccufpc.for
            fpc -noinclude simrlog.for
            f4l simrlog.for
            fpc -noinclude cae_report_err.for
            f4l cae_report_err.for
         endif
         breaksw
      case HOST_RG:
         fpc sp0c0init.for
         f4l sp0c0init.for
         fpc ap0c0init.for
         f4l ap0c0init.for
         breaksw
      case IF_MAST:
         fpc ap0c0init.for
         f4l ap0c0init.for
        breaksw
   endsw

   echo 'set session SIMEX_INIT/comment="for dispatcher usage"'  >$SIMEX_TEMP
   echo "set configuration $CONF"                               >>$SIMEX_TEMP
   echo 'set directory cae_install'                             >>$SIMEX_TEMP
   switch ("$SIMEX_USAG")
      case HOST_IF:
         echo 'enter  sp0c1.def     sp0c1.exe'                  >>$SIMEX_TEMP
         echo 'enter  ap0c1.def     ap0c1.exe'                  >>$SIMEX_TEMP
         echo 'enter  sp0c0.def     sp0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  ap0c0.def     ap0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  sp0c1init.for sp0c1.exe'                  >>$SIMEX_TEMP
         echo 'enter  ap0c1init.for ap0c1.exe'                  >>$SIMEX_TEMP
         echo 'enter  sp0c0init.for sp0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  ap0c0init.for ap0c0.exe'                  >>$SIMEX_TEMP
         if ( "$MACHINE" == "IBM" ) then
            echo 'enter  ccubg.for           ccu.exe'           >>$SIMEX_TEMP
            echo 'enter  ccufpc.for          ccu.exe'           >>$SIMEX_TEMP
            echo 'enter  ccufg.for           sp0c0.exe'         >>$SIMEX_TEMP
            echo 'enter  cae_report_err.for  sp0c0.exe'         >>$SIMEX_TEMP
            echo 'define ccufpc.obj          sp0c0.exe'         >>$SIMEX_TEMP
            echo 'enter  simrlog.for         simrlog.exe'       >>$SIMEX_TEMP
            echo 'define ccufpc.obj          simrlog.exe'       >>$SIMEX_TEMP
         endif
         breaksw
      case HOST_FS:
         echo 'enter  sp0c0.def     sp0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  ap0c0.def     ap0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  sp0c0init.for sp0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  ap0c0init.for ap0c0.exe'                  >>$SIMEX_TEMP
         if ( "$MACHINE" == "IBM" ) then
            echo 'enter  ccubg.for           ccu.exe'           >>$SIMEX_TEMP
            echo 'enter  ccufpc.for          ccu.exe'           >>$SIMEX_TEMP
            echo 'enter  ccufg.for           sp0c0.exe'         >>$SIMEX_TEMP
            echo 'enter  cae_report_err.for  sp0c0.exe'         >>$SIMEX_TEMP
            echo 'define ccufpc.obj          sp0c0.exe'         >>$SIMEX_TEMP
            echo 'enter  simrlog.for         simrlog.exe'       >>$SIMEX_TEMP
            echo 'define ccufpc.obj          simrlog.exe'       >>$SIMEX_TEMP
         endif
         breaksw
      case HOST_RG:
         echo 'enter  sp0c0.def     sp0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  ap0c0.def     ap0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  sp0c0init.for sp0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  ap0c0init.for ap0c0.exe'                  >>$SIMEX_TEMP
         breaksw
      case IF_MAST:
         echo 'enter  ap0c0.def     ap0c0.exe'                  >>$SIMEX_TEMP
         echo 'enter  ap0c0init.for ap0c0.exe'                  >>$SIMEX_TEMP
         breaksw
   endsw
#
   if ("$MACHINE" == "IBM") then
      echo 'enter diag1.dat /ent=std_ent.com SAV_FILES'         >>$SIMEX_TEMP
      echo 'enter diag2.dat /ent=std_ent.com SAV_FILES'         >>$SIMEX_TEMP
      echo 'enter diag3.dat /ent=std_ent.com SAV_FILES'         >>$SIMEX_TEMP
      echo 'enter diag4.dat /ent=std_ent.com SAV_FILES'         >>$SIMEX_TEMP
      echo 'enter diag5.dat /ent=std_ent.com SAV_FILES'         >>$SIMEX_TEMP
      echo 'enter diag6.dat /ent=std_ent.com SAV_FILES'         >>$SIMEX_TEMP
   endif
#
   echo 'build root'                                            >>$SIMEX_TEMP
   echo 'exit'                                                  >>$SIMEX_TEMP
   simex < "$SIMEX_TEMP"
   rm "$SIMEX_TEMP"

endif

#!===========================================================================
#!===========================================================================
#!  BUILD ROSE TEMPLATE CONFIGURATION
#!===========================================================================
#!===========================================================================

if ( "$INSTAL_ROSE" == "YES" ) then

   echo ">>> Installing ROSE TEMPLATE ..."
   source "`echo $SIMEX_BIN`/std_log.com"
   setenv PATH ".:`echo $SIMEX_BIN`:`echo $PATH`"
   $BASEDIR/logicals -c "cae_cdb" "$BASEDIR/install"
   cd "$BASEDIR/install"
   if ("$MACHINE" == "SGI") cp $BASEDIR/lib/* .

#
#  call to ROSE installation script should be here!
#

endif

#!===========================================================================
#!===========================================================================

ABORT:

echo ">>>>>>>>>>>>>>>>  CAELIB V13 INSTALLATION TERMINATED  <<<<<<<<<<<<<<<<"
echo "                     `date`"
echo " "
echo "        REBOOT YOUR SYSTEM TO MAKE ALL THE CHANGES AVAILABLE"
echo " "
rm -r "$SIMEX_LINK" >&/dev/null

exit
