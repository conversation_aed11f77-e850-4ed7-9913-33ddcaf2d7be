#/bin/csh -f
onintr inter
echo " "
echo "*** (REV V1.0) ***"
if ($#argv == 0) then
   echo "Usage: rev file1 extension
else if ($#argv == 1 ) then
#   while ($#argv != 0) 
      set NSRC = `revl $argv[1] +`
      set IEV = $status
      if ($IEV <= 1 || $IEV == 6 || $IEV == 9) then
         cp $argv[1] $NSRC
      else
         echo "Could not get revision level for file: $argv[1]"
      endif
#      shift
#   end
endif
exit
# 
# interrupt handling
#
inter:
exit