#! /bin/csh -f
#  $Revision: OLIB V1.2 FEB-1991 | <PERSON>$
#
#     This script helps manage FORTRAN libraries on UNIX
#     based systems.  It performs most common operations
#     like ADD, DELete or LISt member(s) of library.  It
#     allows operations with file names showing a version
#     number.
#
#  Version 1.0: (28-Jan-1991) <PERSON><PERSON>
#
#     - creation
#
#  Version 1.1: (18-Feb-1991) <PERSON><PERSON>
#
#     - modified USAGE error messages.
#
#  Version 1.2: (22-Feb-1991) <PERSON><PERSON>
#
#     - modified LIS command to list entries of the files instead of the
#       files themselves. (changed t for w in 'ar' call)
#
onintr inter
echo " "
echo "*** OLIB Version 1.2 - February 1991 ***"
echo " "
#
set ERRFLAG = 0
if ( $#argv < 2 ) then
    echo "USAGE: olib command libname [ filename ]"
    exit 1
endif
#
#        validate first argument
#
if ( $argv[1] == "del" ) then
    set OP = "del"
else if ( $argv[1] == "lis" ) then
    set OP = "lis"
else if ( $argv[1] == "add" ) then
    set OP = "add"
else
    echo "ERROR: first argument must be 'del', 'lis' or 'add'."
    exit 1
endif
#
#        validate second argument
#
set NLIB = `revl -$argv[2] @a`
set stat = $status
if !( $stat == 0 || $stat == 5 || $stat == 1 || $stat == 6 ) then
    echo $argv[2] "=>" `reverr $stat`
    exit 1
endif
if ( $stat == 5 && $OP != "add" ) then
    echo "ERROR: Library '$argv[2]' does not exist."
    exit 1
endif
set NEWLIB = `revl -$argv[2] +@a`
#
#        list content of library
#
if ( $OP == "lis" ) then
    ar w $NLIB
    if ( $status != 0 ) then
        echo "ERROR: Unable to list content of '$argv[2]'."
        exit 1
    endif
#
#        delete member of library
#
else if ( $OP == "del" ) then
    if ( $#argv == 3 ) then
        set NSRCO = $argv[3]
	set NSRC = $argv[3]:r
	if ( $NSRCO == $NSRC ) set NSRCO = $NSRC.o  # set default
        cp $NLIB $NEWLIB
        ar d $NEWLIB $NSRCO
        if ( $status != 0 ) then
            rm $NEWLIB
            echo "ERROR: Unable to remove '$argv[3]' from '$argv[2]'."
            exit 1
        endif
    else
        echo "USAGE for 'del': olib del libname filename"
        exit 1
    endif
    echo "'$NSRCO:t' deleted from '$NEWLIB:t'"
#
# add member(s) to library (wildcard accepted)
#
else if ( $OP == "add" ) then
    if ( $#argv < 3 ) then
        echo "USAGE for 'add': olib add libname filename [ filename ]"
        exit 1
    endif
#
    while ( $#argv > 2 )
#
        set NOBJ = `revl -$argv[3] @obj`
        set stat = $status
        if ( $stat != 0 ) then
            echo $argv[3] "=>" `reverr $stat`
            set ERRFLAG = 1
            goto next
        endif
        set TEMP = $NOBJ:r
        set NO = $TEMP:r.o
        cp $NOBJ $NO
#
        if ( -e $NLIB ) cp $NLIB $NEWLIB
        ar ru $NEWLIB $NO
        if ( $status != 0 ) then
            echo "ERROR: Unable to add '$argv[3]' to '$NLIB:t'."
            set ERRFLAG = 1
        else
            echo "'$NO:t' added to '$NEWLIB:t'."
        endif
        rm $NO
#
next:
#
        shift
    end
#
endif
#
exit $ERRFLAG
#
#        interrupt handling
#
inter:
if ( -e $NO ) rm $NO
if ( -e $NEWLIB ) rm $NEWLIB
exit 1
