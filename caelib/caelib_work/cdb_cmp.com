#!  /bin/csh -f
#   $Revision: CDB_CMP - Compile a Common Data Base V1.3 (DF) Feb-92$
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#
#   Version 1.2: <PERSON> (25-Jun-91)
#      - added warning about overwrite of cdb spare files
#
#   Version 1.3: <PERSON><PERSON> (28-Feb-92)
#      - remove message about spare file reinitialized to zero
#        because of the new approach to handle spare labels
#      - support -vax option.
#
set FSE_FILE=""
set FSE_OPTS=""
if ($#argv > 0) then
  if (("$argv[1]" == "-sel") || ("$argv[1]" == "-selfmt")) then
    if ($#argv > 1) then
      set FSE_FILE="$argv[2]"
    endif
    set FSE_OPTS="-sel"    
  else if (("$argv[1]" == "-vax") || ("$argv[1]" == "-vaxfmt")) then
    if ($#argv > 1) then
      set FSE_FILE="$argv[2]"
    endif
    set FSE_OPTS="-vax"    
  else
    set FSE_FILE="$argv[1]"
  endif
endif
#
while ("$FSE_FILE" == "")
  echo -n "_Enter cdb source file ? "
  set FSE_FILE="$<"
end
#
set FSE_FILE="`revl -'$FSE_FILE' %cdb`" 
set stat=$status
if ! ($stat == 0 || $stat == 1 || $stat == 6) then
  if ($stat == 5) then
    echo "%FSE-E-FILENOTFOUND, file does not exist."
  else 
    echo "%FSE-E-FILERROR, error on file $FSE_FILE."
    reverr $stat
  endif
  exit
endif
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_ENTER="$SIMEX_DIR/enter"
set SIMEX_WORK="$SIMEX_DIR/work"
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_MAKE=$SIMEX_WORK/cdbm_$FSE_UNIK
#
setenv TARGET "$FSE_MAKE"
unalias cdbp
cdbp </dev/null $FSE_OPTS $FSE_FILE
set stat=$status
unsetenv TARGET
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit $stat
#
set FSE_SAVE=$SIMEX_ENTER/$FSE_FILE:t
if (-e "$FSE_SAVE") rm $FSE_SAVE
setenv fse_source "$FSE_MAKE"
setenv fse_target "$FSE_SAVE"
setenv fse_action "B"
fse_compile
rm $FSE_MAKE
#echo "Warning: the cdb spare files have been reinitialized to zero."
exit
