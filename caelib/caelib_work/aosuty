#!/bin/csh -f
# *************************************************************************
# *                    AOSUTY  for UNIX systems                           *
# *                                                                       *
# *  Version 1.0    Original version           P. Daigle      7 Jan 1991  *
# *  Version 1.1    Added call to Firgen       P. Daigle     16 Dec 1991  *
# *  Version 1.1    SIMex-PLUS environment     P. Daigle      5 Feb 1992  *
# *************************************************************************
#
onintr INTER
#
# Save current user directory
set CURRENT = "`pwd`"
#
# Setup environment variables for AOSUTY
#
set CAE_AOS = "`logicals -t cae_aos`"
source $CAE_AOS/aos_env
#
# Check if AOSUTY is being used
#
clear
unalias rm
set exec_dir = "`logicals -t cae_caelib_path`/"
#
if (-e "$CAE_AOS/aosuty_used.tmp") then
   echo "Sorry, AOSUTY is already being used..."
   echo "Check if the utility is running on another terminal"
   exit
else
   goto ONE_USER
endif
#
ONE_USER:
touch $CAE_AOS/aosuty_used.tmp
#
# Check is AOSUTY executable exists and run it if it does
#
set CMD="${exec_dir}aosmain"
if (-e $CMD) then
   goto NO_EXIT
else
   clear
   echo ""
   echo "    ************************************************ "
   echo "    **             AOSUTY needs help              ** "
   echo "    **                                            ** "
   echo "    **         Can't find aosuty.exe file         ** "
   echo "    **           in executable directory          ** "
   echo "    **           FATAL ERROR - GOODBYE!           ** "
   echo "    **                                            ** "
   echo "    ************************************************ "
   #
   rm $CAE_AOS/aosuty_used.tmp
   exit
endif
#
#
NO_EXIT:
#
#  Call Main Program
#
cd $CAE_AOS
set CMD="${exec_dir}aosmain"
$CMD
if (-e "$CAE_AOS/abort.tmp.1") goto GOODBYE
#
if (-e "$CAE_AOS/exit.tmp.1") goto FINISHED
#
CHECK_NEXT:
if (-e "$CAE_AOS/tsdgen.tmp.1") then
   #
   #  ------------------- TSDGEN ------------------------  
   #
   rm $CAE_AOS/tsdgen.tmp.*
   set CMD = "${exec_dir}tsdgen"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " tsdgen.exe module not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "$CAE_AOS/harmony.tmp.1") then
   #
   #  ------------------- HARMONY -----------------------
   #
   rm $CAE_AOS/harmony.tmp.*
   set CMD = "${exec_dir}harmony"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " Executable harmony file not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "$CAE_AOS/wavegen.tmp.1") then
   #
   #  ------------------- WAVEGEN -----------------------
   #
   rm $CAE_AOS/wavegen.tmp.*
   set CMD = "${exec_dir}wavegen"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " Executable wavegen file not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "$CAE_AOS/tmsgen.tmp.1") then
   #
   #  ------------------- TMSGEN -----------------------
   #
   rm $CAE_AOS/tmsgen.tmp.*
   set CMD = "${exec_dir}tmsgen"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " Executable tmsgen file not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "$CAE_AOS/formgen.tmp.1") then
   #
   #  ------------------- FORMGEN -----------------------
   #
   rm $CAE_AOS/formgen.tmp.*
   set CMD = "${exec_dir}formgen"
   if (-e $CMD) then
      $CMD
   else
      echo " "
      echo "AOSUTY error :"
      echo " Exectuable formgen file module not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "$CAE_AOS/firgen.tmp.1") then
   #
   #  ------------------- FIRGEN  -----------------------
   #
   rm $CAE_AOS/firgen.tmp.*
   set CMD = "${exec_dir}firgen"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " Executable firgen file not found, returned to AOSUTY"
      echo " "
   endif
endif
#
if (-e "$CAE_AOS/exit_ac.tmp.1") then
   rm $CAE_AOS/exit_ac.tmp.*
   clear
   echo " "
   echo "Sound's Good ..."
   goto FINAL_EXIT
else
   goto NO_EXIT
endif
#
FINISHED:
   rm $CAE_AOS/exit.tmp.*
   clear
   echo " "
   echo "Sound's Good ..."
   goto FINAL_EXIT
#
GOODBYE:
   rm $CAE_AOS/abort.tmp.*
   clear
   echo " "
   echo "One of the AOSUTY programs has generated an internal error"
   echo "This error is unrecoverable, something is very wrong."
   echo "Sound's bad..."
   goto FINAL_EXIT
#
ERROR_BRANCH:
   echo " "
   echo "AOSUTY Internal error: "
   echo "This error is unrecoverable, something is wrong."
   echo "Sound's bad..."
   goto FINAL_EXIT
#
INTER:
   echo " "
   echo " You want to abort AOSUTY, OK your the boss. "
   echo " But it sound's bad ..."
   echo " "
#
FINAL_EXIT:
   rm $CAE_AOS/aosuty_used.tmp
   rm $CAE_AOS/*.tmp.*
   rm $CAE_AOS/*.inf.*
   rm $CAE_AOS/*.fmg.*
   cd $CURRENT
#   rm *.FMG*
#   find $USERDIR -size 0 -name '*.tmp.*' -exec rm {} \;
#   find $USERDIR -name '*.inf.*' -exec rm {} \;
#   find $USERDIR -name '*.fmg.*' -exec rm {} \;
#   find $USERDIR -name '*.FMG*' -exec rm {} \;
   exit
