#! /bin/csh -f
# $Revision: IBM System image backup. With mods. for AIX 3.2(Tuan D. 6/92)$.
# Full system backup procedure for an IBM-based flight simulator.
# Syntax:
#   sysbackup { host[cae]| if| redo{argument}| show{argument}| 
#             restore {argument [filenames]}| help }
#   Where:
#     host: Back up the host computer. This is the default.
#     cae : Backup also the caevg disk. ( Only on system with a three disk 
#           configuration )
#     if  : Backup the IBM Instructor facilities computer.
#     redo: Redo the one part that failed. See argument below.
#     show: Show what are on the  backuped tape. See also argument.
#     restore: Restore file(s) from backup tape. See also argument, filenames.
#     argument: Select one of the following arguments:
#          "/"  Redo/show/restore the bootable, system image tape.
#          "/cae" Redo/show/restore the cae tape. 
#               (Only on system with a three-disk configuration)
#          "/cae1" Redo/show/restore the cae1 tape.
#     filenames: Name of file(s) to restore from tape. If no name is specfied, 
#     the whole tape will be restored.
#  By default, this program does a complete backup of the IBM host computer 
#  which has a two 670MB disks. The O/S and cae file system in one disk, 
#  and the cae1 file system on another disk.
#  There are some sites that were set up with three 355MB disks. The O/S, 
#  the cae, the cae1 file systems each is on a separate disk. For those 
#  sites an option "/cae" must be added on the command.
#  For an IBM IF computer, a more compact backup is possible. Everything 
#  is on the same disk.
#  The "redo" option is provided so that on host computers, if any of the
#  steps is wrong, that step can be redone.
#  
#   
#  Tuan D.
#  CAE Electronics Ltd.,
#  February 1992.
#  
if ( $#argv == 0 ) goto GETHELP
onintr INTR
clear
echo "#1                (0)0lqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqk"
echo "#1                x(B)B                                       [0m(0)0x"
echo "#1                x(B)B        CAE IBM-BASED SIMULATOR        [0m(0)0x"
echo "#1                x(B)B                                       [0m(0)0x"
echo "#1                x(B)B      FULL SYSTEM BACKUP PROGRAM       [0m(0)0x"
echo "#1                x(B)B                                       [0m(0)0x"
echo "#1                x(B)B        Version 12  -  June 1992       [0m(0)0x"
echo "#1                x(B)B                                       [0m(0)0x"
echo "#1                (0)0mqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqj(B)B"
#
set REDO=""
set CAE=""
set IF=""
set opt=1
set logf="/sysbackup.log"
set HOST="T"
set REL="`uname -r`"
set SBTAPE="`printenv SBTAPE`"
if ( "$SBTAPE" == "" ) then 
  set SBTAPE="/dev/rmt0"
else
  echo "> Using tape device: $SBTAPE"
endif
set BIN="`/cae/logicals -t cae_caelib_path`"
setenv PATH ".:`echo $BIN`:`echo $PATH`"
cd /
while ( $opt <= $#argv )

  switch ( "$argv[$opt]" )
  case "help":
  case "HELP":
    set HOST=""
    sleep 3
    goto GETHELP
  case "host":
  case "HOST":
    echo " "
    getsysinfo
    repeat 5 echo " " > $logf
    echo "> Full system backup of `hostname` starting at `date`">>$logf
    echo " " >> $logf
    echo "> Using tape device $SBTAPE." >> $logf
    repeat 3 echo " " >> $logf
    breaksw
  case "redo":
  case "REDO":
    set REDO="Y"
    set CAE=""
    set SYS=""
    set HOST=""
    @ opt = $opt + 1
    if ( $opt > $#argv ) then
      echo "> Missing argument tape name"
      goto GETHELP
    endif
    set tn="$argv[$opt]"
    if ( "$tn" == "/" ) then
      set SYS="Y"
      goto BOOTTAPE
    else if ( "$tn" == "/cae" ) then
      set CAE="Y"
      goto CAETAPE
    else if ( "$tn" == "/cae1" ) then
      goto CAE1TAPE
    else
      repeat 3 echo " "
      echo "> Backup a normal directory: $tn "
#
BACKUP:
#
      echo -n "> Put a new tape in, and type [Y] when ready, or [N] to quit: "
      set x=($<)
      if ( "$x" == "n" || "$x" == "N" ) exit
      if ( ! ( "$x" == "y" || "$x" == "Y" ) ) goto BACKUP
      cd $tn
      find . -print | backup -ivqf "$SBTAPE".4
      exit
    endif
  case "if":
  case "IF":
    set IF="T"
    set HOST=""
    goto BOOTTAPE
  case "/cae":
    set CAE="Y"
    echo "> Will backup /cae file system on tape separately."
    breaksw
  case "show":
  case "SHOW":
    goto SHOWTAPE
  case "restore":
  case "RESTORE":
    goto RESTORE
  default:
    echo "   **** Wrong option: $argv[$opt] ****" 
    exit
  endsw
  @ opt = $opt + 1

end
#
#
BOOTTAPE:
echo " "
echo "> Full system Backup of `hostname` on `date`"
echo " "
if ( "$HOST" == "T" ) echo "   **** STEP 1: Making a bootable tape. Please wait .... ****"
if ( "$HOST" == "T" ) echo "   **** STEP 1: Making a bootable tape. Please wait .... ****">> $logf
echo " "
if ( "$IF" == "T" ) goto KILLPROC
#
echo "> Unmounting the CD-ROM ... "
unmount /dev/cd0
set stat=$status
if ( $stat != 0 ) then
#
CDROM:
#
  if ( "$HOST" == "T" ) echo "   - Could not unmount the CD-ROM.">> $logf
  echo " "
  echo "**** Cannot unmount the CD-ROM." 
  echo " "
  echo "> If there is no CD-ROM on this system, the backup can continue."
  echo "> If there is a CD-ROM, please stop the procedure and unmount it manually."
  echo "  Then restart the backup again."
  echo " "
  echo -n "> Do you want to continue: [Y/N] "
  set x=($<)
  if ( "$x" == "n" || "$x" == "N" ) exit
  if ( ! ( "$x" == "y" || "$x" == "Y" ) ) goto CDROM
else
  if ( "$HOST" == "T" ) echo "   - Unmounted the CD-ROM.">> $logf
endif
#
KILLPROC:
killall - > /dev/null
if ( "$HOST" == "T" ) echo "   - Killed all unnessessary procesess.">> $logf
#
LOADTAPE:
#
echo -n "  ---> Please put in a NEW tape and type Y when you are ready: "
set x=($<)
if ( ! ( "$x" == "y" || "$x" == "Y" ) ) goto  LOADTAPE
#
if ( "$REL" != "1" ) goto REL2
#
bosboot -m -p /usr/bin/tape.proto -d "$SBTAPE" -f /usr/bin/tape.fs \
-b /usr/bin/tape.image > /dev/null
#
if ( $status != 0 ) then
 echo "   **** (FATAL) Cannot create boot image, please verify. **** "
 if ( "$HOST" == "T" ) then
   echo "   **** (FATAL) Cannot create boot image, please verify. **** ">>$logf
   if ( -e $logf ) lp $logf
 endif
 exit
endif
#
/usr/lpp/bosinst/diskette/mkinstdskt /usr/bin/bosinst.image > /dev/null
if ( $status != 0 ) then
 echo "**** (FATAL) Cannot create installed image, please verify. ****"
 if ( "$HOST" == "T" ) then
   echo "   **** (FATAL) Cannot create installed image, please verify. **** ">>$logf
   if ( -e $logf ) lp $logf
 endif
 exit
endif
dd bs=512 conv=sync of="$SBTAPE".5 if=/usr/bin/tape.image>/dev/null
if ( $status != 0 ) then
 echo "**** (FATAL) Cannot write to tape, please verify. ****"
 if ( "$HOST" == "T" ) then
   echo "   **** (FATAL) Cannot write to tape , please verify. **** ">>$logf
   if ( -e $logf ) lp $logf
 endif
 exit
endif
if ( "$HOST" == "T" ) echo "   - Put boot image on tape.">> $logf
dd bs=512 conv=sync of="$SBTAPE".5 if=/usr/bin/bosinst.image> /dev/null
if ( $status != 0 ) then
 echo "**** (FATAL) Cannot write to tape, please verify. ****"
 if ( "$HOST" == "T" ) then
   echo "   **** (FATAL) Cannot write to tape , please verify. **** ">>$logf
   if ( -e $logf ) lp $logf
 endif
 exit
endif
if ( "$HOST" == "T" ) echo "   - Put installation image on tape.">> $logf
dd bs=512 conv=sync of="$SBTAPE".5 if=/bootrec>/dev/null
if ( $status != 0 ) then
 echo "**** (FATAL) Cannot write to tape, please verify. ****"
 if ( "$HOST" == "T" ) then
   echo "   **** (FATAL) Cannot write to tape , please verify. **** ">>$logf
   if ( -e $logf ) lp $logf
 endif
 exit
endif
if ( "$HOST" == "T" ) echo "   - Put boot record on tape.">> $logf
#
SYSTAPE:
#
chdev -l "$SBTAPE" -a "block_size=512 ret=no" > /dev/null
#
REL2:
#
if ( "$HOST" == "T" ) then
clear
echo "#1                (0)0lqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqk"
echo "#1                x(B)B                                       [0m(0)0x"
echo "#1                x(B)B        CAE IBM-BASED SIMULATOR        [0m(0)0x"
echo "#1                x(B)B                                       [0m(0)0x"
echo "#1                x(B)B      FULL SYSTEM BACKUP PROGRAM       [0m(0)0x"
echo "#1                x(B)B                                       [0m(0)0x"
echo "#1                x(B)B        Version 12  -  June 1992       [0m(0)0x"
echo "#1                x(B)B                                       [0m(0)0x"
echo "#1                (0)0mqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqj(B)B"
  echo " " 
  date 
  echo " "
  echo "   **** STEP 2: Backup system image on tape. ****">> $logf
  if ( "$REL" != "2" ) echo "   - Changed tape drive characteristics.">> $logf
endif
echo "#6(B)B[1;5m    `hostname` System Backup in progress ...[0m(0)0(B)B"
mkszfile && mksysb "$SBTAPE".5 > /dev/null
set stat=$status
echo " "
if ( $stat != 0 ) then
 echo "**** (FATAL) SYSTEM IMAGE backup failed, please verify ****"
 date
 if ( "$HOST" == "T" ) then
 echo "   **** (FATAL) SYSTEM IMAGE backup failed, please verify. **** ">>$logf
   if ( -e $logf ) lp $logf
 endif
 exit
else
 echo "> System image backup is successful."
 if ( "$HOST" == "T" ) echo "   - System image backup was successful.">> $logf
endif
if ( "$REDO" == "Y" || "$IF" == "T" ) then
  echo " "
  if ( "$REDO" == "Y" ) then
     echo "> Redo $argv[$opt] completed"
  else
     echo "> Backup of `hostname` completetd "
  endif
  echo " "
  date
  exit
endif
#
CAE1TAPE:
#
cd /
ls -l cae1 | grep -s '^l' > /dev/null
if ( $status == 0 ) goto CAETAPE
echo " "
echo "> Backup /cae1 file system "
echo -n "  ---> Please put in a NEW tape and type Y when you are ready: "
set x=($<)
if ( ! ( "$x" == "y" || "$x" == "Y" ) ) goto  CAE1TAPE
echo " "
if ( "$HOST" == "T" ) echo "   **** STEP 3: Backup /cae1 file system. Please wait .... ****"
echo " "
if ( "$HOST" == "T" ) echo "   **** STEP 3: Backup /cae1 file system. ****">> $logf
cd /cae1
find . -print | backup -iqf "$SBTAPE".4
if ( $status != 0 ) then
 echo " "
 date
 echo "**** BACKUP /cae1 failed, please verify **** "
 if ( "$HOST" == "T" ) then
   echo "   **** (FATAL) Backup of /cae1 failed, please verify. **** ">>$logf
   if ( -e $logf ) lp $logf
 endif
 exit
else
 if ( "$HOST" == "T" ) echo "   - Backup of /cae1 was successful.">> $logf
endif
#
if ( "$REDO" == "Y" ) then
  echo " "
  echo "> Redo $argv[$opt] completed"
  echo " "
  date
  exit
endif
#
CAETAPE:
#
if ( "$CAE" == "" ) goto EXIT
echo "> Backup /cae file system "
echo -n "  ---> Please put in a NEW tape and type Y when you are ready: "
set x=($<)
if ( ! ( "$x" == "y" || "$x" == "Y" ) ) goto  CAETAPE
echo " "
if ( "$HOST" == "T" ) echo "   **** STEP 4: Backup /cae file system. Please wait .... ****"
echo " "
if ( "$HOST" == "T" ) echo "   **** STEP 4: Backup /cae file system. ****">> $logf
cd /cae
find . -print | backup -iqf "$SBTAPE".4
if ( $status != 0 ) then
 echo " "
 date
 echo "**** BACKUP /cae failed, please verify ****"
 if ( "$HOST" == "T" ) then
   echo "   **** (FATAL) Backup of /cae  failed, please verify. **** ">>$logf
   if ( -e $logf ) lp $logf
 endif
else
 if ( "$HOST" == "T" ) echo "   - Backup of /cae1 was successful.">> $logf
endif
#
EXIT:
echo " "
#chdev -l "$SBTAPE" -a ecc_flag=no>/dev/null
if ( "$REDO" == "Y" ) then
  echo "> Redo of $argv[$opt] completed"
else
  echo "****** Full system backup completed on `date` *****"
  if ( "$HOST" == "T" ) then
    repeat 3 echo " " >> $logf
    echo "> Full system backup completed on `date` ">> $logf
    repeat 3 echo " " >> $logf
    if ( -e $logf ) lp $logf
  endif
endif
echo " "
exit
#
#
SHOWTAPE:
repeat 2 echo " "
echo -n "> Please load the tape and type Y when you are ready: "
set x=($<)
if ( ! ( "$x" == "y" || "$x" == "Y" ) ) goto  SHOWTAPE
@ opt = $opt + 1
if ( $opt > $#argv ) then
  echo "> Missing argument tape name."
  goto GETHELP
endif
if ( "$argv[$opt]" == "/" ) then
  if ( "$REL" == "1" ) then
     restore -Tqvf "$SBTAPE".4  -s4
  else if ( "$REL" == "2" ) then
     tctl -f "$SBTAPE" rewind
     tctl -f "$SBTAPE".5 fsf 3
     tar -tvdpf "$SBTAPE".4
  endif
else
  restore -Tqvf "$SBTAPE".4 
endif
exit
#
RESTORE:
#
repeat 2 echo " "
echo -n "> Please load the correct tape and type Y when you are ready: "
set x=($<)
if ( ! ( "$x" == "y" || "$x" == "Y" ) ) goto  RESTORE
@ opt = $opt + 1
if ( $opt > $#argv ) then
  echo "> Missing argument tape name."
  goto GETHELP
else
  set rd="$argv[$opt]"
endif
set rf=" "
while ( $opt <= $#argv )
  @ opt = $opt + 1
  set rf=" $rf $argv[$opt] "
end
cd $rd
echo " "
echo " > Restoring: $rf "
echo " "
if ( "$rd" == "/" ) then
  if ( "$REL" == "1" ) then
    restore -xqvf "$SBTAPE".5  -s4 "$rf"
  else if ( "$REL" == "2" ) then
    tctl -f "$SBTAPE" rewind
    tctl -f "$SBTAPE".5 fsf 3
    tar -xdpvf "$SBTAPE".5 "$rf"
  endif
else
  restore -xqvf "$SBTAPE".4  "$rf"
endif
exit
#
#
GETHELP:
clear
echo " "
echo 'sysbackup { host [cae] | if | redo {argument} | show {argument} '
echo "          | restore {argument} [filename(s)] | help }"
echo " "
echo "Function: Full system backup on bootable tape of IBM-based CAE simulator."
echo "Where:"
echo "   host: Backup the IBM host computer. A log file will be printed." 
echo "   cae: Backup the cae file system on a separate tape."
echo "        The default is to backup cae on the same tape with the O/S."
echo "   if: Backup the Instructor Facilities IBM computer."
echo "   redo: Redo the part that failed."
echo "   show: Show what are on the tape."
echo "         File name shown is the correct name for restoring latter."
echo "   restore: Restore files that were backuped by this procedure."
echo "            The optional argument filename is file name to be restored."
echo "            If no file name is specified, the whole tape is restored."
echo "   The option redo/restore/show requires one of the following arguments:"
echo '     "/" The Operating system bootable image tape.'
echo '     "/cae" The /cae file system tape.'
echo '     "/cae1" The /cae1 file system tape.'
echo "   help: This message."
echo "The default tape drive used is /dev/rmt0."
echo 'It can be changed by typing: setenv SBTAPE "correct_tape_drive_name"'
exit
#
INTR:
echo "***** sysbackup get interrupted *****"
if ( "$HOST" == "T" ) echo "***** sysbackup get interrupted *****">>$logf
exit
