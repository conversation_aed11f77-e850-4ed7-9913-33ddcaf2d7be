#!  /bin/csh -f
#!  $Revision: PGD_CMP - Invoke the PGCDIR or PSGIDIR utility V1.1 May-91$
#!
#!  Version 1.0: <PERSON><PERSON> (18-Apr-91)
#!     - Initial version of this script
#!  Version 1.1: <PERSON> (23-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
set FSE_UNIK="`pid`.tmp.1"
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set FSE_TEMP=$SIMEX_DIR/work/pgdt_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/pgdl_$FSE_UNIK
#
echo '&pgcinit.dat'  >$FSE_TEMP
echo '&page.dat'    >>$FSE_TEMP
echo '&page.sgi'    >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
setenv SIMEX  " "
unalias pgcdir
$argv[1] $argv[2-]
rm $FSE_LIST
exit
