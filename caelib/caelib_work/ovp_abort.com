#! /bin/csh -f
#-----------------------------------------------------------------------
#
# This shell is called by host_ovp in order to kill CTS process
# previously spawned with root privileges
#
#-----------------------------------------------------------------------
#
# $Author: Pierre_Toulouse $
# $Revision: 1.2 $
# $Date: 92/08/10 16:14:18 $
#
# REVISION HISTORY
# ----------------
#
# $Log:	ovp_abort.com,v $
# Revision 1.2  92/08/10  16:14:18  Pierre_Toulouse
# - Modified to kill all spawned CTS processes.
# - Modified header.
# 
# Revision 1.1  92/05/29  16:40:04  Pierre_Toulouse
# Initial revision
# 
#-----------------------------------------------------------------------
#
#  Set the path
#
   set BIN=`/cae/logicals -t cae_caelib_path`
   setenv PATH $PATH':'$BIN
#
#  Get CTS pid
#
   set cts_pid =( `ps -e -u root | grep cts.exe | grep -v grep | cut -c1-6` )
#
#  Kill CTS
#
   if ($#cts_pid != 0) then
      kill -TSTP $cts_pid
   endif
#
#  Run CTS to reset simulator
#
   $BIN/ctsp.com ovp_reset
#
   exit(0)
#
# eof
