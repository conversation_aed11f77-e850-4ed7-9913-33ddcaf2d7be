#
#  $Revision: EXTERN - awk pattern & scanner command files V1.1 (LN) Mar-91$
#
#  awk pattern & scanner command files to generate
#  external symbol for sync tables
#
#  Version 1.0: <PERSON><PERSON> (01-Aug-89)
#     - initial version
#
#  Version 1.1: <PERSON><PERSON> (01-Mar-91)
#     - Modified to support new disp table format
#
BEGIN {
}

{ 
  band_count = 0
  #
  # Skip the critical table
  #
  while ((NF == 0) || ($1 == ";")) {
    getline
  }
  if ( $1 == "crit_table:" ) { # Begin generatin critical table

    getline                    # Skip to new line
    while ((NF == 0) || ($1 == ";")) {
      getline
    }


    while ($1 != "end_crittable:"){

      # Get next line

      getline 
      while ((NF == 0) || ($1 == ";")) {
        getline
      }
    }

  }                           # End generatin Critical table
  #
  #  Skip the synchronous table
  #
  if ( $1 == "sync_table:" ) { # Begin generating synchronous table

    getline                    # Skip to new line
    while ((NF == 0) || ($1 == ";")) {
      getline
    }

    while ($1 != "end_synctable:"){

      # Get next line

      getline 
      while ((NF == 0) || ($1 == ";")) {
        getline
      }
    }
  }                           # End generating synchronous table
  #
  #  this loop determines the total number of modules in a band
  #
  while ( substr($1,0,4) == "band" ) {
      
     i = index($1,":")
     def_name = substr($1,0,i-1)          # save band name
     print "#define "  def_name "_ " band_count
     band_count = band_count + 1
     # Get next line
     getline 
     while ((NF == 0) || ($1 == ";")) {
       getline
     }

     mod_count = 0                      # module counter to zero
     while ((substr($1,0,4) != "band") && (substr($1,0,3) != "end")){
       if (($1 == "prog") || ($1 == "aprog")){
	 i = index($2,".") 
         print " extern " substr($2,0,i-1) ";"
         mod_count++                    # increment module counter
        }
       # Get next line
       getline 
       while ((NF == 0) || ($1 == ";")) {
         getline
       }
     } # end while PROG
     # create define symbol for module counter
     print "#define " def_name "   " mod_count

  } # end while BAND

}
END { }
