#! /bin/csh -f
# $Revision: VERSION V1.8 06-Aug-92 | RBE Making an ident list $
#
clear
#
echo " Version    V1.8   "
echo "      "
echo " This script performs operations to make ident lists "
echo "      "
echo " - Operation (1)  must be run on site," 
echo " - Operation (1c) must be run on OSU computer (caelib source)," 
echo " - All the other can be run anywhere as long as the input files are "
echo "   in the ship directory  "
echo "      "
echo "  [1]  generation of the file (ident_ship.lst), (ident_ship.out) and"
echo "       (ident_ship.mis) from the CAELIB_WORK and WORK configurations "
echo "  [2]  generation of the files .exe,.aaa,.com,.for &.oth from the "
echo "       (ident_ship.out) file "
echo "  [3]  reduction of .exe ->.exr or .aaa -> .aar "
echo "  [4]  generation of shorten list (ident_ship.lss) "
echo "  [a]  all the above: (1), (2), (3) and (4) "
echo "  [1c] generation of the file (ident_ship.lst) and (ident_ship.out)"
echo "       from the OSU master caelib source configuration "
echo "  [ac] all the above: (1c), (2), (3) and (4) "
echo "  [m]  miscellaneous output only "
echo "  [x]  exit "
echo " "
echo -n " Enter operation requested (1/2/3/4/a/1c/ac/m/x) "
set ANS = ($<)
echo " "

if ( $ANS == "x" ) then
  exit
endif

echo -n " Enter output file ID: (xxxx)  "
set SHIP = ($<)
echo " "

if ( $ANS == "ac" || $ANS == '1c' ) then
  echo -n " Enter configuration file : (xxxxxxxxx)  "
  set CONF = ($<)
endif

if ( $ANS == "3" ) then
  echo " For the reduction, do you want to process both, exe only or aaa only"
  echo -n " (b/e/a) "
  set ANS3 = ($<)
else
  set ANS3 = 'b'
endif

set TMP_DIR    = "/cae1/ship"
set CONF_DIR   = "/cae/simex_plus/caelib/"
set TMP1_FILE  = "$TMP_DIR/tmp_1.dat.1"
set TMP2_FILE  = "$TMP_DIR/tmp_2.dat.1"
set TMP3_FILE  = "$TMP_DIR/tmp_3.dat.1"
set DAT1_FILE  = "$TMP_DIR/tmp_4.dat.1"
set LIS1_FILE  = "$TMP_DIR/tmp_5.dat.1"
set DAT2_FILE  = "$TMP_DIR/tmp_6.dat.1"
set LIS2_FILE  = "$TMP_DIR/tmp_7.dat.1"
set SPLIT      = "$TMP_DIR/tmp_8"
set LIS_FILE   = `revl "$TMP_DIR/ident_$SHIP.lst" +`
set OUT_FILE   = `revl "$TMP_DIR/ident_$SHIP.out" +`

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

#============================================
# First part: [1] make file ident_ship.lst ident_ship.out
#


if ( $ANS == "1" || $ANS == "a" ) then

echo "Please wait "

#retrieving wanted file from WORK configuration
echo "&*.exe"        > $DAT1_FILE 
echo "&*.a"         >> $DAT1_FILE 
echo "&*.com"       >> $DAT1_FILE 

echo "&*.def"       >> $DAT1_FILE 
echo "&*init.for"   >> $DAT1_FILE 
echo "&*scal.for"   >> $DAT1_FILE 
echo "&*cfio.for"   >> $DAT1_FILE 
echo "&*dmcfg.for"  >> $DAT1_FILE 
echo "&*dmcfg.inc"  >> $DAT1_FILE 
echo "&*vsio.for"   >> $DAT1_FILE 
echo "&*rand.for"   >> $DAT1_FILE 
echo "&host.inc"    >> $DAT1_FILE 
echo "&ccu.inc"     >> $DAT1_FILE 
echo "&ccufg.for"   >> $DAT1_FILE 
echo "&ccufpc.for"  >> $DAT1_FILE 
echo "&dmcspec.inc" >> $DAT1_FILE 

setenv smp_source $DAT1_FILE 
setenv smp_target $LIS1_FILE 
setenv cae_smp_conf WORK
smp_find_file

echo "&*"       > $DAT2_FILE 
setenv smp_source $DAT2_FILE 
setenv smp_target $LIS2_FILE 
setenv cae_smp_conf CAELIB_WORK 
smp_find_file

cat $LIS1_FILE  > $TMP1_FILE 
cat $LIS2_FILE >> $TMP1_FILE

#deleting unwanted files from list
echo "ap0c0.exe"    > $TMP3_FILE 
echo "ap0c1.exe"   >> $TMP3_FILE 
echo "ap0c2.exe"   >> $TMP3_FILE 
echo "sp0c0.exe"   >> $TMP3_FILE 
echo "sp0c1.exe"   >> $TMP3_FILE 
echo "sp0c2.exe"   >> $TMP3_FILE 
echo "a30_bld.com" >> $TMP3_FILE 
echo "a30_ent.com" >> $TMP3_FILE 
echo "c30_bld.com" >> $TMP3_FILE 
echo "c30_ent.com" >> $TMP3_FILE 
echo "cmhcpy.exe"  >> $TMP3_FILE 
echo "dad_bld.com" >> $TMP3_FILE 
echo "dfc_bld.com" >> $TMP3_FILE 
echo "dfc_ent.com" >> $TMP3_FILE 
echo "dfclib.a"    >> $TMP3_FILE 
echo "drm_lod.com" >> $TMP3_FILE 
echo "e30_bld.com" >> $TMP3_FILE 
echo "e30_ent.com" >> $TMP3_FILE 
echo "mcx_ent.com" >> $TMP3_FILE 
echo "motion.exe"  >> $TMP3_FILE 
echo "prihcpy.exe" >> $TMP3_FILE 

set FILE1 = $TMP1_FILE
set FILE2 = $TMP2_FILE

foreach LINE ("`cat $TMP3_FILE`")
   grep -v $LINE $FILE1 > $FILE2 
   set FILE3 = $FILE2 
   set FILE2 = $FILE1
   set FILE1 = $FILE3
end

#making the ident on files from the list
sort $FILE1 > $FILE2 
uniq $FILE2 > $LIS_FILE 
split -100 $LIS_FILE $SPLIT 

foreach FILE ( $SPLIT* )
   foreach LINE ("`cat $FILE`")
      ident -s $LINE >> $OUT_FILE 
   end
end

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

#making the miscellaneous list
echo "/cae/lib/adisp.a" > $TMP1_FILE
echo "/cae/lib/cdbmap_cdbsrv.o" >> $TMP1_FILE
echo "/cae/lib/common.a" >> $TMP1_FILE
echo "/cae/lib/cts_server.a" >> $TMP1_FILE
echo "/cae/lib/ctsdisp.a" >> $TMP1_FILE
echo "/cae/lib/disp.a" >> $TMP1_FILE
echo "/cae/lib/fakentry.a" >> $TMP1_FILE
echo "/cae/lib/libcae.a" >> $TMP1_FILE
echo "/cae/lib/libcts.a" >> $TMP1_FILE
echo "/cae/lib/libdmc.a" >> $TMP1_FILE
echo "/cae/lib/libeas.a" >> $TMP1_FILE
echo "/cae/lib/libvt100.a" >> $TMP1_FILE
echo "/cae/lib/pfudbg.a" >> $TMP1_FILE
echo "/cae/lib/system.a" >> $TMP1_FILE
echo "/cae/lib/users.a" >> $TMP1_FILE
echo "/cae/simex_plus/support/caelib_sup.com" >> $TMP1_FILE
echo "/cae/simex_plus/support/fse_sup.com" >> $TMP1_FILE
echo "/cae/simex_plus/support/sse_gbl.com" >> $TMP1_FILE
echo "/cae/caeconfig" >> $TMP1_FILE
echo "/cae/sitelogs" >> $TMP1_FILE
echo "/cae/logicals" >> $TMP1_FILE
echo "/etc/hosts " >> $TMP1_FILE
echo "/etc/drivers/cae_entdd" >> $TMP1_FILE
echo "/etc/drivers/cae_sadd " >> $TMP1_FILE
echo "/etc/mfg/rc.prload" >> $TMP1_FILE

set OUT_FILE1 = `revl "$TMP_DIR/ident_$SHIP.mis" + `
set OUT_FILE2 = `revl "$TMP_DIR/ident_$SHIP.lmi" + `
set FILE1 = $TMP1_FILE

foreach LINE ("`cat $FILE1`")
  set LINE1 = `revl "$LINE" `
  if (-e "$LINE1") then
    echo $LINE >> $OUT_FILE2 
    ident -s $LINE1 >> $OUT_FILE1
  endif
end

echo "First part ship terminated "

endif
#first part ship
#============================================
# First part(c): [1c] make file ident_ship.lst ident_ship.out
#

if ( $ANS == "1c" || $ANS == "ac" ) then

echo "Please wait "

set SITE = "_site"
ls -l $CONF_DIR$CONF$SITE  > $TMP1_FILE
grep -v "total" $TMP1_FILE > $LIS1_FILE

split -100 $LIS1_FILE $SPLIT 

foreach FILE (  $SPLIT* )
   foreach LINE ("`cat $FILE`")
      set LINE = ($LINE)
      echo $LINE[11] >> $LIS_FILE    
   end
end

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

split -100 $LIS_FILE $SPLIT 

foreach FILE ( $SPLIT* )
   foreach LINE ("`cat $FILE`")
      ident -s $LINE >> $OUT_FILE 
   end
end

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

echo "First part caelib terminated "

endif

#============================================
# Second part:  make file ident_ship.exe
#                         ident_ship.com
#                         ident_ship.aaa
#                         ident_ship.oth

if ( $ANS == "2" || $ANS == "a" || $ANS == "ac" ) then

echo "Please wait "

set INP_FILE = `revl "$TMP_DIR/ident_$SHIP.out" `

set EXE_FILE = "$TMP_DIR/ident_$SHIP.exe" 
set COM_FILE = "$TMP_DIR/ident_$SHIP.com" 
set AAA_FILE = "$TMP_DIR/ident_$SHIP.aaa" 
set FOR_FILE = "$TMP_DIR/ident_$SHIP.for" 
set OTH_FILE = "$TMP_DIR/ident_$SHIP.oth" 

unalias rm
rm $EXE_FILE >&/dev/null
rm $COM_FILE >&/dev/null
rm $AAA_FILE >&/dev/null
rm $FOR_FILE >&/dev/null
rm $OTH_FILE >&/dev/null
rm $TMP_DIR/tmp_*>&/dev/null

split -400 $INP_FILE $SPLIT 

foreach FILE ( $SPLIT* )
  grep -v "*"       $FILE > $TMP1_FILE 
  grep -v "?"       $TMP1_FILE > $FILE
  grep -v "Source:" $FILE > $TMP1_FILE 
  grep -v "Header:" $TMP1_FILE > $TMP2_FILE 
  sed "s/\/cae\/simex_plus\/element\///" $TMP2_FILE > $FILE 

  foreach LINE ("`cat $FILE`")
    echo $LINE | grep "Revision:" >&/dev/null
    if ( $status == 1 ) then 

      set LINE1 = `echo $LINE | cut -f1 -d':'`
      set LINE = `norev $LINE1`

      echo $LINE | grep "\.exe" >&/dev/null
      if ( $status == 0 ) then 
        set OUT_FILE = $EXE_FILE
      else 
        echo $LINE | grep "\.com" >&/dev/null
        if ( $status == 0 ) then 
          set OUT_FILE = $COM_FILE
        else 
          echo $LINE | grep "\.a" >&/dev/null
          if ( $status == 0 ) then 
            set OUT_FILE = $AAA_FILE
          else 
            echo $LINE | grep "\.for" >&/dev/null
            if ( $status == 0 ) then 
              set OUT_FILE = $FOR_FILE
            else 
              echo $LINE | grep "\.inc" >&/dev/null
              if ( $status == 0 ) then 
                set OUT_FILE = $FOR_FILE
              else 
                 echo $LINE | grep "\.def" >&/dev/null
                if ( $status == 0 ) then 
                  set OUT_FILE = $FOR_FILE
                else 
                  set OUT_FILE = $OTH_FILE
                endif
              endif
            endif
          endif
        endif          
      endif 

      echo " " >> $OUT_FILE

    endif 
    echo $LINE >> $OUT_FILE
  end  
end

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

echo "Second part terminated "

endif
#end of second part
#
#============================================
# Third part:  make file .exr and .aar 
#

if ( $ANS == "3" || $ANS == "a" || $ANS == "ac" ) then

echo "Please wait "

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

set REPEAT  = 't'
set REPEAT1 = 't'
set REPEAT2 = 'f'
set INP_FILE  = "$TMP_DIR/ident_$SHIP.exe" 
set INP_FILE1 = "$TMP_DIR/ident_$SHIP.aaa" 
set OUT_FILE  = "$TMP_DIR/ident_$SHIP.exr" 
set OUT_FILE1 = "$TMP_DIR/ident_$SHIP.aar" 

if ( $ANS3 == "e" ) then
  set REPEAT1 = 'f'
else if ( $ANS3 == "a" ) then
  set REPEAT1 = 'f'
  set INP_FILE = "$TMP_DIR/ident_$SHIP.aaa" 
  set OUT_FILE = "$TMP_DIR/ident_$SHIP.aar" 
endif

while ( $REPEAT == 't' )

  set SOR_FILE = $TMP1_FILE 
  echo " " > $OUT_FILE

  sort $INP_FILE > $DAT1_FILE 
  uniq -d $DAT1_FILE > $DAT2_FILE 
  grep "Revision" $DAT2_FILE > $SOR_FILE 
  split -400 $INP_FILE $SPLIT 
  
  foreach FILE ( $SPLIT* )
    set FILE1 = $LIS1_FILE 
    cat $FILE > $FILE1 
    set FILE2 = $LIS2_FILE 
    foreach LINE ("`cat $SOR_FILE`")
      set LINE = ($LINE)
      set LINE1 = "$LINE[1] $LINE[2] $LINE[3]"
      grep -v "$LINE1" $FILE1 > $FILE2
      set FILE3 = $FILE1
      set FILE1 = $FILE2
      set FILE2 = $FILE3 
    end  
    cat $FILE1 >> $OUT_FILE
  end
  
  set REPEAT  = $REPEAT1 
  set REPEAT1 = $REPEAT2

  set INP_FILE = $INP_FILE1
  set OUT_FILE = $OUT_FILE1
  unalias rm
  rm $TMP_DIR/tmp_*>&/dev/null
  
end
# return to while

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

echo "Third part terminated "

endif
#third part 

#============================================
# Forth part:  make file ident_ship.lss

if ( $ANS == "4" || $ANS == "a" || $ANS == "ac" ) then

echo "Please wait "

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

set INP_FILE = `revl "$TMP_DIR/ident_$SHIP.lst" `
set OUT_FILE = `revl "$TMP_DIR/ident_$SHIP.lss" +`

sed "s/\/cae\/simex_plus\/element\///" $INP_FILE > $TMP1_FILE
split -400 $TMP1_FILE $SPLIT 

foreach FILE ( $SPLIT* )
  foreach LINE ("`cat $FILE`")
    set LINE1 = `norev $LINE`
    echo $LINE1 >> $DAT1_FILE
  end
end

grep "\.exe" $DAT1_FILE  > $OUT_FILE
grep "\.com" $DAT1_FILE >> $OUT_FILE
grep "\.a"   $DAT1_FILE >> $OUT_FILE
grep "\.for" $DAT1_FILE >> $OUT_FILE
grep "\.inc" $DAT1_FILE >> $OUT_FILE
grep "\.def" $DAT1_FILE >> $OUT_FILE
grep -v "\.exe" $DAT1_FILE > $DAT2_FILE
grep -v "\.com" $DAT2_FILE > $DAT1_FILE
grep -v "\.a"   $DAT1_FILE > $DAT2_FILE
grep -v "\.for" $DAT2_FILE > $DAT1_FILE
grep -v "\.inc" $DAT1_FILE > $DAT2_FILE
grep -v "\.def" $DAT2_FILE > $DAT1_FILE
cat $DAT1_FILE >> $OUT_FILE

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

echo "Forth part terminated "

endif
#end of forth part

#============================================
# Miscellaneous only 

if ( $ANS == "m" ) then

unalias rm
rm $TMP_DIR/tmp_*>&/dev/null

echo "/cae/lib/adisp.a" > $TMP1_FILE
echo "/cae/lib/cdbmap_cdbsrv.o" >> $TMP1_FILE
echo "/cae/lib/common.a" >> $TMP1_FILE
echo "/cae/lib/cts_server.a" >> $TMP1_FILE
echo "/cae/lib/cts_disp.a" >> $TMP1_FILE
echo "/cae/lib/disp.a" >> $TMP1_FILE
echo "/cae/lib/fakentry.a" >> $TMP1_FILE
echo "/cae/lib/libcae.a" >> $TMP1_FILE
echo "/cae/lib/libcts.a" >> $TMP1_FILE
echo "/cae/lib/libdmc.a" >> $TMP1_FILE
echo "/cae/lib/libeas.a" >> $TMP1_FILE
echo "/cae/lib/libvt100.a" >> $TMP1_FILE
echo "/cae/lib/pfudbg.a" >> $TMP1_FILE
echo "/cae/lib/system.a" >> $TMP1_FILE
echo "/cae/lib/users.a" >> $TMP1_FILE
echo "/cae/simex_plus/support/caelib_sup.com" >> $TMP1_FILE
echo "/cae/simex_plus/support/fse_sup.com" >> $TMP1_FILE
echo "/cae/simex_plus/support/sse_gbl.com" >> $TMP1_FILE
echo "/cae/caeconfig" >> $TMP1_FILE
echo "/cae/sitelogs" >> $TMP1_FILE
echo "/cae/logicals" >> $TMP1_FILE
echo "/etc/hosts " >> $TMP1_FILE
echo "/etc/drivers/cae_entdd" >> $TMP1_FILE
echo "/etc/drivers/cae_sadd " >> $TMP1_FILE
echo "/etc/mfg/rc.prload" >> $TMP1_FILE

set OUT_FILE1 = `revl "$TMP_DIR/ident_$SHIP.mis" + `
set OUT_FILE2 = `revl "$TMP_DIR/ident_$SHIP.lmi" + `
set FILE1 = $TMP1_FILE

foreach LINE ("`cat $FILE1`")
  set LINE1 = `revl "$LINE" `
  if (-e "$LINE1") then
    echo $LINE >> $OUT_FILE2 
    ident -s $LINE1 >> $OUT_FILE1
  endif
end

echo "miscellaneous part finished "

endif
#miscellaneous part  

exit
