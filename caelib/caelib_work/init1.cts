def cdb1 = "connect/back/nolocal/cdb=cae_simex_plus:usd8-
/exe=fake1.exe cdb1"
def cdb2 = "connect/back/nolocal/cdb=cae_simex_plus:usd8-
/exe=fake2.exe cdb2"
def cdb3 = "connect/back/nolocal/cdb=cae_simex_plus:usd8-
/exe=fake3.exe cdb3"
def cdb4 = "connect/back/nolocal/cdb=cae_simex_plus:usd8-
/exe=fake4.exe cdb4"
def sp0c0d = "connect/fore/local/cdb=cae_cdb:usd8-
/process=CPU0.SP0 usd8"
def ap0c0d = "connect/fore/local/cdb=cae_cdb:usd8-
/process=CPU0.AP0 usd8"
def sp0c0 = "connect/fore/nolocal/cdb=cae_cdb:usd8-
/process=CPU0.SP0 usd8"
def ap0c0 = "connect/fore/nolocal/cdb=cae_cdb:usd8-
/process=CPU0.AP0 usd8"
set task
