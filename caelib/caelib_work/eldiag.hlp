
  The PCU diagnostic is used to verify that the PCU is correctly
  connected to the host and that communication works. 

  The user must begin a session by allocating the serial device that 
  connects to the PCU (see ALLOCATE command). If the device allocation
  is successful, the diagnostic will also send commands to initialize the
  PCU. The user can then work interactively with the diagnostic to test
  the PCU (see Example in on-line help).

1 General

  The PCU diagnostic is used to verify that the PCU is correctly
  connected to the host and that communication works. 

  The user must begin a session by allocating the serial device that 
  connects to the PCU (see ALLOCATE command). If the device allocation
  is successful, the diagnostic will also send commands to initialize the
  PCU. The user can then work interactively with the diagnostic to test
  the PCU (see Example in on-line help).


1 ALLOCATE

  The ALLOCATE command is used to attach the host program to the correct
  serial line specified by the user.

  COMMAND FORMAT: ALLOCATE (ALL) device_name

  Where: device_name is the serial port.

  Note: Enclose device_name in " " to be case sensitive. Otherwise,
        upper case is assumed.

1 ASCII

  The ASCII command will transmit to the PCU ascii characters to
  display. This will run until CTRL_C in entered by the user. 

1 CALIBRATE

  The CALIBRATE command positions a point in the center of the PCU. The
  user must touch this point so that correct coordinates are used by the
  host diagnostic. Once this point is received, the user will be displayed
  a second point at the bottom left corner of the PCU. The user must touch 
  this point also.

  Note that the calibration values will be stored on disk and restored
  at each session.

  COMMAND FORMAT: CALIBRATE (CAL)

1 CLEAR

  The CLEAR command is used to clear the PCU screen.

  COMMAND FORMAT: CLEAR (CLE)

1 EXIT

  The EXIT command is used to terminate the diagnostic session.

  COMMAND FORMAT: EXIT (EXI) (X) (QUI)

1 SET

  The set command will set the terminal port correctly for the PCU.

  COMMAND FORMAT: SET device_name

  Where: device_name is the serial port.
  
  Note: The command will first display what it will do and prompt
        the user if he wishes to continue.

  Note: Enclose device_name in " " to be case sensitive. Otherwise,
        upper case is assumed.

1 TOUCH

  The TOUCH command is used to check the two-way communication to and
  from the HOST and the PCU. The host will transmit commands to the PCU
  to display 3 boxes. The user must touch the boxes at random and verify
  correct operation. Each box will light up when touched.
  If the user decides to touch an area outside the box, an X will be
  displayed near the touched area. Note that a simple linear calibration 
  scheme is used and thus, some touches may not be exact.

  Use CTRL_C to stop or wait for the READ TIME_OUT to occur.

1 Example

  EMERALD> ALLOCATE device_name     Ex. VAX [ALLOC TTB1:]
                                    Ex. IBM [alloc "tty1"]

  EMERALD> SET device_name          Ex. VAX [SET TTB1:] 
                                    Ex. IBM [SET "tty1"] 

  EMERALD> CALIBRATE                User must touch the centered point 
                                    on PCU and then the bottom left point 

  EMERALD> ASCII                    User should verify that ascii 
                                    characters are displayed on the PCU 

  EMERALD> TOUCH                    User must touch the inside of the 
                                    boxes displayed on the PCU. Boxes will
                                    light up on a touch 
