1 @
The @ command is for executing a CTS command file.

Format:
  @file_spec

The file will be read line by line and the commands will be executed as
if they were typed by the user. Execution of a command file may be
aborted by typing Control-C. 

The default extension of the CTS command file is ".cts", so remember
when executing a journal file (see JOURNAL command) to add the ".jou"
extension at the end of the file_name.

It is possible for the lines of the command file to be displayed on
screen, using the "SET VERIFY" command.

File execution may be suspended, resumed or stopped using the STOP,
CONTINUE and ABORT commands. See those commands for more information.
1 ABORT
The ABORT command is for ending the execution of a command file,
with no possibility to resume its execution later.

Format:
  ABORT

If this command is put in a command file, its execution ends
right away, and the user gets the CTS_PLUS> prompt.

When this command is typed in, CTS_PLUS will close all the 
command files that were stopped (see STOP command) and
return to the normal CTS_PLUS mode of operation.
1 BLKMON
The Blkmon command is for initiating constant monitoring of a block
of labels contiguous in memory (mandatory). Label values are 
updated on screen only when they change.

Format:
  BLKMON[/qualifier...] examine_command {examine_command}

On the first pass the current value of the labels are displayed,
then values are updated only when they change.

When monitoring starts, the output window of CTS_PLUS will be shortened to
allow space for the monitor window. The user's input window remains
unchanged. 

The BLKMON command can monitor up to 1000 labels.
2 /STOP
This qualifier is for ending the monitoring operation

Format:
  BLKMON/[NO]STOP
2 /LINE
This qualifier is for specifying the number of lines reserved for
the monitor window (the default is 10).

Format:
  BLKMON/LINE=numeric_value ...

example:
  BLKMON/LINE=5 VUG VM

2 examples

  It is possible to specify only the starting and ending label.
  CTS_PLUS> BLKMON VUG VM          ! labels from VUG to VM 
  CTS_PLUS>                        ! will be monitored
  It is also possible to supply a full examine command.
  CTS_PLUS> BLKMON E/HEX VUG VM    ! this way the values 
  CTS_PLUS>                        ! will be displayed in hex.
  It is also possible to enter local labels.
  CTS_PLUS> BLKMON VFLIGHT.LABEL1 VFLIGHT.LABEL2

  It is also possible to save to a file all the output sent
  to the monitor window, by using the JOURNAL command.
  CTS_PLUS> JOURNAL MONITOR TO MYFILE.LIS
  CTS_PLUS> BLKMON VUG VM
  CTS_PLUS> ........
1 BUSMON
The Busmon command is for monitoring the state of the bus(es) on the
simulator.

Format:
  BUSMON[/qualifier]

If a bus is down, the name of the bus is displayed in the monitor window,
and all the breakers that are on that dead bus are displayed as well.
2 /STOP
This qualifier is for ending the monitoring operation.

Format:
  BUSMON/[NO]STOP
2 /LINE
This qualifier is for specifying the number of lines reserved for
the monitor window (the default is 10).

Format:
  BUSMON/LINE=numeric_value ...

example:
  BUSMON/LINE=5
1 CBMON
The Cbmon command is for monitoring the Circuit Breakers of the CDB.

Format:
  CBMON[/qualifier]

Up to 1000 breakers may be monitored, the values are displayed
only when changes occur (a switch is turned ON or OFF).
2 /STOP
This qualifier is for ending the monitoring operation

Format:
  CBMON/[NO]STOP
2 /LINE
This qualifier is for specifying the number of lines reserved for
the monitor window (the default is 10).

Format:
  CBMON/LINE=numeric_value ...

example:
  CBMON/LINE=5
1 COLLECT
The COLLECT command is for getting values of simulation variables
over a period of time without skiping an iteration.

Format:
  COLLECT[/qualifier] list_of_examine_commands [collect_options]

For each EXAMINE commands it is possible to substitute
a new label_name which will be used to reference whatever is being 
collected.
2 /FREEZE_FLAG=[label_name]
Specifies the name of the Freeze Flag label which should be used
when starting/ending the test. When a test starts CTS_PLUS will always
deposit FALSE into this label, and TRUE at the end.

Format:
  COLLECT/FREEZE_FLAG=[label_name]

CTS_PLUS gives a default value (YIFREZ) for this label, but it is 
possible to change it on the fly using the qualifier, or to change it 
permanently using to SET DEF command.

  CTS_PLUS> SET DEF COLL/FREEZE=YISPARE

The label used has to be declared as a LOGICAL (or BYTE).
2 /ITERATION_COUNTER=[label_name]
Specifies the name of the Iteration Counter label which should be used
when running the test.

Format:
  COLLECT/ITERATION_COUNTER=[label_name]

CTS_PLUS gives a default value (CTSTSTNO) for this label, but it is 
possible to change it on the fly using the qualifier, or to change it 
permanently using to SET DEF command.

  CTS_PLUS> SET DEF COLL/IT=YISPARECNT

The label used has to be declared as a INTEGER*4
2 /TIME_LABEL=[label_name]
Specifies the name of the Time label which should be used
when running the test (The label containing the the TIME in seconds).

Format:
  COLLECT/TIME_LABEL=[label_name]

CTS_PLUS gives a default value (CTSTSTTM) for this label, but it is 
possible to change it on the fly using the qualifier, or to change it 
permanently using to SET DEF command.

  CTS_PLUS> SET DEF COLL/TIME=YISPARETI

The label used has to be declared as a REAL*4
2 /STOP
Stops current collection of simulation variables.

Format:
  COLLECT/[NO]STOP

Data collected so far will be made available to the user.
2 /WAIT
Indicates if the CTS_PLUS should be frozen or not while collection
takes place.

Format:
  COLLECT/[NO]WAIT

The /WAIT is very useful when executing CTS_PLUS command files. If not
used, the CTS_PLUS commands following the COLLECT command in the file will 
be executed even though the operation has not completed.
2 /PROMPT
This argument is used to collect data not at some rate, but when the
user indicates it (by hitting RETURN).

Format:
  COLLECT/[NO]PROMPT .....

When the test starts the user gets a prompt:

  <HIT RETURN TO GET A SAMPLE>

And the prompt will return as long as the test lasts. To end the test
just hit CTRL-Z (or type EXIT). Default is NOPROMPT.
2 /START_PROMPT
This argument is used to force a test to start when the user hits return 
only. By default the test will start after the user enters the test command,
but sometimes a user may wish to set some switches before starting the
actual test, and if the test is in a command file he/she will not
have time to do this. This is where this feature might be used.

Format:
  COLLECT/[NO]START_PROMPT .....

When the test starts the user gets a prompt:

  <HIT RETURN TO START>

The user should hit return when he/she is ready to go.
2 /LIMIT
This is for indicating the maximum duration of a test (in iteration)

Format:
  COLLECT/LIMIT=numeric_value

The default is for 500 samples. If no value are given for the rate of
collection, CTS_PLUS will automatically change it so that the test
covers a total of this many iterations.

example:

  CTS_PLUS> COLL/LIMIT=100 VUG FOR 2000 IT
  here CTS_PLUS will collect data every 20 iterations for a total of
  100 samples.

  CTS_PLUS> COLL/LIM=100 VUG FOR 2000 IT EVERY 1 IT
  here CTS_PLUS will ignore the LIMIT argument, and gather 2000 samples.
2 /GRAPH
This is for indicating is a graph should be automatically output
at the end of the COLLECT. 

Format:
  COLLECT/[NO]GRAPH={SCREEN, IMAGEN, PRINTRONIX}

This option is mostly used for to automatically send plot output to screen.
It is not very practical for hardcopy because the COLLECT command
does not provide any way to format the graph like the GRAPH or the PLOT
commands provide.

example:

  CTS_PLUS> COLL/GRAPH VUG VVG FOR 10
  Note that the /GRAPH option forces CTS_PLUS to freeze the input while
  the test is running like the /WAIT option.
2 AT
Specifies the rate (in hertz) at which collecting should be performed.

Format:
  COLLECT[...] AT numeric_value

By default collection is performed every iteration, if possible.

  CTS_PLUS> COLLECT VUG AT 30     ! 30 times / sec.
2 EVERY
Specifies the period (in seconds or iteration) of two 
consecutive collected samples.

Format:
  COLLECT[...] EVERY numeric_value [IT | SEC]

By default collection is performed every iteration.

  CTS_PLUS> COLLECT VUG EVERY 10.0    ! every 10 seconds.
2 FOR
Specifies the duration (in seconds or iterations) of the collection.

Format:
  COLLECT[...] FOR numeric_value [IT | SEC]

By default the collection lasts for 500 iterations.

  CTS_PLUS> COLLECT VUG FOR 1000 IT

2 WHEN
Specifies the starting condition of the collection.

Format:
  COLLECT[...] WHEN (expression)

The format of the expression is explained in the 'command_format'
section. Of course to make sense, this expression must evalute to
TRUE or FALSE.

The collection will only begin when the expression evaluates to TRUE.

  CTS_PLUS> COLLECT VUG WHEN ((VUG > 0.0) AND NOT YIABORT)
2 UNTIL
Specifies the stopping condition of the collection.

Format:
  COLLECT[...] UNTIL (expression)

The format of the expression is explained in the 'command_format'
section. Of course to make sense, this expression must evalute to
TRUE or FALSE.

The collection will end when the expression evaluates to TRUE.
(or when the duration (FOR argument) is exceeded).

  CTS_PLUS> COLLECT VUG UNTIL ((VUG > 0.0) AND NOT YIABORT)
2 IN
Specifies the name of the result of collection object into which
the collected data should be stored.

Format:
  COLLECT[...] IN object_name

This argument is optional, if not specified the data will be saved
in a collection named RESULT, and since this name is the default
object name for the PLOT and PUT command as well object name
can be ignored all together. They are usefull however when the
user whishes to have a lot of plot result in memory at the same time
(RESULT will be able to contain only 25 plots at a time, if other
objects are used that figure jumps to 250).

  CTS_PLUS> COLL VUG FOR 1 IN MAC
  Collection MAC completed with 30 samples.

  Now MAC can be plotted for instance.
  CTS_PLUS> PLOT/ALL MAC
2 INITIALLY
Specifies a series of command to be executed before the test
starts.

Format:
  COLLECT[...] INITIALLY {[label]:examine_command
                          [label]:deposit_command
                          [label]:hold_command
                          [label]:run_command;...}

This option is used to mostly to get initial conditions (to
be later displayed on the plot), but it may also be used
to initialize label values.

example:
  This will automatically examine those values and include them
  in the plot (if the user plots it).
  CTS_PLUS> COLLECT VUG INITIALLY VBRAKE, VWG FOR 10 SEC
  result will be displayed in hex on the plot
  CTS_PLUS> COLLECT VUG INITIALLY E/HEX VBRAKE, VWG FOR 10 SEC
  VUG will be initialized to 1.0 before the test starts
  CTS> COLLECT VUG INITIALLY E/HEX VBRAKE, VWG; DEP VUG 1.0 FOR 10 SEC
2 example

  This will collect vug for 300 iterations (every iterations)
  CTS_PLUS> COLLECT VUG,VVG[10,VGR(1) FOR 300 IT WHEN (LABEL = 1.4) -
  _CTS_PLUS> EVERY 1 IT INITIALLY VUG,VVG,VWG

  As always it is possible to supply EXAMINE arguments for the labels
  as well
  CTS_PLUS> COLL E/HEX VUG FOR 1
  This way, when save to disk the values will be displayed in HEX.

  This example shows the use of the label_name option, to change the name
  of the label which will be ultimatly displayed on the plot.
  CTS_PLUS> COLL ALTITUDE:E VUG; VELOCITY:E VWG FOR 1
  The labels VUG and VWG are collected, but in the result their name is 
  substituted for the new ones: ALTITUDE and VELOCITY.
1 command_format
This is a representation of the CTS_PLUS screen:

  ------------------------------------------------------------
  |VUG                                           1.1 FT/SEC  |
  |VVG                                           2.2 FT/SEC  |
  | This is the monitor window. Labels to be monitored       |
  | are continuously displayed here.                         |
  |                                                          |
  |--- Monitoring--------------------------------------------|
  |                                                          |
  | This is the output window. Results of CTS_PLUS commands  |
  | (except MONITORs) are displayed here.                    |
  |                                                          |
  |----------------------------------------------------------|
  | This is the input window. Commands are entered here and  |
  | most error messages are diplayed here as well.           |
  | >                                                        |
  | >                                                        |
  |----------------------------------------------------------|

There may be up two three windows on the CTS_PLUS screen. One is for
command input, one for command output, and a third one is for 
monitoring activities (see MONITOR command). This third window
varies in size depending on the number of variables to MONITOR.

The Separation line between the input window and the output window
is used to display information about TESTs. The Tests are fully
explained later in this document.

The command format is explained individually for each command,
but one thing to remember is that there are no operation modes
in CTS_PLUS, which means that all the CTS_PLUS commands are available 
at any time without switching from one mode to the other.

The next topics are for describing syntax of objects which are
common to a lot of CTS_PLUS commands.
2 control_characters
Several characters have special meanings for CTS_PLUS. They are listed 
here:

<CTRL>C: This will interrupt the execution of a CTS_PLUS command
(when possible), or a CTS_PLUS command file (always).

<CTRL>Z: Exits CTS_PLUS in an orderly fashion.

<CTRL>W: Redraws the entire screen (refresh).

DCL editing keys: These are the standard DCL editing keys: (arrow keys,
<CTRL>A, <CTRL>E, <CTRL>H, ... These are used to help edit command lines.
The Up and Down arrow keys are used for command recall.

'!': This character is to indicate a comment. Everithing typed after
she '!' will be ignored.

'-': This is the continuation character. The current line will be
continued on the next one.
2 label_names
The syntax for a label name reference requires some explaining.

Format:
  [module_name.]label_name[ {'[' | ']'}numeric_value] [`{AFT | BEF}]

We will look at every section in detail:

The 'module_name' section is optional and could contain the name
of the simulation module in which to look for the label. If "CDB"
is used, the label will be looked for in the Common Data Base.
If no 'module_name' is specified the label will be looked for in
the current scope defined by the "SET SCOPE" command (see that
command for more information).

The 'label_name' section contains the name of the label to look for.
Wildcard characters ('*', '?') are allowed, the label referenced will 
be the first one found that matches the pattern formed with the
'label_name'. Array reference should be placed here as well.

The next section is for define the number of continous labels
to be treated. For example specifying "[10" in that field
references the block of label formed by the current label and the
next 9 ones. Similarly specifying "]10" in that field
references the block of label formed by the current label and the
next 9 ones. This field is used when EXAMINing or DEPOSITing
values of a block of labels. See the example section to get
a better idea on how this works.

The next section is to indicate if the label should be referenced 
before or after the modules are called (see section on 
primitive_command to see what this does).
3 examples
These are legal label references:

  reference                 meaning

  VFLIGHT.VUG               Label VUG of VFLIGHT module 
  CDB.VUG                   Label VUG of the Common DataBase
  VUG                       Label VUG of the current module set
                            by the SET SCOPE command
  CDB.VU*                   The first label begining by VU in
                            the Common DataBase
  VFLIGHT.VUG[3             Label VUG and the next 2 labels in
                            module VFLIGHT. 
                            **** note that the 'NEXT' labels
                            could be the next labels in alphabetical
                            order or memory location order depending
                            on the context. See EXAMINE command.
  VFLIGHT.VUG]3             Label VUG and the preceeding 2 label
                            in module VFLIGHT.
  VUG`AFT                   Label VUG of current module AFTER
                            modules are called
  VUG`BEF                   Label VUG of current module BEFORE
                            modules are called
  VGR(1)                    Simple array reference
  VFLIGHT.VGR(1)[3`AFT      Putting them all together, if you have to
2 numeric_values
Integer constants may be entered in CTS_PLUS in four different format:
decimal, hexadecimal, binary and octal. Here is an example on
how to use these base modes:
 
           FORMAT             VALUE ( 15 )
          
           BINARY             2#1111#
           DECIMAL            15 or 10#15#
           OCTAL              8#17#
           HEX                16#0F#
2 expressions
Expressions may be used in several CTS_PLUS commands. In most commands
it is required for the expressions to be delimited by round brackets
'(...)'. These expressions follow the ADA standard for expressions
representation, but it is not too different from the FORTRAN one.

This is a list of the operations which may be included in an
expression:

  operation        description            example

  AND              logical AND            A AND B
  OR               logical OR             A OR B
  XOR              logical XOR            A XOR B
  NOT              logical negation       NOT A
  <,>,<=,>=,=,/=   comparaison operators  A < B
  +, -, *, /       math operators         A + B
  MOD, REM         remainder              A MOD B
  **               exponent operator      A ** B
  ABS              absolute value         ABS(A)
  SIN, ASIN        sine, arc sine         SIN(A)
  COS, ACOS        cos, arc cosine        COS(A)
  TAN, ATAN        tangent, arc tangent   TAN(A)
  SINH             hyperbolic sine        SINH(A)
  COSH             hyperbolic cosine      COSH(A)
  TANH             hyperbolic tangent     TANH(A)
  SQRT             square root            SQRT(A)
  LOG, LOG2, LOG10 logarithm functions    LOG(A) (base e)
  EXP              exponantial function   EXP(A)
  MIN, MAX         minimum, maximum       MIN(A, B, C, D,...)
  REAL             real part of complex   REAL(A)
  IMAG             imaginery part of cpx  IMAG(A)
  ATAN2            arc tangent            ATAN2(A, B)

Expressions may contain numeric values also:

  type             example

  INTEGER          2
  REAL             2.2
  COMPLEX          (2.1, 2.2)
  LOGICAL          true, false
  CHARACTER        'a'

And of course, simulation label names, where appropriate. The syntax
for simulation label names is explained in another section 
(label_name syntax)
2 command_files
A CTS command file is a series of CTS command put in a standard text 
file. The file may be created with any text editor or using the CTS
"CREATE" command.

Comments may be included in the command file by preceeding them with
a "!" character.

The file may be executed using the "@" command. Execution of command
files may be imbeded (this means a command file may contain a "@" 
command), but there is a software limitation on the number of imbeded 
calls allowed.
1 CONNECT
The CONNECT command is for connecting CTS_PLUS with a simulator 
program.

Format:
  CONNECT[/qualifier]... CTS-Server_Name

The CONNECT command actually interfaces with two things. A Common
Data Base (CDB) and Cts-Server. For full information on CTS-Servers
see the 'linking' section. A CTS-Server is always referenced by name
and this is the name which should be entered here.
2 /CDB[=file_spec]
This argument is to specify explicitly the CDB used.

Format:
  CONNECT/CDB [=file_spec]
  CONNECT/NOCDB

The file_spec may be the full file name of the Common Data Base
root file. If no CDB is specified, the one used by default is
CAE_CDB:CTS-Server_Name. If the /NOCDB qualifier is used
then no Common Data Base will be open and CDB variables will
be unavailable for the remainder of the CONNECTion.
2 /EXECUTABLE[=file_spec]
This argument is to specify explicitly the .EXE file which 
contains all the simulation modules to run.

Format:
  CONNECT/EXECUTABLE_NAME[=file_spec]

If no /EXECUTABLE_NAME argument is given the default one used is
Cts-Server_Name.
2 /EXTERNAL
This argument is to specify if CTS_PLUS should attempt to CONNECT to
a .EXE file with all the simulation modules or not.

Format:
  CONNECT/[NO]EXTERNAL

If no /NOEXTERNAL argument is given the resulting connection will
be a CDB-only connection.
2 /LOCAL
This argument is to specify if the user whishes to get access to
local symbols of simulation modules or not.

Format:
  CONNECT/[NO]LOCAL

In order to get access to the local symbols, the simulation modules
must be linked with the /DEBUG option.
2 /FOREGROUND
Indicates if the connection should be a foreground one (real simulator),
or a background one (simulation modules linked with LCTSP).

Format:
  CONNECT/FOREGROUND
  CONNECT/BACKGROUND
2 /BACKGROUND
Indicates if the connection should be a foreground one (real simulator),
or a background one (simulation modules linked with LCTSP).

Format:
  CONNECT/BACKGROUND
  CONNECT/FOREGROUND
2 /PROCESS
This argument is to specify explicitly the name of the process 
to which connect.

Format:
  CONNECT/PROCESS=process_name

This argument is only used on a real simulator.

example:
  CONNECT/PROCESS=CPU0.SP0
  CONNECT/PROCESS=CPU1.AP1
1 CONTINUE
The CONTINUE command is for resuming the execution of a command file 
that was previously stopped using the STOP command.

Format:
  CONTINUE

This command may be typed when the prompt is "STOPPED>".
This means that a command file has halted its
execution because a STOP command was encountered.
1 CREATE
The CREATE command is for creating and entering text in file.

Format:
  CREATE file_spec

This command may be used to create files (CTS_PLUS command files for instance)
more quickly than spawning to an editor to do it.

The user just types in the text line after line and hits Control-Z
when finished.

The default extension for the created file is ".CTS", meaning a CTS_PLUS
command file.
1 DEBUG
The DEBUG command is to gain access the System's Symbolic Debugger.

Format:
  DEBUG

When DEBUG is invoked, the CTS_PLUS utility will freeze and the debugger
will be activated. The user may then perform debugging activities
(including setting up break points). CTS_PLUS will resume execution as
soon as the simulation is running again.
1 DEFINE
The DEFINE command is for defining logical symbols for the 
current CTS_PLUS session.

Format:
  DEFINE symbol_name  {numeric_value | expression | quoted_string},...

Once a symbol is defined it can be reference at any time in CTS_PLUS, 
simply by typing it in the correct context. See the "examples" subtopic 
for more information.

Note that the symbols defined are lost when leaving CTS_PLUS.
2 examples
In this example two symbols are defined and then used to
compute a third one.

  CTS_PLUS> DEFINE TESTLENGTH_IN_SEC 4.0
  CTS_PLUS> DEFINE RATE 0.05
  CTS_PLUS> DEFINE TESTLENGTH_IN_IT (TEST_LENGTH_IN_SEC * RATE)

In this example a new CTS_PLUS command is defined and then used.

  CTS_PLUS> DEFINE FREEZE "DEPOSIT CDB.YIFREZ TRUE"
  CTS_PLUS> FREEZE
1 DEPOSIT
The DEPOSIT command is for assigning values to simulation variables.

Format:
  DEPOSIT[/qualifier,...] label_name {numeric_value | ( expression )}

It is possible to do several DEPOSITs on one call just by seperating them 
with commas. 

The label_name field follows the syntax explained in the 'command_format'
section. So does the expression field.

The numeric_value should be a literal value of a type corresponding
to the label being deposited to. The expected representation of this
value depends on the qualifier used (/HEX, /BIN for instance).
2 /BINARY
Specifies the type of input desired for the label to deposit

Format:
  DEPOSIT/BINARY
  DEPOSIT/HEXADECIMAL
  DEPOSIT/DEFAULT_REPRESENTATION
  DEPOSIT/ASCII

The /DEFAULT_REPRESENTATION qualifier specifies to use the default
representation of the current object (decimal form for INTEGERS for
instance).

2 /HEXADECIMAL
Specifies the type of input desired for the label to deposit

Format:
  DEPOSIT/BINARY
  DEPOSIT/HEXADECIMAL
  DEPOSIT/DEFAULT_REPRESENTATION
  DEPOSIT/ASCII

The /DEFAULT_REPRESENTATION qualifier specifies to use the default
representation of the current object (decimal form for INTEGERS for
instance).

2 /DEFAULT_REPRESENTATION
Specifies the type of input desired for the label to deposit

Format:
  DEPOSIT/BINARY
  DEPOSIT/HEXADECIMAL
  DEPOSIT/DEFAULT_REPRESENTATION
  DEPOSIT/ASCII

The /DEFAULT_REPRESENTATION qualifier specifies to use the default
representation of the current object (decimal form for INTEGERS for
instance).
2 /ASCII
Specifies the type of input desired for the label to deposit

Format:
  DEPOSIT/BINARY
  DEPOSIT/HEXADECIMAL
  DEPOSIT/DEFAULT_REPRESENTATION
  DEPOSIT/ASCII

The /DEFAULT_REPRESENTATION qualifier specifies to use the default
representation of the current object (decimal form for INTEGERS for
instance).
2 /AFTER
With this qualifier, the deposit operation is performed after all the
modules are called. See primitive_command section for more information
on that.

Format:
  DEPOSIT/AFTER
  DEPOSIT/BEFORE
2 /BEFORE
With this qualifier, the deposit operation is performed before all the
modules are called. See primitive_command section for more information
on that.

Format:
  DEPOSIT/BEFORE
  DEPOSIT/AFTER
2 /LOCAL
Indicates that the deposit operation should apply only to the
local CDB, and should not be reflected on all the CDBs.

Format:
  DEPOSIT/LOCAL
  DEPOSIT/EXTERNAL
  DEPOSIT/GLOBAL

These options are used only in FOREGROUND and on a QMR-based system.
2 /EXTERNAL
Indicates that the deposit operation should apply only to the
external CDBs, and should not be reflected on the local one.

Format:
  DEPOSIT/LOCAL
  DEPOSIT/EXTERNAL
  DEPOSIT/GLOBAL

These options are used only in FOREGROUND and on a QMR-based system.
2 /GLOBAL
Indicates that the deposit operation should apply to all the CDBs.
This is the default option.

Format:
  DEPOSIT/LOCAL
  DEPOSIT/EXTERNAL
  DEPOSIT/GLOBAL

These options are used only in FOREGROUND and on a QMR-based system.
2 /VERIFY
Indicates if an examine should be preformed after the
deposit to check what value has been stored into the label.

Format:
  DEPOSIT/VERIFY_ALL
  DEPOSIT/VERIFY_NONE
  DEPOSIT/VERIFY_EXPRESSION (default)

It is possible to specify that only expressions should be 
double-checked, which is the case more often then not.

with /VERIFY_ALL all deposit operations will output the label's value.
with /VERIFY_NONE no values are output after the value is deposited.
with /VERIFY_EXPRESSION only deposits of expressions are output on screen.
2 examples

  CTS_PLUS> D VUG 1.1, VVG (VVG*2)  ! VUG <= 1.1, VVG <= 2 x VVG
  Block deposits are also possible:
  CTS_PLUS> D VUG[250 0.0           ! VUG and the next 249 labels
                                    ! are initialized to 0.
  CTS_PLUS> D VUG]250 0.0           ! VUG and the previous 249 labels
                                    ! are initialized to 0.
  It is possible to specify the input type of the data
  (use SET DEFAULT DEP/HEX to set it permanantly).
  CTS_PLUS> DEP/HEX VUG F10EF0      
  this one does the same thing
  CTS_PLUS> DEP VUG 16#F10EF0#
  CTS_PLUS> DEP/BIN VUG 1010101
  CTS_PLUS> DEP/ASCII VUG "ALLO"

  Note that the `D' keyword may be omitted if `=' is used:
  CTS_PLUS> VUG = 1.1               ! is legal.
1 DISCONNECT
The Disconnect command is for terminating the connection between
CTS and its current CTS-Server (see 'linking' section for more
information on CTS-Server.

Format:
  DISCONNECT

The Common Data Base (CDB) is closed and execution of the simulation
modules is ends (if in background mode).

Note that it is not necessary to DISCONNECT before CONNECTing,
It will be done implicitly.
1 DRIVE
The DRIVE command is used to initiate constant ramping (depositing)
of simulation variables over a period of time.

Format:
  DRIVE[/qualifier] list_of_ramping_commands [drive_options]

Note that if a user whishes to collect simulation variables an drive others
at the same time he/she should use the TEST command, for this
one does only ramping.

There are four types of ramp operation possible.
  1) EXPRESSION : an expression is assigned to some label,
                  and it is evaluated every iteration and stored
                  into the label
  2) RAMP TABLE : a list of values as a function of time are provided,
                  and CTS_PLUS interpolates the missing values, so
                  that the selected label gets the values of the
                  function described in the ramp table.
  3) FILE : similar to 2) but the values are read from a disk file instead
            (standard .VIS format, which is the format used when doing
             a PUT/ASCII).
  4) FROM RESULT : The label gets the value of a label that has collected
                   before.

All types can be put in to a drive command.
2 /FREEZE_FLAG=[label_name]
Specifies the name of the Freeze Flag label which should be used
when starting/ending the test. When a test starts CTS_PLUS will always
deposit FALSE into this label, and TRUE at the end.

Format:
  DRIVE/FREEZE_FLAG=[label_name]

CTS_PLUS gives a default value (YIFREZ) for this label, but it is 
possible to change it on the fly using the qualifier, or to change it 
permanently using to SET DEF command.

  CTS_PLUS> SET DEF DRIVE/FREEZE=YISPARE

The label used has to be declared as a LOGICAL (or BYTE).
2 /ITERATION_COUNTER=[label_name]
Specifies the name of the Iteration Counter label which should be used
when running the test.

Format:
  DRIVE/ITERATION_COUNTER=[label_name]

CTS_PLUS gives a default value (CTSTSTNO) for this label, but it is 
possible to change it on the fly using the qualifier, or to change it 
permanently using to SET DEF command.

  CTS_PLUS> SET DEF DRIVE/IT=YISPARECNT

The label used has to be declared as a INTEGER*4
2 /TIME_LABEL=[label_name]
Specifies the name of the Time label which should be used
when running the test (The label containing the the TIME in seconds).

Format:
  DRIVE/TIME_LABEL=[label_name]

CTS_PLUS gives a default value (CTSTSTTM) for this label, but it is 
possible to change it on the fly using the qualifier, or to change it 
permanently using to SET DEF command.

  CTS_PLUS> SET DEF DRIVE/TIME=YISPARETI

The label used has to be declared as a REAL*4
2 /STOP
Stops current ramping of simulation variables.

Format:
  DRIVE/STOP
2 /WAIT
Indicates if the CTS_PLUS should be frozen or not while driving
takes place.

Format:
  DRIVE/[NO]WAIT

The /WAIT is very useful when executing CTS_PLUS command files. If not
use, the CTS commands following the DRIVE command in the file will 
be executed even though the operation has not completed.
2 /START_PROMPT
This argument is used to force a test to start when the user hits return 
only. By default the test will start after the user enters the test command,
but sometimes a user may wish to set some switches before starting the
actual test, and if the test is in a command file he/she will not
have time to do this. This is where this feature might be used.

Format:
  DRIVE/[NO]START_PROMPT .....

When the test starts the user gets a prompt:

  <HIT RETURN TO START>

The user should hit return when he/she is ready to go.
2 AT
Specifies the rate (in hertz) at which driving should be performed.

Format:
  DRIVE[...] AT numeric_value [IT | SEC]

By default driving is performed every iteration.

example:

  CTS_PLUS> DRIVE VUG (VUG+1) AT 10 ! 10 times/sec
2 EVERY
Specifies the period (in seconds) of two consecutive drives.

Format:
  DRIVE[...] EVERY numeric_value [IT | SEC]

By default driving is performed every iteration.

  CTS_PLUS> DRIVE VUG (VUG+1) EVERY 1 SEC ! 1 times/sec
2 FOR
Specifies the duration (in seconds) of the drive.

Format:
  DRIVE[...] FOR numeric_value [IT | SEC]

  CTS_PLUS> DRIVE VUG (VUG+1) EVERY FOR 100 IT 
2 WHEN
Specifies the starting condition of the drive

Format:
  DRIVE[...] WHEN (expression)

The format of the expression is explained in the 'command_format'
section. Of course to make sense, this expression must evalute to
TRUE or FALSE.

The drive will only begin when the expression evaluates to TRUE.

  CTS_PLUS> DRIVE VUG (VUG+1) WHEN ((VUG > 0.0) AND NOT YIABORT)
2 UNTIL
Specifies the stopping condition of the drive.

Format:
  DRIVE[...] UNTIL (expression)

The format of the expression is explained in the 'command_format'
section. Of course to make sense, this expression must evalute to
TRUE or FALSE.

The drive will end when the expression evaluates to TRUE.
(or when the duration (FOR argument) is exceeded).

  CTS_PLUS> DRIVE VUG (VUG+1) UNTIL ((VUG > 0.0) AND NOT YIABORT)
2 INITIALLY and FINALLY
Specifies a series of command to be executed before (or after) the test
starts.

Format:
  DRIVE[...] INITIALLY | FINALLY
                         {[label]:deposit_command
                          [label]:hold_command
                          [label]:run_command;...}

This option could be used to initialize some labels for instance.

example:

  CTS_PLUS> DRIVE VUG (VUG + 1) INITIALLY DEP VUG 1.0
2 RAMP_TABLES
The ramp tables (case #2) has a more complex syntax than the other cases.

Format
  label_name [value { duration_In_Iteration | 
                     $duration_in_second |
                     #absolute_time_in_iteration |
                     @absolute_time_in_Second } [;] ...]

A ramp table is composed of a list of segment (maximum 500).
Each segment contains a value for the label, and a time at which
that value should be reached. It is possible to specify the time in 
iterations or seconds, and it is possible to specify it in absolute time,
or relative to the preceeding segment.

  examples:

  VM 0.5 100; 1.0 75    ! This ramp lasts for 175 iterations.
                        ! VM reaches value 0.5 at iter. 100 and
                        ! 1.0 at iter. 175

  VM 0.5 #100; 1.0 #175 ! This ramp lasts for 175 iterations.
                        ! VM reaches value 0.5 at iter. 100 and
                        ! 1.0 at iter. 175

  VM 0.5 $1.0; 1.0 $2.5 ! This ramp lasts for 3.5 seconds
                        ! VM reaches value 0.5 after 1 sec, and
                        ! 1.0 after 2.5 seconds (at 3.5 seconds)

  VM 0.5 @1.0; 1.0 @3.5 ! This ramp lasts for 3.5 seconds
                        ! VM reaches value 0.5 at 1 sec, and
                        ! 1.0 after 2.5 seconds (at 3.5 seconds)

  Note that everything can be combined in the same table.
  VM 0.5 #100; 1.0 $10.0; -1.0 @20.0; ......

  Note also that it is possible to RAMP any type of labels

  VM YIFREZ TRUE 100; FALSE 75 
  is a legal ramp table (as long as YIFREZ is a LOGICAL).

2 examples

  The DEFINE command can be very useful for drives, as well see:

  define a couple of ramp tables.
  CTS_PLUS> DEF TAB1 "VM 0.5 $1.0; 1.0 $2.5"
  CTS_PLUS> DEF TAB1 "VUG 1.3 $1.0; -1.1 $2.5"
  now starts a drive containing all four possible types.
  CTS_PLUS> DRIVE RAMP TAB1; RAMP TAB2; RAMP VVG (VVG+1); -
  _CTS_PLUS> RAMP VVE FILE=RESULT2C.VIS; RAMP VGR(2) RESULT.2 FOR 10.0
  the first two drive operations are ramp table, then we have
  an expression drive, then ramping from a file and finally from
  an object in memory.

  Note that it is not necessary to use the DEFINE command:
  CTS_PLUS> DRIVE VUG 1.3 $1.0; -1.1 $2.5 FOR 10   
  is perfectly legal.
1 ENQUIRE
The ENQUIRE command is for testing if a module is frozen or not.

Format:
  ENQUIRE[/qualifier]... [module_name],...

It is possible to enquire about the state of several modules on one 
call just by seperating them with commas.

Possible output: RUNNING, FROZEN or **UNKNOWN** (module not linked).
2 /AFTER
With this qualifier, the module(s) are enquired after all the
modules are called. See primitive_command section for more information
on that.

Format:
  ENQUIRE/AFTER
  ENQUIRE/BEFORE
2 /BEFORE
With this qualifier, the module(s) are enquired before all the
modules are called. See primitive_command section for more information
on that.

Format:
  ENQUIRE/BEFORE
  ENQUIRE/AFTER
2 example

  CTS_PLUS> ENQ VFLIGHT
  actual output:
  VFLIGHT                             RUNNING
1 EXAMINE
The EXAMINE command is for outputting values of simulation variables.

Format:
  EXAMINE[/qualifier,...] 
   {label_name | ( expression )}[/expected_value_field],...

It is possible to do several EXAMINEs in one call.

The label_name field follows the syntax explained in the 'command_format'
section. So does the expression field.
2 expected_value_field
This field is for specifying an expected value and tolerance for the 
current label to EXAMINE.

Format:
  /expected_value [absolute_tolerance percentage_tolerance%]...

The expected_value should be a numeric value of a type corresponding
to the object being examined.

The absolute tolerance should be the absolute permitted error on
the value.

The percentage tolerance should be the percentage permitted error on
the value (if both type of tolerance are used, then there is
an error only if both condition are exceeded).

CTS will ouput a '*' besides the actual result if a tolerance error
was noticed.

Note that for some types (complex, and logicals for instance),
tolerances are ignored.

Examples:

  CTS_PLUS> EXAMINE VUG/1.3 VUG/1.1 VUG/1.1 10% VUG/1.1 0.4

  This is the output generated (the actual value is 1.3).
  VUG        AIRSPEED   FT/SEC     (1.3)  1.3 
  VUG        AIRSPEED   FT/SEC     (1.1)  1.3 *
  VUG        AIRSPEED   FT/SEC     (1.1)  1.3 *
  VUG        AIRSPEED   FT/SEC     (1.1)  1.3 

  Note that the expected value is in round brackets
  and that a '*' is output when there is a tolerance error.
2 /AFTER
With this qualifier, the examine operation is performed after all the
modules are called. See primitive_command section for more information
on that.

Format:
  EXAMINE/AFTER
  EXAMINE/BEFORE
2 /ALPHABETIC_ORDER
Labels should be examined in label name alphabetic order.

Format:
  EXAMINE/ALPHABETIC_ORDER ...
  EXAMINE/PHYSICAL_ORDER ...

This qualifier is used when doing block examines. A block examine
consists in examining a label and some number of consecutive
next (or preceeding) labels. 

When using /ALPHABETIC_ORDER the labels are returned in alphabetical order
and when using /PHYSICAL_ORDER they are returned in memory location
order.

Example:

  CTS_PLUS> EXAMINE/ALPHA VUG[3 VUGB]3; EXAMINE/PHYS VUG[3   
  See label_name syntax in command_format_section for
  full information on block examine operations.

  -- first request
  VUG                           0.000
  VUGA                          0.000
  VUGB                          0.000

  -- second request
  VUGB                          0.000
  VUGA                          0.000
  VUG                           0.000

  -- third request
  VUG                           0.000
  VM                            0.000
  YIFREZ                         TRUE
2 /BEFORE
With this qualifier, the examine operation is performed before all the
modules are called. See primitive_command section for more information
on that.

Format:
  EXAMINE/BEFORE
  EXAMINE/AFTER
2 /BINARY
Specifies the type of output desired for the EXAMINEd object.

Format:
  EXAMINE/BINARY
  EXAMINE/ASCII
  EXAMINE/HEXADECIMAL
  EXAMINE/DEFAULT_REPRESENTATION
  EXAMINE/MAX_PRECISION

The /DEFAULT_REPRESENTATION qualifier specifies to use the default
representation of the current object (decimal form for INTEGERS for
instance).

The /MAX_PRECISION applies to REALs only. It forces CTS to output
floating point values with the scientific notation, which may
be more precise than the label's default representation.
2 /BRIEF
To Indicate if full label information should be output or not.

Format:
  EXAMINE/BRIEF
  EXAMINE/FULL

When /FULL is specified 3 lines of output are used instead of one
to make sure that everything is output correctly

Example:

  It is possible for some filed to be too small when doing
  E/BRIEF:
  CTS_PLUS> EXAMINE/BRIEF (VUG + VVG + VWG); E/BIN/BRIEF VUG

  Output generated:
    On the first case the expression is getting truncated,
    On the other one it is the command field.
  (VUG + VVG + V                                   1.3
  VUG   X VELOCITY WRT GROUND (BOD 2#1010101010010101#

  CTS_PLUS> EXAMINE/FULL (VUG + VVG + VWG); E/BIN/BRIEF VUG

  Output generated: 3 lines are used (where necessary).
  (VUG + VVG + VVG)
                                                   1.3
  VUG   
  X VELOCITY WRT GROUND (BODY) FEET/SEC
                                   2#1010101010010101#
2 /DEFAULT_REPRESENTATION
Specifies the type of output desired for the EXAMINEd object.

Format:
  EXAMINE/BINARY
  EXAMINE/ASCII
  EXAMINE/HEXADECIMAL
  EXAMINE/DEFAULT_REPRESENTATION
  EXAMINE/MAX_PRECISION

The /DEFAULT_REPRESENTATION qualifier specifies to use the default
representation of the current object (decimal form for INTEGERS for
instance).

The /MAX_PRECISION applies to REALs only. It forces CTS to output
floating point values with the scientific notation, which may
be more precise than the label's default representation.
2 /FULL
To Indicate if full label information should be output or not.

Format:
  EXAMINE/BRIEF
  EXAMINE/FULL

When /FULL is specified 3 lines of output are used instead of one
to make sure that everything is output correctly

Example:

  It is possible for some filed to be too small when doing
  E/BRIEF:
  CTS_PLUS> EXAMINE/BRIEF (VUG + VVG + VWG); E/BIN/BRIEF VUG

  Output generated:
    On the first case the expression is getting truncated,
    On the other one it is the command field.
  (VUG + VVG + V                                   1.3
  VUG   X VELOCITY WRT GROUND (BOD 2#1010101010010101#

  CTS_PLUS> EXAMINE/FULL (VUG + VVG + VWG); E/BIN/BRIEF VUG

  Output generated: 3 lines are used (where necessary).
  (VUG + VVG + VVG)
                                                   1.3
  VUG   
  X VELOCITY WRT GROUND (BODY) FEET/SEC
                                   2#1010101010010101#
2 /HEXADECIMAL
Specifies the type of output desired for the EXAMINEd object.

Format:
  EXAMINE/BINARY
  EXAMINE/ASCII
  EXAMINE/HEXADECIMAL
  EXAMINE/DEFAULT_REPRESENTATION
  EXAMINE/MAX_PRECISION

The /DEFAULT_REPRESENTATION qualifier specifies to use the default
representation of the current object (decimal form for INTEGERS for
instance).

The /MAX_PRECISION applies to REALs only. It forces CTS to output
floating point values with the scientific notation, which may
be more precise than the label's default representation.
2 /MAX_PRECISION
Specifies the type of output desired for the EXAMINEd object.

Format:
  EXAMINE/BINARY
  EXAMINE/ASCII
  EXAMINE/HEXADECIMAL
  EXAMINE/DEFAULT_REPRESENTATION
  EXAMINE/MAX_PRECISION

The /DEFAULT_REPRESENTATION qualifier specifies to use the default
representation of the current object (decimal form for INTEGERS for
instance).

The /MAX_PRECISION applies to REALs only. It forces CTS to output
floating point values with the scientific notation, which may
be more precise than the label's default representation.
2 /ASCII
Specifies the type of output desired for the EXAMINEd object.

Format:
  EXAMINE/BINARY
  EXAMINE/ASCII
  EXAMINE/HEXADECIMAL
  EXAMINE/DEFAULT_REPRESENTATION
  EXAMINE/MAX_PRECISION

The /DEFAULT_REPRESENTATION qualifier specifies to use the default
representation of the current object (decimal form for INTEGERS for
instance).

The /MAX_PRECISION applies to REALs only. It forces CTS to output
floating point values with the scientific notation, which may
be more precise than the label's default representation.
2 /SCOPE
To indicate if the scope (or module name) of the Label should be 
output as well

Format:
  EXAMINE/[NO]SCOPE

Example:
  CTS_PLUS> E/NOSCOPE VUG, (VUG+1); E/SCOPE VUG, (VUG+1)
  VUG                           0.000
  (VUG+1)                       1.000
  CDB.VUG                       0.000
  (CDB.VUG+1)                   1.000
2 /PHYSICAL_ORDER
Labels should be examined in label memory location order

Format:
  EXAMINE/ALPHABETIC_ORDER ...
  EXAMINE/PHYSICAL_ORDER ...

This qualifier is used when doing block examines. A block examine
consists in examining a label and some number of consecutive
next (or preceeding) labels. 

When using /ALPHABETIC_ORDER the labels are returned in alphabetical order
and when using /PHYSICAL_ORDER they are returned in memory location
order.

Example:

  CTS_PLUS> EXAMINE/ALPHA VUG[3, VUGB]3; EXAMINE/PHYS VUG[3   
  See label_name syntax in command_format_section for
  full information on block examine operations.

  -- first request
  VUG                           0.000
  VUGA                          0.000
  VUGB                          0.000

  -- second request
  VUGB                          0.000
  VUGA                          0.000
  VUG                           0.000

  -- third request
  VUG                           0.000
  VM                            0.000
  YIFREZ                         TRUE
2 /NEXT
Indicates to Examine labels starting from the previous examine 
command.

Format:
  EXAMINE/NEXT=numeric_value *
  EXAMINE/PREV=numeric_value *

Note that the `Prev Screen' and `Next Screen' keys are programmed with
those functions.

Using these functions it is possible to `Scroll' through the entire
CDB (or through all the local symbols in a specific module).

Examples:

  CTS_PLUS> E/ALP VUG
  VUG     Comment                 1.1
  CTS_PLUS> E/NEXT=3 VUG
  VUGA    Comment              102.23
  VUGB    Comment                2.23
  VUGC    Comment                0.23
2 /PREVIOUS
Indicates to Examine labels starting from the previous examine 
command.

Format:
  EXAMINE/NEXT=numeric_value *
  EXAMINE/PREV=numeric_value *

Note that the `Prev Screen' and `Next Screen' keys are programmed with
those functions.

Using these functions it is possible to `Scroll' through the entire
CDB (or through all the local symbols in a specific module).

Examples:

  CTS_PLUS> E/ALP VUG
  VUG     Comment                 1.1
  CTS_PLUS> E/NEXT=3 VUG
  VUGA    Comment              102.23
  VUGB    Comment                2.23
  VUGC    Comment                0.23
2 /ADDRESS
Indicates to output the address of the label (and not its value).

Format:
  EXAMINE/ADDRESS ...
  EXAMINE/VALUE ...

The default output of the address is HEX but it can be overidden
using the correct qualifier.

example
  CTS_PLUS> E/ADDR VUG
  output:
  VUG      Comment              16#04001023#
2 /VALUE
Indicates to output the value of the label.

Format:
  EXAMINE/ADDRESS ...
  EXAMINE/VALUE ...

It is the default mode in CTS_PLUS. The other is the output the
address of the label.
2 examples

These are a few examples of Examine Requests.

  Note that the default command in CTS_PLUS is examine so
  the E keyword may be omitted.
  CTS_PLUS> VUG           ! examines VUG
  CTS_PLUS> VUG,VVG,VWG   ! 3 labels are examined.
  CTS_PLUS> (VUG+1)       ! expression is evaluated.
  CTS_PLUS> VUG]10        ! vug and the next 10 labels are output

  the next few examples are for local symbols.
  CTS_PLUS> BR37AE.LABEL  ! examines local symbol LABEL
                          ! from module BR37AE
  CTS_PLUS> SET SCOPE BR37AE
  CTS_PLUS> LABEL         ! Same thing.
  CTS_PLUS> CDB.VUG       ! the keyword CDB is for indicating
                          ! that the label should be read from the CDB.
  to go back to CDB label acces by defaut:
  CTS_PLUS> SET SCOPE CDB
  CTS_PLUS> VUG
1 EXIT
The EXIT command is for leaving the CTS_PLUS utility.

Format:
  EXIT

Note that typing Control-Z in CTS_PLUS does that function.
1 getting_started
CTS_PLUS is a utility designed to help the debugging and testing of 
simulation programs. It gives the user access to simulation variables, 
and control over modules execution, using a simple yet powerful command 
set. 

CTS_PLUS gives access to two type of variables: Common Data Base (CDB) 
variables and local module variables. This means that CTS_PLUS can be 
used without a CDB, because all the local variables of the modules 
are automatically accessible.

CTS_PLUS allows the user to examine or modify the values of any of these
variables. It is possible to examine the variables continuously on
screen (monitor), or to acummulate them in memory (collect).
It is also possible to continuously deposit values into simulation
variables (ramp). Variables that were 'collected' may be plotted on
screen or paper.

Some basic concepts should be well undestood before using CTS_PLUS
2 linking_and_connecting
CTS_PLUS is a stand-alone utility, which means that it never needs to be 
linked with simulation modules. So, when CTS_PLUS starts running it
does not have any knowledge of any simulation program. The user has 
to provide the name of an executable file which contains all these 
simulation modules linked together. See the "CONNECT" command
for more information and the section about predifined symbols.

All the modules must be linked using the "LCTSP" program, which is
a program separate from CTS_PLUS. What this program does is link the user's
modules with what is called a CTS-Server. This CTS-Server is just one
more module, but it has the ability to communicate with CTS_PLUS, which
means it receives requests from a CTS_PLUS session connected to it and
it is able to return results as well.

For example, if a user wanted to test just one module he could:

  # !call lcts to link his module with the CTS-Server.
  # LCTSP
  Server Name ==> TEST
  .... (see LCTSP section) for more information on LCTSP.
  Linking completed....
  # ! now he could run CTS_PLUS.
  # CTS
  CTS_PLUS> ! and connect to his simulation program.
  CTS_PLUS> CONNECT TEST
  CTS_PLUS> Now the simulation is loaded and testing may begin.

The user should get familiar with the LCTSP utility if he/she
wants to use it in background.

On a real simulator the LCTSP program is not used, and symbols
are defined to specify to which process (or machine) connect to.
See the section on predifined symbols.
2 primitive_commands
The primitive_commands are a subset of the CTS_PLUS command set. These are
the commands that actually access simulation variables. They are:
  EXAMINE, DEPOSIT (to read or modify simulation variable's values) 
  RUN, HOLD (to freeze/unfreeze a simulation module), 
  ENQUIRE (to see if a simulation module is frozen or not). 
All the other CTS_PLUS commands which accesses the simulation program are 
actually combination of these commands, repeated over specific periods
of time.

One particularity that primitive_commands share is that several of
them may be typed on the same command line (separated by semicolons). 
And the corresponding server request will be sent in one packet only. 
This means that if the user types the command:

  CTS_PLUS> EXAMINE VUG; DEPOSIT VVG 1.1; E VGR(1)

All these operations will be performed during the same simulator 
iteration. This is very usefull on a real simulator because the
user is sure that all the values which will be displayed are
from the same iteration. There is no loss if consistency.

Note that CTS_PLUS always suppose that an EXAMINE command is
entered:
  
  CTS_PLUS> VUG
  is the same as
  CTS_PLUS> EXAMINE VUG

And if an equal sign is used it default to DEPOSIT.
  
  CTS_PLUS> VUG = 1
  is the same has
  CTS_PLUS> DEP VUG 1

Another important concept shared by primitive_command is the iteration
synchronization. It is possible to specify that a primitive_command
should be performed BEFORE or AFTER a single iteration. when this 
feature is used on several commands typed on the same line it can
become very powerful:

  CTS_PLUS> EXAMINE/BEFORE VUG; EXAMINE/AFTER VUG;

This way VUG is examined, the modules are called, and vug is examined
again.
2 composite_commands
The composite_commands are another subset of the CTS_PLUS command set.
These commands combine several primitive_commands to form a more
complex and powerful request. These commands are: 
  COLLECT (which gathers simulation values for a period of time),
  DRIVE   (which assigns values to simulation variables for
           a period of time)
  MONITOR (which displays simulation labels on screen continuously)
  TEST    (which combines all of the above)

Here is a quick example of a composite_command.

  CTS_PLUS> COLLECT VUG, VVG, VWG; FOR 10
  See COLLECT and EXAMINE commands for full syntax
  information on those.

This will start execution of the simulation modules and collect three
simulation variables (VUG, VVG, VWG) for 10 seconds.
2 predefined_symbols
A set of symbols are automatically defined when the CTS_PLUS
utility is executed. These symbols are not real CTS_PLUS commands,
but the user does not notice it. To see the symbols, one could
use the "SHOW SYMBOL *" command, to display on screen the value
of all the symbols.

The most important symbols are the ones which are used to connect
to a real simulation process:

  SP0C0  : Synchronous process # 0 on Master computer.
  AP0C0  : ASynchronous process # 0 on Master computer.
  SP0C1  : Synchronous process # 0 on Slave computer #1.

  SP0C0D : Same as above but these ones allow local symbol access.
  AP0C0D : ''                                                   ''
  SP0C1D : ''                                                   ''

Typing in one of this keyword is the same as executing a complex
CONNECT command. For instance on a specific site this symbol could
execute the command:

  typing
  CTS_PLUS> SP0C0
  is the same as
  CTS_PLUS> CONNECT/CDB=BR37/LOCAL/FORE/PROCESS=CPU0.SP0 BR37

Also it is sometimes necessary to access the CDB when no process
are running on the machine. Another set of symbols are available
for that purpose.

  XREF1
  XREF2
  XREF3
  XREF4

example:
  CTS_PLUS> XREF1

Now the user is connected to the foreground's CDB only, so he/she
has no control over which modules are executed, if any.

Several XRFE's symbols are defined because thay accomodate only one
user at a time. So if you type

  CTS_PLUS> XREF1
  and get the error message:
  Connect Error: Maximum number of users for server reached.

you should try:
  CTS_PLUS> XREF2
1 GRAPH
The GRAPH command is for generating plot output on the terminal
screen or on a hardcopy. Note that it is similar to the
PLOT command. only the actual output differs.

Format:
  GRAPH[/ALL] {[NO]CIRCLE} collection_name.label_name,... [graph_options]
  GRAPH[/ALL] {{[NO]CIRCLE} collection_name.label_name,....} [graph_options]

Several arguments may be specified to specify how the graph should
be formatted.

The collection_name defaults to RESULT, which is the default
name used when collecting, so it is possible to:
  CTS_PLUS> COLLECT VUG FOR 1  ! get values of VUG for 1 sec
  CTS_PLUS> GRAPH/ALL          ! outputs on screen the plots.
2 CIRCLE
This keyword can be put in front of a plot number or label name to
indicate that small circles should be placed on the graph for that label.

Format:
  GRAPH.... {[NO]CIRCLE} collection_name.Label_name ....

examples:

  CTS_PLUS> GRAPH CIRCLE 1, 2, CIRCLE 3 TO PRINTRONIX
  This way the first and third plots will have circles on each
  data sample, the other will not.

  It is possible as well to use the CIRCLE_ALL (or CIRCLE_NONE) option
  to select a default setting for the plots not specified. For example:
  CTS_PLUS> GRAPH NOCIRCLE 1, 2, 3, 4, 5 TO PRINTRONIX CIRCLE_ALL
  will put circles on every plots except the fisrt one.

  It is possible to select the CIRCLE option on overlays as well.
  CTS_PLUS> GRAPH {1, CIRCLE 2} TO PRINTRONIX
2 VS
This keyword is used to specify the label to be used on the X-axis.
It always default to TIME, which is the time at which the label(s)
on the Y-Axis were collected.

Format:
  GRAPH ... VS label_name

Note that the label used on the X-Axis has to come from the same colection
as the ones from the Y-Axis.

example

  CTS_PLUS> GRAPH 1 VS 2         ! plot #1 on the Y-Axis, #2 on the X-axis
  CTS_PLUS> GRAPH 1 VS VUG
  CTS_PLUS> GRAPH 1 VS TIME      ! this is the default anyway.
2 XTITLE
This keyword is for defining the comment which should be used to reference
the X-Axis label. By default, the comment field (if available) of the label
from the CDB is used.

Format:
  GRAPH ... XTITLE quoted_string

example:

  CTS_PLUS> GRAPH 1 XTITLE "This is a test."
2 YTITLE
This keyword is for defining the comment which should be used to reference
the Y-Axis label. By default, the comment field (if available) of the label
from the CDB is used.

Format:
  GRAPH ... YTITLE quoted_string

example:

  CTS_PLUS> GRAPH 1 YTITLE "This is a test."
2 XRANGE
This is to define the range of values which should be included in the
plot on the X-axis. By default the value used are the minimum and maximum
of the values collected.

Format:
  XRANGE numeric_value {TO} numeric_value

example:

  we suppose that the values collected on the X-axis are ranging from
  0 to 10.0 (seconds we suppose) and we want to output only the first 
  5 (seconds)

  CTS_PLUS> GRAPH 1 XRANGE 0 TO 5
2 YRANGE
This is to define the range of values which should be included in the
plot on the Y-axis. By default the value used are the minimum and maximum
of the values collected.

Format:
  YRANGE numeric_value {TO} numeric_value

example:

  we suppose that the values collected on the Y-axis are ranging from
  0 to 10.0 but we want to have an axis ranging from -10 to 10:

  CTS_PLUS> GRAPH 1 YRANGE -10 TO 10
2 GRID
To indicate if a square grid should be output as a background for the plot.
By default none is output.

Format:
  GRAPH ... [NO]GRID

example:

  CTS_PLUS> GRAPH 1 GRID
2 INTERPOLATE
To indicate solid lines should be output on the plot between samples.
By default it is done.

Format:
  GRAPH ... [NO]INTERPOLATE

example:

  CTS_PLUS> GRAPH 1 NOINTERPOLATE
2 EXTEND
To indicate that the Y-Axis should englobe the minimum and maximum of the
values supplied, and not reflect the range normaly supplied.

Format:
  GRAPH ... [NO]EXTEND

example:

  CTS_PLUS> GRAPH 1 EXTEND
2 TIME_SHIFT
This is to define a shift value for the X-axis values.

Format:
  GRAPH ... TIME_SHIFT numeric values

example:

  Let us say that the values on the X-axis are ranging from 10 to 15,
  but that we want values from 0 to 5 to be output instead...
  CTS_PLUS> GRAPH 1 TIME_SHIFT -10
2 VALUE_OF_X_INTERVAL or P1
This option is for defining the value assigned to 1 X-axis interval on the
graph. Note that this argument has only is used in the PLOT command
only, but does not generate any error if used in the GRAPH command.

Format: 
  GRAPH ... {VALUE_OF_X_INTERVAL | P1} numeric_value

example:

  CTS_PLUS> GRAPH 1 TO PRINT P1 4.0
  Each intervals on the X-axis will be equal to 4.0
2 NUMBER_OF_Y_INTERVALS or P2
Defines the number of intervals on the Y-Axis.

Format: 
  GRAPH ... {NUMBER_OF_Y_INTERVALS | P2} numeric_value

example:

  CTS_PLUS> GRAPH 1 TO PRINT P2 10
  There will be 10 intervals on the Y-Axis.
2 X_LENGTH_OF_10_INTERVALS or P3
This argument contains the physical length on the piece of paper of the
plot output of 10 X-Axis intervals (in inches or centimeters).

Format: 
  GRAPH ... {X_LENGTH_OF_10_INTERVALS | P3} numeric_value {IN | CM}

example:

  CTS_PLUS> GRAPH 1 TO PRINT P3 10 IN
  10 intervals on the X-Axis will be equal to 10 inches on the graph.
2 Y_LENGTH_OF_10_INTERVALS or P4
This argument contains the physical length on the piece of paper of the
plot output of 10 Y-Axis intervals (in inches or centimeters).

Format: 
  GRAPH ... {Y_LENGTH_OF_10_INTERVALS | P4} numeric_value {IN | CM}

example:

  CTS_PLUS> GRAPH 1 TO PRINT P4 10 IN
  10 intervals on the 4-Axis will be equal to 10 inches on the graph.
2 CIRCLE_ALL
Indicates that circles should be output on the plot for each sample.

Format:
  GRAPH ... CIRCLE_ALL
  GRAPH ... CIRCLE_NONE

See option CIRCLE (NOCIRCLE) for more information.
2 CIRCLE_NONE
Indicates that circles should not be output on the plot for each sample.

Format:
  GRAPH ... CIRCLE_NONE
  GRAPH ... CIRCLE_ALL

See option CIRCLE (NOCIRCLE) for more information.
2 NUMBER_OF_CIRCLES
This argument is to specified the number of circles which should be output
on the graph if some CIRCLE options are on.

Format:
  GRAPH ... NUMBER_OF_CIRCLES numeric_value

This argument can be used if the output seems to be too loaded with circles
for instance.

example:

  CTS_PLUS> GRAPH 1 TO PRINT NUMBER_OF_C 30
2 TO
This keyword is for specifying the device on which to send the plot
output

Format:
  GRAPH .... TO PRINTRONIX
  GRAPH .... TO IMAGEN
  GRAPH .... TO SCREEN (default)

Two types of printer are supported by CTS_PLUS currently: PRINTRONIX and
IMAGEN. Of course the printer you select has to be present on site.
2 TITLE
This is for specifing a title for a GRAPH or PLOT that will be
output at the top of the page.

Format:
  GRAPH ... TTTLE quoted_string

example:

  CTS_PLUS> GRAPH 1 TITLE "This is a test."
2 INITIAL_CONDITIONS
This keyword is for specifying if initial conditions should be
output on the plot output, and from which result of collection.

Format:
  GRAPH... INITIAL_CONDITIONS {collection_name}

If no collection_name is given the one that is used is the one from
which the labels to be plotted come from.

example:

  CTS_PLUS> GRAPH RESULT.1 INITIAL
  the initial conditions of collection RESULT will be output.

  CTS_PLUS> GRAPH RESULT.1 INITIAL TMP
  the initial conditions of collection TMP will be output.
2 examples

These are a few samples of GRAPH commands.

We suppose here that we have 2 results of collection in memory: 
RESULT, and TEMP.

  CTS_PLUS> GRAPH/ALL          ! all the data collected in
                               ! RESULT is output on screen
  CTS_PLUS> GRAPH/ALL TEMP     ! all the data collected in
                               ! TEMP is output on screen
  CTS_PLUS> GRAPH/ALL TO PRINT ! same, but output is sent to the
                               ! printer. (PRINTRONIX)
  CTS_PLUS> GRAPH 2, TEMP.1    ! the second graph from collection
                               ! RESULT and the first one from
                               ! collection TEMP are output.
  CTS_PLUS> GRAPH {2, TEMP.1}, 3
                               ! the second plot from collection 
                               ! RESULT and the first one from TEMP
                               ! are overlaid, and the third plot
                               ! from collection RESULT is output.
  CTS_PLUS> GRAPH VUG          ! it is possible to reference to plot
                               ! by its name as well: label VUG from
                               ! collection RESULT will be plotted here.

  CTS_PLUS> GRAPH VUG VS TIME  ! does the same as above. TIME is
                               ! the default for the X-axis of the plot
  CTS_PLUS> GRAPH VUG VS VWG   ! it is possible to do cross-plot.
  CTS_PLUS> GRAPH VUG INITIAL  ! outputs initial conditions.

Also a lot of arguments may be used to format the GRAPH

  CTS_PLUS> GRAPH/ALL INTERPOLATE CIRCLE_ALL GRID TITLE "This is a test"
1 HELP
The Help command is for displaying information about a CTS_PLUS command 
or topic.

Format:
  HELP [topic[subtopic]...]
1 HOLD
The HOLD command is for freezing one or several simulation module(s).

Format:
  HOLD[/qualifier]... [module_name],...

Several modules may be frozen on one call just by seperating them with
commas.
2 /AFTER
With this qualifier, the module(s) are frozen after all the
modules are called. See primitive_command section for more information
on that.

Format:
  HOLD/AFTER
  HOLD/BEFORE
2 /ALL
All the modules are frozen.

Format:
  HOLD/[NO]ALL
2 /BEFORE
With this qualifier, the module(s) are frozen before all the
modules are called. See primitive_command section for more information
on that.

Format:
  HOLD/BEFORE
  HOLD/AFTER
2 example

  CTS_PLUS> ENQUIRE VFLIGHT
  VFLIGHT                               RUNNING
  CTS_PLUS> HOLD VFLIGHT
  VFLIGHT                                FROZEN
1 INPUT
The INPUT command is for interactively defining a symbol. See DEFINE
command for symbol usage.

Format:
  INPUT[/qualifier...] symbol_name

This command is useful for prompting to user for inputs when executing 
a command file. When the INPUT command is executed, the user will be able
to enter a symbol value, and that value will be put in the symbol
specified in the command call.
2 /PROMPT[= quoted_string]
This option is for defining the prompt which will be used for the INPUT
command. It defaults to an empty string.
2 /DEFAULT[= quoted_string]
Puts in a default values for the symbol if the user
hits return when entering the value.
2 examples
In this example, the command executed prompts the user for a test length.
The result will be put in symbol LENGTH.

  CTS_PLUS> INPUT/DEF="1.0"/PROMPT="ENTER TEST LENGTH (in sec) ==> " LENGTH
1 JOURNAL
The JOURNAL command is for modifying the journaling options of the
current CTS_PLUS session. 

Format:
  JOURNAL {[NO]MONITOR [NO]ERROR [NO]INPUT [NO]OUTPUT [NO]TRIM,...}
          {TO file_spec | ON | OFF | CLOSE }

Journaling is used to save the commands (and outputs) entered by a 
user in a session to a file. The file is written in the same format 
as a legal CTS_PLUS command file, so it can be reexecuted using the 
"@file_spec" command.

Default file extension for this type of file is .JOU
2 TO
Format: 
  JOURNAL TO file_spec

Opens a new journal file, and turns on journaling.

2 ON
Format: 
  JOURNAL ON

Turns journaling ON, the commands typed are sent to the file specified
by the "JOURNAL TO file_spec" instruction.
2 OFF
Format: 
  JOURNAL OFF

Turns journaling OFF, the commands typed are no longer saved to the file.
Use "JOURNAL ON" to turn it on again..
2 CLOSE
Format: 
  JOURNAL CLOSE

Closes current journal file.
2 [NO]TRIM
Format:
  JOURNAL [NO]TRIM

This option is for specifying if a comment character (!) should
be added to the journal file for monitor, error and output types
of output. By using the NOTRIM option and journaling the input,
the journal file could possibly be reexcuted using the @ command.
Default is TRIM.
2 [NO]MONITOR [NO]ERROR [NO]INPUT [NO]OUTPUT
Format: 
  JOURNAL [NO]MONITOR [NO]ERROR [NO]INPUT [NO]OUTPUT

This is to select what should be sent to the journal file.
It defaults to OUTPUT only.

example
  ! sends the command typed and the output of the commands to 
  ! the file
  CTS_PLUS> JOURNAL INPUT, OUTPUT TO FILE.CTS
  ! sends monitor output to a file.
  CTS_PLUS> JOURNAL MONITOR TO FILE.CTS
1 linking
Linking CTS-Server for background activities (LCTSP program).

In order to run a simulation with CTS_PLUS in background mode, the user
needs to link all his modules with a CTS-Server module. This
is performed with the LCTSP program. This program is not a CTS_PLUS 
command, it cannot be called from CTS_PLUS 
(unless using the SPAWN command).

The LCTSP program creates some object files that are CDB dependant
(two .OBJ files actually). The first question LCTSP asks is if
we should regenerate those object files.

  Using a new CDB (Y/N) =>

If the user replies No, the current object files are used, otherwise
new ones are generated.

The user should reply Y when
  1) running LCTSP for the first time,
  2) after the CDB has been recompiled.

  If <YES> was given some other questions are asked:

    CDB name (default_ship) ==> 
    Number of Xrefs (default_for_ship) ==>
    Initialize CDB (Y) => ! this is for initialize CDB values.
    Freeze Flag (YIFREZ) ==> 
    Simulation Iteration Counter (YSITRCNT) ==>
    Iteration Period (YTITRN) ==>
    Simulation Time (YTSIMTM) ==>
    Test Iteration Counter (CTSTSTNO) ==>
    Test Time (CTSTSTTM) ==>

  Note that default values are given in round brackets.

  The last questions prompt the user for label names which have special
  meaning for CTS_PLUS and the simulation. The default values are given in
  round brackets, so the user may just hit return to keep it or he/she
  can enter a new one.

The LCTSP program then asks the following questions:

  Server Name (ship_name)==> 
                  ! name of the server, In CTS the user will
                  ! connect to it by typing "CONNECT server_name"
  Executable Name (default_exec_name) ==> 
                  ! it is possible to type-in
                  ! an alternate executable name (one is given by default)
  CDB variables (y/n) [y] ==>
                  ! if the user whishes to link with a CDB he/she should
                  ! reply "YES"
    -- if yes on the preceeding question.
    Include Iteration Counter Incrementer Module (y/n) [y] ==>
                  ! A module which automatically increments the 
                  ! iteration counter and updates the simulation time
                  ! may be added if desired.
                  ! note that this module is automatically added
                  ! if the user does not use a CDB.
  Number of Users for Server (1) ==>
                  ! This sever may supports several users
                  ! but 1 is the normal value for it.
  Simulation Rate (default_for_ship) ==>
                  ! The user must specify the rate at which the modules
                  ! will be called.

  Object Name ==> ! Now, Here the name of the .o files should
                  ! be entered (one per line).
                  ! Note that is is possible to enter libraries
                  ! and option files as well.
  Object Name ==> ! Hit return on a blank line when through entering
                  ! modules.

  Module Name ==> ! Now, here are entered the names of the simulation
                  ! modules which are to be called every iteration
                  ! (one per line).
                  ! They should all be entry point name. 
                  ! It is possible to add an extension to the name:
                  ! ".F" (module will be freezable) or
                  ! ".U" (module will not be freezable).
                  ! After the module it is possible to specify on
                  ! which BAND the module is suppose to run 
                  ! (A-J or X)
  Module Name ==> ! Hit return on a blank line when through entering
                  ! module names.

Since a user may wish to include a lot of modules together,
it colud be useful to put the replies to these question in a text file
instead, and then do:

  # LCTSP < MYMODULES.MOD

Then only the first question will be asked (Using a new CDB),
then the expected replies for the questions will be read from the file.

example:

  This is a sample text file.

  NASAX
  /cae1/ship/nasax
  Y
  Y
  1
  30
  nasavf.o
  flight.o
  flib.a         
  optionfile.o

  flight,X
  cforces,A

  <one blank line at the end>

  Note that it is possible to include option files and libraries.
1 LIST
The LIST command is for displaying information on screen about current
result of collection object in memory. It will display which labels 
were collected, and in what file (if any) the are kept.

Format:
  LIST[/qualifier] [object_name]
2 /ALL
All the results in memory are dumped. When the /ALL qualifier is used,
no `object_name' argument is expected.

Format:
  LIST/[NO]ALL
2 /BRIEF
Specifies if full object information should be displayed or not

Format:
  LIST/BRIEF
  LIST/FULL

If the /BRIEF qualifier is used then only the name of the object and labels
appear. See the example.
2 /FULL
Specifies if full object information should be displayed or not

Format:
  LIST/BRIEF
  LIST/FULL

If the /BRIEF qualifier is used then only the name of the object and labels
appear. See the example.
2 example

  CTS_PLUS> LIST/BRIEF RESULT
  Actual output:
  COLLECTION    : RESULT
    Time Label  : TESTIME
    LABEL #1    : VUG

  CTS_PLUS> LIST/FULL RESULT
  Actual output:
  COLLECTION    : RESULT
    sampling rate : 30 Hertz
    sample number : 150
    Time Label  : TESTIME
    LABEL #1    : VUG
                  SAVED TO <CAE_SHIP:RESULT1C.VIS>

  Note that in the second case the filename in which the data has
  been save is displayed. If no PUT has been done on the file,
  <NOT SAVED> would be displayed.
1 logical_names
For the CTS_PLUS utility to work properly, several logical names must be 
defined. Most of the logical name definitions should be in the
system's login file, but if the user whishes he/she could still
redefine them for his/her own purpose. The symbols the user may
wish to redefine are:

CAE_INIT1. This symbol should contain the name of a CTS_PLUS command
file to be executed first when CTS_PLUS starts executing. This file
is intendeded to be the global initialization file for CTS_PLUS, and it
could potentially be shared by several CTS_PLUS users.

CAE_INIT2. This symbol should contain the name of a CTS_PLUS command
file to be executed when CTS_PLUS starts executing (right after CAE_INIT1).
This file is intended to be the user's initialization file. Of course
this use of initialization files is only a suggestion.

CAE_TERM. This symbol should contain the type of terminal on which CTS_PLUS
is to be executed. (vt100, vt200 for example...)

CAE_CDB. This symbol should contain the location of the CDB the user
wishes to use. This is used only when linking (LCTSP)

CAE_SHIP. This symbol should contain the name of the ship (contract),
and is used when executing LCTSP

CAE_FRAME. This symbol should contain the iteration rate of the simulator,
and it is used when executing LCTSP

CAE_MAXCDB. This symbol should contain the number of CDB's in the system.
It is used only when doing LCTSP.

CAE_IMGEXE and CAE_PTXEXE. These symbols point to a command file which is 
executed when doing a plot to a IMAGEN/PRINTRONIX device. 
If a user wishes to send his/her plot differently than the default 
he/she can write is own command file (using the default one has 
template) and put it in his own directory. Do not forget to change 
the logical name to point to the new file.

CTS_PLUS_BATCH. The hardcopy plots for CTS_PLUS are submitted as a batch job.
This logical name points to the batch queue to which the job should be
sent to.

1 MONITOR
The Monitor command is used to constantly display on screen the values
of simulation variables.

Format:
  MONITOR[/qualifier] list_of_examine_or_enquire_command [monitor_options]

When monitoring starts, the output window of CTS_PLUS will be shortened to
allow space for the monitor window. The user's input window remains
unchanged.

For each EXAMINE and ENQUIRE commands it is possible to substitute
a new label_name which will be output in the monitor window in place of
the real name of the label.
2 /COLUMN[=numeric_value]
Specifies on how many columns the monitor should be output in
the monitor window. A maximum of 2 columns is supported in CTS_PLUS.

Format:
  MONITOR/COLUMN[=numeric_value]

This feature is usefull when monitoring a lot of label or when
the user wishes to use as few lines as possible in the monitor window.

On the short side, the comment field of the label will not be displayed in
its entirety if 2 columns are used for the display.
2 /STOP
Stops current monitor, and closes the monitor window. The ouput window
is restored to its usual size.

Format:
  MONITOR/STOP
2 AT
Specifies the rate (in hertz) at which monitoring should be performed.

Format:
  MONITOR[...] AT numeric_value

example:

  CTS_PLUS> MONIT VUG AT 10   ! 10 times/sec.
2 EVERY
Specifies the period (in seconds) of two consecutive monitor samples.

Format:
  MONITOR[...] EVERY numeric_value [IT | SEC]

example

  CTS_PLUS> MONITOR VUG EVERY 0.5  ! 2 times/sec.
2 example

  12 labels are monitored here (see command format for
  info on block examine (i.e. [10)
  CTS_PLUS> MONITOR VUG,VVG,VGR(2)[10

  it is possible to use standard EXAMINE arguments as well.
  CTS_PLUS> MON E/HEX vug;E/BIN YIFREZ; ENQUIRE VFLIGHT
  2 labels are examined, and a module is Enquired...

  Label name substitution is allowed, so are expected values.
  CTS_PLUS> MON ALTITUDE:E VUG/1.0 10%
  Actual Display in the monitor window.

  ALTITUDE     WRT X VELOCITY (Feet/SEC)    (1.0)  0.56677 *
1 OLDCTS
The OLDCTS command is for putting CTS_PLUS into a compatible mode with the
old CTS program.

Format:
  OLDCTS
1 PLOT
The PLOT command is for generating plot output on the terminal
screen or on a hardcopy. Note that it is similar to the
GRAPH command. only the actual output differs. The PLOT command
has more flexibility.

Format:
  PLOT[/ALL] {[NO]CIRCLE} collection_name.label_name,... [plot_options]
  PLOT[/ALL] {{[NO]CIRCLE} collection_name.label_name,....} [plot_options]

Several arguments may be specified to specify how the plot should
be formatted.

The collection_name defaults to RESULT, which is the default
name used when collecting, so it is possible to:
  CTS_PLUS> COLLECT VUG FOR 1  ! get values of VUG for 1 sec
  CTS_PLUS> PLOT/ALL          ! outputs on screen the plots.
2 /SUBMIT
The SUBMIT option is for specifying if the print job should be started
or the plots just queued in memory until later.

Format:
  PLOT/[NO]SUBMIT=file_name

Usually, a series of plots are submitted, and for the last one the
user supplies /SUBMIT to make sure the plots are sent to the printer.
The default is /NOSUBMIT. Note that when exiting CTS_PLUS pending
plots will automatically be sent to the printer.
2 CIRCLE
This keyword can be put in front of a plot number or label name to
indicate that small circles should be placed on the plot for that label.

Format:
  PLOT.... {[NO]CIRCLE} collection_name.Label_name ....

examples:

  CTS_PLUS> PLOT CIRCLE 1, 2, CIRCLE 3 TO PRINTRONIX
  This way the first and third plots will have circles on each
  data sample, the other will not.

  It is possible as well to use the CIRCLE_ALL (or CIRCLE_NONE) option
  to select a default setting for the plots not specified. For example:
  CTS_PLUS> PLOT NOCIRCLE 1, 2, 3, 4, 5 TO PRINTRONIX CIRCLE_ALL
  will put circles on every plots except the fisrt one.

  It is possible to select the CIRCLE option on overlays as well.
  CTS_PLUS> PLOT {1, CIRCLE 2} TO PRINTRONIX
2 VS
This keyword is used to specify the label to be used on the X-axis.
It always default to TIME, which is the time at which the label(s)
on the Y-Axis were collected.

Format:
  PLOT ... VS label_name

Note that the label used on the X-Axis has to come from the same colection
as the ones from the Y-Axis.

example

  CTS_PLUS> PLOT 1 VS 2         ! plot #1 on the Y-Axis, #2 on the X-axis
  CTS_PLUS> PLOT 1 VS VUG
  CTS_PLUS> PLOT 1 VS TIME      ! this is the default anyway.
2 XTITLE
This keyword is for defining the comment which should be used to reference
the X-Axis label. By default, the comment field (if available) of the label
from the CDB is used.

Format:
  PLOT ... XTITLE quoted_string

example:

  CTS_PLUS> PLOT 1 XTITLE "This is a test."
2 YTITLE
This keyword is for defining the comment which should be used to reference
the Y-Axis label. By default, the comment field (if available) of the label
from the CDB is used.

Format:
  PLOT ... YTITLE quoted_string

example:

  CTS_PLUS> PLOT 1 YTITLE "This is a test."
2 XRANGE
This is to define the range of values which should be included in the
plot on the X-axis. By default the value used are the minimum and maximum
of the values collected.

Format:
  XRANGE numeric_value {TO} numeric_value

example:

  we suppose that the values collected on the X-axis are ranging from
  0 to 10.0 (seconds we suppose) and we want to output only the first 
  5 (seconds)

  CTS_PLUS> PLOT 1 XRANGE 0 TO 5
2 YRANGE
This is to define the range of values which should be included in the
plot on the Y-axis. By default the value used are the minimum and maximum
of the values collected.

Format:
  YRANGE numeric_value {TO} numeric_value

example:

  we suppose that the values collected on the Y-axis are ranging from
  0 to 10.0 but we want to have an axis ranging from -10 to 10:

  CTS_PLUS> PLOT 1 YRANGE -10 TO 10
2 GRID
To indicate if a square grid should be output as a background for the plot.
By default none is output.

Format:
  PLOT ... [NO]GRID

example:

  CTS_PLUS> PLOT 1 GRID
2 INTERPOLATE
To indicate solid lines should be output on the plot between samples.
By default it is done.

Format:
  PLOT ... [NO]INTERPOLATE

example:

  CTS_PLUS> PLOT 1 NOINTERPOLATE
2 EXTEND
To indicate that the Y-Axis should englobe the minimum and maximum of the
values supplied, and not reflect the range normaly supplied.

Format:
  PLOT ... [NO]EXTEND

example:

  CTS_PLUS> PLOT 1 EXTEND
2 TIME_SHIFT
This is to define a shift value for the X-axis values.

Format:
  PLOT ... TIME_SHIFT numeric values

example:

  Let us say that the values on the X-axis are ranging from 10 to 15,
  but that we want values from 0 to 5 to be output instead...
  CTS_PLUS> PLOT 1 TIME_SHIFT -10
2 VALUE_OF_X_INTERVAL or P1
This option is for defining the value assigned to 1 X-axis interval on the
plot. 

Format: 
  PLOT ... {VALUE_OF_X_INTERVAL | P1} numeric_value

example:

  CTS_PLUS> PLOT 1 TO PRINT P1 4.0
  Each intervals on the X-axis will be equal to 4.0
2 NUMBER_OF_Y_INTERVALS or P2
Defines the number of intervals on the Y-Axis.

Format: 
  PLOT ... {NUMBER_OF_Y_INTERVALS | P2} numeric_value

example:

  CTS_PLUS> PLOT 1 TO PRINT P2 10
  There will be 10 intervals on the Y-Axis.
2 X_LENGTH_OF_10_INTERVALS or P3
This argument contains the physical length on the piece of paper of the
plot output of 10 X-Axis intervals (in inches or centimeters).

Format: 
  PLOT ... {X_LENGTH_OF_10_INTERVALS | P3} numeric_value {IN | CM}

example:

  CTS_PLUS> PLOT 1 TO PRINT P3 10 IN
  10 intervals on the X-Axis will be equal to 10 inches on the plot.
2 Y_LENGTH_OF_10_INTERVALS or P4
This argument contains the physical length on the piece of paper of the
plot output of 10 Y-Axis intervals (in inches or centimeters).

Format: 
  PLOT ... {Y_LENGTH_OF_10_INTERVALS | P4} numeric_value {IN | CM}

example:

  CTS_PLUS> PLOT 1 TO PRINT P4 10 IN
  10 intervals on the 4-Axis will be equal to 10 inches on the plot.
2 CIRCLE_ALL
Indicates that circles should be output on the plot for each sample.

Format:
  PLOT ... CIRCLE_ALL
  PLOT ... CIRCLE_NONE

See option CIRCLE (NOCIRCLE) for more information.
2 CIRCLE_NONE
Indicates that circles should not be output on the plot for each sample.

Format:
  PLOT ... CIRCLE_NONE
  PLOT ... CIRCLE_ALL

See option CIRCLE (NOCIRCLE) for more information.
2 NUMBER_OF_CIRCLES
This argument is to specified the number of circles which should be output
on the plot if some CIRCLE options are on.

Format:
  PLOT ... NUMBER_OF_CIRCLES numeric_value

This argument can be used if the output seems to be too loaded with circles
for instance.

example:

  CTS_PLUS> PLOT 1 TO PRINT NUMBER_OF_C 30
2 TO
This keyword is for specifying the device on which to send the plot
output

Format:
  PLOT .... TO PRINTRONIX
  PLOT .... TO IMAGEN
  PLOT .... TO SCREEN (default)

Two types of printer are supported by CTS_PLUS currently: PRINTRONIX and
IMAGEN. Of course the printer you select has to be present on site.
2 TITLE
This is for specifing a title for a PLOT or GRAPH that will be
output at the top of the page.

Format:
  PLOT ... TTTLE quoted_string

example:

  CTS_PLUS> PLOT 1 TITLE "This is a test."
2 INITIAL_CONDITIONS
This keyword is for specifying if initial conditions should be
output on the plot output, and from which result of collection.

Format:
  PLOT... INITIAL_CONDITIONS {collection_name}

If no collection_name is given the one that is used is the one from
which the labels to be plotted come from.

example:

  CTS_PLUS> PLOT RESULT.1 INITIAL
  the initial conditions of collection RESULT will be output.

  CTS_PLUS> PLOT RESULT.1 INITIAL TMP
  the initial conditions of collection TMP will be output.
2 examples

These are a few samples of PLOT commands.

We suppose here that we have 2 results of collection in memory: 
RESULT, and TEMP.

  CTS_PLUS> PLOT/ALL           ! all the data collected in
                               ! RESULT is output on screen
  CTS_PLUS> PLOT/ALL TEMP      ! all the data collected in
                               ! TEMP is output on screen
  CTS_PLUS> PLOT/ALL TO PRINT  ! same, but output is sent to the
                               ! printer. (PRINTRONIX)
  CTS_PLUS> PLOT 2, TEMP.1     ! the second PLOT from collection
                               ! RESULT and the first one from
                               ! collection TEMP are output.
  CTS_PLUS> PLOT {2, TEMP.1}, 3
                               ! the second plot from collection 
                               ! RESULT and the first one from TEMP
                               ! are overlaid, and the third plot
                               ! from collection RESULT is output.
  CTS_PLUS> PLOT VUG           ! it is possible to reference to plot
                               ! by its name as well: label VUG from
                               ! collection RESULT will be plotted here.

  CTS_PLUS> PLOT VUG VS TIME   ! does the same as above. TIME is
                               ! the default for the X-axis of the plot
  CTS_PLUS> PLOT VUG VS VWG    ! it is possible to do cross-plot.
  CTS_PLUS> PLOT VUG INITIAL   ! outputs initial conditions.

Also a lot of arguments may be used to format the PLOT

  CTS_PLUS> PLOT/ALL INTERPOLATE CIRCLE_ALL GRID TITLE "This is a test"
1 PUT
The PUT command is for writting result of collections to disk.

Format:
  PUT[/qualifier] [collection_name.label_name [file_spec]] [VS label_name]

When running tests, the plot data is kept in memory, so when leaving
CTS_PLUS all the data is lost. The PUT command is for saving those 
results. (to be used by other utilities, for instance).

Two file format are supported: ASCII (File extension .VIS)
                               and BINARY (File extension .BVS).

An ASCII file can contain only one label data. This type of file
can be used by VISA for instance to create high resolution screen
graphs. They can also be used as ramps for CTS_PLUS

The Binary files may be used by a utility called OVP.
2 VS
The VS option is for specifying another time label for the file(s) to be
saved.

Format:
  PUT ... VS label_name

By default the time (label#0) is used for the X-Axis label. It is possible
to use this to save a plot to disk with another label on the X-AXIS

example:
   
  CTS_PLUS> PUT 1 VS 2
  CTS_PLUS> PUT 1 VS VUG   ! it is possible to reference the label
                           ! by name or number.
2 /ALL
All the result of collections are saved, or when a result of collection
is specified all the labels from this object are saved in the file(s)

Format:
  PUT/[NO]ALL [object_name [file_spec]]

2 /ASCII
Specifies that the labels should be saved in an ASCII (.VIS) file.

Format:
  PUT/ASCII  ...
  PUT/BINARY  ...
2 /BINARY
Specifies that the labels should be saved in a binary (.BVS) file.

Format:
  PUT/ASCII  ...
  PUT/BINARY  ...

Note that the content of the file saved cannot be displayed on screen.
2 /TIME_SHIFT
Specifies a time shift value to apply to the resulting labels.

Format:
  PUT/TIME_SHIFT=numeric_value

See the TIME_SHIFT option of the PLOT command for more information.
2 example

  Let us say we are running these tests.
  CTS_PLUS> COLLECT VUG,VVG FOR 1 IN RESULT
  CTS_PLUS> COLLECT VWG FOR 1 in TEMP

  And we want to save those to disk.
  CTS_PLUS> PUT/ALL
  file CAE_SHIP:RESULT1C.VIS;1 created.
  file CAE_SHIP:RESULT2C.VIS;1 created.
  file CAE_SHIP:RESULT3C.VIS;1 created.

  CTS_PLUS> PUT 1, TEMP.1
  file CAE_SHIP:RESULT1C.VIS;2 created.
  file CAE_SHIP:TEMP1C.VIS;1 created.

  CTS_PLUS> PUT/ALL TEMP
  file CAE_SHIP:TEMP1C.VIS;2 created.

  it is possible to supply a file name
  CTS_PLUS> PUT 1 MYFILE, 2 MYOTHERFILE
  file CAE_SHIP:MYFILE1C.VIS;1 created.
  file CAE_SHIP:MYOTHERFILE2C.VIS;1 created.
  CTS_PLUS> PUT/ALL TEMP CAE_CDB:RESULT
  file CAE_CDB:RESULT1C.VIS;1 created.

  And it is possible to save a file, and use another label as
  the X-Axis.
  CTS_PLUS> PUT 1 VS 2
  file CAE_SHIP:RESULT1C.VIS;3 created.

  The syntax is the same for BINARY files, except that the labels
  from the same collection are saved in the same file.
  CTS_PLUS> PUT/BIN RESULT MYFILE
  file CAE_SHIP:MYFILE.BVS;1 created.
1 RAMP
The RAMP command is for assigning values to simulation variables over
a period of time.

Format:
  RAMP[/qualifier]...label_name 
    {ramp_table | 
     ( expression ) |
     Result_Of_Colection_Object_Name.label_name}

Three forms of RAMPs are supported: Ramp Tables, Expressions, Playback.

Note that only one RAMP operation may be defined in a single command
call (comma operator is not supported) and that the RAMP command
is legal only in a DRIVE command (see that command for more information).
2 Ramp_Tables
A ramp_table is a series of instructions that defines values for
simulation variables as a function of time.

Format:
  [numeric_value { AT | FOR } numeric_value { IT | SEC }]....

A Ramp_Table entry is a simulation variable value which the simulation
variable should reach at a specific time:
  1.1 AT 10 SEC

Values are linearly interpolated form one value to another when 
necessary.

The time at which the value should be reached may be absolute 
(from the begining of the ramping) or relative (to the previous 
table entry):
  1.1 AT 10 SEC  or,
  1.1 FOR 10 SEC

The time value may be specified in seconds or number of iterations:
  1.1 AT 10 SEC  or,
  1.1 FOR 200 IT

Note that it is possible to simplify the data entry to its simplest 
format:
  1.1 AT 10 SEC is the same as
  1.1 A10S

Also note that the expression evaluator is available in the
ramp tables.
  ( SIN(18.2)+2 ) AT 10 SEC is legal, 
  but simulator variables references are not allowed.

3 example
Full Ramp Table example:

  CTS> RAMP VUG 1.1 AT 1 SEC, 2.2 AT 2 SEC, -1.0 FOR 2 SEC -
                10.2 FOR 100 IT

  CTS> ! VUG will equal 1.1 one second after the begining of the ramping
  CTS> !                2.2 one second later,
  CTS> !               -1.0 two seconds later,
  CTS> !               10.2 100 iterations later.
2 expression
It is possible to continuously assign a value to a label by
ramping an expression.

Format:
  ( expression )

For the full syntax of the 'expression' see the 'command_format'
section.

Note that ramping duration is always defined in the DRIVE command,
(ramps are parts of DRIVE command).

Example:
  CTS> RAMP VUG (VVG+VWG)
2 Playback
Values of previously COLLECTed data may be ramped back to a simulation
variable.

Format:
  Result_Of_Collection_Object_Name.Label_Name

For example, if there is a Result_Of_Collection_Object named
'RESULT' in CTS, and in that object label VUG was collected it is
possible to:

  CTS> RAMP VVG RESULT.VUG

See the section on 'cts_objects' for more information on
Result_Of_Collection_Objects. See also the COLLECT command.
2 /AFTER
With this qualifier, the ramping operation is performed after all the
modules are called. See primitive_command section for more information
on that.

Format:
  RAMP/AFTER
  RAMP/BEFORE
2 /BEFORE
With this qualifier, the ramp operation is performed before all the
modules are called. See primitive_command section for more information
on that.

Format:
  RAMP/BEFORE
  RAMP/AFTER
1 REFRESH
The REFRESH command is for rewriting the whole CTS_PLUS screen.

Format:
  REFRESH

Note that typing Control-W in CTS_PLUS does that function.
1 REMOVE
The REMOVE command is for deleting a result of collection

Format:
  REMOVE[/qualifier] [object_name]

Of course this command only removes internal objects, not disk files.

When doing Test in CTS_PLUS it is possible to store results in different
object, (which will result in different files if the results are kept
using the PUT command). But CTS_PLUS can store only 10 different result
of collections at the same time, so the user may be forced
to remove one (or all) from memory in order to run new tests.

example:

  CTS_PLUS> COLLECT VUG FOR 1
  Collect Error: Out of storage place for result of collection.
  ! here there is no more memory available to run the test.
  CTS_PLUS> PUT RESULT
  ! now we save one to disk...
  CTS_PLUS> REMOVE RESULT
  ! we remove one from memory. and now we can run the test again.
  CTS_PLUS> COLLECT ........
2 /ALL
Specifies that all the objects should be removed.

Format:
  REMOVE/[NO]ALL
2 example

  CTS_PLUS> REMOVE EXAMPLE
  CTS_PLUS> ! result of collection example is deleted from memory.
  CTS_PLUS> REMOVE/ALL
  CTS_PLUS> ! all the result of collections are removed
1 RUN
The RUN command is for unfreezing one or several simulation module(s).

Format:
  RUN[/qualifier]... [module_name],...

Several modules may be unfrozen on one call just by seperating them with
commas.

Note: Of course if the simulation Freeze Flag is on the modules will
still not be unfrozen.
2 /AFTER
With this qualifier, the module(s) are unfrozen after all the
modules are called. See primitive_command section for more information
on that.

Format:
  RUN/AFTER
  RUN/BEFORE
2 /ALL
All the modules are unfrozen.

Format:
  RUN/ALL
2 /BEFORE
With this qualifier, the module(s) are unfrozen before all the
modules are called. See primitive_command section for more information
on that.

Format:
  RUN/BEFORE
  RUN/AFTER
1 SAY
The SAY command is for outputing text on CTS_PLUS output window.

Format:
  SAY {numeric_value | expression | quoted_string}

The SAY command may be used to output messages in the output window (useful
especially in command files). It can also be used as a calculator, since
expressions may be included in a SAY statement.

Note that simulation variables name may not be included in expressions.
Use the EXAMINE command for that purpose.
2 examples
  The Say command may be used as a calculator
  CTS_PLUS> SAY 4.4 + SIN(23.1)

  Or to output messages.
  CTS_PLUS> SAY "HELLO WORLD"
1 SET
The SET command is used for changing parameters in the current CTS_PLUS
session.

Format:
  SET set_parameter...
2 DEGREE
The SET DEGREE command is for setting the angle mode of expression 
evaluation in CTS_PLUS.

Format:
  SET DEGREE
  SET RADIAN
2 DEFAULT
The SET DEFAULT command is for modifying the default qualifiers of a 
CTS_PLUS command.

Format:
  SET DEFAULT [cts_command[/qualifiers...],...]

If no qualifier is given to the specified cts_command, then the default
startup qualifiers (the ones used when the CTS_PLUS session begins) are 
used.

example:

  CTS_PLUS> SET DEFAULT EXAMINE/MAX_PRECISION

  Now when using the EXAMINE command, it will default automatically
  to the MAX_PRECISION qualifier, unless given another qualifier
  explicitly.
2 DIRECTORY
The SET DIRECTORY command is for changing the default directory.

Format:
  SET DIRECTORY file_spec
2 INPUT
The SET INPUT command is for changing the size of the input window

Format:
  SET INPUT=numeric_value
2 RADIAN
The SET RADIAN command is for setting the angle mode of expression
evaluation in CTS_PLUS.

Format:
  SET RADIAN
  SET DEGREE
2 SCOPE
The SET SCOPE command is for setting the default scope to be applied
to a label name.

Format:
  SET SCOPE

This scope is automaticly added to a label name if no scope is specified
for that label name. 

example:

  CTS_PLUS> EXAMINE VFLIGHT.VUG

  is the same as

  CTS_PLUS> SET SCOPE VFLIGHT
  CTS_PLUS> EXAMINE VUG

For accessing common database variable, the scope should be "CDB" or "XREF".
This is of course only a predifined name, to help the user access CDB 
variables more easily, and does not refer to a real simulation module.
2 TASK
The SET TASK command is for setting CTS_PLUS in a mode in which input of
command is allowed while monitors and/or test are running.

Format:
  SET TASK
  SET NOTASK

When SET NOTASK is done, a monitor or a test while cause CTS_PLUS to
freeze its input and wait for CONTROL-C to interrupt the command
(in the case of a monitor).
2 VERIFY
The SET VERIFY command is used for showing lines from a command file as
they are being executed.

Format:
  SET VERIFY
  SET NOVERIFY
2 PROMPT
The SET PROMPT is used to change the input prompt "CTS_PLUS> "

Format:
  SET PROMPT quoted_string

example:
  CTS_PLUS> SET PROM "."
  .!now a dot is the default prompt.
2 MODULE
The SET MODULE is used to check if symbols are available for
a specific module

Format:
  SET MODULE[/ALL] module_name

example:
  CTS_PLUS> SET MODULE/ALL
  CTS_PLUS> SHOW MODULE
  ! will display all the modules and if the local symbols are
  ! available or not.
  CTS_PLUS> SET MODULE VFLIGHT
  CTS_PLUS> SH MODULE VFLIGHT
  ! same, but for 1 module only
1 SHOW
The SHOW command is used for displaying on screen information about the
current CTS_PLUS session.

Format:
  SHOW show_parameter...
2 CONNECTION
The SHOW CONNECTION command is used to display information about
the current CTS_PLUS common database/external server connection. See the
CONNECT command for more information about connections.

Format:
  SHOW CONNECTION

Here is an example of SHOW CONNECTION result.

CTS_PLUS: Connected to         <TEST>              ! name of connection
          Common database is   /cae1/ship/sctb     ! CDB name.
          Image is             /cae1/ship/test     ! executable name
          Rate is              30 Hertz            ! simulation rate.
          Simulation is        Background          ! background/foreground
          Local Symbol access  ON
2 DEFAULT
The SHOW DEFAULT command is for showing the default qualifiers for one
or several CTS_PLUS commands.

Format:
  SHOW DEFAULT [cts_command,...]

If no argument is given the all the CTS_PLUS command and their default
qualifiers will be displayed on screen.
2 DIRECTORY
The SHOW DIRECTORY command is for showing the default work directory.

Format:
  SHOW DIRECTORY
2 MODULE
The SHOW MODULE is for showing information about the modules of the
simulation program executing.

Format:
  SHOW MODULE [module_name,...]

If no argument is given for this command, all the modules of the current 
connection's executable will be displayed.

The information given by this instruction is as follows:

  - Module name
  - Local symbol access for that module
  - Module's programming language.

example:

  > SHOW MODULE VFLIGHT

  Module Name                              Symbols Language
  VFLIGHT                                  YES     FORTRAN
2 SCOPE
The SHOW SCOPE command is for showing the default scope to be applied
to a label name.

Format:
  SHOW SCOPE

See SET SCOPE for more informations about scopes.
2 SYMBOL
The SHOW SYMBOL command is for displaying the symbols defined (using the
command DEFINE or INPUT) during the current CTS_PLUS session.

Format:
  SHOW SYMBOL [symbol_name,...]

Wildcard characters ('*', '?') are supported, so "SHOW SYMBOL *" will
dump on screen the entire symbol table.
1 SPAWN
The SPAWN command is for executing one or several DCL commands

Format:
  SPAWN/[NO]PROMPT [dcl_command]

If no command is typed after SPAWN, the user will go back to DCL.
When his DCL session ends, CTS_PLUS will get the control back.
2 /PROMPT
This argument is for indicating if a prompt asking the user to
<HIT RETURN TO CONTINUE> should be output after the spawn completes.
It is usefull, because CTS_PLUS redraws the screen after a
spawn command is executed, so output of the spawned command is lost.

Format:
  SPAWN/[NO]PROMPT
1 STOP
The STOP command is for halting the execution of a command
file, with the possibility of resuming its execution later
(using the CONTINUE command).

Format:
  STOP

When this command is executed the user gets back into
normal keyboard input mode, except that the prompt changes
to "STOPPED>", to remind him that there is a command
file pending to be executed.
2 example
This is a comamnd file which uses the STOP command.
  CTS_PLUS> @example ! executes command file EXAMPLE.CTS
  DEP VUG 1.0
  DEP VVG 2.1
  STOP
  STOPPED> ! now the user may type in some commands
  STOPPED> E VUG
  STOPPED> CONTINUE ! executes the remainder of the command file
  COLLECT VUG FOR 1
  ....
1 TEST
The TEST command is used to initiate constant gathering and/or 
ramping (depositing) of simulation variables over a period of time.
It is actually a combination of the DRIVE and the COLLECT commands.

Format:
  TEST[/qualifier] [module_name,...] 
                   [COLLECT collect_command] [DRIVE drive_command]
                   [test_options]

This command is used in fact to coordinate the operation of a
COLLECT and a DRIVE command. Please refer to those commands for
more information.

It is possible to include a list of modules to be run
in the TEST by typing them after the command qualifiers, example:

  CTS_PLUS> TEST VFLIGHT,UFI .....
  VFLIGHT and UFI (and no others) will be included in the test.
  By default the current included modules are taken
  (see the RUN and HOLD commands).

  It is also possible to include automatically all the modules.
  CTS_PLUS> TEST/ALL .....

When the test complete, the values of the COLLECT command are 
stored in memory in a Result Of Collection.
They may then be saved to disk (in VISA or OVP compatible format), or
plotted.
2 /ALL
To include all the modules in the current test.

Format:
  TEST/ALL
2 /FREEZE_FLAG=[label_name]
Specifies the name of the Freeze Flag label which should be used
when starting/ending the test. When a test starts CTS_PLUS will always
deposit FALSE into this label, and TRUE at the end.

Format:
  TEST/FREEZE_FLAG=[label_name]

CTS_PLUS gives a default value (YIFREZ) for this label, but it is 
possible to change it on the fly using the qualifier, or to change it 
permanently using to SET DEF command.

  CTS_PLUS> SET DEF TEST/FREEZE=YISPARE

The label used has to be declared as a LOGICAL (or BYTE).
2 /ITERATION_COUNTER=[label_name]
Specifies the name of the Iteration Counter label which should be used
when running the test.

Format:
  TEST/ITERATION_COUNTER=[label_name]

CTS_PLUS gives a default value (CTSTSTNO) for this label, but it is 
possible to change it on the fly using the qualifier, or to change it 
permanently using to SET DEF command.

  CTS_PLUS> SET DEF TEST/IT=YISPARECNT

The label used has to be declared as a INTEGER*4
2 /TIME_LABEL=[label_name]
Specifies the name of the Time label which should be used
when running the test (The label containing the the TIME in seconds).

Format:
  TEST/TIME_LABEL=[label_name]

CTS_PLUS gives a default value (CTSTSTTM) for this label, but it is 
possible to change it on the fly using the qualifier, or to change it 
permanently using to SET DEF command.

  CTS_PLUS> SET DEF TEST/TIME=YISPARETI

The label used has to be declared as a REAL*4
2 /STOP
Stops current test.

Format:
  TEST/[NO]STOP

Data collected so far will be made available to the user.
2 /WAIT
Indicates if the CTS_PLUS should be frozen or not while the TEST
runs

Format:
  TEST/[NO]WAIT

The /WAIT is very useful when executing CTS_PLUS command files. If not
used, the CTS_PLUS commands following the TEST command in the file will 
be executed even though the operation has not completed.
2 /PROMPT
This argument is used to collect data not at some rate, but when the
user indicates it (by hitting RETURN).

Format:
  TEST/[NO]PROMPT .....

When the test starts the user gets a prompt:

  <HIT RETURN TO GET A SAMPLE>

And the prompt will return as long as the test lasts. To end the test
just hit CTRL-Z (or type EXIT). Default is NOPROMPT.
2 /START_PROMPT
This argument is used to force a test to start when the user hits return 
only. By default the test will start after the user enters the test command,
but sometimes a user may wish to set some switches before starting the
actual test, and if the test is in a command file he/she will not
have time to do this. This is where this feature might be used.

Format:
  TEST/[NO]START_PROMPT .....

When the test starts the user gets a prompt:

  <HIT RETURN TO START>

The user should hit return when he/she is ready to go.
2 /LIMIT
This is for indicating the maximum duration of a test (in iteration)

Format:
  TEST/LIMIT=numeric_value

The default is for 500 samples. If no value are given for the rate of
collection, CTS_PLUS will automatically change it so that the test
covers a total of this many iterations.

example:

  CTS_PLUS> TEST/LIMIT=100 COLLECT VUG FOR 2000 IT
  here CTS_PLUS will collect data every 20 iterations for a total of
  100 samples.

  CTS_PLUS> COLL/LIM=100 COLLECT VUG FOR 2000 IT EVERY 1 IT
  here CTS_PLUS will ignore the LIMIT argument, and gather 2000 samples.
2 /COLUMN
Specifies on how many columns the monitor should be output in
the monitor window. A maximum of 2 columns is supported in CTS_PLUS.

Format:
  MONITOR/COLUMN[=numeric_value]

This feature is usefull when monitoring a lot of label or when
the user wishes to use as few lines as possible in the monitor window.

On the short side, the comment field of the label will not be displayed in
its entirety if 2 columns are used for the display.

This argument goes along with the MONITOR options of the TEST command.
(it is possible to start a monitor while a test is running.
2 /GRAPH
This is for indicating is a graph should be automatically output
at the end of the TEST. 

Format:
  TEST/[NO]GRAPH={SCREEN, IMAGEN, PRINTRONIX}

This option is mostly used for to automatically send plot output to screen.
It is not very practical for hardcopy because the TEST command
does not provide any way to format the graph like the GRAPH or the PLOT
commands provide.

example:

  CTS_PLUS> TEST/GRAPH VUG VVG FOR 10
  Note that the /GRAPH option forces CTS_PLUS to freeze the input while
  the test is running like the /WAIT option.
2 AT
Specifies the rate (in hertz) at which collecting/driving should 
be performed.

Format:
  COLLECT[...] AT numeric_value
  COLLECT[...] COLLECT_AT numeric_value
  COLLECT[...] DRIVE_AT numeric_value
  COLLECT[...] MONITOR_AT numeric_value

By default it is performed every iteration, if possible.

Note that AT applies to all the operation of the TEST (COLLECT, DRIVE,
MONITOR). But individual keywords for these function are also
available.

  CTS_PLUS> TEST COLLECT VUG AT 30     ! 30 times / sec.
2 EVERY
Specifies the period (in seconds or iteration) of two 
consecutive collection/ramp operation.

Format:
  TEST[...] EVERY numeric_value [IT | SEC]
  TEST[...] MONITOR_EVERY numeric_value [IT | SEC]
  TEST[...] COLLECT_EVERY numeric_value [IT | SEC]
  TEST[...] DRIVE_EVERY numeric_value [IT | SEC]

By default it is performed every iteration.

Note that EVERY applies to all the operation of the TEST (COLLECT, DRIVE,
MONITOR). But individual keywords for these function are also
available.

  CTS_PLUS> TEST COLLECT VUG EVERY 10.0  ! every 10 seconds.
2 FOR
Specifies the duration (in seconds or iterations) of the test

Format:
  TEST[...] FOR numeric_value [IT | SEC]

By default the test lasts for 500 iterations.

  CTS_PLUS> TEST COLLECT VUG FOR 1000 IT

2 WHEN
Specifies the starting condition of the test.

Format:
  TEST[...] WHEN (expression)

The format of the expression is explained in the 'command_format'
section. Of course to make sense, this expression must evalute to
TRUE or FALSE.

The test will only begin when the expression evaluates to TRUE.

  CTS_PLUS> TEST COLLECT VUG WHEN ((VUG > 0.0) AND NOT YIABORT)
2 UNTIL
Specifies the stopping condition of the test.

Format:
  TEST[...] UNTIL (expression)

The format of the expression is explained in the 'command_format'
section. Of course to make sense, this expression must evalute to
TRUE or FALSE.

The collection will end when the expression evaluates to TRUE.
(or when the duration (FOR argument) is exceeded).

  CTS_PLUS> TEST COLLECT VUG UNTIL ((VUG > 0.0) AND NOT YIABORT)
2 IN
Specifies the name of the result of collection object into which
the collected data should be stored.

Format:
  TEST[...] IN object_name

This argument is optional, if not specified the data will be saved
in a collection named RESULT, and since this name is the default
object name for the PLOT and PUT command as well object name
can be ignored all together. They are usefull however when the
user whishes to have a lot of plot result in memory at the same time
(RESULT will be able to contain only 25 plots at a time, if other
objects are used that figure jumps to 250).

  CTS_PLUS> TEST COLLECT VUG FOR 1 IN MAC
  Collection MAC completed with 30 samples.

  Now MAC can be plotted for instance.
  CTS_PLUS> PLOT/ALL MAC
2 INITIALLY and FINALLY
Specifies a series of command to be executed before (or after) the test
starts.

Format:
  TEST[...] INITIALLY | FINALLY
                         {[label]:examine_command
                          [label]:deposit_command
                          [label]:hold_command
                          [label]:run_command;...}

This option is used to mostly to get initial conditions (to
be later displayed on the plot), but it may also be used
to initialize label values.

example:
  This will automatically examine those values and include them
  in the plot (if the user plots it).
  CTS_PLUS> TEST COLLECT VUG INITIALLY VBRAKE, VWG FOR 10 SEC
  result will be displayed in hex on the plot
  CTS_PLUS> TEST COLLECT VUG INITIALLY E/HEX VBRAKE, VWG FOR 10 SEC
  VUG will be initialized to 1.0 before the test starts
  CTS_PLUS> TEST COLLECT VUG INITIALLY E/HEX VBRAKE, VWG; -
  _CTS_PLUS> DEP VUG 1.0 FOR 10 SEC
2 COLLECT
A legal COLLECT command can be imbeded in a TEST command.

Format:
  TEST[...] COLLECT collect_command

The collect_command syntax is the same as the COLLECT command,
and is covered in that section.

example:
  CTS_PLUS> TEST COLLECT VUG,VVG,VWG FOR 10.5
2 DRIVE
A legal DRIVE command can be imbeded in a TEST command.

Format:
  TEST[...] DRIVE drive_command

The drive_command syntax is the same as the DRIVE command,
and is covered in that section.

example:
  CTS_PLUS> TEST DRIVE VUG 1.0 @10.0; 1.1 @15.0 FOR 15.0
2 MONITOR
A legal MONITOR command can be imbeded in a TEST command.

Format:
  TEST[...] monitor_command

It is sometime useful to have a monitor running whil a test
runs. This feature of the test command allows a monitor to start
when the TEST command is executed.

The monitor_command syntax is the same as the MONITOR command,
and is covered in that section.

example:
  CTS_PLUS> TEST MONITOR VUG,VVG COLLECT VUG,VVG,VWG FOR 1
2 example

  Since the TEST command is a combination of the COLLECT, MONITOR
  and COLLECT command, you could consult the examples of those commands.
  However here is an example of the combination of all those functions.

  Let us say we want to collect 2 labels and ramp a couple of
  label as well. We also want to monitor some labels. We want
  only module CFORCES and VFLIGHT to be included.

  Define a couple of ramp tables.
  CTS_PLUS> DEF TAB1 "VM 0.5 $1.0; 1.0 $2.5"
  CTS_PLUS> DEF TAB1 "VUG 1.3 $1.0; -1.1 $2.5"
  Start a test
  CTS_PLUS> TEST CFORCES,VFLIGHT DRIVE RAMP TAB1; RAMP TAB2 - 
  _CTS_PLUS> COLLECT VUG,VVG MONITOR VUG[10 FOR 100.0 EVERY 10 IT
  Test wil last 100 seconds, samples will be taken every 10 
  iterations.
1 TYPE
The TYPE command is for displaying the content of a text file.

Format:
  TYPE file_spec

It is often used in CTS_PLUS to display result of tests.
example:
  CTS_PLUS> COLL VUG FOR 1
  CTS_PLUS> PUT/ALL
  CAE_SHIP:RESULT1C.VIS created.
  CTS_PLUS> TY RESULT1C.VIS
  ! this will output the content of the file on screen.
1 UNDEFINE
The UNDEFINE command is for removing symbols from the current CTS_PLUS session.

Format:
  UNDEFINE symbol_name,...

Wildcard characters ('*', '?') are supported in the symbol name, so
it is possible to delete all the symbols with the command:

  CTS_PLUS> UNDEFINE *

And it is possible to undefine specific symbols:

  CTS_PLUS> UNDEFINE Y*   ! all symbols begining by "Y" will be remove.
1 WAIT
The WAIT command is used for pausing the CTS_PLUS utility for a specific
period of time.

Format:
  WAIT [numeric_value_in_seconds] 

Once the WAIT command has been entered, the CTS_PLUS user interface will 
be frozen, and no commands may be typed. But the MONITOR and TEST functions 
are still allowed to run, and are updated on screen.

The WAIT command may be aborted by Control-C. Using the WAIT command with 
no argument will make the utility pause until Control-C is typed.

This command is useful to slow down execution of a command file.

Note that typing Control-D in CTS_PLUS does that function.
