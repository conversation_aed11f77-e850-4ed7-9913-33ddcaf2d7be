1 HELP
   The PFU utility retrieves on-line  help text messages and displays
 information on the user terminal.

   Input_Syntax

  HELP

   Processing

   PFU provides a help retrieval routine which returns help information
 from the PFU.HLP help library.

   Outputs

   Help text is displayed on the user terminal.  Additional information
 may be obtained by typing HEL<PERSON> [ sub-topic ] or <CR> to exit  from  the
 HELP session.

   Special_Requirements

   The pfu.hlp help library is assumed to be in the directory pointed to 
   by logical name cae_help.


1 Purpose
 This document is the UNIX PFU Utility Program Performance Specification.

1 Scope
   The object of this document is to describe the Program performance
 Utility (PFU) on UNIX Based Simulators.

1 Documents
  General User Manual volume 1, chapter 15

1 Requirements
   General

   The following section defines and specifies functional , operational
 and performance requirements of the PFU Utility.

   Description

   The  PFU  Program performance Utility described in this document is
 designed to operate on any IRIS based machines running IRIX V3.0 or 
 and higher releases, and on any RISC/6000 IBM AIX ******** machine.

2 Equipment
 When  timings are performed by the PFU utility ,the UNIX Interval timer
 is read and the sum of both ,the  internal clock and the Interval
 timer reading  brings the timing resolution to 1 us.

2 Interface
   The PFU utility interfaces with any one of the  Asynchronous  or
 Synchronous foreground dispatchers.

2 Functional
   The PFU utility is a background program that interfaces directly
 with the Synchronous and Asynchronous dispatchers. Communication  is
 established through a reserved  area in the installed PFUBLK
 Global Partition. When performing timings ,the  PFU utility request
 clock readings from the dispatcher .When all readings are returned,
 the utility computes  all  statistical information and displays the
 results in a formatted form.

   No additional equipment is required by the utility.

2 Requirements
   The PFU Utility is used to perform the following functions :

      Perform timings on the foreground
      Help in organizing the scheduling sequence of modules
      Help in debugging the foreground

1 Performances
   A list of functions performed by the PFU utility is given below :

     Time MODULE execution time in specified process
     Time BAND execution time in specified process
     Time LEG execution time in specified process
     Time MODULE scheduling period in specified process
     Time BAND scheduling period in specified process
     Time LEG scheduling period in specified process
     SUMMARY of PROGRAM timings in specified process
     SUMMARY of BAND timings in specified process
     SUMMARY of LEG timings in specified process
     Link a MODULE in specified Process
     Link a BAND in specified Process
     Unlink a MODULE in specified Process
     Unlink a BAND in specified Process
     Move a MODULE band to band in specified process
     Swap a BAND leg to leg in specified process
     List a BAND in specified Process
     List every BAND in specified Process
     List a LEG  in specified Process
     List every LEG in specified process
     Find a MODULE in any process
     Change base iteration rate of the foreground
     Display simulator STATUS
     Freeze simulator
     Unfreeze simulator
     Set overrun check
     Set PRINT mode to get HARDCOPY listing of timings
     Set NOPRINT mode to suppress HARDCOPY listing of timings
     Set SYNCHRONOUS mode
     Set ASYNCHRONOUS mode
     Set PROCESS to specific task
     Show running processes
     MONITOR spare time in processor
     MONITOR overruns in processor
     MONITOR MODULE Timing in specified processor
     MONITOR BAND Timing in specified processor
     MONITOR LEG Timing in specified processor
     SHELL Execute a specific shell command 
     On line load facility for patching a modified simulation
     module without having to do a F/G reload or relink.


     Note:

       In the following section , entries delimited by { } are
       compulsory while entries delimited by [ ] are optional.
1 TIME
   With this option,you can compute and display foreground
 performances.

2 PROGRAM
   With this option,you can compute and display the execution time
 required by any foreground MODULE.

   Input_Syntax

   TIME         {Module Name} [Number of Samples]
   TIME PROGRAM {Module Name} [Number of Samples]

   The Number of Samples must be in the range of 2-98 , any other
 value  specified is illegal and cause the command to be rejected.
 If Number of Samples is omitted ,a default of 20 samples will be
 considered.

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command) receives and services the request by reading a real time
 clock before and after  scheduling the specified MODULE .  Timing
 values are returned to the  PFU  utility through a timing  buffer
 in the PFUBLK global partition.

   outputs

   Every timing sample is displayed on the users terminal together
 with  the status of the MODULE and with a statistical summary that
 includes  maximum ,  minimum,normalized and average timings of the
 sample.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.

2 BAND
   With this option,you can compute and display the execution time
 required by any foreground BAND.

   Input_Syntax

   TIME BAND {Band Name} [Number of Samples]

   The Number of Samples must be in the range of 2-98 , any other
 value  specified is illegal and cause the command to be rejected.
 If Number  of Samples is omitted ,a default of 20 samples will be
 considered.

   The Band Name can take any one of the following values :

   1_1 2_1 2_2 3_1 3_2 3_3 3_4 4_1 4_2 4_3 4_4 4_5 4_6 4_7 4_8

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command) receives and services the request by reading a Real Time
 Clock before and after  scheduling the specified  BAND  .  Timing
 values are returned to the  PFU  utility through a timing buffer
 in the PFUBLK global partition.

   Outputs

   Every timing sample is displayed on the users terminal followed
 by  a statistical summary that includes the maximum , minimum  and
 average timings of the sample.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.

2 LEG
   With this option,you can compute and display the execution time
 required by any foreground LEG.

   Input_Syntax

   TIME LEG {Leg Number} [Number of Samples]

   The Number of Samples must be in the range of 2-98 , any other
 value  specified is illegal and cause the command to be rejected.
 If  Number of Samples is omitted ,a default of 20 samples will be
 considered.

   The Leg Number must be within the range of LEGs as described in
 the LEG table of the SYNCTBLi.MAR file which reside in the CAE$DISP
 directory, any other values are rejected and no default Leg Number
 is supplied.

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command) receives and services the request by reading a Real Time
 Clock before and after  scheduling the  specified  LEG  .  Timing
 values are returned to the  PFU  utility through a timing buffer
 in the PFUBLK global partition.

   Outputs

   Every timing sample is displayed on the users terminal followed
 by a statistical summary that includes the maximum , minimum  and
 average timings of the sample.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.

2 PROGRAM_Scheduling
   With this option,you can time and display the Scheduling Period
 of  any  foreground   MODULE  during a maximum of  96  consecutive
 iterations.

   Input_Syntax

   TIME /PERIOD         {Module Name} [Number of Samples]
   TIME /PERIOD PROGRAM {Module Name} [Number of Samples]

   The Number of Samples must be in the range of 1-96 , any other
 value  specified is illegal and cause the command to be rejected.
 If Number  of Samples is omitted ,a default of 20 samples will be
 considered.

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command) receives and services the request by reading a Real Time
 Clock before each call to the specified MODULE. Timing values are
 returned  to  the  PFU  utility  through  a  timing buffer in the
 PFUBLK global partition.

   outputs

   Every consecutive timing is displayed on the users terminal and
 followed  by a statistical summary that includes maximum , minimum
 and average Scheduling Period of the sample.

   Special_Requirements

   PFU  must be set to corresponding process , see the SET PROCESS
 command.
2 BAND_Scheduling
   With this option,you can time and display the Scheduling Period
 of  any   foreground  BAND  during  a  maximum  of  96 consecutive
 iterations.

   Input_Syntax

   TIME /PERIOD BAND {Band Name} [Number of Samples]

   The Number of Samples must be in the range of 1-96 , any other
 value  specified is illegal and cause the command to be rejected.
 If  Number of Samples is omitted ,a default of 20 samples will be
 considered.

   The Band Name can take any one of the following values :

   1_1 2_1 2_2 3_1 3_2 3_3 3_4 4_1 4_2 4_3 4_4 4_5 4_6 4_7 4_8

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command) receives and services the request by reading a Real Time
 Clock before each call to the specified BAND  . Timing values are
 returned  to  the  PFU  utility  through  a  timing buffer in the
 PFUBLK global partition.

   Outputs

   Every consecutive timing is displayed on the users terminal and
 followed  by a statistical summary that includes maximum , minimum
 and average Scheduling Period of the sample.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
2 LEG_Scheduling
   With this option,you can time and display the Scheduling Period
 of  any  foreground  LEG   during  a  maximum  of  96  consecutive
 iterations.

   Input_Syntax

   TIME /PERIOD LEG {Leg Number} [Number of Samples]

   The Number of Samples must be in the range of 1-96 , any other
 value specified is illegal and cause the command to be rejected.
 If Number of Samples is omitted ,a default of 20 samples will be
 considered.

   The Leg Number must be within the range of LEGs as described in
 the LEG table of the SYNCTBLi.MAR file which reside in the CAE$DISP
 directory, any other values are rejected and no default Leg Number
 is supplied.

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command) receives and services the request by reading a Real Time
 Clock before each call to the specified LEG .  Timing  values are
 returned  to  the  PFU  utility  through  a  timing buffer in the
 PFUBLK global partition.

   Outputs

   Every consecutive timing is displayed on the users terminal and
 followed by a statistical summary that includes maximum , minimum
 and average Scheduling Period of the sample.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.

1 SUMMARY
   With this option,you can compute and display the execution time
 of  each MODULE in every processor or optionally , of every MODULE
 in  a specified processor . Note that the global SUMMARY will also
 include   a statistical figure that represents the total processor
 execution  time taken by the modules and the available spare  time
 on that  processor.Note that while a maximum of 98 timing  samples
 per module  can be requested,only the maximum, minimum, normalized
 and average timing values will be displayed.

  Input_Syntax

   SUMMARY              [Processor (Default=ALL)] [Number of Samples]
   SUMMARY /ALL         [Processor (Default=ALL)] [Number of Samples]

   The Number of Samples must be in the range of 2-98 , any other
 value  specified is illegal and cause the command to be rejected.
 If Number  of Samples is omitted ,a default of 20 samples will be
 considered.

   The Processor Name can take one of the following values :

   CPU0 - Main    CPU processor IPU0 - Main    attached processor
   CPU1 - Slave 1 CPU processor IPU1 - Slave 1 attached processor
   CPU2 - Slave 2 CPU processor IPU2 - Slave 2 attached processor
   CPU3 - Slave 3 CPU processor IPU3 - Slave 3 attached processor
   CPU4 - Slave 4 CPU processor IPU4 - Slave 4 attached processor

   Processing

   The dispatcher of each foreground process that is  running  in
 the specified  processor  receives and  services each request  by
 reading a Real Time Clock before and after scheduling each one of
 their foreground modules . Timing values are returned to the  PFU
 utility  through  a  timing  buffer  in  the  PFUBLK global
 partition.

   Outputs

   The maximum,minimum,average and normalized timing value of each
 PROGRAM   dispatched in the specified processor is displayed . The
 display  also includes additional information such as  band name ,
 iteration rates and module status during timing session.

  Special_Requirements

  None.
2 PROGRAM
   With this option,you can compute and display the execution time
 of every  MODULE in a specified process .Note that while 98 timing
 samples  per module can be requested , only the maximum ,minimum ,
 normalized and average timing values will be displayed.

   Input_Syntax

   SUMMARY         [Number of Samples]
   SUMMARY PROGRAM [Number of Samples]

   The Number of Samples must be in the range of 2-98 , any other
 value  specified is illegal and cause the command to be rejected.
 If  Number of Samples is omitted ,a default of 20 samples will be
 considered.

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command) receives and services the request by reading a Real Time
 Clock before and after scheduling each foreground MODULE . Timing
 values are returned to the PFU utility through  a  timing  buffer
 in the PFUBLK global partition.

   Outputs

   The maximum,minimum,average and normalized timing value of each
 PROGRAM  timing of the specified process is displayed. The display
 includes  additional information such as band name, iteration rate
 and module status during timing session.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
2 BAND
   With this option,you can compute and display the execution time
 of  every  BAND in a specified process . Note that while 98 timing
 samples  per band can be requested ,only the maximum ,minimum ,and
 average timing values will be displayed.

   Input_Syntax

   SUMMARY BAND [Number of Samples]

   The Number of Samples must be in the range of 2-98 , any other
 value specified is illegal and cause the command to be rejected.
 If Number of Samples is omitted ,a default of 20 samples will be
 considered.

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command) receives and services the request by reading a Real Time
 Clock before and after scheduling each foreground  BAND .  Timing
 values are returned to the PFU utility  through a  timing  buffer
 in the PFUBLK global partition.

   Outputs

   The maximum,minimum,average and normalized timing value of  each
 BAND  timing  of the specified  process is displayed . The display
 includes  additional information such as band name, iteration rate
 and module status during timing session.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
2 LEG
   With this option,you can compute and display the execution time
 of  every  LEG  in a specified process . Note that while 98 timing
 samples  per leg can be requested , only the maximum ,minimum ,and
 average timing values will be displayed.

   Input_Syntax

   SUMMARY LEG [Number of Samples]

   The Number of Samples must be in the range of 2-98 , any  other
 value  specified is illegal and cause the command to be rejected.
 If  Number of Samples is omitted ,a default of 20 samples will be
 considered.

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command) receives and services the request by reading a Real Time
 Clock before and after scheduling each foreground  LEG  .  Timing
 values are returned to the PFU utility  through  a timing  buffer
 in the PFUBLK. global partition.

   Outputs

   The maximum , minimum and average timing value of each LEG that
 is  dispatched in the specified process is displayed . The display
 includes  additional information such as leg composition of the leg
 being timed.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
1 LINK
   With this option,you can request the dispatcher of  a  specified
 process to start scheduling modules that where previously unlinked
 from the foreground,see the UNLINK command. You may also use  this
 command to restore a module from the debugging mode,see the  DEBUG
 command.

2 MODULE
   With this option,you can request the dispatcher of a  specified
 process  to start scheduling a MODULE that was previously unlinked
 from the foreground,see the UNLINK command.

   Input_Syntax

   LINK {Module Name}

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command)receives and services the request by resetting the status
 of the specified module to the running state in its PROGRAM table.
 (See UNLINK command).

   Outputs

   The foreground acknowledges the request

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
2 Debugged_MODULE
   With this option,you can request the dispatcher of a  specified
 process  to  restore  the  call  to  the  original module after a
 debugging session,see the DEBUG command.

   Input_Syntax

   LINK {Module Name}

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command)receives and services the request by resetting the status
 of the specified module to the running state in its PROGRAM table.
 (See UNLINK command).

   Outputs

   The foreground acknowledges the request

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
2 BAND
   With this option,you can request the dispatcher of a  specified
 process  to start scheduling a BAND that was  previously  unlinked
 from the  foreground,see the UNLINK command.

   Input_Syntax

   LINK BAND {Band Name}

   The Band Name can take any one of the following values

   1_1 2_1 2_2 3_1 3_2 3_3 3_4 4_1 4_2 4_3 4_4 4_5 4_6 4_7 4_8

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command)receives and services the request by resetting  the status
 of every module of the specified  BAND to the running state in its
 PROGRAM table (See UNLINK command).

   Outputs

   The foreground acknowledges the request.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
1 UNLINK
   With this option,you can request the dispatcher of a  specified
 process  to stop scheduling foreground modules,see the LINK command
2 MODULE
   With this option,you can request the dispatcher of a  specified
 process  to stop scheduling a specified foreground MODULE, see the
 LINK command.

   Input_Syntax

   UNLINK {Module Name}

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command)receives and services the request by  setting  the status
 of  the  specified module to the unlinked  state in  its  PROGRAM
 table ( See LINK command ) .

   Outputs

   The foreground acknowledges the request.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
2 BAND
   With this option,you can request the dispatcher of a  specified
 process  to stop scheduling a specified foreground  BAND , see the
 LINK command.

   Input_Syntax

   UNLINK BAND {Band Name}

   The Band Name can take any one of the following values

   1_1 2_1 2_2 3_1 3_2 3_3 3_4 4_1 4_2 4_3 4_4 4_5 4_6 4_7 4_8

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command)receives and services the request by  setting  the status
 of every module of the specified BAND to the  unlinked  state  in
 its PROGRAM table (See LINK command).

   Outputs

   The foreground acknowledges the request.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.

1 MOVE
   With this option,you can change the order of scheduling  of any
 modules  in a specified  process  by  moving  it  anywhere  in the
 dispatcher's program table dynamically.

   Input_Syntax

   MOVE {Module Name} {Band Name} {Relative Position}

   The Band Name can take any one of the following values

   1_1 2_1 2_2 3_1 3_2 3_3 3_4 4_1 4_2 4_3 4_4 4_5 4_6 4_7 4_8

   The Relative Position ranges from 1 to the number of  modules
 in the destination Band (+1 if move is outside of current band)

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command)receives and services the request by moving the specified
 MODULE to the specified scheduling position in the specified BAND.

   Outputs

   The foreground acknowledges the request.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
1 SWAP
   With this option,you can change the order of scheduling  of any
 band in a specified process by swapping it with any other band in
 the dispatcher's band table dynamically.

   Input_Syntax

   SWAP {Band Name} {Band Name}

   The Band Names can take any one of the following values

   1_1 2_1 2_2 3_1 3_2 3_3 3_4 4_1 4_2 4_3 4_4 4_5 4_6 4_7 4_8

   Processing

   The  dispatcher of the corresponding process ( See SET PROCESS
 command ) receives  and  services  the  request  by swapping the
 specified BANDs in the dispatcher's band table.

   Note: When swapping a non-critical band with a critical band,
 the non-critical band becomes  critical  and the  critical band
 becomes non-critical,see the LIST LEG command.

   Outputs

   The foreground acknowledges the request.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
1 LIST
   With this option,you can display the names of every module that
 are scheduled in a specific Process.
2 BAND
   With this option,you can display the names of every module that
 are scheduled in a specified band of a specific Process.

   Input_Syntax

   LIST {Band Name}

   The Band Name can take any one of the following values

   X A B C D E F G H I J K L M N O

   Processing

   The temporary storage area containing the module names of each
 module in  the  specified process is scanned.This storage area is
 re-initialized each time a SET PROCESS command is issued .See the
 SET PROCESS  command.

   Outputs

   Every module name of the band are displayed together with their
 status and iteration rates.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
3 /ALL
   With this option,you can display the names of every module that
 are scheduled in a specific process .

   Input_Syntax

   LIST /ALL

   Processing

   The temporary storage area containing the module names of each
 module in the specified process is scanned . This storage area is
 re-initialized each time a SET PROCESS command is issued .See the
 SET PROCESS  command.

   Outputs

   Every module name of the band are displayed together with their
 status and iteration rates.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
2 LEG
   With this option,you can display the names of every band that
 are scheduled in a specified LEG of a specific Process.

   Input_Syntax

   LIST LEG {Leg Number}

   The Leg Number must be within the range of LEGs as described in
 the LEG table of the SYNCTBLi.MAR file which reside in the CAE$DISP
 directory, any other values are rejected and no default Leg Number
 is supplied.

   Processing

   The temporary storage area containing the band names of each
 band in  the  specified LEG is scanned.This storage area is
 re-initialized each time a SET PROCESS command is issued .See the
 SET PROCESS  command.

   Outputs

   Every band name of the LEG are displayed together with their
 status and iteration rates.

   Note: Critical bands are placed between brackets followed by all
 non-critical bands.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
3 /ALL
   With this option,you can display the names of every band that
 are scheduled in every leg of a specific process .

   Input_Syntax

   LIST /ALL LEG

   Processing

   The temporary  storage area containing the band names of each
 band in each leg of the the specified process is scanned . This
 storage area is re-initialized each time a SET PROCESS  command
 is issued .See the SET PROCESS  command.

   Outputs

   Every leg of the specific process is displayed together with
 each band that compose them.

   Special_Requirements

   PFU must be set to corresponding process , see the SET PROCESS
 command.
1 FIND
   With this option,you can request PFU to scan the program  table
 of  ALL processes to find  the  occurrence of a particular MODULE.

   Input_Syntax

   FIND {Module Name}

   Module Name can be any module name string,wild card (*) is also
 supported , in  wich  case, information is given on every matching
 module.

   Processing

   The temporary module name storage area is scanned ( See the SET
 PROCESS command.

   Outputs

   The band name ,module status and process name where the module
 was found is displayed.

   Special_Requirements

   None.
1 MONITOR
   With this option,you can request PFU to monitor some foreground
 characteristics.
2 SPARE
   With this option,you can request PFU to monitor the available
 spare time in any processor.

   Input_Syntax

   MONITOR [SPARE] [Processor (Default=ALL)]

   Spare time statistics are monitored and displayed on the screen.
 To terminate the monitoring , use CTRL^C.

   The Processor Name is optional and defaults to ALL.It can take
 any of the following values :

   CPU0 - Main    CPU processor IPU0 - Main    attached processor
   CPU1 - Slave 1 CPU processor IPU1 - Slave 1 attached processor
   CPU2 - Slave 2 CPU processor IPU2 - Slave 2 attached processor
   CPU3 - Slave 3 CPU processor IPU3 - Slave 3 attached processor
   CPU4 - Slave 4 CPU processor IPU4 - Slave 4 attached processor

   Processing

   The spare time  CDB variables are read and statistics
 are computed.The UNIX  CPU  and  ATTACHED  schedulers                 
 compute their respective spare  time  statistics  before
 scheduling tasks or before entering the IDLE state. By definition,any
 time that is not consumed by a Real Time Task (priority higher than
 38) is considered spare time.

   Outputs

   The spare time statistics are displayed on the screen. They are
 maximum, minimum, average spare time over a cycle of iteration
 corresponding to the maximum number of legs in the foreground
 processes.

   Special_Requirements

   None.
2 OVERRUN
   With this option,you can request PFU to monitor the accumulated
 number of overruns encountered by synchronous processes.

   Input_Syntax

   MONITOR OVERRUN [Processor (Default=ALL)]

   Overrun statistics are monitored and displayed on the screen.
 To terminate the monitoring , use CTRL^C.

   The Processor Name is optional and default to ALL.It can take
 any of the following values :

   CPU0 - Main    CPU processor IPU0 - Main    attached processor
   CPU1 - Slave 1 CPU processor IPU1 - Slave 1 attached processor
   CPU2 - Slave 2 CPU processor IPU2 - Slave 2 attached processor
   CPU3 - Slave 3 CPU processor IPU3 - Slave 3 attached processor
   CPU4 - Slave 4 CPU processor IPU4 - Slave 4 attached processor

   Processing

   The overrun CDB variables  are  read  and displayed
 on the screen. Note that if the CAE$MAXOVR logical name translates
 to zero or is not defined,no overrun statistics are kept by the
 foreground.

   Outputs

   The accumulated number of overruns are displayed on the screen.

   Special_Requirements

   The CAE$MAXOVR logical must translate to something  else than
 zero. It defines the maximum number of consecutive overruns that
 are to be tolerated by the foreground (usually 600).
2 MODULE
  With this option, you can request The PFU to monitor the execution
time of a module of a specified processor.

   Input_Syntax

   MONITOR MODULE {Module Name}

   Timing statistics are monitored and displayed on the screen.
 To terminate the monitoring , use CTRL^C.

   Processing

   The module timing variables  are  read  and displayed
 on the screen.

   Outputs

   The maximum, minimum and instantaneous execution time of module
2 BAND
  With this option, you can request The PFU to monitor the execution
time of a band of a specified processor.

   Input_Syntax

   MONITOR BAND {Band Name}

   Timing statistics are monitored and displayed on the screen.
 To terminate the monitoring , use CTRL^C.

   Processing

   The Band timing variables  are  read  and displayed
 on the screen.

   Outputs

   The maximum, minimum and instantaneous execution time of Band
2 LEG
  With this option, you can request The PFU to monitor the execution
time of a LEG of a specified processor.

   Input_Syntax

   MONITOR LEG {Leg Number}

   Timing statistics are monitored and displayed on the screen.
 To terminate the monitoring , use CTRL^C.

   Processing

   The Leg timing variables  are  read  and displayed
 on the screen.

   Outputs

   The maximum, minimum and instantaneous execution time of Leg

1 SHELL
   With this option,you can issues a shell command from the
 PFU utility.

   Input_Syntax

   SHELL               

   Processing

   The PFU utility asks user for a shell command and then issues
  the shell command using the SYSTEM routine. The control is restored 
  to PFU upon termination.

   Outputs

   They are the outputs of the activated utility.

   Special_Requirements

   The user's account must have sufficient quotas and privileges.
1 STATUS
   With this option , you  can  display information concerning the
 simulator's current status.

   Input_Syntax

   STATUS

1 FREEZE
   With this option,you can FREEZE the simulator.On heavily loaded
 systems ,the FREEZE  and UNFREEZE  options may be useful to give
 background time for concurrent processing.

   Input_Syntax

   FREEZE

   Outputs

   None.

   Special_Requirements

   None.
1 UNFREEZE
   With this option , you  can unfreeze the simulator . On heavily
 loaded systems,the FREEZE and UNFREEZE options may be useful  to
 give background time for concurrent processing.

   Input_Syntax

   UNFREEZE

   Outputs

   None.

   Special_Requirements

   None.
1 SET
    With this option,you can change several PFU default characteristics
 to ease the performance monitoring session
2 FRAME
   With this option,you can change the basic iteration rate of the
 synchronous processes dynamically.

   Input_Syntax

   SET FRAME {Iteration Frequency}

   The iteration frequency can take any value in the range of 1-480
 Care  should  be taken when selecting a high frequency  since  this
 may hang the system if insufficient spare time is available.

   Processing

   The simulator CDB iteration frequency and Period
 are reset by the foreground dispatcher .

   Outputs

   The foreground acknowledges the request.

   Special_Requirements

   The SET FRAME command only works for Synchronous processes

2 OVERRUN
   With this option , you can  disable  or enable  foreground overrun
 checks .This is sometimes useful when you  wish  to perform timings
 on a system running with a reduced base iteration rate or when there
 is insufficient spare time for doing timings.

   Input_Syntax

   SET OVERRUN {Maximum Number of consecutive overruns to tolerate}

   The value of the overrun parameter must be a positive number. If
 the  overrun  parameter  is set to zero , no  overrun  checks  are
 performed by the foreground.

   Processing

   The simulator CDB variable representing the number
 of  consecutive  overruns  tolerated is  reset by  the foreground
 dispatcher.

   Outputs

   The foreground acknowledges the request.

   Special_Requirements

   SET OVERRUN only works on Synchronous processes

2 PRINT
   With this option , you  can  request a  hardcopy listing of any
 PFU  screen display . This  is  most useful when you wish to file
 some  timing results for future considerations,see the SET NOPRINT
 command.

   Input_Syntax

   SET PRINT

   Processing

   A scratch file is opened with the DISPOSE=PRINT option. Outputs
 are written into the file until it is closed (See the SET NOPRINT
 command).

   Outputs

   PFU acknowledges the request

   Special_Requirements

   None.

2 NOPRINT
   With this option,you can direct the file  previously  generated
 from  the SET PRINT command to the line printer queue (see the SET
 PRINT command).

   Input_Syntax

   SET NOPRINT

   Processing

   The scratch file previously opened by the SET PRINT command is
 closed (See the SET PRINT command).

   Outputs

   PFU acknowledges the request,the saved file is queued to the
 line printer queue.

   Special_Requirements

   Print mode must be enabled,see SET PRINT command.

2 PROCESS
   With this option,you load into PFU the dispatcher PROGRAM table
 of  the specific task. This is usually the first command  that you
 issue  after invoking the PFU utility.This command is prerequisite
 to most PFU functions.

   Input_Syntax

   SET         {Process Pseudo-name}
   SET PROCESS {Process Pseudo-name}

   The Process Pseudo-name is the processor name to which is appended
 the process name as it is loaded by the MOM process (see UNIX Series
 programming memo # 500).

   The following are typical Process Pseudo-names :

     CPU0.SP0 - Synchronous         process running in MAIN processor
     IPU0.SP1 - Synchronous         process running in ATTACHED processor
     CPU0.AP0 - First asynchronous  process running in MAIN processor
     CPU0.AP1 - Second asynchronous process running in MAIN processor
     CPU0.AP2 - Third  asynchronous process running in MAIN processor
     CPU0.AP4 - Fourth asynchronous process running in MAIN processor

   Processing

   The PROGRAM table of the corresponding process (i.e. the task with
 the  corresponding  process pseudo-name) is saved in a PFU  temporary
 storage  area  .

   Outputs

   The foreground acknowledges the request.

   Special_Requirements

   The specified process must be loaded and running.

1 SHOW
   With this option,you can display foreground characteristics

2 FOREGROUND
   With this option ,you can see witch of the loaded processes are
 currently running.

   Input_Syntax

   SHOW FOREGROUND

   Processing

   The foreground dispatchers receive and service the request by writing
 their task pseudo-names in a reserved area in  the  PFUBLK  global
 partition.

   Outputs

   The pseudo-names of all currently running processes are displayed
 on the users terminal.

   Special_Requirements

   None.

1 DEBUG
   This  facility eases debugging of foreground modules by speeding
 up  the process of replacing, on a trial basis, foreground modules
 in the configuration.

   Input_syntax

   DEBUG {Source file name} {Module entry point}

   The source file name is the path name of the module you  wish  to
 debug and replace in the foreground.

   The Module entry point is the entry point that will be called  by
 the  temporary process generated by PFU.It must match the foreground
 module  entry point  name that you wish to replace .

   Processing

   The PFU utility will look in your current default directory for an
 object file corresponding  to  the  source file name entered. If the
 file does not exist,  PFU will generate one using the -g option. 
 If it exist, PFU will prompt you and ask if you wish
 to generate a new file.  All  new  files  are opened in your current
 default directory. 

   If the Module entry point is omitted,PFU will scan the object file
 for entry point names and select the  one  that is  scheduled by the
 dispatcher.If more than one entry point is found,PFU will prompt the
 user for the one he wishes to debug.

   PFU then attempts to find an image file corresponding to the entry
 point that you wish to DEBUG in the current default directory. If it
 exist, it will prompt you and ask if you want to generate a new one,
 otherwise, PFU generates a temporary suspended task, linked with  the
 object resulting from the source file entered that calls the  Module
 entry point and  suspends itself.

   PFU will prompt you for a DEBUGGER OVERLAY. If you wish to set break
 points in your module, answer "YES", otherwise, PFU utility  issues  a
 request to the dispatcher  of the specified process (See SET PROCESS
 command) to replace the module's entry point CALL by a RESUME to the
 generated process  right away.

   Outputs

   None.

   Special_Requirements

   PFU must be set to corresponding process (see the SET PROCESS command).

1 X
   Used to exit the PFU session.

   Input_Syntax

   X

   Processing

   PFU parses the input line , branches to the closing routine and
 terminates the program. If the SET PRINT was set, the output  file
 is submitted to the line printer queue.

   Outputs

   The user is returned to DCL level.

   Special_Requirements

   None.

1 RESET
   This command allows the user to recover from any abnormal  foreground
 behavior.

  Input_Syntax

  RESET

  Processing

    All PFU variables in the PFUBLK global partition are reset
 to their initial state and running foreground processes  acknowledge
 the request. Note that any previous  process  setting with usage of
 the SET PROCESS  is lost. Note also that using RESET while  another
 user is in PFU  will abort that users command.

  Outputs

  The user screen is refreshed to its initial state and setting.

  Special_Requirements

  None.

1 Test_Method
   The method of testing  PFU should include  performing each of its
 functions on any running foreground processes.

   Test_Requirements

   The PFU utility will operate only  on sites that run Asynchronous
 and Synchronous dispatchers revision level 15.0 and higher releases.

1 Command_Summary
   A complete list of the commands available in the PFU utility is
 given below.

   The  syntax  for   entering each PFU command is described below.
 Entries in {  } are  compulsory while entries in [  ] are optional.
 Most command ,qualifier or parameters can be abbreviated to a three
 characters descriptor.

      TIME [ /PERIOD ]            {Module Name}     [Number of Samples]
      TIME [ /PERIOD ] PROGRAM    {Module Name}     [Number of Samples]
      TIME [ /PERIOD ] BAND       {Band Name}       [Number of Samples]
      TIME [ /PERIOD ] LEG        {Leg  Number}     [Number of Samples]
      SUMMARY [ /ALL ] [Processor (default=ALL)]    [Number of Samples]
      SUMMARY          PROGRAM                      [Number of Samples]
      SUMMARY          BAND                         [Number of Samples]
      SUMMARY          LEG                          [Number of Samples]
      HELP
      SET                         {Process Pseudo-name}
      SET              PROCESS    {Process Pseudo-name}
      SET              PRINT
      SET              NOPRINT
      SET              FRAME      {Iteration Frequency}
      SET              OVERRUN    {Maximum Number of Overruns}
      SHOW             FOREGROUND
      DEBUG                       {Filename} [Module Entry Point]
      STATUS
      LIST  [/ALL ]               [Band Name]
      LIST  [/ALL ]    LEG        [Leg Number]
      FREEZE
      UNFREEZE
      FIND                        {Module Name (Wild Card (*) Permitted) }
      UNLINK                      {Module Name}
      UNLINK           BAND       {Band Name}
      LINK                        {Module Name}
      LINK             BAND       {Band Name}
      MOVE          {Module Name} {Band Name} {Relative Band Position}
      SWAP          {Band Name}   {Band Name}
      X
      RESET
      MONITOR                     [Processor (Default=ALL)]
      MONITOR          SPARE      [Processor (Default=ALL)]
      MONITOR          OVERRUN    [Processor (Default=ALL)]
      MONITOR          MODULE     {Module Name}
      MONITOR          BAND       {Band Name}
      MONITOR          LEG        {Leg Number}
      SHELL                       
