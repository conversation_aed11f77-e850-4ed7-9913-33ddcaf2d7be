#! /bin/csh -f
#
# $Revision: BACKUP procedure V3.7 08-Jul-92 | MB fix record output msg $
# $R_______: BACKUP procedure V3.6 10-Feb-92 | RBE valid for all IBM$
# $R_______: BACKUP procedure Version 3.5 (MB) 10/Sept/1991$
#
# Version 3.6 - <PERSON>
#  - Make it more general (any micro-channel number)
#
# Version 3.7 - <PERSON>
#  - Gives correct number of records output/input for 670 or 335 mb disks
#
#  This script backs up a removable disk to another
#
onintr inter
echo
echo "*** BACKUP Version 3.7 - July 1992  ***"
echo 
#
set FROMSTATE="inexistant"
set TOSTATE="inexistant"
set FNEWDEV="dummy"
set TNEWDEV="dummy"
#
# Stop networks.
#
echo "===> Killing all task." > back.log
killall -
echo " " >> back.log
#
# Get source and target chassis.
#
echo -n "Enter source chassis: "
set FROM=($<)
echo -n "Enter target chassis: "
set TO=($<)
echo "Source chassis: $FROM" >> back.log
echo "Target chassis: $TO" >> back.log
echo -n "Enter size of disk (355 or 670): "
set SIZ=($<)
#
# Check all the disks on system to find the name of disks in chassis specified.
#
echo " " >> back.log
echo "===> Disks present:" >> back.log
foreach I ("`lsdev -c disk -C`")
  echo "$I" >> back.log
#
# Get state and name of source disk
#
# RBE+
  if (`echo $I|cut -f3 -d' '|cut -f4 -d'-'` == "$FROM") then
# RBE-
     set FROMSTATE="`echo $I|cut -f2 -d' '`"
     set FROMNAME="`echo $I|cut -f1 -d' '`"
#
# Get state and name of target disk
#
  else
# RBE+
  if (`echo $I|cut -f3 -d' '|cut -f4 -d'-'` == "$TO") then
# RBE-
        set TOSTATE="`echo $I|cut -f2 -d' '`"
        set TONAME="`echo $I|cut -f1 -d' '`"
     endif
  endif
end
#
# If source device is not available we have to create a temporary device.
#
if ($FROMSTATE != "Available") then
   echo " " >> back.log
   echo "===> Creating temporary source device." >> back.log
   mkdev -c disk -t osdisk -s scsi -p scsi0 -w $FROM -lbaksr >>& back.log
   if ($status != 0) then
      echo "No disk on chassis $FROM or chassis not powered."
      echo -n "Insert disk, power chassis. Ready (y/n): "
      set ANS=($<)
      echo " " >> back.log
      echo "===> Giving second chance to user." >> back.log
      if ($ANS != "Y" && $ANS != "y") then
         echo " " >> back.log
         echo "===> User decided to exit." >> back.log
         echo "Backup exits."
         exit
      endif
      mkdev -c disk -t osdisk -s scsi -p scsi0 -w $FROM -lbaksr >>& back.log
      if ($status != 0) then
         echo " " >> back.log
         echo "===> Can not assign chassis $TO. Backup exits." >> back.log
         echo "Can not assign chassis $FROM. Backup exits."
         exit
      endif
      set FNEWDEV = "baksr"
   endif
   set FNEWDEV = "baksr"
endif
#
# If device is not available we have to create a temporary device.
#
if ($TOSTATE != "Available") then
   echo " " >> back.log
   echo "===> Creating a temporary target device." >> back.log
   mkdev -c disk -t osdisk -s scsi -p scsi0 -w $TO -lbaktg >>& back.log
   if ($status != 0) then
      echo " " >> back.log
      echo "===> Giving second chance to user." >> back.log
      echo "No disk on chassis $TO or chassis not powered."
      echo -n "Insert disk, power chassis. Ready (y/n): "
      set ANS=($<)
      if ($ANS != "Y" && $ANS != "y") then
         if ($FNEWDEV != "dummy") then
            echo " " >> back.log
            echo "===> Removing source temporary device." >> back.log
            rmdev -l $FNEWDEV -d >>& back.log
            exit
         endif
         echo " " >> back.log
         echo "===> User decided to exit." >> back.log
         exit
      endif
      mkdev -c disk -t osdisk -s scsi -p scsi0 -w $TO -lbaktg >>& back.log
      if ($status != 0) then
         if ($FNEWDEV != "dummy") then
            echo " " >> back.log
            echo "===> Removing source temporary device." >> back.log
            rmdev -l $FNEWDEV -d >>& back.log
            exit
         endif
         echo " " >> back.log
         echo "===> Can not assign chassis $TO. Backup exits." >> back.log
         echo "Can not assign chassis $TO. Backup exits."
         exit
      endif
      set TNEWDEV = "baktg"
   endif
   set TNEWDEV = "baktg"
endif
#
# Last chance to change our mind.
#
echo -n "Next step will overwrite disk in chassis $TO . Ready (y/n)? "
set ANS=($<)
if ($ANS != "y" && $ANS != "Y") then
   echo " " >> back.log
   echo "===> User had second though." >> back.log
   if ($FNEWDEV != "dummy") then
      echo " " >> back.log
      echo "===> Removing temporary source device." >> back.log
      rmdev -l $FNEWDEV -d >>& back.log
   endif
   if ($TNEWDEV != "dummy") then
      echo " " >> back.log
      echo "===> Removing temporary source device." >> back.log
      rmdev -l $TNEWDEV -d >>& back.log
   endif
   exit
endif
#
# Set up the final device names (source and target)
#
if ($FNEWDEV != "dummy") set FROMNAME=`echo $FNEWDEV`
if ($TNEWDEV != "dummy") set TONAME=`echo $TNEWDEV`
#
# Copy one device to the other.
#
echo " " >> back.log
echo "===> Refresh dumpdates file" >> back.log
if (-e /etc/dumpdates) then
   rm /etc/dumpdates
endif
touch /etc/dumpdates
echo " " >> back.log
echo `date` >> back.log
echo "===> dd if=/dev/$FROMNAME of=/dev/$TONAME bs=1024k" >> back.log
dd if=/dev/$FROMNAME of=/dev/$TONAME bs=1024k >>& back.log
tail -3 back.log
if ("$SIZ" == "670") then
   echo "Backup completed. Backup successful if 639+1 records copied."
else
   echo "Backup completed. Backup successful if 339+1 records copied."
endif
echo "===> Backup completed." >> back.log
#
# Is there any temporary device to remove?
#
if ($FNEWDEV != "dummy") then
   echo " " >> back.log
   echo "===> Remove temporary source device $FNEWDEV" >> back.log
   rmdev -l $FNEWDEV -d >>& back.log
endif
if ($TNEWDEV != "dummy") then
   echo " " >> back.log
   echo "===> Remove temporary target device $TNEWDEV" >> back.log
   rmdev -l $TNEWDEV -d >>& back.log
endif
echo "Backup log in back.log"
echo "===> System ready for shutdown." >> back.log
echo "System ready for shutdown, log file is back.log ."
exit
#
# interrupt handling
#
inter:
if ($FNEWDEV != "dummy") then
   echo " " >> back.log
   echo "===> Remove temporary source device $FNEWDEV" >> back.log
   rmdev -l $FNEWDEV -d >>& back.log
endif
if ($TNEWDEV != "dummy") then
   echo " " >> back.log
   echo "===> Remove temporary target device $TNEWDEV" >> back.log
   rmdev -l $TNEWDEV -d >>& back.log
endif
exit
